import { resolve } from 'path'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { ConfigEnv, UserConfig } from 'vite'
// import commonjsPlugin from 'vite-plugin-commonjs'
import { vitePluginBuildClean, vitePluginCssModuleAlias } from '@tx/plugin'
import { ProjectConfigType, createProxyCdn, genViteVueConfig } from '@tx/vite-config'

export default async function (options: ConfigEnv): Promise<UserConfig> {
  const { default: ItResolver } = await import('@intsig/toolkit/resolver')
  const { default: TxResolver } = await import('@tx/ui/resolver')
  const antdResolver = AntDesignVueResolver()
  const itResolver = ItResolver()
  const txResolver = TxResolver()

  const isBuild = options.command === 'build'
  const isServe = options.command === 'serve'

  const projectConfig: ProjectConfigType = {
    externals: {
      '@vxe-ui/core': 'VxeUI',
      diff: 'JsDiff',
      'guacamole-common-js': 'Guacamole',
      'js-base64': 'Base64',
      // 'sql-formatter': 'sqlFormatter',
      'sql-formatter': '`请使用 await loadSqlFormatter()`',
      // 'vue-code-diff': 'vue-code-diff',
      'vue-code-diff': '`请使用 NocCodeDiff`',
      'vue-container-query': 'VueContainerQuery',
      'vue-i18n': 'VueI18n',
      'vue-json-editor': '`请使用 NocEditorJson`',
      'vxe-table': 'VXETable',
      'xe-utils': 'XEUtils',
    },
    plugins: {
      more: [
        // commonjsPlugin(),
        Components({
          /**
           * 防止自动引入 src 内的组件，避免组件同名时导致错误引入
           * https://github.com/antfu/unplugin-vue-components#configuration
           */
          dirs: ['src/not_exist'],
          dts: true,
          resolvers: [
            name => {
              // 已全量引入，无需再次构建
              if (isBuild) {
                return
              }
              // 已全量引入，移除样式，生成 .d.ts
              let res = antdResolver['resolve'](name)
              if (res) {
                // 无法获取 Symbol('menuContextKey')
                if (/(layout|menu)/i.test(res.name)) {
                  return
                }
                delete res['sideEffects']
                return res
              }
            },
            itResolver,
            txResolver,
          ],
        }),
        // 构建文件清理，删除构建目录下 30 天前创建的文件
        vitePluginBuildClean({ expiry: 30 }),
        vitePluginCssModuleAlias(),
      ],
    },
    // report: true,
  }
  const config = genViteVueConfig(options, projectConfig) as unknown as UserConfig

  config.css = {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  }

  if (isBuild && config.build) {
    config.base = '/'
    config.build.emptyOutDir = false
    config.build.sourcemap = true
  }

  if (isServe && config.server) {
    config.server.open = '/'
    config.server.port = 8080

    if (1) {
      Object.assign(config.server.proxy, {
        // '/api/iseafs': {
        //   changeOrigin: true,
        //   target: 'http://127.0.0.1:8024/v1/iseafs',
        //   rewrite: path => {
        //     return path.replace('/api/iseafs', '')
        //   },
        // },
        '/api/auth': {
          changeOrigin: true,
          target: 'http://127.0.0.1:8002/v1/auth',
          rewrite: path => {
            return path.replace('/api/auth', '')
          },
        },
        '/api': {
          changeOrigin: true,
          target: 'http://127.0.0.1:8000/v1',
          rewrite: path => {
            return path.replace(/^\/api/, '')
          },
        },
        '/desktopapi': {
          changeOrigin: true,
          target: 'https://sonar-test.intsig.net',
          rewrite: path => {
            return path.replace(/^\/desktopapi/, '')
          },
        },
        '/ospstorage': {
          changeOrigin: true,
          target: 'https://cloud-fs-test-api.intsig.net',
          rewrite: path => {
            return path.replace(/^\/ospstorage/, '')
          },
        },
        '/filestorage': {
          changeOrigin: true,
          target: 'https://cloud-test-storage.intsig.net',
          rewrite: path => {
            return path.replace(/^\/filestorage/, '')
          },
        },
      })
    } else {
      Object.assign(config.server.proxy, {
        // '/api/iseafs': {
        //   changeOrigin: true,
        //   target: 'http://127.0.0.1:8024/v1/iseafs',
        //   rewrite: path => {
        //     return path.replace('/api/iseafs', '')
        //   },
        // },
        '/api': {
          changeOrigin: true,
          target: 'https://cloud-test-api.intsig.net/v1',
          rewrite: path => {
            return path.replace(/^\/api/, '')
          },
        },
        '/desktopapi': {
          changeOrigin: true,
          target: 'https://sonar-test.intsig.net',
          rewrite: path => {
            return path.replace(/^\/desktopapi/, '')
          },
        },
        '/ospstorage': {
          changeOrigin: true,
          target: 'https://cloud-fs-test-api.intsig.net',
          rewrite: path => {
            return path.replace(/^\/ospstorage/, '')
          },
        },
        '/filestorage': {
          changeOrigin: true,
          target: 'https://cloud-test-storage.intsig.net',
          rewrite: path => {
            return path.replace(/^\/filestorage/, '')
          },
        },
      })
    }
  }

  return config
}
