<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-07-11 15:36:40
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-08-10 11:43:58
 * @FilePath: \cloud_web\public\index.html
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>logo.png">
    <title>智能基础服务云平台</title>
    <% for (var i in htmlWebpackPlugin.options.CDN && htmlWebpackPlugin.options.CDN.js) { %>
    <script src="<%= htmlWebpackPlugin.options.CDN.js[i] %>"></script>
    <% } %>
    <script src="https://static.intsig.net/web_basic_front/static/sdk/common_sdk/common_sdk.min.js"></script>
    <style>.first-loading-wrp{display:flex;justify-content:center;align-items:center;flex-direction:column;min-height:420px;height:100%}.first-loading-wrp>h1{font-size:128px}.first-loading-wrp .loading-wrp{padding:98px;display:flex;justify-content:center;align-items:center}.dot{animation:antRotate 1.2s infinite linear;transform:rotate(45deg);position:relative;display:inline-block;font-size:32px;width:32px;height:32px;box-sizing:border-box}.dot i{width:14px;height:14px;position:absolute;display:block;background-color:#1890ff;border-radius:100%;transform:scale(.75);transform-origin:50% 50%;opacity:.3;animation:antSpinMove 1s infinite linear alternate}.dot i:nth-child(1){top:0;left:0}.dot i:nth-child(2){top:0;right:0;-webkit-animation-delay:.4s;animation-delay:.4s}.dot i:nth-child(3){right:0;bottom:0;-webkit-animation-delay:.8s;animation-delay:.8s}.dot i:nth-child(4){bottom:0;left:0;-webkit-animation-delay:1.2s;animation-delay:1.2s}@keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@-webkit-keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@keyframes antSpinMove{to{opacity:1}}@-webkit-keyframes antSpinMove{to{opacity:1}}</style>
    <!-- require cdn assets css -->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
    <% } %>

    <script type = "text/javascript">
      (function() {
        if (window.zhuge) return;
        window.zhuge = [];
        window.zhuge.methods = "_init identify track trackRevenue getDid getSid getKey setSuperProperty setUserProperties setWxProperties setPlatform".split(" ");
        window.zhuge.factory = function(b) {
          return function() {
            var a = Array.prototype.slice.call(arguments);
            a.unshift(b);
            window.zhuge.push(a);
            return window.zhuge;
          }
        };
        for (var i = 0; i < window.zhuge.methods.length; i++) {
          var key = window.zhuge.methods[i];
          window.zhuge[key] = window.zhuge.factory(key);
        }
        window.zhuge.load = function(b, x) {
          if (!document.getElementById("zhuge-js")) {
            var a = document.createElement("script");
            var verDate = new Date();
            var verStr = verDate.getFullYear().toString() + verDate.getMonth().toString() + verDate.getDate().toString();

            a.type = "text/javascript";
            a.id = "zhuge-js";
            a.async = !0;
            a.src = '//zhugeio-new.intsig.net/zhuge.js?v=' + verStr;
            a.onerror = function() {
              window.zhuge.identify = window.zhuge.track = function(ename, props, callback) {
                if(callback && Object.prototype.toString.call(callback) === '[object Function]') {
                  callback();
                } else if (Object.prototype.toString.call(props) === '[object Function]') {
                  props();
                }
              };
            };
            var c = document.getElementsByTagName("script")[0];
            c.parentNode.insertBefore(a, c);
            window.zhuge._init(b, x)
          }
        };
        window.zhuge.load('e1fd3d1baaca4b94a40c89934784f240', { //配置应用的AppKey
          debug:true,
          adTrack: false,//广告监测开关，默认为false
          zgsee: false,//视屏采集开关， 默认为false
          autoTrack: true,
          //启用全埋点采集（选填，默认false）
          singlePage: false //是否是单页面应用（SPA），启用autoTrack后生效（选填，默认false）
        });
      })();
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but vue-antd-pro doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="first-loading-wrp">
        <h1>Cloud</h1>
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
        <div style="display: flex; justify-content: center; align-items: center;">智能基础服务云平台</div>
      </div>
    </div>
    <!-- require cdn assets js -->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
    <script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
    <!-- built files will be auto injected -->
  </body>
</html>
