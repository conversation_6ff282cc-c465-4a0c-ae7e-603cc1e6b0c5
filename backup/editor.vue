<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-20 15:05:27
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-21 16:48:02
 * @FilePath: \cloud_web\src\views\comp\editor.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div ref="container" class="monaco-editor" ></div>
</template>

<script>
// import * as monaco from 'monaco-editor'
// 请使用 TxCodeEditor
export default {
  inject: ['reload'],
  mounted () {
    this.init()
    // this.shuru()
  },
  watch: {
    opts: {
      handler () {
        this.reload()
      },
      deep: true
    }
  },
  props: {
    opts: {
      type: Object,
      default () {
        return {
        }
      }
    }
    // height: {
    //   type: Number,
    //   default: 300
    // },
    // isDiff: {
    //   type: Boolean,
    //   default: false
    // }
  },

  data () {
    return {
      defaultOpts: {
        value: '', // 编辑器的值
        theme: 'vs-dark' // 编辑器主题：vs, hc-black, or vs-dark，更多选择详见官网
        // roundedSelection: true, // 右侧不显示编辑器预览框
        // autoIndent: true // 自动缩进
      },
      // 编辑器对象
      monacoEditor: {},
      oldValue: '',
      newValue: ''
    }
  },
  methods: {
    init () {
      // 初始化container的内容，销毁之前生成的编辑器
      this.$refs.container.innerHTML = ''
      // 生成编辑器配置
      const editorOptions = Object.assign(this.defaultOpts, this.opts)
      console.log(this.opts, 'this.optsthis.opts')
      console.log(editorOptions, 'editorOptionseditorOptions')
      this.monacoEditor = monaco.editor.create(this.$refs.container, editorOptions)

      // if (!this.isDiff) {
      //   // 初始化编辑器实例
      //   // 编辑器内容发生改变时触发
      //   this.monacoEditor.onDidChangeModelContent(() => {
      //     this.$emit('change', this.monacoEditor.getValue())
      //   })
      // } else {
      //   this.oldValue = this.readLocalFile('./testCode1.js')
      //   this.newValue = this.readLocalFile('./testCode2.js')
      //   editorOptions.readOnly = true
      //   editorOptions.language = 'javascript'
      //   // editorOptions.inlineHints = true;
      //   // 初始化编辑器实例
      //   this.monacoDiffInstance = monaco.editor.createDiffEditor(this.$refs['container'], editorOptions)
      //   this.monacoDiffInstance.setModel({
      //     original: monaco.editor.createModel(this.oldValue, editorOptions.language),
      //     modified: monaco.editor.createModel(this.newValue, editorOptions.language)
      //   })
      // }
    },
    // upDateDiff (val) {
    //   this.monacoDiffInstance.updateOptions({
    //     renderSideBySide: !val
    //   })
    // },
    // 供父组件调用手动获取值
    getVal () {
      return this.monacoEditor.getValue()
    }
    // 读取本地文件函数
    // readLocalFile (fileUrl) {
    //   let xhr = null
    //   if (window.XMLHttpRequest) {
    //     xhr = new XMLHttpRequest()
    //   } else {
    //     // eslint-disable-next-line
    //     xhr = new ActiveXObject('Microsoft.XMLHTTP')
    //   }
    //   const okStatus = document.location.protocol === 'file' ? 0 : 200
    //   xhr.open('GET', fileUrl, false)
    //   xhr.overrideMimeType('text/html;charset=utf-8')
    //   xhr.send(null)
    //   return xhr.status === okStatus ? xhr.responseText : null
    // },
    // shuru () {
    //   // this.monacoEditor.setValue('hy')
    // }
  }
}
</script>

<style>
.monaco-editor {
  width: 100%;
  height:400px;
  margin-top: 16px;
  border: '1px solid grey';
}
</style>
