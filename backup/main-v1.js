/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-04 14:10:01
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-03-14 10:37:07
 * @FilePath: \cloud_web\src\main.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
// with polyfills
import 'core-js/stable'
import 'regenerator-runtime/runtime'

// if (process.env.NODE_ENV !== 'production') {

// }
import Vue from 'vue'

import App from '../src/App.vue'
import router from '../src/router'
import store from '../src/store'
import i18n from '../src/locales'
import { VueAxios } from '../src/utils/request'
import '../src/base/noc'
import { promiseHttps } from '@aim/helper'
import ProLayout, { PageHeaderWrapper } from '@ant-design-vue/pro-layout'
import themePluginConfig from '../config/themePluginConfig'
import { FormModel, Carousel, Tree, Slider, Tabs, Modal, DatePicker } from 'ant-design-vue'
import { Button as AButton, Icon as AIcon } from 'ant-design-vue'

import VXETable from 'vxe-table'
// import 'vxe-table/lib/style.css'


// mock
// WARNING: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV.
// import './mock'

import bootstrap from '../src/core/bootstrap'
import '../src/core/lazy_use' // use lazy load components
import '../src/permission' // permission control
import suite from '../src/suite'
// import './utils/filter' // global filter
import '../src/utils/vendorLoader' // 第三方资源加载助手
import '../src/assets/style/index.less'
import '@tx/style/dist/tx-style.css'

// 确保访问 https
promiseHttps()

Vue.use(FormModel) // global style
Vue.use(Carousel)
Vue.use(Tree)
Vue.use(Slider)
Vue.use(Tabs)
Vue.use(Modal)
Vue.use(DatePicker)
Vue.use(VXETable)


Vue.config.productionTip = false

// mount axios to `Vue.$http` and `this.$http`
Vue.use(VueAxios)
Vue.use(suite)
// use pro-layout components
Vue.component('pro-layout', ProLayout)
Vue.component('page-container', PageHeaderWrapper)
Vue.component('page-header-wrapper', PageHeaderWrapper)

/**
 * 准备升级 antd 按钮和图标
 */
Vue.component('tx-button', AButton)
Vue.component('tx-icon', AIcon)

window.umi_plugin_ant_themeVar = themePluginConfig.theme

new Vue({
  router,
  store,
  i18n,
  // init localstorage, vuex, Logo message
  created: bootstrap,
  render: h => h(App)
}).$mount('#app')
