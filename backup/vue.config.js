const path = require('path')
const { createVueConfig, isVueCliBuild } = require('@aim/helper/vue-cli/vue.config')
const webpack = require('webpack')
const GitRevisionPlugin = require('git-revision-webpack-plugin')
const GitRevision = new GitRevisionPlugin()
const buildDate = JSON.stringify(new Date().toLocaleString())
const createThemeColorReplacerPlugin = require('./config/plugin.config')
const CompressionPlugin = require('compression-webpack-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}

// check Git
function getGitHash() {
  try {
    return GitRevision.version()
  } catch (e) {}
  return 'unknown'
}

// vue.config.js
module.exports = createVueConfig({
  chainWebpack(config) {
    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .oneOf('inline')
      .resourceQuery(/inline/)
      .use('vue-svg-icon-loader')
      .loader('vue-svg-icon-loader')
      .end()
      .end()
      .oneOf('external')
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'assets/[name].[hash:8].[ext]',
        esModule: false,
      })
  },
  configureWebpack(config) {
    Object.assign(config.externals, {
      '@ant-design/icons/lib/dist': 'AntDesignIcons',
      // '@antv/data-set': 'DataSet',
      '@antv/data-set': '`请使用 await loadAntdDataSet()`',
      // TODO 使用 import { xxx } from 'ant-design-vue' 导入，而不是 import xxx from 'ant-design-vue/es/xxx'
      // TODO，移除 @ant-design-vue/pro-layout
      'ant-design-vue/es/affix': 'antd.Affix',
      'ant-design-vue/es/alert': 'antd.Alert',
      'ant-design-vue/es/anchor': 'antd.Anchor',
      'ant-design-vue/es/autocomplete': 'antd.AutoComplete',
      'ant-design-vue/es/avatar': 'antd.Avatar',
      'ant-design-vue/es/backtop': 'antd.BackTop',
      'ant-design-vue/es/badge': 'antd.Badge',
      'ant-design-vue/es/base': 'antd.Base',
      'ant-design-vue/es/breadcrumb': 'antd.Breadcrumb',
      'ant-design-vue/es/button': 'antd.Button',
      'ant-design-vue/es/calendar': 'antd.Calendar',
      'ant-design-vue/es/card': 'antd.Card',
      'ant-design-vue/es/carousel': 'antd.Carousel',
      'ant-design-vue/es/cascader': 'antd.Cascader',
      'ant-design-vue/es/checkbox': 'antd.Checkbox',
      'ant-design-vue/es/col': 'antd.Col',
      'ant-design-vue/es/collapse': 'antd.Collapse',
      'ant-design-vue/es/comment': 'antd.Comment',
      'ant-design-vue/es/config-provider': 'antd.ConfigProvider',
      'ant-design-vue/es/date-picker': 'antd.DatePicker',
      'ant-design-vue/es/descriptions': 'antd.Descriptions',
      'ant-design-vue/es/divider': 'antd.Divider',
      'ant-design-vue/es/drawer': 'antd.Drawer',
      'ant-design-vue/es/dropdown': 'antd.Dropdown',
      'ant-design-vue/es/empty': 'antd.Empty',
      'ant-design-vue/es/form': 'antd.Form',
      'ant-design-vue/es/form-model': 'antd.FormModel',
      'ant-design-vue/es/icon': 'antd.Icon',
      'ant-design-vue/es/input': 'antd.Input',
      'ant-design-vue/es/input-number': 'antd.InputNumber',
      'ant-design-vue/es/layout': 'antd.Layout',
      'ant-design-vue/es/list': 'antd.List',
      'ant-design-vue/es/locale-provider': 'antd.LocaleProvider',
      'ant-design-vue/es/mentions': 'antd.Mentions',
      'ant-design-vue/es/menu': 'antd.Menu',
      'ant-design-vue/es/modal': 'antd.Modal',
      'ant-design-vue/es/page-header': 'antd.PageHeader',
      'ant-design-vue/es/pagination': 'antd.Pagination',
      'ant-design-vue/es/popconfirm': 'antd.Popconfirm',
      'ant-design-vue/es/popover': 'antd.Popover',
      'ant-design-vue/es/progress': 'antd.Progress',
      'ant-design-vue/es/radio': 'antd.Radio',
      'ant-design-vue/es/rate': 'antd.Rate',
      'ant-design-vue/es/result': 'antd.Result',
      'ant-design-vue/es/row': 'antd.Row',
      'ant-design-vue/es/select': 'antd.Select',
      'ant-design-vue/es/skeleton': 'antd.Skeleton',
      'ant-design-vue/es/slider': 'antd.Slider',
      'ant-design-vue/es/space': 'antd.Space',
      'ant-design-vue/es/spin': 'antd.Spin',
      'ant-design-vue/es/statistic': 'antd.Statistic',
      'ant-design-vue/es/steps': 'antd.Steps',
      'ant-design-vue/es/switch': 'antd.Switch',
      'ant-design-vue/es/table': 'antd.Table',
      'ant-design-vue/es/tabs': 'antd.Tabs',
      'ant-design-vue/es/tag': 'antd.Tag',
      'ant-design-vue/es/time-picker': 'antd.TimePicker',
      'ant-design-vue/es/timeline': 'antd.Timeline',
      'ant-design-vue/es/tooltip': 'antd.Tooltip',
      'ant-design-vue/es/transfer': 'antd.Transfer',
      'ant-design-vue/es/tree': 'antd.Tree',
      'ant-design-vue/es/tree-select': 'antd.TreeSelect',
      'ant-design-vue/es/upload': 'antd.Upload',
      'ant-design-vue/es/message': 'antd.message',
      'ant-design-vue/es/notification': 'antd.notification',
      diff: 'JsDiff',
      'guacamole-common-js': 'Guacamole',
      'js-base64': 'Base64',
      // 'sql-formatter': 'sqlFormatter',
      'sql-formatter': '`请使用 await loadSqlFormatter()`',
      'viser-vue': 'ViserVue',
      // 'vue-code-diff': 'vue-code-diff',
      'vue-code-diff': '`请使用 NocCodeDiff`',
      'vue-container-query': 'VueContainerQuery',
      'vue-i18n': 'VueI18n',
      'vue-json-editor': '`请使用 NocEditorJson`',
      'vxe-table': 'VXETable',
      'xe-utils': 'XEUtils',
    })

    if (isVueCliBuild()) {
      config.plugins.push(
        ...[
          // Ignore all locale files of moment.js
          new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
          // 压缩成 .gz 文件
          /*
        new CompressionPlugin({
          filename: '[path][base].gz',
          algorithm: 'gzip',
          test: /\.js$|\.css$|\.html$/,
          threshold: 10240,
          minRatio: 0.8
        }),
        */
          new webpack.DefinePlugin({
            APP_VERSION: `"${require('./package.json').version}"`,
            GIT_HASH: JSON.stringify(getGitHash()),
            BUILD_DATE: buildDate,
          }),
          new webpack.NormalModuleReplacementPlugin(/msun-lib-ui[/\\]lib[/\\]styles[/\\](.*)\.css/),
          createThemeColorReplacerPlugin(),
        ]
      )
    }
  },
  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          // less vars，customize ant design theme
          // 'primary-color': '#F5222D',
          // 'link-color': '#F5222D',
          'border-radius-base': '2px',
        },
        // DO NOT REMOVE THIS LINE
        javascriptEnabled: true,
      },
    },
  },
  devServer: {
    open: true,
    port: 8080,
    proxy: {
      '/api': {
        // target: 'https://cloud-test-api.intsig.net/v1',
        target: 'https://cloud-test.intsig.net/api',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '',
        },
      },
      '/ospstorage': {
        target: 'https://cloud-fs-test-api.intsig.net',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/ospstorage': '',
        },
      },
      // 本地调试解决跨域，需要配置环境变量
      '/authapi': {
        target: process.env.VUE_APP_BASE_AUTH_PATH,
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/authapi': '',
        },
      },
      // 本地调试解决跨域，需要配置环境变量
      '/desktopapi': {
        target: 'https://sonar-test.intsig.net',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/desktopapi': '',
        },
      },
      '/filestorage': {
        target: 'https://cloud-test-storage.intsig.net',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/filestorage': '',
        },
      },
    },
  },
  // 暂时关闭 eslint，只依赖 IDE 做检测
  lintOnSave: false,
  /**
   * 开启 sourcemap
   * 使用命令 `tx sourcemap xxx.js:行号:列号` 分析报错信息
   * @see https://tianxuan.intsig.com/cli.md
   */
  productionSourceMap: true,
})
