import { login, getInfo, logout } from '@/api/login'
import { welcome } from '@/utils/util'
import { removeToken, setAccessToken } from '@/utils/auth'

const user = {
  state: {
    token: '',
    // x_token: '',
    cloud_token: '',
    name: '',
    email: '',
    welcome: '',
    avatar: '',
    roles: [],
    info: {},
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, { name, welcome }) => {
      state.name = name
      state.welcome = welcome
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_EMAIL: (state, email) => {
      state.email = email
    },
    SET_DIR: (state, redirect) => {
      state.redirect = redirect
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_INFO: (state, info) => {
      state.info = info
    },
  },

  actions: {
    SetRedirect({ commit }, redirect) {
      commit('SET_DIR', redirect)
    },

    // 登录
    Login({ commit }) {
      return new Promise((resolve, reject) => {
        login()
          .then(response => {
            const resData = response.Data
            setAccessToken(resData.token)

            commit('SET_NAME', { name: resData.name, welcome: welcome() })
            commit('SET_AVATAR', resData.avatar)
            commit('SET_EMAIL', resData.email)

            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 获取用户信息
    GetInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(response => {
            const userInfo = response.Data
            // if (result.role && result.role.permissions.length > 0) {
            //   const role = result.role
            //   role.permissions = result.role.permissions
            //   role.permissions.map(per => {
            //     if (per.actionEntitySet != null && per.actionEntitySet.length > 0) {
            //       const action = per.actionEntitySet.map(action => { return action.action })
            //       per.actionList = action
            //     }
            //   })
            //   role.permissionList = role.permissions.map(permission => { return permission.permissionId })
            //   commit('SET_ROLES', result.role)
            //   commit('SET_INFO', result)
            // } else {
            //   reject(new Error('getInfo: roles must be a non-null array !'))
            // }

            commit('SET_NAME', { name: userInfo.name, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            commit('SET_EMAIL', userInfo.email)

            resolve(response)
          })
          .catch(error => {
            // reject(error)
            removeToken(true)
          })
      })
    },

    // 登出
    Logout({ commit, state }) {
      return new Promise(resolve => {
        logout().then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          removeToken(true)
        })
        resolve()
      })
        .catch(() => {
          // resolve()
        })
        .finally(() => {})
    },
  },
}

export default user
