import { allOrgUser } from '@/api/ssoApi'
import cloneDeep from 'lodash.clonedeep'
import { onSSoLogout } from '@/utils/ssoLogout'

const org = {
  state: {
    userTree: [],
    departTree: [],
    allDepart: {},
    allUser: {},
  },

  mutations: {
    SET_ORG: (state, source) => {
      const allDepart_ = {}
      const allUser_ = {}
      const idFullPathMapper_ = {}
      function loop(data, onlyDept = false, result = [], path = [], pathName = []) {
        ;(data ?? []).forEach(item => {
          const { child_org = [], user_list = [] } = item
          ;[...(user_list ?? []), ...(child_org ?? [])].forEach(chunk => {
            const uniqueId = 'uid' in chunk ? chunk.uid : chunk.oid
            const wrapPath = [...path, uniqueId]
            const wrapPathName = [...pathName, chunk.name]
            const isUser = 'uid' in chunk
            const isExist = result.some(item => {
              return item.id === uniqueId
            })
            if (!isExist) {
              const value = wrapPath.join('/')
              if (idFullPathMapper_[uniqueId]) {
                idFullPathMapper_[uniqueId].push(value)
              } else {
                idFullPathMapper_[uniqueId] = [value]
              }
              if (isUser) {
                if (!onlyDept) {
                  const res = {
                    value: value,
                    id: uniqueId,
                    name: chunk.name,
                    isDepart: false,
                    department: wrapPathName.slice(0, -1).join('/'),
                    email: chunk.email,
                    avatar: chunk.thumb_avatar,
                  }
                  result.push(res)
                  allUser_[uniqueId] = res
                }
              } else {
                // 只选人员时： 过滤调空部分
                if (!onlyDept && child_org.length === 0 && user_list.length === 0) return
                const res = {
                  value: value,
                  id: uniqueId,
                  name: chunk.name,
                  isDepart: true,
                  checkable: undefined,
                  fullLabel: wrapPathName.join('/'),
                  children: loop([chunk], onlyDept, [], wrapPath, wrapPathName),
                }
                result.push(res)
                allDepart_[uniqueId] = res
              }
            }
          })
        })
        return result
      }

      state.userTree = loop(source)
      state.allDepart = cloneDeep(allDepart_)
      state.allUser = cloneDeep(allUser_)
      state.departTree = loop(source, true)
    },
  },

  actions: {
    // 获取组织架构
    GetAllOrgUser({ commit }) {
      return new Promise((resolve, reject) => {
        allOrgUser({ need_pre_entry: true, need_org: true, need_user: true })
          .then(response => {
            const { data, code, msg } = response?.data
            if (code === 0) {
              commit('SET_ORG', data ? [data] : [])
            } else {
              commit('SET_ORG', [])
              if (code === 10002002) {
                onSSoLogout({ code, msg })
              }
              throw Error(msg)
            }
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
  },
}

export default org
