function getUserInfo(field) {
  return noc.user.getUserInfo()[field]
}
const getters = {
  isMobile: state => state.app.isMobile,
  lang: state => state.app.lang,
  theme: state => state.app.theme,
  color: state => state.app.color,
  // token: state => state.user.token,
  // avatar: state => state.user.avatar,
  // email: state => state.user.email,
  // nickname: state => state.user.name,
  // name: state => state.user.name,
  avatar: () => getUserInfo('avatar'),
  email: () => getUserInfo('email'),
  nickname: () => getUserInfo('name'),
  name: () => getUserInfo('name'),
  org: () => getUserInfo('org'),
  dep: () => getUserInfo('dep'),
  token: () => noc.user.getToken(),
  uid: () => getUserInfo('uid'),
  welcome: state => state.user.welcome,
  roles: state => state.user.roles,
  userInfo: state => state.user.info,
  addRouters: state => state.permission.addRouters,
  multiTab: state => state.app.multiTab,
  clbStateInfo: state => state.app.clbStateInfo,
}

export default getters
