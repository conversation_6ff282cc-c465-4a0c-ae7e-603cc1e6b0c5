/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-20 14:38:10
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-04 14:50:54
 * @FilePath: \cloud_web\src\store\app-mixin.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import { mapState } from 'vuex'

const baseMixin = {
  computed: {
    ...mapState({
      layout: state => state.app.layout,
      navTheme: state => state.app.theme,
      primaryColor: state => state.app.color,
      colorWeak: state => state.app.weak,
      fixedHeader: state => state.app.fixedHeader,
      fixedSidebar: state => state.app.fixedSidebar,
      contentWidth: state => state.app.contentWidth,
      autoHideHeader: state => state.app.autoHideHeader,
      isMobile: state => state.app.isMobile,
      sideCollapsed: state => state.app.sideCollapsed,
      multiTab: state => state.app.multiTab,
      clbStateInfo: state => state.app.clbStateInfo,
    }),
    isTopMenu() {
      return this.layout === 'topmenu'
    },
  },
  methods: {
    isSideMenu() {
      return !this.isTopMenu
    },
  },
}

export { baseMixin }
