<template>
  <pro-layout
    ref="myPro"
    v-bind="layoutProps"
    v-model:openKeys="data.openKeys"
    v-model:selectedKeys="data.selectedKeys"
    :siderWidth="hiddenWidth"
    :headerRender="showHeader"
  >
    <template #headerContentRender>
      <div class="d-inline-flex align-items-center h-100">
        <span class="cursor-pointer pl-2 pr-3" @click="layoutProps.collapsed = !layoutProps.collapsed">
          <a-icon
            style="font-size: 16px"
            :type="layoutProps.collapsed ? 'MenuUnfoldOutlined' : 'MenuFoldOutlined'"
          ></a-icon>
        </span>
        <a-switch :checked="layoutProps.navTheme == 'light'" size="small" @change="themeChange" />
      </div>
    </template>
    <template #rightContentRender>
      <right-content :is-mobile="layoutProps.isMobile" :theme="layoutProps.navTheme" :top-menu="false" />
    </template>
    <!-- <template #footerRender>
      <GlobalFooter id="myFooters" />
    </template> -->
    <router-view />
  </pro-layout>
</template>

<script lang="ts" setup>
import { onMounted, provide, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { i18nRender } from '@/locales'
import { SIDEBAR_TYPE, TOGGLE_NAV_THEME } from '@/store/mutation-types'
import defaultSettings from '@/config/defaultSettings'
import RightContent from '@/components/GlobalHeader/RightContentNew.vue'
import GlobalFooter from '@/components/GlobalFooter/index.vue'
import { putUserTheme } from '@/api/login'

const data = reactive({
  openKeys: [],
  selectedKeys: [],
  // musk: document.getElementsByClassName('ant-drawer-mask'),
})
const hiddenWidth = ref(256)
let showHeader = ref(null)

const layoutProps = reactive({
  title: defaultSettings.title,
  logo: '/vendor-cdn/@img/logo.svg',
  navTheme: noc.store.getItem(TOGGLE_NAV_THEME),
  menuData: [],
  collapsed: noc.store.getItem('SIDEBAR_TYPE') == 'true',
  isMobile: false,
  locale: i18nRender,
  collapsedButtonRender: false,
  /**
   * 不能传 onCollapse 或绑定事件 @collapse
   */
})

const route = useRoute()

const store = useStore()

const routesState = store.getters.addRouters

function themeChange(checked) {
  layoutProps.navTheme = checked ? 'light' : 'dark'

  putUserTheme({ theme: layoutProps.navTheme }).then(response => {})
}

function menuCopy(item) {
  let newItem
  if (Array.isArray(item)) {
    newItem = []
    item.forEach(subItem => {
      newItem.push(menuCopy(subItem))
    })
  } else if (item && typeof item == 'object') {
    newItem = {}
    for (let key in item) {
      if (key == 'icon') {
        /**
         * 图标改为传入 VNode
         */
        if (item[key]) {
          newItem[key] = globalThis.AntdIconVue.createIconVNode(item[key])
        }
      } else {
        newItem[key] = menuCopy(item[key])
      }
    }
  } else {
    newItem = item
  }
  return newItem
}

function routeToMenu(route) {
  let menuData = []
  /**
   * 显示可见菜单
   */
  route.children.forEach(item => {
    if (!item.hidden) {
      let clone = menuCopy(item)
      if (noc.isArray(item.children)) {
        clone.children = routeToMenu(item)
      }
      menuData.push(clone)
    }
  })
  return menuData
}

function onRouteChange() {
  let { path } = route
  let openKey
  layoutProps.menuData.find(item => {
    if (Array.isArray(item.children)) {
      if (item.children.find(child => child.path == path)) {
        openKey = item.path
        return true
      }
    }
  })
  if (openKey && !data.openKeys.includes(openKey)) {
    data.openKeys.push(openKey)
  }
  data.selectedKeys = [path]
}
// function widthChange() {
//   if (layoutProps.collapsed) {
//     layoutProps.collapsed = false
//   }
//   hiddenWidth.value == 1 ? (hiddenWidth.value = 256) : (hiddenWidth.value = 1)
//   let headers = document.getElementsByClassName('ant-layout-header')
//   console.log(headers, 'headersheadersheaders')
//   const footer = document.getElementById('myFooters')
//   if (headers[0].style.display == 'none') {
//     footer.style.display = 'block'
//     headers[0].style.display = 'block'
//   } else {
//     footer.style.display = 'none'
//     headers[0].style.display = 'none'
//   }
// }

function toogleFull() {
  showHeader.value = false
  layoutProps.collapsed = false
  const footer = document.getElementById('myFooters')
  hiddenWidth.value = 0
  if (footer && footer.style) {
    footer.style.display = 'none'
  }
}
function toogleNormal() {
  layoutProps.collapsed = false
  const footer = document.getElementById('myFooters')
  hiddenWidth.value = 256
  footer.style.display = 'block'
}

provide('width', hiddenWidth)
provide('toogleFull', toogleFull)
provide('toogleNormal', toogleNormal)

watch(() => route.path, onRouteChange)

watch(
  routesState,
  routesState => {
    let topRoute: any = routesState.find((item: any) => item.path === '/')
    let menuData = []
    if (Array.isArray(topRoute?.children)) {
      menuData = routeToMenu(topRoute)
    }
    layoutProps.menuData = menuData
    onRouteChange()
  },
  {
    immediate: true,
  }
)

onMounted(() => {
  let a = noc.$('.ant-pro-sider-logo > a')[0]
  if (a) {
    a.href = '/homepage'
  }
  noc.$('.ant-drawer-mask')[0]?.addEventListener('click', muskClick)
})

watch(
  () => layoutProps.navTheme,
  theme => {
    noc.store.setItem('TOGGLE_NAV_THEME', theme)
  }
)

function muskClick() {
  layoutProps.collapsed = !layoutProps.collapsed
}

watch(
  () => layoutProps.collapsed,
  collapsed => {
    if (!collapsed) {
      setTimeout(() => {
        noc.$('.ant-drawer-mask')[0]?.addEventListener('click', muskClick)
      })
    } else {
      noc.$('.ant-drawer-mask')[0]?.removeEventListener('click', muskClick)
    }
    noc.store.setItem('SIDEBAR_TYPE', String(collapsed))
  }
)
</script>
<style lang="less">
#app {
  .ant-pro-sider {
    > .ant-layout-sider-children {
      height: 100vh;

      /* 火狐滚动条宽度，最小是 8px，此处统一，方便设置菜单位置 */

      ::-webkit-scrollbar {
        height: 8px;
        width: 8px;
      }

      ::-webkit-scrollbar-thumb {
        background-color: transparent;
        border-radius: 8px;
        outline: none;
      }

      /* 鼠标移动上去再显示滚动条 */

      :hover::-webkit-scrollbar-thumb {
        background-color: #556370;
      }

      ::-webkit-scrollbar-track-piece {
        // background-color: #02172b;
      }

      /* 火狐浏览器滚动条样式：只能设置宽度（常量）和颜色 */

      > div {
        scrollbar-color: transparent transparent;
        scrollbar-width: thin;

        &:hover {
          scrollbar-color: #556370 transparent;
        }
      }
    }

    + .ant-layout {
      max-height: 100vh;
      overflow: auto;
      scroll-behavior: smooth;
    }
  }

  .ant-pro-sider-light {
    ::-webkit-scrollbar-track-piece {
      background-color: #f0f2f5;
    }
  }

  .ant-pro-sider-logo {
    overflow-x: hidden;
  }

  .ant-pro-sider-menu {
    .ant-menu-sub.ant-menu-inline {
      background: transparent;
    }

    .ant-menu-item:first-child {
      margin-top: 0;
    }
  }

  /* ant-pro 限制内容区域直接子元素 ant-layout 最大高度，取消该限制 */

  .ant-pro-basicLayout-content {
    > .ant-layout {
      max-height: initial;
    }
  }
}
</style>
