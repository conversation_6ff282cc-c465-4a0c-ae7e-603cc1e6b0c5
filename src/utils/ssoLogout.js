import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'

function redirectSSO() {
  noc.store.remove(ACCESS_TOKEN)
  noc.user.logout()
  noc.sso.redirect()
}
export function onSSoLogout(res) {
  const { code, msg } = res
  if (code === 10002002) {
    console.log(msg)
    let seconds = 3 // 倒计时 3 秒
    let timer = null

    const modal = Modal.confirm({
      title: '无效的鉴权标志',
      icon: createVNode(ExclamationCircleOutlined),
      content: createVNode('div', {}, `SSO登录状态过期， ${seconds} 秒后将自动跳转登录页面.`),
      okText: `立即跳转`, // 动态更新按钮文本
      keyboard: false,
      centered: true,
      cancelButtonProps: {
        style: { display: 'none' }, // 忽略类型检查
      },
      onOk() {
        redirectSSO()
        clearInterval(timer) // 清除定时器
      },
    })

    // 倒计时逻辑
    timer = setInterval(() => {
      seconds -= 1
      modal.update({
        content: createVNode('div', {}, `SSO登录状态过期， ${seconds} 秒后将自动跳转登录页面.`),
      })

      // 倒计时结束后自动触发 onOk
      if (seconds <= 0) {
        clearInterval(timer)
        modal.destroy() // 关闭弹窗
        redirectSSO()
      }
    }, 1000)
  } else {
    message.error(msg)
  }
}
