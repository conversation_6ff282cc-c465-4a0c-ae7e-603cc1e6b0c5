/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2022-08-15 19:14:53
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-09-05 14:45:31
 * @FilePath: \cloud_web\src\utils\request.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import axios from 'axios'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { VueAxios } from './axios'
import { getAccessToken, removeToken } from '@/utils/auth'

// 创建 axios 实例
const desktopRequest = axios.create({
  baseURL: '/desktopapi',
  timeout: 2400000
})

// 异常拦截处理器
const errorHandler = (error) => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed'
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
desktopRequest.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
}, errorHandler)

// response interceptor
desktopRequest.interceptors.response.use((response) => {
  return response.data
}, errorHandler)

const installer = {
  vm: {},
  install (Vue) {
    Vue.use(VueAxios, desktopRequest)
  }
}

export default desktopRequest

export {
  installer as VueAxios,
  desktopRequest as axios
}
