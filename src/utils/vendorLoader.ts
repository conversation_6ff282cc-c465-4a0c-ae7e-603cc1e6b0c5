import { loader as vendorLoader } from '@aim/helper'
const { load } = vendorLoader

const global = globalThis as any

vendorLoader.register({
  /**
   * TODO，删除代码，2023.12
   */
  /*
  '@antv/data-set': {
    name: 'DataSet',
    url: '@antv~data-set@0.11.8/data-set.min.js',
  },
  */
  '@antv/g2': {
    name: 'G2',
    url: '@antv~g2@3.5.19/g2.min.js',
    /**
     * TODO，G2 4.0 升级指南
     * https://antv-g2.gitee.io/zh/docs/manual/upgrade
     */
    // url: '@antv~g2@4.2.10/g2.min.js',
  },
  /**
   * 恢复diff库支持
   */
  diff: {
    name: 'JsDiff',
    url: 'diff@3.5.0/diff.min.js',
  },
  diff2html: {
    name: 'Diff2Html',
    url: [
      //
      'diff2html@3.4.31/diff2html.min.css',
      'diff2html@3.4.31/diff2html.min.js',
    ],
  },
  'highlight.js': {
    name: 'hljs',
    url: [
      //
      'highlightjs@11.7.0/build/styles/vs.min.css',
      'highlightjs@11.7.0/build/highlight.min.js',
    ],
  },
  'js-base64': {
    name: 'Base64',
    url: 'js-base64@3.7.5/base64.js',
  },
  jszip: {
    name: 'JSZip',
    url: 'jszip@3.10.1/jszip.min.js',
  },
  'sql-formatter': {
    name: 'sqlFormatter',
    url: 'sql-formatter@13.1.0/sql-formatter.min.js',
  },
  /**
   * 已封装到组件 NocCodeDiff
   */
  'vue-code-diff': {
    name: 'CodeDiff',
    url: [
      //
      'vue-code-diff@1.8.0/index.umd.js',
    ],
  },
  /**
   * 已封装到组件 NocEditorJson
   */
  'vue-json-editor': {
    name: 'JSONEditor',
    url: [
      //
      'vue-json-editor@1.4.3/jsoneditor.min.css',
      'vue-json-editor@1.4.3/jsoneditor.min.js?t=202311',
    ],
  },
  xlsx: {
    name: 'XLSX',
    url: 'xlsx@0.18.5/xlsx.full.min.js',
  },
})

// export const loadAntdDataSet = () => load('@antv/data-set')
export const loadAntdG2 = () => load('@antv/g2')
export const loadJSZip = () => load('jszip')
export const loadSqlFormatter = () => load('sql-formatter')
export const loadVueCodeDiff = async () => {
  let [hljs] = await Promise.all([load('highlight.js')])
  window['highlight.js'] = hljs
  return load('vue-code-diff')
}
export const loadXLSX = () => load('xlsx')
export const vendorLoad = load

global.vendorLoad = load
global.vendorLoader = vendorLoader
