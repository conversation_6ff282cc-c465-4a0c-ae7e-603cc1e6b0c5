import Guacamole from 'guacamole-common-js'

const touchpad = function (element) {
  /**
   * Reference to this Guacamole.Mouse.Touchpad.
   * @private
   */
  var guacTouchpad = this
  /**
   * The distance a two-finger touch must move per scrollwheel event, in
   * pixels.
   */
  this.scrollThreshold = 20 * (window.devicePixelRatio || 1)
  /**
   * The maximum number of milliseconds to wait for a touch to end for the
   * gesture to be considered a click.
   */
  this.clickTimingThreshold = 250
  /**
   * The maximum number of pixels to allow a touch to move for the gesture to
   * be considered a click.
   */
  this.clickMoveThreshold = 10 * (window.devicePixelRatio || 1)
  /**
   * The current mouse state. The properties of this state are updated when
   * mouse events fire. This state object is also passed in as a parameter to
   * the handler of any mouse events.
   *
   * @type {Guacamole.Mouse.State}
   */
  this.currentState = new Guacamole.Mouse.State(
    0,
    0,
    false,
    false,
    false,
    false,
    false
  )
  /**
   * Fired whenever a mouse button is effectively pressed. This can happen
   * as part of a "click" gesture initiated by the user by tapping one
   * or more fingers over the touchpad element, as part of a "scroll"
   * gesture initiated by dragging two fingers up or down, etc.
   *
   * @event
   * @param {Guacamole.Mouse.State} state The current mouse state.
   */
  this.onmousedown = null
  /**
   * Fired whenever a mouse button is effectively released. This can happen
   * as part of a "click" gesture initiated by the user by tapping one
   * or more fingers over the touchpad element, as part of a "scroll"
   * gesture initiated by dragging two fingers up or down, etc.
   *
   * @event
   * @param {Guacamole.Mouse.State} state The current mouse state.
   */
  this.onmouseup = null
  /**
   * Fired whenever the user moves the mouse by dragging their finger over
   * the touchpad element.
   *
   * @event
   * @param {Guacamole.Mouse.State} state The current mouse state.
   */
  this.onmousemove = null
  var touchCount = 0
  var lastTouchX = 0
  var lastTouchY = 0
  var lastTouchTime = 0
  var pixelsMoved = 0
  var touchButtons = {
    1: 'left',
    2: 'right',
    3: 'middle'
  }
  var gestureInIProgress = false
  var clickReleaseTimeout = null
  element.addEventListener(
    'touchend',
    function (e) {
      e.preventDefault()

      // If we're handling a gesture AND this is the last touch
      if (gestureInIProgress && e.touches.length === 0) {
        var time = new Date().getTime()
        // Get corresponding mouse button
        var button = touchButtons[touchCount]
        // If mouse already down, release anad clear timeout
        if (guacTouchpad.currentState[button]) {
          // Fire button up event
          guacTouchpad.currentState[button] = false
          if (guacTouchpad.onmouseup) { guacTouchpad.onmouseup(guacTouchpad.currentState) }
          // Clear timeout, if set
          if (clickReleaseTimeout) {
            window.clearTimeout(clickReleaseTimeout)
            clickReleaseTimeout = null
          }
        }
        // If single tap detected (based on time and distance)
        if (
          time - lastTouchTime <= guacTouchpad.clickTimingThreshold &&
          pixelsMoved < guacTouchpad.clickMoveThreshold
        ) {
          // Fire button down event
          guacTouchpad.currentState[button] = true
          if (guacTouchpad.onmousedown) { guacTouchpad.onmousedown(guacTouchpad.currentState) }
          // Delay mouse up - mouse up should be canceled if
          // touchstart within timeout.
          clickReleaseTimeout = window.setTimeout(function () {
            // Fire button up event
            guacTouchpad.currentState[button] = false
            if (guacTouchpad.onmouseup) { guacTouchpad.onmouseup(guacTouchpad.currentState) }

            // Gesture now over
            gestureInIProgress = false
          }, guacTouchpad.clickTimingThreshold)
        }
        // If we're not waiting to see if this is a click, stop gesture
        if (!clickReleaseTimeout) gestureInIProgress = false
      }
    },
    false
  )
  element.addEventListener(
    'touchstart',
    function (e) {
      e.preventDefault()
      // Track number of touches, but no more than three
      touchCount = Math.min(e.touches.length, 3)
      // Clear timeout, if set
      if (clickReleaseTimeout) {
        window.clearTimeout(clickReleaseTimeout)
        clickReleaseTimeout = null
      }
      // Record initial touch location and time for touch movement
      // and tap gestures
      if (!gestureInIProgress) {
        // Stop mouse events while touching
        gestureInIProgress = true
        // Record touch location and time
        var startingTouch = e.touches[0]
        lastTouchX = startingTouch.clientX
        lastTouchY = startingTouch.clientY
        lastTouchTime = new Date().getTime()
        pixelsMoved = 0
      }
    },
    false
  )
  element.addEventListener(
    'touchmove',
    function (e) {
      e.preventDefault()
      // Get change in touch location
      var touch = e.touches[0]
      var deltaX = touch.clientX - lastTouchX
      var deltaY = touch.clientY - lastTouchY
      // Track pixels moved
      pixelsMoved += Math.abs(deltaX) + Math.abs(deltaY)
      // If only one touch involved, this is mouse move
      if (touchCount === 1) {
        // Calculate average velocity in Manhatten pixels per millisecond
        var velocity = pixelsMoved / (new Date().getTime() - lastTouchTime)
        // Scale mouse movement relative to velocity
        var scale = 1 + velocity
        // Update mouse location
        guacTouchpad.currentState.x += deltaX * scale
        guacTouchpad.currentState.y += deltaY * scale
        // Prevent mouse from leaving screen
        if (guacTouchpad.currentState.x < 0) guacTouchpad.currentState.x = 0
        else if (guacTouchpad.currentState.x >= element.offsetWidth) { guacTouchpad.currentState.x = element.offsetWidth - 1 }
        if (guacTouchpad.currentState.y < 0) guacTouchpad.currentState.y = 0
        else if (guacTouchpad.currentState.y >= element.offsetHeight) { guacTouchpad.currentState.y = element.offsetHeight - 1 }
        // Fire movement event, if defined
        if (guacTouchpad.onmousemove) { guacTouchpad.onmousemove(guacTouchpad.currentState) }
        // Update touch location
        lastTouchX = touch.clientX
        lastTouchY = touch.clientY
      } else if (touchCount === 2) {
        // If change in location passes threshold for scroll
        if (Math.abs(deltaY) >= guacTouchpad.scrollThreshold) {
          // Decide button based on Y movement direction
          var button
          if (deltaY > 0) button = 'down'
          else button = 'up'
          // Fire button down event
          guacTouchpad.currentState[button] = true
          if (guacTouchpad.onmousedown) { guacTouchpad.onmousedown(guacTouchpad.currentState) }
          // Fire button up event
          guacTouchpad.currentState[button] = false
          if (guacTouchpad.onmouseup) { guacTouchpad.onmouseup(guacTouchpad.currentState) }
          // Only update touch location after a scroll has been
          // detected
          lastTouchX = touch.clientX
          lastTouchY = touch.clientY
        }
      }
    },
    false
  )
}

export default {
  touchpad
}
