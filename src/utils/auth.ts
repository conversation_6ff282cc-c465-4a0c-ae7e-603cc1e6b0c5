import { getSsoExpirySecond } from '@/config'
import { ACCESS_TOKEN } from '@/store/mutation-types'

export function getAccessToken() {
  return noc.store.getItem(ACCESS_TOKEN)
}

export function setAccessToken(token) {
  noc.store.setItem(ACCESS_TOKEN, token)
}

export function getToken() {
  return noc.user.getToken()
}

export function removeToken(redirectHome = false) {
  noc.store.remove(ACCESS_TOKEN)
  noc.user.logout()
  if (redirectHome) {
    location.href = '/'
  }
}

export function setToken(token) {
  noc.user.login({
    expiry: Date.now() / 1000 + getSsoExpirySecond(),
    token: token,
  })
}
