class Strings {
  hasText = function (text) {
    return !(text === undefined || text === null || text.length === 0)
  }

  zeroPad = function zeroPad (num, minLength) {
    let str = num.toString()
    while (str.length < minLength) { str = '0' + str }
    return str
  };
}

const strings = new Strings()

class Times {
  formatTime = function formatTime (millis) {
    const totalSeconds = Math.floor(millis / 1000)

    // Split into seconds and minutes
    const seconds = totalSeconds % 60
    const minutes = Math.floor(totalSeconds / 60)

    // Format seconds and minutes as MM:SS
    return strings.zeroPad(minutes, 2) + ':' + strings.zeroPad(seconds, 2)
  };
}

const times = new Times()
export default times
