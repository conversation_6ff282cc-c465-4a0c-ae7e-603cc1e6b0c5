/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-08-29 18:07:11
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-08-29 18:07:34
 * @FilePath: \cloud_web\src\utils\feedback.ts
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import { getSsoPlatformId } from '@/config'

/**
 * 意见反馈，初始化配置
 */
export function feedbackInit() {
  let feedback
  let global = globalThis as any
  let i = 0

  setTimeout(loadCheck)

  function loadCheck() {
    i++
    feedback = global.feedback
    if (feedback || i > 100) {
      load()
    } else {
      setTimeout(loadCheck, 100)
    }
  }

  function load() {
    if (!feedback) {
      return
    }

    let { options } = feedback
    options.env = noc.isOnline() ? 'online' : 'test'
    options.pid = getSsoPlatformId()
    options.token = noc.user.getToken()
  }
}
