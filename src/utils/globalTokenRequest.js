/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-15 19:14:53
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-26 11:01:03
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import axios from 'axios'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { VueAxios } from './axios'
import { getApiCloudBase } from '@/config'
import { getAccessToken, removeToken } from '@/utils/auth'

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: getApiCloudBase(),
  timeout: 120000, // 请求超时时间
})

// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      console.log(data, '1dd1d1d1d1111')
      notification.error({
        message: 'Forbidden',
        description: data.message,
      })
      if (data.message && data.message == 'global token is invalid') {
        window.location.reload()
      }
    }
    if (error.response.status === 500 || error.response.status === 400 || data.code === 400 || data.code === 504) {
      if (data.message && data.message !== '' && data.reason !== '') {
        notification.error({
          message: data.message,
          // description: data.reason,
        })
      } else {
        let message = data.message
        if (data.msg !== undefined) {
          message = data.msg
        }
        notification.error({
          description: message,
        })
      }
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

function getCustomHeader() {
  const customHeader = {}
  // 根据需求动态设置请求头
  const token = localStorage.getItem('globalToken')
  if (token) {
    customHeader.GlobalToken = `${token}`
  }
  // 可以继续添加其他自定义请求头
  return customHeader
}
// request interceptor
request.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }

  const customHeader = getCustomHeader() // 自定义获取请求头的函数
  // 添加自定义请求头
  if (customHeader) {
    config.headers = { ...config.headers, ...customHeader }
  }

  return config
}, errorHandler)

// response interceptor
request.interceptors.response.use(response => {
  return response.data
}, errorHandler)

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, request)
  },
}

export const globalTokenRequest = request

export { installer as VueAxios, request as axios }
