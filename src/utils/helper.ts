/**
 * 字符串反序列化成对象，如果已经是对象则原样返回
 * @param s 待转换反序列化的字符串
 */
export function stringToObject<T = any>(s: string): T {
  let res = s as any
  try {
    if (noc.isString(res)) {
      res = JSON.parse(res)
    }
  } catch (e) {}
  if (!res || typeof res != 'object') {
    res = {}
  }
  return res
}

/**
 * 域名格式验证
 * @param s 待验证的字符串
 * @return 验证通过返回空字符串，验证不通过返回对应消息
 */
export function validateDomain(s): string {
  let message = ''
  s = s.trim()
  if (s == '') {
    message = '请输入解析记录'
  } else {
    if (/^([\w-]+\.)+[a-zA-Z]+$/.test(s) && !/^(\d+\.)+\d+$/.test(s)) {
      // 格式符合
    } else {
      message = '域名语法格式不正确，请填写一个域名，如: cloud.intsig.com'
    }
  }
  return message
}

/**
 * IP格式验证
 * @param s 待验证的字符串
 * @return 验证通过返回空字符串，验证不通过返回对应消息
 */
export function validateIntranetIP(s): string {
  let message = ''
  s = s.trim()
  if (s == '') {
    message = '请输入解析记录'
  } else {
    if (/^(\d{1,3}\.){3}\d+$/.test(s) && (s.startsWith('10.') || s.startsWith('172.16.') || s.startsWith('192.168'))) {
      // 格式符合
    } else {
      message = 'IP语法格式不正确，请输入IP，格式：**************'
    }
  }
  return message
}
