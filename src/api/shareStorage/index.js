/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-05-06 18:28:41
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-05-15 14:09:51
 * @FilePath: \cloud_web\src\api\shareStorage\index.js
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import request from '@/utils/request'
export function getOspShareNames (parameter) {
 return request({
  url: '/storage/osp_share/names_for_join',
  method: 'get',
  params: parameter
 })
}

export function ospShareNamesExist (parameter) {
 return request({
  url: '/storage/osp_share/exist',
  method: 'get',
  params: parameter
 })
}
