import request from '@/utils/request'

const urls = {
  instances: '/bff/clb/instances',
  instance: '/bff/clb/instance',
  delete: '/bff/clb/instance/',
  clusterVips: '/bff/clb/instance/vips',
}

export const getInstanceListData = function (params) {
  return request({
    method: 'get',
    url: urls.instances,
    params: params,
  })
}

export const deleteClbInstance = function (params) {
  return request({
    method: 'delete',
    url: urls.delete + params.instanceId,
  })
}

export const listClusterVips = function (params) {
  return request({
    method: 'get',
    url: urls.clusterVips,
    params: params,
  })
}

export const addInstance = function (data) {
  return request({
    method: 'post',
    url: urls.instance,
    data: data,
  })
}

export const updateInstance = function (data) {
  return request({
    method: 'put',
    url: urls.instance,
    data: data,
  })
}
