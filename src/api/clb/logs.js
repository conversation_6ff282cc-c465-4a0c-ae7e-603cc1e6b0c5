import request from '@/utils/request'
const urls = {
  getLogsList: '/bff/clb/list/opt_log_list',
  reportLogList: '/bff/clb/list/report_log_list',
  dpvsLog: '/bff/clb/list/dpvs-log',
  node: '/bff/clb/list/node',
}
export const getLogsList = function (params) {
  return request({
    method: 'get',
    url: urls.getLogsList,
    params: params,
  })
}
export const getReportLogList = function (params) {
  return request({
    method: 'get',
    url: urls.reportLogList,
    params: params,
  })
}

export const getDpvsLogList = function (params) {
  return request({
    method: 'get',
    url: urls.dpvsLog,
    params: params,
  })
}

export const getNodeList = function (params) {
  return request({
    method: 'get',
    url: urls.node,
    params: params,
  })
}
