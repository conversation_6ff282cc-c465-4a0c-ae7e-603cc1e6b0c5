import request from '@/utils/request'

const urls = {
  clusters: '/bff/clb/cluster',
  vips_nodes: '/bff/clb/nodes_vips',
  clusterList: '/bff/clb/list/cluster',
  node: '/bff/clb/node/',
  cluster: '/bff/clb/cluster/',
  vips: '/bff/clb/vips',
  nodes: '/bff/clb/node',
  monitorData: '/bff/clb/monitor/charts'
}

export const getClusterData = function (params) {
  return request({
    method: 'get',
    url: urls.clusters,
    params: params,
  })
}

export const listVips = function () {
  return request({
    method: 'get',
    url: urls.vips,
  })
}

export const addCluster = function (data) {
  return request({
    method: 'post',
    url: urls.clusters,
    data: data,
  })
}

export const upateNode = function (data) {
  return request({
    method: 'put',
    url: urls.nodes,
    data: data,
  })
}
export const upateCluster = function (data) {
  return request({
    method: 'put',
    url: urls.clusters,
    data: data,
  })
}

export const deleteClbClusterData = function (params) {
  return request({
    method: 'delete',
    url: urls.cluster + params.clusterId,
  })
}

export const getClusterListData = function (params) {
  return request({
    method: 'get',
    url: urls.clusterList,
    params: params,
  })
}
export const getClbNodeData = function (params) {
  return request({
    method: 'get',
    url: urls.node + params.nodeId,
  })
}

export const deleteClbNodeData = function (params) {
  return request({
    method: 'delete',
    url: urls.node + params.nodeId,
  })
}
export const addClbNode = function (data) {
  return request({
    method: 'post',
    url: urls.nodes,
    data: data,
  })
}
export const  getMonitorData = function (data) {
  return request({
    method: 'post',
    url: urls.monitorData,
    data: data,
  })
}

export const getClusterVipNodes = function (params) {
  return request({
    method: 'get',
    url: urls.vips_nodes,
    params: params,
  })
}
// export const dblConfirm = function (data) {
//   return request({
//     method: 'post',
//     url: urls.ddlConfirm,
//     data: data
//   })
// }

// export const dbPasswdCheck = function (data) {
//   return request({
//     method: 'post',
//     url: urls.dbPasswdCheck,
//     data: data
//   })
// }

// export const dmlDataLevels = function (data) {
//   return request({
//     method: 'post',
//     url: urls.dbLevel,
//     data: data
//   })
// }
// export const dbSqlSingle = function (data) {
//   return request({
//     method: 'post',
//     url: urls.retry,
//     data: data
//   })
// }

// export const allErrorRetry = function (params) {
//   return request({
//     method: 'get',
//     url: urls.allRetry,
//     params: params
//   })
// }
