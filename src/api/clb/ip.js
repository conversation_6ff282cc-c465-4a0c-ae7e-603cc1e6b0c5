import request from '@/utils/request'

const urls = {
  ips: '/bff/clb/ips',
  ip: '/bff/clb/vip',
  lips: '/bff/clb/ips/use',
  bond: '/bff/clb/ips/bond',
}

export const getIpsData = function (params) {
  return request({
    method: 'get',
    url: urls.ips,
    params: params,
  })
}

export const creatIp = function (data) {
  return request({
    method: 'post',
    url: urls.ip,
    data: data,
  })
}

export const deleteIp = function (data) {
  return request({
    method: 'delete',
    url: urls.ip + `/${data.id}`,
  })
}

export const getLipsData = function (params) {
  return request({
    method: 'get',
    url: urls.lips,
    params: params,
  })
}

export const bondLip = function (data) {
  return request({
    method: 'post',
    url: urls.bond,
    data: data,
  })
}

export const getbondLip = function (params) {
  return request({
    method: 'get',
    url: urls.bond,
    params: params,
  })
}


export const deletebondLip = function (params) {
  return request({
    method: 'delete',
    url: urls.bond,
    params: params,
  })
}