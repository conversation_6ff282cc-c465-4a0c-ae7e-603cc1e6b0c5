import request from '@/utils/request'

const urls = {
  listens: '/bff/clb/listens',
  weight: '/bff/clb/listen/weight',
  status: '/bff/clb/listen/status',
  listenServe: '/bff/clb/listen/server',
  listen: '/bff/clb/listen',
  ips: '/bff/clb/listen/ips',
  clusterInstance:'/bff/clb/listen/cluster-instances'
}

export const getClbClusterInstance = function () {
  return request({
    method: 'get',
    url: urls.clusterInstance,
  })
}
export const getClbListenServerData = function (params) {
  return request({
    method: 'get',
    url: urls.listens,
    params: params,
  })
}

export const getClbIpsData = function (params) {
  return request({
    method: 'get',
    url: urls.ips,
    params: params,
  })
}

export const addListen = function (data) {
  return request({
    method: 'post',
    url: urls.listen,
    data: data,
  })
}

export const updateListen = function (data) {
  return request({
    method: 'put',
    url: urls.listen,
    data: data,
  })
}

export const updateListenWeight = function (data) {
  return request({
    method: 'put',
    url: urls.weight,
    data: data,
  })
}

export const updateListenStatus = function (data) {
  return request({
    method: 'put',
    url: urls.status,
    data: data,
  })
}
export const deleteListen = function (params) {
  return request({
    method: 'delete',
    url: urls.listen,
    params: params,
  })
}
export const deleteListenServe = function (params) {
  return request({
    method: 'delete',
    url: urls.listenServe,
    params: params,
  })
}

export const createListenServe = function (data) {
  return request({
    method: 'post',
    url: urls.listenServe,
    data: data,
  })
}


// export const dblUpload = function (data) {
//   return request({
//     method: 'post',
//     url: urls.dblUpload,
//     data: data
//   })
// }
// export const dblDownloadload = function (params) {
//   return request({
//     method: 'get',
//     url: urls.dblDownloadload,
//     params: params
//   })
// }
// export const dblConfirm = function (data) {
//   return request({
//     method: 'post',
//     url: urls.ddlConfirm,
//     data: data
//   })
// }

// export const dbPasswdCheck = function (data) {
//   return request({
//     method: 'post',
//     url: urls.dbPasswdCheck,
//     data: data
//   })
// }

// export const dmlDataLevels = function (data) {
//   return request({
//     method: 'post',
//     url: urls.dbLevel,
//     data: data
//   })
// }
// export const dbSqlSingle = function (data) {
//   return request({
//     method: 'post',
//     url: urls.retry,
//     data: data
//   })
// }

// export const allErrorRetry = function (params) {
//   return request({
//     method: 'get',
//     url: urls.allRetry,
//     params: params
//   })
// }
