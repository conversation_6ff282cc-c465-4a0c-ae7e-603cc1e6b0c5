import request from '@/utils/request'

const cronTaskConfigApi = {
  List: '/bff/cron/config',
  Create: '/bff/cron/config',
  Update: '/bff/cron/config',
  Delete: '/bff/cron/config',
  Run: '/bff/cron/run',
  Get: '/bff/cron/config/',
}

export function getCronTaskConfigList(parameter) {
  return request({
    url: cronTaskConfigApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getCronTaskConfigInfo(id) {
  return request({
    url: `${cronTaskConfigApi.Get}${id}`,
    method: 'get',
  })
}

export function createCronTaskConfig(data) {
  return request({
    url: cronTaskConfigApi.Create,
    method: 'post',
    data: data,
  })
}

export function updateCronTaskConfig(data) {
  return request({
    url: cronTaskConfigApi.Update,
    method: 'put',
    data: data,
  })
}

export function deleteCronTaskConfig(data) {
  return request({
    url: `${cronTaskConfigApi.Delete}/${data.name}`,
    method: 'delete',
    data: data,
  })
}

export function runCronTaskAtOnce(data) {
  return request({
    url: cronTaskConfigApi.Run,
    method: 'post',
    data: data,
  })
}
