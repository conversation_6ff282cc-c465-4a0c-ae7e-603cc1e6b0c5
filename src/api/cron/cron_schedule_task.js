import request from '@/utils/request'

const cronScheduleTaskApi = {
  List: '/bff/cron/schedule/task',
  Create: '/bff/cron/schedule/task',
  Update: '/bff/cron/schedule/task',
  Delete: '/bff/cron/schedule/task',
  ListName: '/bff/cron/schedule/task_name',
  ListCreator: '/bff/cron/schedule/task_creator',
  Get: '/bff/cron/schedule/task/',
}

export function getCronScheduleTaskList(parameter) {
  return request({
    url: cronScheduleTaskApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getCronScheduleTaskNameList() {
  return request({
    url: cronScheduleTaskApi.ListName,
    method: 'get',
    params: {},
  })
}

export function getCronScheduleTaskCreatorList() {
  return request({
    url: cronScheduleTaskApi.ListCreator,
    method: 'get',
    params: {},
  })
}

export function getCronScheduleTaskInfo(id) {
  return request({
    url: `${cronScheduleTaskApi.Get}${id}`,
    method: 'get',
  })
}

export function creatCronScheduleTask(data) {
  return request({
    url: cronScheduleTaskApi.Create,
    method: 'post',
    data: data,
  })
}

export function updateCronScheduleTask(data) {
  return request({
    url: cronScheduleTaskApi.Update,
    method: 'put',
    data: data,
  })
}

export function deleteCronScheduleTask(data) {
  return request({
    url: `${cronScheduleTaskApi.Delete}/${data.id}`,
    method: 'delete',
    data: data,
  })
}
