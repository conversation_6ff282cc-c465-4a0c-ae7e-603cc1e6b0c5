import request from '@/utils/request'

const cronScheduleJobApi = {
  List: '/bff/cron/schedule/job',
  Create: '/bff/cron/schedule/job',
  Update: '/bff/cron/schedule/job',
  Delete: '/bff/cron/schedule/job',
  Run: '/bff/cron/schedule/job/run',
  Get: '/bff/cron/schedule/job/',
  ListCreator: '/bff/cron/schedule/job_creator',
}

export function getCronScheduleJobList(parameter) {
  return request({
    url: cronScheduleJobApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getCronScheduleJobInfo(id) {
  return request({
    url: `${cronScheduleJobApi.Get}${id}`,
    method: 'get',
  })
}

export function createCronScheduleJob(data) {
  return request({
    url: cronScheduleJobApi.Create,
    method: 'post',
    data: data,
  })
}

export function updateCronScheduleJob(data) {
  return request({
    url: cronScheduleJobApi.Update,
    method: 'put',
    data: data,
  })
}

export function deleteCronScheduleJob(data) {
  return request({
    url: `${cronScheduleJobApi.Delete}/${data.id}`,
    method: 'delete',
    data: data,
  })
}

export function runCronScheduleJoAtOnce(data) {
  return request({
    url: cronScheduleJobApi.Run,
    method: 'post',
    data: data,
  })
}

export function getCronScheduleJobCreatorList() {
  return request({
    url: cronScheduleJobApi.ListCreator,
    method: 'get',
    params: {},
  })
}
