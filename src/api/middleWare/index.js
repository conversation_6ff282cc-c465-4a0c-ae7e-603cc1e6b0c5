/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-12-30 15:52:23
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-31 10:08:23
 * @FilePath: \cloud_web\src\api\middleWare\index.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const Api = {
  MiddlewareQuery: '/bff/user/middleware/manager',
  CreateMiddleWare: '/bff/user/middleware/manager',
  UpdateMiddleWare: '/bff/user/middleware/manager',
  DeleteMiddleWare: '/bff/user/middleware/manager',
  UserMiddleWare: '/bff/user/middleware/menu',
}

export function middlewareQuery(parameter) {
  return request({
    url: Api.MiddlewareQuery,
    method: 'get',
    params: parameter,
  })
}

export function createMiddleWare(data) {
  return request({
    url: Api.CreateMiddleWare,
    method: 'post',
    data: data,
  })
}

export function updateMiddleWare(params) {
  return request({
    url: Api.UpdateMiddleWare,
    method: 'put',
    data: params,
  })
}

export function deleteMiddleWare(data) {
  return request({
    url: Api.DeleteMiddleWare,
    method: 'delete',
    params: data,
  })
}
export function userMiddleWare(data) {
  return request({
    url: Api.UserMiddleWare,
    method: 'get',
    params: data,
  })
}
