/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-09-03 17:30:47
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-09-10 14:06:55
 * @FilePath: \cloud_web\src\api\devops\cloudChrome.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const ccdApi = {
  createExt: '/bff/coder/cloud-chrome-extension/create',
  upDate: '/bff/coder/cloud-chrome-extension/re-create',
  statusExt: '/bff/coder/cloud-chrome-extension',
  DownloadAssetFile: '/coder/cloud-chrome-extension/download',
}

export function statusExt(parameter) {
  return request({
    url: ccdApi.statusExt,
    method: 'get',
    params: parameter,
  })
}

export function createExt(data) {
  return request({
    url: `${ccdApi.createExt}`,
    method: 'post',
    data: data,
  })
}
export function upDate(data) {
  return request({
    url: `${ccdApi.upDate}`,
    method: 'post',
    data: data,
  })
}
import axios from 'axios'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { getAccessToken, removeToken } from '@/utils/auth'
import { getApiCloudBase } from '@/config'
const assetDownloadRequest = axios.create({
  // API 请求的默认前缀
  baseURL: getApiCloudBase(),
  timeout: 60000000, // 请求超时时间
})
// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message,
      })
    }
    if (error.response.status === 500 || error.response.status === 400 || data.code === 400) {
      notification.error({
        message: 'error',
        description: data.message,
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
assetDownloadRequest.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
}, errorHandler)

// response interceptor
assetDownloadRequest.interceptors.response.use(response => {
  return response.data
}, errorHandler)
export function downloadAssetFile(params) {
  return assetDownloadRequest({
    url: `${ccdApi.DownloadAssetFile}`,
    method: 'get',
    params: params,
    responseType: 'blob',
  })
}
