import request from '@/utils/request'

const termApi = {
  TermConfigAdminList: '/bff/coder/term/config/admin',
  TermConfigList: '/bff/coder/term/config',
  CreateTermConfig: '/bff/coder/term/config/create',
  GetTermConfig: '/bff/coder/term/config/info/',
  UpdateTermConfig: '/bff/coder/term/config/update',
  DeleteTermConfig: '/bff/coder/term/config/',
  GetTermMonitor: '/bff/coder/term/monitor/info/',
  TermRecList: '/bff/coder/term/rec',
  CreateTermMenu: '/bff/coder/term/menu/create',
  DeleteTermMenu: '/bff/coder/term/menu/delete',
  UpdateTermMenu: '/bff/coder/term/menu/update',
  UpdateTermMenuName: '/bff/coder/term/menu/update/name',
  UpdateTermMenuContent: '/bff/coder/term/menu/update/content',
  GetTermDashboard: '/bff/coder/term/dashboard',
  TermCommandList: '/bff/coder/term/command',
  TermUserCommand: '/bff/coder/term/user/command',
  TermCommonCommandAdminList: '/bff/coder/term/common/command/admin',
  TermCommonCommandList: '/bff/coder/term/common/command',
  CreateTermCommonCommand: '/bff/coder/term/common/command/create',
  GetTermCommonCommand: '/bff/coder/term/common/command/info/',
  UpdateTermCommonCommand: '/bff/coder/term/common/command/update',
  DeleteTermCommonCommand: '/bff/coder/term/common/command/',
  TermUserCommonCommand: '/bff/coder/term/common/command/used',
  AllTermGatewayList: '/bff/coder/term/gateway/all',
  TermGatewayList: '/bff/coder/term/gateway',
  CreateTermGateway: '/bff/coder/term/gateway/create',
  GetTermGateway: '/bff/coder/term/gateway/info/',
  UpdateTermGateway: '/bff/coder/term/gateway/update',
  DeleteTermGateway: '/bff/coder/term/gateway/',
  SendPlantform: '/bff/auth/codenumber/send',
  CheckCode: '/bff/auth/codenumber/check',
  ListTermQuickCommand: '/bff/coder/term/quick/command',
  CreateTermQuickCommand: '/bff/coder/term/quick/command',
  GetTermQuickCommand: '/bff/coder/term/quick/command/info/',
  UpdateTermQuickCommand: '/bff/coder/term/quick/command',
  DeleteTermQuickCommand: '/bff/coder/term/quick/command/',
  updateUserPlugin: '/bff/coder/term/plugin/update',

}

export function termConfigAdminList(parameter) {
  return request({
    url: termApi.TermConfigAdminList,
    method: 'get',
    params: parameter,
  })
}
export function checkCode(parameter) {
  return request({
    url: termApi.CheckCode,
    method: 'get',
    params: parameter,
  })
}
export function sendPlantform(parameter) {
  return request({
    url: termApi.SendPlantform,
    method: 'put',
    data: parameter,
  })
}
export function termConfigList(parameter) {
  return request({
    url: termApi.TermConfigList,
    method: 'get',
    params: parameter,
  })
}

export function getTermConfigInfo(id) {
  return request({
    url: `${termApi.GetTermConfig}${id}`,
    method: 'get',
  })
}

export function createTermConfig(data) {
  return request({
    url: `${termApi.CreateTermConfig}`,
    method: 'post',
    data: data,
  })
}

export function updateTermConfig(data) {
  return request({
    url: `${termApi.UpdateTermConfig}`,
    method: 'put',
    data: data,
  })
}

export function deleteTermConfig(id) {
  return request({
    url: `${termApi.DeleteTermConfig}${id}`,
    method: 'delete',
  })
}

export function getTermMonitorInfo(ip) {
  return request({
    url: `${termApi.GetTermMonitor}${ip}`,
    method: 'get',
  })
}

export function termRecList(parameter) {
  return request({
    url: termApi.TermRecList,
    method: 'get',
    params: parameter,
  })
}

export function createTermMenu(data) {
  return request({
    url: `${termApi.CreateTermMenu}`,
    method: 'post',
    data: data,
  })
}

export function deleteTermMenu(data) {
  return request({
    url: `${termApi.DeleteTermMenu}`,
    method: 'post',
    data: data,
  })
}

export function updateTermMenu(data) {
  return request({
    url: `${termApi.UpdateTermMenu}`,
    method: 'post',
    data: data,
  })
}

export function updateTermMenuName(data) {
  return request({
    url: `${termApi.UpdateTermMenuName}`,
    method: 'post',
    data: data,
  })
}

export function updateTermMenuContent(data) {
  return request({
    url: `${termApi.UpdateTermMenuContent}`,
    method: 'post',
    data: data,
  })
}

export function getTermDashboard() {
  return request({
    url: termApi.GetTermDashboard,
    method: 'get',
  })
}

export function termCommandList(parameter) {
  return request({
    url: termApi.TermCommandList,
    method: 'get',
    params: parameter,
  })
}

export function termUserCommand(parameter) {
  return request({
    url: termApi.TermUserCommand,
    method: 'get',
    params: parameter,
  })
}

export function termCommonCommandAdminList(parameter) {
  return request({
    url: termApi.TermCommonCommandAdminList,
    method: 'get',
    params: parameter,
  })
}

export function termCommonCommandList(parameter) {
  return request({
    url: termApi.TermCommonCommandList,
    method: 'get',
    params: parameter,
  })
}

export function getTermCommonCommandInfo(id) {
  return request({
    url: `${termApi.GetTermCommonCommand}${id}`,
    method: 'get',
  })
}

export function createTermCommonCommand(data) {
  return request({
    url: `${termApi.CreateTermCommonCommand}`,
    method: 'post',
    data: data,
  })
}

export function updateTermCommonCommand(data) {
  return request({
    url: `${termApi.UpdateTermCommonCommand}`,
    method: 'put',
    data: data,
  })
}

export function deleteTermCommonCommand(id) {
  return request({
    url: `${termApi.DeleteTermCommonCommand}${id}`,
    method: 'delete',
  })
}

export function termUserCommonCommand(parameter) {
  return request({
    url: termApi.TermUserCommonCommand,
    method: 'get',
    params: parameter,
  })
}

export function allTermGatewayList(parameter) {
  return request({
    url: termApi.AllTermGatewayList,
    method: 'get',
    params: parameter,
  })
}

export function getTermGatewayList(parameter) {
  return request({
    url: termApi.TermGatewayList,
    method: 'get',
    params: parameter,
  })
}

export function getTermGatewayInfo(id) {
  return request({
    url: `${termApi.GetTermGateway}${id}`,
    method: 'get',
  })
}

export function createTermGateway(data) {
  return request({
    url: `${termApi.CreateTermGateway}`,
    method: 'post',
    data: data,
  })
}

export function updateTermGateway(data) {
  return request({
    url: `${termApi.UpdateTermGateway}`,
    method: 'put',
    data: data,
  })
}

export function deleteTermGateway(id) {
  return request({
    url: `${termApi.DeleteTermGateway}${id}`,
    method: 'delete',
  })
}

export function listTermQuickCommand(parameter) {
  return request({
    url: termApi.ListTermQuickCommand,
    method: 'get',
    params: parameter,
  })
}

export function getTermQuickCCommand(id) {
  return request({
    url: `${termApi.GetTermQuickCommand}${id}`,
    method: 'get',
  })
}

export function createTermQuickCommand(data) {
  return request({
    url: `${termApi.CreateTermQuickCommand}`,
    method: 'post',
    data: data,
  })
}

export function updateTermQuickCommand(data) {
  return request({
    url: `${termApi.UpdateTermQuickCommand}`,
    method: 'put',
    data: data,
  })
}

export function deleteTermQuickCommand(id) {
  return request({
    url: `${termApi.DeleteTermQuickCommand}${id}`,
    method: 'delete',
  })
}


export function updateUserPlugin(data) {
  return request({
    url: `${termApi.updateUserPlugin}`,
    method: 'put',
    data: data,
  })
}

