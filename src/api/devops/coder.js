import request from '@/utils/request'

const coderApi = {
  List: '/bff/coder',
  Create: '/bff/coder/create',
  IdeList: '/bff/coder/ide',
  UserIdeList: '/bff/coder/ide/user',
  CreateIde: '/bff/coder/ide/create',
  GetIde: '/bff/coder/ide/info/',
  UpdateIde: '/bff/coder/ide/update',
  DeleteIde: '/bff/coder/ide/',
}

export function getCoderList(parameter) {
  return request({
    url: coderApi.List,
    method: 'get',
    params: parameter,
  })
}

export function CreateCoder(data) {
  return request({
    url: `${coderApi.Create}`,
    method: 'post',
    data: data,
  })
}

export function getIdeList(parameter) {
  return request({
    url: coderApi.IdeList,
    method: 'get',
    params: parameter,
  })
}

export function getUserIdeList(parameter) {
  return request({
    url: coderApi.UserIdeList,
    method: 'get',
    params: parameter,
  })
}

export function getIdeInfo(id) {
  return request({
    url: `${coderApi.GetIde}${id}`,
    method: 'get',
  })
}

export function createIde(data) {
  return request({
    url: `${coderApi.CreateIde}`,
    method: 'post',
    data: data,
  })
}

export function updateIde(data) {
  return request({
    url: `${coderApi.UpdateIde}`,
    method: 'put',
    data: data,
  })
}

export function deleteIde(id) {
  return request({
    url: `${coderApi.DeleteIde}${id}`,
    method: 'delete',
  })
}
