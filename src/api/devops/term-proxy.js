import request from '@/utils/request'

const termProxyApi = {
  TermFileContent: '/term-api/file-content',
  TermFileContentSave: '/term-api/file-content/save',
  TermFileLs: '/term-api/file-ls',
  TermFileRename: '/term-api/file-rename',
  TermCmdList: '/term-api/cmd/list',
  AliasUpdate: '/term-api/alias/update',
  AliasRefresh: '/term-api/alias/refresh',

}

export function termFileContent(parameter) {
  return request({
    url: termProxyApi.TermFileContent,
    method: 'get',
    params: parameter,
  })
}
export function termFileContentSave(data) {
  return request({
    url: termProxyApi.TermFileContentSave,
    method: 'post',
    data: data,
  })
}
export function termFileLs(parameter) {
  return request({
    url: termProxyApi.TermFileLs,
    method: 'get',
    params: parameter,
  })
}
export function termFileRename(data) {
  return request({
    url: termProxyApi.TermFileRename,
    method: 'post',
    data: data,
  })
}
export function termCmdList(params) {
  return request({
    url: termProxyApi.TermCmdList,
    method: 'get',
    params: params,
  })
}

export function aliasUpdate(data) {
  return request({
    url: termProxyApi.AliasUpdate,
    method: 'post',
    data: data,
  })
}
export function aliasRefresh(data) {
  return request({
    url: termProxyApi.AliasRefresh,
    method: 'post',
    data: data,
  })
}
