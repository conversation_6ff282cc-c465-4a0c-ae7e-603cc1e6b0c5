/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-03 09:54:39
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-03 09:56:33
 * @FilePath: \cloud_web\src\api\commonVerify\index.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import { globalTokenRequest } from '@/utils/globalTokenRequest'
import request from '@/utils/request'
const dataSetApi = {
  GlobalOtp: '/bff/auth/global-otp',
  CheckCode: '/bff/auth/global-otp/check ',
  SendCOde: '/bff/auth/global-otp/send',
}
export function globalOtp(parameter) {
  return request({
    url: dataSetApi.GlobalOtp,
    method: 'get',
    params: parameter,
  })
}
export function CheckCode(data) {
  return request({
    url: `${dataSetApi.CheckCode}`,
    method: 'post',
    data: data,
  })
}
export function sendCOde(parameter) {
  return request({
    url: `${dataSetApi.SendCOde}?type=${parameter}`,
    method: 'get',
  })
}
