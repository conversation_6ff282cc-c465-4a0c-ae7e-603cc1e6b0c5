import request from '@/utils/request'

const Api = {
  url: '/bff/monitor/api',
  filter: '/bff/monitor/fileter',
}

export function getAPIList(parameter) {
  return request({
    url: Api.url,
    method: 'get',
    params: parameter,
  })
}

export function createApi(data) {
  return request({
    url: Api.url,
    method: 'post',
    data: data,
  })
}

export function deleteApi(params) {
  return request({
    url: Api.url,
    method: 'delete',
    params: params,
  })
}

export function updateApi(data) {
  return request({
    url: Api.url,
    method: 'put',
    data: data,
  })
}
