import request from '@/utils/request'

const Api = {
  url: '/bff/monitor/rule',
}

export function getRulesList(parameter) {
  return request({
    url: Api.url,
    method: 'get',
    params: parameter,
  })
}

export function createRule(data) {
  return request({
    url: Api.url,
    method: 'post',
    data: data,
  })
}

export function deleteRule(params) {
  return request({
    url: Api.url,
    method: 'delete',
    params: params,
  })
}

export function updateRule(data) {
  return request({
    url: Api.url,
    method: 'put',
    data: data,
  })
}
