import request from '@/utils/request'

const alert = {
  urls: '/bff/monitor/aletmanagers',
  url: '/bff/monitor/aletmanagerinfo',
}

export function alertmanagerList(parameter) {
  return request({
    url: alert.urls,
    method: 'get',
    params: parameter,
  })
}

export function createAlertmanager(data) {
  return request({
    url: alert.url,
    method: 'post',
    data: data,
  })
}

export function deleteAlertmanager(params) {
  return request({
    url: alert.url,
    method: 'delete',
    params: params,
  })
}

export function updatAlertmanager(data) {
  return request({
    url: alert.url,
    method: 'put',
    data: data,
  })
}
