import request from '@/utils/request'

const Api = {
  url: '/bff/monitor/exporter',
  idc: '/bff/asset/idc',
  filter: '/bff/monitor/fileter',
}

export function getExporterList(parameter) {
  return request({
    url: Api.url,
    method: 'get',
    params: parameter,
  })
}

export function getIdcList() {
  return request({
    url: Api.idc,
    method: 'get',
  })
}

export function getFilterList(parameter) {
  return request({
    url: Api.filter,
    method: 'get',
    params: parameter,
  })
}


export function createExporter(data) {
  return request({
    url: Api.url,
    method: 'post',
    data: data,
  })
}

export function deleteExporter(params) {
  return request({
    url: Api.url,
    method: 'delete',
    params: params,
  })
}

export function updateExporter(data) {
  return request({
    url: Api.url,
    method: 'put',
    data: data,
  })
}
