import request from '@/utils/request'

const assetImage = {
  List: '/bff/asset/image/list',
  Create: '/bff/asset/image/create',
  CreateRetry: '/bff/asset/image/create/retry',
  Delete: '/bff/asset/image/delete',
  Restore: '/bff/asset/image/restore',

  AutoBackupAssetList: '/bff/asset/auto_backup_image/list',
  AutoBackupAssetCreate: '/bff/asset/auto_backup_image/create',
  AutoBackupAssetDelete: '/bff/asset/auto_backup_image/delete',
  AutoBackupAssetUpdate: '/bff/asset/auto_backup_image/update',
}
export function listAssetImages(parameter) {
  return request({
    url: assetImage.List,
    method: 'get',
    params: parameter,
  })
}

export function createAssetImages(data) {
  return request({
    url: `${assetImage.Create}`,
    method: 'post',
    data: data,
  })
}
export function createAssetImagesRetry(data) {
  return request({
    url: `${assetImage.CreateRetry}`,
    method: 'post',
    data: data,
  })
}

export function deleteAssetImages(id) {
  return request({
    url: `${assetImage.Delete}/${id}`,
    method: 'delete',
  })
}

export function restoreAssetImages(data) {
  return request({
    url: `${assetImage.Restore}`,
    method: 'post',
    data: data,
  })
}


export function autoBackupAssetList(parameter) {
  return request({
    url: assetImage.AutoBackupAssetList,
    method: 'get',
    params: parameter,
  })
}

export function autoBackupAssetCreate(data) {
  return request({
    url: `${assetImage.AutoBackupAssetCreate}`,
    method: 'post',
    data: data,
  })
}
export function autoBackupAssetUpdate(data) {
  return request({
    url: `${assetImage.AutoBackupAssetUpdate}`,
    method: 'post',
    data: data,
  })
}

export function autoBackupAssetDelete(id) {
  return request({
    url: `${assetImage.AutoBackupAssetDelete}/${id}`,
    method: 'delete',
  })
}

