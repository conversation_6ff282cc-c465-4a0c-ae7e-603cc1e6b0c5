import request from '@/utils/request'

const assetSecurityGroup = {
  List: '/bff/asset/security-group/list',
  GroupName: '/bff/asset/security-group-name/list',

}
export function listAssetSecurityGroup(parameter) {
  return request({
    url: assetSecurityGroup.List,
    method: 'get',
    params: parameter,
  })
}

export function listAssetSecurityGroupName(parameter) {
  return request({
    url: assetSecurityGroup.GroupName,
    method: 'get',
    params: parameter,
  })
}
