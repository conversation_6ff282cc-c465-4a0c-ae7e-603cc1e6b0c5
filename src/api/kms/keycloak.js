import request from '@/utils/request'

const kmsKeycloakPathApi = {
  getKmsKeycloakPolicies: '/bff/kms/keycloak/policy',
  ListKmsKeycloakUser: '/bff/kms/keycloak/user',
  BanKmsKeycloakUserToRole: '/bff/kms/keycloak/user_ban',
  UnbanKmsKeycloakUserToRole: '/bff/kms/keycloak/user_unban',
  ListKmsKeycloakActionLog: '/bff/kms/keycloak/action_log',
}

export function getKmsKeycloakPolicies(parameter) {
  return request({
    url: kmsKeycloakPathApi.getKmsKeycloakPolicies,
    method: 'get',
    params: parameter,
  })
}

export function listKmsKeycloakUser(parameter) {
  return request({
    url: kmsKeycloakPathApi.ListKmsKeycloakUser,
    method: 'get',
    params: parameter,
  })
}

export function banKmsKeycloakUserToRole (data) {
  return request({
    url: kmsKeycloakPathApi.BanKmsKeycloakUserToRole,
    method: 'post',
    data: data
  })
}

export function unbanKmsKeycloakUserToRole(data) {
  return request({
    url: kmsKeycloakPathApi.UnbanKmsKeycloakUserToRole,
    method: 'post',
    data: data,
  })
}

export function listKmsKeycloakActionLog(parameter) {
  return request({
    url: kmsKeycloakPathApi.ListKmsKeycloakActionLog,
    method: 'get',
    params: parameter,
  })
}
