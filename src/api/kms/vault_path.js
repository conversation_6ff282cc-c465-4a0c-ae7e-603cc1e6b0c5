import request from '@/utils/request'

const kmsVaultPathApi = {
  List: '/bff/kms/vault_path',
  Update: '/bff/kms/vault_path',
  check: '/bff/kms/vault/check_role',
  consulCheck: '/bff/consul/secret/value/check',
}

export function getVaultPathList(parameter) {
  return request({
    url: kmsVaultPathApi.List,
    method: 'get',
    params: parameter,
  })
}

export function updateVaultPath(data) {
  return request({
    url: kmsVaultPathApi.Update,
    method: 'put',
    data: data,
  })
}

export function checkKmsVaultRoleExist(parameter) {
  return request({
    url: kmsVaultPathApi.check,
    method: 'get',
    params: parameter,
  })
}

export function getConsulSecretExistCheck(parameter) {
  return request({
    url: kmsVaultPathApi.consulCheck,
    method: 'get',
    params: parameter,
  })
}
