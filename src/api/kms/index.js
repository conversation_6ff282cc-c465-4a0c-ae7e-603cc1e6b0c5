/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-03-24 10:23:41
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-24 18:08:47
 * @FilePath: \cloud_web\src\api\kms\index.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const userApi = {
  secretInfo: '/bff/kms/secret_info',
  userCheck: '/bff/kms/user/check',
  pwdLogin: '/bff/kms/user/login',
  dbCreate: '/bff/kms/user',
  groupCreate: '/bff/kms/record/group/create',
  dbLists: '/bff/kms/record/list',
  listCreate: '/bff/kms/record/create',
  listModify: '/bff/kms/record/update',
  listDelete: '/bff/kms/record/delete',
  groupDelete: '/bff/kms/record/group/delete',
  batchCreate: '/bff/kms/record/batch_create',
  userDelete: '/bff/kms/user/',
  modifySecretKey: '/bff/kms/user/secret_key',
}

export function secretInfo(parameter) {
  return request({
    url: userApi.secretInfo,
    method: 'get',
    params: parameter,
  })
}
export function userDelete(parameter) {
  return request({
    url: userApi.userDelete + parameter,
    method: 'delete',
  })
}

export function userCHeck(parameter) {
  return request({
    url: userApi.userCheck,
    method: 'get',
    params: parameter,
  })
}
export function pwdLogin(parameter) {
  return request({
    url: userApi.pwdLogin,
    method: 'post',
    data: parameter,
  })
}
export function modifySecretKey(parameter) {
  return request({
    url: userApi.modifySecretKey,
    method: 'post',
    data: parameter,
  })
}
export function dbCreate(parameter) {
  return request({
    url: userApi.dbCreate,
    method: 'post',
    data: parameter,
  })
}
export function groupCreate(parameter) {
  return request({
    url: userApi.groupCreate,
    method: 'post',
    data: parameter,
  })
}
export function batchCreate(parameter) {
  return request({
    url: userApi.batchCreate,
    method: 'post',
    data: parameter,
  })
}

export function dbLists(parameter) {
  return request({
    url: userApi.dbLists,
    method: 'post',
    data: parameter,
  })
}
export function listCreate(parameter) {
  return request({
    url: userApi.listCreate,
    method: 'post',
    data: parameter,
  })
}

export function listModify(parameter) {
  return request({
    url: userApi.listModify,
    method: 'post',
    data: parameter,
  })
}

export function listDelete(parameter) {
  return request({
    url: userApi.listDelete,
    method: 'post',
    data: parameter,
  })
}

export function groupDelete(parameter) {
  return request({
    url: userApi.groupDelete,
    method: 'post',
    data: parameter,
  })
}
