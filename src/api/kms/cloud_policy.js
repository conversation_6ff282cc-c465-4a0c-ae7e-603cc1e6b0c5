import request from '@/utils/request'

const cloudPolicyPathApi = {
  CloudPolicy: '/bff/kms/cloud_policy',
  CloudPolicyKey: '/bff/kms/cloud_policy_key'
}

export function createCloudPolicy (data) {
  return request({
    url: cloudPolicyPathApi.CloudPolicy,
    method: 'post',
    data: data
  })
}

export function listCloudPolicy (parameter) {
  return request({
    url: cloudPolicyPathApi.CloudPolicy,
    method: 'get',
    params: parameter
  })
}

export function listCloudPolicyKey (parameter) {
  return request({
    url: cloudPolicyPathApi.CloudPolicyKey,
    method: 'get',
    params: parameter
  })
}

export function getCloudPolicy (parameter) {
  return request({
    url: `${cloudPolicyPathApi.CloudPolicy}/${parameter.id}`,
    method: 'get'
  })
}

export function updateCloudPolicy (data) {
  return request({
    url: cloudPolicyPathApi.CloudPolicy,
    method: 'put',
    data: data
  })
}
