import request from '@/utils/request'

const cloudAccessKeyApi = {
  List: '/bff/kms/cloud/access_key',
  check: '/bff/kms/cloud/check_user',
}

export function getCloudAccessKeyList(parameter) {
  return request({
    url: cloudAccessKeyApi.List,
    method: 'get',
    params: parameter,
  })
}

export function checkKmsRamUserExist(parameter) {
  return request({
    url: cloudAccessKeyApi.check,
    method: 'get',
    params: parameter,
  })
}
