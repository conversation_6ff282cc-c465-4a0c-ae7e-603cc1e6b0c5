/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2022-10-10 18:11:17
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-20 17:18:46
 * @FilePath: \cloud_web\src\api\gateway\apiRoute.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
const urls = {
  createRoute: '/bff/gateway/route',
  routeList: '/bff/gateway/route',
  getDetails: '/bff/gateway/route/info/',
  changeState: '/bff/gateway/route/state',
  syncRoute: '/bff/gateway/route/sync',
  routeDel: '/bff/gateway/route/',
  routeRecord: '/bff/gateway/route/record',
  networkCheck: '/bff/gateway/stream_route_node_check',
}

export function createRoute(data) {
  return globalTokenRequest({
    url: urls.createRoute,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('createRoute Error: ' + error)
  })
}

export function editRoute(data) {
  return globalTokenRequest({
    url: urls.createRoute,
    method: 'put',
    data: data,
  }).catch(function (error) {
    console.log('createRoute Error: ' + error)
  })
}
// api路由表格数据
export function getRouteList(parameter) {
  return request({
    url: urls.routeList,
    method: 'get',
    params: parameter,
  })
}
export function getDetails(parameter) {
  return globalTokenRequest({
    url: urls.getDetails + parameter.id,
    method: 'get',
    // params: parameter
  }).catch(function (error) {
    console.log('createRoute Error: ' + error)
  })
}
// 上下线
export function changeState(parameter) {
  return request({
    url: urls.changeState,
    method: 'put',
    data: parameter,
  }).catch(function (error) {
    console.log('createRoute Error: ' + error)
  })
}
// 同步
export function syncRoute(params) {
  return globalTokenRequest({
    method: 'get',
    url: urls.syncRoute,
    params: params,
  })
}
// 同步
export function routeDel(params) {
  return globalTokenRequest({
    method: 'delete',
    url: urls.routeDel + params.id,
  })
}

// 获取路由操作日志

export function getRouteRecord(params) {
  return request({
    method: 'get',
    url: urls.routeRecord,
    params: params,
  })
}


export function checkUpstreamNodeNetwork(params) {
  return globalTokenRequest({
    method: 'get',
    url: urls.networkCheck,
    params: params,
  })
}

