import request from '@/utils/request'

const streamRoute = {
  api: '/bff/gateway/stream_route',
  port: '/bff/gateway/stream_route_port',
}

// 创建
export function createStreamRoute(data) {
  return request({
    url: streamRoute.api,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('createStream Error: ' + error)
  })
}

// 删除
export function deleteStreamRoute(parameter) {
  return request({
    url: streamRoute.api + '/' + parameter.id,
    method: 'delete',
  })
}

export function listStreamRoute(parameter) {
  return request({
    url: streamRoute.api,
    method: 'get',
    params: parameter,
  })
}

// 详情
export function getStreamRoute(parameter) {
  return request({
    url: streamRoute.api + '/' + parameter.id,
    method: 'get',
  })
}

// 修改

export function putStreamRoute(data) {
  return request({
    url: streamRoute.api + '/' + data.id,
    method: 'put',
    data: data,
  })
}

export function getStreamRoutePort(parameter) {
  return request({
    url: streamRoute.port,
    method: 'get',
    params: parameter,
  })
}
