import request from '@/utils/request'
const globalRule = {
  url: '/bff/gateway/global',
  check: '/bff/gateway/global/check/blacklist',
  ips: '/bff/gateway/global/blackips',
  check: '/bff/gateway/global/check/blacklist',
  ip: '/bff/gateway/global/blackip',
  sync: '/bff/gateway/global/blackip/sysnc'
}

export function createGlobalRule(data) {
  return request({
    url: globalRule.url,
    method: 'post',
    data: data,
  })
}

export function listGlobalRule(parameter) {
  return request({
    url: globalRule.url,
    method: 'get',
    params: parameter,
  })
}
export function getGlobalRule(parameter) {
  return request({
    url: globalRule.ip,
    method: 'get',
    params: parameter,
  })
}

export function syncGlobalRuleBlackIp() {
  return request({
    url: globalRule.sync,
    method: 'get',
  })
}

export function getBlackIps() {
  return request({
    url: globalRule.ips,
    method: 'get',
  })
}

export function deleteGlobalRule(id) {
  return request({
    method: 'delete',
    url: globalRule.url + '/' + id,
  })
}

export function CheckBlackIps(data) {
  return request({
    url: globalRule.check,
    method: 'post',
    data: data,
  })
}
