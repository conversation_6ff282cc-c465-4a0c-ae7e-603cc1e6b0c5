/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-09-09 10:26:38
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-09-26 10:52:26
 * @FilePath: \cloud_web\src\api\gateway\upStream.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
const gateWayApi = {
  upstreamList: '/bff/gateway/upstream',
  syncDatas: '/bff/gateway/upstream/sync',
  createStream: '/bff/gateway/upstream',
  delStream: '/bff/gateway/upstream/',
  updateStream: '/bff/gateway/upstream'

}
// upstream表格数据
export function upstreamList (parameter) {
  return globalTokenRequest({
    url: gateWayApi.upstreamList,
    method: 'get',
    params: parameter
  })
}
// 同步数据
export function syncDatas (parameter) {
  return globalTokenRequest({
    url: gateWayApi.syncDatas,
    method: 'get',
    params: parameter
  })
}

// 创建Stream
export function createStream (data) {
  return globalTokenRequest({
    url: gateWayApi.createStream,
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('createStream Error: ' + error)
  })
}
// 删除集群delStream
export function delStream (parameter) {
  return globalTokenRequest({
    url: gateWayApi.delStream + parameter.id,
    method: 'delete'
  })
}

// 升级Stream
export function updateStream (data) {
  console.log(data)
  return globalTokenRequest({
    url: gateWayApi.updateStream,
    method: 'put',
    data: data
  })
}
// 升级节点
export function updateNode (data) {
  console.log(data)
  return request({
    url: gateWayApi.updateNode,
    method: 'put',
    data: data
  })
}
// 删除集群

// 删除节点
export function deleteNode (parameter) {
  return request({
    url: gateWayApi.deleteNode + parameter.id,
    method: 'delete'
    // params: parameter
  })
}
