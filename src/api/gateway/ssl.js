
import { globalTokenRequest } from '@/utils/globalTokenRequest'
const urls = {
  createSsl: '/bff/gateway/ssl',
  syncSsl: '/bff/gateway/ssl/sync/',
  SnisList: '/bff/gateway/ssl/snis',
  DeleteSsl: '/bff/gateway/ssl/',
  SslList: '/bff/gateway/ssl'
}

export function createSsl (data) {
  return globalTokenRequest({
    url: urls.createSsl,
    method: 'post',
    data: data
  })
}

export function getSslList (parameter) {
  return globalTokenRequest({
    url: urls.SslList,
    method: 'get',
    params: parameter
  })
}

// 同步
export function syncSsl (envId) {
  return globalTokenRequest({
    method: 'get',
    url: urls.syncSsl + envId
  })
}

export function getSnisList () {
  return globalTokenRequest({
    method: 'get',
    url: urls.SnisList
  })
}

export function deleteSsl (id) {
  return globalTokenRequest({
    method: 'delete',
    url: urls.DeleteSsl + id

  })
}
