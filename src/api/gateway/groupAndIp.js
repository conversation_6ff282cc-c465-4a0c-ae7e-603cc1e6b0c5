/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-09-09 10:26:38
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-09 17:30:16
 * @FilePath: \cloud_web\src\api\gateway\groupAndIp.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const gateWayApi = {
  tableInfos: '/bff/gateway/cluster_node',
  groupNames: '/bff/gateway/cluster',
  createGroup: '/bff/gateway/cluster/create',
  createNode: '/bff/gateway/node/create',
  updateGroup: '/bff/gateway/cluster/update',
  updateNode: '/bff/gateway/node/update',
  deleteGroup: '/bff/gateway/cluster/delete/',
  deleteNode: '/bff/gateway/node/delete/',
  upstream_id: '/bff/gateway/upstream_id',
  connectivity: '/bff/gateway/connectivity'
}
// 集群页面表格数据
export function tableInfos (parameter) {
  return request({
    url: gateWayApi.tableInfos,
    method: 'get',
    params: parameter
  })
}
// upstreamid
export function upstreamids (parameter) {
  return request({
    url: gateWayApi.upstream_id,
    method: 'get',
    params: parameter
  })
}
// 集群select
export function groupNames (parameter) {
  return request({
    url: gateWayApi.groupNames,
    method: 'get',
    params: parameter
  })
}
// 创建集群
export function createGroup (data) {
  return globalTokenRequest({
    url: `${gateWayApi.createGroup}`,
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('createGroup Error: ' + error)
  })
}
// 创建IP
export function createNode (data) {
  return globalTokenRequest({
    url: `${gateWayApi.createNode}`,
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('createNode Error: ' + error)
  })
}

// 升级集群
export function updateGroup (data) {
  console.log(data)
  return globalTokenRequest({
    url: gateWayApi.updateGroup,
    method: 'put',
    data: data
  })
}
// 升级节点
export function updateNode (data) {
  console.log(data)
  return globalTokenRequest({
    url: gateWayApi.updateNode,
    method: 'put',
    data: data
  })
}
// 删除集群
export function deleteGroup (parameter) {
  return globalTokenRequest({
    url: gateWayApi.deleteGroup + parameter.id,
    method: 'delete'
  })
}
// 删除节点
export function deleteNode (parameter) {
  return globalTokenRequest({
    url: gateWayApi.deleteNode + parameter.id,
    method: 'delete'
    // params: parameter
  })
}

export function gatewayConnectivity(data){
  return request({
    url: gateWayApi.connectivity,
    method: 'post',
    data: data
  })
}
