/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-10 18:11:17
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-20 17:18:46
 * @FilePath: \cloud_web\src\api\gateway\apiRoute.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
const urls = {
  create: '/bff/gateway/consumer',
  sync: '/bff/gateway/consumer/sync',
  List: '/bff/gateway/consumers',
  Delete: '/bff/gateway/consumer/'
}

export function createConsumer (data) {
  return request({
    url: urls.create,
    method: 'post',
    data: data
  })
}

export function getConsumerList (parameter) {
  return request({
    url: urls.List,
    method: 'get',
    params: parameter
  })
}

// 同步
export function syncConsumer (parameter) {
  return request({
    method: 'get',
    url: urls.sync,
    params: parameter
  })
}

export function deleteConsumer (id) {
  return request({
    method: 'delete',
    url: urls.Delete + id
  })
}
