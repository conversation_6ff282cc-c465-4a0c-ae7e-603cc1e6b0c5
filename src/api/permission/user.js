import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const userApi = {
  List: '/bff/user',
  userTeamList: '/bff/user/asset/emailToTeam',
  UserEmailList: '/bff/user/user/user_email_list',
}

export function getUserList(parameter) {
  /**
   * 搜索字段为空，接口报错
   */
  if (parameter.searchText == '') {
    return noc.promise(resolve => {
      resolve({
        Data: {
          data: [],
        },
      })
    })
  }
  return globalTokenRequest({
    url: userApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getUserTeamList(parameter) {
  return request({
    url: userApi.userTeamList,
    method: 'get',
    params: parameter,
  })
}

export function getUserEmailList(parameter) {
  return request({
    url: userApi.UserEmailList,
    method: 'get',
    params: parameter,
  })
}
