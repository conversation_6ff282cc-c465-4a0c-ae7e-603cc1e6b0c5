// import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
const roleApi = {
  List: '/bff/auth/role_sensitive_backend_route', // 敏感后端路由列表
  Item: '/bff/auth/role_sensitive_backend_route', // 敏感后端路由新增
  BackedRoute: '/bff/auth/role_backend_route/which/path'
}

export function getSensitiveList(parameter) {
  return globalTokenRequest({
    url: roleApi.List,
    method: 'get',
    params: parameter,
  })
}
export function getSensitiveDetail(data) {
  return globalTokenRequest({
    url: `${roleApi.Item}/${data.id}`,
    method: 'get',
    data: data,
  })
}
export function getBackedRoute(data) {
  return globalTokenRequest({
    url: `${roleApi.BackedRoute}`,
    method: 'get',
    data: data,
  })
}
export function createSensitive(parameter) {
  return globalTokenRequest({
    url: roleApi.Item,
    method: 'post',
    data: parameter,
  })
}

export function updateSensitive(parameter) {
  return globalTokenRequest({
    url: roleApi.Item,
    method: 'put',
    data: parameter,
  })
}

export function deleteSensitive(id) {
  return globalTokenRequest({
    url: `${roleApi.Item}/${id}`,
    method: 'delete',
  })
}
