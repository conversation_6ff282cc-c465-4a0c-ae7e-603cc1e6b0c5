import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const policyApi = {
  List: '/bff/auth/policies',
  CreateSubjectPolicy: '/bff/auth/authorization',
  CreatePolicy: '/bff/auth/policy',
  SubjectPolicy: '/bff/auth/permissions',
  DeleteSubjectPolicy: '/bff/auth/subTypes',
}

export function createSubjectPolicy(data) {
  return globalTokenRequest({
    url: policyApi.CreateSubjectPolicy,
    method: 'put',
    data: data,
  })
}

export function getPolicyData(parameter) {
  return globalTokenRequest({
    url: policyApi.List,
    method: 'get',
    params: parameter,
  })
}

export function createPolicy(data) {
  return globalTokenRequest({
    url: `${policyApi.CreatePolicy}/${data.policyName}`,
    method: 'put',
    data: data,
  })
}

export function subjectPolicyList(parameter) {
  return globalTokenRequest({
    url: policyApi.SubjectPolicy,
    method: 'get',
    params: parameter,
  })
}

export function deleteSubjectPolicy(data) {
  return globalTokenRequest({
    url: `${policyApi.DeleteSubjectPolicy}/${data.subjectType}/subjects/${data.subject}/policies/${data.policyName}`,
    method: 'delete',
  })
}
