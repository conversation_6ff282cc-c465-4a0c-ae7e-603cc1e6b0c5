/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-13 10:15:25
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-24 11:12:42
 * @FilePath: \cloud_web\src\api\permission\role.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const roleApi = {
  List: '/bff/auth/roles',
  Create: '/bff/auth/role',
  Delete: '/bff/auth/role',
  Binding: '/bff/auth/roleBinding',
  BindingDelete: '/bff/auth/users',
  menus: '/bff/auth/user/meaus',
  menuControl: '/bff/auth/user/meau/show',
  RoleUserList: 'bff/auth/role/users',
  BackendRoute: '/bff/auth/role_backend_route/which/path',
  RoleBindRoute: '/bff/auth/role_bind_backend_route',
}
export function getRoleUsesrList(data) {
  return globalTokenRequest({
    url: roleApi.RoleUserList,
    method: 'get',
    params: data,
  })
}
export function getBackendRoute(data) {
  return globalTokenRequest({
    url: roleApi.BackendRoute,
    method: 'get',
    params: data,
  })
}
export function getRoleBindRoute(data) {
  return globalTokenRequest({
    url: roleApi.RoleBindRoute,
    method: 'get',
    params: data,
  })
}
export function UpdateRoleBindRoute(data) {
  return globalTokenRequest({
    url: roleApi.RoleBindRoute,
    method: 'post',
    data: data,
  })
}
export function UpdateRoleInfo(data) {
  return globalTokenRequest({
    url: `${roleApi.Create}/${data.roleName}`,
    method: 'put',
    data: data,
  })
}
export function DelRoleBindRoute(data) {
  return globalTokenRequest({
    url: roleApi.RoleBindRoute + '/' + data.id,
    method: 'delete',
  })
}
export function getMenuShow() {
  return request({
    url: roleApi.menus,
    method: 'get',
  })
}

export function menuShowControl(data) {
  return request({
    url: roleApi.menuControl,
    method: 'put',
    data: data,
  })
}
export function getRoleList(parameter) {
  return globalTokenRequest({
    url: roleApi.List,
    method: 'get',
    params: parameter,
  })
}

export function createRole(data) {
  return globalTokenRequest({
    url: roleApi.Create,
    method: 'post',
    data: data,
  })
}

export function roleBinding(data) {
  return globalTokenRequest({
    url: roleApi.Binding,
    method: 'put',
    data: data,
  })
}

export function roleBindingDelete(data) {
  return globalTokenRequest({
    url: `${roleApi.BindingDelete}/${data.user}/roles`,
    method: 'delete',
    data: data,
  })
}

export function deleteRole(data) {
  return globalTokenRequest({
    url: `${roleApi.Delete}/${data.roleName}`,
    method: 'delete',
    data: data,
  })
}
