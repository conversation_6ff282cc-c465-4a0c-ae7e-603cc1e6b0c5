/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2022-08-23 16:21:46
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-08-23 17:05:23
 * @FilePath: \cloud_web\src\api\permission\routerRole.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
// import request from '@/utils/request'
// const roleApi = {
//   List: '/subTypes/auth/roles',
//   Create: '/bff/auth/role',
//   Binding: '/bff/auth/roleBinding',
//   BindingDelete: '/bff/auth/users'
// }
