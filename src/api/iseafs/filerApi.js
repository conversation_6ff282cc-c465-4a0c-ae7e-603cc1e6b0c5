import request from '@/utils/request'
import { func } from 'vue-types'

const filerApi = {
  getFilers: '/bff/iseafs/filers',
  createFiler: '/bff/iseafs/filer/create',
  updateFiler: '/bff/iseafs/filer/update',
  deleteFiler: '/bff/iseafs/filer/delete/',
  updateFilerStatus: '/bff/iseafs/filer/update_status',
  getFilerSetting: '/bff/iseafs/filer/setting',
  checkFilerOnlineSetting: '/bff/iseafs/filer/check_online_setting',
  batchSyncFilerHealthStatus: '/bff/iseafs/filer/batch_sync_health_status'
  // getFilers: '/iseafs/filers'
}

export function batchSyncFilerHealthStatus(data) {
  return request({
    url: filerApi.batchSyncFilerHealthStatus,
    method: 'post',
    data: data
  })
}

// 获取Filer列表
export function getFilers(parameter) {
  return request({
    url: filerApi.getFilers,
    method: 'get',
    params: parameter
  })
}

export function createFiler(data) {
  return request({
    url: filerApi.createFiler,
    method: 'post',
    data: data
  })
}

export function updateFiler(data) {
  return request({
    url: filerApi.updateFiler,
    method: 'put',
    data: data
  })
}

export function updateFilerStatus(data) {
  return request({
    url: filerApi.updateFilerStatus,
    method: 'put',
    data: data
  })
}

export function getFilerSetting(parameter) {
  return request({
    url: filerApi.getFilerSetting,
    method: 'get',
    params: parameter
  })
}

export function checkFilerOnlineSetting(parameter) {
  return request({
    url: filerApi.checkFilerOnlineSetting,
    method: 'get',
    params: parameter
  })
}

export function deleteFiler(parameter) {
  return request({
    url: filerApi.deleteFiler + parameter.id,
    method: 'delete'
  })
}