/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-03-03 19:33:10
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-03-03 19:33:10
 * @FilePath: \cloud_web\src\api\iseafs\setApi.js
 * @Description:
 *
 * Copyright (c) 2025 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import { func } from 'vue-types'

const setApi = {
  getSets: '/bff/iseafs/sets',
  createSet: '/bff/iseafs/set/create',
  updateSet: '/bff/iseafs/set/update',
  deleteSet: '/bff/iseafs/set/delete/',
  getClusterNames: '/bff/iseafs/cluster/names',
  updateSetStatus: '/bff/iseafs/set/update_status',
  getSetTopo: '/bff/iseafs/set/topo',
  listSetsFilerStatus: '/bff/iseafs/sets/filer_status',
  getMasterleaderForSets: '/bff/iseafs/sets/master_leader',
  pushSetStatusToFiler: '/bff/iseafs/sets/push_status_to_filer'
  // getSets: '/iseafs/sets',
  // createSet: '/iseafs/set/create',
  // updateSet: '/iseafs/set/update',
  // deleteSet: '/iseafs/set/delete/',
  // getClusterNames: '/iseafs/cluster/names',
  // updateSetStatus: '/iseafs/set/update_status',
  // getSetTopo: '/iseafs/set/topo'
}

// 获取集群列表
export function getSets(parameter) {
  return request({
    url: setApi.getSets,
    method: 'get',
    params: parameter
  })
}

// 创建集群
export function createSet(data) {
  return request({
    url: setApi.createSet,
    method: 'post',
    data: data
  })
}

// 更新集群
export function updateSet(data) {
  return request({
    url: setApi.updateSet,
    method: 'put',
    data: data
  })
}

// 删除集群
export function deleteSet(parameter) {
  return request({
    url: setApi.deleteSet + parameter.id,
    method: 'delete'
  })
}

// 获取集群名称列表
export function getClusterNames() {
  return request({
    url: setApi.getClusterNames,
    method: 'get'
  })
}

export function updateSetStatus(data) {
  return request({
    url: setApi.updateSetStatus,
    method: 'put',
    data: data
  })
}

export function getSetTopo(parameter) {
  return request({
    url: setApi.getSetTopo,
    method: 'get',
    // responseType: 'text', // 指定响应类型为文本
    params: parameter
  })
}

export function listSetsFilerStatus(parameter) {
  return request({
    url: setApi.listSetsFilerStatus,
    method: 'get',
    params: parameter
  })
}

export function getMasterleaderForSets(parameter) {
  return request({
    url: setApi.getMasterleaderForSets,
    method: 'get',
    params: parameter
  })
}

export function pushSetStatusToFiler(data) {
  return request({
    url: setApi.pushSetStatusToFiler,
    method: 'put',
    data: data
  })
}