/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2025-03-03 19:33:10
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-03-03 19:33:10
 * @FilePath: \cloud_web\src\api\iseafs\clusterApi.js
 * @Description:
 *
 * Copyright (c) 2025 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import { func } from 'vue-types'

const clusterApi = {
  getClusters: '/bff/iseafs/clusters',
  createCluster: '/bff/iseafs/cluster/create',
  updateCluster: '/bff/iseafs/cluster/update',
  deleteCluster: '/bff/iseafs/cluster/delete/',
  getClusterNames: '/bff/iseafs/cluster/names',

//   getClusters: '/iseafs/clusters',
//   createCluster: '/iseafs/cluster/create',
//   updateCluster: '/iseafs/cluster/update',
//   deleteCluster: '/iseafs/cluster/delete/',
}


// 获取集群列表
export function getClusters(parameter) {
  return request({
    url: clusterApi.getClusters,
    method: 'get',
    params: parameter
  })
}

// 创建集群
export function createCluster(data) {
  return request({
    url: clusterApi.createCluster,
    method: 'post',
    data: data
  })
}

// 更新集群
export function updateCluster(data) {
  return request({
    url: clusterApi.updateCluster,
    method: 'put',
    data: data
  })
}

// 删除集群
export function deleteCluster(parameter) {
  return request({
    url: clusterApi.deleteCluster + parameter.uuid,
    method: 'delete'
  })
}

export function updateSetStatus(data) {
  return request({
    url: clusterApi.updateSetStatus,
    method: 'put',
    data: data
  })
}

export function getClusterNames() {
  return request({
    url: clusterApi.getClusterNames,
    method: 'get'
  })
}