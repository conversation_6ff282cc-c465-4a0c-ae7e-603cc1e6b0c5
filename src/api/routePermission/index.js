/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-25 17:59:01
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-03 11:23:57
 * @FilePath: \cloud_web\src\api\routePermission\index.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const roleApi = {
  Router: '/auth/router',
  Routers: '/auth/routers',
  GetAuth: '/auth/router/authorization',
  Auth: '/auth/router/authorize',
  unAuth: '/auth/router/unauthorize',
  getMenus: '/auth/routers/menu',
  CheckNetAccess: '/auth/net/access',
  sortRoute: '/auth/router_rank',
  Roles: '/bff/auth/role_select',
  UserRoles: '/bff/auth/user_role_select'
}
export function sortRoute(parameter) {
  return globalTokenRequest({
    url: roleApi.sortRoute,
    method: 'put',
    data: parameter,
  })
}
export function editRoute(parameter) {
  return globalTokenRequest({
    url: roleApi.Router,
    method: 'post',
    data: parameter,
  })
}
export function getRouters(parameter) {
  return globalTokenRequest({
    url: roleApi.Routers,
    method: 'get',
    params: parameter,
  })
}
export function getMenus(parameter) {
  return request({
    url: roleApi.getMenus,
    method: 'get',
    params: parameter,
  })
}
export function getNetAccess() {
  return request({
    url: roleApi.CheckNetAccess,
    method: 'get',
  })
}
export function deleteRouter(parameter) {
  return globalTokenRequest({
    url: roleApi.Routers,
    method: 'delete',
    data: parameter,
  })
}
export function putRouter(data) {
  return globalTokenRequest({
    url: roleApi.Router,
    method: 'put',
    data: data,
  })
}
export function GetAuth(parameter) {
  return globalTokenRequest({
    url: roleApi.GetAuth,
    method: 'get',
    params: parameter,
  })
}
export function Auth(data) {
  return globalTokenRequest({
    url: roleApi.Auth,
    method: 'put',
    data: data,
  })
}
export function unAuth(data) {
  return globalTokenRequest({
    url: roleApi.unAuth,
    method: 'put',
    data: data,
  })
}
export function getRolesList() {
  return globalTokenRequest({
    url: roleApi.Roles,
    method: 'get',
  })
}
export function getUserRolesList() {
  return globalTokenRequest({
    url: roleApi.UserRoles,
    method: 'get',
  })
}
