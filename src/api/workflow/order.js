import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const orderApi = {
  List: '/bff/workflow/order',
  ListMyApplication: '/bff/workflow/my_application',
  ListMyApproval: '/bff/workflow/my_approval',
  ListHistoryOrder: '/bff/workflow/history_order',
  Info: '/bff/workflow/order/info/',
  ImportInfo: '/bff/workflow/order/import_info',
  AddRecord : '/bff/workflow/add_record',
  UpdateRecord : '/bff/workflow/update_record',
  CheckRecord : '/bff/workflow/check_record',
  Create: '/bff/workflow/order/create',
  Approve: '/bff/workflow/order/approve',
  OrSign: '/bff/workflow/order/or_sign',
  OrderType: '/bff/workflow/order/order_type',
  MyApprovalOrderUser: '/bff/workflow/order/my_approval_users',
  Dashboard: '/bff/workflow/order/dashboard',
  UserIpList: '/bff/workflow/order/user_ip_list',
  BindUserIp: '/bff/workflow/order/bind_user_ip',
  servedProducts: 'workflow/bigdata-platform/bigdata/common/product ',
  security_level: 'workflow/bigdata-platform/bigdata/etl_sqoop/work_order',
  ReasonList: '/bff/workflow/reason_list',
  GpuApplyCron: '/bff/workflow/cron/gpu_apply',
  ImageCacheCron: '/bff/workflow/cron/image_cache',
  TemplateSelect: '/bff/dbm/mysql_template_select',
  CheckUserNeedsCaptchaVerification: '/bff/workflow/captcha-verification/check',
}

export function getOrderList(parameter) {
  return globalTokenRequest({
    url: orderApi.List,
    method: 'get',
    params: parameter,
  })
}
export function getTemplateSelect(parameter) {
  return request({
    url: orderApi.TemplateSelect,
    method: 'get',
    params: parameter,
  })
}
export function ReasonList(parameter) {
  return request({
    url: orderApi.ReasonList,
    method: 'get',
    params: parameter,
  })
}
export function getMyApplicationList(parameter) {
  return request({
    url: orderApi.ListMyApplication,
    method: 'get',
    params: parameter,
  })
}

export function getMyApprovalList(parameter) {
  return request({
    url: orderApi.ListMyApproval,
    method: 'get',
    params: parameter,
  })
}

export function getHistoryOrderList(parameter){
  return request({
    url: orderApi.ListHistoryOrder,
    method: 'get',
    params: parameter,
  })
}

export function getOrderInfo(id) {
  return request({
    url: `${orderApi.Info}${id}`,
    method: 'get',
  })
}

export function getImportOrderInfo(parameter) {
  return globalTokenRequest({
    url: orderApi.ImportInfo,
    method: 'get',
    params: parameter,
  })
}

export function addGpuServiceRecord(data) {
  return request({
    url: orderApi.AddRecord,
    method: 'post',
    data: data,
  })
}

export function updateGpuServiceRecord(parameter) {
  return request({
    url: orderApi.UpdateRecord,
    method: 'post',
    params: parameter,
  })
}

export function checkGpuServiceRecord(parameter) {
  return request({
    url: orderApi.CheckRecord,
    method: 'get',
    params: parameter,
  })
}

export function createOrder(data) {
  return globalTokenRequest({
    url: `${orderApi.Create}`,
    method: 'post',
    data: data,
  })
}

export function approveOrder(data) {
  return request({
    url: `${orderApi.Approve}`,
    method: 'post',
    data: data,
  })
}

export function orSignOrder(data) {
  return request({
    url: `${orderApi.OrSign}`,
    method: 'post',
    data: data,
  })
}

export function getOrderTypeList() {
  return globalTokenRequest({
    url: orderApi.OrderType,
    method: 'get',
  })
}

export function getMyApprovalOrderUser(parameter) {
  return request({
    url: orderApi.MyApprovalOrderUser,
    method: 'get',
    params: parameter,
  })
}

export function getOrderDashboard(parameter) {
  return request({
    url: orderApi.Dashboard,
    method: 'get',
    params: parameter,
  })
}

export function getUserIpList(parameter) {
  return request({
    url: orderApi.UserIpList,
    method: 'get',
    params: parameter,
  })
}

export function bindUserIp(data) {
  return request({
    url: `${orderApi.BindUserIp}`,
    method: 'post',
    data: data,
  })
}

export function getServedProducts() {
  return request({
    url: orderApi.servedProducts,
    method: 'get',
  })
}

export function getSecurity_level(data) {
  return request({
    url: orderApi.security_level,
    method: 'post',
    data: data,
  })
}

export function gpuApplyCron(data) {
  return request({
    url: `${orderApi.GpuApplyCron}`,
    method: 'post',
    data: data,
  })
}

export function imageCacheCron(data) {
  return request({
    url: `${orderApi.ImageCacheCron}`,
    method: 'post',
    data: data,
  })
}

export function checkUserNeedsCaptchaVerification() {
  return request({
    url: orderApi.CheckUserNeedsCaptchaVerification,
    method: 'get',
  })
}
