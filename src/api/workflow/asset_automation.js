import request from '@/utils/request'

const automationApi = {
  KvmBaseInfo: '/bff/asset/automation/get_kvm_base_info',
  AwsBaseInfo: '/bff/asset/automation/get_aws_base_info',
  AliyunBaseInfo: '/bff/asset/automation/get_aliyun_base_info',
  AliyunDiskInfo: '/bff/asset/automation/get_aliyun_disk_info',
  TencentBaseInfo: '/bff/asset/automation/get_tencent_base_info',
  AwsSecurityList: '/bff/asset/aws_base_info_security/',
  CheckDeleteAssetInfo: '/bff/asset/check_delete_asset_status/',
  AssetImageList: '/bff/asset/asset_image_list/',
  offlineAssetCpuCheck: '/bff/asset/automation/offline_check_cpu',
  offlineAssetMemCheck: '/bff/asset/automation/offline_check_mem',
  offlineAssetTcpCheck: '/bff/asset/automation/offline_check_tcp',
  offlineAssetLoadCheck: '/bff/asset/automation/offline_check_load',
  offlineAssetNetInCheck: '/bff/asset/automation/offline_check_net_in',
  offlineAssetNetOutCheck: '/bff/asset/automation/offline_check_net_out',
  offlineAssetSyslogCheck: '/bff/asset/automation/offline_check_syslog',
  AssetIps: '/bff/asset/asset/ips',
  CheckHost: '/bff/asset/jms_auth/check_host',
  getAssetPrice: '/bff/asset/asset/asset_price',
  awsSecurityBase: '/bff/asset/asset/aws_security_base_info',
  cpuList: '/bff/asset/kvm/instance_type/list',
  allocationHost: '/bff/asset/kvm/available_host/list',
  serverResult: '/bff/asset/kvm/process_status',
  reTry: '/bff/asset/kvm/create/retry',
  dbServerreTry: '/bff/asset/kvm/db_asset/retry',
  dbInitreTry:'/bff/asset/kvm/db_init/retry',
  dbResult: '/bff/asset/kvm/db_init/process_status',
  addMysqlSlave: '/bff/dbm/mysql_instance/create_slave',
  addRedisSlave: '/bff/dbm/redis_instance/create_slave',
  listJmsAuthResult: '/bff/asset/jms-auth/result/list',
  retryJmsAuth: '/bff/asset/jms-auth/retry',
}
export function addMysqlSlave(parameter) {
  return request({
    url: automationApi.addMysqlSlave,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function addRedisSlave(parameter) {
  return request({
    url: automationApi.addRedisSlave,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function cpuList(parameter) {
  return request({
    url: automationApi.cpuList,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}

export function reTry(parameter) {
  return request({
    url: automationApi.reTry,
    method: 'post',
    data: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function dbServerreTry(parameter) {
  return request({
    url: automationApi.dbServerreTry,
    method: 'post',
    data: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function dbInitreTry(parameter) {
  return request({
    url: automationApi.dbInitreTry,
    method: 'post',
    data: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}

export function serverResult(parameter) {
  return request({
    url: automationApi.serverResult,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function dbResult(parameter) {
  return request({
    url: automationApi.dbResult,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function allocationHost(parameter) {
  return request({
    url: automationApi.allocationHost,
    method: 'post',
    data: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}
export function vmBaseInfo(parameter) {
  return request({
    url: automationApi.KvmBaseInfo,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}

export function awsBaseInfo(parameter) {
  return request({
    url: automationApi.AwsBaseInfo,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('awsBaseInfo Error: ' + error)
  })
}
export function awsSecurityBaseInfo(parameter) {
  return request({
    url: automationApi.awsSecurityBase,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('awsSecurityBase Error: ' + error)
  })
}

export function aliyunBaseInfo(parameter) {
  return request({
    url: automationApi.AliyunBaseInfo,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('aliyunBaseInfo Error: ' + error)
  })
}
export function aliyunDiskInfo(data) {
  return request({
    url: automationApi.AliyunDiskInfo,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('aliyunDiskInfo Error: ' + error)
  })
}
export function tencentBaseInfo(parameter) {
  return request({
    url: automationApi.TencentBaseInfo,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('TencentBaseInfo Error: ' + error)
  })
}

export function awsSecurityList(data) {
  return request({
    url: automationApi.AwsSecurityList,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('securityList Error: ' + error)
  })
}

// 服务器注销检查
export function getCheckIpInfo(data) {
  return request({
    url: automationApi.CheckDeleteAssetInfo,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('getCheckIpInfo Error: ' + error)
  })
}
// 选择镜像时获取信息
export function assetImageList(data) {
  return request({
    url: automationApi.AssetImageList,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('assetList Error: ' + error)
  })
}

export function offlineAssetCpuCheck(parameter) {
  return request({
    url: automationApi.offlineAssetCpuCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetCpuCheck Error: ' + error)
  })
}
export function offlineAssetMemCheck(parameter) {
  return request({
    url: automationApi.offlineAssetMemCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetMemCheck Error: ' + error)
  })
}
export function offlineAssetTcpCheck(parameter) {
  return request({
    url: automationApi.offlineAssetTcpCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetTcpCheck Error: ' + error)
  })
}
export function offlineAssetLoadCheck(parameter) {
  return request({
    url: automationApi.offlineAssetLoadCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetLoadCheck Error: ' + error)
  })
}
export function offlineAssetNetInCheck(parameter) {
  return request({
    url: automationApi.offlineAssetNetInCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetNetInCheck Error: ' + error)
  })
}
export function offlineAssetNetOutCheck(parameter) {
  return request({
    url: automationApi.offlineAssetNetOutCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetNetOutCheck Error: ' + error)
  })
}

export function offlineAssetSyslogCheck(parameter) {
  return request({
    url: automationApi.offlineAssetSyslogCheck,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('offlineAssetSyslogCheck Error: ' + error)
  })
}
export function getAssetPrice(parameter) {
  return request({
    url: automationApi.getAssetPrice,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('getAssetPrice Error: ' + error)
  })
}
export function checkHost(parameter) {
  return request({
    url: automationApi.CheckHost,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('checkHost Error: ' + error)
  })
}

export function getAllAssetsIp(parameter) {
  return request({
    url: automationApi.AssetIps,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('getAllAssetsIp Error: ' + error)
  })
}

export function listJmsAuthResult(parameter) {
  return request({
    url: automationApi.listJmsAuthResult,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('listJmsAuthResult Error: ' + error)
  })
}

export function retryJmsAuth(data) {
  return request({
    url: automationApi.retryJmsAuth,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('retryJmsAuth Error: ' + error)
  })
}
