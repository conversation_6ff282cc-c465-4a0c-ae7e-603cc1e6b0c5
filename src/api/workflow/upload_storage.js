import uploadStorageRequest from '@/utils/uploadStorageRequest'

// 默认混合云
const desktopApi = {
  upload: '/upload',
  download: '/download',
}
// export function uploadDbfile(data) {
//   return uploadStorageRequest({
//     url: `${desktopApi.upload}/${data.get('name').split('.')[0]}`,
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//     },
//     data: data,
//   })
// }
export function uploadDbfile(data, name) {
  return uploadStorageRequest({
    url: `${desktopApi.upload}/${name}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/octet-stream',
    },
    data: data,
  })
}
export function downLoadDbfile(data) {
  return uploadStorageRequest({
    url: `${desktopApi.download}/${data.name.split('.')[0]}`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}
