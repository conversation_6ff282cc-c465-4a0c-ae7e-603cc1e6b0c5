import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const processApi = {
  List: '/bff/workflow/process',
  Info: '/bff/workflow/process/info/',
  Create: '/bff/workflow/process/create',
  Update: '/bff/workflow/process/update',
  Delete: '/bff/workflow/process/',
  OrderType: '/bff/workflow/process/order_type',
  AgentList: '/bff/workflow/agent',
  AgentInfo: '/bff/workflow/agent/info/',
  AgentCreate: '/bff/workflow/agent/create',
  AgentUpdate: '/bff/workflow/agent/update',
  AgentDelete: '/bff/workflow/agent/',
}

export function getProcessList(parameter) {
  return globalTokenRequest({
    url: processApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getProcessInfo(id) {
  return globalTokenRequest({
    url: `${processApi.Info}${id}`,
    method: 'get',
  })
}

export function createProcess(data) {
  return globalTokenRequest({
    url: `${processApi.Create}`,
    method: 'post',
    data: data,
  })
}

export function updateProcess(data) {
  return globalTokenRequest({
    url: `${processApi.Update}`,
    method: 'put',
    data: data,
  })
}

export function deleteProcess(id) {
  return globalTokenRequest({
    url: `${processApi.Delete}${id}`,
    method: 'delete',
  })
}

export function getProcessOrderTypeList() {
  return globalTokenRequest({
    url: processApi.OrderType,
    method: 'get',
  })
}

export function getAgentList(parameter) {
  return request({
    url: processApi.AgentList,
    method: 'get',
    params: parameter,
  })
}

export function getAgentInfo(id) {
  return request({
    url: `${processApi.AgentInfo}${id}`,
    method: 'get',
  })
}

export function createAgent(data) {
  return request({
    url: `${processApi.AgentCreate}`,
    method: 'post',
    data: data,
  })
}

export function updateAgent(data) {
  return request({
    url: `${processApi.AgentUpdate}`,
    method: 'put',
    data: data,
  })
}

export function deleteAgent(id) {
  return request({
    url: `${processApi.AgentDelete}${id}`,
    method: 'delete',
  })
}
