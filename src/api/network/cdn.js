import request from '@/utils/request'

const userApi = {
  List: '/bff/network/cdn/refresh_record',
  Refresh: '/bff/network/cdn/refresh',
  UpdateRefreshStatus: '/bff/network/cdn_record/update'
}

export function listCdnRefreshRecord (parameter) {
  return request({
    url: userApi.List,
    method: 'get',
    params: parameter
  })
}
export function cdnRefresh (parameter) {
  return request({
    url: userApi.Refresh,
    method: 'post',
    data: parameter
  })
}

export function UpdateRefreshStatus (parameter) {
  return request({
    url: userApi.UpdateRefreshStatus,
    method: 'post',
    data: parameter
  })
}
