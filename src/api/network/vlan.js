import request from '@/utils/request'

const networkApi = {
  VlanList: '/bff/network/vlan',
  CreateVlan: '/bff/network/vlan/create',
  GetVlan: '/bff/network/vlan/info/',
  UpdateVlan: '/bff/network/vlan/update',
  DeleteVlan: '/bff/network/vlan/',
  VlanOptionsList: '/bff/network/vlan/options',
}

export function vlanList(parameter) {
  return request({
    url: networkApi.VlanList,
    method: 'get',
    params: parameter,
  })
}

export function createVlan(data) {
  return request({
    url: `${networkApi.CreateVlan}`,
    method: 'post',
    data: data,
  })
}

export function getVlan(id) {
  return request({
    url: `${networkApi.GetVlan}${id}`,
    method: 'get',
  })
}

export function updateVlan(data) {
  return request({
    url: `${networkApi.UpdateVlan}`,
    method: 'put',
    data: data,
  })
}

export function deleteVlan(id) {
  return request({
    url: `${networkApi.DeleteVlan}${id}`,
    method: 'delete',
  })
}

export function vlanOptionsList(parameter) {
  return request({
    url: networkApi.VlanOptionsList,
    method: 'get',
    params: parameter,
  })
}
