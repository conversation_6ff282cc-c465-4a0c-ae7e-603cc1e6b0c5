import request from '@/utils/request'

const networkApi = {
  List: '/bff/network/vip'
}

export function listVip (parameter) {
  return request({
    url: networkApi.List,
    method: 'get',
    params: parameter
  })
}

export function getVip (id) {
  return request({
    url: networkApi.List + '/' + id,
    method: 'get'
  })
}

export function createVip (parameter) {
  return request({
    url: networkApi.List,
    method: 'post',
    data: parameter
  })
}

export function deleteVip (id) {
  return request({
    url: networkApi.List + '/' + id,
    method: 'delete'
  })
}

export function updateVip (parameter) {
  return request({
    url: networkApi.List,
    method: 'put',
    data: parameter
  })
}
