import request from '@/utils/request'

const api = {
  list: '/bff/network/export_ip',
  which: '/bff/network/export_ip/which',
  create: '/bff/network/export_ip',
  update: '/bff/network/export_ip',
  export: '/bff/network/export_ip/export',
  delete: '/bff/network/export_ip/',
  get: '/bff/network/export_ip/'
}

export function listNetworkExportIp (parameter) {
  return request({
    url: api.list,
    method: 'get',
    params: parameter
  })
}

export function getNetworkExportIp (id) {
  return request({
    url: `${api.get}${id}`,
    method: 'get'
  })
}

export function listExportIpWhich (parameter) {
  return request({
    url: api.which,
    method: 'get',
    params: parameter
  })
}

export function createNetworkExportIp (parameter) {
  return request({
    url: api.create,
    method: 'post',
    data: parameter
  })
}

export function updateNetworkExportIp (parameter) {
  return request({
    url: api.update,
    method: 'put',
    data: parameter
  })
}

export function exportNetworkExportIp (parameter) {
  return request({
    url: api.export,
    method: 'get',
    params: parameter
  })
}

export function deleteNetworkExportIp (id) {
  return request({
    url: `${api.delete}${id}`,
    method: 'delete'
  })
}
