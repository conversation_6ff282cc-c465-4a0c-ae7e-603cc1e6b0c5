import request from '@/utils/request'

const networkApi = {
  GetClientCountryName: '/bff/network/cloudflare/client-country-name',
  GetDomain: '/bff/network/cloudflare/domain',
  GetCloudflareBytesData: '/bff/network/cloudflare/bytes-data/list',
  GetCloudflareChartData: '/bff/network/cloudflare/chart-data/list',
}

export function getClientCountryNameList(parameter) {
  return request({
    url: networkApi.GetClientCountryName,
    method: 'get',
    params: parameter,
  })
}
export function getDomainList(parameter) {
  return request({
    url: networkApi.GetDomain,
    method: 'get',
    params: parameter,
  })
}
export function getCloudflareBytesDataList(parameter) {
  return request({
    url: networkApi.GetCloudflareBytesData,
    method: 'get',
    params: parameter,
  })
}
export function getCloudflareChartDataList(parameter) {
  return request({
    url: networkApi.GetCloudflareChartData,
    method: 'get',
    params: parameter,
  })
}

