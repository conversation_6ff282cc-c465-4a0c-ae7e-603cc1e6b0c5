import request from '@/utils/request'

const networkApi = {
  List: '/bff/network/dhcp_item'
}

export function listDhcpItem (parameter) {
  return request({
    url: networkApi.List,
    method: 'get',
    params: parameter
  })
}

export function getDhcpItem (id) {
  return request({
    url: networkApi.List + '/' + id,
    method: 'get'
  })
}

export function createDhcpItem (parameter) {
  return request({
    url: networkApi.List,
    method: 'post',
    data: parameter
  })
}

export function deleteDhcpItem (id) {
  return request({
    url: networkApi.List + '/' + id,
    method: 'delete'
  })
}

export function updateDhcpItem (parameter) {
  return request({
    url: networkApi.List,
    method: 'put',
    data: parameter
  })
}
