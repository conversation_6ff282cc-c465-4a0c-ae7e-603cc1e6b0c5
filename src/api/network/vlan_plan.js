import request from '@/utils/request'

const networkApi = {
  VlanPlanList: '/bff/network/vlan_plan',
  CreateVlanPlan: '/bff/network/vlan_plan/create',
  UpdateVlanPlan: '/bff/network/vlan_plan/update',
  DeleteVlanPlan: '/bff/network/vlan_plan/',
}

export function vlanPlanList(parameter) {
  return request({
    url: networkApi.VlanPlanList,
    method: 'get',
    params: parameter,
  })
}

export function createVlanPlan(data) {
  return request({
    url: `${networkApi.CreateVlanPlan}`,
    method: 'post',
    data: data,
  })
}


export function updateVlanPlan(data) {
  return request({
    url: `${networkApi.UpdateVlanPlan}`,
    method: 'put',
    data: data,
  })
}

export function deleteVlanPlan(id) {
  return request({
    url: `${networkApi.DeleteVlanPlan}${id}`,
    method: 'delete',
  })
}

