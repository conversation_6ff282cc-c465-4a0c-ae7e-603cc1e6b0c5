import request from '@/utils/request'

const api = {
  list: '/bff/network/lb',
  which: '/bff/network/lb/which',
  export: '/bff/network/lb/export',
  get: '/bff/network/lb/'
}

export function listNetworkLb (parameter) {
  return request({
    url: api.list,
    method: 'get',
    params: parameter
  })
}

export function getNetworkLb (id) {
  return request({
    url: `${api.get}${id}`,
    method: 'get'
  })
}

export function listNetworkLbWhich (parameter) {
  return request({
    url: api.which,
    method: 'get',
    params: parameter
  })
}

export function exportNetworkLb (parameter) {
  return request({
    url: api.export,
    method: 'get',
    params: parameter
  })
}
