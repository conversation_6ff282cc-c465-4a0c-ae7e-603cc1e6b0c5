import request from '@/utils/request'

const networkApi = {
  List: '/bff/network/eip',
  IdcList: '/bff/network/eip/idc/list',
}

export function listEip(parameter) {
  return request({
    url: networkApi.List,
    method: 'get',
    params: parameter
  })
}
export function getEipIdc() {
  return request({
    url: networkApi.IdcList,
    method: 'get',
  })
}
export function getEip(id) {
  return request({
    url: networkApi.List + '/' + id,
    method: 'get',
  })
}
