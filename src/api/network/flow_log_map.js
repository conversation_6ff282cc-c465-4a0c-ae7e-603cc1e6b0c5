import request from '@/utils/request'

const networkApi = {
  FlowLogMapList: '/bff/network/flow_log_map',
  CreateFlowLogMap: '/bff/network/flow_log_map/create',
  UpdateFlowLogMap: '/bff/network/flow_log_map/update',
  DeleteFlowLogMap: '/bff/network/flow_log_map/',
}

export function flowLogMapList(parameter) {
  return request({
    url: networkApi.FlowLogMapList,
    method: 'get',
    params: parameter,
  })
}

export function createFlowLogMap(data) {
  return request({
    url: `${networkApi.CreateFlowLogMap}`,
    method: 'post',
    data: data,
  })
}


export function updateFlowLogMap(data) {
  return request({
    url: `${networkApi.UpdateFlowLogMap}`,
    method: 'put',
    data: data,
  })
}

export function deleteFlowLogMap(id) {
  return request({
    url: `${networkApi.DeleteFlowLogMap}${id}`,
    method: 'delete',
  })
}

