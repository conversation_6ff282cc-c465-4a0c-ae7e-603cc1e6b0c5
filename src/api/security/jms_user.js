import request from '@/utils/request'

const jmsUser = {
  list: '/bff/asset/jms_user/user',
  ssh: '/bff/asset/jms_user/ssh_reset',
  pw: '/bff/asset/jms_user/pw_reset',
  bond: '/bff/asset/jms_user/bond',
  unblock: '/bff/asset/jms_user/unblock',
  asset: '/bff/asset/jms_user/asset',
  test: '/bff/asset/jms_user/asset/test',
  userInfo: '/bff/asset/jms_user/user/info',
  taskLog: '/bff/asset/jms_user/asset/task_log',
}

export function jmsAssetTaskLog(parameter) {
  return request({
    url: jmsUser.taskLog,
    method: 'get',
    params: parameter,
  })
}

export function jmsUserUpdate(parameter) {
  return request({
    url: jmsUser.userInfo,
    method: 'get',
    params: parameter,
  })
}

export function jmsAssetTest(data) {
  return request({
    url: jmsUser.test,
    method: 'post',
    data: data,
  })
}

export function jmsAssetList(parameter) {
  return request({
    url: jmsUser.asset,
    method: 'get',
    params: parameter,
  })
}

export function jmsUserList(parameter) {
  return request({
    url: jmsUser.list,
    method: 'get',
    params: parameter,
  })
}

export function jmsUserSSH(data) {
  return request({
    url: jmsUser.ssh,
    method: 'put',
    data: data,
  })
}

export function jmsUserUnblock(data) {
  return request({
    url: jmsUser.unblock,
    method: 'put',
    data: data,
  })
}

export function jmsUserPw(data) {
  return request({
    url: jmsUser.pw,
    method: 'put',
    data: data,
  })
}

export function jmsUserMfaBond(data) {
  return request({
    url: jmsUser.bond,
    method: 'post',
    data: data,
  })
}
