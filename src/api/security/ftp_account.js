import request from '@/utils/request'

const api = {
  List: '/bff/safety/ftp',
  Create: '/bff/safety/ftp',
  Update: '/bff/safety/ftp',
  Delete: '/bff/safety/ftp',
  which: '/bff/safety/ftp/distinct',
  Get: '/bff/safety/ftp',
}

export function getFtpAccountList(parameter) {
  return request({
    url: api.List,
    method: 'get',
    params: parameter,
  })
}

export function getFtpAccountInfo(parameter) {
  return request({
    url: `${api.Get}/${parameter.id}`,
    method: 'get',
  })
}

export function createFtpAccount(data) {
  return request({
    url: api.Create,
    method: 'post',
    data: data,
  })
}

export function updateFtpAccount(data) {
  return request({
    url: api.Update,
    method: 'put',
    data: data,
  })
}

export function deleteFtpAccount(data) {
  return request({
    url: `${api.Delete}/${data.id}`,
    method: 'delete',
    data: data,
  })
}

export function listFtpAccountWhich(parameter) {
  return request({
    url: api.which,
    method: 'get',
    params: parameter,
  })
}
