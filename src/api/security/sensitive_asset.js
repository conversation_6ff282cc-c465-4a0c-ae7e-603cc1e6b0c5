import request from '@/utils/request'

const api = {
  asset: '/bff/assets/sensitive',
}

export function getSensitiveAssetList(parameter) {
  return request({
    url: api.asset,
    method: 'get',
    params: parameter,
  })
}

export function createSensitiveAsset(data) {
  return request({
    url: api.asset,
    method: 'post',
    data: data,
  })
}

export function updateSensitiveAsset(data) {
  return request({
    url: api.asset,
    method: 'put',
    data: data,
  })
}

export function deleteSensitiveAsset(params) {
  return request({
    url:  api.asset,
    method: 'delete',
    params: params,
  })
}