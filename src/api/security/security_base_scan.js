import request from '@/utils/request'

const baseTask = {
  list: '/bff/security/base-scan-detail/list',
  creat: '/bff/security/base-scan-detail/create',
  put: '/bff/security/base-scan-detail/update',
  delete: '/bff/security/base-scan-detail/delete',
  history: '/bff/security/base-scan-detail/history/list',
}

export function baseTaskList(parameter) {
  return request({
    url: baseTask.list,
    method: 'get',
    params: parameter,
  })
}

export function baseTaskHistory(parameter) {
  return request({
    url: baseTask.history,
    method: 'get',
    params: parameter,
  })
}

export function baseTaskCreate(data) {
  return request({
    url: baseTask.creat,
    method: 'post',
    data: data,
  })
}

export function baseTaskUpdate(data) {
  return request({
    url: baseTask.put,
    method: 'put',
    data: data,
  })
}

export function baseTaskDelete(parameter) {
  return request({
    url: baseTask.delete,
    method: 'delete',
    params: parameter,
  })
}
