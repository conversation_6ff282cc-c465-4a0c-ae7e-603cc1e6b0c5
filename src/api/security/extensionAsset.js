/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-10-11 10:32:06
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-10-16 17:11:03
 * @FilePath: \cloud_web\src\api\security\extensionAsset.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const userAsset = {
  getList: '/bff/coder/cloud-chrome-extension/list',
  updateUserAsset: '/bff/coder/cloud-chrome-extension/update',
  unBlockUser: '/bff/coder/cloud-chrome-extension/unblock',
  deleteUser: '/bff/coder/cloud-chrome-extension/delete',
}
// 请求列表
export function getList(parameter) {
  return request({
    url: userAsset.getList,
    method: 'get',
    params: parameter,
  })
}
// 更新用户信息
export function updateUserAsset(data) {
  return request({
    url: `${userAsset.updateUserAsset}`,
    method: 'post',
    data: data,
  })
}
// 解禁用户信息
export function unBlockUser(data) {
  return request({
    url: `${userAsset.unBlockUser}`,
    method: 'post',
    data: data,
  })
}
// 用户删除
export function deleteUser(data) {
  return request({
    url: `${userAsset.deleteUser}`,
    method: 'delete',
    params: data,
  })
}
