import request from '@/utils/request'

const baselineApi = {
  List: '/bff/safety/baseline',
  ListGitlabPP: '/bff/safety/gitlab_pp',
  ListSecurityGroupRecode: '/bff/safety/security_group_recode',
  GetSecurityGroupRecode: '/bff/safety/security_group_recode/info',
  CreateSecurityGroupRecode: '/bff/safety/security_group_recode',
  BatchCreateSecurityGroupRecode: '/bff/safety/security_group_recode/batch',
  ExportSecurityGroupRecode: '/bff/safety/security_group_recode/export'
}

export function getBaselineList (parameter) {
  return request({
    url: baselineApi.List,
    method: 'get',
    params: parameter
  })
}

export function gitlabPPList (parameter) {
  return request({
    url: baselineApi.ListGitlabPP,
    method: 'get',
    params: parameter
  })
}

export function listSecurityGroupRecode (parameter) {
  return request({
    url: baselineApi.ListSecurityGroupRecode,
    method: 'get',
    params: parameter
  })
}

export function getSecurityGroupRecode (parameter) {
  return request({
    url: baselineApi.GetSecurityGroupRecode,
    method: 'get',
    params: parameter
  })
}

export function batchCreateSecurityGroupRecode (data) {
  return request({
    url: baselineApi.BatchCreateSecurityGroupRecode,
    method: 'post',
    data: data
  })
}

export function createSecurityGroupRecode (data) {
  return request({
    url: baselineApi.CreateSecurityGroupRecode,
    method: 'post',
    data: data
  })
}

export function exportSecurityGroupRecode (parameter) {
  return request({
    url: baselineApi.ExportSecurityGroupRecode,
    method: 'get',
    params: parameter
  })
}
