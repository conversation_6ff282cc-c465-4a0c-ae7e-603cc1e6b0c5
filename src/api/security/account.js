import request from '@/utils/request'

const Api = {
  List: '/bff/safety/account_info',
  ListPolicy: '/bff/safety/account/policy',
  password: '/bff/safety/account/password',
  mfa: '/bff/safety/account/mfa',
  web: '/bff/safety/account/userlogin',
  bindmfa: '/bff/safety/account/bindmfa',
  record: '/bff/safety/ramrecord',
  newSupplier: '/bff/safety/ram/newsupplier',
}

export function listAccountInfo(parameter) {
  return request({
    url: Api.List,
    method: 'get',
    params: parameter,
  })
}

export function PublicAccountListPolicy(parameter) {
  return request({
    url: Api.ListPolicy,
    method: 'get',
    params: parameter,
  })
}

export function PasswordReset(data) {
  return request({
    url: Api.password,
    method: 'put',
    data: data,
  })
}

export function MFAReset(data) {
  return request({
    url: Api.mfa,
    method: 'put',
    data: data,
  })
}

export function DisableWebLog(params) {
  return request({
    url: Api.web,
    method: 'delete',
    params: params,
  })
}

export function EnableWebLog(data) {
  return request({
    url: Api.web,
    method: 'post',
    data: data,
  })
}

export function BindAwsMFA(data) {
  return request({
    url: Api.bindmfa,
    method: 'post',
    data: data,
  })
}

export function GetUserRecord(parameter) {
  return request({
    url: Api.record,
    method: 'get',
    params: parameter,
  })
}

export function GetUserNewSupplier(parameter) {
  return request({
    url: Api.newSupplier,
    method: 'get',
    params: parameter,
  })
}


