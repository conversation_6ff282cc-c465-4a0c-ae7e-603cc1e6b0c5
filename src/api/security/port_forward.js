import request from '@/utils/request'

const api = {
  PortForward: '/bff/safety/security_port_forward',
  Get: '/bff/safety/security_port_forward/info',
}

export function listSecurityPortForward(parameter) {
  return request({
    url: api.PortForward,
    method: 'get',
    params: parameter,
  })
}

export function getSecurityPortForward(parameter) {
  return request({
    url: api.Get,
    method: 'get',
    params: parameter,
  })
}

export function createSecurityPortForward(data) {
  return request({
    url: api.PortForward,
    method: 'post',
    data: data,
  })
}

export function updateSecurityPortForward(data) {
  return request({
    url: api.PortForward,
    method: 'put',
    data: data,
  })
}

export function deleteSecurityPortForward(params) {
  return request({
    url: api.PortForward,
    method: 'delete',
    params: params,
  })
}
