import request from '@/utils/request'

const ramApi = {
  List: '/bff/safety/ram',
  Info: '/bff/safety/ram/info/',
  Update: '/bff/safety/ram/update',
  User: '/bff/safety/account/user'
}

export function getRamList (parameter) {
  return request({
    url: ramApi.List,
    method: 'get',
    params: parameter
  })
}

export function getRamInfo (id) {
  return request({
    url: `${ramApi.Info}${id}`,
    method: 'get'
  })
}

export function updateRam (data) {
  return request({
    url: `${ramApi.Update}`,
    method: 'put',
    data: data
  })
}

export function getRamUser(params) {
  return request({
    url: `${ramApi.User}`,
    method: 'get',
    params: params
  })
}

export function deleteRamUser(params) {
  return request({
    url: `${ramApi.User}`,
    method: 'delete',
    params: params
  })
}
