import request from '@/utils/request'

const ufileToken = {
  api: '/bff/safety/ufile_token',
}

export function ufileTokenList(parameter) {
  return request({
    url: ufileToken.api,
    method: 'get',
    params: parameter,
  })
}

export function ufileTokenPut(data) {
  return request({
    url: `${ufileToken.api}`,
    method: 'put',
    data: data,
  })
}

export function ufileTokenDelete(params) {
  return request({
    url: `${ufileToken.api}`,
    method: 'delete',
    params: params,
  })
}
