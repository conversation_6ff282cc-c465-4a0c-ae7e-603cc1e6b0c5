import request from '@/utils/request'

const api = {
  task: '/bff/asset/jms/base_line_task',
  log: '/bff/asset/jms/base_line_log',
}

export function getJmsBaseTaskList(parameter) {
  return request({
    url: api.task,
    method: 'get',
    params: parameter,
  })
}
export function getJmsBaseTaskLog(parameter) {
  return request({
    url: api.log,
    method: 'get',
    params: parameter,
  })
}

export function createJmsBaseTask(data) {
  return request({
    url: api.task,
    method: 'post',
    data: data,
  })
}

export function updateJmsBaseTask(data) {
  return request({
    url: api.task,
    method: 'put',
    data: data,
  })
}

export function deleteJmsBaseTask(params) {
  return request({
    url: api.task,
    method: 'delete',
    params: params,
  })
}
