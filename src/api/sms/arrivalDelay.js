import request from '@/utils/request'

const arrivalDelay = {
  ArrivalDelayList: '/bff/sms/arrival_delay',
  ArrivalDelayAccountList: '/bff/sms/arrival_delay_account',
  ArrivalDelayTemplateList: '/bff/sms/arrival_delay_template',
  ArrivalDelayCarrierList: '/bff/sms/arrival_delay_carrier',
  ArrivalDelayChart: '/bff/sms/arrival_delay_chart',
  ArrivalDelayTotalChart: '/bff/sms/arrival_delay_total_chart',
}

export function getArrivalDelayList(parameter) {
  return request({
    url: arrivalDelay.ArrivalDelayList,
    method: 'get',
    params: parameter,
  })
}

export function getArrivalDelayAccount(parameter) {
  return request({
    url: arrivalDelay.ArrivalDelayAccountList,
    method: 'get',
    params: parameter,
  })
}
export function getArrivalDelayTemplate(parameter) {
  return request({
    url: arrivalDelay.ArrivalDelayTemplateList,
    method: 'get',
    params: parameter,
  })
}
export function getArrivalDelayCarrier(parameter) {
  return request({
    url: arrivalDelay.ArrivalDelayCarrierList,
    method: 'get',
    params: parameter,
  })
}

export function getArrivalDelayChart(data) {
  return request({
    url: `${arrivalDelay.ArrivalDelayChart}`,
    method: 'post',
    data: data,
  })
}

export function getArrivalDelayTotalChart(data) {
  return request({
    url: `${arrivalDelay.ArrivalDelayTotalChart}`,
    method: 'post',
    data: data,
  })
}

