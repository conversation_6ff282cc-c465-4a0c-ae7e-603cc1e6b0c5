import request from '@/utils/request'

const deliveredRate = {
  DeliveredRateList: '/bff/sms/delivered_rate',
  DeliveredRateAccountList: '/bff/sms/delivered_rate_account',
  DeliveredRateTemplateList: '/bff/sms/delivered_rate_template',
  DeliveredRateCarrierList: '/bff/sms/delivered_rate_carrier',
  DeliveredRateChart: '/bff/sms/delivered_rate_chart',
  DeliveredRateSubmitChart: '/bff/sms/delivered_rate_submit_chart',
}

export function getDeliveredRateList(parameter) {
  return request({
    url: deliveredRate.DeliveredRateList,
    method: 'get',
    params: parameter,
  })
}

export function getDeliveredRateAccount(parameter) {
  return request({
    url: deliveredRate.DeliveredRateAccountList,
    method: 'get',
    params: parameter,
  })
}
export function getDeliveredRateTemplate(parameter) {
  return request({
    url: deliveredRate.DeliveredRateTemplateList,
    method: 'get',
    params: parameter,
  })
}
export function getDeliveredRateCarrier(parameter) {
  return request({
    url: deliveredRate.DeliveredRateCarrierList,
    method: 'get',
    params: parameter,
  })
}

export function getDeliveredRateChart(data) {
  return request({
    url: `${deliveredRate.DeliveredRateChart}`,
    method: 'post',
    data: data,
  })
}

export function DeliveredRateSubmitChart(data) {
  return request({
    url: `${deliveredRate.DeliveredRateSubmitChart}`,
    method: 'post',
    data: data,
  })
}

