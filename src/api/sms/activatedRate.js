import request from '@/utils/request'

const activatedRate = {
  ActivatedRateList: '/bff/sms/activated_rate',
  AreaCode: '/bff/sms/activated_rate_area_code',
  ActivatedRateChart: '/bff/sms/activated_rate_chart',
  ActivatedChart: '/bff/sms/activated_chart',
  ActivatedAvgRateChart: '/bff/sms/activated_rate_avg_chart',
}

export function getActivatedRateList(parameter) {
  return request({
    url: activatedRate.ActivatedRateList,
    method: 'get',
    params: parameter,
  })
}

export function getAreaCode(parameter) {
  return request({
    url: activatedRate.AreaCode,
    method: 'get',
    params: parameter,
  })
}

export function getActivatedRateChart(data) {
  return request({
    url: `${activatedRate.ActivatedRateChart}`,
    method: 'post',
    data: data,
  })
}

export function getActivated<PERSON>hart(data) {
  return request({
    url: `${activatedRate.ActivatedChart}`,
    method: 'post',
    data: data,
  })
}

export function getActivatedAvgRate<PERSON>hart(data) {
  return request({
    url: `${activatedRate.ActivatedAvgRateChart}`,
    method: 'post',
    data: data,
  })
}
