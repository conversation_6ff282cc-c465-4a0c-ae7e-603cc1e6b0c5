import request from '@/utils/request'

const desktopPermissionApi = {
  ListDesktopPermission: '/bff/desktop/desktop_permission',
  CreateDesktopPermission: '/bff/desktop/desktop_permission/create',
  DeleteDesktopPermission: '/bff/desktop/desktop_permission/',
  UpdateDesktopPermission: '/bff/desktop/desktop_permission/update',
  ListDesktopPermissionName: '/bff/desktop/desktop_permission/name',
  GetDesktopPermission: '/bff/desktop/desktop_permission/info/',
  getDesktopPermissionByIp: '/bff/desktop/desktop_permission_by_ip'
}

export function getDesktopPermissionList (parameter) {
  return request({
    url: desktopPermissionApi.ListDesktopPermission,
    method: 'get',
    params: parameter
  })
}
export function getDesktopPermissionInfo (id) {
  return request({
    url: `${desktopPermissionApi.GetDesktopPermission}${id}`,
    method: 'get'
  })
}
export function createDesktopPermission (data) {
  return request({
    url: `${desktopPermissionApi.CreateDesktopPermission}`,
    method: 'post',
    data: data
  })
}
export function getDesktopPermissionByIp (parameter) {
  return request({
    url: `${desktopPermissionApi.getDesktopPermissionByIp}`,
    method: 'get',
    params: parameter
  })
}
export function updateDesktopPermission (data) {
  return request({
    url: `${desktopPermissionApi.UpdateDesktopPermission}`,
    method: 'post',
    data: data
  })
}
export function deleteDesktopPermission (id) {
  return request({
    url: `${desktopPermissionApi.DeleteDesktopPermission}${id}`,
    method: 'delete'
  })
}
export function getDesktopPermissionNameList (parameter) {
  return request({
    url: desktopPermissionApi.ListDesktopPermissionName,
    method: 'get',
    params: parameter
  })
}
