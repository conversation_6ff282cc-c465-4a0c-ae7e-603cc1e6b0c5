import request from '@/utils/request'

const desktopUserApi = {
  ListDesktopUser: '/bff/desktop/desktop_user',
  ListAllDesktopUser: '/bff/desktop/desktop_user_list',
  ListRemoteDesktopUser: '/bff/desktop/remote_desktop_user',
  CreateDesktopUser: '/bff/desktop/desktop_user/create',
  DeleteDesktopUser: '/bff/desktop/desktop_user/',
  UpdateDesktopUser: '/bff/desktop/desktop_user/update',
  GetDesktopUser: '/bff/desktop/desktop_user/',
}

export function getDesktopUserList(parameter) {
  return request({
    url: desktopUserApi.ListDesktopUser,
    method: 'get',
    params: parameter,
  })
}
export function getAllDesktopUser(parameter) {
  return request({
    url: desktopUserApi.ListAllDesktopUser,
    method: 'get',
    params: parameter,
  })
}
export function getRemoteDesktopUserList(parameter) {
  return request({
    url: desktopUserApi.ListRemoteDesktopUser,
    method: 'get',
    params: parameter,
  })
}
export function getDesktopUserInfo(id) {
  return request({
    url: `${desktopUserApi.GetDesktopUser}${id}`,
    method: 'get',
  })
}
export function createDesktopUser(data) {
  return request({
    url: `${desktopUserApi.CreateDesktopUser}`,
    method: 'post',
    data: data,
  })
}
export function updateDesktopUser(data) {
  return request({
    url: `${desktopUserApi.UpdateDesktopUser}`,
    method: 'post',
    data: data,
  })
}
export function deleteDesktopUser(id) {
  return request({
    url: `${desktopUserApi.DeleteDesktopUser}${id}`,
    method: 'delete',
  })
}
