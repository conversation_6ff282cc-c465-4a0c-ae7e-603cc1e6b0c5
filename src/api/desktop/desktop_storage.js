import desktopRequest from '@/utils/desktopRequest'
import request from '@/utils/request'

const desktopTencentApi = {
  Ls: '/desktop/tencent/desktop/ls',
  Download: '/desktop/tencent/desktop/download',
  Upload: '/desktop/tencent/desktop/upload',
  Mkdir: '/desktop/tencent/desktop/mkdir',
  Rm: '/desktop/tencent/desktop/rm',
  Rename: '/desktop/tencent/desktop/rename',
  CheckNetwork: '/desktop/tencent/desktop/check-network'
}
// 默认混合云
const desktopApi = {
  Ls: '/desktop/ls',
  Download: '/desktop/download',
  Upload: '/desktop/upload',
  Mkdir: '/desktop/mkdir',
  Rm: '/desktop/rm',
  Rename: '/desktop/rename',
  CheckNetwork: '/desktop/check-network'
}

export function desktopTencentFileLs (data) {
  return request({
    url: `${desktopTencentApi.Ls}`,
    method: 'post',
    data: data
  })
}
export function desktopTencentFileDownload (parameter) {
  return request({
    url: desktopTencentApi.Download,
    method: 'get',
    params: parameter
  })
}
export function desktopTencentFileUpload (data) {
  return request({
    url: `${desktopTencentApi.Upload}`,
    method: 'post',
    data: data
  })
}
export function desktopTencentFileMkdir (data) {
  return request({
    url: `${desktopTencentApi.Mkdir}`,
    method: 'post',
    data: data
  })
}
export function desktopTencentFileRm (data) {
  return request({
    url: `${desktopTencentApi.Rm}`,
    method: 'post',
    data: data
  })
}
export function desktopTencentFileRename (data) {
  return request({
    url: `${desktopTencentApi.Rename}`,
    method: 'post',
    data: data
  })
}
export function checkNetwork (parameter) {
  return request({
    url: '/desktop/tencent/desktop/check-network',
    method: 'get',
    data: parameter,
  })
}


// 默认混合云
export function desktopFileLs (data) {
  return desktopRequest({
    url: `${desktopApi.Ls}`,
    method: 'post',
    data: data
  })
}
export function desktopFileDownload (parameter) {
  return desktopRequest({
    url: desktopApi.Download,
    method: 'get',
    params: parameter
  })
}
export function desktopFileUpload (data) {
  return desktopRequest({
    url: `${desktopApi.Upload}`,
    method: 'post',
    data: data
  })
}
export function desktopFileMkdir (data) {
  return desktopRequest({
    url: `${desktopApi.Mkdir}`,
    method: 'post',
    data: data
  })
}
export function desktopFileRm (data) {
  return desktopRequest({
    url: `${desktopApi.Rm}`,
    method: 'post',
    data: data
  })
}
export function desktopFileRename (data) {
  return desktopRequest({
    url: `${desktopApi.Rename}`,
    method: 'post',
    data: data
  })
}
