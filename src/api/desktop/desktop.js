import request from '@/utils/request'
import desktopRequest from '@/utils/desktopRequest'

const desktopApi = {
  List: '/bff/desktop/list_desktop_session',
  Disconnect: '/bff/desktop/disconnect_desktop_session',
  ListDesktop: '/bff/desktop/desktop',
  GetDesktopDashboard: '/bff/desktop/desktop_dashboard',
  Create: '/bff/desktop/desktop/create',
  Delete: '/bff/desktop/desktop/',
  Update: '/bff/desktop/desktop/update',
  Get: '/bff/desktop/desktop/',
  GetPermission: '/bff/desktop/desktop_permission/',
  Start: '/bff/desktop/desktop/start',
  Stop: '/bff/desktop/desktop/stop',
  Reboot: '/bff/desktop/desktop/reboot',
  CreateImage: '/bff/desktop/desktop/create_image',
  UpdateCronStop: '/bff/desktop/desktop/cron_stop/update',
  UpdateCron: '/bff/desktop/desktop/cron/update',
  TeskInfo: '/bff/cron/config/desktop/on_off',
  UpdateTeskInfo: '/bff/desktop/desktop/cron/update',
  Collect: '/bff/asset/term/favorite',
}
export function collectItem(parameter) {
  return request({
    url: desktopApi.Collect,
    method: 'post',
    data: parameter,
  })
}
export function getDesktopSessionList(parameter) {
  return request({
    url: desktopApi.List,
    method: 'get',
    params: parameter,
  })
}
export function getTeskInfo(parameter) {
  return request({
    url: desktopApi.TeskInfo,
    method: 'get',
    params: parameter,
  })
}
export function disconnectSessionTencent(data) {
  return request({
    url: '/desktop/tencent/desktop/disconnect_desktop_session',
    method: 'post',
    data: data,
  })
}
export function disconnectSession(data) {
  return desktopRequest({
    url: '/desktop/disconnect_desktop_session',
    method: 'post',
    data: data,
  })
}

export function updateTeskInfo(data) {
  return desktopRequest({
    url: '/desktop/desktop/cron/update',
    method: 'post',
    data: data,
  })
}
export function getDesktopList(parameter) {
  return request({
    url: desktopApi.ListDesktop,
    method: 'get',
    params: parameter,
  })
}
export function getDesktopInfo(id) {
  return request({
    url: `${desktopApi.Get}${id}`,
    method: 'get',
  })
}
export function createDesktop(data) {
  return request({
    url: `${desktopApi.Create}`,
    method: 'post',
    data: data,
  })
}
export function updateDesktop(data) {
  return request({
    url: `${desktopApi.Update}`,
    method: 'post',
    data: data,
  })
}
export function deleteDesktop(id) {
  return request({
    url: `${desktopApi.Delete}${id}`,
    method: 'delete',
  })
}
export function getPermission(parameter) {
  return request({
    url: `${desktopApi.GetPermission}`,
    params: parameter,
  })
}
export function stopDesktop(data) {
  return request({
    url: `${desktopApi.Stop}`,
    method: 'post',
    data: data,
  })
}
export function startDesktop(data) {
  return request({
    url: `${desktopApi.Start}`,
    method: 'post',
    data: data,
  })
}
export function rebootDesktop(data) {
  return request({
    url: `${desktopApi.Reboot}`,
    method: 'post',
    data: data,
  })
}
export function createImageDesktop(data) {
  return request({
    url: `${desktopApi.CreateImage}`,
    method: 'post',
    data: data,
  })
}
export function updateCronStop(data) {
  return request({
    url: `${desktopApi.UpdateCronStop}`,
    method: 'post',
    data: data,
  })
}
export function updateCron(data) {
  return request({
    url: `${desktopApi.UpdateCron}`,
    method: 'post',
    data: data,
  })
}

export function GetDesktopDashboard(parameter) {
  return request({
    url: `${desktopApi.GetDesktopDashboard}`,
    method: 'get',
    params: parameter,
  })
}
