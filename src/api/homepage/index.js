/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2022-08-04 17:31:38
 * @LastEditors: yhdup <EMAIL>
 * @LastEditTime: 2022-08-05 17:30:50
 * @FilePath: \cloud_web\src\api\homepage\index.js\
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'

const api = {
  getIngerest: '/bff/user/interests', // 获取感兴趣列表
  interestAct: '/bff/user/interest'// 感兴趣动作
}

export function getIngerestList (parameter) {
  return request({
    url: api.getIngerest,
    method: 'get',
    params: parameter
  })
}
export function interestAct (data) {
  return request({
    url: api.interestAct,
    method: 'put',
    data: data
  })
}
