/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-17 10:27:31
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-04-23 15:40:21
 * @FilePath: \cloud_web\src\api\ssoApi\index.js
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-02-22 15:31:05
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-04-20 17:55:50
 * @FilePath: \cloud_web\src\api\ssoApi\index.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import axios from 'axios'
import { getSsoPlatformId,getSsoWebApi } from '@/config'


const service = axios.create({
 // baseURL: 'https://webapi-sso-inernal.intsig.net',
 // baseURL: 'https://webapi-sso.intsig.net',
 baseURL: getSsoWebApi(),

 // baseURL: 'https://webapi-sso-sandbox.intsig.net',


 timeout: 50000
})
// request interceptor
service.interceptors.request.use(
 config => {
  // const names = store.getters.email.split('@')
  config.headers['Content-Type'] = 'application/json'
  config.headers['X-Token'] = noc.user.getToken()
  return config
 },
 error => {
  // do something with request error
  console.log(error) // for debug
  return Promise.reject(error)
 }
)

export function allOrgUser (data) {
 return service({
  url: `/common/organization/get-all-org-user?platform_id=${getSsoPlatformId()}`,
  method: 'post',
  data: data
 }).catch(function (error) {
  console.log('saveUserRole Error: ' + error)
 })
}
