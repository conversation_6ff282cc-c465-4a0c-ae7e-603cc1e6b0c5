/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-03-24 10:23:41
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-02-27 16:42:56
 * @FilePath: \cloud_web\src\api\bill\index.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const billInfo = {
  userCostDetail: '/bff/cost/month/personal/detail',
  teamMember: '/bff/user/cost/managmentPeople',
  saveCostWaveReason: '/bff/cost/month/personal/fluctuation/analysis',
  groutList: '/bff/user/cost/managmentTeam',
  groupInfo: '/bff/cost/month/team/detail',
  departmentList: '/bff/user/cost/dep',
  departmentInfo: '/bff/cost/month/dep/detail',
  orgList: '/bff/user/cost/org',
  orgInfo: '/bff/cost/month/org/detail',
  compInfo: '/bff/cost/month/company/detail',
  checkOutAccount: '/bff/cost/statistic/check_out_account',
  selectList: '/bff/cost/operation/which',
  busOptData: '/bff/cost/operation/dashboard',
  busTable: '/bff/cost/operation',
  exportCostOperationSupplier: '/bff/cost/operation/export',
  analysisReason: '/bff/cost/month/conclusion/fluctuation/analysis  ',
}
export function analysisReason(parameter) {
  return request({
    url: billInfo.analysisReason,
    method: 'put',
    data: parameter,
  })
}
export function userCostDetail(parameter) {
  return request({
    url: billInfo.userCostDetail,
    method: 'get',
    params: parameter,
  })
}
export function busTable(parameter) {
  return request({
    url: billInfo.busTable,
    method: 'get',
    params: parameter,
  })
}
export function busOptData(parameter) {
  return request({
    url: billInfo.busOptData,
    method: 'get',
    params: parameter,
  })
}

export function selectList(parameter) {
  return request({
    url: billInfo.selectList,
    method: 'get',
    params: parameter,
  })
}
export function teamMember(parameter) {
  return request({
    url: billInfo.teamMember,
    method: 'get',
    params: parameter,
  })
}
export function saveCostWaveReason(parameter) {
  return request({
    url: billInfo.saveCostWaveReason,
    method: 'put',
    data: parameter,
  })
}
export function groutList(params) {
  return request({
    url: billInfo.groutList,
    method: 'get',
    params,
  })
}
export function groupInfo(params) {
  return request({
    url: billInfo.groupInfo,
    method: 'get',
    params,
  })
}
export function departmentList(params) {
  return request({
    url: billInfo.departmentList,
    method: 'get',
    params,
  })
}
export function departmentInfo(params) {
  return request({
    url: billInfo.departmentInfo,
    method: 'get',
    params,
  })
}

export function orgList(params) {
  return request({
    url: billInfo.orgList,
    method: 'get',
    params,
  })
}
export function orgInfo(params) {
  return request({
    url: billInfo.orgInfo,
    method: 'get',
    params,
  })
}
export function compInfo(params) {
  return request({
    url: billInfo.compInfo,
    method: 'get',
    params,
  })
}

export function checkOutAccount(parameter) {
  return request({
    url: billInfo.checkOutAccount,
    method: 'get',
    params: parameter,
  })
}

export function exportCostOperationSupplier(parameter) {
  return request({
    url: billInfo.exportCostOperationSupplier,
    method: 'get',
    params: parameter,
  })
}
