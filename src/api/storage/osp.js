/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2022-08-09 19:53:51
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-06-26 19:53:19
 * @FilePath: \cloud_web\src\api\storage\osp.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import myRequest from '@/utils/myRequest'
// import uploadReq from '@/utils/uploadFIle'

import axios from 'axios'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { getApiCloudBase } from '@/config'
import { getAccessToken, removeToken } from '@/utils/auth'

// 创建 axios 实例
const downloadRequest = axios.create({
  // API 请求的默认前缀
  baseURL: getApiCloudBase(),
  timeout: 60000000, // 请求超时时间
})

// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message,
      })
    }
    if (error.response.status === 500 || error.response.status === 400 || data.code === 400) {
      notification.error({
        message: 'error',
        description: data.message,
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
downloadRequest.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
}, errorHandler)

// response interceptor
downloadRequest.interceptors.response.use(response => {
  return response.data
}, errorHandler)

const ospApi = {
  List: '/bff/storage/osp',
  FileName: '/bff/storage/osp/file_name',
  Upload: '/bff/storage/osp/upload',
  Download: '/bff/storage/osp/download/',
  CreateBucket: '/storage/osp/create-bucket',
  GetBuckets: '/storage/osp/get-bucket-account',
  getUserOpsList: '/osp',
  getPrincipal: '/storage/osp_share/principal/', //获取负责人
  getShareUser: '/storage/osp_share/users/', //获取共享人
  modifyPrincipal: '/storage/osp_share/update/principal', //修改负责人
  modifyUser: '/storage/osp_share/update/users', //修改共享人
  delStorage: '/storage/osp_share/del/', //删除共享存储
}
const bucketApi = {
  bucketInfo: '/storage/osp/bucket_access', // get获取用户桶信息
  deleteFile: '/storage/osp/delete_file', // delete用户删除文件
  // downloadFile: '/storage/osp/presign_file', // ge't 用户下载文件
  downloadFile: '/storage/osp/download_file', // ge't 用户下载文件
  uploadFile: '/storage/osp/upload_file', // post 用户上传文件
  userFileList: '/storage/osp/list_file', // get 用户文件列表
  createBucket: '/storage/osp/bucket/create', // create
  createFolder: '/storage/osp/create_folder', // 创建文件夹
  ospShare: '/storage/osp_share', // 共享网盘列表
  ospShareInfo: '/storage/osp_share/info/',
  updateOspShare: '/storage/osp_share/update',
  deleteOspShare: '/storage/osp_share/del/',
  checkFileSafety: '/storage/osp/check_file_safety',
  shareFile: '/storage/osp/share_file',
  shareLog: '/storage/osp/list_share_file_access_log',
}
// **************************************************************************
// 创建用户桶信息
export function createUserBucket(parameter) {
  return request({
    url: bucketApi.createBucket,
    method: 'post',
    data: parameter,
  })
}

// 获取用户桶信息
export function userBucketInfo(parameter) {
  return request({
    url: bucketApi.bucketInfo,
    method: 'get',
    params: parameter,
  })
}
// 用户删除文件
export function deleteFile(parameter) {
  return request({
    url: bucketApi.deleteFile,
    method: 'delete',
    data: parameter,
  })
}
// 用户下载文件
export function downloadFile(parameter) {
  return downloadRequest({
    url: bucketApi.downloadFile,
    method: 'get',
    params: parameter,
    responseType: 'blob',
  })
}

export function createFolder(parameter) {
  return request({
    url: bucketApi.createFolder,
    method: 'post',
    data: parameter,
  })
}
// 用户上传文件
export function uploadFile(parameter) {
  return request({
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    url: bucketApi.uploadFile,
    method: 'post',
    data: parameter,
  })
}
//  用户文件列表
export function userFileList(parameter) {
  return request({
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    url: bucketApi.userFileList,
    method: 'get',
    params: parameter,
  })
}
// *******************************************************************************

export function getUserOpsList(parameter) {
  return myRequest({
    url: ospApi.getUserOpsList,
    method: 'get',
    params: parameter,
  })
}

export function getOspList(parameter) {
  return request({
    url: ospApi.List,
    method: 'get',
    params: parameter,
  })
}
export function GetBuckets(parameter) {
  return request({
    url: ospApi.GetBuckets,
    method: 'get',
    params: parameter,
  })
}
export function getFileNameList() {
  return request({
    url: `${ospApi.FileName}`,
    method: 'get',
  })
}

export function postOspUpload(data) {
  return request({
    url: `${ospApi.Upload}`,
    method: 'post',
    data: data,
  })
}

export function getOspDownload(id) {
  return request({
    url: `${ospApi.Download}${id}`,
    method: 'get',
  })
}
export function CreateBucket(data) {
  return request({
    url: `${ospApi.CreateBucket}`,
    method: 'post',
    data: data,
  })
}
// 共享网盘列表
export function ospShare(parameter) {
  return request({
    url: bucketApi.ospShare,
    method: 'get',
    params: parameter,
  })
}

export function getOspShareInfo(id) {
  return request({
    url: `${bucketApi.ospShareInfo}${id}`,
    method: 'get',
  })
}

export function updateOspShare(data) {
  return request({
    url: `${bucketApi.updateOspShare}`,
    method: 'put',
    data: data,
  })
}

export function deleteOspShare(id) {
  return request({
    url: `${bucketApi.deleteOspShare}${id}`,
    method: 'delete',
  })
}

//获取共享存储负责人
export function getPrincipal(id) {
  return request({
    url: ospApi.getPrincipal + id,
    method: 'get',
  })
}
//获取共享存储共享人
export function getShareUser(id) {
  return request({
    url: ospApi.getShareUser + id,
    method: 'get',
  })
}
//修改负责人
export function modifyPrincipal(params) {
  return request({
    url: ospApi.modifyPrincipal,
    method: 'put',
    data: params,
  })
}
//修改共享人
export function modifyUser(params) {
  return request({
    url: ospApi.modifyUser,
    method: 'put',
    data: params,
  })
}
//删除共享存储
export function delStorage(id) {
  return request({
    url: ospApi.delStorage + id,
    method: 'delete',
  })
}

//获取共享存储共享人
export function checkFileSafety(params) {
  return request({
    url: bucketApi.checkFileSafety,
    method: 'get',
    params,
  })
}
export function shareLog(params) {
  return request({
    url: bucketApi.shareLog,
    method: 'get',
    params,
  })
}

export function shareFile(data) {
  return request({
    url: bucketApi.shareFile,
    method: 'post',
    data: data,
  })
}
