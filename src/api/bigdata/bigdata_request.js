import axios from 'axios'
import store from '@/store'
import { getBigDataApi } from '@/config'

const service = axios.create({
  baseURL: getBigDataApi(),
  timeout: 50000,
})
// request interceptor
service.interceptors.request.use(
  config => {
    config.headers['x-bigdata-user-uid'] = noc.user.getUserInfo().uid
    config.headers['x-bigdata-user-email'] = noc.user.getUserInfo().email
    config.headers['x-bigdata-user-cname'] = encodeURIComponent(noc.user.getUserInfo().name)
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

export function assetsList(data) {
  return service({
    url: `/api/component/auth/assets`,
    method: 'get',
    params: data,
  }).catch(function (error) {
    console.log('saveUserRole Error: ' + error)
  })
}
