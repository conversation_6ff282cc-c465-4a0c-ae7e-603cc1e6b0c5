import axios from 'axios'
import store from '@/store'

const service = axios.create({
  baseURL: 'https://bigdata.intsig.net',
  timeout: 50000,
})
// request interceptor
service.interceptors.request.use(
  config => {
    const names = store.getters.email.split('@')
    const email = store.getters.email
    config.headers['x-user'] = names[0]
    config.headers['x-term'] = 'CMDB'
    config.headers['x-bigdata-sso-user-uid'] = noc.user.getUserInfo().uid
    config.headers['x-bigdata-sso-user-email'] = email
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

export function saveUserRole(data) {
  return service({
    url: '/system/other/saveUserRole',
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('saveUserRole Error: ' + error)
  })
}

// export function roleTrees() {
//   return service({
//     url: '/api/rest_j/v1/dss/customize/perms',
//     method: 'get',
//   }).catch(function (error) {
//     console.log('roleTrees Error: ' + error)
//   })
// }
export function roleTrees() {
  return service({
    url: '/api/bigdata/component/auth/perms',
    method: 'get',
  }).catch(function (error) {
    console.log('roleTrees Error: ' + error)
  })
}
