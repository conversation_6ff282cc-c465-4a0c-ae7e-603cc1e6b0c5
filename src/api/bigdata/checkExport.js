// import request from '@/utils/request'
/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-15 19:14:53
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-02-06 11:29:34
 * @FilePath: \cloud_web\src\api\bigdata\checkExport.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import axios from 'axios'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { getApiCloudBase } from '@/config'
import { getAccessToken, removeToken } from '@/utils/auth'

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: getApiCloudBase(),
  timeout: 6000 // 请求超时时间
})

// 异常拦截处理器
const errorHandler = (error) => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message
      })
    }
    if (error.response.status === 500) {
      notification.error({
        message: 'error',
        description: data.message
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed'
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    // config.headers['Authorization'] = `Bearer ${token}`
    config.headers['Authorization'] = 'Basic YmlnZGF0YTo2eWhuN3VqbQ=='
  }

  return config
}, errorHandler)

export const workflowOrderApprove = function (params) {
  return request({
    url: '/workflow/order/approve',
    method: 'post',
    data: params
  })
}
export const workflowOrderId = function (id) {
  return request({
    url: `/workflow/order/info/${id}`,
    method: 'get',
    params: id
  })
}
export const workflowOrderCreate = function (data) {
  return request({
    url: '/workflow/order/create',
    method: 'post',
    data: data
  })
}
