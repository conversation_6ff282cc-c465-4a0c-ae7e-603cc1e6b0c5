import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
import { exportCostBillStatistic } from '@/api/cost/statistic'
const dataSetApi = {
  GlobalOtp: '/bff/auth/global-otp',
  CheckCode: '/bff/auth/global-otp/check ',
  SendCOde: '/bff/auth/global-otp/send',
  SlurmUserList: '/bff/ai/slurm/user',
  CreateSlurmUser: '/bff/ai/slurm/user/create',
  GetSlurmUser: '/bff/ai/slurm/user/info/',
  UpdateSlurmUser: '/bff/ai/slurm/user/update',
  DeleteSlurmUser: '/bff/ai/slurm/user/',
  SlurmJobList: '/bff/ai/slurm/job',
  GetSlurmJob: '/bff/ai/slurm/job/info/',
  RefreshSlurmJob: '/bff/ai/slurm/job/refresh',
  CreateSlurmJob: '/bff/ai/slurm/job/create',
  CancelSlurmJob: '/bff/ai/slurm/job/cancel',
  UpdateNoticeStatus: '/bff/ai/slurm/monitor/notice',
  GetSlurmSInfo: '/bff/ai/slurm/job/sinfo',
  SlurmJobReportList: '/bff/ai/slurm/job/report',
  SlurmJobReportByGroupList: '/bff/ai/slurm/job/report/group',
  SlurmNodeList: '/bff/ai/slurm/nodes',
  CreateSlurmNode: '/bff/ai/slurm/nodes/create',
  GetSlurmNode: '/bff/ai/slurm/nodes/info/',
  UpdateSlurmNode: '/bff/ai/slurm/nodes/update',
  DeleteSlurmNode: '/bff/ai/slurm/nodes/',
  SlurmGroupList: '/bff/ai/slurm/group',
  CreateSlurmGroup: '/bff/ai/slurm/group/create',
  GetSlurmGroup: '/bff/ai/slurm/group/info/',
  UpdateSlurmGroup: '/bff/ai/slurm/group/update',
  DeleteSlurmGroup: '/bff/ai/slurm/group/',
  GetSlurmGroupCount: '/bff/ai/slurm/group/count/',
  UpdateSlurmGroupCount: '/bff/ai/slurm/group/count/update',
  GetSlurmJobNvidiaSmi: '/bff/ai/slurm/nvidia-smi',
  SlurmNodeHealthyList: '/bff/ai/slurm/node/healthy',
  CreateSlurmNodeHealthy: '/bff/ai/slurm/node/healthy/create',
  GetSlurmNodeHealthy: '/bff/ai/slurm/node/healthy/info/',
  UpdateSlurmNodeHealthy: '/bff/ai/slurm/node/healthy/update',
  DeleteSlurmNodeHealthy: '/bff/ai/slurm/node/healthy/',
  SlurmDashboard: '/bff/ai/slurm/dashboard',
  RunSlurmSuspendedJob: '/bff/ai/slurm/job/run_suspended',
  SlurmUseInfo: '/bff/ai/slurm/job/group_used',
  ListAiTrainingNode: '/bff/ai/training/nodes',
  CreateAiTrainingNode: '/bff/ai/training/nodes/create',
  GetAiTrainingNode: '/bff/ai/training/nodes/info/',
  UpdateAiTrainingNode: '/bff/ai/training/nodes/update',
  DeleteAiTrainingNode: '/bff/ai/training/nodes/',
  SlurmPrice: '/bff/ai/slurm/price',
  SlurmCost: '/bff/ai/slurm/cost',
  ExportSlurmCost: '/bff/ai/slurm/cost/export',
  SlurmCostStatistical: '/bff/ai/slurm/cost_statistical',
  ExportSlurmCostStatistical: '/bff/ai/slurm/cost_statistical/export',
  SlurmSplitRatio: '/bff/ai/slurm/cost_split_ratio',
  SlurmJuiceSummaryList: '/bff/ai/slurm/juice_summary',
  CheckGroupAndPublicResource: '/bff/ai/slurm/job/check_resource',
}

export function slurmDashboard(parameter) {
  return request({
    url: dataSetApi.SlurmDashboard,
    method: 'get',
    params: parameter,
  })
}
export function sendCOde(parameter) {
  return request({
    url: `${dataSetApi.SendCOde}?type=${parameter}`,
    method: 'get',
  })
}
export function globalOtp(parameter) {
  return request({
    url: dataSetApi.GlobalOtp,
    method: 'get',
    params: parameter,
  })
}

export function slurmUserList(parameter) {
  return request({
    url: dataSetApi.SlurmUserList,
    method: 'get',
    params: parameter,
  })
}

export function createSlurmUser(data) {
  return request({
    url: `${dataSetApi.CreateSlurmUser}`,
    method: 'post',
    data: data,
  })
}
export function CheckCode(data) {
  return request({
    url: `${dataSetApi.CheckCode}`,
    method: 'post',
    data: data,
  })
}

export function getSlurmUserInfo(id) {
  return request({
    url: `${dataSetApi.GetSlurmUser}${id}`,
    method: 'get',
  })
}

export function updateSlurmUser(data) {
  return request({
    url: `${dataSetApi.UpdateSlurmUser}`,
    method: 'put',
    data: data,
  })
}

export function deleteSlurmUser(id) {
  return request({
    url: `${dataSetApi.DeleteSlurmUser}${id}`,
    method: 'delete',
  })
}

export function slurmJobList(parameter) {
  return globalTokenRequest({
    url: dataSetApi.SlurmJobList,
    method: 'get',
    params: parameter,
  })
}

export function getSlurmJobInfo(id) {
  return globalTokenRequest({
    url: `${dataSetApi.GetSlurmJob}${id}`,
    method: 'get',
  })
}

export function refreshSlurmJob() {
  return globalTokenRequest({
    url: dataSetApi.RefreshSlurmJob,
    method: 'get',
  })
}

export function createSlurmJob(data) {
  return request({
    url: `${dataSetApi.CreateSlurmJob}`,
    method: 'post',
    data: data,
  })
}

export function cancelSlurmJob(data) {
  return globalTokenRequest({
    url: `${dataSetApi.CancelSlurmJob}`,
    method: 'post',
    data: data,
  })
}

export function updateNoticeStatus(data) {
  return request({
    url: `${dataSetApi.UpdateNoticeStatus}`,
    method: 'put',
    data: data,
  })
}

export function getSlurmSInfo() {
  return globalTokenRequest({
    url: dataSetApi.GetSlurmSInfo,
    method: 'get',
  })
}

export function slurmJobReportList(parameter) {
  return request({
    url: dataSetApi.SlurmJobReportList,
    method: 'get',
    params: parameter,
  })
}

export function slurmJobReportByGroupList(parameter) {
  return request({
    url: dataSetApi.SlurmJobReportByGroupList,
    method: 'get',
    params: parameter,
  })
}

export function slurmNodeList(parameter) {
  return request({
    url: dataSetApi.SlurmNodeList,
    method: 'get',
    params: parameter,
  })
}

export function createSlurmNode(data) {
  return request({
    url: `${dataSetApi.CreateSlurmNode}`,
    method: 'post',
    data: data,
  })
}

export function getSlurmNode(id) {
  return request({
    url: `${dataSetApi.GetSlurmNode}${id}`,
    method: 'get',
  })
}

export function updateSlurmNode(data) {
  return request({
    url: `${dataSetApi.UpdateSlurmNode}`,
    method: 'put',
    data: data,
  })
}

export function deleteSlurmNode(id) {
  return request({
    url: `${dataSetApi.DeleteSlurmNode}${id}`,
    method: 'delete',
  })
}

export function slurmGroupList(parameter) {
  return request({
    url: dataSetApi.SlurmGroupList,
    method: 'get',
    params: parameter,
  })
}

export function createSlurmGroup(data) {
  return request({
    url: `${dataSetApi.CreateSlurmGroup}`,
    method: 'post',
    data: data,
  })
}

export function getSlurmGroup(id) {
  return request({
    url: `${dataSetApi.GetSlurmGroup}${id}`,
    method: 'get',
  })
}

export function updateSlurmGroup(data) {
  return request({
    url: `${dataSetApi.UpdateSlurmGroup}`,
    method: 'put',
    data: data,
  })
}

export function deleteSlurmGroup(id) {
  return request({
    url: `${dataSetApi.DeleteSlurmGroup}${id}`,
    method: 'delete',
  })
}

export function getSlurmGroupCount(email) {
  return request({
    url: `${dataSetApi.GetSlurmGroupCount}${email}`,
    method: 'get',
  })
}

export function updateSlurmGroupCount(data) {
  return request({
    url: `${dataSetApi.UpdateSlurmGroupCount}`,
    method: 'put',
    data: data,
  })
}

export function getSlurmJobNvidiaSmi(parameter) {
  return globalTokenRequest({
    url: dataSetApi.GetSlurmJobNvidiaSmi,
    method: 'get',
    params: parameter,
  })
}

export function slurmNodeHealthyList(parameter) {
  return request({
    url: dataSetApi.SlurmNodeHealthyList,
    method: 'get',
    params: parameter,
  })
}

export function createSlurmNodeHealthy(data) {
  return request({
    url: `${dataSetApi.CreateSlurmNodeHealthy}`,
    method: 'post',
    data: data,
  })
}

export function getSlurmNodeHealthy(id) {
  return request({
    url: `${dataSetApi.GetSlurmNodeHealthy}${id}`,
    method: 'get',
  })
}

export function updateSlurmNodeHealthy(data) {
  return request({
    url: `${dataSetApi.UpdateSlurmNodeHealthy}`,
    method: 'put',
    data: data,
  })
}

export function deleteSlurmNodeHealthy(id) {
  return request({
    url: `${dataSetApi.DeleteSlurmNodeHealthy}${id}`,
    method: 'delete',
  })
}

export function runSlurmSuspendedJob(data) {
  return globalTokenRequest({
    url: `${dataSetApi.RunSlurmSuspendedJob}`,
    method: 'post',
    data: data,
  })
}

export function SlurmUseInfo(parameter) {
  return request({
    url: dataSetApi.SlurmUseInfo,
    method: 'get',
    params: parameter,
  })
}

export function listAiTrainingNode(parameter) {
  return request({
    url: dataSetApi.ListAiTrainingNode,
    method: 'get',
    params: parameter,
  })
}

export function createAiTrainingNode(data) {
  return request({
    url: `${dataSetApi.CreateAiTrainingNode}`,
    method: 'post',
    data: data,
  })
}

export function getAiTrainingNode(id) {
  return request({
    url: `${dataSetApi.GetAiTrainingNode}${id}`,
    method: 'get',
  })
}

export function updateAiTrainingNode(data) {
  return request({
    url: `${dataSetApi.UpdateAiTrainingNode}`,
    method: 'put',
    data: data,
  })
}

export function deleteAiTrainingNode(id) {
  return request({
    url: `${dataSetApi.DeleteAiTrainingNode}${id}`,
    method: 'delete',
  })
}

export function createSlurmPrice(data) {
  return request({
    url: dataSetApi.SlurmPrice,
    method: 'post',
    data: data,
  })
}

export function listSlurmPrice(parameter) {
  return request({
    url: dataSetApi.SlurmPrice,
    method: 'get',
    params: parameter,
  })
}

export function updateSlurmPrice(data) {
  return request({
    url: dataSetApi.SlurmPrice,
    method: 'put',
    data: data,
  })
}

export function deleteSlurmPrice(id) {
  return request({
    url: `${dataSetApi.SlurmPrice}/${id}`,
    method: 'delete',
  })
}

export function getSlurmPrice(id) {
  return request({
    url: `${dataSetApi.SlurmPrice}/${id}`,
    method: 'get',
  })
}

export function listSlurmCostMonth(parameter) {
  return request({
    url: dataSetApi.SlurmCost,
    method: 'get',
    params: parameter,
  })
}

export function exportSlurmCostMonth(parameter) {
  return request({
    url: dataSetApi.ExportSlurmCost,
    method: 'get',
    params: parameter,
  })
}

export function listSlurmCostMonthStatistical(parameter) {
  return request({
    url: dataSetApi.SlurmCostStatistical,
    method: 'get',
    params: parameter,
  })
}

export function exportSlurmCostMonthStatistical(parameter) {
  return request({
    url: dataSetApi.ExportSlurmCostStatistical,
    method: 'get',
    params: parameter,
  })
}

export function listSlurmSplitRatio(parameter) {
  return request({
    url: dataSetApi.SlurmSplitRatio,
    method: 'get',
    params: parameter,
  })
}

export function createSlurmSplitRatio(data) {
  return request({
    url: dataSetApi.SlurmSplitRatio,
    method: 'post',
    data: data,
  })
}
export function updateSlurmSplitRatio(data) {
  return request({
    url: dataSetApi.SlurmSplitRatio,
    method: 'put',
    data: data,
  })
}

export function deleteSlurmSplitRatio(id) {
  return request({
    url: `${dataSetApi.SlurmSplitRatio}/${id}`,
    method: 'delete',
  })
}

export function getSlurmSplitRatio(id) {
  return request({
    url: `${dataSetApi.SlurmSplitRatio}/${id}`,
    method: 'get',
  })
}

export function slurmJuiceSummaryList(parameter) {
  return request({
    url: dataSetApi.SlurmJuiceSummaryList,
    method: 'get',
    params: parameter,
  })
}
export function checkGroupAndPublicResource(parameter) {
  return request({
    url: dataSetApi.CheckGroupAndPublicResource,
    method: 'get',
    params: parameter,
  })
}
