import request from '@/utils/request'

const dataSetApi = {
  DataSetList: '/bff/ai/data_set',
  CreateDataSet: '/bff/ai/data_set/create',
  GetDataSet: '/bff/ai/data_set/info/',
  UpdateDataSet: '/bff/ai/data_set/update',
  DeleteDataSet: '/bff/ai/data_set/',
}

export function dataSetList(parameter) {
  return request({
    url: dataSetApi.DataSetList,
    method: 'get',
    params: parameter,
  })
}

export function createDataSet(data) {
  return request({
    url: `${dataSetApi.CreateDataSet}`,
    method: 'post',
    data: data,
  })
}

export function getDataSetInfo(id) {
  return request({
    url: `${dataSetApi.GetDataSet}${id}`,
    method: 'get',
  })
}

export function updateDataSet(data) {
  return request({
    url: `${dataSetApi.UpdateDataSet}`,
    method: 'put',
    data: data,
  })
}

export function deleteDataSet(id) {
  return request({
    url: `${dataSetApi.DeleteDataSet}${id}`,
    method: 'delete',
  })
}
