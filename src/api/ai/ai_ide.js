import request from '@/utils/request'

const aiIdeApi = {
  AiIdeList: '/bff/ai/ide',
  UserAiIdeList: '/bff/ai/ide/user',
  CreateAiIde: '/bff/ai/ide/create',
  GetAiIde: '/bff/ai/ide/info/',
  UpdateAiIde: '/bff/ai/ide/update',
  DestroyAiIde: '/bff/ai/ide/destroy/',
  DeleteAiIde: '/bff/ai/ide/',
  AiIdeInstallPackage: '/bff/ai/ide/install',
  AiIdeImageList: '/bff/ai/ide/image',
  UserAiIdeImageList: '/bff/ai/ide/image/user',
  CreateAiIdeImage: '/bff/ai/ide/image/create',
  GetAiIdeImage: '/bff/ai/ide/image/info/',
  UpdateAiIdeImage: '/bff/ai/ide/image/update',
  EnterAiIdeImage: '/bff/ai/ide/image/enter',
  DeleteAiIdeImage: '/bff/ai/ide/image/',
  AiIdeNodeList: '/bff/ai/ide/node',
  CreateAiIdeNode: '/bff/ai/ide/node/create',
  GetAiIdeNode: '/bff/ai/ide/node/info/',
  UpdateAiIdeNode: '/bff/ai/ide/node/update',
  DeleteAiIdeNode: '/bff/ai/ide/node/',
  ListAiIdeGpuType: '/bff/ai/ide/gpu_type',
  ListAiIdeUserImage: '/bff/ai/ide/user/image',
  AiIdeUserGpuList: '/bff/ai/ide/user/gpu',
  CreateAiIdeUserGpu: '/bff/ai/ide/user/gpu/create',
  GetAiIdeUserGpu: '/bff/ai/ide/user/gpu/info/',
  UpdateAiIdeUserGpu: '/bff/ai/ide/user/gpu/update',
  DeleteAiIdeUserGpu: '/bff/ai/ide/user/gpu/',
}

export function getAiIdeList(parameter) {
  return request({
    url: aiIdeApi.AiIdeList,
    method: 'get',
    params: parameter,
  })
}

export function getUserAiIdeList(parameter) {
  return request({
    url: aiIdeApi.UserAiIdeList,
    method: 'get',
    params: parameter,
  })
}

export function getAiIdeInfo(id) {
  return request({
    url: `${aiIdeApi.GetAiIde}${id}`,
    method: 'get',
  })
}

export function createAiIde(data) {
  return request({
    url: `${aiIdeApi.CreateAiIde}`,
    method: 'post',
    data: data,
  })
}

export function updateAiIde(data) {
  return request({
    url: `${aiIdeApi.UpdateAiIde}`,
    method: 'put',
    data: data,
  })
}

export function destroyAiIde(id) {
  return request({
    url: `${aiIdeApi.DestroyAiIde}${id}`,
    method: 'delete',
  })
}

export function deleteAiIde(id) {
  return request({
    url: `${aiIdeApi.DeleteAiIde}${id}`,
    method: 'delete',
  })
}

export function aiIdeInstallPackage(data) {
  return request({
    url: `${aiIdeApi.AiIdeInstallPackage}`,
    method: 'post',
    data: data,
  })
}

export function getAiIdeImageList(parameter) {
  return request({
    url: aiIdeApi.AiIdeImageList,
    method: 'get',
    params: parameter,
  })
}

export function getUserAiIdeImageList(parameter) {
  return request({
    url: aiIdeApi.UserAiIdeImageList,
    method: 'get',
    params: parameter,
  })
}

export function createAiIdeImage(data) {
  return request({
    url: `${aiIdeApi.CreateAiIdeImage}`,
    method: 'post',
    data: data,
  })
}

export function getAiIdeImageInfo(id) {
  return request({
    url: `${aiIdeApi.GetAiIdeImage}${id}`,
    method: 'get',
  })
}

export function updateAiIdeImage(data) {
  return request({
    url: `${aiIdeApi.UpdateAiIdeImage}`,
    method: 'put',
    data: data,
  })
}

export function enterAiIdeImage(data) {
  return request({
    url: `${aiIdeApi.EnterAiIdeImage}`,
    method: 'post',
    data: data,
  })
}

export function deleteAiIdeImage(id) {
  return request({
    url: `${aiIdeApi.DeleteAiIdeImage}${id}`,
    method: 'delete',
  })
}

export function getAiIdeNodeList(parameter) {
  return request({
    url: aiIdeApi.AiIdeNodeList,
    method: 'get',
    params: parameter,
  })
}

export function getAiIdeNodeInfo(id) {
  return request({
    url: `${aiIdeApi.GetAiIdeNode}${id}`,
    method: 'get',
  })
}

export function createAiIdeNode(data) {
  return request({
    url: `${aiIdeApi.CreateAiIdeNode}`,
    method: 'post',
    data: data,
  })
}

export function updateAiIdeNode(data) {
  return request({
    url: `${aiIdeApi.UpdateAiIdeNode}`,
    method: 'put',
    data: data,
  })
}

export function deleteAiIdeNode(id) {
  return request({
    url: `${aiIdeApi.DeleteAiIdeNode}${id}`,
    method: 'delete',
  })
}

export function getListAiIdeGpuType() {
  return request({
    url: `${aiIdeApi.ListAiIdeGpuType}`,
    method: 'get',
  })
}

export function getListAiIdeUserImage() {
  return request({
    url: `${aiIdeApi.ListAiIdeUserImage}`,
    method: 'get',
  })
}

export function getAiIdeUserGpuList(parameter) {
  return request({
    url: aiIdeApi.AiIdeUserGpuList,
    method: 'get',
    params: parameter,
  })
}

export function getAiIdeUserGpuInfo(id) {
  return request({
    url: `${aiIdeApi.GetAiIdeUserGpu}${id}`,
    method: 'get',
  })
}

export function createAiIdeUserGpu(data) {
  return request({
    url: `${aiIdeApi.CreateAiIdeUserGpu}`,
    method: 'post',
    data: data,
  })
}

export function updateAiIdeUserGpu(data) {
  return request({
    url: `${aiIdeApi.UpdateAiIdeUserGpu}`,
    method: 'put',
    data: data,
  })
}

export function deleteAiIdeUserGpu(id) {
  return request({
    url: `${aiIdeApi.DeleteAiIdeUserGpu}${id}`,
    method: 'delete',
  })
}
