import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
const assetApi = {
  List: '/bff/asset',
  Info: '/bff/asset/',
  Idc: '/bff/asset/idc',
  Project: '/bff/asset/project',
  Team: '/bff/asset/team',
  CostUser: '/bff/asset/cost_user',
  Application: '/bff/asset/application',
  Organization: '/bff/asset/organization',
  Department: '/bff/asset/department',
  Update: '/bff/asset/update-info',
  ListJms: '/bff/asset/jms_auth/jms_log',
  CreateJmsAuth: '/bff/asset/jms_auth/create',
  CreateJmsAuthByAdmin: '/bff/asset/admin/jms_auth/create',
  CancelJmsAuth: '/bff/asset/jms_auth/delete',
  TermMenu: '/bff/asset/term/menu',
  CheckMFA: '/bff/asset/mfa',
  UpdateComment: '/bff/asset/comment/update',
  CreateServerAddEntry: '/bff/asset/asset_create_manual',
  AssetHostList: '/bff/asset/asset_host/list_host',
  CreateAssetHost: '/bff/asset/asset_host/create_host',
  UpdateAssetHost: '/bff/asset/asset_host/update_host',
  DeleteAssetHost: '/bff/asset/asset_host/delete/',
  DiskInfo: '/bff/asset/host/disk',
  DiskPartInfo: '/bff/asset/host/disk_part',
  NetworkInfo: '/bff/asset/host/network',
  CreateNetwork: '/bff/asset/host/network/create',
  UpdateNetwork: '/bff/asset/host/network/update',
  DeleteNetwork: '/bff/asset/host/network/delete',
  GetHostDashboard: '/bff/asset/host/dashboard',
  UserOrgAllInfo: '/bff/user/info/cascader',
  SwitchInfo: '/bff/asset/host/switch',
  CreateSwitch: '/bff/asset/host/switch/create',
  UpdateSwitch: '/bff/asset/host/switch/update',
  DeleteSwitch: '/bff/asset/host/switch/delete',
  ListDhcpUnusedData: '/bff/asset/host/dhcp/unused',
  assetMonitor: '/bff/asset/monitor/info',
  KvmOperate: '/bff/asset/kvm/operation',
  DhcpDelete: '/bff/asset/host/dhcp/unused/delete',
  ListDiskAssignable: '/bff/asset/host/disk_assignable',
  AssetOverview: '/bff/asset/host/dashboard/overview',
  ModuleOverview: '/bff/asset/host/dashboard/module',
  GpuType: '/bff/asset/gpu_type',
  AssetFile: '/bff/asset/file/list',
  DownloadAssetFile: '/asset/file/download',
  DownloadCcFile: '/coder/cloud-chrome-extension/download',
  DownloadAssetFileTaskRetry: '/bff/asset/file/fetch/retry',
  AssetFilter: 'bff/asset/filter',
  ColumnList: 'bff/asset/user/column/list',
  ColumnUpdate: 'bff/asset/user/column/update',
  CustomUserAssets: '/bff/asset/label/update',
  GetJmsTaskLog: '/bff/asset/jms-auth/task-log',
  GetProductLineByIp: '/bff/asset/product_line/ip',
}
export function assetFilter(parameter) {
  return request({
    url: assetApi.AssetFilter,
    method: 'post',
    data: parameter,
  })
}
export function customUserAssets(parameter) {
  return request({
    url: assetApi.CustomUserAssets,
    method: 'post',
    data: parameter,
  })
}
export function columnUpdate(parameter) {
  return request({
    url: assetApi.ColumnUpdate,
    method: 'post',
    data: parameter,
  })
}
export function columnList(parameter) {
  return request({
    url: assetApi.ColumnList,
    method: 'get',
    params: parameter,
  })
}
export function moduleOverview(parameter) {
  return request({
    url: assetApi.ModuleOverview,
    method: 'get',
    params: parameter,
  })
}
export function assetOverview(parameter) {
  return request({
    url: assetApi.AssetOverview,
    method: 'get',
    params: parameter,
  })
}

export function getAssetList(parameter) {
  return request({
    url: assetApi.List,
    method: 'get',
    params: parameter,
  })
}

export function GetUserOrgAllInfo(parameter) {
  return request({
    url: assetApi.UserOrgAllInfo,
    method: 'get',
    params: parameter,
  })
}

export function getAssetInfo(id) {
  return request({
    url: `${assetApi.Info}${id}`,
    method: 'get',
  })
}
export function getAssetListIdc() {
  return request({
    url: `${assetApi.Idc}`,
    method: 'get',
  })
}
export function getAssetListProject() {
  return request({
    url: `${assetApi.Project}`,
    method: 'get',
  })
}
export function getAssetListTeam() {
  return request({
    url: `${assetApi.Team}`,
    method: 'get',
  })
}
export function getAssetListCostUser() {
  return request({
    url: `${assetApi.CostUser}`,
    method: 'get',
  })
}
export function getAssetListApplication() {
  return request({
    url: `${assetApi.Application}`,
    method: 'get',
  })
}
export function getAssetListOrganization() {
  return request({
    url: `${assetApi.Organization}`,
    method: 'get',
  })
}
export function getAssetListDepartment() {
  return request({
    url: `${assetApi.Department}`,
    method: 'get',
  })
}
export function updateAsset(data) {
  console.log(data)
  return request({
    url: `${assetApi.Update}`,
    method: 'put',
    data: data,
  })
}
export function getJmsList(parameter) {
  return request({
    url: `${assetApi.ListJms}`,
    method: 'get',
    params: parameter,
  })
}

export function getTermMenu() {
  return request({
    url: `${assetApi.TermMenu}`,
    method: 'get',
  })
}

export function checkMfa(parameter) {
  return request({
    url: `${assetApi.CheckMFA}`,
    method: 'get',
    params: parameter,
  })
}

export function updateComment(data) {
  return request({
    url: `${assetApi.UpdateComment}`,
    method: 'post',
    data: data,
  })
}

export function CreateJmsAuth(data) {
  return request({
    url: `${assetApi.CreateJmsAuth}`,
    method: 'post',
    data: data,
  })
}
export function CreateJmsAuthByAdmin(data) {
  return globalTokenRequest({
    url: `${assetApi.CreateJmsAuthByAdmin}`,
    method: 'post',
    data: data,
  })
}
export function CancelJmsAuth(data) {
  return request({
    url: `${assetApi.CancelJmsAuth}`,
    method: 'post',
    data: data,
  })
}
export function createServerAddEntry(data) {
  return request({
    url: `${assetApi.CreateServerAddEntry}`,
    method: 'post',
    data: data,
  })
}

export function CreateAssetHost(data) {
  return request({
    url: `${assetApi.CreateAssetHost}`,
    method: 'post',
    data: data,
  })
}
export function UpdateAssetHost(data) {
  return request({
    url: `${assetApi.UpdateAssetHost}`,
    method: 'post',
    data: data,
  })
}

export function DeleteAssetHost(id) {
  return request({
    url: `${assetApi.DeleteAssetHost}${id}`,
    method: 'delete',
  })
}
export function getAsseHostList(parameter) {
  return request({
    url: assetApi.AssetHostList,
    method: 'get',
    params: parameter,
  })
}

export function getHostDiskList(parameter) {
  return request({
    url: assetApi.DiskInfo,
    method: 'get',
    params: parameter,
  })
}

export function listDiskAssignable(parameter) {
  return request({
    url: assetApi.ListDiskAssignable,
    method: 'get',
    params: parameter,
  })
}

export function getHostDiskInfo(id) {
  return request({
    url: assetApi.DiskInfo + '/' + id,
    method: 'get',
  })
}
export function getHostDiskPartList(parameter) {
  return request({
    url: assetApi.DiskPartInfo,
    method: 'get',
    params: parameter,
  })
}
export function getHostDiskPartInfo(id) {
  return request({
    url: assetApi.DiskPartInfo + '/' + id,
    method: 'get',
  })
}

export function getHostNetworkList(parameter) {
  return request({
    url: assetApi.NetworkInfo,
    method: 'get',
    params: parameter,
  })
}
export function GetHostDashboard(parameter) {
  return request({
    url: assetApi.GetHostDashboard,
    method: 'get',
    params: parameter,
  })
}
export function getHostNetworkInfo(id) {
  return request({
    url: assetApi.NetworkInfo + '/' + id,
    method: 'get',
  })
}
export function createNetwork(data) {
  return request({
    url: `${assetApi.CreateNetwork}`,
    method: 'post',
    data: data,
  })
}
export function updateNetwork(data) {
  return request({
    url: `${assetApi.UpdateNetwork}`,
    method: 'post',
    data: data,
  })
}
export function deleteNetwork(data) {
  return request({
    url: `${assetApi.DeleteNetwork}`,
    method: 'post',
    data: data,
  })
}

export function getHostSwitchList(parameter) {
  return request({
    url: assetApi.SwitchInfo,
    method: 'get',
    params: parameter,
  })
}
export function createSwitch(data) {
  return request({
    url: `${assetApi.CreateSwitch}`,
    method: 'post',
    data: data,
  })
}
export function updateSwitch(data) {
  return request({
    url: `${assetApi.UpdateSwitch}`,
    method: 'post',
    data: data,
  })
}
export function deleteSwitch(data) {
  return request({
    url: `${assetApi.DeleteSwitch}`,
    method: 'post',
    data: data,
  })
}
export function listDhcpUnusedData(parameter) {
  return request({
    url: assetApi.ListDhcpUnusedData,
    method: 'get',
    params: parameter,
  })
}

export function assetMonitor(parameter) {
  return request({
    url: assetApi.assetMonitor + '/' + parameter.ip,
    method: 'get',
  })
}

export function kvmOperate(data) {
  return globalTokenRequest({
    url: `${assetApi.KvmOperate}`,
    method: 'post',
    data: data,
  })
}

export function DeleteDhcp(data) {
  return request({
    url: `${assetApi.DhcpDelete}`,
    method: 'post',
    data: data,
  })
}

export function getAssetListGpuType() {
  return request({
    url: `${assetApi.GpuType}`,
    method: 'get',
  })
}

export function getJmsTaskLog(params) {
  return request({
    url: `${assetApi.GetJmsTaskLog}`,
    method: 'get',
    params: params,
  })
}

export function getProductLineByIp(params) {
  return request({
    url: `${assetApi.GetProductLineByIp}`,
    method: 'get',
    params: params,
  })
}

export function getAssetFile(params) {
  return request({
    url: `${assetApi.AssetFile}`,
    method: 'get',
    params: params,
  })
}
export function downloadAssetFileTaskRetry(data) {
  return request({
    url: `${assetApi.DownloadAssetFileTaskRetry}`,
    method: 'post',
    data: data,
  })
}

import axios from 'axios'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { getApiCloudBase } from '@/config'
import { getAccessToken, removeToken } from '@/utils/auth'

// 创建 axios 实例
const assetDownloadRequest = axios.create({
  // API 请求的默认前缀
  baseURL: getApiCloudBase(),
  timeout: 60000000, // 请求超时时间
})

// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message,
      })
    }
    if (error.response.status === 500 || error.response.status === 400 || data.code === 400) {
      notification.error({
        message: 'error',
        description: data.message,
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
assetDownloadRequest.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
}, errorHandler)

// response interceptor
assetDownloadRequest.interceptors.response.use(response => {
  return response.data
}, errorHandler)
export function downloadAssetFile(params) {
  return assetDownloadRequest({
    url: `${assetApi.DownloadAssetFile}`,
    method: 'get',
    params: params,
    responseType: 'blob',
  })
}
