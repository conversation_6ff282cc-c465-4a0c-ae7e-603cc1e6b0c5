/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-11-27 14:16:23
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-25 17:59:37
 * @FilePath: \cloud_web\src\api\login.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const userApi = {
  Login: '/bff/login',
  Logout: '/bff/loginout',
  ForgePassword: '/auth/forge-password',
  Register: '/auth/register',
  twoStepCode: '/auth/2step-code',
  SendSms: '/account/sms',
  SendSmsErr: '/account/sms_err',
  // get my info
  UserInfo: '/bff/userinfo',
  UserMenu: '/user/nav',
  userTheme: '/bff/user/theme',
}

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function login() {
  return request({
    url: userApi.Login,
    method: 'get',
    // headers: { 'X-Token': noc.user.getToken() },
    headers: { 'Cloud-Token': noc.user.getToken() },
  })
}

export function getSmsCaptcha(parameter) {
  return request({
    url: userApi.SendSms,
    method: 'post',
    data: parameter,
  })
}

export function getInfo() {
  return request({
    url: userApi.UserInfo,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  })
}

export function getCurrentUserNav() {
  return request({
    url: userApi.UserMenu,
    method: 'get',
  })
}

export function logout() {
  return request({
    url: userApi.Logout,
    method: 'get',
  })
}

/**
 * get user 2step code open?
 * @param parameter {*}
 */
export function get2step(parameter) {
  return request({
    url: userApi.twoStepCode,
    method: 'post',
    data: parameter,
  })
}

export function putUserTheme(data) {
  return request({
    url: userApi.userTheme,
    method: 'put',
    data: data,
  })
}
