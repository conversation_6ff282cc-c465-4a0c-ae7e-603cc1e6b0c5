/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-24 19:37:08
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-24 19:42:11
 * @FilePath: \cloud_web\src\api\db\dbApproval.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'

const urls = {
  mongodbBackupList: '/bff/db/mongodb_backup/list',
  tidbBackupList: '/bff/db/tidb_backup/list',
  mongodbBackupDateList: '/bff/db/mongodb_backup/date/list',
  tidbBackupDateList: '/bff/db/tidb_backup/date/list',
}
export const getMongodbBackupList = function (params) {
  return request({
    method: 'get',
    url: urls.mongodbBackupList,
    params: params
  })
}
export const getTidbBackupList = function (params) {
  return request({
    method: 'get',
    url: urls.tidbBackupList,
    params: params
  })
}

export const getMongodbBackupDateList = function () {
  return request({
    method: 'get',
    url: urls.mongodbBackupDateList,
  })
}

export const getTidbBackupDateList = function () {
  return request({
    method: 'get',
    url: urls.tidbBackupDateList,
  })
}
