import request from '@/utils/request'

const safeGatewayApi = {
  List: '/bff/db/safe_gateway',
  Create: '/bff/db/safe_gateway',
  Delete: '/bff/db/safe_gateway/'
}

export function getSafeGatewayList (parameter) {
  return request({
    url: safeGatewayApi.List,
    method: 'get',
    params: parameter
  })
}

export function safeGatewayCreate (data) {
  return request({
    url: `${safeGatewayApi.Create}`,
    method: 'post',
    data: data
  })
}

export function safeGatewayDelete (id) {
  return request({
    url: `${safeGatewayApi.Delete}${id}`,
    method: 'delete'
  })
}
