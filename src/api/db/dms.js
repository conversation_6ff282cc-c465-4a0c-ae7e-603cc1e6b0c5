/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-27 14:29:16
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-11-01 11:52:56
 * @FilePath: \cloud_web\src\api\db\dms.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'

const urls = {
  mysql_ip: '/bff/db/mysql_ip',
  mysql_ip_dbname: '/bff/db/mysql_ip_dbname',
  mysql_dbname: '/bff/db/mysql_dbnmaes',
  mysql_dbname_ip: '/bff/db/mysql_dbname_ip',
  mysql_dbname_tables: '/bff/db/mysql_dbname_tables',
  clickhouse: '/bff/db/clickhouse_tree',
  sql_history: '/bff/db/mysql_query_sql/log',
  comment_ipdbs: '/bff/db/comment/dbs',
  comment_tables: '/bff/db/comment/tables',
  comment: '/bff/db/comment/field',
  comments: '/bff/db/comment/fields',
  dba_comment: '/bff/db/comment/push',
  ck_store: '/bff/db/ckunfreeze',
  ck_result: '/bff/db/ckunfreeze/result',
}

export const auditDownloadList = function (data, cancelToken) {
  return request({
    method: 'get',
    url: 'bff/db/ck_audit_download',
    params: data,
    cancelToken
  })
}
export const createAuditDownload = function (data, cancelToken) {
  return request({
    method: 'post',
    url: '/bff/db/ck_audit_download',
    data: data,
    cancelToken
  })
}
export const configAuditDownload = function (data, cancelToken) {
  return request({
    method: 'put',
    url: '/bff/db/ck_audit_download',
    data: data,
    cancelToken
  })
}


export const getIpList = function (params) {
  return request({
    method: 'get',
    url: urls.mysql_ip,
    params: params
  })
}
export const getIpDbList = function (params) {
  return request({
    method: 'get',
    url: urls.mysql_ip_dbname,
    params: params
  })
}
export const getDbList = function (params) {
  return request({
    method: 'get',
    url: urls.mysql_dbname,
    params: params
  })
}
export const getDbIpList = function (params) {
  return request({
    method: 'get',
    url: urls.mysql_dbname_ip,
    params: params
  })
}
export const getTableList = function (params) {
  return request({
    method: 'get',
    url: urls.mysql_dbname_tables,
    params: params
  })
}

export const getSqlHistory = function (params) {
  return request({
    method: 'get',
    url: urls.sql_history,
    params: params
  })
}
export const getClickHouseTree = function (params) {
  return request({
    method: 'get',
    url: urls.clickhouse,
    params: params
  })
}

export const getCIpDbList = function () {
  return request({
    method: 'get',
    url: urls.comment_ipdbs,
  })
}

export const getCTableList = function (params) {
  return request({
    method: 'get',
    url: urls.comment_tables,
    params: params
  })
}

export const UpdateTableComment = function (data) {
  return request({
    method: 'put',
    url: urls.comment,
    data: data
  })
}

export const UpdateBatchTableComment = function (data) {
  return request({
    method: 'put',
    url: urls.comments,
    data: data
  })
}

export const dbaPushComment = function (data) {
  return request({
    method: 'post',
    url: urls.dba_comment,
    data: data
  })
}

export const getCkIdcDbsTables = function (params) {
  return request({
    method: 'get',
    url: urls.ck_store,
    params: params
  })
}

export const getCkUnfreezeResult = function (params) {
  return request({
    method: 'get',
    url: urls.ck_result,
    params: params
  })
}
