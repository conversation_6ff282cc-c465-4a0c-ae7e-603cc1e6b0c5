import request from '@/utils/request'

const mysqlTemplateApi = {
  MysqlTemplate: '/bff/dbm/mysql_template',
  GetMysqlTemplateList: '/bff/dbm/mysql_template_select',
  CheckDefaultMysqlTemplate: '/bff/dbm/mysql_template/check_default'
}

export function listMysqlTemplate (parameter) {
  return request({
    url: mysqlTemplateApi.MysqlTemplate,
    method: 'get',
    params: parameter
  })
}

export function createMysqlTemplate (data) {
  return request({
    url: mysqlTemplateApi.MysqlTemplate,
    method: 'post',
    data: data
  })
}

export function updateMysqlTemplate (data) {
  return request({
    url: mysqlTemplateApi.MysqlTemplate,
    method: 'put',
    data: data
  })
}

export function getMysqlTemplate (data) {
  return request({
    url: `${mysqlTemplateApi.MysqlTemplate}/${data.id}`,
    method: 'get'
  })
}

export function deleteMysqlTemplate (data) {
  return request({
    url: `${mysqlTemplateApi.MysqlTemplate}/${data.id}`,
    method: 'delete'
  })
}

export function checkDefaultMysqlTemplate (parameter) {
  return request({
    url: mysqlTemplateApi.CheckDefaultMysqlTemplate,
    method: 'get',
    params: parameter
  })
}

export function getMysqlTemplateList (parameter) {
  return request({
    url: mysqlTemplateApi.GetMysqlTemplateList,
    method: 'get',
    params: parameter
  })
}
