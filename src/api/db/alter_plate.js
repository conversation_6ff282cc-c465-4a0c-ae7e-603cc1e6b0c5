import request from '@/utils/request'

const urls = {
  bussiness: '/bff/db/alter/bussiness',
  dbInfo: '/bff/db/alter/dbInfo',
  plate: '/bff/db/alter/plate',
  info: '/bff/db/alter/info',
  status: '/bff/db/alter/status',
  nameCheck: '/bff/db/alter/name'
}

export const getBussiness = function (params) {
  return request({
    method: 'get',
    url: urls.bussiness,
    params: params
  })
}
export const getDbInfo = function (params) {
  return request({
    method: 'get',
    url: urls.dbInfo,
    params: params
  })
}

export const getAlterPlate = function (params) {
  return request({
    method: 'get',
    url: urls.plate,
    params: params
  })
}

export const getAlterInfo = function (params) {
  return request({
    method: 'get',
    url: urls.info,
    params: params
  })
}
export const alterStatusChange = function (data) {
  return request({
    method: 'put',
    url: urls.status,
    data: data
  })
}

export const alterPlateDelete = function (data) {
  return request({
    method: 'delete',
    url: `${urls.plate}/${data.id}`
  })
}

export const alterPlateUpdate = function (data) {
  return request({
    method: 'put',
    url: urls.plate,
    data: data
  })
}

export const alterNameCheck = function (params) {
  return request({
    method: 'get',
    url: urls.nameCheck,
    params: params
  })
}

