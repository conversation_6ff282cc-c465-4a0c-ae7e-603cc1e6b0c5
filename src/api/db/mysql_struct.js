import request from '@/utils/request'

const urls = {
  ip: '/bff/db/table_struct/ip',
  tree: '/bff/db/table_struct/tree',
  struct: '/bff/db/table_struct/struct',
  comment: '/bff/db/table_struct/comment',
  structSql: '/bff/db/table_struct/table_sql'
}
export const getIp = function (params) {
  return request({
    method: 'get',
    url: urls.ip,
    params: params
  })
}
export const getTree = function (params) {
  return request({
    method: 'get',
    url: urls.tree,
    params: params
  })
}

export const getStruct = function (params) {
  return request({
    method: 'get',
    url: urls.struct,
    params: params
  })
}

export const changeFiledComment = function (data) {
  return request({
    method: 'put',
    url: urls.comment,
    data: data
  })
}

export const getTableStructSql = function (params) {
  return request({
    method: 'get',
    url: urls.structSql,
    params: params
  })
}
