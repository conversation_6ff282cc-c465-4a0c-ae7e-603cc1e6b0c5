import request from '@/utils/request'

const redisTemplateApi = {
  RedisTemplate: '/bff/dbm/redis_template',
  GetRedisTemplateList: '/bff/dbm/redis_template_select',
  CheckDefaultRedisTemplate: '/bff/dbm/redis_template/check_default'
}

export function listRedisTemplate (parameter) {
  return request({
    url: redisTemplateApi.RedisTemplate,
    method: 'get',
    params: parameter
  })
}

export function createRedisTemplate (data) {
  return request({
    url: redisTemplateApi.RedisTemplate,
    method: 'post',
    data: data
  })
}

export function updateRedisTemplate (data) {
  return request({
    url: redisTemplateApi.RedisTemplate,
    method: 'put',
    data: data
  })
}

export function getRedisTemplate (data) {
  return request({
    url: `${redisTemplateApi.RedisTemplate}/${data.id}`,
    method: 'get'
  })
}

export function deleteRedisTemplate (data) {
  return request({
    url: `${redisTemplateApi.RedisTemplate}/${data.id}`,
    method: 'delete'
  })
}

export function checkDefaultRedisTemplate (parameter) {
  return request({
    url: redisTemplateApi.CheckDefaultRedisTemplate,
    method: 'get',
    params: parameter
  })
}

export function getRedisTemplateList (parameter) {
  return request({
    url: redisTemplateApi.GetRedisTemplateList,
    method: 'get',
    params: parameter
  })
}
