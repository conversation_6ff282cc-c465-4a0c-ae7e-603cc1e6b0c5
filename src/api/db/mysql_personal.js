import request from '@/utils/request'

const mysqlPersonalApi = {
  List: '/bff/db/mysql_personal',
  Create: '/bff/db/mysql_personal/create',
  DbName: '/bff/db/mysql_personal/db_name',
  Delete: '/bff/db/mysql_personal',
}

export function getMysqlPersonalList(parameter) {
  return request({
    url: mysqlPersonalApi.List,
    method: 'get',
    params: parameter,
  })
}

export function MysqlPersonalCreate(data) {
  return request({
    url: `${mysqlPersonalApi.Create}`,
    method: 'post',
    data: data,
  })
}

export function getMysqlPersonalDbNameList() {
  return request({
    url: mysqlPersonalApi.DbName,
    method: 'get',
  })
}

export function MysqlPersonalDelete(data) {
  return request({
    url: `${mysqlPersonalApi.Delete}/${data.id}?version=${data.version}`,
    method: 'delete',
  })
}
