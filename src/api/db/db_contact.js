import request from '@/utils/request'

const urls = {
    dbContactList: '/bff/db/contact',
    dbContactUpdate: '/bff/db/contact',
}

export const listDbContact = function (params) {
    return request({
        method: 'get',
        url: urls.dbContactList,
        params: params,
    })
}

export const updateDbContact = function (data) {
    return request({
        method: 'put',
        url: urls.dbContactUpdate,
        data: data,
    })
}

