import request from '@/utils/request'

const mysqlInstanceApi = {
  MysqlInstance: '/bff/dbm/mysql_instance',
  MysqlInstanceVip: '/bff/dbm/mysql_instance/vip',
  MysqlInstanceAsset: '/bff/dbm/mysql_instance/asset',
  DbActionLog: '/bff/dbm/db_action_log',
  UpdateInstanceTemplate: '/bff/dbm/mysql_instance/change_template',
  MysqlInstanceAccount: '/bff/dbm/mysql_instance/account',
  DeleteMysqlAccountStatus: '/bff/dbm/mysql_instance/account_delete_status',
  DeleteMysqlAccount: '/bff/dbm/mysql_instance/account_delete',
  TableSchema: '/bff/dbm/mysql_instance/database',
  OperationLog: '/bff/dbm/mysql_instance/operation_log',
  SlowLog: '/bff/dbm/mysql_instance/slow_log',
  SlowLogTrend: '/bff/dbm/mysql_instance/slow_log_trend',
  BackupManual: '/bff/dbm/mysql_instance/backup/manual',
  BackupManualRun: '/bff/dbm/mysql_instance/backup/run',
  BackupBinlog: '/bff/dbm/mysql_instance/binlog/info',
  BackupList: '/bff/dbm/mysql_instance/backup/auto',
  ChangeOrder: '/bff/dbm/mysql_instance/change_order',
  BackupTask: '/bff/dbm/mysql_instance/backup/task',
  ipMysqlList: '/bff/dbm/mysql_instance/backup/nodes',
  instanceTask: '/bff/dbm/mysql_instance/backup/task',
  taskFlush: 'bff/dbm/mysql_instance/backup/flush',
  BackupRestoreMasterSlave: '/bff/dbm/mysql_instance/backup/master_slave',
  BackupRestoreMasterSlaveResult:'/bff/dbm/mysql_instance/backup/master_slave_result'
}

export function listMysqlInstance(parameter) {
  return request({
    url: mysqlInstanceApi.MysqlInstance,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceVip(parameter) {
  return request({
    url: mysqlInstanceApi.MysqlInstanceVip,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceAsset(parameter) {
  return request({
    url: mysqlInstanceApi.MysqlInstanceAsset,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceAccount(parameter) {
  return request({
    url: mysqlInstanceApi.MysqlInstanceAccount,
    method: 'get',
    params: parameter,
  })
}

export function listDbActionLog(parameter) {
  return request({
    url: mysqlInstanceApi.DbActionLog,
    method: 'get',
    params: parameter,
  })
}

export function getMysqlInstance(data) {
  return request({
    url: `${mysqlInstanceApi.MysqlInstance}/${data.id}`,
    method: 'get',
  })
}

export function updateInstanceTemplate(data) {
  return request({
    url: mysqlInstanceApi.UpdateInstanceTemplate,
    method: 'post',
    data: data,
  })
}

export function deleteMysqlAccountStatus(data) {
  return request({
    url: mysqlInstanceApi.DeleteMysqlAccountStatus,
    method: 'put',
    data: data,
  })
}

export function deleteMysqlAccount(data) {
  return request({
    url: mysqlInstanceApi.DeleteMysqlAccount,
    method: 'put',
    data: data,
  })
}

export function getMysqlInstanceTableSchma(parameter) {
  return request({
    url: mysqlInstanceApi.TableSchema,
    method: 'get',
    params: parameter,
  })
}

export function deleteMysqlInstanceTableSchma(data) {
  return request({
    url: mysqlInstanceApi.TableSchema,
    method: 'put',
    data: data,
  })
}

export function listMysqlInstanceOperationLog(parameter) {
  return request({
    url: mysqlInstanceApi.OperationLog,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceSlowLog(parameter) {
  return request({
    url: mysqlInstanceApi.SlowLog,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceSlowLogTrend(parameter) {
  return request({
    url: mysqlInstanceApi.SlowLogTrend,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceBackupManul(parameter) {
  return request({
    url: mysqlInstanceApi.BackupManual,
    method: 'get',
    params: parameter,
  })
}

export function mysqlInstanceBackupManulRun(data) {
  return request({
    url: mysqlInstanceApi.BackupManualRun,
    method: 'post',
    data: data,
  })
}

export function mysqlInstanceBackupList(parameter) {
  return request({
    url: mysqlInstanceApi.BackupList,
    method: 'get',
    params: parameter,
  })
}

export function mysqlInstanceBackupTask(parameter) {
  return request({
    url: mysqlInstanceApi.BackupTask,
    method: 'get',
    params: parameter,
  })
}

export function mysqlInstanceBinlog(parameter) {
  return request({
    url: mysqlInstanceApi.BackupBinlog,
    method: 'get',
    params: parameter,
  })
}

export function listMysqlInstanceChangeOrder(parameter) {
  return request({
    url: mysqlInstanceApi.ChangeOrder,
    method: 'get',
    params: parameter,
  })
}

export function getIpsList(parameter) {
  return request({
    url: mysqlInstanceApi.ipMysqlList,
    method: 'get',
    params: parameter,
  })
}

export function createMysqlInstanceTask(data) {
  return request({
    url: mysqlInstanceApi.instanceTask,
    method: 'post',
    data: data,
  })
}

export function getTaskFlush(parameter) {
  return request({
    url: mysqlInstanceApi.taskFlush,
    method: 'get',
    params: parameter,
  })
}

export function PostBackupRestoreMasterSlave(data) {
  return request({
    url: mysqlInstanceApi.BackupRestoreMasterSlave,
    method: 'post',
    data: data,
  })
}


export function getBackupRestoreMasterSlaveResult(parameter) {
  return request({
    url: mysqlInstanceApi.BackupRestoreMasterSlaveResult,
    method: 'get',
    params: parameter,
  })
}