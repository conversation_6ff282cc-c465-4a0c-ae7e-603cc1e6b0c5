import request from '@/utils/request'

const drmsApi = {
  List: '/bff/db/drms',
  Ip: '/bff/db/drms/ip',
  Name: '/bff/db/drms/name',
  DbType: '/bff/db/drms/db_type',
  BackupList: '/bff/db/drms/backup',
  CreateBackupList: '/bff/db/drms/backup/create',
  GetBackupList: '/bff/db/drms/backup/info/',
  UpdateBackupList: '/bff/db/drms/backup/update',
  DeleteBackupList: '/bff/db/drms/backup/'
}

export function getDrmsList (parameter) {
  return request({
    url: drmsApi.List,
    method: 'get',
    params: parameter
  })
}

export function getDrmsIpList () {
  return request({
    url: drmsApi.Ip,
    method: 'get'
  })
}

export function getDrmsNameList () {
  return request({
    url: drmsApi.Name,
    method: 'get'
  })
}

export function getDrmsDbTypeList () {
  return request({
    url: drmsApi.DbType,
    method: 'get'
  })
}

export function getBackupList (parameter) {
  return request({
    url: drmsApi.BackupList,
    method: 'get',
    params: parameter
  })
}

export function getBackupInfo (id) {
  return request({
    url: `${drmsApi.GetBackupList}${id}`,
    method: 'get'
  })
}

export function createBackupList (data) {
  return request({
    url: `${drmsApi.CreateBackupList}`,
    method: 'post',
    data: data
  })
}

export function updateBackupList (data) {
  return request({
    url: `${drmsApi.UpdateBackupList}`,
    method: 'put',
    data: data
  })
}

export function deleteBackupList (id) {
  return request({
    url: `${drmsApi.DeleteBackupList}${id}`,
    method: 'delete'
  })
}
