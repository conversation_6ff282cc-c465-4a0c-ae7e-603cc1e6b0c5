/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-24 19:37:08
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-25 18:27:22
 * @FilePath: \cloud_web\src\api\db\dbApproval.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'

const urls = {
  dbList: '/bff/db/mysql_host_dbname',
  dbNamesList: '/bff/db/dcl/dbnames',
  downloadRetry: '/bff/db/sql_download_retry',
  tableLists: '/bff/db/table_lists',
}
export const tableLists = function (params) {
  return request({
    method: 'get',
    url: urls.tableLists,
    params: params,
  })
}
export const getDbList = function (params) {
  return request({
    method: 'get',
    url: urls.dbList,
    params: params,
  })
}
export const getDbNamesList = function (params) {
  return request({
    method: 'get',
    url: urls.dbNamesList,
    params: params,
  })
}
export const downloadRetry = function (data) {
  return request({
    method: 'post',
    url: urls.downloadRetry,
    data: data,
  })
}
