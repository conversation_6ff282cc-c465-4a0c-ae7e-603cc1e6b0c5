/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-05-23 17:49:50
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-05-23 17:55:28
 * @FilePath: \cloud_web\src\api\db\serverMonitor.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

const monitorApi = {
  MonitorDashboard: '/bff/asset/monitor_dashboard',
}

export function monitorDashboard(parameter) {
  return request({
    url: monitorApi.MonitorDashboard,
    method: 'get',
    params: parameter,
  })
}
