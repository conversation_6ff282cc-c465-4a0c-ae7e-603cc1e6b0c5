import request from '@/utils/request'

const redisInstanceApi = {
  RedisInstance: '/bff/dbm/redis_instance',
  RedisInstanceVip: '/bff/dbm/redis_instance/vip',
  RedisInstanceAsset: '/bff/dbm/redis_instance/asset',
  ReedisIp: '/bff/dbm/redis/ips',
  RedisSlowLog: '/bff/dbm/redis/slow_log',
}

export function listRedisInstance(parameter) {
  return request({
    url: redisInstanceApi.RedisInstance,
    method: 'get',
    params: parameter,
  })
}

export function listRedisInstanceVip(parameter) {
  return request({
    url: redisInstanceApi.RedisInstanceVip,
    method: 'get',
    params: parameter,
  })
}

export function listRedisInstanceAsset(parameter) {
  return request({
    url: redisInstanceApi.RedisInstanceAsset,
    method: 'get',
    params: parameter,
  })
}

export function getRedisInstance(data) {
  return request({
    url: `${redisInstanceApi.RedisInstance}/${data.id}`,
    method: 'get',
  })
}

export function listRedisNodeIp(parameter) {
  return request({
    url: redisInstanceApi.ReedisIp,
    method: 'get',
    params: parameter,
  })
}


export function listRedisSlowLog(parameter) {
  return request({
    url: redisInstanceApi.RedisSlowLog,
    method: 'get',
    params: parameter,
  })
}


