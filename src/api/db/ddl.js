
import request from '@/utils/request'

const urls = {
  dblUpload: '/bff/db/uploadsql',
  dblDownloadload: '/bff/db/downloadsql',
  ddlConfirm: '/bff/db/uploadsql/confirm',
  dbPasswdCheck: '/bff/db/passwd_check',
  dbLevel: '/bff/db/dml/datalevel',
  retry: '/bff/db/ddl/retry',
  dclcheckHostPassword: '/bff/db/dcl/checkpassword',
  dclGenSQL: '/bff/db/dcl/generate',
  allRetry: '/bff/db/ddl/allretry',
}

export const dclGenerateAuthSqls = function (data) {
  return request({
    method: 'post',
    url: urls.dclGenSQL,
    data: data
  })
}
export const dclGetPasword = function (data) {
  return request({
    method: 'post',
    url: urls.dclcheckHostPassword,
    data: data
  })
}
export const dblUpload = function (data) {
  return request({
    method: 'post',
    url: urls.dblUpload,
    data: data
  })
}
export const dblDownloadload = function (params) {
  return request({
    method: 'get',
    url: urls.dblDownloadload,
    params: params
  })
}
export const dblConfirm = function (data) {
  return request({
    method: 'post',
    url: urls.ddlConfirm,
    data: data
  })
}

export const dbPasswdCheck = function (data) {
  return request({
    method: 'post',
    url: urls.dbPasswdCheck,
    data: data
  })
}

export const dmlDataLevels = function (data) {
  return request({
    method: 'post',
    url: urls.dbLevel,
    data: data
  })
}
export const dbSqlSingle = function (data) {
  return request({
    method: 'post',
    url: urls.retry,
    data: data
  })
}

export const allErrorRetry = function (params) {
  return request({
    method: 'get',
    url: urls.allRetry,
    params: params
  })
}
