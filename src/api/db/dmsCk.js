/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-27 14:29:16
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-11-01 11:52:56
 * @FilePath: \cloud_web\src\api\db\dms.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import request from '@/utils/request'
import axios from 'axios'
import store from '@/store'

import { getApiCloudBase } from '@/config'
import notification from 'ant-design-vue/es/notification'
import { getAccessToken,removeToken } from '@/utils/auth'


const urls = {
  authTreeList: '/bff/db/ckauth/tree',
  favorite: '/bff/db/ck/favorite',
  uATL: '/bff/db/ck/user_tree',
  searchData: '/bff/db/clickhouse_query_sql',
  sql_run: '/bff/db/mysql_query_sql',
  cklog: '/bff/db/ck_query_sql/log',
  highQuota: '/bff/db/ck/highquota/order'
}

const request1 = axios.create({
  baseURL: getApiCloudBase(),
  timeout: 300000 // 请求超时时间
})

request1.interceptors.request.use(config => {
  const token = getAccessToken()
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
})
// 异常拦截处理器
const errorHandler = (error) => {
  if (error.response) {
    const token = getAccessToken()
    const data = error.response.data
    if (error.response.status === 500 || error.response.status === 504) {
      notification.error({
        message: 'error',
        description: data.message
      })
    }
    if (error.response.status === 401) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed'
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

request1.interceptors.response.use((response) => {
  return response
}, errorHandler)

export const ckSearch = function (data, cancelToken) {
  return request1({
    method: 'post',
    url: urls.searchData,
    data: data,
    cancelToken
  })
}

export const sqlRun = function (data, cancelToken) {
  return request1({
    method: 'post',
    data: data,
    url: urls.sql_run,
    cancelToken
  })
}

export const getCkAuthTreeList = function () {
  return request({
    method: 'get',
    url: urls.authTreeList
  })
}
export const getCkUserAuthTreeList = function (params) {
  return request({
    method: 'get',
    url: urls.uATL,
    params: params
  })
}

export const getUserFavorite = function (params) {
  return request({
    method: 'get',
    url: urls.favorite,
    params: params
  })
}
export const creatUserFavorite = function (data) {
  return request({
    method: 'post',
    url: urls.favorite,
    data: data
  })
}
export const deleteUserFavorite = function (id) {
  return request({
    method: 'delete',
    url: urls.favorite + '/' + id
  })
}

export const getCkSqlHistory = function (params) {
  return request({
    method: 'get',
    url: urls.cklog,
    params: params
  })
}

export const creatHighQuotaOrder = function (data) {
  return request({
    method: 'post',
    url: urls.highQuota,
    data: data
  })
}
