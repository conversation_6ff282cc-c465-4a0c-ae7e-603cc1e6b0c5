/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 10:21:20
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-04-24 10:22:47
 * @FilePath: \cloud_web\src\api\dashboard\index.js
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import request from '@/utils/request'

const costApi = {
 productsInfo: '/bff/user/workbench/products',
}

export function productsInfo (parameter) {
  return request({
    url: costApi.productsInfo,
    method: 'get',
    params: parameter
  })
}
export function createProducts (parameter) {
 return request({
   url: costApi.productsInfo,
   method: 'post',
   data: parameter
 })
}
