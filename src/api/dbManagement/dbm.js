import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
const mysqlApi = {
  account: '/bff/dbm/mysql/account',
  accountChangePwd: '/bff/dbm/mysql/account_for_change_pwd',
  batchAccountChangePwd: '/bff/dbm/mysql/batch_account_for_change_pwd',
  accountDelete: '/bff/dbm/mysql/account_delete',
  slave: '/bff/dbm/mysql/master_slave',
  init: '/bff/dbm/mysql/init',
  initRun: '/bff/dbm/mysql/init_run',
  ck: '/bff/dbm/clickhouse/asset',
  ckf: '/bff/dbm/clickhouse/asset_fliter',
  asset: '/bff/dbm/mysql/asset',
  status: '/bff/dbm/mysql/account_status',
}

const clickhouseApi = {
  pull: '/bff/cron/clickhouse/asset_pull',
  asset: '/bff/dbm/mysql/asset',
}

const rcdaApi = {
  account: '/bff/dbm/mysql/rcda',
  password: '/bff/dbm/mysql/rcda/passwd',
  token: '/bff/dbm/mysql/rcda/token',
  check: '/bff/dbm/mysql/rcda/check',
  instancePass: '/bff/dbm/mysql/rcda/updatepasswd',
  batch_update_dba_user: '/bff/dbm/mysql/rcda/batch_all_passwd',
}

const redis = {
  ip: '/bff/dbm/redis/privateip',
  aliyun: '/bff/dbm/redis/aliyun',
  hypire: '/bff/dbm/redis/ip_info',
}

const regUrl = {
  api: '/bff/dbm/regurl',
}

const database = {
  api: '/bff/dbm/datebases',
}

const mongodb = {
  api: '/bff/dbm/mongodb/list',
}

export function getmysqlAccountList(parameter) {
  return request({
    url: mysqlApi.account,
    method: 'get',
    params: parameter,
  })
}

export function getmysqlAccountForChangePwdList(parameter) {
  return request({
    url: mysqlApi.accountChangePwd,
    method: 'get',
    params: parameter,
  })
}
export function creatBatchAccountForChangePwd(data) {
  return request({
    url: mysqlApi.batchAccountChangePwd,
    method: 'post',
    data: data,
  })
}

export function deleteMysqlAccount(data) {
  return request({
    url: mysqlApi.accountDelete,
    method: 'post',
    data: data,
  })
}

export function getmysqlSlvaeList(parameter) {
  return request({
    url: mysqlApi.slave,
    method: 'get',
    params: parameter,
  })
}

export function getmysqlInitList(parameter) {
  return request({
    url: mysqlApi.init,
    method: 'get',
    params: parameter,
  })
}

export function mysqlInitRun(data) {
  return request({
    url: mysqlApi.initRun,
    method: 'put',
    data: data,
  })
}

export function getClickhouseAssetFilter() {
  return request({
    url: mysqlApi.ckf,
    method: 'get',
  })
}

export function getClickhouseAsset(parameter) {
  return request({
    url: mysqlApi.ck,
    method: 'get',
    params: parameter,
  })
}

export function syncClickhouseAsset(parameter) {
  return request({
    url: clickhouseApi.pull,
    method: 'get',
    params: parameter,
  })
}

export function updateClickhouseAsset(data) {
  return request({
    url: clickhouseApi.asset,
    method: 'put',
    data: data,
  })
}

export function getMysqlAsset(parameter) {
  return request({
    url: mysqlApi.asset,
    method: 'get',
    params: parameter,
  })
}

export function getmysqlRCDA(parameter) {
  return globalTokenRequest({
    url: rcdaApi.account,
    method: 'get',
    params: parameter,
  })
}

export function getmysqlPassword(data) {
  return globalTokenRequest({
    url: rcdaApi.password,
    method: 'post',
    data: data,
  })
}

export function deleteMysqlRCDA(data) {
  return globalTokenRequest({
    url: rcdaApi.account,
    method: 'put',
    data: data,
  })
}

export function updataPasswdMysqlRCDA(data) {
  return globalTokenRequest({
    url: rcdaApi.password,
    method: 'put',
    data: data,
  })
}

export function createRCDA(data) {
  return globalTokenRequest({
    url: rcdaApi.account,
    method: 'post',
    data: data,
  })
}

export function checkRCDAToken(data) {
  return globalTokenRequest({
    url: rcdaApi.token,
    method: 'put',
    data: data,
  })
}

export function checkRCDAUser(data) {
  return globalTokenRequest({
    url: rcdaApi.check,
    method: 'put',
    data: data,
  })
}

export function mysqlAccountStatus(data) {
  return request({
    url: mysqlApi.status,
    method: 'put',
    data: data,
  })
}

export function updataMysqlInstancePass(data) {
  return globalTokenRequest({
    url: rcdaApi.instancePass,
    method: 'put',
    data: data,
  })
}

export function getRedisPriveateIp() {
  return request({
    url: redis.ip,
    method: 'get',
  })
}

export function getRedisAliyun(parameter) {
  return request({
    url: redis.aliyun,
    method: 'get',
    params: parameter,
  })
}

export function getRedisInfo(parameter) {
  return request({
    url: redis.hypire,
    method: 'get',
    params: parameter,
  })
}

export function getRegUrl(parameter) {
  return request({
    url: regUrl.api,
    method: 'get',
    params: parameter,
  })
}

export function createRegUrl(data) {
  return request({
    url: regUrl.api,
    method: 'post',
    data: data,
  })
}

export function updateRegUrl(data) {
  return request({
    url: regUrl.api,
    method: 'put',
    data: data,
  })
}

export function delRegUrl(parameter) {
  return request({
    url: regUrl.api,
    method: 'delete',
    params: parameter,
  })
}

export function getDatabases(parameter) {
  return request({
    url: database.api,
    method: 'get',
    params: parameter,
  })
}

export function updateDatabases(data) {
  return request({
    url: database.api,
    method: 'put',
    data: data,
  })
}

export function getMongodb(parameter) {
  return request({
    url: mongodb.api,
    method: 'get',
    params: parameter,
  })
}

export const UpdateDbaUserPW = function (params) {
  return globalTokenRequest({
    method: 'get',
    url: rcdaApi.batch_update_dba_user,
    params: params,
  })
}
