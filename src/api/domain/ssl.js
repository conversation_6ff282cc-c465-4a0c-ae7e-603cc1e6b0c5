import request from '@/utils/request'

const sslApi = {
  List: '/bff/domain/ssl',
  Create: '/bff/domain/ssl/upload',
  Delete: '/bff/domain/ssl/delete/',
  Update: '/bff/domain/ssl/update',
  certList: '/bff/domain/ssl/cert_name'
}

export function getSslList(parameter) {
  return request({
    url: sslApi.List,
    method: 'get',
    params: parameter
  })
}
export function uploadSsl(data) {
  return request({
    url: `${sslApi.Create}`,
    method: 'post',
    data: data
  })
}
export function deleteSsl(id) {
  return request({
    url: `${sslApi.Delete}${id}`,
    method: 'delete'
  })
}
export function updateSsl(data) {
  return request({
    url: `${sslApi.Update}`,
    method: 'put',
    data: data
  })
}

export function getDomainSslList() {
  return request({
    url: sslApi.certList,
    method: 'get',
  })
}
