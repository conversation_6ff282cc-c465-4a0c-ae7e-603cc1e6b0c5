import request from '@/utils/request'

const domainApi = {
  List: '/bff/domain/dns',
  Type: '/bff/domain/dns/type',
  Domain: '/bff/domain/dns/domain',
  Source: '/bff/domain/dns/source',
  Label: '/bff/domain/dns/label/update',
  Check: '/bff/domain/dns/dcheck',
  ThirdCheck: '/bff/domain/dns/third_check',
  wafGateway: '/bff/domain/dns/waf_gateway',
  wafDelete: '/bff/domain/dns/waf',
  delete: '/bff/domain/dns/',
  backupList: '/bff/domain/dns/backup',
  renew: '/bff/domain/dns/renew',
  cancelDnsMonitor: '/bff/domain/dns/cancel_monitor'
}

export function getDnsList(parameter) {
  return request({
    url: domainApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getDnsBackupList(parameter) {
  return request({
    url: domainApi.backupList,
    method: 'get',
    params: parameter,
  })
}

export function deleteWaf(parameter) {
  return request({
    url: domainApi.wafDelete,
    method: 'delete',
    params: parameter,
  })
}

export function deleteDns(id) {
  return request({
    url: `${domainApi.delete}${id}`,
    method: 'delete',
  })
}

export function getDnsTypeList(id) {
  return request({
    url: `${domainApi.Type}`,
    method: 'get',
  })
}
export function getDnsDomainList(params) {
  return request({
    url: `${domainApi.Domain}`,
    method: 'get',
    params: params,
  })
}
export function getDnsSourceList() {
  return request({
    url: `${domainApi.Source}`,
    method: 'get',
  })
}
export function updateDnsLabel(data) {
  return request({
    url: domainApi.Label,
    method: 'put',
    data: data,
  })
}
export function updateDnsRenew(data) {
  return request({
    url: domainApi.renew,
    method: 'put',
    data: data,
  })
}


export function checkDnsDelete(params) {
  return request({
    url: domainApi.Check,
    method: 'get',
    params: params,
  })
}
export function thirdServerCheck(data) {
  return request({
    url: domainApi.ThirdCheck,
    method: 'post',
    data: data,
  })
}

export function getDnsWafGateway(parameter) {
  return request({
    url: domainApi.wafGateway,
    method: 'get',
    params: parameter,
  })
}
export function cancelDnsMonitor(parameter) {
  return request({
    url: domainApi.cancelDnsMonitor,
    method: 'post',
    data: parameter,
  })
}


const geolocationOptions = [
  {
    value: 'ContinentCode:AF',
    label: '非洲',
  },
  {
    value: 'ContinentCode:AN',
    label: '南极洲',
  },
  {
    value: 'ContinentCode:AS',
    label: '亚洲',
  },
  {
    value: 'ContinentCode:EU',
    label: '欧洲',
  },
  {
    value: 'ContinentCode:OC',
    label: '大洋洲',
  },
  {
    value: 'ContinentCode:NA',
    label: '北美洲',
  },
  {
    value: 'ContinentCode:SA',
    label: '南美洲',
  },
  {
    value: 'CountryCode:*',
    label: '全球',
  },
  {
    value: 'CountryCode:AF',
    label: '阿富汗',
  },
  {
    value: 'CountryCode:AL',
    label: '阿尔巴尼亚',
  },
  {
    value: 'CountryCode:DZ',
    label: '阿尔及利亚',
  },
  {
    value: 'CountryCode:AS',
    label: '美属萨摩亚',
  },
  {
    value: 'CountryCode:AD',
    label: '安道尔',
  },
  {
    value: 'CountryCode:AO',
    label: '安哥拉',
  },
  {
    value: 'CountryCode:AI',
    label: '安圭拉',
  },
  {
    value: 'CountryCode:AQ',
    label: '南极洲',
  },
  {
    value: 'CountryCode:AG',
    label: '安提瓜和巴布达',
  },
  {
    value: 'CountryCode:AR',
    label: '阿根廷',
  },
  {
    value: 'CountryCode:AM',
    label: '亚美尼亚',
  },
  {
    value: 'CountryCode:AW',
    label: '阿鲁巴',
  },
  {
    value: 'CountryCode:AU',
    label: '澳大利亚',
  },
  {
    value: 'CountryCode:AT',
    label: '奥地利',
  },
  {
    value: 'CountryCode:AZ',
    label: '阿塞拜疆',
  },
  {
    value: 'CountryCode:BS',
    label: '巴哈马',
  },
  {
    value: 'CountryCode:BH',
    label: '巴林',
  },
  {
    value: 'CountryCode:BD',
    label: '孟加拉国',
  },
  {
    value: 'CountryCode:BB',
    label: '巴巴多斯',
  },
  {
    value: 'CountryCode:BY',
    label: '白俄罗斯',
  },
  {
    value: 'CountryCode:BE',
    label: '比利时',
  },
  {
    value: 'CountryCode:BZ',
    label: '伯利兹',
  },
  {
    value: 'CountryCode:BJ',
    label: '贝宁',
  },
  {
    value: 'CountryCode:BM',
    label: '百慕大',
  },
  {
    value: 'CountryCode:BT',
    label: '不丹',
  },
  {
    value: 'CountryCode:BO',
    label: '玻利维亚',
  },
  {
    value: 'CountryCode:BA',
    label: '波黑',
  },
  {
    value: 'CountryCode:BW',
    label: '博茨瓦纳',
  },
  {
    value: 'CountryCode:BV',
    label: '布韦岛',
  },
  {
    value: 'CountryCode:BR',
    label: '巴西',
  },
  {
    value: 'CountryCode:IO',
    label: '英属印度洋领地',
  },
  {
    value: 'CountryCode:VG',
    label: '英属维尔京群岛',
  },
  {
    value: 'CountryCode:BN',
    label: '文莱',
  },
  {
    value: 'CountryCode:BG',
    label: '保加利亚',
  },
  {
    value: 'CountryCode:BF',
    label: '布基纳法索',
  },
  {
    value: 'CountryCode:MM',
    label: '缅甸',
  },
  {
    value: 'CountryCode:BI',
    label: '布隆迪',
  },
  {
    value: 'CountryCode:CV',
    label: '佛得角',
  },
  {
    value: 'CountryCode:KH',
    label: '柬埔寨',
  },
  {
    value: 'CountryCode:CM',
    label: '喀麦隆',
  },
  {
    value: 'CountryCode:CA',
    label: '加拿大',
  },
  {
    value: 'CountryCode:KY',
    label: '开曼群岛',
  },
  {
    value: 'CountryCode:CF',
    label: '中非',
  },
  {
    value: 'CountryCode:TD',
    label: '乍得',
  },
  {
    value: 'CountryCode:CL',
    label: '智利',
  },
  {
    value: 'CountryCode:CN',
    label: '中国',
  },
  {
    value: 'CountryCode:CX',
    label: '圣诞岛',
  },
  {
    value: 'CountryCode:CC',
    label: '科科斯（基林）群岛',
  },
  {
    value: 'CountryCode:CO',
    label: '哥伦比亚',
  },
  {
    value: 'CountryCode:KM',
    label: '科摩罗',
  },
  {
    value: 'CountryCode:CD',
    label: '刚果民主共和国',
  },
  {
    value: 'CountryCode:CG',
    label: '刚果共和国',
  },
  {
    value: 'CountryCode:CK',
    label: '库克群岛',
  },
  {
    value: 'CountryCode:CR',
    label: '哥斯达黎加',
  },
  {
    value: 'CountryCode:CI',
    label: '科特迪瓦',
  },
  {
    value: 'CountryCode:HR',
    label: '克罗地亚',
  },
  {
    value: 'CountryCode:CU',
    label: '古巴',
  },
  {
    value: 'CountryCode:CW',
    label: '库拉索',
  },
  {
    value: 'CountryCode:CY',
    label: '塞浦路斯',
  },
  {
    value: 'CountryCode:CZ',
    label: '捷克',
  },
  {
    value: 'CountryCode:DK',
    label: '丹麦',
  },
  {
    value: 'CountryCode:DJ',
    label: '吉布提',
  },
  {
    value: 'CountryCode:DM',
    label: '多米尼克',
  },
  {
    value: 'CountryCode:DO',
    label: '多米尼加',
  },
  {
    value: 'CountryCode:EC',
    label: '厄瓜多尔',
  },
  {
    value: 'CountryCode:EG',
    label: '埃及',
  },
  {
    value: 'CountryCode:SV',
    label: '萨尔瓦多',
  },
  {
    value: 'CountryCode:GQ',
    label: '赤道几内亚',
  },
  {
    value: 'CountryCode:ER',
    label: '厄立特里亚',
  },
  {
    value: 'CountryCode:EE',
    label: '爱沙尼亚',
  },
  {
    value: 'CountryCode:ET',
    label: '埃塞俄比亚',
  },
  {
    value: 'CountryCode:FK',
    label: '福克兰群岛',
  },
  {
    value: 'CountryCode:FO',
    label: '法罗群岛',
  },
  {
    value: 'CountryCode:FJ',
    label: '斐济',
  },
  {
    value: 'CountryCode:FI',
    label: '芬兰',
  },
  {
    value: 'CountryCode:FR',
    label: '法国',
  },
  {
    value: 'CountryCode:FX',
    label: '法国本土',
  },
  {
    value: 'CountryCode:GF',
    label: '法属圭亚那',
  },
  {
    value: 'CountryCode:PF',
    label: '法属波利尼西亚',
  },
  {
    value: 'CountryCode:TF',
    label: '法属南部和南极领地',
  },
  {
    value: 'CountryCode:GA',
    label: '加蓬',
  },
  {
    value: 'CountryCode:GM',
    label: '冈比亚',
  },
  {
    value: 'CountryCode:PS',
    label: '巴勒斯坦',
  },
  {
    value: 'CountryCode:GE',
    label: '格鲁吉亚',
  },
  {
    value: 'CountryCode:DE',
    label: '德国',
  },
  {
    value: 'CountryCode:GH',
    label: '加纳',
  },
  {
    value: 'CountryCode:GI',
    label: '直布罗陀',
  },
  {
    value: 'CountryCode:GR',
    label: '希腊',
  },
  {
    value: 'CountryCode:GL',
    label: '格陵兰',
  },
  {
    value: 'CountryCode:GD',
    label: '格林纳达',
  },
  {
    value: 'CountryCode:GP',
    label: '瓜德罗普',
  },
  {
    value: 'CountryCode:GU',
    label: '关岛',
  },
  {
    value: 'CountryCode:GT',
    label: '危地马拉',
  },
  {
    value: 'CountryCode:GG',
    label: '根西',
  },
  {
    value: 'CountryCode:GN',
    label: '几内亚',
  },
  {
    value: 'CountryCode:GW',
    label: '几内亚比绍',
  },
  {
    value: 'CountryCode:GY',
    label: '圭亚那',
  },
  {
    value: 'CountryCode:HT',
    label: '海地',
  },
  {
    value: 'CountryCode:HM',
    label: '赫德岛和麦克唐纳群岛',
  },
  {
    value: 'CountryCode:VA',
    label: '梵蒂冈',
  },
  {
    value: 'CountryCode:HN',
    label: '洪都拉斯',
  },
  {
    value: 'CountryCode:HK',
    label: '中国香港',
  },
  {
    value: 'CountryCode:HU',
    label: '匈牙利',
  },
  {
    value: 'CountryCode:IS',
    label: '冰岛',
  },
  {
    value: 'CountryCode:IN',
    label: '印度',
  },
  {
    value: 'CountryCode:ID',
    label: '印尼',
  },
  {
    value: 'CountryCode:IR',
    label: '伊朗',
  },
  {
    value: 'CountryCode:IQ',
    label: '伊拉克',
  },
  {
    value: 'CountryCode:IE',
    label: '爱尔兰',
  },
  {
    value: 'CountryCode:IM',
    label: '马恩岛',
  },
  {
    value: 'CountryCode:IL',
    label: '以色列',
  },
  {
    value: 'CountryCode:IT',
    label: '意大利',
  },
  {
    value: 'CountryCode:JM',
    label: '牙买加',
  },
  {
    value: 'CountryCode:JP',
    label: '日本',
  },
  {
    value: 'CountryCode:JE',
    label: '泽西',
  },
  {
    value: 'CountryCode:JO',
    label: '约旦',
  },
  {
    value: 'CountryCode:KZ',
    label: '哈萨克斯坦',
  },
  {
    value: 'CountryCode:KE',
    label: '肯尼亚',
  },
  {
    value: 'CountryCode:KI',
    label: '基里巴斯',
  },
  {
    value: 'CountryCode:KP',
    label: '朝鲜',
  },
  {
    value: 'CountryCode:KR',
    label: '韩国',
  },
  {
    value: 'CountryCode:XK',
    label: '科索沃',
  },
  {
    value: 'CountryCode:KW',
    label: '科威特',
  },
  {
    value: 'CountryCode:KG',
    label: '吉尔吉斯斯坦',
  },
  {
    value: 'CountryCode:LA',
    label: '老挝',
  },
  {
    value: 'CountryCode:LV',
    label: '拉脱维亚',
  },
  {
    value: 'CountryCode:LB',
    label: '黎巴嫩',
  },
  {
    value: 'CountryCode:LS',
    label: '莱索托',
  },
  {
    value: 'CountryCode:LR',
    label: '利比里亚',
  },
  {
    value: 'CountryCode:LY',
    label: '利比亚',
  },
  {
    value: 'CountryCode:LI',
    label: '列支敦士登',
  },
  {
    value: 'CountryCode:LT',
    label: '立陶宛',
  },
  {
    value: 'CountryCode:LU',
    label: '卢森堡',
  },
  {
    value: 'CountryCode:MO',
    label: '中国澳门',
  },
  {
    value: 'CountryCode:MK',
    label: '北马其顿',
  },
  {
    value: 'CountryCode:MG',
    label: '马达加斯加',
  },
  {
    value: 'CountryCode:MW',
    label: '马拉维',
  },
  {
    value: 'CountryCode:MY',
    label: '马来西亚',
  },
  {
    value: 'CountryCode:MV',
    label: '马尔代夫',
  },
  {
    value: 'CountryCode:ML',
    label: '马里',
  },
  {
    value: 'CountryCode:MT',
    label: '马耳他',
  },
  {
    value: 'CountryCode:MH',
    label: '马绍尔群岛',
  },
  {
    value: 'CountryCode:MQ',
    label: '马提尼克',
  },
  {
    value: 'CountryCode:MR',
    label: '毛里塔尼亚',
  },
  {
    value: 'CountryCode:MU',
    label: '毛里求斯',
  },
  {
    value: 'CountryCode:YT',
    label: '马约特',
  },
  {
    value: 'CountryCode:MX',
    label: '墨西哥',
  },
  {
    value: 'CountryCode:FM',
    label: '密克罗尼西亚联邦',
  },
  {
    value: 'CountryCode:MD',
    label: '摩尔多瓦',
  },
  {
    value: 'CountryCode:MC',
    label: '摩纳哥',
  },
  {
    value: 'CountryCode:MN',
    label: '蒙古',
  },
  {
    value: 'CountryCode:ME',
    label: '黑山',
  },
  {
    value: 'CountryCode:MS',
    label: '蒙特塞拉特',
  },
  {
    value: 'CountryCode:MA',
    label: '摩洛哥',
  },
  {
    value: 'CountryCode:MZ',
    label: '莫桑比克',
  },
  {
    value: 'CountryCode:NA',
    label: '纳米比亚',
  },
  {
    value: 'CountryCode:NR',
    label: '瑙鲁',
  },
  {
    value: 'CountryCode:NP',
    label: '尼泊尔',
  },
  {
    value: 'CountryCode:NL',
    label: '荷兰',
  },
  {
    value: 'CountryCode:AN',
    label: '荷属安的列斯',
  },
  {
    value: 'CountryCode:NC',
    label: '新喀里多尼亚',
  },
  {
    value: 'CountryCode:NZ',
    label: '新西兰',
  },
  {
    value: 'CountryCode:NI',
    label: '尼加拉瓜',
  },
  {
    value: 'CountryCode:NE',
    label: '尼日尔',
  },
  {
    value: 'CountryCode:NG',
    label: '尼日利亚',
  },
  {
    value: 'CountryCode:NU',
    label: '纽埃',
  },
  {
    value: 'CountryCode:NF',
    label: '诺福克岛',
  },
  {
    value: 'CountryCode:MP',
    label: '北马里亚纳群岛',
  },
  {
    value: 'CountryCode:NO',
    label: '挪威',
  },
  {
    value: 'CountryCode:OM',
    label: '阿曼',
  },
  {
    value: 'CountryCode:PK',
    label: '巴基斯坦',
  },
  {
    value: 'CountryCode:PW',
    label: '帕劳',
  },
  {
    value: 'CountryCode:PA',
    label: '巴拿马',
  },
  {
    value: 'CountryCode:PG',
    label: '巴布亚新几内亚',
  },
  {
    value: 'CountryCode:PY',
    label: '巴拉圭',
  },
  {
    value: 'CountryCode:PE',
    label: '秘鲁',
  },
  {
    value: 'CountryCode:PH',
    label: '菲律宾',
  },
  {
    value: 'CountryCode:PN',
    label: '皮特凯恩群岛',
  },
  {
    value: 'CountryCode:PL',
    label: '波兰',
  },
  {
    value: 'CountryCode:PT',
    label: '葡萄牙',
  },
  {
    value: 'CountryCode:PR',
    label: '波多黎各',
  },
  {
    value: 'CountryCode:QA',
    label: '卡塔尔',
  },
  {
    value: 'CountryCode:RE',
    label: '留尼汪',
  },
  {
    value: 'CountryCode:RO',
    label: '罗马尼亚',
  },
  {
    value: 'CountryCode:TW',
    label: '台湾',
  },
  {
    value: 'CountryCode:RU',
    label: '俄罗斯',
  },
  {
    value: 'CountryCode:RW',
    label: '卢旺达',
  },
  {
    value: 'CountryCode:BL',
    label: '圣巴泰勒米',
  },
  {
    value: 'CountryCode:SH',
    label: '圣赫勒拿、阿森松和特里斯坦-达库尼亚',
  },
  {
    value: 'CountryCode:KN',
    label: '圣基茨和尼维斯',
  },
  {
    value: 'CountryCode:LC',
    label: '圣卢西亚',
  },
  {
    value: 'CountryCode:MF',
    label: '法属圣马丁',
  },
  {
    value: 'CountryCode:VC',
    label: '圣文森特和格林纳丁斯',
  },
  {
    value: 'CountryCode:WS',
    label: '萨摩亚',
  },
  {
    value: 'CountryCode:SM',
    label: '圣马力诺',
  },
  {
    value: 'CountryCode:ST',
    label: '圣多美和普林西比',
  },
  {
    value: 'CountryCode:SA',
    label: '沙特阿拉伯',
  },
  {
    value: 'CountryCode:SN',
    label: '塞内加尔',
  },
  {
    value: 'CountryCode:RS',
    label: '塞尔维亚',
  },
  {
    value: 'CountryCode:SC',
    label: '塞舌尔',
  },
  {
    value: 'CountryCode:SL',
    label: '塞拉利昂',
  },
  {
    value: 'CountryCode:SG',
    label: '新加坡',
  },
  {
    value: 'CountryCode:SX',
    label: '荷属圣马丁',
  },
  {
    value: 'CountryCode:SK',
    label: '斯洛伐克',
  },
  {
    value: 'CountryCode:SI',
    label: '斯洛文尼亚',
  },
  {
    value: 'CountryCode:SB',
    label: '所罗门群岛',
  },
  {
    value: 'CountryCode:SO',
    label: '索马里',
  },
  {
    value: 'CountryCode:ZA',
    label: '南非',
  },
  {
    value: 'CountryCode:GS',
    label: '南乔治亚和南桑威奇群岛',
  },
  {
    value: 'CountryCode:SS',
    label: '南苏丹',
  },
  {
    value: 'CountryCode:ES',
    label: '西班牙',
  },
  {
    value: 'CountryCode:LK',
    label: '斯里兰卡',
  },
  {
    value: 'CountryCode:SD',
    label: '苏丹',
  },
  {
    value: 'CountryCode:SR',
    label: '苏里南',
  },
  {
    value: 'CountryCode:PM',
    label: '圣皮埃尔和密克隆',
  },
  {
    value: 'CountryCode:SZ',
    label: '斯威士兰',
  },
  {
    value: 'CountryCode:SE',
    label: '瑞典',
  },
  {
    value: 'CountryCode:CH',
    label: '瑞士',
  },
  {
    value: 'CountryCode:SY',
    label: '叙利亚',
  },
  {
    value: 'CountryCode:TJ',
    label: '塔吉克斯坦',
  },
  {
    value: 'CountryCode:TZ',
    label: '坦桑尼亚',
  },
  {
    value: 'CountryCode:TH',
    label: '泰国',
  },
  {
    value: 'CountryCode:TL',
    label: '东帝汶',
  },
  {
    value: 'CountryCode:TG',
    label: '多哥',
  },
  {
    value: 'CountryCode:TK',
    label: '托克劳',
  },
  {
    value: 'CountryCode:TO',
    label: '汤加',
  },
  {
    value: 'CountryCode:TT',
    label: '特立尼达和多巴哥',
  },
  {
    value: 'CountryCode:TN',
    label: '突尼斯',
  },
  {
    value: 'CountryCode:TR',
    label: '土耳其',
  },
  {
    value: 'CountryCode:TM',
    label: '土库曼斯坦',
  },
  {
    value: 'CountryCode:TC',
    label: '特克斯和凯科斯群岛',
  },
  {
    value: 'CountryCode:TV',
    label: '图瓦卢',
  },
  {
    value: 'CountryCode:UG',
    label: '乌干达',
  },
  {
    value: 'CountryCode:UA',
    label: '乌克兰',
  },
  {
    value: 'CountryCode:AE',
    label: '阿联酋',
  },
  {
    value: 'CountryCode:GB',
    label: '英国',
  },
  {
    value: 'CountryCode:US',
    label: '美国',
  },
  {
    value: 'CountryCode:UM',
    label: '美国本土外小岛屿',
  },
  {
    value: 'CountryCode:UY',
    label: '乌拉圭',
  },
  {
    value: 'CountryCode:UZ',
    label: '乌兹别克斯坦',
  },
  {
    value: 'CountryCode:VU',
    label: '瓦努阿图',
  },
  {
    value: 'CountryCode:VE',
    label: '委内瑞拉',
  },
  {
    value: 'CountryCode:VN',
    label: '越南',
  },
  {
    value: 'CountryCode:VI',
    label: '美属维尔京群岛',
  },
  {
    value: 'CountryCode:WF',
    label: '瓦利斯和富图纳',
  },
  {
    value: 'CountryCode:EH',
    label: '西撒哈拉',
  },
  {
    value: 'CountryCode:UN',
    label: '联合国',
  },
  {
    value: 'CountryCode:EU',
    label: '欧盟',
  },
  {
    value: 'CountryCode:YE',
    label: '也门',
  },
  {
    value: 'CountryCode:ZA',
    label: '扎伊尔',
  },
  {
    value: 'CountryCode:ZM',
    label: '赞比亚',
  },
  {
    value: 'CountryCode:ZW',
    label: '津巴布韦',
  },
  {
    value: 'CountryCode:RO',
    label: '罗得西亚',
  },
  {
    value: 'CountryCode:WI',
    label: '西印度群岛联邦',
  },
  {
    value: 'CountryCode:EN',
    label: '独联体',
  },
  {
    value: 'CountryCode:AZ',
    label: '澳大拉西亚',
  },
  {
    value: 'CountryCode:YG',
    label: '南斯拉夫',
  },
  {
    value: 'CountryCode:UR',
    label: '苏联',
  },
  {
    value: 'CountryCode:DH',
    label: '达荷美',
  },
  {
    value: 'CountryCode:VL',
    label: '上沃尔特',
  },
  {
    value: 'CountryCode:BO',
    label: '波希米亚',
  },
  {
    value: 'CountryCode:TC',
    label: '捷克斯洛伐克',
  },
  {
    value: 'CountryCode:UA',
    label: '阿拉伯联合共和国',
  },
]

export function geolocationOptionsApi() {
  return geolocationOptions
}
