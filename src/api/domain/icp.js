import request from '@/utils/request'

const icpApi = {
  List: '/bff/domain/icp',
  Source: '/bff/domain/icp/source',
  Create: '/bff/domain/icp/create',
  Delete: '/bff/domain/icp/delete/',
  Update: '/bff/domain/icp/update'
}

export function getIcpList (parameter) {
  return request({
    url: icpApi.List,
    method: 'get',
    params: parameter
  })
}
export function getIcpSourceList () {
  return request({
    url: `${icpApi.Source}`,
    method: 'get'
  })
}
export function creatIcp (data) {
  return request({
    url: `${icpApi.Create}`,
    method: 'post',
    data: data
  })
}
export function deleteIcp (id) {
  return request({
    url: `${icpApi.Delete}${id}`,
    method: 'delete'
  })
}
export function updateIcp (data) {
  return request({
    url: `${icpApi.Update}`,
    method: 'put',
    data: data
  })
}
