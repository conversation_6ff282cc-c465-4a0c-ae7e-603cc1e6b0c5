import request from '@/utils/request'

const domainApi = {
  List: '/bff/domain/domain_list',
  Info: '/bff/domain/info/',
  Source: '/bff/domain/source_list',
  SendCode: '/bff/domain/download/send',
  CheckCode: '/bff/domain/download/check',
  Download: '/bff/domain/ssl/info',
  Create: '/bff/domain/create',
  Update: '/bff/domain/dns_source',
  upstreamId: '/bff/gateway/upstream_id',
  clusterEnv: '/bff/gateway/cluster',
  ipList: '/bff/domain/dns/ips',
  domainCheck: '/bff/domain/dns/check',
  domainWhois: '/bff/domain/whois',
  sendWxCode: '/bff/auth/codenumber/desktop/send ',
  checkWxCode: '/bff/auth/codenumber/desktop/check',
  notifyOff:'/bff/domain/notifyoff'
}
export function sendWxCode(parameter) {
  return request({
    url: domainApi.sendWxCode,
    method: 'put',
    data: parameter,
  })
}
export function checkWxCode(parameter) {
  return request({
    url: domainApi.checkWxCode,
    method: 'get',
    params: parameter,
  })
}
export function getDomainList(parameter) {
  return request({
    url: domainApi.List,
    method: 'get',
    params: parameter,
  })
}
export function creatDomain(data) {
  return request({
    url: `${domainApi.Create}`,
    method: 'post',
    data: data,
  }).catch(function (error) {
    console.log('creatDomain Error: ' + error)
  })
}
export function UpdateDomain(data) {
  return request({
    url: `${domainApi.Update}`,
    method: 'put',
    data: data,
  }).catch(function (error) {
    console.log('UpdateDomain Error: ' + error)
  })
}
export function getDomainInfo(id) {
  return request({
    url: `${domainApi.Info}${id}`,
    method: 'get',
  })
}
export function getDomainListSource() {
  return request({
    url: `${domainApi.Source}`,
    method: 'get',
  })
}

export function notifyOff(data) {
  return request({
    url: `${domainApi.notifyOff}`,
    method: 'post',
    data: data,
  })
}

export function sslDownloadSend(data) {
  console.log(data)
  return request({
    url: domainApi.SendCode,
    method: 'put',
    data: data,
  })
}
export function sslDownloadCheck(parameter) {
  return request({
    url: domainApi.CheckCode,
    method: 'get',
    params: parameter,
  })
}
export function sslDownload(parameter) {
  return request({
    url: domainApi.Download,
    method: 'get',
    params: parameter,
  })
}

export function upstreamIdList() {
  return request({
    url: `${domainApi.upstreamId}`,
    method: 'get',
  }).catch(function (error) {
    console.log('upstreamIdList Error: ' + error)
  })
}
export function getIpList(parameter) {
  return request({
    url: `${domainApi.ipList}`,
    method: 'get',
    params: parameter,
  }).catch(function (error) {
    console.log('getIpList Error: ' + error)
  })
}
export function envDomainList() {
  return request({
    url: `${domainApi.clusterEnv}`,
    method: 'get',
  }).catch(function (error) {
    console.log('envDomainList Error: ' + error)
  })
}

export function DomainCheck(data) {
  return request({
    url: `${domainApi.domainCheck}`,
    method: 'get',
    params: data,
  })
}

export function domainPurchaseCheck(data) {
  return request({
    url: `${domainApi.domainWhois}`,
    method: 'get',
    params: data,
  })
}
