import axios from 'axios'

const request = axios.create({
  baseURL: '/cmdb-api',
  timeout: 5000
})

// const request = axios.create({
//   baseURL: 'https://matrix-api-test.intsig.net/api/v1',
//   timeout: 50000
// })

export function downloadReportFile () {
  return request({
    url: '/cmdb/account_detailed/download/cost_report/',
    method: 'post',
    responseType: 'blob'
  }).catch(function (error) {
    console.log('downloadReportFile Error: ' + error)
  })
}

export function downloadItCostReportFile () {
  return request({
    url: '/cmdb/account_detailed/download/it_cost_report/',
    method: 'post',
    responseType: 'blob'
  }).catch(function (error) {
    console.log('downloadItCostReportFile Error: ' + error)
  })
}

export function downloadCreateInvoicesReportFile () {
  return request({
    url: '/cmdb/account_detailed/download/create_invoices_report/',
    method: 'post',
    responseType: 'blob'
  }).catch(function (error) {
    console.log('downloadCreateInvoicesReportFile Error: ' + error)
  })
}
