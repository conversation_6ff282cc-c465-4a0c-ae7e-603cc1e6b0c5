import axios from 'axios'

const request = axios.create({
  baseURL: '/cmdb-api',
  timeout: 5000
})

export function workflowOrderCreate (data) {
  return request({
    url: '/cmdb/workflow/order/create/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('workflowOrderCreate Error: ' + error)
  })
}

export function workflowOrderList (data) {
  return request({
    url: '/cmdb/workflow/order/',
    method: 'post',
    params: data
  }).catch(function (error) {
    console.log('workflowOrderList Error: ' + error)
  })
}

export function workflowOrderId (id) {
  return request({
    url: '/cmdb/workflow/order/' + id + '/',
    method: 'post',
    data: id
  }).catch(function (error) {
    console.log('workflowOrderId Error: ' + error)
  })
}

export function workflowOrderApprove (data) {
  return request({
    url: '/cmdb/workflow/order/approve/' + data.id + '/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('workflowOrderApprove Error: ' + error)
  })
}

export function workflowOrderTypeList () {
  return request({
    url: '/cmdb/workflow/order/order_type/',
    method: 'post'
  }).catch(function (error) {
    console.log('workflowOrderTypeList Error: ' + error)
  })
}

export function workflowFounderList () {
  return request({
    url: '/cmdb/workflow/order/founder_list/',
    method: 'post'
  }).catch(function (error) {
    console.log('workflowFounderList Error: ' + error)
  })
}
export function preServerModifyInfo (data) {
  return request({
    url: '/cmdb/asset_automation/auto_update_vm_info/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('preServerModifyInfo Error: ' + error)
  })
}
