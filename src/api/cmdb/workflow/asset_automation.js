import axios from 'axios'

const request = axios.create({
  baseURL: '/cmdb-api',
  timeout: 5000
})
//
// const request = axios.create({
//   baseURL: 'https://matrix-api-test.intsig.net/api/v1',
//   timeout: 5000
// })

export function vmBaseInfo (data) {
  return request({
    url: '/cmdb/asset/auto_kvm_info/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('vmBaseInfo Error: ' + error)
  })
}

export function awsBaseInfo (data) {
  return request({
    url: '/cmdb/aws_asset/aws_base_info/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('awsBaseInfo Error: ' + error)
  })
}

export function aliyunBaseInfo (data) {
  return request({
    url: '/cmdb/aliyun_asset/aliyun_base_info/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('aliyunBaseInfo Error: ' + error)
  })
}

export function securityList (data) {
  return request({
    url: '/cmdb/aws_asset/aws_base_info_security/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('securityList Error: ' + error)
  })
}

// 服务器注销检查
export function getCheckIpInfo (data) {
  return request({
    url: '/cmdb/asset_automation/auto_check_asset_status/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('getCheckIpInfo Error: ' + error)
  })
}
// 选择镜像时获取信息
export function assetList (data) {
  return request({
    url: '/cmdb/asset/asset_list/',
    method: 'post',
    data: data
  }).catch(function (error) {
    console.log('assetList Error: ' + error)
  })
}
