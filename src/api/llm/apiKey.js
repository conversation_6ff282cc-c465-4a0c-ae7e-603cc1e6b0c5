import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'
import axios from 'axios'
import { notification } from 'ant-design-vue'
import { getAccessToken, removeToken } from '@/utils/auth'
import { getApiCloudBase } from '@/config'
// 创建 axios 实例
const downloadRequest = axios.create({
  // API 请求的默认前缀
  baseURL: getApiCloudBase(),
  timeout: 60000000, // 请求超时时间
})

// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data
    const token = getAccessToken()
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message,
      })
    }
    if (error.response.status === 500 || error.response.status === 400 || data.code === 400) {
      notification.error({
        message: 'error',
        description: data.message,
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      })
      if (token) {
        store.state.token = ''
        store.state.roles = []
        removeToken(true)
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
downloadRequest.interceptors.request.use(config => {
  const token = getAccessToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
}, errorHandler)

// response interceptor
downloadRequest.interceptors.response.use(response => {
  return response.data
}, errorHandler)
// API Key 使用场景
export function getApiKeyScopes(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/scopes',
    method: 'get',
    params: parameter,
  })
}
// 应用名称查重接口
export function checkAppName(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/check-app-name',
    method: 'get',
    params: parameter,
  })
}

// API Key 创建
export function createApiKey(data) {
  return request({
    url: '/bff/apikey/llm/api-key/create',
    method: 'post',
    data: data,
  })
}

// API Key 列表查询接口
export function getApiKeyList(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/list',
    method: 'get',
    params: parameter,
  })
}

// API Key 密钥获取
export function getApiKeySecret(parameter) {
  return globalTokenRequest({
    url: '/bff/apikey/llm/api-key/get-secret',
    method: 'get',
    params: parameter,
  })
}

// 禁用 API Key
export function disableApiKey(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/disable',
    method: 'get',
    params: parameter,
  })
}

// 启用 API Key
export function enableApiKey(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/enable',
    method: 'get',
    params: parameter,
  })
}

// 删除 API Key
export function deleteApiKey(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/delete',
    method: 'get',
    params: parameter,
  })
}

// API Key 使用统计接口
export function getApiKeyStats(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/stats',
    method: 'post',
    data: parameter,
  })
}

// 模型列表下发接口
export function getApiKeyModels(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/models',
    method: 'get',
    params: parameter,
  })
}

// 模型列表下发接口
export function getApiKeyApplications(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/applications',
    method: 'get',
    params: parameter,
  })
}

// 用量监控
export function getApiKeyCharts(parameter) {
  return request({
    url: '/bff/apikey/llm/api-key/charts',
    method: 'post',
    data: parameter,
  })
}

// 下载
export function exportApiKeyExcel(parameter) {
  return downloadRequest({
    url: '/bff/apikey/llm/api-key/excel',
    method: 'post',
    data: parameter,
    responseType: 'blob',
  })
}
