import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const consulConsoleApi = {
  kvTreeList: '/bff/consul/kv/treelist',
  kvInfo: '/bff/consul/kv',
  clusters: '/bff/consul/clusters',
  tokenList: '/bff/consul/token/list',
  tokenDetail: '/bff/consul/token/detail',
  updateToken: '/bff/consul/token/update',
  deleteToken: '/bff/consul/token/delete',
  cloneToken: '/bff/consul/token/clone',
  createToken: '/bff/consul/token/create',
  createRole: '/bff/consul/role/create',
  roleDetail: '/bff/consul/role/detail',
  updateRole: '/bff/consul/role/update',
  roleList: '/bff/consul/role/list',
  delteRole: '/bff/consul/role/delete',
  createPolicy: '/bff/consul/policy/create',
  policyDetail: '/bff/consul/policy/detail',
  updatePolicy: '/bff/consul/policy/update',
  policyList: '/bff/consul/policy/list',
  deltePolicy: '/bff/consul/policy/delete',
  consulKey: '/bff/consul/key',
}
// 机房列表
export function getClustersList(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.clusters,
    method: 'get',
    params: parameter,
  })
}
// 获取Key树形结构
export function getKvTreeList(parameter) {
  return globalTokenRequest({
    url: `${consulConsoleApi.kvTreeList}`,
    method: 'get',
    params: parameter,
  })
}
// 创建KV
export function createKvInfo(parameter) {
  return globalTokenRequest({
    url: `${consulConsoleApi.kvInfo}`,
    method: 'post',
    data: parameter,
  })
}
// 获取KV详情
export function getKvInfoDetail(parameter) {
  return globalTokenRequest({
    url: `${consulConsoleApi.kvInfo}`,
    method: 'get',
    params: parameter,
  })
}

// 更新KV
export function updateKvInfo(parameter, id) {
  return globalTokenRequest({
    url: `${consulConsoleApi.kvInfo}/${id}`,
    method: 'put',
    data: parameter,
  })
}

// 删除KV
export function deleteKvInfo(parameter) {
  return globalTokenRequest({
    url: `${consulConsoleApi.kvInfo}`,
    method: 'delete',
    data: parameter,
  })
}

// Token 列表
export function getTokenList(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.tokenList,
    method: 'get',
    params: parameter,
  })
}

// Token 详情
export function getTokenDetail(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.tokenDetail,
    method: 'get',
    params: parameter,
  })
}
// Token 创建
export function createToken(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.createToken,
    method: 'post',
    data: parameter,
  })
}

// Token 更新
export function updateToken(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.updateToken,
    method: 'post',
    data: parameter,
  })
}
// Token 复制
export function cloneToken(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.cloneToken,
    method: 'get',
    params: parameter,
  })
}
// Token 删除
export function deleteToken(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.deleteToken,
    method: 'get',
    params: parameter,
  })
}
// 创建Consul role
export function createRole(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.createRole,
    method: 'post',
    data: parameter,
  })
}
// 获取角色详细信息 role
export function getRoleDetail(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.roleDetail,
    method: 'get',
    params: parameter,
  })
}
// 更新 consul role信息
export function updateRole(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.updateRole,
    method: 'post',
    data: parameter,
  })
}
// Consul role列表
export function getRoleList(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.roleList,
    method: 'get',
    params: parameter,
  })
}
// 删除 consul role
export function delteRole(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.delteRole,
    method: 'get',
    params: parameter,
  })
}

// 创建policy
export function createPolicy(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.createPolicy,
    method: 'post',
    data: parameter,
  })
}

// 更新policy
export function updatePolicy(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.updatePolicy,
    method: 'post',
    data: parameter,
  })
}

// Policy 列表
export function getPolicyList(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.policyList,
    method: 'get',
    params: parameter,
  })
}

// policy详情
export function getPolicyDetail(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.policyDetail,
    method: 'get',
    params: parameter,
  })
}
// policy详情
export function deltePolicy(parameter) {
  return globalTokenRequest({
    url: consulConsoleApi.deltePolicy,
    method: 'get',
    params: parameter,
  })
}

// 工作流程的变更
export function getConsulKey(parameter) {
  return request({
    url: consulConsoleApi.consulKey,
    method: 'get',
    params: parameter,
  })
}
