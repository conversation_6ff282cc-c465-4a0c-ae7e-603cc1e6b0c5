import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const discoveryApi = {
  CreateService: '/bff/consul/discovery/resign',
  ServiceResign: '/bff/consul/discovery/resign-online',
  ServiceDeResign: '/bff/consul/discovery/resign-offline',
  DeleteServiceResign: '/bff/consul/discovery/resign',
  ListService: '/bff/consul/discovery/services',
  Bussiness: '/bff/consul/discovery/bussiness',
  AuthDiscovery: '/bff/consul/discovery/auth',
  AssetList: '/bff/asset/ips/list',
}

export function getAssetList(parameter) {
  return globalTokenRequest({
    url: discoveryApi.AssetList,
    method: 'get',
    params: parameter,
  })
}

export function getConsulSeList(parameter) {
  return request({
    url: discoveryApi.List,
    method: 'get',
    params: parameter,
  })
}

export function createService(data) {
  return globalTokenRequest({
    url: discoveryApi.CreateService,
    method: 'post',
    data: data,
  })
}

export function updateService(data) {
  return globalTokenRequest({
    url: discoveryApi.CreateService,
    method: 'put',
    data: data,
  })
}

export function serviceResign(parameter) {
  return globalTokenRequest({
    url: discoveryApi.ServiceResign,
    method: 'get',
    params: parameter,
  })
}

export function serviceDeResign(parameter) {
  return globalTokenRequest({
    url: discoveryApi.ServiceDeResign,
    method: 'get',
    params: parameter,
  })
}

export function deleteServiceResign(parameter) {
  return globalTokenRequest({
    url: discoveryApi.DeleteServiceResign,
    method: 'delete',
    params: parameter,
  })
}

export function listService(parameter) {
  return globalTokenRequest({
    url: discoveryApi.ListService,
    method: 'get',
    params: parameter,
  })
}

export function serviceBussiness(parameter) {
  return globalTokenRequest({
    url: discoveryApi.Bussiness,
    method: 'get',
    params: parameter,
  })
}

export function authDiscovery(data) {
  return globalTokenRequest({
    url: `${discoveryApi.AuthDiscovery}`,
    method: 'post',
    data: data,
  })
}
