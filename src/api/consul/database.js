import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const consulDb<PERSON><PERSON> = {
  List: '/bff/consul/db_manage',
  Search: '/bff/consul/search_contidion',
  RecordList: '/bff/consul/records',
  RecordSearch: '/bff/consul/record/search_contidion',
  CreatConsulDb: '/bff/consul/db_manage/create',
  ModifyConsulDb: '/bff/consul/db_manage',
  BatchModifyHostPort: '/bff/consul/batch/db_manage',
  DeleteConsulDb: '/bff/consul/db_manage/delete',
  BatchDelete: '/bff/consul/db_manage/deletes',
  ConsulDbCheck: '/bff/consul/db_manage/check_exist',
  BatchPwUpdate: '/bff/consul/batch/pw',
}

export function getConsulRecordSearch(parameter) {
  return globalTokenRequest({
    url: `${consulDbApi.RecordSearch}`,
    method: 'get',
    params: parameter,
  })
}
export function getConsulDbList(parameter) {
  return globalTokenRequest({
    url: consulDbApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getConsulDbSearch(parameter) {
  return globalTokenRequest({
    url: `${consulDbApi.Search}`,
    method: 'get',
    params: parameter,
  })
}

export function getConsulRecordList(parameter) {
  return globalTokenRequest({
    url: consulDbApi.RecordList,
    method: 'get',
    params: parameter,
  })
}

export function creatConsulDb(data) {
  return globalTokenRequest({
    url: `${consulDbApi.CreatConsulDb}`,
    method: 'post',
    data: data,
  })
}

export function modifyConsulDb(data) {
  return globalTokenRequest({
    url: `${consulDbApi.ModifyConsulDb}`,
    method: 'put',
    data: data,
  })
}
export function batchModifyHostPort(data) {
  return globalTokenRequest({
    url: `${consulDbApi.BatchModifyHostPort}`,
    method: 'put',
    data: data,
  })
}

export function batchDelete(data) {
  return globalTokenRequest({
    url: `${consulDbApi.BatchDelete}`,
    method: 'post',
    data: data,
  })
}

export function deleteConsulDb(data) {
  return globalTokenRequest({
    url: `${consulDbApi.DeleteConsulDb}`,
    method: 'post',
    data: data,
  })
}

export function getConsulDbCheck(data) {
  return request({
    url: consulDbApi.ConsulDbCheck,
    method: 'post',
    data: data,
  })
}

export function BatchPwUpdate(data) {
  return globalTokenRequest({
    url: consulDbApi.BatchPwUpdate,
    method: 'put',
    data: data,
  })
}
