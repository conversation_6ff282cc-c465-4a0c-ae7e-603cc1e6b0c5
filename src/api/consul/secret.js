/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-16 11:25:53
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-17 15:53:40
 * @FilePath: \cloud_web\src\api\consul\secret.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'
import { globalTokenRequest } from '@/utils/globalTokenRequest'

const consulSe<PERSON><PERSON> = {
  List: '/bff/consul/secrets',
  Info: '/bff/consul/secret/value',
  Delete: '/bff/consul/secret',
  Modify: '/bff/consul/secret',
  Creat: '/bff/consul/secret',
  RecordList: '/bff/consul/secret/records',
  UserSeList: '/bff/consul/user/secret',
  CheckKey: '/bff/consul/key/check'
}

export function getConsulSeList(parameter) {
  return globalTokenRequest({
    url: consulSeApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getConsulSeInfo(parameter) {
  return globalTokenRequest({
    url: `${consulSeApi.Info}`,
    method: 'get',
    params: parameter,
  })
}
export function modifyConsulSe(data) {
  return globalTokenRequest({
    url: `${consulSeApi.Modify}`,
    method: 'put',
    data: data,
  })
}
export function deleteConsulSe(params) {
  return globalTokenRequest({
    url: `${consulSeApi.Delete}`,
    method: 'delete',
    params: params,
  })
}

export function creatConsulSe(data) {
  return globalTokenRequest({
    url: `${consulSeApi.Creat}`,
    method: 'post',
    data: data,
  })
}

export function getConsulSeRecordList(parameter) {
  return globalTokenRequest({
    url: consulSeApi.RecordList,
    method: 'get',
    params: parameter,
  })
}
export function getUserSeList(parameter) {
  return globalTokenRequest({
    url: consulSeApi.UserSeList,
    method: 'get',
    params: parameter,
  })
}
export function getCheckKey(parameter) {
  return globalTokenRequest({
    url: consulSeApi.CheckKey,
    method: 'get',
    params: parameter
  })
}
