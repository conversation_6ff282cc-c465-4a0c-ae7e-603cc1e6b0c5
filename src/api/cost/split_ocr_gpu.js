import request from '@/utils/request'

const costApi = {
  ListOverview: '/bff/cost/split_ocr_overview',
  ListCall: '/bff/cost/split_ocr_call',
  ListCopy: '/bff/cost/split_ocr_copy',
  Create: '/bff/cost/split_ocr',
  Export: '/bff/cost/split_ocr/export',
}

export function getCostSplitOcrOverviewList(parameter) {
  return request({
    url: costApi.ListOverview,
    method: 'get',
    params: parameter,
  })
}

export function getCostSplitOcrCallList(parameter) {
  return request({
    url: costApi.ListCall,
    method: 'get',
    params: parameter,
  })
}

export function getCostSplitOcrCopyList(parameter) {
  return request({
    url: costApi.ListCopy,
    method: 'get',
    params: parameter,
  })
}

export function createCostSplitOcr(data) {
  return request({
    url: costApi.Create,
    method: 'post',
    data: data,
  })
}

export function exportCostSplitOcr(parameter) {
  return request({
    url: costApi.Export,
    method: 'get',
    params: parameter,
  })
}
