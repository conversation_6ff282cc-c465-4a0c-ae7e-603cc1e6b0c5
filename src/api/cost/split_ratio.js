import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/split_ratio',
  Export: '/bff/cost/split_ratio/export',
  Get: '/bff/cost/split_ratio/',
  Update: '/bff/cost/split_ratio',
  ListCostSplitRatioOrg: '/bff/cost/split_ratio_org',
  ExportCostSplitRatioOrg: '/bff/cost/split_ratio_org/export',
  ListCostSplitCost: '/bff/cost/split_cost',
  ExportCostSplitCost: '/bff/cost/split_cost/export',
  ListCostSplitCostOrg: '/bff/cost/split_cost_org',
  ExportCostSplitCostOrg: '/bff/cost/split_cost_org/export',
  ListBusiness: '/bff/cost/split_ratio/list/business',
}

export function getCostSplitRatioList(parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getCostSplitRatioInfo(id) {
  return request({
    url: `${costApi.Get}${id}`,
    method: 'get',
  })
}

export function updateCostSplitRatio(data) {
  return request({
    url: costApi.Update,
    method: 'put',
    data: data,
  })
}

export function getSplitRatioBusinessList(parameter) {
  return request({
    url: costApi.ListBusiness,
    method: 'get',
    params: parameter,
  })
}

export function listCostSplitRatioOrg(parameter) {
  return request({
    url: costApi.ListCostSplitRatioOrg,
    method: 'get',
    params: parameter,
  })
}

export function listCostSplitCost(parameter) {
  return request({
    url: costApi.ListCostSplitCost,
    method: 'get',
    params: parameter,
  })
}

export function listCostSplitCostOrg(parameter) {
  return request({
    url: costApi.ListCostSplitCostOrg,
    method: 'get',
    params: parameter,
  })
}

export function exportCostSplitRatio(parameter) {
  return request({
    url: costApi.Export,
    method: 'get',
    params: parameter,
  })
}

export function exportCostSplitRatioOrg(parameter) {
  return request({
    url: costApi.ExportCostSplitRatioOrg,
    method: 'get',
    params: parameter,
  })
}

export function exportCostSplitCost(parameter) {
  return request({
    url: costApi.ExportCostSplitCost,
    method: 'get',
    params: parameter,
  })
}

export function exportCostSplitCostOrg(parameter) {
  return request({
    url: costApi.ExportCostSplitCostOrg,
    method: 'get',
    params: parameter,
  })
}
