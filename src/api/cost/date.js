import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/date',
  DashboardUser: '/bff/cost/dashboard/user',
  UserDate: '/bff/cost/user/date',
  TeamCost: '/bff/cost/dashboard/team',
  DepCost: '/bff/cost/dashboard/dep',
  OrgCost: '/bff/cost/dashboard/org',
  CompCost: '/bff/cost/dashboard/company',
  Export: '/bff/cost/date/export',
  CreateDiff: '/bff/cost/date/diff',
  BusinessData: '/bff/cost/statistic/dashboard',
  Estimation: '/bff/cost/estimation/dashboard',
  EstimationTable: '/bff/cost/estimation',
  CostUpdate: '/bff/cost/estimation',
  BusinessAuth: '/bff/cost/statistic/authorized/business',
  ExportCostEstimation: '/bff/cost/estimation/export',
  ExportCostReportEstimation: '/bff/cost/estimation/report_export',
  CostAnalysis: '/bff/cost/analysis',
  StorageCostInfo: 'bff/cost/analysis/dashboard',
  GetCostAnalysisSelected: 'bff/cost/analysis/selected',
  ExportCostAnalysis: '/bff/cost/analysis/export',
  GetCostAnalysisDashboard: '/bff/cost/analysis/dashboard',
  UpdateCostAnalysisReason: '/bff/cost/analysis'
}

export function getBusinessData (parameter) {
  return request({
    url: costApi.BusinessData,
    method: 'get',
    params: parameter
  })
}

export function getStorageCostInfo (parameter) {
  return request({
    url: costApi.StorageCostInfo,
    method: 'get',
    params: parameter
  })
}

export function getCostAnalysisSelected (parameter) {
  return request({
    url: costApi.GetCostAnalysisSelected,
    method: 'get',
    params: parameter
  })
}

export function getCostAnalysisDashboard (parameter) {
  return request({
    url: costApi.GetCostAnalysisDashboard,
    method: 'get',
    params: parameter
  })
}

export function BusinessAuth (parameter) {
  return request({
    url: costApi.BusinessAuth,
    method: 'get',
    params: parameter
  })
}

export function CostUpdate (parameter) {
  return request({
    url: costApi.CostUpdate,
    method: 'put',
    data: parameter
  })
}

export function getCostDateList (parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter
  })
}

export function getEstimation (parameter) {
  return request({
    url: costApi.Estimation,
    method: 'get',
    params: parameter
  })
}

export function exportCostEstimation (parameter) {
  return request({
    url: costApi.ExportCostEstimation,
    method: 'get',
    params: parameter
  })
}

export function exportCostReportEstimation (parameter) {
  return request({
    url: costApi.ExportCostReportEstimation,
    method: 'get',
    params: parameter
  })
}

export function getEstimationTable (parameter) {
  return request({
    url: costApi.EstimationTable,
    method: 'get',
    params: parameter
  })
}

export function getCostDashboardUser (parameter) {
  return request({
    url: costApi.DashboardUser,
    method: 'get',
    params: parameter
  })
}

export function getTeamCost (parameter) {
  return request({
    url: costApi.TeamCost,
    method: 'get',
    params: parameter
  })
}

export function getDepCost (parameter) {
  return request({
    url: costApi.DepCost,
    method: 'get',
    params: parameter
  })
}

export function getOrgCost (parameter) {
  return request({
    url: costApi.OrgCost,
    method: 'get',
    params: parameter
  })
}

export function getCompCost (parameter) {
  return request({
    url: costApi.CompCost,
    method: 'get',
    params: parameter
  })
}

export function getCostUserDate (parameter) {
  return request({
    url: costApi.UserDate,
    method: 'get',
    params: parameter
  })
}

export function exportCostDate (parameter) {
  return request({
    url: costApi.Export,
    method: 'get',
    params: parameter
  })
}

export function createCostDateDiff (data) {
  return request({
    url: costApi.CreateDiff,
    method: 'post',
    data: data
  })
}

export function listCostAnalysis (parameter) {
  return request({
    url: costApi.CostAnalysis,
    method: 'get',
    params: parameter
  })
}

export function exportCostAnalysis (parameter) {
  return request({
    url: costApi.ExportCostAnalysis,
    method: 'get',
    params: parameter
  })
}

export function updateCostAnalysisReason (data) {
  return request({
    url: costApi.UpdateCostAnalysisReason,
    method: 'put',
    data: data
  })
}
