import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/split_ratio_product',
  Get: '/bff/cost/split_ratio_product/',
  Update: '/bff/cost/split_ratio_product',
  Create: '/bff/cost/split_ratio_product',
  ProductLine: '/bff/cost/product_line'
}

export function getCostSplitRatioProductList (parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter
  })
}

export function getCostSplitRatioProductInfo (id) {
  return request({
    url: `${costApi.Get}${id}`,
    method: 'get'
  })
}

export function updateCostSplitRatioProduct (data) {
  return request({
    url: costApi.Update,
    method: 'put',
    data: data
  })
}

export function createCostSplitRatioProduct (data) {
  return request({
    url: costApi.Create,
    method: 'post',
    data: data
  })
}

export function listCostProductLine (parameter) {
  return request({
    url: costApi.ProductLine,
    method: 'get',
    params: parameter
  })
}

export function getCostProductLine (id) {
  return request({
    url: `${costApi.ProductLine}/${id}`,
    method: 'get'
  })
}

export function updateCostProductLine (data) {
  return request({
    url: costApi.ProductLine,
    method: 'put',
    data: data
  })
}

export function createCostProductLine (data) {
  return request({
    url: costApi.ProductLine,
    method: 'post',
    data: data
  })
}

export function deleteCostProductLine (id) {
  return request({
    url: `${costApi.ProductLine}/${id}`,
    method: 'delete'
  })
}
