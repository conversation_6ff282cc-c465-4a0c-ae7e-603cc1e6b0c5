import request from '@/utils/request'

const budgetApi = {
  List: '/bff/cost/budget',
  Info: '/bff/cost/budget/info/',
  Create: '/bff/cost/budget/create',
  Update: '/bff/cost/budget/update',
  Delete: '/bff/cost/budget/',
}

export function getBudgetList(parameter) {
  return request({
    url: budgetApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getBudgetInfo(id) {
  return request({
    url: `${budgetApi.Info}${id}`,
    method: 'get',
  })
}

export function createBudget(data) {
  return request({
    url: `${budgetApi.Create}`,
    method: 'post',
    data: data,
  })
}

export function updateBudget(data) {
  return request({
    url: `${budgetApi.Update}`,
    method: 'put',
    data: data,
  })
}

export function deleteBudget(id) {
  return request({
    url: `${budgetApi.Delete}${id}`,
    method: 'delete',
  })
}
