import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/sms_supplier',
  Get: '/bff/cost/sms_supplier/',
  Update: '/bff/cost/sms_supplier',
  Create: '/bff/cost/sms_supplier',
  Export: '/bff/cost/sms_supplier/export',
  ListCostMonthSms: '/bff/cost/month/sms',
  ExportCostMonthSms: '/bff/cost/month/sms/export',
}

export function listSmsSupplierList(parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter,
  })
}

export function exportSmsSupplierList(parameter) {
  return request({
    url: costApi.Export,
    method: 'get',
    params: parameter,
  })
}

export function getSmsSupplier(id) {
  return request({
    url: `${costApi.Get}${id}`,
    method: 'get',
  })
}

export function updateSmsSupplier(data) {
  return request({
    url: costApi.Update,
    method: 'put',
    data: data,
  })
}

export function createSmsSupplier(data) {
  return request({
    url: costApi.Create,
    method: 'post',
    data: data,
  })
}

export function listCostMonthSms(parameter) {
  return request({
    url: costApi.ListCostMonthSms,
    method: 'get',
    params: parameter,
  })
}

export function exportCostMonthSms(parameter) {
  return request({
    url: costApi.ExportCostMonthSms,
    method: 'get',
    params: parameter,
  })
}
