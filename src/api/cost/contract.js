import request from '@/utils/request'
const contractApi = {
    cm: '/bff/cost/contract',
    status: '/bff/cost/contract/status',
    pdfInfo: '/bff/cost/contract/url',
    ops: '/db/osp/upload_file'
}
export function uploadFile (data) {
    return request({
    headers: {
        'Content-Type': 'multipart/form-data'
    },
    url: contractApi.ops,
    method: 'post',
    data: data
    })
}

export function getContractList (parameter) {
    return request({
    url: contractApi.cm,
    method: 'get',
    params: parameter
    })
}

export function deleteContract (parameter) {
    return request({
        url: contractApi.cm,
        method: 'delete',
        params: parameter
    })
}

export function batchContract (data) {
    return request({
        url: contractApi.cm,
        method: 'post',
        data: data
    })
}

export function updateContract (data) {
    return request({
    url: contractApi.status,
    method: 'put',
    data: data
    })
}

export function saveContractFileInfo (data) {
    return request({
        url: contractApi.pdfInfo,
        method: 'post',
        data: data
    })
}
