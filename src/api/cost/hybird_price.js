import request from '@/utils/request'

const priceApi = {
  cn: '/bff/cost/pybird/price',
  Discount: '/bff/cost/pybird/discount',
  exchangeRate: '/bff/cost/exchange_rates'
}

export function getHybirdPriceList (parameter) {
  return request({
    url: priceApi.cn,
    method: 'get',
    params: parameter
  })
}
export function updateHybirdPriceDiscount (parameter) {
  return request({
    url: priceApi.Discount,
    method: 'get',
    params: parameter
  })
}

export function deleteHybirdPrice (parameter) {
  return request({
    url: priceApi.cn,
    method: 'delete',
    params: parameter
  })
}

export function createHybirdPrice (data) {
  return request({
    url: priceApi.cn,
    method: 'post',
    data: data
  })
}

export function updateHybirdPrice (data) {
  return request({
    url: priceApi.cn,
    method: 'put',
    data: data
  })
}

export function getExchangeRateList (parameter) {
  return request({
    url: priceApi.exchangeRate,
    method: 'get',
    params: parameter
  })
}
