import request from '@/utils/request'

const fixedAssetsApi = {
  List: '/bff/cost/fixed_assets',
  Create: '/bff/cost/fixed_assets',
  ListRatio: '/bff/cost/fixed_assets/ratio',
  CreateRatio: '/bff/cost/fixed_assets/ratio',
  UpdateRatio: '/bff/cost/fixed_assets/ratio',
  Dashboard: '/bff/cost/fixed_assets/dashboard',
  GetRatio: '/bff/cost/fixed_assets/ratio/',
  Which: '/bff/cost/fixed_assets/which/',
  Get: '/bff/cost/fixed_assets/',
  Update: '/bff/cost/fixed_assets'
}

export function listFixedAssets (parameter) {
  return request({
    url: fixedAssetsApi.List,
    method: 'get',
    params: parameter
  })
}

export function getFixedAssets (id) {
  return request({
    url: `${fixedAssetsApi.Get}${id}`,
    method: 'get'
  })
}

export function createFixedAssets (data) {
  return request({
    url: `${fixedAssetsApi.Create}`,
    method: 'post',
    data: data
  })
}

export function updateFixedAssets (data) {
  return request({
    url: `${fixedAssetsApi.Update}`,
    method: 'put',
    data: data
  })
}

export function listFixedAssetsRatio (parameter) {
  return request({
    url: fixedAssetsApi.ListRatio,
    method: 'get',
    params: parameter
  })
}

export function updateFixedAssetsRatio (data) {
  return request({
    url: `${fixedAssetsApi.UpdateRatio}`,
    method: 'put',
    data: data
  })
}

export function createFixedAssetsRatio (data) {
  return request({
    url: `${fixedAssetsApi.CreateRatio}`,
    method: 'post',
    data: data
  })
}

export function listFixedAssetsDashboard (parameter) {
  return request({
    url: fixedAssetsApi.Dashboard,
    method: 'get',
    params: parameter
  })
}

export function getFixedAssetsRatio (id) {
  return request({
    url: `${fixedAssetsApi.GetRatio}${id}`,
    method: 'get'
  })
}

export function listFixedAssetsWhich (which) {
  return request({
    url: `${fixedAssetsApi.Which}${which}`,
    method: 'get'
  })
}
