import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/month',
  Get: '/bff/cost/month/',
  Update: '/bff/cost/month',
  CreateDiff: '/bff/cost/month/diff',
  ResList: '/bff/cost/month/which/',
  ProductNum: '/bff/cost/month/product',
  Export: '/bff/cost/month/export',
  FileTask: '/bff/cost/file_task',
  BillCheck: '/bff/cost/bill_check',
  CostMonthSupplierCheck: '/bff/cost/month/supplier_check',
}

export function getCostMonthList(parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getCostMonthInfo(id) {
  return request({
    url: `${costApi.Get}${id}`,
    method: 'get',
  })
}

export function updateCostMonth(data) {
  return request({
    url: costApi.Update,
    method: 'put',
    data: data,
  })
}

export function createCostMonthDiff(data) {
  return request({
    url: costApi.CreateDiff,
    method: 'post',
    data: data,
  })
}

export function getResList(which) {
  return request({
    url: `${costApi.ResList}${which}`,
    method: 'get',
  })
}

export function getProductNumByEmail(data) {
  return request({
    url: costApi.ProductNum,
    method: 'post',
    data: data,
  })
}

export function exportCostMonth(parameter) {
  return request({
    url: costApi.Export,
    method: 'get',
    params: parameter,
  })
}

export function getFileTaskList(parameter) {
  return request({
    url: costApi.FileTask,
    method: 'get',
    params: parameter,
  })
}

export function getBillCheckList(parameter) {
  return request({
    url: costApi.BillCheck,
    method: 'get',
    params: parameter,
  })
}

export function costMonthSupplierCheck(parameter) {
  return request({
    url: costApi.CostMonthSupplierCheck,
    method: 'get',
    params: parameter,
  })
}

export function createBillCheck(data) {
  return request({
    url: costApi.BillCheck,
    method: 'post',
    data: data,
  })
}
