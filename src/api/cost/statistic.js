import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/statistic',
  Export: '/bff/cost/statistic/export',
  ResList: '/bff/cost/statistic/which/',
  Product: '/bff/cost/statistic/product',
}

export function getCostStatisticList(parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getResList(which) {
  return request({
    url: `${costApi.ResList}${which}`,
    method: 'get',
  })
}

export function exportCostBillStatistic(parameter) {
  return request({
    url: costApi.Export,
    method: 'get',
    params: parameter,
  })
}

export function createCostProductDifference(data) {
  return request({
    url: costApi.Product,
    method: 'put',
    data: data,
  })
}
