import request from '@/utils/request'

const costApi = {
  List: '/bff/cost/label',
  Info: '/bff/cost/label/info/',
  // SupplierList: '/bff/cost/label/supplier',
  // ModuleList: '/bff/cost/label/module',
  // SecondaryOrganizationList: '/bff/cost/label/secondaryOrganization',
  // TertiaryOrganizationList: '/bff/cost/label/tertiaryOrganization',
  // ProjectList: '/bff/cost/label/project',
  ResList: '/bff/cost/label/which/',
  Update: '/bff/cost/label/update',
  ProductLineList: '/bff/cost/label/product_line',
}

export function getCostLabelList(parameter) {
  return request({
    url: costApi.List,
    method: 'get',
    params: parameter,
  })
}

export function getCostLabelInfo(id) {
  return request({
    url: `${costApi.Info}${id}`,
    method: 'get',
  })
}

// export function getSupplierList () {
//   return request({
//     url: costApi.SupplierList,
//     method: 'get'
//   })
// }

// export function getModuleList () {
//   return request({
//     url: costApi.ModuleList,
//     method: 'get'
//   })
// }

// export function getSecondaryOrganizationList () {
//   return request({
//     url: costApi.SecondaryOrganizationList,
//     method: 'get'
//   })
// }

// export function getTertiaryOrganizationList () {
//   return request({
//     url: costApi.TertiaryOrganizationList,
//     method: 'get'
//   })
// }

// export function getProjectList () {
//   return request({
//     url: costApi.ProjectList,
//     method: 'get'
//   })
// }

export function getResList(which) {
  return request({
    url: `${costApi.ResList}${which}`,
    method: 'get',
  })
}

export function updateCostLabel(data) {
  return request({
    url: `${costApi.Update}`,
    method: 'put',
    data: data,
  })
}

export function getProductLineList(parameter) {
  return request({
    url: costApi.ProductLineList,
    method: 'get',
    params: parameter,
  })
}
