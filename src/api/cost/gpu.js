import request from '@/utils/request'

const gpuApi = {
  ListGpuAsset: '/bff/cost/gpu/asset',
  listGpuAssetExport: '/bff/cost/gpu/asset/export',
  CreateGpuAsset: '/bff/cost/gpu/asset/create',
  GetGpuAsset: '/bff/cost/gpu/asset/info/',
  UpdateGpuAsset: '/bff/cost/gpu/asset/update',
  DeleteGpuAsset: '/bff/cost/gpu/asset/',
  ListGpuAssetChangeLog: '/bff/cost/gpu/asset/change_log',
  ListGpuAssetChangeLogIp: '/bff/cost/gpu/asset/change_log/ips',
  CreateGpuAssetChangeLog: '/bff/cost/gpu/asset/change_log'
}

export function listGpuAsset (parameter) {
  return request({
    url: gpuApi.ListGpuAsset,
    method: 'get',
    params: parameter
  })
}

export function listGpuAssetExport (parameter) {
  return request({
    url: gpuApi.listGpuAssetExport,
    method: 'get',
    params: parameter
  })
}

export function createGpuAsset (data) {
  return request({
    url: `${gpuApi.CreateGpuAsset}`,
    method: 'post',
    data: data
  })
}

export function getGpuAsset (id) {
  return request({
    url: `${gpuApi.GetGpuAsset}${id}`,
    method: 'get'
  })
}

export function updateGpuAsset (data) {
  return request({
    url: `${gpuApi.UpdateGpuAsset}`,
    method: 'put',
    data: data
  })
}

export function deleteGpuAsset (id) {
  return request({
    url: `${gpuApi.DeleteGpuAsset}${id}`,
    method: 'delete'
  })
}

export function listGpuAssetChangeLog (parameter) {
  return request({
    url: gpuApi.ListGpuAssetChangeLog,
    method: 'get',
    params: parameter
  })
}

export function listGpuAssetWhich (parameter) {
  return request({
    url: gpuApi.ListGpuAssetChangeLogIp,
    method: 'get',
    params: parameter
  })
}

export function createGpuAssetChangeLog (data) {
  return request({
    url: gpuApi.CreateGpuAssetChangeLog,
    method: 'post',
    data: data
  })
}
