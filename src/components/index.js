/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-20 14:38:10
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-05-12 11:27:54
 * @FilePath: \cloud_web\src\components\index.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
// chart
// import Bar from '@/components/Charts/Bar'
import ChartCard from '@/components/Charts/ChartCard.vue'
// import Liquid from '@/components/Charts/Liquid'
// import MiniArea from '@/components/Charts/MiniArea'
// import MiniSmoothArea from '@/components/Charts/MiniSmoothArea'
// import MiniBar from '@/components/Charts/MiniBar'
import MiniProgress from '@/components/Charts/MiniProgress.vue'
// import Radar from '@/components/Charts/Radar'
import RankList from '@/components/Charts/RankList.vue'
// import TransferBar from '@/components/Charts/TransferBar'
// import TagCloud from '@/components/Charts/TagCloud'

// pro components
import AvatarList from '@/components/AvatarList'
import Ellipsis from '@/components/Ellipsis'
import FooterToolbar from '@/components/FooterToolbar'
import NumberInfo from '@/components/NumberInfo'
import Tree from '@/components/Tree/Tree'
import Trend from '@/components/Trend'
import STable from '@/components/Table/index.tsx'
import MultiTab from '@/components/MultiTab'
import IconSelector from '@/components/IconSelector'
import TagSelect from '@/components/TagSelect'
import StandardFormRow from '@/components/StandardFormRow'
import ArticleListContent from '@/components/ArticleListContent'

import Dialog from '@/components/Dialog'

export {
  AvatarList,
  // Bar,
  ChartCard,
  // Liquid,
  // MiniArea,
  // MiniSmoothArea,
  // MiniBar,
  MiniProgress,
  // Radar,
  // TagCloud,
  RankList,
  // TransferBar,
  Trend,
  Ellipsis,
  FooterToolbar,
  NumberInfo,
  Tree,
  STable,
  MultiTab,
  IconSelector,
  TagSelect,
  StandardFormRow,
  ArticleListContent,

  Dialog
}
