<template>
  <div class="object-array-form">
    <a-form-item v-for="item in localValue" :key="item.id">
      <div class="object-item">
        <a-space v-for="field in schema" :key="field.model" style="margin-bottom: 8px">
          <span class="field-label">{{ field.label }}：</span>
          <a-input
            v-if="field.type === 'string'"
            v-model:value="item[field.model]"
            :placeholder="field.placeholder"
            :status="getFieldStatus(item, field)"
          />
          <a-select
            v-else-if="field.type === 'select'"
            v-model:value="item[field.model]"
            :options="field.options"
            style="width: 200px"
          />
        </a-space>
        <a-button
          type="link"
          danger
          @click="removeItem(item.id)"
          class="remove-btn"
        >
          <delete-outlined />
        </a-button>
      </div>
    </a-form-item>
    
    <a-button type="dashed" @click="addItem">
      <plus-outlined />
      添加过滤规则
    </a-button>
  </div>
</template>

<script setup>
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { ref, watch, onMounted } from 'vue';
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
    required: true // 强制要求父组件传递value
  },
  schema: {
  type: Array,
  required: true,
  validator: (schema) => schema.every(field => field.model && field.type),
},
  errorMessage: {
  type: String,
  default: '必填项未填写'
}
});
const emit = defineEmits(['update:value']);
const localValue = ref(
  props.value.map(item => ({
    ...item,
    id: item.id || Date.now() + Math.random()
  }))
);

// 监听localValue变化并执行校验
watch(
  () => localValue.value,
  (newVal) => {
    let hasError = false;
    newVal.forEach(item => {
      props.schema.forEach(field => {
        if (field.required && !item[field.model]) {
          hasError = true;
        }
      });
    });
    errors.value = hasError ? [props.errorMessage] : [];
  },
  { deep: true }
);

const createNewItem = () => {
  const newItem = {};
  props.schema.forEach(field => {
    newItem[field.model] = field.default ?? '';
  });
  newItem.id = Date.now() + Math.random(); // 添加唯一标识符
  return newItem;
};

const addItem = () => {
  const newItem = createNewItem();
  localValue.value = [...localValue.value, newItem];
  emit('update:value', localValue.value);
};

const removeItem = (itemId) => {
  localValue.value = localValue.value.filter(item => item.id !== itemId);
  emit('update:value', localValue.value);
};

const getFieldStatus = (item, field) => {
  if (field.required && !item[field.model]) {
    return 'error';
  }
  return '';
};

// 初始化时验证schema有效性
onMounted(() => {
  if (!props.schema.length) {
    console.error('Schema must contain at least one field');
  }
});
</script>

<style scoped>
.object-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
}

.field-label {
  min-width: 80px;
  text-align: right;
}

.remove-btn {
  margin-left: auto;
}

.error-message {
  color: red;
  margin-top: 10px;
}
</style>