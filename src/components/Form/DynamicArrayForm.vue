<template>
  <div class="dynamic-array-form">
    <transition-group name="list" tag="div">
      <div
        v-for="(item, idx) in localItems"
        :key="`item-${item.id}`"
        class="array-item"
      >
        <a-input
          :value="item.value"
          @change="(e) => handleInputChange(idx, e)"
          :placeholder="field.placeholder"
          class="array-input"
          :ref="el => { if(el) inputRefs[item.id] = el }"
        />
        <a-button
          danger
          ghost
          @click="confirmRemove(idx)"
          class="remove-btn"
          :title="`删除${field.label}`"
        >
          <template #icon><delete-outlined /></template>
        </a-button>
      </div>
    </transition-group>

    <a-button
      type="dashed"
      @click="addItem"
      class="add-btn"
      :disabled="isAddDisabled"
    >
      <template #icon><plus-circle-outlined /></template>
      添加{{ field.label }}
    </a-button>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';

const props = defineProps({
  field: {
    type: Object,
    required: true,
    validator: (value) => value && typeof value === 'object' && 'label' in value
  },
  value: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:value']);

const localItems = ref([]);
const nextId = ref(1);
const inputRefs = reactive({});

const isAddDisabled = computed(() => {
  return !!props.field.maxItems && localItems.value.length >= props.field.maxItems;
});

watch(
  () => props.value,
  (newVal) => {
    // 确保 value 是数组
    const arrayValue = Array.isArray(newVal) ? newVal : [];
    
    // 初始化本地数据
    localItems.value = arrayValue.map((val, index) => {
      const existingItem = localItems.value[index];
      const id = existingItem ? existingItem.id : nextId.value++;
      return {
        id,
        value: val || ''
      };
    });
    
    // 如果是空数组且field有默认数据，初始化一个空项
    if (arrayValue.length === 0 && !isAddDisabled.value) {
      addItem();
    }
  },
  { immediate: true }
);

const addItem = () => {
  if (isAddDisabled.value) {
    message.warning(`已达到最大允许数量（${props.field.maxItems}）`);
    return;
  }
  
  const newId = nextId.value++;
  const newItem = {
    id: newId,
    value: ''
  };
  
  localItems.value.push(newItem);
  // syncValues();
  
  // // 自动聚焦到新输入框
  // nextTick(() => {
  //   if (inputRefs[newId]) {
  //     inputRefs[newId].focus();
  //   }
  // });
};

const confirmRemove = (index) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除这个${props.field.label}吗？`,
    onOk: () => removeItem(index)
  });
};

const removeItem = (index) => {
  if (index >= 0 && index < localItems.value.length) {
    localItems.value.splice(index, 1);
    syncValues();
  }
};

const handleInputChange = (index, e) => {
  const newValue = e.target.value;
  if (index >= 0 && index < localItems.value.length) {
    localItems.value[index].value = newValue;
    syncValues();
  }
};

const syncValues = () => {
  try {
    // 提取纯字符串数组作为输出值
    const values = localItems.value
      .map(item => item.value ? item.value.trim() : '')
      .filter(v => v !== '');
      
    emit('update:value', values);
  } catch (error) {
    console.error('同步数据时发生错误:', error);
    message.error('数据同步失败，请检查输入');
  }
};
</script>

<style scoped>
.dynamic-array-form {
  width: 100%;
}

.array-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.array-input {
  flex: 1;
}

.remove-btn {
  flex-shrink: 0;
}

.add-btn {
  margin-top: 8px;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.3s;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>