<!-- 用法：
在需要二次验证的页面，引入该组件并注册，将页面所有内容复制进组件中即可
例:
<SecondaryVerification>
...页面内容...
</SecondaryVerification>
-->

<template>
  <div class="secondary-verification">
    <template v-if="!verified">
      <a-card :bordered="false">
        <div style="width: 245px; margin: auto" class="box-card">
          <p style="text-align: center">当前状态需要二次认证</p>
          <div style="width: 100%; margin-bottom: 17px">
            <a-input style="width: 140px" placeholder="请输入验证码" v-model:value="verificationCode" />
            <a-button style="width: 105px" @click="getVerifyCode">获取验证码</a-button>
          </div>
          <a-button type="primary" style="width: 100%; margin: 0" @click="checkoutCode">认证</a-button>
        </div>
      </a-card>
    </template>
    <slot v-else></slot>
  </div>
</template>
<script>
import { ref } from 'vue'
import { waterMarkVs } from '@/utils/watermark'
import { myColumns } from '@/views/ai/aiData.js'
import { globalOtp, sendCOde, CheckCode } from '@/api/commonVerify/index'
export default {
  name: 'SecondaryVerification',
  props: {
    title: {
      type: String,
      default: '二次验证',
    },
    inputType: {
      type: String,
      default: 'text',
    },
    placeholder: {
      type: String,
      default: '请输入验证码',
    },
    verificationType: {
      type: String,
      default: 'sms',
      validator: value => ['sms', 'email', 'custom'].includes(value),
    },
    customVerifyFunction: {
      type: Function,
      default: null,
    },
  },
  emits: ['verification-success', 'verification-failure'],
  setup(props, { emit }) {
    let authOtpKey = ref('') //key
    const plantform = ref('web')
    const verificationCode = ref('')
    const verified = ref(false)
    // 密钥密钥
    const getInitUsersStatus = () => {
      authOtpKey.value = ''
      myColumns.forEach(item => {
        authOtpKey.value += item.scopeKey
      })
      authOtpKey.value = waterMarkVs + authOtpKey.value
    }
    getInitUsersStatus()
    // 平台判断
    const plantFormType = () => {
      var ua = window.navigator.userAgent.toLowerCase()
      if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
        plantform.value = 'qw'
      } else {
        plantform.value = 'web'
      }
    }
    plantFormType()
    // 判断页面是否需要二次验证 （过期状态）
    const checkStatus = () => {
      globalOtp().then(res => {
        authToStr(res.Data.network, authOtpKey.value).then(r => {
          authToStr(res.Data.otpToken, authOtpKey.value).then(res1 => {
            if (res1) {
              localStorage.setItem('globalToken', res.Data.otpToken)
              verified.value = true
              emit('verification-success')
            } else {
              verified.value = false
              emit('verification-failure')
            }
          })
        })
      })
    }

    // 加解密
    const authToStr = async (text, a) => {
      const importedKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(a),
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      )
      const textData = atob(text)
      const dataArray = new Uint8Array(textData.split('').map(char => char.charCodeAt(0)))

      const nonceSize = 12
      const nonce = dataArray.slice(0, nonceSize)
      const ciphertext = dataArray.slice(nonceSize)
      try {
        const myData = await crypto.subtle.decrypt({ name: 'AES-GCM', iv: nonce }, importedKey, ciphertext)
        return new TextDecoder().decode(myData)
      } catch (error) {
        return null
      }
    }
    // 提交校验
    const checkoutCode = async () => {
      CheckCode({
        code: verificationCode.value,
      }).then(res => {
        if (res.Data.message == 'passCheck') {
          authToStr(res.Data.otpToken, authOtpKey.value)
            .then(res1 => {
              if (res1) {
                localStorage.setItem('globalToken', res.Data.otpToken)
                verified.value = true
                emit('verification-success')
              } else {
                verified.value = false
                emit('verification-failure')
              }
            })
            .catch(err => {
              console.log(err, 'err')
            })
        } else {
          emit('verification-failure')
          noc.notice.error({
            message: '认证失败',
            duration: 3,
          })
        }
      })
    }
    const getVerifyCode = () => {
      sendCOde(plantform.value)
        .then(res => {
          console.log(res, 'msg')
          noc.notice.ok({
            message: '发送成功',
          })
        })
        .catch(() => {
          noc.notice.error({
            message: '发送失败',
          })
        })
    }
    checkStatus()
    return {
      getVerifyCode,
      plantform,
      verificationCode,
      checkoutCode,
      verified,
    }
  },
}
</script>

<style scoped>
.secondary-verification {
  /* 添加您的样式 */
}
.error {
  color: red;
}
</style>
