/*
@header-menu-prefix-cls: ~'@{ant-prefix}-pro-header-menu';
@header-drop-down-prefix-cls: ~'@{ant-prefix}-pro-drop-down';

.@{header-menu-prefix-cls} {

  .anticon {
    margin-right: 8px;
  }
  .ant-dropdown-menu-item {
    min-width: 160px;
  }
}

.@{header-drop-down-prefix-cls} {

  line-height: @layout-header-height;
  vertical-align: top;
  cursor: pointer;

  > i {
    font-size: 16px !important;
    transform: none !important;

    svg {
      position: relative;
      top: -1px;
    }
  }
}
*/
