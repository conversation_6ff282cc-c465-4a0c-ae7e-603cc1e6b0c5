import { defineComponent, renderSlot } from 'vue'
import { pageTotalShow } from '@aim/helper'

export default defineComponent({
  name: 'STable',
  props: {
    rowKey: {
      type: [String, Function],
      default: 'key',
    },
    columns: {
      default: () => [],
      type: Array,
    },
    data: {
      type: Function,
      required: true,
    },
    pageNum: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    showSizeChanger: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'default',
    },
    alert: {
      type: [Object, Boolean],
      default: null,
    },
    rowSelection: {
      type: Object,
      default: undefined,
    },
    showPagination: {
      type: [String, Boolean],
      default: 'auto',
    },
    pagination: {
      type: Object,
      default: undefined,
    }
  },
  data() {
    return {
      localLoading: false,
      localDataSource: [],
      localPagination: Object.assign(
        {
          locale: { items_per_page: '项 / 页' },
          showTotal: pageTotalShow,
        },
        this.pagination
      ),

      selectedRows: [],
      selectedRowKeys: [],

      // 存储表格 onchange 时的对象
      filters: {},
      sorter: {},
    }
  },
  computed: {
    showAlert() {
      return (this.alert && this.alert.show && this.rowSelection && this.rowSelection.selectedRowKeys) || this.alert
    },
  },
  created() {
    const localPageNum = this.pageNum
    this.localPagination =
      (['auto', true].includes(this.showPagination) &&
        Object.assign({}, this.localPagination, {
          current: localPageNum,
          pageSize: this.pageSize,
          showSizeChanger: this.showSizeChanger,
        })) ||
      false
    this.loadData()
  },
  methods: {
    onChange(pagination, filters, sorter) {
      Object.assign(this.localPagination, pagination)
      this.filters = filters
      this.sorter = sorter
      this.loadData()
    },
    /**
     * 加载数据方法
     * @param {Object} pagination 分页选项器
     * @param {Object} filters 过滤条件
     * @param {Object} sorter 排序条件
     */
    loadData(pagination?, filters = this.filters, sorter = this.sorter) {
      this.filters = filters
      this.sorter = sorter

      const parameter = Object.assign(
        {
          pageNo:
            (pagination && pagination.current) || (this.showPagination && this.localPagination.current) || this.pageNum,
          pageSize:
            (pagination && pagination.pageSize) ||
            (this.showPagination && this.localPagination.pageSize) ||
            this.pageSize,
        },
        (sorter &&
          sorter.field && {
          sortField: sorter.field,
        }) ||
          {},
        (sorter &&
          sorter.order && {
          sortOrder: sorter.order,
        }) ||
          {},
        {
          ...filters,
        }
      )
      const result = this.data(parameter)
      if (result && typeof result.then === 'function') {
        this.localLoading = true
        result
          .then(res => {
            this.localPagination =
              (this.showPagination &&
                Object.assign({}, this.localPagination, {
                  current: res.pageNo, // 返回结果中的当前分页数
                  total: res.totalCount, // 返回结果中的总记录数
                  showSizeChanger: this.showSizeChanger,
                  pageSize: (pagination && pagination.pageSize) || this.localPagination.pageSize,
                })) ||
              false
            if (res.data == null) {
              res.data = []
            }
            // 为防止删除数据后导致页面当前页面数据长度为 0，自动翻页到上一页
            if (res.data.length === 0 && this.showPagination && this.localPagination.current > 1) {
              this.localPagination.current--
              this.loadData()
              return
            }
            /**
             * 这里用于判断接口是否有返回 res.totalCount 且 this.showPagination = true 且 pageNo 和 pageSize 存在 且 totalCount 小于等于 pageNo * pageSize 的大小
             * 当情况满足时，表示数据不满足分页大小，关闭 table 分页功能
             */
            try {
              if (
                ['auto', true].includes(this.showPagination) &&
                res.totalCount <= res.pageNo * this.localPagination.pageSize
              ) {
                this.localPagination.hideOnSinglePage = true
              }
            } catch (e) {
              this.localPagination = false
            }
            this.localDataSource = res.data // 返回结果中的数组数据
          })
          .finally(() => {
            this.localLoading = false
          })
      }
    },
    /**
     * 清空已选中项
     */
    clearSelected() {
      if (this.rowSelection) {
        this.rowSelection.onChange([], [])
        this.updateSelected([], [])
      }
    },
    /**
     * 刷新表格
     * @param goToFirst 是否回到第一页
     */
    refresh(goToFirst = false) {
      if (goToFirst) {
        this.localPagination.current = 1
      }
      this.loadData()
    },
    /**
     * 更新已选中项
     */
    updateSelected(selectedRowKeys, selectedRows) {
      this.selectedRows = selectedRows
      this.selectedRowKeys = selectedRowKeys
    },
    /**
     * 处理交给 table 使用者去处理 clear 事件时，内部选中统计同时调用
     * @param callback
     * @returns {*}
     */
    renderClear(callback) {
      if (this.selectedRowKeys.length <= 0) return null
      return (
        <a
          style="margin-left: 24px"
          onClick={() => {
            callback()
            this.clearSelected()
          }}
        >
          清空
        </a>
      )
    },
    renderAlert() {
      // 绘制 清空 按钮
      const clearItem =
        typeof this.alert.clear === 'boolean' && this.alert.clear
          ? this.renderClear(this.clearSelected)
          : this.alert !== null && typeof this.alert.clear === 'function'
          ? this.renderClear(this.alert.clear)
          : null

      // 绘制 alert 组件
      return (
        <a-alert showIcon={true} style="margin-bottom: 16px">
          <template v-slot:message>
            <span style="margin-right: 12px">
              已选择: <a style="font-weight: 600">{this.selectedRows.length}</a>
            </span>
            {clearItem}
          </template>
        </a-alert>
      )
    },
  },
  render() {
    const props = {}
    const localKeys = Object.keys(this.$data)
    const showAlert =
      (typeof this.alert === 'object' &&
        this.alert !== null &&
        this.alert.show &&
        typeof this.rowSelection.selectedRowKeys !== 'undefined') ||
      this.alert

    Object.keys(this.$props).forEach(k => {
      const localKey = `local${k.substring(0, 1).toUpperCase()}${k.substring(1)}`
      if (localKeys.includes(localKey)) {
        props[k] = this[localKey]
        return props[k]
      }
      if (k === 'rowSelection') {
        if (showAlert && this.rowSelection) {
          // 如果需要使用alert，则重新绑定 rowSelection 事件
          props[k] = {
            ...this.rowSelection,
            selectedRows: this.selectedRows,
            selectedRowKeys: this.selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              this.updateSelect(selectedRowKeys, selectedRows)
              typeof this[k].onChange !== 'undefined' && this[k].onChange(selectedRowKeys, selectedRows)
            },
          }
          return props[k]
        } else if (!this.rowSelection) {
          // 如果没打算开启 rowSelection 则清空默认的选择项
          props[k] = null
          return props[k]
        }
      }
      this[k] && (props[k] = this[k])
      return props[k]
    })
    const slots = this.$slots
    const slotsRender = {}
    Object.keys(slots).forEach(name => {
      slotsRender[name] = props => renderSlot(slots, name, props)
    })

    const table = (
      <a-table
        {...{
          props,
          columns: this.columns,
          dataSource: this.localDataSource,
          loading: this.localLoading,
          pagination: this.localPagination,
          rowKey: this.rowKey,
          rowSelection: this.rowSelection,
          showSizeChanger: this.showSizeChanger,
        }}
        onChange={this.loadData}
        onExpand={(expanded, record) => {
          this.$emit('expand', expanded, record)
        }}
        v-slots={slotsRender}
      ></a-table>
    )

    return (
      <div class="table-wrapper">
        {showAlert ? this.renderAlert() : null}
        {table}
      </div>
    )
  },
  watch: {
    'localPagination.current': {
      handler() {
        this.selectedRowKeys = []
        this.selectedRows = []
      },
    },
    pageNum: {
      handler(val) {
        Object.assign(this.localPagination, {
          current: val,
        })
      },
    },
    pageSize: {
      handler(val) {
        Object.assign(this.localPagination, {
          pageSize: val,
        })
      },
    },
    showSizeChanger: {
      handler(val) {
        Object.assign(this.localPagination, {
          showSizeChanger: val,
        })
      },
    },
  },
})
