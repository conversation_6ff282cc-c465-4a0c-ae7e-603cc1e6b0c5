<template>
  <div class="code-editor">
    <div class="toolbar">
      <div>Value</div>
      <div>
        <select v-model="language" class="language-select">
          <option value="json">JSON</option>
          <option value="xml">XML</option>
          <option value="hcl">HCL</option>
          <option value="yaml">YAML</option>
        </select>
        <a-divider type="vertical" />
        <a-tooltip>
          <template #title>代码美化</template>
          <reconciliation-outlined @click="formatCode" class="beautify-button" />
        </a-tooltip>
      </div>
    </div>
    <TxEditorCode
      v-model="content"
      :language="language"
      :options="defaultOpts"
      class="monaco-editor"
      height="380"
      @change="changeValue"
      @widgetCreate="editorCreate"
    />
  </div>
</template>

<script>
export default {
  props: {
    opts: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: String,
    },
  },
  data() {
    return {
      defaultOpts: {
        value: '', // 编辑器的值
        theme: 'vs-dark', // 编辑器主题：vs, hc-black, or vs-dark，更多选择详见官网
        minimap: {
          // 关闭小地图
          enabled: false,
        },
        foldingStrategy: 'indentation', // 代码可分小段折叠
        scrollbar: {
          // 滚动条设置
          verticalScrollbarSize: 8, // 竖滚动条
          horizontalScrollbarSize: 8, // 横滚动条
        },
        autoClosingBrackets: 'always', // 是否自动添加结束括号(包括中括号) "always" | "languageDefined" | "beforeWhitespace" | "never"
        autoClosingDelete: 'always', // 是否自动删除结束括号(包括中括号) "always" | "never" | "auto"
        autoClosingQuotes: 'always', // 是否自动添加结束的单引号 双引号 "always" | "languageDefined" | "beforeWhitespace" | "never"
        overviewRulerBorder: false,
        autoIndent: true, // 自动缩进
        // 存在性能问题，组件已支持自动计算编辑器区域大小
        // automaticLayout: true, // 自动布局
        readOnly: false, // 只读
      },
      language: 'json',
      monacoEditor: null,
      content: '',
    }
  },
  watch: {
    value: {
      handler(val) {
        this.content = val
      },
      immediate: true,
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 生成编辑器配置
      Object.assign(this.defaultOpts, this.opts, { value: this.content })
    },
    editorCreate(editor) {
      this.monacoEditor = editor
    },
    formatCode() {
      this.monacoEditor.getAction('editor.action.formatDocument').run()
    },
    changeValue() {
      this.$emit('update', this.content)
    },
  },
}
</script>

<style lang="less" scoped>
.monaco-editor {
  width: 100%;
  height: 380px;
  border: 1px solid grey;
}
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 34px;
  line-height: 34px;
  border-radius:
    2px 2px,
    0px,
    0px;
  background: rgba(229, 229, 229, 1);
  padding: 0 14px;
  span {
    font-weight: 400;
  }
}
.beautify-button {
  font-size: 20px;
}
</style>
