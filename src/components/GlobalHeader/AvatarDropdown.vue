<template>
  <a-dropdown v-if="avatar || nickname" overlayClassName="popup-user-menu" placement="bottomRight">
    <span class="cursor-pointer">
      <a-avatar v-if="avatar" class="position-relative" size="small" :src="avatar" style="top: -3px" />
      <span v-if="nickname" class="ml-2">{{ nickname }}</span>
    </span>
    <template v-slot:overlay>
      <a-menu>
        <a-menu-item key="center">
          <router-link to="/dashboard/center">
            <a-icon class="mr-2" type="user" />
            {{ $t('menu.account.center') }}
          </router-link>
        </a-menu-item>
        <a-menu-item key="menuShow">
          <router-link to="/dashboard/menuShow">
            <a-icon class="mr-2" type="user" />
            {{ $t('menu.account.menuShow') }}
          </router-link>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="logout" @click="handleLogout">
          <a-icon class="mr-2" type="logout" />
          {{ $t('menu.account.logout') }}
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <span v-else>
    <a-spin size="small" :style="{ marginLeft: 8, marginRight: 8 }" />
  </span>
</template>
<script>
import { Modal } from 'ant-design-vue'
import { mapGetters } from 'vuex'

export default {
  name: 'AvatarDropdown',
  computed: {
    ...mapGetters(['avatar', 'nickname']),
  },
  methods: {
    handleLogout() {
      Modal.confirm({
        title: this.$t('layouts.usermenu.dialog.title'),
        content: this.$t('layouts.usermenu.dialog.content'),
        onOk: () => {
          this.$store.dispatch('Logout').then()
        },
      })
    },
  },
}
</script>
<style lang="less">
.popup-user-menu {
  .ant-dropdown-menu-item {
    min-width: 160px;
    padding-top: 8px;
    padding-bottom: 8px;
  }
}
</style>
