<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-11-27 14:16:23
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-02-10 15:17:32
 * @FilePath: \cloud_web\src\components\GlobalHeader\RightContentNew.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="d-inline-flex align-items-center">
    <a-cascader
      autocomplete="off"
      style="margin-right: 16px"
      v-model:value="cloud_menu_value"
      :options="menus"
      :show-search="true"
      placeholder="菜单搜索"
      :fieldNames="{
        label: 'name',
        value: 'path',
        children: 'children',
      }"
      @change="handleChange"
    />
    <router-link :to="'/workflow/user-route-approve'" style="margin-right: 14px; color: rgba(0, 0, 0, 0.5)">
      权限申请
    </router-link>
    <a-tooltip placement="bottom" title="帮助文档">
      <TxA class="text-black-50" href="https://doc.intsig.net/pages/viewpage.action?pageId=401736095">
        文档
        <a-icon type="question-circle" />
      </TxA>
    </a-tooltip>
    <avatar-dropdown-new class="ml-3" />
  </div>
</template>
<script>
import AvatarDropdownNew from './AvatarDropdownNew.vue'
import { getMenus } from '@/api/routePermission/index'
export default {
  name: 'RightContent',
  components: {
    AvatarDropdownNew,
  },
  data() {
    return {
      cloud_menu_value: '',
      menus: [],
    }
  },
  methods: {
    handleChange(val, selectedOptions) {
      const target = selectedOptions[selectedOptions.length - 1]
      this.$router.push(target.path)
    },
  },
  mounted() {
    getMenus().then(res => {
      this.menus = res.routers
    })
  },
}
</script>
