<template>
  <a-dropdown v-if="avatar || nickname" overlayClassName="popup-user-menu" placement="bottomRight">
    <span class="cursor-pointer">
      <a-avatar v-if="avatar" class="position-relative" size="small" :src="avatar" style="top: -3px" />
      <span v-if="nickname" class="ml-2">{{ nickname }}</span>
    </span>
    <template v-slot:overlay>
      <a-menu class="ant-pro-drop-down menu">
        <a-menu-item key="center">
          <template #icon>
            <a-icon type="user" />
          </template>
          <router-link to="/dashboard/center">
            {{ $t('menu.account.center') }}
          </router-link>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="menuShow">
          <template #icon>
            <a-icon type="MenuOutlined" />
          </template>
          <router-link to="/dashboard/menu_control">
            {{ $t('menu.account.menuShow') }}
          </router-link>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="logout" @click="handleLogout">
          <template #icon>
            <a-icon type="logout" />
          </template>
          {{ $t('menu.account.logout') }}
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <span v-else>
    <a-spin size="small" :style="{ marginLeft: 8, marginRight: 8 }" />
  </span>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  name: 'AvatarDropdown',
  computed: {
    ...mapGetters(['avatar', 'nickname']),
  },
  methods: {
    handleLogout(e) {
      noc.modal.confirm({
        title: this.$t('layouts.usermenu.dialog.title'),
        content: this.$t('layouts.usermenu.dialog.content'),
        onOk: () => {
          this.$store.dispatch('Logout').then()
        },
      })
    },
  },
}
</script>
<style lang="less">
.popup-user-menu {
  .ant-dropdown-menu-item {
    min-width: 160px;
    padding-top: 8px;
    padding-bottom: 8px;
  }
}
</style>
