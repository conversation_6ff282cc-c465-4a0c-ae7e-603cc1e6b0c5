<template>
  <div class="tips-panel">
    <div class="tips-title">
      <a-icon class="tips-title-icon" type="info-circle" />
      {{ title }}
      <span class="tips-title-switch" @click="toggleDetailShow">
        {{ detailShow ? '收起' : '展开' }}
        <a-icon class="icon" :type="detailShow ? 'caret-up' : 'caret-down'" />
      </span>
    </div>
    <div v-show="detailShow" class="tips-detail">
      <slot name="content">
        <div v-for="(item, idx) in contentFormat" :key="idx" class="tips-item">
          <span v-if="item.title" class="tips-item-title">
            {{ item.title }}
          </span>
          <span v-if="item.type === 'link'" class="tips-item-link">
            <span class="tips-item-text">{{ item.text }}：</span>
            <a :href="item.link" target="_blank">{{ item.link }}</a>
          </span>
          <span v-else class="tips-item-text">
            {{ item.text }}
          </span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TipsPanel',
  props: {
    title: {
      type: String,
      default: '',
    },
    content: {
      type: [Array, String],
      default: '',
    },
    expand: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    contentFormat() {
      return [this.content].flat().map(e => {
        if (typeof e === 'object') {
          return e
        } else {
          return {
            text: e,
          }
        }
      })
    },
  },
  data() {
    return {
      detailShow: false,
    }
  },
  created() {
    this.detailShow = this.expand
  },
  methods: {
    toggleDetailShow() {
      this.detailShow = !this.detailShow
    },
  },
}
</script>

<style lang="less" scoped>
.tips-panel {
  padding: 16px;
  border-radius: 10px;
  color: #515977;
  background-color: #f1f4fa;
  .tips-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    &-icon {
      margin-right: 4px;
    }
    &-switch {
      margin-left: 8px;
      cursor: pointer;
      color: #4070ff;
      .icon {
        color: #858ba0;
      }
    }
  }
  .tips-detail {
    margin-top: 10px;
    .tips-item {
      &-title {
        font-size: 12px;
        font-weight: 600;
      }
      &-text {
        font-size: 12px;
      }
    }
    .tips-item-link {
      font-weight: 600;
    }
  }
}
</style>
