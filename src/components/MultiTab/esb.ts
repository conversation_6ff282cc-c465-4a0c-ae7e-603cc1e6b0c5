import { errorReport } from '@aim/helper'

type Func<T = any> = (...args: any[]) => T

export type ESB = {
  _context: any
  /**
   * 使用 emit
   */
  $emit?: (type: string, ...args: any[]) => any
  /**
   * 绑定事件上下文
   * @param context
   */
  bind: (context?: any) => void
  /**
   * 作用同 emit，可以被外部重新赋值
   */
  call?: (type: string, ...args: any[]) => any
  /**
   * 克隆一个 esb 新对象
   * @param context 事件回调默认上下文
   */
  clone: (context?: any) => ESB
  /**
   * 解绑所有事件监听器
   */
  clear: () => void
  /**
   * 触发事件
   * - 如果回调是 async 则等待执行结果，再执行下一个回调
   * - 如果回调返回 false，则返回 false，停止执行后续事件回调
   * @param type 事件类型
   * @param args 事件回调参数
   * @return 返回最后调用的回调返回的结果
   */
  emit: (type: string, ...args: any[]) => any
  /**
   * 解绑事件监听器
   * @param type 事件类型
   * @param callback 事件回调
   * @param context 执行事件回调函数时的上下文
   */
  off: (type: string, callback?: Func | string | object) => any
  /**
   * 绑定事件监听器
   * @param type 事件类型
   * @param callback 事件回调
   * @param context 执行事件回调函数时的上下文
   */
  on: (type: string, callback?: Func | string | object, context?: any) => any
  /**
   * 绑定只执行一次的事件监听器
   */
  once: (type: string, callback?: Func | string | object, context?: any) => any
}

/**
 * 事件服务总线，提供全局事件管理
 */

function EsbFactory(context?: any): ESB {
  let init = () => Object.create(null)
  let esb: ESB = init()
  let map = init()

  Object.assign(esb, {
    bind(context) {
      esb._context = context
    },
    clone: EsbFactory,
    clear() {
      map = init()
    },
    async emit(type, ...args) {
      let res
      let list = map[type]
      if (list) {
        // 防止执行回调时 绑定/解绑 事件导致 list 长度变化
        list = list.slice()
        for (let i = 0, l = list.length; i < l; i++) {
          let context = list[i].context === undefined ? esb._context : list[i].context
          if (context?._isDestroyed) {
            // 标注组件销毁时，msb 已清除所有事件
            // 重复判断？
            errorReport(new Error('组件已销毁，跳过响应事件 ' + type))
            continue
          }
          res = await list[i].callback.apply(context, args)
          if (res === false) {
            // 某个事件回调返回 false，停止执行后续事件回调
            break
          }
        }
      }
      return res
    },
    off(type, callback) {
      let list = map[type]
      if (list) {
        if (callback) {
          // 解绑某个事件
          list.splice(
            list.findIndex(item => item.callback == callback),
            1
          )
        } else {
          // 解绑所有事件
          list.splice(0, list.length)
        }
      }
      if (!list[0]) {
        delete map[type]
      }
    },
    on(type, callback, context) {
      if (typeof callback != 'function') {
        throw new Error(`${type}'s callback is not a function`)
      }
      let list = map[type] || (map[type] = [])
      if (!list.find(item => item.callback == callback)) {
        list.push({
          callback,
          context,
        })
      }
    },
    once(type, callback, context) {
      let wrapper =
        callback._callback_once ||
        (callback._callback_once = function (...args) {
          esb.off(type, wrapper)
          callback.apply(this, args)
        })
      esb.on(type, wrapper, context)
    },
  })

  esb.bind(context)

  return esb
}

export default EsbFactory()
