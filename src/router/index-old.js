/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-18 15:47:52
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-08-26 14:25:54
 * @FilePath: \cloud_web\src\router\index.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
import Vue from 'vue'
import Router from 'vue-router'
import { constantRouterMap } from '@/config/router.config'

// hack router push callback
const originalPush = Router.prototype.push
Router.prototype.push = function push (location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(Router)
export default new Router({
  mode: 'history',
  routes: constantRouterMap
})

// import Vue from 'vue'
// import Router from 'vue-router'

// import { constantRouterMap, asyncRouterMap } from '@/config/router.config'
// Vue.use(Router)
// // console.log(constantRouterMap, '-----')
// console.log(asyncRouterMap, 'asyncRouterMapasyncRouterMapasyncRouterMap')
// export default new Router({
//   mode: 'history',
//   routes: constantRouterMap
// })
