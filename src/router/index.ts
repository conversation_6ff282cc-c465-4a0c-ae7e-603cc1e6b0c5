import { createRouter, createWebHistory } from 'vue-router'
import { constantRouterMap } from '@/config/router.config'

const router = createRouter({
  history: createWebHistory('/'),
  routes: constantRouterMap,
})

let scrollTopMap = {}

function getScrollTag() {
  return document.querySelector('#app .ant-pro-sider + .ant-layout') || document.documentElement
}

router.afterEach(() => {
  setTimeout(() => {
    getScrollTag().scrollTop = scrollTopMap[router.currentRoute.value.fullPath] || 0
  })
})

router.beforeEach((to, from, next) => {
  scrollTopMap[router.currentRoute.value.fullPath] = getScrollTag().scrollTop
  next()
})

export default router
