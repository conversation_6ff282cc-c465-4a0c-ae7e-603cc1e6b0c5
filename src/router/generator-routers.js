import { markRaw } from 'vue'
 
import * as loginService from '@/api/login'
import { TOGGLE_NAV_THEME } from '@/store/mutation-types'

import { getMenus } from '@/api/routePermission/index'
// import { bxAnaalyse } from '@/core/icons'

 
import { BasicLayout, BlankLayout, PageView, RouteView } from '@/layouts'
import MysqlAccountForChangePwd from '@/views/dbManagement/MysqlAccountForChangePwd.vue'

// 前端路由表
const constantRouterComponents = {
  // 基础页面 layout 必须引入
  BasicLayout: BasicLayout,
  BlankLayout: BlankLayout,
  RouteView: RouteView,
  RouterView: RouteView,
  PageView: PageView,
  403: () => import(/* webpackChunkName: "error" */ '@/views/exception/403.vue'),
  404: () => import(/* webpackChunkName: "error" */ '@/views/exception/404.vue'),
  500: () => import(/* webpackChunkName: "error" */ '@/views/exception/500.vue'),

  // 你需要动态引入的页面组件
  // 仪表盘
  Workplace: () => import('@/views/dashboard/Workplace.vue'), // 工作台
  // Analysis: () => import('@/views/dashboard/Analysis.vue'), // 分析页
  Center: () => import('@/views/account/center/index.vue'), // 个人页
  MenuControl: () => import('@/views/dashboard/MenuControl.vue'), // 菜单管理页

  // 弹性计算
  AssetList: () => import('@/views/server/AssetList.vue'), // 服务器
  AssetInfo: () => import('@/views/server/AssetInfo.vue'), // 服务器详情
  AssetImageManage: () => import('@/views/server/AssetImageManage.vue'), // 镜像管理
  CombatPlatform: () => import('@/views/server/CombatPlatform.vue'), // 资产查询
  // 硬件资源
  HostDashboard: () => import('@/views/host/Dashboard.vue'), // 物理机
  HostList: () => import('@/views/host/HostList.vue'), // 物理机
  HostDisk: () => import('@/views/host/HostDisk.vue'), // 磁盘
  HostNetwork: () => import('@/views/host/HostNetwork.vue'), // 网络
  HostSwitch: () => import('@/views/host/HostSwitch.vue'), // 交换机
  DhcpUnused: () => import('@/views/host/DhcpUnused.vue'), // dhcp清理
  AssetCheck: () => import('@/views/host/AssetCheck.vue'), // 容量检测
  // Iseafs 存储系统
  ClusterManage: () => import('@/views/iseafs/clusterManage.vue'), // 集群管理
  FilerManage: () => import('@/views/iseafs/filerManage.vue'), // filer管理
  SetManage: () => import('@/views/iseafs/setManage.vue'), // set管理
  // KMS 密钥管理
  KeysList: () => import('@/views/kms/kesList.vue'), // 密钥管理
  PwdManage: () => import('@/views/kms/PwdManage.vue'), // 密码管理
  VaultPath: () => import('@/views/kms/VaultPath.vue'), // Vault 管理
  CloudAccessKey: () => import('@/views/kms/CloudAccessKey.vue'), // 云厂商AccessKey管理
  CloudPolicy: () => import('@/views/kms/CloudPolicy.vue'), // 云厂商AccessKey管理
  KeycloakUser: () => import('@/views/kms/KeycloakUser.vue'), // KeycloakUser
  KeycloakManage: () => import('@/views/kms/KeycloakManage.vue'), // KeycloakManage
  Keycloak: () => import('@/views/kms/Keycloak.vue'), // Keycloak
  KeycloakActionLog: () => import('@/views/kms/KeycloakActionLog.vue'), // Keycloak

  // 审计模块
  LogQuery: () => import('@/views/audit/logQuery.vue'), // 日志查询
  QxbLogQuery: () => import('@/views/audit/qxbLogQuery.vue'), // 启信宝日志查询
  WafQuery: () => import('@/views/audit/wafQuery.vue'), //
  // 工作流
  CreateWorkflow: () => import('@/views/workflow/CreateWorkflow.vue'), // 新建工单
  MyApplication: () => import('@/views/workflow/MyApplication.vue'), // 我创建的
  MyApproval: () => import('@/views/workflow/MyApproval.vue'), // 我的审批
  WorkflowList: () => import('@/views/workflow/WorkflowList.vue'), // 所有工单
  Process: () => import('@/views/workflow/Process.vue'), // 流程配置
  WorkflowAgent: () => import('@/views/workflow/WorkflowAgent.vue'), // 流程委托
  ServerModifyTag: () => import('@/views/workflow/list/ServerModifyTag.vue'), // 服务器标签变更
  ServerModifyConfig: () => import('@/views/workflow/list/ServerModifyConfig.vue'), // 服务器配置变更
  VmAssetModifyConfig: () => import('@/views/workflow/list/ServerConfigChange.vue'), // x虚拟机配置变更
  ServerDelete: () => import('@/views/workflow/list/ServerDelete.vue'), // 服务器注销
  VmAssetDelete: () => import('@/views/workflow/list/VmAssetDelete.vue'), // 虚拟机注销
  ServerAdd: () => import('@/views/workflow/list/ServerAdd.vue'), // 服务器新增
  ServerCreate: () => import('@/views/workflow/list/ServerCreate.vue'), // 服务器创建
  ServerDbAdd: () => import('@/views/workflow/list/ServerDbAdd.vue'), // 数据库服务器新增
  ServerDbCreate: () => import('@/views/workflow/list/ServerDbCreate.vue'), // 数据库服务器创建
  CloudDesktop: () => import('@/views/workflow/list/CloudDesktop.vue'), // 个人云桌面
  DlpDesktop: () => import('@/views/workflow/list/DlpDesktop.vue'), // DLP云桌面
  DlpDesktopPolicy: () => import('@/views/workflow/list/DlpDesktopPolicy.vue'), // DLP云桌面场景
  ServerDesktop: () => import('@/views/workflow/list/ServerDesktop.vue'), // 服务器云桌面
  DesktopDestroy: () => import('@/views/workflow/list/DesktopDestroy.vue'), // 云桌面注销
  ServerPermissionOperation: () => import('@/views/workflow/list/ServerPermissionOperation.vue'), // 服务器权限操作
  DnsAdd: () => import('@/views/workflow/dns/DnsAdd.vue'), // 域名解析新增
  DnsModify: () => import('@/views/workflow/dns/DnsModify.vue'), // 域名解析更新
  DnsDelete: () => import('@/views/workflow/dns/DnsDelete.vue'), // 域名解析注销
  GatewayStreamRoute: () => import('@/views/workflow/dns/GatewayStreamRoute.vue'), // 网关流量路由配置
  DomainPurchase: () => import('@/views/workflow/dns/DomainPurchase.vue'), // 域名购买
  RemoteDesktop: () => import('@/views/workflow/list/RemoteDesktop.vue'), // 远程办公云桌面
  RemoteDesktopUrl: () => import('@/views/workflow/list/RemoteDesktopUrl.vue'), // 远程办公URL白名单
  GatewayRouteConfig: () => import('@/views/workflow/list/GatewayRouteConfig.vue'), // 网关路由配置
  OpsHelp: () => import('@/views/workflow/list/OpsHelp.vue'), // 运维人工协助
  OpsSource: () => import('@/views/workflow/list/OpsSource.vue'), // 运维资源申请
  CoderAccount: () => import('@/views/workflow/list/CoderAccount.vue'), // Coder账号申请
  CloudIde: () => import('@/views/workflow/list/CloudIde.vue'), // 云IDE申请
  CodeCostDetectionApplication: () => import('@/views/workflow/list/CodeCostDetectionApplication.vue'), // 代码成本检测申请
  BaselineCheckResources: () => import('@/views/workflow/list/BaselineCheckResources.vue'), // 基线检查-资源类
  SecretsManager: () => import('@/views/workflow/list/SecretsManager.vue'), // SecretsManager申请
  Superset: () => import('@/views/workflow/list/Superset.vue'), // Superset权限申请
  BigDataExctraction: () => import('@/views/workflow/bigdata/BigDataExctraction.vue'), // 大数据抽取申请
  BigDataCreateTable: () => import('@/views/workflow/bigdata/BigDataCreateTable.vue'), // 大数据建表修改表申请
  BigdataAssetChange: () => import('@/views/workflow/bigdata/BigdataAssetChange.vue'), // 大数据资产交接申请
  IdsPermission: () => import('@/views/workflow/bigdata/IdsPermission.vue'), // IDS模块权限申请
  SharedTheme: () => import('@/views/workflow/bigdata/sharedTheme.vue'), // 大数据共享主题库
  SDDecryptionToken: () => import('@/views/workflow/bigdata/SDDecryptionToken.vue'), // 敏感数据解密Token权限申请
  SDDecryption: () => import('@/views/workflow/bigdata/SDDecryption.vue'), // 敏感数据解密申请(大数据平台)
  SDUse: () => import('@/views/workflow/bigdata/SDUse.vue'), // 敏感数据使用(运营平台)申请
  DbManipulation: () => import('@/views/workflow/list/DbManipulation.vue'), // 数据操作申请
  DCL: () => import('@/views/workflow/list/DmsDcl.vue'), // Dcl
  BatchDclForMysqlPwdChange: () => import('@/views/workflow/list/BatchDclForMysqlPwdChange.vue'), // Dcl
  UploadDataDb: () => import('@/views/workflow/list/UploadDataDb.vue'), // 上传EXCEL/CSV数据到线上表
  DDL: () => import('@/views/workflow/list/DmsDdl.vue'), // Ddl
  DDLQA: () => import('@/views/workflow/list/DmsDdlQA.vue'),
  DML: () => import('@/views/workflow/list/DmsDml.vue'), // Dml
  DMLQA: () => import('@/views/workflow/list/DmsDmlQA.vue'), // Dml
  DbApproval: () => import('@/views/workflow/list/DbApproval.vue'), // 数据库权限审批表单
  DbSource: () => import('@/views/workflow/list/DbSource.vue'), // 数据库资源获取申请
  CkAuth: () => import('@/views/workflow/list/CkAuth.vue'), // clickhouse查询授权
  CkSource: () => import('@/views/workflow/list/CkSource.vue'), // clickhouse数据导出申请
  CkRestore: () => import('@/views/workflow/list/CkRestore.vue'), // clickhouse数据恢复申请归档
  ConsulDatabase: () => import('@/views/workflow/list/ConsulDatabase.vue'), // Consul 资源创建申请
  ConsulSecret: () => import('@/views/workflow/list/ConsulSecret.vue'), // Consul密钥配置申请
  DbAlter: () => import('@/views/workflow/list/DbAlter.vue'), // 数据库告警平台申请
  BigDataQuery: () => import('@/views/workflow/bigdata/BigDataQuery.vue'), // 大数据表权限导出、查询
  BigDataExport: () => import('@/views/workflow/bigdata/BigDataExport.vue'), // 大数据表权限导出、查询
  CronJobExecute: () => import('@/views/workflow/list/CronJobExecute.vue'), // 作业执行
  ShareStorageCreate: () => import('@/views/workflow/list/ShareStorageCreate.vue'), // 共享云存储创建
  ShareStorageAdd: () => import('@/views/workflow/list/ShareStorageAdd.vue'), // 加入共享云存储
  AppObjectStoragePersonal: () => import('@/views/workflow/list/AppObjectStoragePersonal.vue'), // 对象存储个人版申请
  AliyunRedisSafeGroup: () => import('@/views/workflow/list/AliyunRedisSafeGroup.vue'), // 阿里云Redis安全组白名单申请
  CloudAccountPermissions: () => import('@/views/workflow/list/CloudAccountPermissions.vue'), // 公有云账号权限申请
  RamPersonPermissions: () => import('@/views/workflow/list/RamPersonPermissions.vue'), // 公有云个人账号权限申请
  RamBusinessPermissions: () => import('@/views/workflow/list/RamBusinessPermissions.vue'), // 公有云业务账号权限申请
  RamBusinessAccessVault: () => import('@/views/workflow/list/RamBusinessAccessVault.vue'), // 公有云业务账号权限申请(vault)
  RamPersonPermissionsKeycloak: () => import('@/views/workflow/list/RamPersonPermissionsKeycloak.vue'), // 公有云个人账号权限申请(keycloak)
  JenkinsAuth: () => import('@/views/workflow/list/JenkinsAuth.vue'), // Jenkins权限申请
  JenkinsJob: () => import('@/views/workflow/list/JenkinsJob.vue'), // Jenkins新建Job项目申请
  JenkinsJobDel: () => import('@/views/workflow/list/JenkinsJobDel.vue'), // Jenkins删除Job项目申请
  DataSetDownload: () => import('@/views/workflow/list/DataSetDownload.vue'), // 数据集下载申请
  SgUnbind: () => import('@/views/workflow/list/SgUnbind.vue'), // 安全组解绑申请
  BigdataNet: () => import('@/views/workflow/list/BigdataNet.vue'), // 大数据网络互通申请
  ServerDataDownload: () => import('@/views/workflow/list/ServerDataDownload.vue'), // 服务器数据下载申请
  SecurityGroupRelease: () => import('@/views/workflow/list/SecurityGroupRelease.vue'), // 安全组放开申请
  ServerAppDeploy: () => import('@/views/workflow/list/ServerAppDeploy.vue'), // 服务器应用部署
  SecurityFtpAccount: () => import('@/views/workflow/list/SecurityFtpAccount.vue'), // FTP账号申请
  QxbOpsAuth: () => import('@/views/workflow/list/QxbOpsAuth.vue'), // 启信宝OPS权限申请
  SlurmAuth: () => import('@/views/workflow/list/SlurmAuth.vue'), // Slurm权限申请
  UserRouteApprove: () => import('@/views/workflow/list/UserRouteApprove.vue'), // 用户路由权限申请
  CostLabelChange: () => import('@/views/workflow/list/CostLabelChange.vue'), // 账单标签变更
  CkHighQuota: () => import('@/views/workflow/list/CkHighQuota.vue'), // clickhouse高配查询申请
  SlurmJobCreate: () => import('@/views/workflow/list/SlurmJobCreate.vue'), // Slurm任务创建申请
  GrafanaAuth: () => import('@/views/workflow/list/GrafanaAuth.vue'), // Grafana权限申请
  DomainSslDownload: () => import('@/views/workflow/list/DomainSslDownload.vue'), // 域名SSL证书下载申请
  GpuServiceApply: () => import('@/views/workflow/list/GpuServiceApply.vue'), // GPU服务发布
  GatewayIpBan: () => import('@/views/workflow/list/GatewayIpBan.vue'), // 网关IP黑名单
  CSSyncScan: () => import('@/views/workflow/list/CSSyncScan.vue'), // CS同步库扫描
  AiIdeApply: () => import('@/views/workflow/list/AiIdeApply.vue'), // AI开发IDE申请
  AiIdeGpuQuota: () => import('@/views/workflow/list/AiIdeGpuQuota.vue'), // IDE申请GPU卡配额
  BigdataDownload: () => import('@/views/workflow/list/BigdataDownload.vue'), // 大数据网络互通申请
  RedisFirewallWhite: () => import('@/views/workflow/list/RedisFirewallWhite.vue'), // Redis防火墙白名单
  CostSplitRatio: () => import('@/views/workflow/list/CostSplitRatio.vue'), // 运维费用分摊比例
  ServerDataDownloadDf: () => import('@/views/workflow/list/ServerDataDownloadDf.vue'), // DF取数申请
  OverseasAcceleration: () => import('@/views/workflow/list/OverseasAcceleration.vue'), // 海外项目通信加速申请
  ServerAdminAuth: () => import('@/views/workflow/list/ServerAdminAuth.vue'), // 服务器管理员授权
  ApiKey: () => import('@/views/workflow/list/ApiKey.vue'), // 大模型密钥申请

  // 存储
  ObjectStoragePersonal: () => import('@/views/storage/ObjectStoragePersonal.vue'), // 云存储
  OspShare: () => import('@/views/storage/Share.vue'), // 共享网盘管理
  OspShareOpt: () => import('@/views/storage/shareStorageOpt.vue'), // 共享盘上传下载文件操作
  SoftwareCenter: () => import('@/views/storage/SoftwareCenter.vue'), // 软件中心
  iFile: () => import('@/views/storage/iFile.vue'), // iFile
  StorageConsole: () => import('@/views/storage/StorageConsole.vue'), // 存储控制台

  // 数据库
  MysqlPersonal: () => import('@/views/db/MysqlPersonal.vue'), // 'Mysql(个人版)'
  Mysql: () => import('@/views/db/Mysql.vue'), // Mysql
  Redis: () => import('@/views/db/Redis.vue'), // Redis
  GlobalTable: () => import('@/views/db/GlobalTable.vue'), // 全球表
  ClickHouse: () => import('@/views/db/ClickHouse.vue'), // ClickHouse 数据库查询
  DMS: () => import('@/views/db/Dms.vue'), // DMS
  SafeGateway: () => import('@/views/db/SafeGateway.vue'), // 数据库安全网关
  MysqlStruct: () => import('@/views/db/MysqlStruct.vue'), // 数据字典
  AlterPlate: () => import('@/views/db/DbAlterPlate.vue'), // 数据库告警平台
  DbComment: () => import('@/views/db/DbComment.vue'), // 数据库字段Comment信息
  MysqlTemplate: () => import('@/views/db/MysqlTemplate.vue'), // MySQL参数模板
  RedisTemplate: () => import('@/views/db/RedisTemplate.vue'), // Redis参数模板
  MysqlInfo: () => import('@/views/db/MysqlInfo.vue'), // MySQL详情
  RedisInfo: () => import('@/views/db/RedisInfo.vue'), // Redis详情
  ServerMonitor: () => import('@/views/db/modules/serverMonitor/index.vue'), // 服务器数据监控
  DbContact: () => import('@/views/db/DbContact.vue'), // 数据库联系人

  // 数据分析
  clickVisual: () => import('@/views/analysis/ClickVisual.vue'), //
  // 费用
  DailyCost: () => import('@/views/cost/DailyCost.vue'), // 日账单
  MonthlyCost: () => import('@/views/cost/MonthlyCost.vue'), // 月账单
  LabelCost: () => import('@/views/cost/LabelCost.vue'), // 账单标签
  Bill: () => import('@/views/cost/Bill.vue'), // 账单
  SplitRatio: () => import('@/views/cost/SplitRatio.vue'), // 分摊比例
  UcloudHybirdPrice: () => import('@/views/cost/UcloudHybirdPrice.vue'), // Ucloud混合云价格
  ExchangeRate: () => import('@/views/cost/ExchangeRate.vue'), // 汇率表（美元）
  StatisticalCost: () => import('@/views/cost/StatisticalCost.vue'), // 账单统计
  Contract: () => import('@/views/cost/Contract.vue'), // 合同管理
  SplitRatioProduct: () => import('@/views/cost/SplitRatioProduct.vue'), // 产品段分摊比例
  Budget: () => import('@/views/cost/Budget.vue'), // 预算
  LowUsageRate: () => import('@/views/cost/LowUsageRate.vue'), // 预算
  SplitOcrGpu: () => import('@/views/cost/SplitOcrGpu.vue'), // OCR GPU 分摊比例
  FixedAssets: () => import('@/views/cost/FixedAssets.vue'), // 固定资产
  CostEstimation: () => import('@/views/cost/CostEstimation.vue'), // 费用预估
  BusinessOperations: () => import('@/views/cost/BusinessOperations.vue'), // 业务运营

  SmsSupplier: () => import('@/views/cost/SmsSupplier.vue'), // 短信费用 -- 平台架构录入
  BusinessData: () => import('@/views/cost/BusinessData.vue'), // 费用分析
  GpuAsset: () => import('@/views/cost/GpuAsset.vue'), // GPU固定资产
  // 监控
  //
  // 大数据
  //
  // 网络与CDN
  ApiGateway: () => import('@/views/network/ApiGateway.vue'), // API网关
  Cdn: () => import('@/views/network/Cdn.vue'), // CDN
  EIP: () => import('@/views/network/Eip.vue'), // Eip
  VIP: () => import('@/views/network/Vip.vue'), // Vip
  Lb: () => import('@/views/network/Lb.vue'), // 负载均衡
  Ip: () => import('@/views/network/Ip.vue'), // IP管理
  ExportIp: () => import('@/views/network/ExportIp.vue'), // 出口IP
  Vlan: () => import('@/views/network/Vlan.vue'), // Vlan
  PrivateLine: () => import('@/views/monitor/PrivateLine.vue'), // Vlan

  // 域名
  Domain: () => import('@/views/domain/Domain.vue'), // 域名
  DNS: () => import('@/views/domain/DNS.vue'), // 域名解析
  SSL: () => import('@/views/domain/SSL.vue'), // SSL证书
  ICP: () => import('@/views/domain/ICP.vue'), // 域名备案
  DNSBackup: () => import('@/views/domain/DNSBackup.vue'), // 域名解析备份
  // 安全
  JumpServer: () => import('@/views/security/JumpServer.vue'), // 用户账号信息
  AccountInfo: () => import('@/views/security/AccountInfo.vue'), // 用户账号信息
  BaselineCheck: () => import('@/views/security/BaselineCheck.vue'), // 基线检查
  Ram: () => import('@/views/security/Ram.vue'), // Ram访问控制
  Jms: () => import('@/views/security/Jms.vue'), // 堡垒机授权记录
  SupplierUser: () => import('@/views/security/SupplierUser.vue'), // 供应商RAM用户
  SupplierUserRecord: () => import('@/views/security/SupplierUserRecord.vue'), // 供应商RAM用户操作记录
  FtpAccount: () => import('@/views/security/FtpAccount.vue'), // FTP账号管理
  UfileToken: () => import('@/views/security/UfileToken.vue'), // Ufile Token管理
  JmsUser: () => import('@/views/security/JmsUser.vue'), // 堡垒机用户管理
  GitlabPP: () => import('@/views/security/GitlabPP.vue'), // GITLAB公开项目
  UserAsset: () => import('@/views/security/UserAsset.vue'), // 零信任插件用户资产
  SensitiveAsset: () => import('@/views/security/SensitiveAsset.vue'), // 集权资产列表
  JmsBaselineCheck: () => import('@/views/security/JmsBaselineCheck.vue'), // 堡垒机基线检查
  SecurityGroupRecode: () => import('@/views/security/SecurityGroupRecode.vue'), // 安全组放开记录
  SecurityBaseScan: () => import('@/views/security/SecurityBaseScan.vue'), // 安全基线扫描
  SecurityPortForward: () => import('@/views/security/SecurityPortForward.vue'), // 端口映射

  // 开发与运维
  Coder: () => import('@/views/devops/Coder.vue'), // 云端开发工具
  CodeCostDetection: () => import('@/views/devops/CodeCostDetection.vue'), // 代码成本检测
  IDE: () => import('@/views/devops/Ide.vue'), // 云IDE
  IdeManage: () => import('@/views/devops/IdeManage.vue'), // 云IDE管理
  // Terminal: () => import('@/views/devops/Terminal.vue'), // 云终端
  Terminal: () => import('@/views/devops/TerminalIndex.vue'), // 云终端
  TerminalManager: () => import('@/views/devops/TerminalManager.vue'), // 云终端管理
  // WXTerminal: () => import('@/views/devops/WxTermIndexPage.vue'), // 企微应用版云终端
  WXTerminal: () => import('@/views/devops/WxTerminal.vue'), // 企微应用版云终端
  CloudChromeExtension: () => import('@/views/devops/CloudChromeExtension.vue'), // 云chrome插件开通下载
  cloudGPT: () => import('@/views/devops/cloudGPT.vue'), // cloudGPT

  // 个人页
  // center: () => import('@/views/account/center/index.vue'), // 个人页

  // 权限管理
  UserManager: () => import('@/views/permission/UserManager.vue'), // 用户管理
  RoleManager: () => import('@/views/permission/RoleManager.vue'), // 角色管理
  PolicyManager: () => import('@/views/permission/PolicyManager.vue'), // 策略管理
  PolicyBinding: () => import('@/views/permission/PolicyBinding.vue'), // 权限绑定
  routeConfig: () => import('@/views/permission/routeConfig.vue'), // 模块管理
  CloudRequestLogManager: () => import('@/views/permission/CloudRequestLogManager.vue'), // 访问日志
  sensitiveManager: () => import('@/views/permission/sensitiveManager.vue'), // 敏感权限管理

  // Api网关
  gatewayEnv: () => import('@/views/gateway/gatewayEnv.vue'), // 集群节点管理
  upStream: () => import('@/views/gateway/upStream/index.vue'), // 集群节点管理
  apiRoutes: () => import('@/views/gateway/apiRoute/index.vue'), // api路由
  GatewaySSL: () => import('@/views/gateway/SSL.vue'), // 网关证书
  GwConsumer: () => import('@/views/gateway/Consumer.vue'), // 网关消费者
  GlobalRule: () => import('@/views/gateway/GlobalRule.vue'), // 全局规则管理
  RouteRecord: () => import('@/views/gateway/RouteRecord.vue'), // route 操作记录
  StreamRoute: () => import('@/views/gateway/StreamRoute.vue'), // stream route 管理
  // list
  // account
  // 'TestWork': () => import(/* webpackChunkName: "TestWork" */ '@/views/dashboard/TestWork.vue')

  // Consul管理模块
  OnlineConsulDb: () => import('@/views/consul/OnlineConsulDb.vue'), // 线上数据库管理
  OnlineConsulRecord: () => import('@/views/consul/OnlineConsulRecord.vue'), // 线上数据库管理操作记录
  TestConsulDb: () => import('@/views/consul/TestConsulDb.vue'), // 测试数据库管理
  TestConsulRecord: () => import('@/views/consul/TestConsulRecord.vue'), // 测试数据库管理操作记录
  OnlineConsulST: () => import('@/views/consul/OnlineConsulSecret.vue'), // 线上密钥管理
  OnlineConsulSTR: () => import('@/views/consul/OnlineConsulSecretRecord.vue'), // 线上密钥管理操作记录
  TestConsulST: () => import('@/views/consul/TestConsulSecret.vue'), // 测试密钥管理管理
  TestConsulSTR: () => import('@/views/consul/TestConsulSecretRecord.vue'), // 测试密钥管理管理操作记录
  Discovery: () => import('@/views/consul/Discovery.vue'), // 服务注册中心
  ConsoleManage: () => import('@/views/consul/manage/console.vue'), // Consul控制台
  // 云桌面
  Desktop: () => import('@/views/desktop/Desktop.vue'), // 云桌面
  DesktopOffline: () => import('@/views/desktop/DesktopOffline.vue'), // 云桌面
  DesktopOnline: () => import('@/views/desktop/DesktopOnline.vue'), // 云桌面
  DesktopManage: () => import('@/views/desktop/DesktopManage.vue'), // 云桌面
  RemoteWork: () => import('@/views/desktop/RemoteWork.vue'), // 远程办公云桌面
  WXRemoteDesktop: () => import('@/views/desktop/WXRemoteWork.vue'), // 微信应用远程办公云桌面
  // 作业平台
  CronTaskConfig: () => import('@/views/cron/CronTaskConfig.vue'), // 定时任务配置
  CronTaskResult: () => import('@/views/cron/CronTaskResult.vue'), // 定时任务执行历史
  CronScheduleExecution: () => import('@/views/cron/CronScheduleExecution.vue'), // 作业执行历史
  CronScheduleTask: () => import('@/views/cron/CronScheduleTask.vue'), // 任务配置中心
  CronScheduleJob: () => import('@/views/cron/CronScheduleJob.vue'), // 作业配置中心

  // DRMS
  DrmsBackup: () => import('@/views/drms/DrmsBackup.vue'), // 备份资产清单
  Backup: () => import('@/views/drms/Backup.vue'), // 数据备份管理
  ImageBackup: () => import('@/views/drms/ImageBackup.vue'), // 镜像备份管理
  CodeBackup: () => import('@/views/drms/CodeBackup.vue'), // 代码仓库备份管理

  // 数据库管理
  MysqlAccount: () => import('@/views/dbManagement/MysqlAccount.vue'), // Mysql账号管理
  MysqlAccountForChangePwd: () => import('@/views/dbManagement/MysqlAccountForChangePwd.vue'), // Mysql账号管理
  MasterSlave: () => import('@/views/dbManagement/MasterSlave.vue'), // Mysql主从对应关系
  MysqlInit: () => import('@/views/dbManagement/MysqlInit.vue'), // Mysql初始化
  ClickhouseAsset: () => import('@/views/dbManagement/ClickhouseAsset.vue'), // Clickhouse资产管理
  MysqlAsset: () => import('@/views/dbManagement/MysqlAsset.vue'), // Mysql资产管理
  MysqlRiskAccount: () => import('@/views/dbManagement/MysqlRiskAccount.vue'), // Mysql风险账号管理
  RedisAliyun: () => import('@/views/dbManagement/RedisAliyun.vue'), // redis阿里云管理
  RegUrl: () => import('@/views/dbManagement/RegUrl.vue'), // url正则表达式
  NoManagementDatabase: () => import('@/views/dbManagement/NoManagementDatabase.vue'), // 未管理数据库

  // 监控
  PrometheusRule: () => import('@/views/monitor/PrometheusRule.vue'), // 监控策略
  PrometheusExporter: () => import('@/views/monitor/PrometheusExporter.vue'), // 监控nodeExporter节点
  PrometheusApi: () => import('@/views/monitor/PrometheusApi.vue'), // 监控api
  Alertmanager: () => import('@/views/monitor/Alertmanager.vue'), // 告警管理

  // 中间件
  MiddleWare: () => import('@/views/middleware/icron/autom/index.vue'), // 中间件
  MiddleWarePage: () => import('@/views/middleware/middleWareEntry.vue'), // 中间件导航页
  IcronIndexPage: () => import('@/views/middleware/icron/indexPage.vue'), // icron导航页
  IrmqIndexPage: () => import('@/views/middleware/irmq/indexPage.vue'), //  Irmq导航页
  LogAlertIndexPage: () => import('@/views/middleware/logAlert/indexPage.vue'), // LogAlert导航页
  MiddleWareConfig: () => import('@/views/middleware/middleWareConfig.vue'), // 中间件配置页
  // 算法
  DataSet: () => import('@/views/ai/DataSet.vue'), // 数据集管理
  SlurmNode: () => import('@/views/ai/SlurmNode.vue'), // Slurm节点管理
  SlurmUser: () => import('@/views/ai/SlurmUser.vue'), // Slurm用户管理
  SlurmGroup: () => import('@/views/ai/SlurmGroup.vue'), // Slurm用户组管理
  SlurmJob: () => import('@/views/ai/SlurmJob.vue'), // Slurm任务管理
  SlurmJobReport: () => import('@/views/ai/SlurmJobReport.vue'), // Slurm报表
  SlurmNodeHealthy: () => import('@/views/ai/SlurmNodeHealthy.vue'), // Slurm节点检测
  SlurmReport: () => import('@/views/ai/SlurmReport.vue'), // slurm报表
  AiIde: () => import('@/views/ai/AiIde.vue'), // AI开发IDE
  AiIdeManage: () => import('@/views/ai/AiIdeManage.vue'), // AI开发IDE管理
  WxIde: () => import('@/views/ai/WxIde.vue'), // 企微应用版云IDE
  AiTrainingNode: () => import('@/views/ai/AiTrainingNode.vue'), // AI训练固定资产
  SlurmPrice: () => import('@/views/ai/SlurmPrice.vue'), // Slurm定价
  SlurmCostMonth: () => import('@/views/ai/SlurmCostMonth.vue'), // Slurm费用
  SlurmJuiceSummary: () => import('@/views/ai/SlurmJuiceSummary.vue'), // Slurm节点管理
  // 大模型
  LlmApiKey: () => import('@/views/llm/LlmApiKey.vue'), // API_keys页面
  KeyManage: () => import('@/views/llm/KeyManage.vue'), // 密钥管理
  AnalysisDashboard: () => import('@/views/llm/AnalysisDashboard.vue'), // 调用分析
  // 负载均衡
  ClbIp: () => import('@/views/clb/ClbIp.vue'), // 公网IP管理
  Cluster: () => import('@/views/clb/Cluster.vue'), // 集群管理
  ClbInstance: () => import('@/views/clb/ClbInstance.vue'), // 实例管理
  ClbListen: () => import('@/views/clb/ClbListen.vue'), // 监听器管理
  ClbLogs: () => import('@/views/clb/Logs.vue'), // 日志管理
  ActivatedRateCs: () => import('@/views/sms/smsActivatedRateCs.vue'),
  ActivatedRateCc: () => import('@/views/sms/smsActivatedRateCc.vue'),
  DeliveredRate: () => import('@/views/sms/smsDeliveredRate.vue'),
  ArrivalDelay: () => import('@/views/sms/smsArrivalDelay.vue'),
  FlowLogMap: () => import('@/views/network/FlowLogMap.vue'),
  VlanPlan: () => import('@/views/network/VlanPlan.vue'),
  MongodbBackup: () => import('@/views/db/MongodbBackup.vue'),
  TidbBackup: () => import('@/views/db/TidbBackup.vue'),
  Mongodb: () => import('@/views/dbManagement/Mogodb.vue'),
  CloudflareDataTransfer: () => import('@/views/network/cloudflareDataTransfer.vue'),
  SecurityGroup: () => import('@/views/server/SecurityGroup.vue'),
}

// 前端未找到页面路由（固定不用改）
const notFoundRouter = {
  component: constantRouterComponents['404'],
  // path: '*',
  path: '/:pathMatch(.*)',
  // redirect: '/404',
  hidden: true,
}

// 根级菜单
const rootRouter = {
  key: '',
  name: '',
  path: '/',
  component: 'BasicLayout',
  redirect: '/dashboard/workplace',
  meta: {
    title: '首页',
  },
  children: [],
  webTheme: 'dark',
}
/**
 * 动态生成菜单
 * @param token
 * @returns {Promise<Router>}
 */
export const generatorDynamicRouter = token => {
  return new Promise((resolve, reject) => {
    getMenus(token)
      .then(res => {
        localStorage.setItem('menus', JSON.stringify(res.routers))
        const result = res.routers
        const menuNav = []
        // const childrenNav = []
        //      后端数据, 根级树数组,  根级 PID
        // listToTree(result, childrenNav, 0)
        rootRouter.children = result
        noc.store.setItem(TOGGLE_NAV_THEME, res.webTheme)
        menuNav.push(rootRouter)
        // menuNav.push(a)
        // debugger
        const routers = generator(menuNav)
        routers.push(notFoundRouter)
        // debugger
        resolve(routers)
      })
      .catch(err => {
        reject(err)
      })
  })
}

/**
 * 格式化树形结构数据 生成 vue-router 层级路由表
 *
 * @param routerMap
 * @param parent
 * @returns {*}
 */
export const generator = (routerMap, parent) => {
  // const keepAliveRoute = ['我创建的', '我的审批', '所有工单',"工作流",'个人云桌面']
  return routerMap.map(item => {
    // const { title, show, hideChildren, hiddenHeaderContent, target, icon } = item.meta || {}
    if (item.children.length === 0) {
      delete item.children
    }
    let component = constantRouterComponents[item.component || item.key]
    if (component) {
      component = markRaw(component)
    }
    const currentRouter = {
      // 如果路由设置了 path，则作为默认 path，否则 路由地址 动态拼接生成如 /dashboard/workplace
      // path: item.path || `${(parent && parent.path) || ''}/${item.key}`,
      // 路由名称，建议唯一
      name: item.name || item.key || '',
      // 该路由对应页面的 组件 :方案1
      // component:1 constantRouterComponents[item.component || item.key],
      // 该路由对应页面的 组件 :方案2 (动态加载)
      component,
      hidden: item.hidden || false,
      // component: item.component,
      // meta: 页面标题, 菜单图标, 页面权限(供指令权限用，可去掉)
      meta: {
        title: item.name,
        icon: item.icon === 'bxAnaalyse' ? 'dashboard' : item.icon || undefined,
        hiddenHeaderContent: item.hiddenHeaderContent || false,
        permission: item.name,
        // keepAlive:item.path.includes('/workflow/')? true : false,
      },
      redirect: item.redirect,
    }
    if (item.path) {
      if (item.path.includes('http')) {
        currentRouter.path = 'http' + item.path.split('http')[1]
        delete currentRouter.component
        currentRouter.meta.target = '_blank'
      } else {
        currentRouter.path = item.path || `${(parent && parent.path) || ''}/${item.key}`
      }
    }
    // 是否设置了隐藏菜单
    if (item.show === false) {
      currentRouter.hidden = true
    }
    // 是否设置了隐藏子菜单
    if (item.hideChildren) {
      currentRouter.hideChildrenInMenu = true
    }
    // 为了防止出现后端返回结果不规范，处理有可能出现拼接出两个 反斜杠
    if (!currentRouter.path.startsWith('http')) {
      currentRouter.path = currentRouter.path.replace('//', '/')
    }
    // 重定向
    item.redirect && (currentRouter.redirect = item.redirect)
    // 是否有子菜单，并递归处理
    if (item.children && item.children.length > 0) {
      // Recursion
      currentRouter.children = generator(item.children, currentRouter)
    }
    return currentRouter
  })
}

/**
 * 数组转树形结构
 * @param list 源数组
 * @param tree 树
 * @param parentId 父ID
 */
// const listToTree = (list, tree, parentId) => {
//   list.forEach(item => {
//     // 判断是否为父级菜单
//     if (item.parentId === parentId) {
//       const child = {
//         ...item,
//         key: item.key || item.name,
//         children: []
//       }
//       // 迭代 list， 找到当前菜单相符合的所有子菜单
//       listToTree(list, child.children, item.id)
//       // 删掉不存在 children 值的属性
//       if (child.children.length <= 0) {
//         delete child.children
//       }
//       // 加入到树中
//       tree.push(child)
//     }
//   })
// }
