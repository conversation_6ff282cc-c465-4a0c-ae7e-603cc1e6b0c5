import { pageProgress } from '@aim/helper/x/progress'
import '@/components/NProgress/nprogress.less'
import router from './router'
import store from './store'
import { i18nRender } from '@/locales'
import { removeToken, setToken } from '@/utils/auth'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'

pageProgress.config({ showSpinner: false })

let isAuthReady: boolean

/**
 * 无需登录即可访问的网页
 */
const ignoreLoginList = [
  //
  '/',
  '/homepage',
]

function nextLoggedIn(to, from, next) {
  store
    .dispatch('GetInfo')
    .then(res => {
      noc.user.setUserInfo(res.Data)
      store.commit('SET_NAME', res.Data)
      return store.dispatch('GenerateRoutes', {})
    })
    .then(() => {
      /**
       * 根据权限生成可访问的路由表，动态添加可访问路由表
       */
      store.getters.addRouters.forEach(item => {
        router.addRoute(item)
      })
      next({
        ...to,
        replace: true,
      })
    })
    .catch(error => {
      noc.message.error(String(error))
      removeToken()
    })
}

function nextNotLoggedIn(to, from, next) {
  if (to.path == '/') {
    pageProgress.done()
    /**
     * 跳转 / -> /homepage
     * TODO，优化路由，去掉 /homepage
     */
    next('/homepage')
  } else if (ignoreLoginList.includes(to.path)) {
    /**
     * 无需登录即可访问的页面
     */
    next()
  } else {
    /**
     * 跳转到 SSO 登录
     */
    noc.sso.redirect()
  }
}

router.beforeEach((to, from, next) => {
  pageProgress.begin()

  let urlToken = noc.url.get('token')
  let userToken = noc.user.getToken()

  if (urlToken) {
    /**
     * SSO 登录跳回
     */
    setToken(urlToken)
    store
      .dispatch('Login')
      .then(() => {
        let redirect = noc.url.getRedirect()
        if (redirect) {
          /**
           * 网址中存在安全的 redirect 参数，则跳转到该网址
           */
        } else {
          /**
           * 删除 SSO 登录跳回网址中的 token 参数
           */
          redirect = noc.url.set('token', '')
          /**
           * 跳转 /homepage -> /dashboard/workplace
           */
          redirect = redirect.replace(/([.:]\w+)\/homepage/, '$1/dashboard/workplace')
        }
        if (noc.user.isLogin()) {
          location.replace(redirect)
        } else {
          noc.message.error('登录状态设置失败')
        }
      })
      .catch(error => {
        let message = error?.message
        if (message) {
          noc.message.error([error.request?.responseURL, error.message].join(' '))
        }
        pageProgress.done()
      })
  } else if (userToken) {
    /**
     * 用户已登录
     */
    if (isAuthReady) {
      next()
    } else {
      /**
       * 初始化与登录相关的 store；此处可以获取用户权限、初始化菜单列表等等
       */
      isAuthReady = true
      nextLoggedIn(to, from, next)
    }
  } else {
    /**
     * 用户未登录
     */
    nextNotLoggedIn(to, from, next)
  }

  let global = globalThis as any
  let name = noc.user.getUserInfo().name
  if (name) {
    global.zhuge.identify(name)
  }
})

router.afterEach(to => {
  pageProgress.done()
  if (to.meta && to.meta.title) {
    setDocumentTitle(`${i18nRender(to.meta.title)} - ${domTitle}`)
  }
})
