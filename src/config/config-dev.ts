/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-10-30 14:10:50
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-01-31 14:38:27
 * @FilePath: \cloud_web\src\config\config-dev.ts
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
/**
 * 开发环境配置
 */
import { AppConfigType } from './types'
import configONLINE from './config-online'

let configDEV = {
  API_CLOUD_BASE: '/api',
  API_STORAGE_BASE: '/ospstorage',
  SSO_PLATFORM_ID: 'jI00FeL6g5drNeHVkrAvenkNR0DWbNBw',
  SSO_WEB_API: 'https://webapi-sso-sandbox.intsig.net',
  CLOUD_TERM_KEY: 'TerminalCloudKey',
  BIG_DATA_API: 'https://bigdata-test.intsig.net'
} as AppConfigType

let useOnlineApi

/**
 * 开发环境调用线上接口时打开注释
 * SSO 目前线上不支持跳转回 localhost，仍需要后端支持使用账号和密码登录（不止是跳转到 SSO 登录）才能实现该功能
 */
// useOnlineApi = true

if (useOnlineApi) {
  configDEV = configONLINE
  configDEV.ENV_FLAG = 'ONLINE'
}

export default configDEV
