/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-10 14:14:07
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-01-31 14:54:06
 * @FilePath: \cloud_web\src\config\index.ts
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { configLoad, envResolve } from '@aim/helper'
import { AppConfigType } from './types'

import dev from './config-dev'
import test from './config-test'
import pre from './config-pre'
import online from './config-online'

const config = configLoad({ dev, test, pre, online }) as AppConfigType

if (!config.ENV_FLAG) {
  config.ENV_FLAG = envResolve()
}

if (!config.SSO_EXPIRY_SECOND) {
  /**
   * 登录有效期，7天
   */
  config.SSO_EXPIRY_SECOND = 604800
}

export function getApiCloudBase(): string {
  return config.API_CLOUD_BASE
}

export function getApiStorageBase(): string {
  return config.API_STORAGE_BASE
}

export function getEnvFlag(): string {
  return config.ENV_FLAG
}

export function getSsoExpirySecond(): number {
  return config.SSO_EXPIRY_SECOND
}

export function getSsoPlatformId(): string {
  return config.SSO_PLATFORM_ID
}

export function getSsoWebApi(): string {
  return config.SSO_WEB_API
}
export function getBigDataApi(): string {
  return config.BIG_DATA_API
}
export function getCloudTermKey(): string {
  return config.CLOUD_TERM_KEY
}

export default config
