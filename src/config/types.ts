/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-10-30 14:10:50
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-01-31 14:55:41
 * @FilePath: \cloud_web\src\config\types.ts
 * @Description:  
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
export type AppConfigType = {
  /**
   * 云平台 API 接口基址
   */
  API_CLOUD_BASE: string
  /**
   * 存储 API 接口基址
   */
  API_STORAGE_BASE: string
  /**
   * 环境标识，可以用于在开发环境调用线上接口
   */
  ENV_FLAG?: 'DEV' | 'TEST' | 'PRE' | 'ONLINE'
  /**
   * SSO 登录状态有效期，单位秒
   */
  SSO_EXPIRY_SECOND?: number
  /**
   * SSO 平台标识
   */
  SSO_PLATFORM_ID: string
  /**
   * SSO API 地址
   */
  SSO_WEB_API: string
  /**
   * CLOUD TERM 加密密钥
   */
  CLOUD_TERM_KEY: string
  /**
   * bigdata api
   */
  BIG_DATA_API: string
}
