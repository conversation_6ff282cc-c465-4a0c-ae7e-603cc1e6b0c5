// eslint-disable-next-line
import { UserLayout, BasicLayout, BlankLayout } from '@/layouts'
// import { bxAnaalyse } from '@/core/icons'
// import { component } from 'node_modules/vue/types/umd'

// const RouteView = {
//   name: 'RouteView',
//   render: h => h('router-view')
// }

export const asyncRouterMap = [
  // {
  //   path: '/',
  //   name: 'index',
  //   component: BasicLayout,
  //   meta: { title: 'menu.home' },
  //   redirect: '/dashboard/workplace',
  //   children: [
  //     // dashboard
  //     {
  //       path: '/dashboard',
  //       name: '仪表盘',
  //       redirect: '/dashboard/workplace',
  //       component: RouteView,
  //       meta: { title: 'menu.dashboard', keepAlive: true, icon: bxAnaalyse, permission: ['dashboard'] },
  //       children: [
  //         {
  //           path: '/dashboard/workplace',
  //           name: '工作台',
  //           component: () => import('@/views/dashboard/Workplace'),
  //           meta: { title: 'menu.dashboard.workplace', keepAlive: true, permission: ['dashboard'] }
  //         },
  //         {
  //           path: '/dashboard/analysis/:pageNo([1-9]\\d*)?',
  //           name: '分析页',
  //           component: () => import('@/views/dashboard/Analysis'),
  //           meta: { title: 'menu.dashboard.analysis', keepAlive: false, permission: ['dashboard'] }
  //         }
  //       ]
  //     },
  //     // list
  //     {
  //       path: '/server',
  //       name: '弹性计算',
  //       component: RouteView,
  //       redirect: '/server/asset-list',
  //       meta: { title: 'menu.server', icon: 'cloud-server', permission: ['server'] },
  //       children: [
  //         {
  //           path: '/server/asset-list',
  //           name: '服务器',
  //           component: () => import('@/views/list/AssetList'),
  //           meta: { title: 'menu.server.asset-list', keepAlive: true, permission: ['server'] }
  //         },
  //         {
  //           path: '/server/asset-info',
  //           name: '服务器详情',
  //           component: () => import('@/views/list/AssetInfo'),
  //           hidden: true,
  //           meta: { title: 'menu.server.asset-info', keepAlive: true, permission: ['server'] }
  //         },
  //         {
  //           path: '/server/desktop',
  //           name: '云桌面',
  //           component: () => import('@/views/list/Desktop'),
  //           meta: { title: 'menu.server.desktop', keepAlive: true, permission: ['server'] }
  //         },
  //         {
  //           path: 'http://192.168.28.121/',
  //           name: '函数计算',
  //           meta: { title: 'menu.server.lambda', target: '_blank' }
  //         }
  //       ]
  //     },
  //     // workflow
  //     {
  //       path: '/workflow',
  //       name: '工作流',
  //       component: RouteView,
  //       redirect: '/workflow/my-application',
  //       meta: { title: 'menu.workflow', icon: 'solution', permission: ['workflow'] },
  //       children: [
  //         {
  //           path: '/workflow/my-application',
  //           name: '我创建的',
  //           component: () => import('@/views/workflow/MyApplication'),
  //           meta: { title: 'menu.workflow.my-application', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/my-approval/:pageNo([1-9]\\d*)?',
  //           name: '我的审批',
  //           component: () => import('@/views/workflow/MyApproval'),
  //           meta: { title: 'menu.workflow.my-approval', keepAlive: true, permission: ['workflow'] }
  //         },
  //         // {
  //         //   path: '/workflow/workflow-list/:pageNo([1-9]\\d*)?',
  //         //   name: '所有工单',
  //         //   component: () => import('@/views/workflow/WorkflowList'),
  //         //   meta: { title: 'menu.workflow.workflow-list', keepAlive: true, permission: ['workflow'] }
  //         // },
  //         {
  //           path: '/workflow/server-modify-tag',
  //           name: '服务器标签变更',
  //           component: () => import('@/views/workflow/list/ServerModifyTag'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.server-modify-tag', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/server-modify-config',
  //           name: '服务器配置变更',
  //           component: () => import('@/views/workflow/list/ServerModifyConfig'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.server-modify-config', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/server-del',
  //           name: '服务器注销',
  //           component: () => import('@/views/workflow/list/ServerDelete'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.server-del', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/server-add',
  //           name: '服务器新增',
  //           component: () => import('@/views/workflow/list/ServerAdd'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.server-add', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/dns-add',
  //           name: 'DnsAdd',
  //           component: () => import('@/views/workflow/dns/DnsAdd'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.dns-add', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/dns-edit',
  //           name: 'DnsModify',
  //           component: () => import('@/views/workflow/dns/DnsModify'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.dns-edit', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/dns-del',
  //           name: 'DnsDel',
  //           component: () => import('@/views/workflow/dns/DnsDelete'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.dns-del', keepAlive: true, permission: ['workflow'] }
  //         },
  //         {
  //           path: '/workflow/ops-help',
  //           name: 'OpsHelp',
  //           component: () => import('@/views/workflow/list/OpsHelp'),
  //           hidden: true,
  //           meta: { title: 'menu.workflow.ops-help', keepAlive: true, permission: ['workflow'] }
  //         }
  //       ]
  //     },
  //     // storage
  //     {
  //       path: '/storage',
  //       component: RouteView,
  //       redirect: '/storage/object-storage-personal',
  //       name: '存储',
  //       meta: { title: 'menu.storage', icon: 'folder-open', keepAlive: true, permission: ['storage'] },
  //       children: [
  //         {
  //           path: '/storage/object-storage-personal',
  //           name: '对象存储(个人版)',
  //           component: () => import('@/views/storage/ObjectStoragePersonal'),
  //           meta: { title: 'menu.storage.object-storage-personal', keepAlive: true, permission: ['storage'] }
  //         }
  //       ]
  //     },
  //     // db
  //     {
  //       path: '/db',
  //       name: '数据库',
  //       component: RouteView,
  //       redirect: '/db/dms',
  //       meta: { title: 'menu.db', icon: 'database', permission: ['db'] },
  //       children: [
  //         {
  //           path: '/db/mysql-personal',
  //           name: 'MysqlPersonal',
  //           component: () => import('@/views/db/MysqlPersonal'),
  //           meta: { title: 'menu.db.mysql-personal', keepAlive: true, permission: ['db'] }
  //         },
  //         {
  //           path: '/db/mysql',
  //           name: 'Mysql',
  //           component: () => import('@/views/db/Mysql'),
  //           meta: { title: 'menu.db.mysql', keepAlive: true, permission: ['db'] }
  //         },
  //         {
  //           path: '/db/redis',
  //           name: 'Redis',
  //           component: () => import('@/views/db/Redis'),
  //           meta: { title: 'menu.db.redis', keepAlive: true, permission: ['db'] }
  //         },
  //         {
  //           path: '/db/global-table',
  //           name: '全球表',
  //           component: () => import('@/views/db/GlobalTable'),
  //           meta: { title: 'menu.db.global-table', keepAlive: true, permission: ['db'] }
  //         },
  //         {
  //           path: '/db/click-house',
  //           name: 'ClickHouse',
  //           component: () => import('@/views/db/ClickHouse'),
  //           meta: { title: 'menu.db.click-house', keepAlive: true, permission: ['db'] }
  //         },
  //         {
  //           path: '/db/dms',
  //           name: 'DMS',
  //           component: () => import('@/views/db/Dms'),
  //           meta: { title: 'menu.db.dms', keepAlive: true, permission: ['db'] }
  //         }
  //       ]
  //     },
  //     // data analysis
  //     {
  //       path: '/analysis',
  //       name: '数据分析',
  //       component: RouteView,
  //       redirect: '/analysis/grafana',
  //       meta: { title: 'menu.analysis', icon: 'line-chart', permission: ['analysis'] },
  //       children: [
  //         {
  //           path: 'https://grafana-autom.intsig.net',
  //           name: 'Grafana',
  //           meta: { title: 'Grafana', target: '_blank' }
  //         },
  //         {
  //           path: 'https://dbaudit-sandbox.intsig.net/',
  //           name: 'ClickVisual',
  //           meta: { title: 'menu.analysis.click-visual', target: '_blank' }
  //         },
  //         {
  //           path: 'http://superset-test.intsig.net/',
  //           name: 'Superset',
  //           meta: { title: 'menu.analysis.superset', target: '_blank' }
  //         }
  //       ]
  //     },
  //     // cost
  //     {
  //       path: '/cost',
  //       name: '费用',
  //       component: RouteView,
  //       redirect: '/cost/bill',
  //       meta: { title: '费用', icon: 'account-book', permission: ['cost'] },
  //       children: [
  //         {
  //           path: '/cost/bill',
  //           name: '账单',
  //           component: () => import('@/views/cost/Bill'),
  //           meta: { title: '账单', keepAlive: true, permission: ['cost'] }
  //         },
  //         {
  //           path: '/cost/asset',
  //           name: '服务器账单',
  //           component: () => import('@/views/cost/Asset'),
  //           meta: { title: '服务器账单', keepAlive: true, permission: ['cost'] }
  //         },
  //         {
  //           path: '/cost/account-detail',
  //           name: '费用详单',
  //           component: () => import('@/views/cost/AccountDetail'),
  //           meta: { title: '费用详单', keepAlive: true, permission: ['cost'] }
  //         }
  //       ]
  //     },
  //     // monitor
  //     {
  //       path: '/monitor',
  //       name: '监控',
  //       component: RouteView,
  //       redirect: '/monitor/alertplus',
  //       meta: { title: '监控', icon: 'fund', permission: ['monitor'] },
  //       children: [
  //         {
  //           path: 'https://grafana-autom.intsig.net/dashboards/f/YAIqFEgMz/interface-traffic',
  //           name: 'NetMonitor',
  //           meta: { title: 'NetMonitor', target: '_blank' }
  //         },
  //         {
  //           path: 'https://alertplus.intsig.net',
  //           name: 'AlertPlus',
  //           meta: { title: 'AlertPlus', target: '_blank' }
  //         },
  //         {
  //           path: 'https://prometheus.intsig.net/',
  //           name: 'Prometheus',
  //           meta: { title: 'Prometheus', target: '_blank' }
  //         }
  //       ]
  //     },
  //     // bigdata
  //     {
  //       path: '/bigdata',
  //       name: '大数据',
  //       component: RouteView,
  //       redirect: '/bigdata/ids',
  //       meta: { title: '大数据', icon: 'table', permission: ['bigdata'] },
  //       children: [
  //         {
  //           path: 'https://ids.intsig.net/',
  //           name: 'IDS',
  //           meta: { title: 'IDS', target: '_blank' }
  //         }
  //       ]
  //     },
  //     // network
  //     {
  //       path: '/network',
  //       name: '网络与CDN',
  //       component: RouteView,
  //       redirect: '/network/api-gateway',
  //       meta: { title: '网络与CDN', icon: 'deployment-unit', permission: ['network'] },
  //       children: [
  //         {
  //           path: '/network/api-gateway',
  //           name: 'API网关',
  //           component: () => import('@/views/network/ApiGateway'),
  //           meta: { title: 'API网关', keepAlive: true, permission: ['network'] }
  //         },
  //         {
  //           path: '/network/cdn',
  //           name: 'CDN',
  //           component: () => import('@/views/network/Cdn'),
  //           meta: { title: 'CDN', keepAlive: true, permission: ['network'] }
  //         },
  //         {
  //           path: '/network/lb',
  //           name: '负载均衡',
  //           component: () => import('@/views/network/Lb'),
  //           meta: { title: '负载均衡', keepAlive: true, permission: ['network'] }
  //         },
  //         {
  //           path: '/network/ip',
  //           name: 'IP管理',
  //           component: () => import('@/views/network/Ip'),
  //           meta: { title: 'IP管理', keepAlive: true, permission: ['network'] }
  //         }
  //       ]
  //     },
  //     // domain
  //     {
  //       path: '/domain',
  //       name: '域名',
  //       component: RouteView,
  //       redirect: '/domain/domain-list',
  //       meta: { title: '域名', icon: 'global', permission: ['gateway'] },
  //       children: [
  //         {
  //           path: '/domain/domain-list',
  //           name: '域名',
  //           component: () => import('@/views/domain/Domain'),
  //           meta: { title: '域名', keepAlive: true, permission: ['domain'] }
  //         },
  //         {
  //           path: '/domain/dns-list',
  //           name: '域名解析',
  //           component: () => import('@/views/domain/DNS'),
  //           meta: { title: '域名解析', keepAlive: true, permission: ['dns'] }
  //         },
  //         {
  //           path: '/domain/ssl-list',
  //           name: 'SSL证书',
  //           component: () => import('@/views/domain/SSL'),
  //           meta: { title: 'SSL证书', keepAlive: true, permission: ['ssl'] }
  //         },
  //         {
  //           path: '/domain/icp-list',
  //           name: '域名备案',
  //           component: () => import('@/views/domain/ICP'),
  //           meta: { title: '域名备案', keepAlive: true, permission: ['icp'] }
  //         }
  //       ]
  //     },
  //     // safety
  //     {
  //       path: '/safety',
  //       name: '安全',
  //       component: RouteView,
  //       redirect: '/safety/jumpserver',
  //       meta: { title: '安全', icon: 'safety', permission: ['safety'] },
  //       children: [
  //         {
  //           path: 'https://bastion3.intsig.net/',
  //           name: '堡垒机',
  //           meta: { title: '堡垒机', target: '_blank' }
  //         }
  //       ]
  //     },
  //     // devops
  //     {
  //       path: '/devops',
  //       component: RouteView,
  //       redirect: '/devops/coder',
  //       name: '开发与运维',
  //       meta: { title: '开发与运维', icon: 'laptop', keepAlive: true, permission: ['devops'] },
  //       children: [
  //         {
  //           path: '/devops/coder',
  //           name: '云端开发工具',
  //           component: () => import('@/views/devops/Coder'),
  //           meta: { title: '云端开发工具', keepAlive: true, permission: ['devops'] }
  //         },
  //         {
  //           path: '/devops/ccd',
  //           name: '代码成本检测',
  //           component: () => import('@/views/devops/CodeCostDetection'),
  //           meta: { title: '代码成本检测', keepAlive: true, permission: ['devops'] }
  //         }
  //       ]
  //     },
  //     // account
  //     {
  //       path: '/account',
  //       component: RouteView,
  //       redirect: '/account/center',
  //       name: '个人页',
  //       meta: { title: 'menu.account', icon: 'user', keepAlive: true, permission: ['user'] },
  //       children: [
  //         {
  //           path: '/account/center',
  //           name: '个人中心',
  //           component: () => import('@/views/account/center'),
  //           meta: { title: 'menu.account.center', keepAlive: true, permission: ['user'] }
  //         }
  //       ]
  //     },
  //     {
  //       path: '/permission',
  //       component: RouteView,
  //       redirect: '/permission/user',
  //       name: '权限管理',
  //       meta: { title: '权限管理', icon: 'setting', keepAlive: true, permission: ['permission'] },
  //       children: [
  //         {
  //           path: '/permission/user',
  //           name: '用户管理',
  //           component: () => import('@/views/permission/UserManager'),
  //           meta: { title: '用户管理', keepAlive: true, permission: ['permission'] }
  //         },
  //         {
  //           path: '/permission/role',
  //           name: '角色管理',
  //           component: () => import('@/views/permission/RoleManager'),
  //           meta: { title: '角色管理', keepAlive: true, permission: ['permission'] }
  //         },
  //         {
  //           path: '/permission/policy',
  //           name: '策略管理',
  //           component: () => import('@/views/permission/PolicyManager'),
  //           meta: { title: '策略管理', keepAlive: true, permission: ['permission'] }
  //         },
  //         {
  //           path: '/permission/binding',
  //           name: '权限绑定',
  //           component: () => import('@/views/permission/PolicyBinding'),
  //           meta: { title: '权限绑定', keepAlive: true, permission: ['permission'] }
  //         },
  //         {
  //           path: '/permission/routes',
  //           name: '模块管理',
  //           component: () => import('@/views/permission/routeConfig.vue'),
  //           meta: { title: '模块管理', keepAlive: true, permission: ['permission'] }
  //         }
  //       ]
  //     }
  //   ]
  // },
  // {
  //   path: '*',
  //   redirect: '/404',
  //   hidden: true
  // }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login.vue')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register.vue')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult.vue')
      },
      {
        path: 'recover',
        name: 'recover',
        component: undefined
      }
    ]
  },
  {
    path: '/homepage',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/homePage/index.vue')
  },

  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404.vue')
  },
  {
    path: '/desktop/session',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/desktop/DesktopSession.vue')
  },
  {
    path: '/desktop/session-recoding',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/desktop/DesktopOfflineSessionPlay.vue')
  },
  {
    path: '/desktop/session-monitor',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/desktop/DesktopOnlineSessionMonitor.vue')
  }
]
