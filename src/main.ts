import { promiseHttps, promiseLocalhost } from '@aim/helper'

/**
 * 确保构建产物访问 https
 */
promiseHttps()

/**
 * 确保开发环境访问 localhost
 */
promiseLocalhost()

/**
 * 确保JsDiff全局可用
 */
if (typeof window !== 'undefined' && window.JsDiff) {
  window.diffLines = window.JsDiff.diffLines
}

import { App as AppType, createApp } from 'vue'
import { appRegister, errorWatch } from '@aim/helper'
import { assetErrorWatch } from '@aim/helper/x/app'
import { vClassAddon } from '@tx/plugin/directive'
import Antd from 'ant-design-vue'
import AntdIcon from '@ant-design/icons-vue'
import VueClipboard from 'vue-clipboard2'
import VueCropper from 'vue-cropper/next'
import VxeTable from 'vxe-table'
import contextmenu from 'v-contextmenu'
import 'v-contextmenu/dist/themes/default.css'

import '@/base'

/**
 * 样式助手
 * https://tianxuan.intsig.com/ui/style/
 */
import '@tx/style/index.css'

/**
 * 项目样式
 */
import './assets/style/index.less'

const global = globalThis as any

global.moment = global.dayjs

import App from './App.vue'
import bootstrap from './core/bootstrap'
import { VueAxios } from './utils/request'
import i18n from './locales'
import router from './router'
import store from './store'
import suite from './suite'

import './permission' // permission control
import './utils/vendorLoader' // 第三方资源加载助手

const app: AppType = createApp(App) //
  .use(Antd as any)
  .use(AntdIcon as any)
  .use(VueAxios as any)
  .use(VueClipboard as any)
  .use(VueCropper as any)
  .use(VxeTable as any)
  .use(contextmenu as any)
  .use(i18n as any)
  .use(router as any)
  .use(store as any)
  .use(suite as any)
  .use(vClassAddon)

appRegister(app)

/**
 * 全局 app 实例
 */
globalThis.mainApp = app

app.mount('#app')

// 资源监听
assetErrorWatch(url => {
  // 忽略 zhugeio 加载失败提示
  if (url.includes('zhugeio.intsig.net') || url.includes('zhugeio-new.intsig.net')) {
    return false
  }
  // 相关域名下的资源加载出错时才提醒
  return url.includes('intsig.net') || url.includes(location.host)
})

bootstrap()

// 报错监听
errorWatch(app, (error, vm, info) => {
  return {
    param: {
      // 标签，可选
      tag: info,
      user: String(noc.user.getUserInfo()['email'] || '').replace(/@.*$/, ''),
    },
  }
})
