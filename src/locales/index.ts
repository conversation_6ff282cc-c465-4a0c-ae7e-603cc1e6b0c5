import { createI18n } from 'vue-i18n'

import enUS from './lang/en-US'
import zhCN from './lang/zh-CN'

/**
 * 构建未通过
 */
// let defaultLang = noc.store.getItem('lang') || 'zh-CN'

let defaultLang = localStorage.getItem('lang') || 'zh-CN'

const messages = {
  'zh-CN': {
    ...zhCN,
  },
  'en-US': {
    ...enUS,
  },
}

if (!messages.hasOwnProperty(defaultLang)) {
  defaultLang = 'zh-CN'
}

const i18n = createI18n({
  fallbackLocale: defaultLang,
  locale: defaultLang,
  messages,
  silentTranslationWarn: true,
})

function setI18nLanguage(lang) {
  document.querySelector('html').setAttribute('lang', lang)
  return lang
}

export function loadLanguageAsync(lang = defaultLang) {
  return new Promise(resolve => {
    // 缓存语言设置
    noc.store.setItem('lang', lang)
    setI18nLanguage(lang)
    return resolve(lang)
  })
}

export function i18nRender(key) {
  return key
  // return i18n.t(`${key}`)
}

export default {
  install(app) {
    app.use(i18n)
  },
}
