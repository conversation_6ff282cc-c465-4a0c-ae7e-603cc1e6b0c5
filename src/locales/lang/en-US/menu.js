export default {
    'menu.welcome': 'Welcome',
    'menu.home': 'Home',
    'menu.dashboard': 'Dashboard',
    'menu.dashboard.analysis': 'Analysis',
    'menu.dashboard.monitor': 'Monitor',
    'menu.dashboard.workplace': 'Workplace',
    'menu.form': 'Form',
    'menu.form.basic-form': 'Basic Form',
    'menu.form.step-form': 'Step Form',
    'menu.form.step-form.info': 'Step Form(write transfer information)',
    'menu.form.step-form.confirm': 'Step Form(confirm transfer information)',
    'menu.form.step-form.result': 'Step Form(finished)',
    'menu.form.advanced-form': 'Advanced Form',
    'menu.server': 'Compute',
    'menu.server.asset-list': 'Server',
    'menu.server.asset-info': 'Server Info',
    'menu.server.lambda': 'Lambda',
    'menu.desktop': 'Desktop',
    'menu.workflow': 'Workflow',
    'menu.workflow.workflow-list': 'Workflow List',
    'menu.workflow.my-application': 'I Created',
    'menu.workflow.my-approval': 'My Approval',
    'menu.workflow.server-modify-config': 'Server Config Modify',
    'menu.workflow.server-modify-tag': 'Server Tag Modify',
    'menu.workflow.server-del': 'Server Delete',
    'menu.workflow.server-add': 'Server Create',
    'menu.workflow.ops-help': 'Ops Help',
    'menu.storage': 'Storage',
    'menu.storage.object-storage-personal': 'Object Storage Personal',
    'menu.db': 'Database',
    'menu.db.mysql-personal': 'MySQL Personal',
    'menu.db.mysql': 'MySQL',
    'menu.db.redis': 'Redis',
    'menu.db.global-table': 'Global Table',
    'menu.db.click-house': 'ClickHouse',
    'menu.db.dms': 'DMS',
    'menu.cost': 'Cost',
    'menu.cost.bill': 'Bill',
    'menu.cost.asset': 'Server Bill',
    'menu.analysis': 'Data Analysis',
    'menu.analysis.grafana': 'Grafana',
    'menu.analysis.click-visual': 'ClickVisual',
    'menu.analysis.superset': 'Superset',
    'menu.monitor': 'Monitor',
    'menu.monitor.alertplus': 'AlertPlus',
    'menu.monitor.net': 'Net Monitor',
    'menu.monitor.prometheus': 'Prometheus',
    'menu.network': 'Network And CDN',
    'menu.network.api-gateway': 'Api Gateway',
    'menu.network.cdn': 'CDN',
    'menu.network.lb': 'Load Balance',
    'menu.network.ip': 'IP List',
    'menu.safety': 'Safety',
    'menu.safety.jumpserver': 'JumpServer',
    'menu.devops': 'DevOps',
    'menu.devops.coder': 'Coder',
    'menu.devops.ccd': 'Code Cost Detection',
    'menu.bigdata': 'Big Data',
    'menu.bigdata.ids': 'IDS',
    'menu.list.card-list': 'Card List',
    'menu.list.search-list': 'Search List',
    'menu.list.search-list.articles': 'Search List(articles)',
    'menu.list.search-list.projects': 'Search List(projects)',
    'menu.list.search-list.applications': 'Search List(applications)',
    'menu.profile': 'Profile',
    'menu.profile.basic': 'Basic Profile',
    'menu.profile.advanced': 'Advanced Profile',
    'menu.result': 'Result',
    'menu.result.success': 'Success',
    'menu.result.fail': 'Fail',
    'menu.exception': 'Exception',
    'menu.exception.not-permission': '403',
    'menu.exception.not-find': '404',
    'menu.exception.server-error': '500',
    'menu.exception.trigger': 'Trigger',
    'menu.account': 'Account',
    'menu.account.center': 'Account Center',
    'menu.account.settings': 'Account Settings',
    'menu.account.trigger': 'Trigger Error',
    'menu.account.logout': 'Logout',
    'menu.permission': 'Permission Manager',
    'menu.permission.user': 'User Manager',
    'menu.permission.role': 'Role Manager',
    'menu.permission.policy': 'Policy Manager',
    'menu.permission.binding': 'Permission Binding',
    'menu.cron': 'Schedule Platform',
    'menu.cron.cron-task-config': 'Cron Task Config'
}
