<template>
  <x-select
    :options="data.options"
    :placeholder="placeholder"
    :value="modelValue"
    @change="onChange"
  ></x-select>
</template>
<script lang="ts" setup>
/**
 * 部门选择器
 * @example
 * ```vue
 *   <SelectOrganization v-model="xxx" @change="(value) => {}">
 * ```
 */

import { onMounted, reactive } from 'vue'
import { getAssetListDepartment } from '@/api/asset'

defineOptions({
  name: 'SelectOrganization',
})

const emits = defineEmits(['change', 'update:modelValue'])

defineProps({
  modelValue: noc.propAny(),
  placeholder: noc.propString('请选择部门'),
})

const data = reactive({
  options: [],
})

function onChange(value) {
  emits('update:modelValue', value)
  emits('change', value)
}

onMounted(async () => {
  const res = await getAssetListDepartment()
  const list = noc.promiseArray(res?.Data?.department).map(item => {
    return {
      label: item,
      value: item,
    }
  })
  data.options = noc.sort(list, 'label')
})
</script>
