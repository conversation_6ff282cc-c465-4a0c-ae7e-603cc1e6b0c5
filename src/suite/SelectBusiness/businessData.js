/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-12-11 16:33:49
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-12-11 16:56:13
 * @FilePath: \cloud_web\src\suite\SelectBusiness\businessData.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const businessData = [
  {
    label: 'CS',
    value: 'CS',
  },
  {
    label: 'CS-CC',
    value: 'CS-CC',
  },
  {
    label: 'DG',
    value: 'DG',
  },
  {
    label: 'SSG',
    value: 'SSG',
  },
  {
    label: '战略合作',
    value: '战略合作',
  },
  {
    label: 'OCR',
    value: 'OCR',
  },
  {
    label: 'ACG',
    value: 'ACG',
  },
  {
    label: 'AIM平台架构',
    value: 'AIM平台架构',
  },
  {
    label: 'AIM运维',
    value: 'AIM运维',
  },
  {
    label: '大数据',
    value: '大数据',
  },
  {
    label: 'AIM企业信息化',
    value: 'AIM企业信息化',
  },
  {
    label: '橘子兼职',
    value: '橘子兼职',
  },
  {
    label: '搜索(DG)',
    value: '搜索(DG)',
  },
  {
    label: '营销云',
    value: '营销云',
  },
  {
    label: 'AI开放平台',
    value: 'AI开放平台',
  },
  {
    label: 'AIM',
    value: 'AIM',
  },
  {
    label: 'AIM自然语言算法',
    value: 'AIM自然语言算法',
  },
  {
    label: '总裁办',
    value: '总裁办',
  },
]
