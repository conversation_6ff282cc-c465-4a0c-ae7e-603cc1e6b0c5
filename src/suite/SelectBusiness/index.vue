<template>
  <x-select :options="data.options" :placeholder="placeholder" :value="modelValue" @change="onChange"></x-select>
</template>
<script lang="ts" setup>
/**
 * 业务选择器
 */

import { onMounted, reactive } from 'vue'
import { businessData } from './businessData'
//   import { getAssetListOrganization } from '@/api/asset'

defineOptions({
  name: 'SelectOrganization',
})

const emits = defineEmits(['change', 'update:modelValue'])

defineProps({
  modelValue: noc.propAny(),
  placeholder: noc.propString('请选择业务'),
})

const data = reactive({
  options: [],
})

function onChange(value) {
  emits('update:modelValue', value)
  emits('change', value)
}

onMounted(async () => {
  //   const res = await getAssetListOrganization()
  //   const list = noc.promiseArray(res?.Data?.organization).map(item => {
  //     return {
  //       label: item,
  //       value: item,
  //     }
  //   })
  data.options = businessData
})
</script>
