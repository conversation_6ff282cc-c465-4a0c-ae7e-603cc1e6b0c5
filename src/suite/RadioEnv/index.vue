<template>
  <a-radio-group :value="modelValue" button-style="solid" @change="onChange">
    <a-radio-button value="online">生产环境</a-radio-button>
    <a-radio-button value="pre">预发布环境</a-radio-button>
    <a-radio-button value="test">测试环境</a-radio-button>
    <a-radio-button value="dev">开发环境</a-radio-button>
  </a-radio-group>
</template>
<script lang="ts" setup>
/**
 * 环境选择器
 */

defineOptions({
  name: 'RadioEnv',
})

const emits = defineEmits(['change', 'update:modelValue'])

defineProps({
  modelValue: noc.propAny(),
})

function onChange(e) {
  let { value } = e.target
  emits('update:modelValue', value)
  emits('change', value)
}
</script>
