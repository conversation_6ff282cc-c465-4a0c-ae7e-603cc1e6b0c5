<template>
  <div class="noc-editor-json" :style="{ visibility: editor ? '' : 'hidden' }">
    <div class="jsoneditor-vue"></div>
    <div class="jsoneditor-btns" v-if="showBtns !== false">
      <button class="json-save-btn" type="button" @click="onSave()" :disabled="error">{{ locale[lang].save }}</button>
    </div>
  </div>
</template>

<script>
/**
 * 修改自 vue-json-editor/vue-json-editor.vue
 *
 * 改动：
 * - JsonEditor 改成从 CDN 引入
 * - modes 按字母排序
 * - powered 声明取消点击
 * - 设置组件名称 NocEditorJson
 */

import { vendorLoad } from '@/utils/vendorLoader'

export default {
  name: 'NocEditorJson',
  emits: ['has-error', 'json-change', 'update:modelValue'],
  props: {
    expandedOnStart: noc.propBoolean(),
    lang: noc.propString('en'),
    mode: noc.propString('tree'),
    modes: {
      type: Array,
      default: function () {
        return ['tree', 'code', 'form', 'text', 'view'].sort()
      },
    },
    modelValue: noc.propAny(),
    showBtns: noc.propBoolean(),
  },
  data() {
    return {
      editor: null,
      error: false,
      expandedModes: ['tree', 'view', 'form'],
      internalChange: false,
      locale: {
        it: {
          save: 'SALVA',
        },
        en: {
          save: 'SAVE',
        },
        zh: {
          save: '保存',
        },
      },
    }
  },
  async mounted() {
    let self = this
    let JsonEditor = await vendorLoad('vue-json-editor')
    let options = {
      mode: this.mode,
      modes: this.modes, // allowed modes
      onChange: () => {
        try {
          let value = self.editor.get()
          self.error = false
          self.internalChange = true
          setTimeout(() => {
            self.internalChange = false
          })
          self.$emit('json-change', value)
          self.$emit('update:modelValue', value)
        } catch (e) {
          self.error = true
          self.$emit('has-error', e)
        }
      },
      onModeChange: () => {
        this.expandAll()
      },
    }
    this.editor = new JsonEditor(this.$el.querySelector('.jsoneditor-vue'), options, this.modelValue)
  },
  methods: {
    onSave() {
      let value
      try {
        value = this.editor.get()
      } catch (e) {
      }
      if (value != null) {
        this.$emit('json-save', value)
      }
    },
    expandAll() {
      if (this.expandedOnStart && this.expandedModes.includes(this.editor.getMode())) {
        this.editor.expandAll()
      }
    },
    setEditorValue(value) {
      /**
       * 传入 undefined 会触发 JS 错误
       */
      if (noc.isVoid(value)) {
        value = {}
      }
      if (this.editor) {
        this.editor.set(value)
      }
    },
  },
  watch: {
    modelValue: {
      handler(value) {
        if (!this.internalChange) {
          this.setEditorValue(value)
          this.error = false
          this.expandAll()
        }
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.ace_line_group {
  text-align: left;
}

.json-editor-container {
  display: flex;
  width: 100%;
}

.json-editor-container .tree-mode {
  width: 50%;
}

.json-editor-container .code-mode {
  flex-grow: 1;
}

.jsoneditor-btns {
  text-align: center;
  margin-top: 10px;
}

.jsoneditor-vue .jsoneditor-outer {
  min-height: 150px;
}

.jsoneditor-vue div.jsoneditor-tree {
  min-height: 350px;
}

.json-save-btn {
  background-color: #20a0ff;
  border: none;
  color: #ffffff;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
}

.json-save-btn:focus {
  outline: none;
}

.json-save-btn[disabled] {
  background-color: #1d8ce0;
  cursor: not-allowed;
}

code {
  background-color: #f5f5f5;
}
</style>

<style>
.jsoneditor-menu .jsoneditor-poweredBy {
  pointer-events: none;
}
</style>
