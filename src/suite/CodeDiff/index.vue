<template lang="pug">
component(
  v-if="loaded"
  :is="componentName"
  :context="context"
  :diffStyle="diffStyle"
  :drawFileList="drawFileList"
  :fileName="fileName"
  :isShowNoChange="isShowNoChange"
  :newString="newString"
  :oldString="oldString"
  :outputFormat="outputFormat"
  :renderNothingWhenEmpty="renderNothingWhenEmpty"
)
</template>
<script>
/**
 * 封装 vue-code-diff，使用 CDN 引入资源，用法完全一样
 * https://www.npmjs.com/package/vue-code-diff
 */

import { loadVueCodeDiff } from '@/utils/vendorLoader'

export default {
  name: 'NocCodeDiff',
  props: {
    context: Number,
    diffStyle: String, // 'char' | 'word',
    drawFileList: Boolean,
    fileName: String,
    isShowNoChange: Boolean,
    newString: String,
    oldString: String,
    outputFormat: String, // 'line-by-line' | 'side-by-side',
    renderNothingWhenEmpty: Boolean,
  },
  data() {
    return {
      componentName: null,
      loaded: false,
    }
  },
  async mounted() {
    const VueCodeDiff = await loadVueCodeDiff()
    if (VueCodeDiff) {
      if (!VueCodeDiff._installed) {
        globalThis.mainApp.use(VueCodeDiff)
        VueCodeDiff._installed = true
      }
      this.componentName = 'CodeDiff'
      this.loaded = true
    }
  },
}
</script>
