import { defineAsyncComponent } from 'vue'

import ProLayout from '@ant-design-vue/pro-layout'
import { Form, FormItem } from 'ant-design-vue'
import AIcon from './legacy/AIcon.vue'
import PageContainer from './PageContainer/index.vue'

import Dialog from '@/components/Dialog'
import MultiTab from '@/components/MultiTab'
import PageLoading from '@/components/PageLoading/index.jsx'
import PermissionHelper from '@/core/permission/permission'

import NocEditor<PERSON>son from './EditorJson/index.vue'

import DirectiveAction from '@/core/directives/action'

const RadioEnv = defineAsyncComponent(() => import('./RadioEnv/index.vue'))
const SelectDepartment = defineAsyncComponent(() => import('./SelectDepartment/index.vue'))
const SelectOrganization = defineAsyncComponent(() => import('./SelectOrganization/index.vue'))

const XSelect = defineAsyncComponent(() => import('@tx/ui/x/Select'))

export default {
  install(app: any) {
    app.use(DirectiveAction)
    app.use(ProLayout)

    app.component('PageContainer', PageContainer)
    app.component('page-header-wrapper', PageContainer)

    app.component(Dialog)
    app.component(MultiTab)
    app.component(PageLoading)

    app.component('a-icon', AIcon)
    app.component('a-form-model', Form)
    app.component('a-form-model-item', FormItem)

    app.component(NocEditorJson.name, NocEditorJson)

    app.component('RadioEnv', RadioEnv)
    app.component('SelectDepartment', SelectDepartment)
    app.component('SelectOrganization', SelectOrganization)

    app.component('XSelect', XSelect)
    app.component('x-select', XSelect)

    app.use(PermissionHelper)
  },
}
