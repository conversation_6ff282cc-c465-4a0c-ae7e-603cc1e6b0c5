<template lang="pug">
.page-main-head(v-if="data.breads.length")
  a-breadcrumb
    a-breadcrumb-item(v-for="item in data.breads")
      router-link(:to="item.path") {{ item.name }}
  .page-main-head-title
    span.ant-page-header-back.text-hover(v-if="$attrs.onBack" @click="$attrs.onBack")
      tx-icon(type="arrow-left")
    | {{ data.name }}
PageContainer
  template(#content)
    slot(name="content")
  template(#extraContent)
    slot(name="extraContent")
  slot
</template>
<script lang="ts" setup>
// pro-layout breadcrumbRender slot 无效？

import { reactive, watch } from 'vue'
import { useRoute } from 'vue-router'
import { PageContainer } from '@ant-design-vue/pro-layout'

/**
 * TODO，调整为属性或方法
 */
// defineEmits(['back'])

const route = useRoute()

const data = reactive({
  breads: [],
  name: '',
})

watch(route, () => {
  data.name = (route.meta.name || route.name) as string
  data.breads = route.matched.slice().filter(item => item.path && item.path != '/').map(item => {
    return {
      name: item.meta.name || item.name,
      path: item.path,
    }
  })
  setTimeout(() => {
    let customHead = noc.$('.page-main-head')[0]
    let node = noc.$('.ant-pro-page-container-wrap')[0]
    if (node) {
      if (node.textContent == '') {
        node.style.display = 'none'
      } else {
        node.style.display = ''
      }
      if (customHead) {
        customHead.style.marginBottom = node.style.display ? '48px' : ''
      }
    }
  })
}, {
  immediate: true,
})
</script>
<style lang="less">
.page-main-head {
  background: white;
  margin: -24px;
  padding: 24px;
}

.page-main-head-title {
  display: block;
  color: rgba(0, 0, 0, .85);
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  margin-top: 8px;
  padding-right: 12px;
}
</style>
