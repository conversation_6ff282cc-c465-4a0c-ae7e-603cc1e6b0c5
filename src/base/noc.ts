/**
 * 文档 https://tianxuan.intsig.com/helper/
 */

import '@aim/helper/x/modalDrag'

/**
 * 消息框、对话框、通知框
 */
import { message } from '@aim/helper/x/message'
import { modal } from '@aim/helper/x/modal'
import { notice } from '@aim/helper/x/notice'

/**
 * 对象 helper
 */
import { base64, cookie, http, sso, store, url, user } from '@aim/helper'

/**
 * 函数 helper
 */

// 属性定义
import {
  propAny,
  propArray,
  propBoolean,
  propBooleanString,
  propBooleanVoid,
  propFunction,
  propNumber,
  propNumberString,
  propNumberVoid,
  propObject,
  propString,
  propStringVoid,
} from '@aim/helper'

// 环境判断
import { isLocalhost, isNotOnline, isOnline, isPre, isTest } from '@aim/helper'

import {
  // 类型判断
  isArray,
  isBoolean,
  isDate,
  isEmpty,
  isFunction,
  isNumber,
  isObject,
  isString,
  isVoid,
} from '@aim/helper'

// 对象和数组操作
import { clone, create, find, findIndex, genId, loop, loopUntil, sort } from '@aim/helper'

// 元素选择
import { $ } from '@aim/helper'

// 其他
import {
  base64jpg,
  base64reg,
  date,
  datetime,
  debounce,
  jsonParse,
  log,
  noop,
  numberCompare,
  numberFormat,
  promise,
  promiseArray,
  sleep,
  throttle,
  trim,
  type,
  unique,
} from '@aim/helper'

const global: any = globalThis

let helperStatic = {
  // 网站名称，主子应用共用
  SITE_NAME: '',
  // 消息框、对话框、通知框
  message,
  modal,
  notice,
  // 对象 helper
  base64,
  cookie,
  http,
  sso,
  store,
  url,
  user,
  // 属性定义
  propAny,
  propArray,
  propBoolean,
  propBooleanString,
  propBooleanVoid,
  propFunction,
  propNumber,
  propNumberString,
  propNumberVoid,
  propObject,
  propString,
  propStringVoid,
  // 环境判断
  isLocalhost,
  isTest,
  isPre,
  isOnline,
  isNotOnline,
  // 类型判断
  isArray,
  isBoolean,
  isDate,
  isEmpty,
  isFunction,
  isNumber,
  isObject,
  isString,
  isVoid,
  // 对象和数组操作
  clone,
  create,
  find,
  findIndex,
  genId,
  loop,
  loopUntil,
  sort,
  // 元素选择
  $,
  // 其他
  base64jpg,
  base64reg,
  date,
  datetime,
  debounce,
  jsonParse,
  log,
  noop,
  numberCompare,
  numberFormat,
  promise,
  promiseArray,
  sleep,
  throttle,
  trim,
  type,
  unique,
}

/**
 * 全局变量，简化使用
 */
if (global.noc) {
  helperStatic = global.noc
} else {
  global.noc = helperStatic
}

/**
 * 导出变量 noc
 */
export let noc = helperStatic

/**
 * 声明全局变量 noc
 */
declare global {
  const noc: typeof helperStatic

  interface Window {
    noc: typeof helperStatic
  }
}
