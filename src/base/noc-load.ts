import { noc } from './noc'
import { sso, store as storage, user } from '@aim/helper'
import { config as itConfig } from '@intsig/toolkit'
import { config as txConfig } from '@tx/ui'
import { getEnvFlag, getSsoPlatformId } from '@/config'
import { onSSoLogout } from '@/utils/ssoLogout'
import { message } from 'ant-design-vue'

const global = globalThis as any

// message 全局配置
noc.message.config({ top: '45px' })

// SSO 选项设置
sso.init({
  platformId: getSsoPlatformId(),
  ENV: getEnvFlag(),
})

// cookie、storage 名称
let suffix = ''
if (noc.isTest()) {
  suffix = '-test'
}

user.COOKIE_ACCOUNT = 'cloud-a' + suffix
user.COOKIE_TOKEN = 'cloud-token' + suffix // x-token、x-token-test
user.COOKIE_USER_ID = 'cloud-uid' + suffix
user.STORAGE_PREFIX = 'cloud-u-'

user.getUserInfo = () => {
  return storage.get('userInfo') || ''
}

user.removeUserInfo = () => {
  storage.remove('userInfo')
}

user.setUserInfo = info => {
  storage.set('userInfo', info)
}

// 检测并载入登录用户信息
user.init()

// 只判断 Token 是否存在
user.isLogin = () => !!user.getToken()

itConfig.set({
  ssoPlatformId: getSsoPlatformId(),
  ssoToken: user.getToken(),
  onSSoLogout: res => {
    const { code, msg } = res
    if (code === ********) {
      onSSoLogout(res)
    } else {
      message.error(msg)
    }
  },
})

txConfig.set({
  // 全局助手变量名称
  helperName: 'noc',
  // Monaco Editor 网址基址
  monacoCdnBase: '/vendor-cdn/monaco-editor@0.52.2',
  // Monaco Editor Worker 入口，需要同域
  monacoWorkerUrl: '/vendor-cdn/monaco-editor@0.52.2/worker-loader-proxy.js',
})

if (!global.require) {
  /**
   * TODO，临时支持 require 图像，完全去掉 require 后删除
   */
  global.require = (path: string) => {
    return path.replace('@/assets', '/vendor-cdn/@img')
  }
}

/**
 * TODO，临时支持 Antd3 表单验证调整为返回 Promise
 */
global.antdFormValidate = (form: any, validCallback: (values: any) => any) => {
  return form
    .validate()
    .then(validCallback)
    .catch((e: any) => {
      /**
       * 表单验证错误
       */
      if (e && e.values) {
        return
      }
      /**
       * 其他错误
       */
      global.console.warn(e)
      return e
    })
}

/**
 * TODO，临时支持 Antd3 表单元素验证调整为返回 Promise
 */
global.antdFormValidateCallback = (error?: any) => {
  if (error?.message || typeof error == 'string') {
    return Promise.reject(error.message || error)
  }
  return Promise.resolve()
}

/**
 * TODO，临时支持 Antd3 表单验证 rules 不支持 xxx.yyy ？只保留最后一级属性名称
 */
global.antdFormRulesFormat = (rules: any) => {
  Object.keys(rules).forEach(key => {
    if (key.includes('.')) {
      let value = rules[key]
      delete rules[key]
      rules[key.replace(/^.*\.(\w+)$/, '$1')] = value
    }
  })
  return rules
}

/**
 * 声明全局变量 noc
 */
declare global {
  const antdFormRulesFormat: <T extends object>(rules: T) => T
  const antdFormValidate: (form: any, validCallback: (values: any) => any) => Promise<any>
  const antdFormValidateCallback: (message?: any) => typeof Promise
}
