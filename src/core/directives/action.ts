import { App as AppType } from 'vue'
import store from '@/store'

/**
 * Action 权限指令
 * 指令用法：
 *  - 在需要控制 action 级别权限的组件上使用 v-action:[method] , 如下：
 *    <i-button v-action:add >添加用户</i-button>
 *    <tx-button v-action:delete>删除用户</tx-button>
 *    <a v-action:edit @click="edit(record)">修改</a>
 *
 *  - 当前用户没有权限时，组件上使用了该指令则会被隐藏
 *  - 当后台权限跟 pro 提供的模式不同时，只需要针对这里的权限过滤进行修改即可
 *
 *  @see https://github.com/vueComponent/ant-design-vue-pro/pull/53
 */

export default {
  install(app: AppType) {
    app.directive('action', {
      mounted: (el, binding, vnode) => {
        const actionName = binding.arg
        const roles = store.getters.roles
        const elVal = app.config.globalProperties.$route.meta.permission
        const permissionId: any = (elVal instanceof String && [elVal]) || elVal
        roles.permissions.forEach(p => {
          if (!permissionId.includes(p.permissionId)) {
            return
          }
          if (p.actionList && !p.actionList.includes(actionName)) {
            ;(el.parentNode && el.parentNode.removeChild(el)) || (el.style.display = 'none')
          }
        })
      },
    })
    /**
     * antd 弃用的指令
     */
    app.directive('decorator', {
      mounted(el, binding, vnode, prevVnode) {
        console.warn('Antd 不支持指令 v-decorator，请更新用法')
      },
    })
  },
}
