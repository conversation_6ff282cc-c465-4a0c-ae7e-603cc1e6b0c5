import { reactive } from 'vue'

export const PERMISSION_ENUM = {
  add: { key: 'add', label: '新增' },
  delete: { key: 'delete', label: '删除' },
  edit: { key: 'edit', label: '修改' },
  query: { key: 'query', label: '查询' },
  get: { key: 'get', label: '详情' },
  enable: { key: 'enable', label: '启用' },
  disable: { key: 'disable', label: '禁用' },
  import: { key: 'import', label: '导入' },
  export: { key: 'export', label: '导出' },
}

export default {
  install(app) {
    /**
     * TODO，移除使用
     */
    Object.defineProperties(app.config.globalProperties || app.prototype, {
      $auth: {
        configurable: true,
        get() {
          const vm = this
          return permissions => {
            const [permission, action] = permissions.split('.')
            const permissionList = vm.$store.getters.roles.permissions
            return (
              permissionList
                .find(val => {
                  return val.permissionId === permission
                })
                .actionList.findIndex(val => {
                  return val === action
                }) > -1
            )
          }
        },
      },
      /**
       * TODO，使用 helper 而不是原型链方法
       */
      $form: {
        configurable: true,
        value: {
          /**
           * @deprecated 该方法已弃用
           */
          createForm: (vm, formRefName = 'formRef', data = {}) => {
            if (data.constructor != Object) {
              data = {}
            }
            let res = reactive({ ...data })
            Object.defineProperties(res, {
              /**
               * @deprecated 使用 Object.assign
               */
              setFieldsValue: {
                configurable: true,
                value: fields => {
                  Object.assign(res, fields)
                },
              },
              /**
               * @deprecated 使用 helper 而不是原型链方法
               */
              validateFields: {
                configurable: true,
                value: (callback?: (errors, values) => void) => {
                  antdFormValidate(vm.$refs[formRefName], values => {
                    let errors = undefined
                    callback(errors, values)
                  }).catch(e => {
                    let values = undefined
                    callback(e, values)
                  })
                },
              },
            })
            return res
          },
        },
      },
      /**
       * 临时 polyfill，待移除
       */
      $set: {
        configurable: true,
        value: (object, key, value) => {
          object[key] = value
        },
      },
    })
  },
}
