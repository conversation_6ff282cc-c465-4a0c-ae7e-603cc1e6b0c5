/*
表格扩展行使用 a-descriptions，它内部的 table width 是 auto 时，table-layout fixed 失效，此时指定单元格宽度
若将 table width 设置为 100%，扩展行展开前后，其他行单元格宽度会出现变化
*/
.ant-table-expanded-row {
  .ant-descriptions-view {
    > table {
      td {
        width: 33.3333%;

        &[colspan="2"] {
          width: 66.6666%;
        }

        &[colspan="3"] {
          width: 100%;
        }
      }
    }
  }
}

/* 行内表格子元素 ant-row 撑满一行 */
.ant-form-inline {
  > .ant-row {
    width: 100%;

    &:first-child:last-child {
      flex: 1;
    }
  }
}

/* 原来版本缺少 CSS 间距，使用的是 HTML 空白 */
.ant-form-item-control-input-content {
  > .ant-btn:first-child + div,
  > .ant-radio-group:first-child + div {
    margin-top: 0.25rem;
  }
}

.ant-select-dropdown {
  // 固定显示滚动条
  .ant-select-tree-list-holder,
  .rc-virtual-list-holder {
    overflow-y: auto !important;
  }

  .rc-virtual-list-scrollbar,
  .ant-select-tree-list-scrollbar {
    display: none !important;
  }
}

.ant-table-cell {
  word-break: break-word;
}
