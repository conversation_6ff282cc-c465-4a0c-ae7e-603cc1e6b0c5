<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="5" :sm="24">
              <a-form-item label="ip">
                <a-input v-model:value="queryParam.ip" placeholder="IP" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <a-popconfirm title="确定删除？" @confirm="deleteDhcp(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <a-drawer
        title="详情"
        placement="right"
        :closable="false"
        width="40%"
        :visible="detailIdVisible"
        @close="detailIdVisible = false"
      >
        <a-card :bordered="false" :model="detailData" ref="detailData">
          <a-descriptions :column="2">
            <a-descriptions-item label="IP">{{ detailData.ip }}</a-descriptions-item>
            <a-descriptions-item label="机房">{{ detailData.idc }}</a-descriptions-item>
            <a-descriptions-item label="用户">{{ detailData.user }}</a-descriptions-item>
            <a-descriptions-item label="密码">{{ detailData.password }}</a-descriptions-item>
            <a-descriptions-item label="机柜">{{ detailData.rack }}</a-descriptions-item>
            <a-descriptions-item label="资产Id">{{ detailData.propertyId }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ detailData.comment }}</a-descriptions-item>
            <a-descriptions-item label="状态">{{ detailData.status }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-drawer>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { DeleteDhcp, listDhcpUnusedData } from '@/api/asset'
import CreateForm from '@/views/server/modules/CreateForm.vue'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '服务器名',
    dataIndex: 'hostname',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '250px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      createVisible: false,
      updateVisible: false,
      assetIdcList: [],
      // create model
      visible: false,
      confirmLoading: false,
      createLoading: false,
      updateLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return listDhcpUnusedData(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      rules: antdFormRulesFormat({
        ip: [{ required: true, message: '请确认物理机IP', trigger: 'change' }],
        idc: [{ required: true, message: '请确认机房', trigger: 'change' }],
        rack: [{ required: true, message: '请确认机柜信', trigger: 'change' }],
        user: [{ required: true, message: '请确认用户', trigger: 'change' }],
        password: [{ required: true, message: '请确认密码', trigger: 'change' }],
      }),
      detailIdVisible: false,
      userpRression: false,
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
  },
  methods: {
    deleteDhcp(record) {
      const sendData = { ip: record.ip, hostname: record.hostname }
      DeleteDhcp(sendData).then(response => {
        const msg = response.Data.message
        if (msg === 'Success') {
          notification.success({
            message: '操作成功',
          })
        } else {
          notification.error({
            message: '操作失败:' + msg,
          })
        }
      })
    },
    statusFilter(type) {
      if (type === 1) {
        return '使用中'
      } else if (type === 2) {
        return '已销毁'
      }
    },
    statusTypeFilter(type) {
      if (type === 1) {
        return 'success'
      } else {
        return 'error'
      }
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
  },
}
</script>
