<template>
  <page-header-wrapper>
    <div>
      <a-row
        style="margin-bottom: 15px; height: 50%; margin-right: 40px"
        type="flex"
        justify="space-between"
        align="bottom"
      >
        <a-col :span="24">
          <a-card :loading="loading" title="购买时间统计">
            <a-skeleton v-if="loading" />
            <hostChart :compRef="'buyTime'" :chartData="buyTimeData" />
          </a-card>
        </a-col>
      </a-row>
      <a-row
        style="margin-bottom: 15px; height: 50%; margin-right: 40px"
        type="flex"
        justify="space-between"
        align="bottom"
      >
        <a-col :span="24">
          <a-card :loading="loading" title="GPU统计">
            <a-skeleton v-if="loading" />
            <hostChart :compRef="'gpu'" :chartData="gpuData" />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </page-header-wrapper>
</template>

<script>
import hostChart from '@/views/server/components/hostChart.vue'
import { GetHostDashboard } from '@/api/asset'
export default {
  components: {
    hostChart,
  },
  name: 'FileSystem',
  props: {
    desktopType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      buyTimeData: [],
      gpuData: [],
    }
  },
  methods: {
    getHostDashboard() {
      this.loading = true
      GetHostDashboard().then(res => {
        this.buyTimeData = res.Data.buyTimeData
        this.gpuData = res.Data.gpuData
        this.loading = false
      })
    },
  },
  created() {
    this.getHostDashboard()
  },
  mounted() {},
}
</script>

<style scoped lang="less">
.cardcs {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
</style>
