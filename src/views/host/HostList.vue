<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="IP模糊查询">
                <a-input v-model:value="queryParam.ip" placeholder="IP" @pressEnter="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="服务器模糊查询">
                <a-input v-model:value="queryParam.hostname" placeholder="hostname" @pressEnter="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model:value="queryParam.status" allowClear placeholder="请选择">
                  <a-select-option value="10">全部</a-select-option>
                  <a-select-option value="0">已关机</a-select-option>
                  <a-select-option value="1">运行中</a-select-option>
                  <a-select-option value="2">已销毁</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="IDC">
                  <a-select v-model:value="queryParam.idc" allowClear placeholder="请选择">
                    <a-select-option value="ucloud-shanghai2-hybrid">ucloud-shanghai2-hybrid</a-select-option>
                    <a-select-option value="sh-office">sh-office</a-select-option>
                    <a-select-option value="ucloud-shanghai1-zhuanqiao">ucloud-shanghai1-zhuanqiao</a-select-option>
                    <a-select-option value="shanghai8-songjiang">shanghai8-songjiang</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="序列号模糊查询">
                  <a-input v-model:value="queryParam.productSerial" placeholder="序列号" @pressEnter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="机柜模糊查询">
                  <a-input v-model:value="queryParam.rack" placeholder="机柜" @pressEnter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="资产id模糊查询">
                  <a-input v-model:value="queryParam.property" placeholder="资产id" @pressEnter="handleSearch" />
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'left', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="handleSearch">查询</tx-button>
                <tx-button style="margin-left: 8px" type="primary" icon="plus" @click="handleAddEntry">
                  手动录入
                </tx-button>
                <a-upload :before-upload="beforeUpload" :showUploadList="false" accept=".xlsx,.xls">
                  <a-button class="ml-2" :loading="confirmLoading">
                    <a-icon type="upload" />
                    上传
                  </a-button>
                </a-upload>
                <tx-button
                  style="margin-left: 8px"
                  type="primary"
                  icon="download"
                  :loading="downloadLoading"
                  @click="handleDownload"
                >
                  导出
                </tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <a-table
        ref="table"
        :rowKey="record => record.id"
        :columns="columns"
        :data-source="hostData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'hostname'">
            {{ record.hostname }}
            <!-- <a @click="redirectToGrafana(record)" target="_blank">
              <area-chart-outlined/>
            </a> -->
            <router-link style="color: #1890ff" target="_blank" :to="`/db/server-monitor?id=${record.id}&type=2`">
              <area-chart-outlined />
            </router-link>
          </template>
          <template v-if="column.dataIndex == 'status'">
            <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleDetail(record)">详情</a>
            <a style="margin-left: 10px" @click="handleModifyEntry(record)">更新</a>
            <a-popconfirm title="确定删除？" @confirm="handleDeleteEntry(record)">
              <a style="margin-left: 10px; color: red">删除</a>
            </a-popconfirm>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <div v-if="record.kvmAssetList !== null && record.kvmAssetList.length > 0">
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsKvmAsset"
                  :data-source="record.kvmAssetList"
                  :pagination="false"
                  :rowKey="record => record.id"
                  class="kvmAssetTable"
                  style="width: 110%"
                >
                  <template #bodyCell="{ column, record, text }">
                    <template v-if="column.dataIndex === 'status'">
                      <a-badge :status="kvmStatusTypeFilter(text)" :text="kvmStatusFilter(text)" />
                    </template>
                    <template v-if="column.dataIndex === 'action'">
                      <a-dropdown
                        :trigger="['click']"
                        :visible="dropdownVisible[record.hostIp][record.id]"
                        @visible-change="changeDropdownVisible($event, record.hostIp, record.id)"
                        ref="assetDropdown"
                      >
                        <template #overlay>
                          <a-menu>
                            <a-menu-item :disabled="record.status !== 2">
                              <a-popconfirm
                                title="您确定要开机吗?"
                                ok-text="确认"
                                cancel-text="取消"
                                @confirm.stop="confirmKvmOperate(record, 'start', false)"
                              >
                                <a @click.stop="keepVisible(record.hostIp, record.id)" href="#">开机</a>
                              </a-popconfirm>
                            </a-menu-item>
                            <a-menu-item :disabled="record.status !== 1">
                              <a-popconfirm
                                title="您确定要关机吗?"
                                ok-text="确认"
                                cancel-text="取消"
                                @confirm="confirmKvmOperate(record, 'shutdown', false)"
                              >
                                <a @click="keepVisible(record.hostIp, record.id)" href="#">关机</a>
                              </a-popconfirm>
                            </a-menu-item>
                            <a-menu-item :disabled="record.status !== 1">
                              <a-popconfirm
                                ok-text="确认"
                                cancel-text="取消"
                                @confirm="confirmKvmOperate(record, 'shutdown', true)"
                              >
                                <template #title>
                                  <span style="color: red; font-weight: bold">您确定要强制关机吗?</span>
                                </template>
                                <a @click="keepVisible(record.hostIp, record.id)" href="#">强制关机</a>
                              </a-popconfirm>
                            </a-menu-item>
                            <a-menu-item :disabled="record.status === 2">
                              <a-popconfirm
                                title="您确定要重启吗?"
                                ok-text="确认"
                                cancel-text="取消"
                                @confirm="confirmKvmOperate(record, 'reboot', false)"
                              >
                                <a @click="keepVisible(record.hostIp, record.id)" href="#">重启</a>
                              </a-popconfirm>
                            </a-menu-item>
                            <a-menu-item :disabled="record.status === 2">
                              <a-popconfirm
                                ok-text="确认"
                                cancel-text="取消"
                                @confirm="confirmKvmOperate(record, 'reboot', true)"
                              >
                                <template #title>
                                  <span style="color: red; font-weight: bold">您确定要强制重启吗?</span>
                                </template>
                                <a @click="keepVisible(record.hostIp, record.id)" href="#">强制重启</a>
                              </a-popconfirm>
                            </a-menu-item>
                          </a-menu>
                        </template>
                        <a>
                          状态变更
                          <a-icon type="down" />
                        </a>
                      </a-dropdown>
                      <a-divider type="vertical" />
                      <a-popconfirm
                        ok-text="确认"
                        cancel-text="取消"
                        @confirm="confirmKvmOperate(record, 'shutdown', true)"
                      >
                        <template #title>
                          <span style="color: red; font-weight: bold">您确定要删除吗?</span>
                        </template>
                        <a style="color: red" @click="keepVisible(record.hostIp, record.id)" href="#">删除</a>
                      </a-popconfirm>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </div>
        </template>
      </a-table>
      <create-form
        ref="createModal"
        :visible="visible"
        :loading="confirmLoading"
        :model="mdl"
        @cancel="handleCancel"
        @ok="handleOk"
      />
      <step-by-step-modal ref="modal" @ok="handleOk" />
    </a-card>
    <a-drawer
      title="详情"
      placement="right"
      :closable="false"
      width="40%"
      :visible="detailIdVisible"
      @close="detailIdVisible = false"
    >
      <a-card :bordered="false" :model="detailData" ref="detailData">
        <a-descriptions :column="2" title="基础信息">
          <a-descriptions-item label="服务器名">{{ detailData.hostname }}</a-descriptions-item>
          <a-descriptions-item label="IP">{{ detailData.ip }}</a-descriptions-item>
          <a-descriptions-item label="机房">{{ detailData.idc }}</a-descriptions-item>
          <a-descriptions-item label="机柜">{{ detailData.rack }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge :status="statusTypeFilter(detailData.status)" :text="statusFilter(detailData.status)" />
          </a-descriptions-item>
          <a-descriptions-item label="资产id">{{ detailData.property }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="2" title="登录连接信息">
          <a-descriptions-item label="ipmiIp">{{ detailData.ipmiIp }}</a-descriptions-item>
          <a-descriptions-item label="ipmiUser">{{ detailData.ipmiUser }}</a-descriptions-item>
          <a-descriptions-item label="ipmi密码">{{ detailData.ipmiPassword }}</a-descriptions-item>
          <a-descriptions-item label="ansibleIp">{{ detailData.ansibleIp }}</a-descriptions-item>
          <a-descriptions-item label="ansiblePort">{{ detailData.ansiblePort }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="2">
          <a-descriptions-item label="购买时间">{{ detailData.buyTime }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ detailData.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ detailData.updatedAt }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 12px" />
        <a-descriptions :column="2" title="配置信息">
          <a-descriptions-item label="序列号">{{ detailData.productSerial }}</a-descriptions-item>
          <a-descriptions-item label="处理核心数">{{ detailData.processorCores }}</a-descriptions-item>
          <a-descriptions-item label="内存">{{ detailData.memTotal / 1024 }}</a-descriptions-item>
          <a-descriptions-item label="CPU线程数">{{ detailData.processorThreadsPerCore }}</a-descriptions-item>
          <a-descriptions-item label="Cpu类型">{{ detailData.processorModel }}</a-descriptions-item>
          <a-descriptions-item label="处理核心数">{{ detailData.processorCores }}</a-descriptions-item>
          <a-descriptions-item label="DNS名称">{{ detailData.dnsNameservers }}</a-descriptions-item>
          <a-descriptions-item label="厂商">{{ detailData.supplier }}</a-descriptions-item>
          <a-descriptions-item label="制作商">{{ detailData.productName }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ detailData.comment }}</a-descriptions-item>
          <a-descriptions-item label="Cpu类型">{{ detailData.processorModel }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="2" title="监控信息">
          <a-descriptions-item label="根目录磁盘使用率">
            {{ percentageFilter(detailData.diskRootUsage) }}
          </a-descriptions-item>
          <a-descriptions-item label="可用内存">{{ detailData.freeMem }}G</a-descriptions-item>
          <a-descriptions-item label="实际可用内存">{{ detailData.availableMem }}G</a-descriptions-item>
          <a-descriptions-item label="CPU平均负载(15分钟)">{{ detailData.perCpuAvgLoad }}</a-descriptions-item>
          <a-descriptions-item label="CPU分配率">
            {{ percentageFilter(detailData.cpuOversoldRate) }}
          </a-descriptions-item>
          <a-descriptions-item label="内存分配率">
            {{ percentageFilter(detailData.memOversoldRate) }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="2" title="标签信息">
          <a-descriptions-item label="是否有虚拟机">{{ detailData.needKvm }}</a-descriptions-item>
          <a-descriptions-item label="虚拟机数量">{{ detailData.kvmCount }}</a-descriptions-item>
          <a-descriptions-item label="所属业务">{{ detailData.business }}</a-descriptions-item>
          <a-descriptions-item label="服务环境">{{ detailData.env }}</a-descriptions-item>
          <a-descriptions-item label="过保情况">
            <a-badge
              :status="overWarrantyStatusTypeFilter(detailData.overWarrantyStatus)"
              :text="overWarrantyStatusFilter(detailData.overWarrantyStatus)"
            />
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-drawer>
    <a-modal
      title="服务器手动录入"
      :visible="serverAddEntryVisible"
      @ok="confirmServerAddEntry"
      width="800px"
      @cancel="serverAddEntryVisible = false"
    >
      <a-form-model
        ref="orderForm"
        :rules="serverRules"
        :model="serverTemp"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="服务器名" name="hostname">
          <a-input v-model:value="serverTemp.hostname" />
        </a-form-model-item>
        <a-form-model-item label="服务器IP" name="ip">
          <a-input v-model:value="serverTemp.ip" />
        </a-form-model-item>
        <a-form-model-item label="机房" name="idc">
          <a-select v-model:value="serverTemp.idc" placeholder="请选择机房">
            <a-select-option value="ucloud-shanghai2-hybrid">UCloud混合云</a-select-option>
            <a-select-option value="ucloud-shanghai2-public">UCloud云主机</a-select-option>
            <a-select-option value="ucloud-shanghai1-zhuanqiao">UCloud颛桥</a-select-option>
            <a-select-option value="shanghai8-songjiang">松江机房</a-select-option>
            <a-select-option value="aws-cn-north-1">AWS北京(cn-north-1)</a-select-option>
            <a-select-option value="aws-cn-northwest-1">AWS宁夏(cn-northwest-1)</a-select-option>
            <a-select-option value="aws-eu-west-1">AWS欧洲(爱尔兰)(eu-west-1)</a-select-option>
            <a-select-option value="aws-us-west-1">AWS美国西部(加利福尼亚北部)(us-west-1)</a-select-option>
            <a-select-option value="aws-us-west-2">AWS美国西部(俄勒冈)(us-west-2)</a-select-option>
            <a-select-option value="aws-ap-east-1">AWS亚太地区(香港)(ap-east-1)</a-select-option>
            <a-select-option value="aws-ap-south-1">AWS亚太地区(孟买)(ap-south-1)</a-select-option>
            <a-select-option value="aws-ap-southeast-1">AWS亚太地区(新加坡)(ap-southeast-1)</a-select-option>
            <a-select-option value="aws-ap-northeast-1">AWS亚太地区(东京)(ap-northeast-1)</a-select-option>
            <a-select-option value="aliyun-hehe-cn-hangzhou">阿里云(合合)</a-select-option>
            <a-select-option value="sh-office">上海云立方</a-select-option>
            <a-select-option value="aliyun-qxb-cn-hangzhou">阿里云(启信宝)</a-select-option>
            <a-select-option value="tencent-shanghai">腾讯云上海区域</a-select-option>
            <a-select-option value="tencent-tokyo">腾讯云东京区域</a-select-option>
            <a-select-option value="azure-cn-chinaeast2">微软云上海2</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="ipmiIp" name="ipmiIp">
          <a-input v-model:value="serverTemp.ipmiIp" />
        </a-form-model-item>
        <a-form-model-item label="ipmiUser" name="ipmiUser">
          <a-input v-model:value="serverTemp.ipmiUser" />
        </a-form-model-item>
        <a-form-model-item label="ipmi密码" name="ipmiPassword">
          <a-input v-model:value="serverTemp.ipmiPassword" />
        </a-form-model-item>
        <a-form-model-item label="机柜" name="rack">
          <a-input v-model:value="serverTemp.rack" />
        </a-form-model-item>
        <a-form-model-item label="ansible_ip" name="ansibleIp">
          <a-input v-model:value="serverTemp.ansibleIp" />
        </a-form-model-item>
        <a-form-model-item label="ansible_port" name="ansiblePort">
          <a-input v-model:value="serverTemp.ansiblePort" />
        </a-form-model-item>
        <a-form-model-item label="上架时间" name="buyTime">
          <a-input v-model:value="serverTemp.buyTime" />
        </a-form-model-item>
        <a-form-model-item label="供应商" name="supplier">
          <a-input v-model:value="serverTemp.supplier" />
        </a-form-model-item>
        <a-form-model-item label="备注" name="comment">
          <a-input v-model:value="serverTemp.comment" />
        </a-form-model-item>
        <a-form-model-item label="资产id" name="property">
          <a-input v-model:value="serverTemp.property" />
        </a-form-model-item>
        <a-form-model-item label="序列号" name="productSerial">
          <a-input v-model:value="serverTemp.productSerial" />
        </a-form-model-item>
        <a-form-model-item label="所属业务" name="business">
          <a-select v-model:value="serverTemp.business" placeholder="请选择业务">
            <a-select-option value="AIM运维">AIM运维</a-select-option>
            <a-select-option value="DG">DG</a-select-option>
            <a-select-option value="搜索(DG)">搜索(DG)</a-select-option>
            <a-select-option value="大数据">大数据</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="服务环境" name="env">
          <a-select v-model:value="serverTemp.env" placeholder="请选择环境">
            <a-select-option value="online">生产环境</a-select-option>
            <a-select-option value="test">测试环境</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="过保情况" name="overWarrantyStatus">
          <a-radio-group name="radioGroup" v-model:value="serverTemp.overWarrantyStatus" :default-value="1">
            <a-radio :value="1">未过保</a-radio>
            <a-radio :value="2">过保</a-radio>
            <a-radio :value="3">禁用</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!--  // 修改-->
    <a-modal
      title="物理机更新"
      :visible="serverModifyEntryVisible"
      @ok="confirmServerModifyEntry"
      width="800px"
      @cancel="serverModifyEntryVisible = false"
    >
      <a-form-model
        ref="orderForm"
        :rules="serverRules"
        :model="serverTemp"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="服务器名" name="hostname">
          <a-input v-model:value="serverTemp.hostname" />
        </a-form-model-item>
        <a-form-model-item label="服务器IP" name="ip">
          <a-input v-model:value="serverTemp.ip" />
        </a-form-model-item>
        <a-form-model-item label="机房" name="idc">
          <a-select v-model:value="serverTemp.idc" placeholder="请选择机房">
            <a-select-option value="ucloud-shanghai2-hybrid">UCloud混合云</a-select-option>
            <a-select-option value="ucloud-shanghai2-public">UCloud云主机</a-select-option>
            <a-select-option value="ucloud-shanghai1-zhuanqiao">UCloud颛桥</a-select-option>
            <a-select-option value="shanghai8-songjiang">松江机房</a-select-option>
            <a-select-option value="aws-cn-north-1">AWS北京(cn-north-1)</a-select-option>
            <a-select-option value="aws-cn-northwest-1">AWS宁夏(cn-northwest-1)</a-select-option>
            <a-select-option value="aws-eu-west-1">AWS欧洲(爱尔兰)(eu-west-1)</a-select-option>
            <a-select-option value="aws-us-west-1">AWS美国西部(加利福尼亚北部)(us-west-1)</a-select-option>
            <a-select-option value="aws-us-west-2">AWS美国西部(俄勒冈)(us-west-2)</a-select-option>
            <a-select-option value="aws-ap-east-1">AWS亚太地区(香港)(ap-east-1)</a-select-option>
            <a-select-option value="aws-ap-south-1">AWS亚太地区(孟买)(ap-south-1)</a-select-option>
            <a-select-option value="aws-ap-southeast-1">AWS亚太地区(新加坡)(ap-southeast-1)</a-select-option>
            <a-select-option value="aws-ap-northeast-1">AWS亚太地区(东京)(ap-northeast-1)</a-select-option>
            <a-select-option value="aliyun-hehe-cn-hangzhou">阿里云(合合)</a-select-option>
            <a-select-option value="sh-office">上海云立方</a-select-option>
            <a-select-option value="aliyun-qxb-cn-hangzhou">阿里云(启信宝)</a-select-option>
            <a-select-option value="tencent-shanghai">腾讯云上海区域</a-select-option>
            <a-select-option value="tencent-tokyo">腾讯云东京区域</a-select-option>
            <a-select-option value="azure-cn-chinaeast2">微软云上海2</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="ipmiIp" name="ipmiIp">
          <a-input v-model:value="serverTemp.ipmiIp" />
        </a-form-model-item>
        <a-form-model-item label="ipmiUser" name="ipmiUser">
          <a-input v-model:value="serverTemp.ipmiUser" />
        </a-form-model-item>
        <a-form-model-item label="ipmiIp" name="ipmiIp">
          <a-input v-model:value="serverTemp.ipmiIp" />
        </a-form-model-item>
        <a-form-model-item label="ipmi密码" name="ipmiPassword">
          <a-input v-model:value="serverTemp.ipmiPassword" />
        </a-form-model-item>
        <a-form-model-item label="机柜" name="rack">
          <a-input v-model:value="serverTemp.rack" />
        </a-form-model-item>
        <a-form-model-item label="ansible_ip" name="ansibleIp">
          <a-input v-model:value="serverTemp.ansibleIp" />
        </a-form-model-item>
        <a-form-model-item label="ansible_port" name="ansiblePort">
          <a-input v-model:value="serverTemp.ansiblePort" />
        </a-form-model-item>
        <a-form-model-item label="机柜" name="rack">
          <a-input v-model:value="serverTemp.rack" />
        </a-form-model-item>
        <a-form-model-item label="上架时间" name="buyTime">
          <a-input v-model:value="serverTemp.buyTime" />
        </a-form-model-item>
        <a-form-model-item label="供应商" name="supplier">
          <a-input v-model:value="serverTemp.supplier" />
        </a-form-model-item>
        <a-form-model-item label="备注" name="comment">
          <a-input v-model:value="serverTemp.comment" />
        </a-form-model-item>
        <a-form-model-item label="资产id" name="property">
          <a-input v-model:value="serverTemp.property" />
        </a-form-model-item>
        <a-form-model-item label="序列号" name="productSerial">
          <a-input v-model:value="serverTemp.productSerial" />
        </a-form-model-item>
        <a-form-model-item label="所属业务" name="business">
          <a-select v-model:value="serverTemp.business" placeholder="请选择业务">
            <a-select-option value="AIM运维">AIM运维</a-select-option>
            <a-select-option value="DG">DG</a-select-option>
            <a-select-option value="搜索(DG)">搜索(DG)</a-select-option>
            <a-select-option value="大数据">大数据</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="服务环境" name="env">
          <a-select v-model:value="serverTemp.env" placeholder="请选择环境">
            <a-select-option value="online">生产环境</a-select-option>
            <a-select-option value="test">测试环境</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="状态" name="status">
          <a-select v-model:value="serverTemp.status" allowClear placeholder="请选择">
            <a-select-option :value="0">已关机</a-select-option>
            <a-select-option :value="1">运行中</a-select-option>
            <a-select-option :value="2">已销毁</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="是否过保" name="overWarrantyStatus">
          <a-radio-group
            name="radioGroup"
            v-model:value="serverTemp.overWarrantyStatus"
            :default-value="serverTemp.overWarrantyStatus"
          >
            <a-radio :value="1">未过保</a-radio>
            <a-radio :value="2">过保</a-radio>
            <a-radio :value="3">禁用</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
// import StepByStepModal from './modules/StepByStepModal.vue'
import StepByStepModal from '@/views/server/modules/StepByStepModal.vue'
// import CreateForm from './modules/CreateForm.vue'
import CreateForm from '@/views/server/modules/CreateForm.vue'
import {
  CreateAssetHost,
  DeleteAssetHost,
  UpdateAssetHost,
  getAsseHostList,
  getAssetInfo,
  getAssetList,
  kvmOperate,
} from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'
import { approveOrder } from '@/api/workflow/order'
import { notification } from 'ant-design-vue'
import { getUserList } from '@/api/permission/user'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '服务器名',
    dataIndex: 'hostname',
  },
  {
    title: '机房',
    dataIndex: 'idc',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: '机柜',
    dataIndex: 'rack',
    sorter: true,
  },
  {
    title: '序列号',
    dataIndex: 'productSerial',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const columnsKvmAsset = [
  {
    title: '主机名',
    dataIndex: 'hostname',
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: 'CPU',
    dataIndex: 'cpu',
    customRender: ({ text }) => text + 'Core',
  },
  {
    title: '内存',
    dataIndex: 'memory',
    customRender: ({ text }) => text + 'G',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '负责人',
    dataIndex: 'principal',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '300px',
    scopedSlots: { customRender: 'action' },
  },
]

const statusMap = {
  0: {
    status: 'processing',
    text: '关闭',
  },
  1: {
    status: 'success',
    text: '运行中',
  },
  2: {
    status: 'error',
    text: '已关机',
  },
}

const envMap = {
  dev: '开发环境',
  test: '测试环境',
  pre: '预发布环境',
  online: '生产环境',
}
const regionMap = {
  'cn-hangzhou': '中国-杭州',
  'cn-shanghai': '中国-上海',
  'cn-hongkong': '中国-香港',
  'ap-shanghai-1': '上海一区',
  'ap-shanghai-2': '上海二区',
  'ap-shanghai-3': '上海三区',
  'ap-shanghai-4': '上海四区',
  'ap-shanghai-5': '上海五区',
  'ap-east-1': '亚太-香港',
  'ap-northeast-1': '亚太-东京',
  'ap-south-1': '亚太-孟买',
  'ap-southeast-1': '亚太-新加坡',
  'cn-north-1': '中国-北京',
  'cn-northeast-1': '中国-宁夏',
  'eu-west-1': '欧洲-爱尔兰',
  'sa-east-1': '南美-圣保罗',
  'us-west-1': '美西-加利福尼亚',
  'us-west-2': '美西-俄亥俄',
}
const pagination = {
  showTotal: total => `共 ${total} 条`,
}
const kvmStatusMap = {
  0: {
    status: 'processing',
    text: '关闭',
  },
  1: {
    status: 'success',
    text: '运行中',
  },
  2: {
    status: 'error',
    text: '已关机',
  },
  3: {
    status: 'processing',
    text: '关机中',
  },
  4: {
    status: 'processing',
    text: '开机中',
  },
  5: {
    status: 'processing',
    text: '重启中',
  },
  10: {
    status: 'processing',
    text: '准备中',
  },
}

export default {
  name: 'TableList',
  components: {
    CreateForm,
    StepByStepModal,
  },
  data() {
    this.columns = columns
    this.columnsKvmAsset = columnsKvmAsset
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      serverModifyEntryVisible: false,
      downloadLoading: false,
      dropdownVisible: {},
      assetOrgList: [],
      assetDepList: [],
      createData: { hosts: [] },
      assetCostUserList: [],
      hostData: [],
      // create model
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: '1', pageNo: 1, pageSize: 10 },
      allData: [],
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {
        ipmiUser: '',
        memAvailable: 0,
        processorModel: '',
        id: 0,
        processorCores: 0,
        status: true,
        ipmiPassword: '',
        memBuffer: 0,
        dateShutdown: 0,
        processorCount: 0,
        ip: '',
        needKvm: false,
        hostname: '',
        memCache: 0,
        idc: '',
        processorThreadsPerCore: 0,
        comment: '',
        dnsNameservers: '',
        rack: '',
        processorVcpus: 0,
        ansibleIp: '',
        costLastMonth: 0,
        osDistributionRelease: '',
        hostModel: '',
        ansiblePort: 0,
        memTotal: 0,
        supplier: '',
        productSerial: 0,
        osDistributionVersion: 0,
        systemVendor: 'Inspu',
        memUsed: 0,
        ansibleExtraVars: '',
        buyTime: '',
        osDistributionMajorVersion: 0,
        productName: '',
        ipmiIp: '',
        memFree: 0,
      },
      detailIdVisible: false,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
      serverRules: antdFormRulesFormat({
        hostname: [{ required: true, message: '填写服务器名', trigger: 'blur' }],
        ip: [{ required: true, message: '填写服务器IP', trigger: 'blur' }],
        idc: [{ required: true, message: '请选择机房信息', trigger: 'blur' }],
        key: [{ required: true, message: '填写服务器ID', trigger: 'blur' }],
        os: [{ required: true, message: '请选择操作系统', trigger: 'blur' }],
        cpu: [{ required: true, message: '请填写CPU核数', trigger: 'change' }],
        memory: [{ required: true, message: '请填写内存大小', trigger: 'change' }],
        principalEmails: [{ required: true, message: '请填负责人', trigger: 'blur' }],
        ansiblePort: [{ required: true, message: '请填写ansiblePort', trigger: 'blur' }],
      }),
      serverTemp: {
        ip: '',
        idc: '',
        ipmiIp: '',
        ipmiUser: '',
        ipmiPassword: '',
        rack: '',
        ansibleIp: '',
        ansiblePort: '',
        buyTime: '',
        supplier: '',
        comment: '',
        business: 'AIM运维',
        env: 'online',
        overWarrantyStatus: 1,
      },
    }
  },
  created() {
    this.loadHostData({})
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(this.localUser.split('@')[0])
  },
  unmounted() {
    removeWatermark()
  },
  methods: {
    redirectToGrafana(record) {
      // const url = 'https://grafana-autom.intsig.net/d/SVtwZjIVk/node-exporter?var-machine_room=' + record.idc + '&var-node=' + record.ip + ':9100&orgId=1'
      window.open(record.grafanaUrl, '_blank')
    },
    handleSearch() {
      this.queryParam.pageNo = 1
      this.loadHostData(this.queryParam)
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination }
      pager.current = pagination.current
      this.pagination = pager
      this.queryParam.pageSize = pagination.pageSize
      this.queryParam.pageNo = pagination.current
      this.queryParam.sortField = sorter.field
      this.queryParam.sortOrder = sorter.order
      this.loadHostData(this.queryParam)
    },
    // 加载数据
    loadHostData(queryParam) {
      if (Object.keys(queryParam).length === 0) {
        queryParam = this.queryParam
      }
      this.downloadqueryParam = JSON.parse(JSON.stringify(queryParam))
      // this.loading = true
      getAsseHostList(queryParam).then(res => {
        // 记录所有虚拟机下拉状态
        this.dropdownVisible = {}
        let pagination = { ...this.pagination }
        if (res.Data.hasOwnProperty('data')) {
          if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
            this.hostData = []
            pagination.total = 0
            pagination.pageNo = 1
            pagination.pageSize = 10
            this.pagination = pagination
            this.loading = false
          } else {
            res.Data.data.forEach(item => {
              let kvmDropdownVisible = {}
              if (item.kvmAssetList !== null) {
                item.kvmAssetList.forEach(kvmItem => {
                  kvmDropdownVisible[kvmItem.id] = false
                })
              }
              this.dropdownVisible[item.ip] = kvmDropdownVisible
            })
            this.hostData = res.Data.data
            if (res.Data.totalCount <= 10) {
              pagination = false
              this.pagination = pagination
              this.loading = false
            } else {
              pagination.total = res.Data.totalCount
              this.pagination = pagination
              this.loading = false
              if (this.pagination.showTotal === undefined) {
                this.$set(this.pagination, 'showTotal', total => `共 ${total} 条`)
              }
            }
          }
        } else {
          this.hostData = []
          pagination.total = 0
          pagination.pageNo = 1
          pagination.pageSize = 10
          this.pagination = pagination
          this.loading = false
        }
      })
    },
    // 下拉框切换
    changeDropdownVisible(dropdownVisible, recordIp, kvmRecordID) {
      this.dropdownVisible[recordIp][kvmRecordID] = dropdownVisible
    },
    keepVisible(recordIp, kvmRecordID) {
      this.dropdownVisible[recordIp][kvmRecordID] = true
    },
    // 更新虚拟机状态
    updateAssetStatus(recordIp, kvmRecordId, status) {
      if (status !== undefined) {
        this.hostData.forEach(item => {
          if (item.ip === recordIp) {
            item.kvmAssetList.forEach(kvmItem => {
              if (kvmItem.id === kvmRecordId) {
                kvmItem.status = status
              }
            })
          }
        })
      } else {
        getAssetInfo(kvmRecordId).then(response => {
          const newStatus = response.Data.status
          this.hostData.forEach(item => {
            if (item.ip === recordIp) {
              item.kvmAssetList.forEach(kvmItem => {
                if (kvmItem.id === kvmRecordId) {
                  kvmItem.status = newStatus
                }
              })
            }
          })
        })
      }
    },
    // kvm虚拟机操作
    confirmKvmOperate(kvmRecord, operation, force) {
      const originStatus = kvmRecord.status
      const sendData = {
        source: 'host',
        operation: operation,
        hostname: kvmRecord.hostname,
        uuid: kvmRecord.uuid,
        ip: kvmRecord.ip,
        hostIp: kvmRecord.hostIp,
        status: kvmRecord.status,
      }
      if (force) {
        sendData.force = true
      }
      this.updateAssetStatus(kvmRecord.hostIp, kvmRecord.id, 10)
      kvmOperate(sendData).then(response => {
        const msg = response.Data.message
        if (msg === 'success') {
          notification.success({
            message: '操作成功',
          })
          // 修改状态
          if (operation === 'shutdown') {
            this.updateAssetStatus(kvmRecord.hostIp, kvmRecord.id, 3)
          } else if (operation === 'start') {
            this.updateAssetStatus(kvmRecord.hostIp, kvmRecord.id, 4)
          } else {
            this.updateAssetStatus(kvmRecord.hostIp, kvmRecord.id, 5)
          }
          let statusInterval = setInterval(() => {
            this.hostData.forEach(item => {
              if (item.ip === kvmRecord.hostIp) {
                item.kvmAssetList.forEach(kvmItem => {
                  if (kvmItem.status === 1 || kvmItem.status === 2) {
                    clearInterval(statusInterval)
                  }
                })
              }
            })
            this.updateAssetStatus(kvmRecord.hostIp, kvmRecord.id)
          }, 15000)
          // 开始循环获取数据
        } else {
          this.updateAssetStatus(kvmRecord.hostIp, kvmRecord.id, originStatus)
          notification.error({
            message: '操作失败:' + msg,
          })
        }
      })
      this.dropdownVisible[kvmRecord.hostIp][kvmRecord.id] = false
    },
    envFilter(type) {
      return envMap[type]
    },
    regionFilter(type) {
      return regionMap[type]
    },
    statusFilter(type) {
      if (type === 1) {
        return '运行中'
      } else if (type === 2) {
        return '已销毁'
      } else {
        return '已关机'
      }
    },
    overWarrantyStatusFilter(type) {
      if (type === 1) {
        return '未过保'
      } else if (type === 2) {
        return '已过保'
      } else if (type === 3) {
        return '已禁用'
      } else {
        return '未知'
      }
    },
    overWarrantyStatusTypeFilter(type) {
      if (type === 1) {
        return 'success'
      } else if (type === 2) {
        return 'warning'
      } else if (type === 3) {
        return 'error'
      } else {
        return 'default'
      }
    },
    kvmStatusFilter(type) {
      return kvmStatusMap[type]?.text || type
    },
    kvmStatusTypeFilter(type) {
      return kvmStatusMap[type]?.status || type
    },
    statusTypeFilter(type) {
      if (type === 1) {
        return 'success'
      } else {
        return 'error'
      }
    },
    percentageFilter(num) {
      const text = num * 100

      return text.toString() + '%'
    },
    handleAdd() {
      this.$router.push({ path: '/workflow/server-add' })
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    confirmServerAddEntry() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.serverAddEntryVisible = false
          this.createData.hosts.push(this.serverTemp)
          CreateAssetHost(this.createData).then(response => {
            this.createData.hosts = []
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code === 400) {
              notification.error({
                message: '创建失败',
              })
            } else {
              notification.success({
                message: '创建成功',
              })
            }
          })
        }
      })
    },
    createHosts() {
      CreateAssetHost(this.createData).then(response => {
        if (response === undefined) {
          notification.error({
            message: '创建失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 400) {
          notification.error({
            message: '创建失败',
          })
        } else {
          notification.success({
            message: '创建成功',
          })
        }
      })
      this.createData.hosts = []
    },
    handleModifyEntry(data) {
      this.serverTemp = data
      this.serverModifyEntryVisible = true
    },
    handleDeleteEntry(data) {
      DeleteAssetHost(data.id).then(response => {
        this.$refs.table.refresh()
        if (response === undefined) {
          notification.error({
            message: '删除失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 400) {
          notification.error({
            message: '删除失败',
          })
        } else {
          notification.success({
            message: '删除成功',
          })
        }
      })
    },
    beforeUpload(file) {
      this.createData.hosts = []
      const reader = new FileReader()
      reader.onload = evt => {
        const data = evt.target.result
        const workbook = XLSX.read(data, { type: 'binary' })
        const jsonData = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]])
        this.createData.hosts.push(...jsonData)
        this.createHosts(this.createData)
      }
      reader.readAsBinaryString(file)
      return false
    },
    confirmServerModifyEntry() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.serverModifyEntryVisible = false
          UpdateAssetHost(this.serverTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '更新失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code === 400) {
              notification.error({
                message: '更新失败',
              })
            } else {
              notification.success({
                message: '更新成功',
              })
            }
          })
        }
      })
    },
    handleInfo(record) {
      this.$router.push({ path: '/server/asset-info', query: { id: record.id } })
    },
    handleEditTag(record) {
      let routeUrl = this.$router.resolve({ path: '/workflow/server-modify-tag', query: { assetId: record.id } })
      window.open(routeUrl.href, '_blank')
    },
    handleEditConfig(record) {
      let routeUrl = this.$router.resolve({ path: '/workflow/server-modify-config', query: { assetId: record.id } })
      window.open(routeUrl.href, '_blank')
    },
    handleDownload() {
      this.downloadLoading = true
      const downloadColumns = [
        {
          title: '服务器名',
          dataIndex: 'hostname',
        },
        {
          title: '机房',
          dataIndex: 'idc',
        },
        {
          title: 'IP',
          dataIndex: 'ip',
        },
        {
          title: '序列号',
          dataIndex: 'productSerial',
        },
        {
          title: 'IpmiIp',
          dataIndex: 'ipmiIp',
        },
        {
          title: 'ipmi用户',
          dataIndex: 'ipmiUser',
        },
        {
          title: 'Ipmi密码',
          dataIndex: 'ipmiPassword',
        },
        {
          title: 'CPU',
          dataIndex: 'processorCores',
        },
        {
          title: '内存',
          dataIndex: 'memTotal',
        },
        {
          title: '机柜',
          dataIndex: 'rack',
        },
        {
          title: '厂商',
          dataIndex: 'supplier',
        },
        {
          title: '制作商',
          dataIndex: 'productName',
        },
        {
          title: '购买时间',
          dataIndex: 'buyTime',
        },
        {
          title: '备注',
          dataIndex: 'comment',
        },
        {
          title: '资产ID',
          dataIndex: 'property',
        },
        {
          title: '状态',
          dataIndex: 'status',
        },
      ]
      this.downloadqueryParam.pageSize = 10000
      getAsseHostList(this.downloadqueryParam).then(async res => {
        if (res.Data.hasOwnProperty('data')) {
          const XLSX = await loadXLSX()
          const result = res.Data.data
          this.downloadLoading = false
          result.forEach(function (item, index) {
            if (item.status === 0) {
              item.status = '已关机'
            } else if (item.status === 2) {
              item.status = '已销毁'
            } else {
              item.status = '运行中'
            }
          })
          const tableData = this.transData(downloadColumns, result)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Host')
          XLSX.writeFile(wb, 'Host.xlsx')
        } else {
          this.downloadtableList = []
        }
      })
    },
    transData(columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map(item => {
        return obj.keys.map(key => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    handleOk() {
      const form = this.$refs.createModal.form
      this.confirmLoading = true
      form.validateFields((errors, values) => {
        if (!errors) {
          if (values.id > 0) {
            // 修改 e.g.
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then(res => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('修改成功')
            })
          } else {
            // 新增
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then(res => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('新增成功')
            })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    handleDel(record) {
      let routeUrl = this.$router.resolve({ path: '/workflow/server-del', query: { assetId: record.id } })
      window.open(routeUrl.href, '_blank')
    },
    handleDelMore() {
      const idData = JSON.stringify(this.selectedRowKeys)
      let routeUrl = this.$router.resolve({ path: '/workflow/server-del', query: { assetId: idData } })
      window.open(routeUrl.href, '_blank')
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // 详细信息 相关接口
    handleDetail(record) {
      this.hostData.forEach(item => {
        if (item.id === record.id) {
          this.detailData = item
        }
      })
      this.detailIdVisible = true
    },
  },
}
</script>

<style lang="less" scoped>
.kvmAssetTable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 10px;
    position: relative;
  }

  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    text-align: center !important;
  }
  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
