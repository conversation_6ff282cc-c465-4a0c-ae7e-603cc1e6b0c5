<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-06-13 09:56:27
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-12 17:42:52
 * @FilePath: \cloud_web\src\views\list\AssetCheck.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <a-row style="margin-bottom: 8px">
      <a-col :span="24">
        <a-select ref="select" v-model:value="company" style="width: 120px">
          <a-select-option value="合合">合合</a-select-option>
          <a-select-option value="CS">CS</a-select-option>
          <a-select-option value="大数据">大数据</a-select-option>
          <a-select-option value="DG">DG</a-select-option>
        </a-select>
        <a-select ref="select" v-model:value="env" style="width: 120px; margin-left: 16px">
          <a-select-option value="online">正式环境</a-select-option>
          <a-select-option value="test">测试环境</a-select-option>
        </a-select>
        <a-select ref="select" v-model:value="server" style="width: 200px; margin-left: 16px">
          <a-select-option value="ucloud-shanghai2-hybrid">UCloud混合云</a-select-option>
        </a-select>
        <a-button style="margin-left: 16px" type="primary" @click="queryData">查询</a-button>
      </a-col>
    </a-row>
    <a-card title="总览">
      <a-row>
        <a-col :span="12">
          <a-spin :spinning="spinning">
            <a-card style="width: calc(100% - 16px); height: 420px" title="物理机数量">
              <a-empty style="height: 100%" v-if="!machineNum || !machineNum.length" />

              <RingChart v-if="machineNum && machineNum.length" :filedName="['value', 'name']" :chartData="machineNum"
                :compRef="'machineNum'" :customText="'总数'" />
            </a-card>
          </a-spin>
        </a-col>
        <a-col :span="12">
          <a-spin :spinning="spinning">
            <a-card style="height: 420px" title="即将禁用物理机列表">
              <a-empty style="height: 100%" v-if="!banList || !banList.length" />
              <a-table :pagination="false" class="banList" v-else :columns="columns" :data-source="banList"
                :scroll="{ y: 280 }">
              </a-table>
            </a-card>
          </a-spin>

        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-spin :spinning="spinning">
            <a-card style="width: calc(100% - 16px); height: 420px;" title="主存储">
              <a-empty style="height: 100%" v-if="!mainStorage || !mainStorage.rate" />
              <div style="width: 100%; height: 260px">
                <LiquidChart v-if="mainStorage && mainStorage.rate" :chartData="mainStorage" :compRef="'mainStorage'"
                  :customText="'已使用'" />
              </div>
              <p style="text-align: center;font-size: 12px;margin-bottom: 6px;" v-if="mainStorage && mainStorage.rate">
              <p style="margin-bottom: 6px;">可用容量/总量：{{ mainStorage.usage }}G/{{ mainStorage.total }}G</p>
              <p style="margin-bottom: 6px;">预计 <span style="color:#0052D9">可继续创建{{ mainStorage.kvmCount }}台云主机</span>
              </p>
              <p style="color: #9E9E9E;margin-bottom: 6px;">统计可用余量时已剔除禁用和资源不足的物理机</p>
              </p>
            </a-card>
          </a-spin>

        </a-col>
        <a-col :span="12">
          <a-spin :spinning="spinning">

            <a-card style="height: 420px" title="vlan容量">
              <a-empty style="height: 100%" v-if="!vlanInfo || !vlanInfo.length" />
              <div style="display: flex; width: 100%;height: 100%;">
                <div style=" width: 70%;height: 100%;">
                  <RingChart v-if="vlanInfo && vlanInfo.length" :filedName="['value', 'name']" :chartData="vlanInfo"
                    :compRef="'vlanInfo'" :customText="'剩余可用总数'" />
                </div>
                <div style=" width: 30%;height: 100%;">
                  <a-table :pagination="false" class="banList" v-if="vlanInfo && vlanInfo.length" :columns="vlanColumns"
                    :data-source="vlanInfo" :scroll="{ y: 280 }">
                  </a-table>
                </div>
              </div>
            </a-card>
          </a-spin>

        </a-col>
      </a-row>
    </a-card>
    <a-card title="容量监测">
      <a-tabs v-model:activeKey="activeKey" @change="tabchange">
        <a-tab-pane key="主存储" tab="主存储">
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="width: calc(100% - 16px); height: 420px;" title="主存储使用情况">
                  <a-empty style="height: 100%" v-if="!storageUsageInfo || !storageUsageInfo.length" />
                  <BarGroup v-if="storageUsageInfo && storageUsageInfo.length" :chartData="storageUsageInfo"
                    :compRef="'storageUsageInfo'" :filedName="['value', 'category', 'name']" />
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
          <a-row>
          <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="height: 420px" title="主存储余量">
                  <a-empty style="height: 100%" v-if="!storageAllowance || !storageAllowance.length" />
                  <BarStack v-if="storageAllowance && storageAllowance.length"
                    :filedName="['value', 'name', 'category']" :chartData="storageAllowance"
                    :compRef="'storageAllowance'" :customText="'剩余可用总数'" />
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="width: 100% ; " title="物理机列表">
                  <a-table :columns="storageColumns" :data-source="storageMachineList">
                    <template #bodyCell="{ column, text, record }">
                      <template v-if="column.dataIndex === 'allocateRate'">
                        <span style="font-size: 12px">可分配：{{ record.availableCount }}G</span>
                        <p style="display: flex">
                          <a-progress :showInfo="false" :percent="text" size="small" />
                          <span style="font-size: 12px;display: inline-block;width: 120px;margin-left:8px">{{
         text.toFixed(2).padStart(4, '0')}}%</span>
                        </p>
                      </template>
                    </template>
                  </a-table>
                </a-card>
              </a-spin>

            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="CPU" tab="CPU">
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="width: calc(100% - 16px); height: 420px;" title="CPU使用情况">
                  <a-empty style="height: 100%" v-if="!cpuUsageInfo || !cpuUsageInfo.length" />
                  <BarGroup v-if="cpuUsageInfo && cpuUsageInfo.length" :chartData="cpuUsageInfo"
                    :compRef="'cpuUsageInfo'" :filedName="['value', 'category', 'name']" />
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
          <a-row>
          <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="height: 420px" title="CPU余量">
                  <a-empty style="height: 100%" v-if="!cpuAllowance || !cpuAllowance.length" />
                  <BarStack v-if="cpuAllowance && cpuAllowance.length" :filedName="['value', 'name', 'category']"
                    :chartData="cpuAllowance" :compRef="'cpuAllowance'" :customText="'剩余可用总数'" />
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">

                <a-card style="width: 100% ; " title="物理机列表">
                  <a-table :columns="cpuColumns" :data-source="cpuMachineList">
                    <template #bodyCell="{ column, text, record }">
                      <template v-if="column.dataIndex === 'allocateRate'">
                        <span style="font-size: 12px">可分配：{{ record.availableCount }}G</span>
                        <p style="display: flex">
                          <a-progress :showInfo="false" :percent="text" size="small" />
                          <span style="font-size: 12px;display: inline-block;width: 120px;margin-left:8px">{{
        text }}%</span>
                        </p>
                      </template>
                    </template>
                  </a-table>
                </a-card>
              </a-spin>

            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="内存" tab="内存">
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="width: calc(100% - 16px); height: 420px;" title="内存使用情况">
                  <a-empty style="height: 100%" v-if="!memUsageInfo || !memUsageInfo.length" />
                  <BarGroup v-if="memUsageInfo && memUsageInfo.length" :chartData="memUsageInfo"
                    :compRef="'memUsageInfo'" :filedName="['value', 'category', 'name']" />
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
          <a-row>
          <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="height: 420px" title="内存余量">
                  <a-empty style="height: 100%" v-if="!memAllowance || !memAllowance.length" />
                  <BarStack v-if="memAllowance && memAllowance.length" :filedName="['value', 'name', 'category']"
                    :chartData="memAllowance" :compRef="'memAllowance'" :customText="'剩余可用总数'" />
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">
                <a-card style="width: 100% ; " title="物理机列表">
                  <a-table :columns="memColumns" :data-source="memMachineList">
                    <template #bodyCell="{ column, text, record }">
                      <template v-if="column.dataIndex === 'allocateRate'">
                        <span style="font-size: 12px">可分配：{{ record.availableCount }}G</span>
                        <p style="display: flex">
                          <a-progress :showInfo="false" :percent="text" size="small" />
                          <span style="font-size: 12px;display: inline-block;width: 120px;margin-left:8px">{{
        text }}%</span>
                        </p>
                      </template>
                    </template>
                  </a-table>
                </a-card>
              </a-spin>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="磁盘" tab="磁盘">
          <a-tabs v-model:activeKey="diskType" @change="tabchange">
            <a-tab-pane key="SSD" tab="固态硬盘">
              <a-row>
                <a-col :span="24">
                  <a-spin :spinning="spinning">

                    <a-card style="height: 420px" title="固态硬盘余量">
                      <a-empty style="height: 100%" v-if="!SsdAllowance || !SsdAllowance.length" />
                      <BarStack v-if="SsdAllowance && SsdAllowance.length" :filedName="['value', 'name', 'category']"
                        :chartData="SsdAllowance" :compRef="'SsdAllowance'" :customText="'剩余可用总数'" />
                    </a-card>
                  </a-spin>

                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-spin :spinning="spinning">

                    <a-card style="width: 100% ; " title="物理机列表">
                      <a-table :columns="ssdColumns" :data-source="ssdMachineList">
                      </a-table>
                    </a-card>
                  </a-spin>

                </a-col>

              </a-row>
            </a-tab-pane>
            <a-tab-pane key="HDD" tab="机械硬盘">
              <a-row>
                <a-col :span="24">
                  <a-spin :spinning="spinning">

                    <a-card style="height: 420px" title="机械硬盘余量">
                      <a-empty style="height: 100%" v-if="!HddAllowance || !HddAllowance.length" />
                      <BarStack v-if="HddAllowance && HddAllowance.length" :filedName="['value', 'name', 'category']"
                        :chartData="HddAllowance" :compRef="'HddAllowance'" :customText="'剩余可用总数'" />
                    </a-card>
                  </a-spin>

                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-spin :spinning="spinning">

                    <a-card style="width: 100% ; " title="物理机列表">
                      <a-table :columns="hddColumns" :data-source="HddMachineList">
                      </a-table>
                    </a-card>
                  </a-spin>

                </a-col>

              </a-row>

            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
        <a-tab-pane key="网络流量" tab="网络流量">
          <a-row>
            <a-col :span="24">
              <a-spin :spinning="spinning">

                <a-card style="width: 100% ; " title="物理机列表">
                  <a-table :columns="netColumns" :data-source="netMachineList">
                  </a-table>
                </a-card>
              </a-spin>

            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </page-header-wrapper>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { assetOverview, moduleOverview } from '@/api/asset'
import { storageColumns, cpuColumns, memColumns, ssdColumns, hddColumns, netColumns } from '@/views/server/modules/assetCheckData.js'
import RingChart from '@/views/server/modules/ringChart.vue'
import LiquidChart from '@/views/server/modules/liquidChart.vue'
import BarGroup from '@/views/server/modules/barGroup.vue'
import BarStack from '@/views/server/modules/barStack.vue'
const company = ref('合合')
const env = ref('online')
const server = ref('ucloud-shanghai2-hybrid')
const allData = ref(null)
const vlanColumns = [
  {
    title: '网段',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '剩余可用',
    dataIndex: 'value',
    key: 'value',
  },
]
const columns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '云主机数量',
    dataIndex: 'kvmCount',
    key: 'kvmCount',
  }, {
    title: '距禁用天数',
    dataIndex: 'days',
    key: 'days',
  }
]
const spinning = ref(false)
const activeKey = ref('主存储')
const diskType = ref('SSD')
const machineNum = ref([]) //物理机数量
const mainStorage = ref(null) //主存储
const vlanInfo = ref([]) // vlan 容量
const banList = ref([])//即将禁用物理机列表
const storageUsageInfo = ref([])//主存储使用情况
const storageAllowance = ref([])//主存储余量
const storageMachineList = ref([])//主存储物理机列表
const cpuUsageInfo = ref([])//CPU使用情况
const cpuAllowance = ref([])//CPU余量
const cpuMachineList = ref([])//cpu物理机列表
const memUsageInfo = ref([])//内存使用情况
const memAllowance = ref([])//内存余量
const memMachineList = ref([])//内存物理机列表
const netMachineList = ref([])//网络物理机列表
const SsdAllowance = ref([])//ssd余量
const ssdMachineList = ref([])//ssd物理机
const HddAllowance = ref([])//hdd余量
const HddMachineList = ref([])//hdd物理机
const queryData = () => {
  spinning.value = true
  machineNum.value = []
  mainStorage.value = []
  vlanInfo.value = []
  banList.value = []
  storageUsageInfo.value = []
  storageAllowance.value = []
  storageMachineList.value = []
  cpuUsageInfo.value = []
  cpuAllowance.value = []
  cpuMachineList.value = []
  memUsageInfo.value = []
  memAllowance.value = []
  memMachineList.value = []
  netMachineList.value = []
  SsdAllowance.value = []
  ssdMachineList.value = []
  HddAllowance.value = []
  HddMachineList.value = []

  const params = {
    business: company.value,
    env: env.value,
    idc: server.value,
  }
  assetOverview(params)
    .then(res => {
      console.log(res.Data,'res.Data');
      allData.value = res.Data
      machineNum.value = allData.value.hostStatus
      mainStorage.value = allData.value.diskRoot
      vlanInfo.value = allData.value.vlan
      allData.value.hostToBeDisabled ?
        banList.value = allData.value.hostToBeDisabled :
        banList.value = []
      tabchange()
      spinning.value = false

    })
    .catch(() => {
    })

}
const tabchange = () => {
  const obj = {
    business: company.value,
    env: env.value,
    idc: server.value,
    module: activeKey.value,
  }
  if (activeKey.value == '磁盘') {
    obj.diskType = diskType.value
  }
  spinning.value = true
  moduleOverview(obj).then(res => {
    console.log(res.Data, 'moduleOverview');
  spinning.value = false
    switch (activeKey.value) {
      case '主存储':
        storageUsageInfo.value = res.Data.usage
        storageAllowance.value = res.Data.remain
        storageMachineList.value = res.Data.hostCapacity
        break
      case 'CPU':
        cpuUsageInfo.value = res.Data.usage
        cpuAllowance.value = res.Data.remain
        cpuMachineList.value = res.Data.hostCapacity
        break
      case '内存':
        memUsageInfo.value = res.Data.usage
        memAllowance.value = res.Data.remain
        memMachineList.value = res.Data.hostCapacity
        break
      case '网络流量':
        netMachineList.value = res.Data.hostCapacity
        break
      case '磁盘':
        if (diskType.value == 'SSD') {
          SsdAllowance.value = res.Data.remain
          ssdMachineList.value = res.Data.hostCapacity
        } else if (diskType.value == 'HDD') {
          HddAllowance.value = res.Data.remain
          HddMachineList.value = res.Data.hostCapacity
        }
        break

    }

  }).catch(err => {
    console.log(err, 'err');
  })
}
queryData()
</script>
<style lang="less" scoped>
.banList {
  /deep/ .ant-table-cell {
    padding: 6px !important;
  }
}
</style>
