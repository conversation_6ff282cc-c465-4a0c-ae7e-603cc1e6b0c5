<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.searchKey" placeholder="IP" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 6) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button type="primary" style="margin-left: 8px" @click="unUsedSearch">未使用资源查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button type="primary" style="margin-left: 8px"
                  @click="()=>(this.diskAssignableVisible=true)"
                >磁盘推荐</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'size'">
            <a-badge :text="sizeFilter(text)" />
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleDetail(record)">详情</a>
          </template>
          <template v-if="column.dataIndex === 'usage'">
            <a-row>
              <a-col :span="16">
                <a-progress
                  stroke-linecap="square"
                  :strokeColor="record.usage > 0.8 ? 'red' : 'rgba(31, 197, 197, 1)'"
                  :percent="record.usage === 0 ? 0: (record.usage * 100).toFixed(2)"
                  :show-info="false"
                />
              </a-col>
              <a-col :span="1"></a-col>
              <a-col :span="7">
                <p class="field" style="font-size: 12px">
                  {{ record.usage === 0 ? 0 : (record.usage * 100).toFixed(2) }}%
                </p>
              </a-col>
            </a-row>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a>分区</a>
          <div v-if="record.hostDiskPart && record.hostDiskPart.length > 0">
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsHostPart"
                  :data-source="record.hostDiskPart"
                  :pagination="pagination"
                  :rowKey="record => record.id"
                  class="diskPartTable"
                ></a-table>
              </a-col>
            </a-row>
          </div>
        </template>

      </s-table>
      <a-drawer
        title="详情"
        placement="right"
        :closable="false"
        width="40%"
        :visible="detailIdVisible"
        @close="detailIdVisible = false"
      >
        <a-card :bordered="false" :model="detailData" ref="detailData">
          <a-descriptions :column="2">
            <a-descriptions-item label="物理机IP">{{ detailData.ip }}</a-descriptions-item>
            <a-descriptions-item label="磁盘路径">{{ detailData.path }}</a-descriptions-item>
            <a-descriptions-item label="磁盘大小">{{ detailData.size }}</a-descriptions-item>
            <a-descriptions-item label="磁盘类型">{{ detailData.type }}</a-descriptions-item>
            <a-descriptions-item label="磁盘状态">{{ detailData.status }}</a-descriptions-item>
            <a-descriptions-item label="磁盘类型">{{ detailData.ipmiIp }}</a-descriptions-item>
            <a-descriptions-item label="wwn">{{ detailData.wwn }}</a-descriptions-item>
            <a-descriptions-item label="sector_size">{{ detailData.sectorSize }}</a-descriptions-item>
            <a-descriptions-item label="usage">{{ detailData.usage }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
        <!--      <li-->
        <!--        style="width: 100%; display: flex; justify-content: space-between; margin-bottom: 8px"-->
        <!--        v-for="i in dailyCostColumn"-->
        <!--        :key="i.dataIndex"-->
        <!--        :value="i.dataIndex"-->
        <!--      >-->
        <!--        <span>{{ i.title }}</span>-->
        <!--        <a-switch v-model:checked="i.show" @change="switchChange(...arguments, i)" />-->
        <!--      </li>-->
        <div v-for="hostDiskPart in hostDiskPartDetailData">
          <a-card :bordered="false" :model="hostDiskPart">
            <a-descriptions :column="2">
              <a-descriptions-item label="物理机IP">{{ hostDiskPart.ip }}</a-descriptions-item>
              <a-descriptions-item label="分区路径">{{ hostDiskPart.path }}</a-descriptions-item>
              <a-descriptions-item label="分区大小">{{ hostDiskPart.size }}</a-descriptions-item>
              <a-descriptions-item label="分区ID">{{ hostDiskPart.uuid }}</a-descriptions-item>
              <a-descriptions-item label="磁盘大小">{{ hostDiskPart.diskSize }}</a-descriptions-item>
              <a-descriptions-item label="磁盘状态">{{ hostDiskPart.free }}</a-descriptions-item>
              <a-descriptions-item label="使用状态">{{ hostDiskPart.isUsed }}</a-descriptions-item>
              <a-descriptions-item label="开始位置">{{ hostDiskPart.start }}</a-descriptions-item>
              <a-descriptions-item label="结束位置">{{ hostDiskPart.end }}</a-descriptions-item>
              <a-descriptions-item label="sector_size">{{ hostDiskPart.sectorSize }}</a-descriptions-item>
              <a-descriptions-item label="usage">{{ hostDiskPart.usage }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
        </div>
      </a-drawer>

      <a-modal :visible="diskAssignableVisible" width="80%" title="磁盘推荐" :closable="false">
        <div>
          <a-form layout="inline" style="margin-bottom: 8px">
            <a-row :gutter="12">
              <a-col :md="8" :sm="24">
                <a-form-item label="磁盘">
                  <a-input-group compact>
                    <a-select v-model:value="diskAssignableQuery.type">
                      <a-select-option value="SSD">SSD</a-select-option>
                      <a-select-option value="HDD">HDD</a-select-option>
                    </a-select>
                    <a-input-number v-model:value="diskAssignableQuery.size" :min="100" style="width: 70%"
                      :formatter="value => `${value}G`" :parser="value => value.replace('G', '')"
                    />
                  </a-input-group>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <tx-button type="primary" @click="queryDiskAssignable">查询</tx-button>
              </a-col>
            </a-row>
          </a-form>
          <s-table
            ref="diskAssignableTable"
            :rowKey="(record) => record.id"
            :columns="columnsDiskAssignable"
            :data="listDiskAssignable"
            height="350"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex === 'usageAfter'">
                <a-row>
                  <a-col :span="16">
                    <a-progress
                      stroke-linecap="square"
                      :strokeColor="record.usageAfter > 0.8 ? 'red' : 'rgba(31, 197, 197, 1)'"
                      :percent="record.usageAfter === 0 ? 0: (record.usageAfter * 100).toFixed(2)"
                      :show-info="false"
                    />
                  </a-col>
                  <a-col :span="1"></a-col>
                  <a-col :span="7">
                    <p class="field" style="font-size: 12px">
                      {{ record.usageAfter === 0 ? 0 : (record.usageAfter * 100).toFixed(2) }}%
                    </p>
                  </a-col>
                </a-row>
              </template>
            </template>
          </s-table>
        </div>
        <template #footer>
          <tx-button key="submit" type="primary" @click="() => (this.diskAssignableVisible = false)">关闭</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { getAssetInfo, getHostDiskInfo, getHostDiskList, listDiskAssignable } from '@/api/asset'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: '物理机IP',
    dataIndex: 'ip',
    sorter: true
  },
  {
    title: '磁盘路径',
    dataIndex: 'path'
  },
  {
    title: '磁盘大小',
    dataIndex: 'size',
    sorter: true
  },
  {
    title: '磁盘类型',
    dataIndex: 'type',
    scopedSlots: { customRender: 'type' },
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
    sorter: true
  },
  {
    title: '使用率',
    dataIndex: 'usage',
    width: '200px',
    scopedSlots: { customRender: 'usage' },
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' }
  }
]

const columnsHostPart = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: '分区路径',
    dataIndex: 'path'
  },
  {
    title: '分区ID',
    dataIndex: 'uuid'
  },
  {
    title: '分区大小',
    dataIndex: 'size',
    sorter: true
  },
  {
    title: '磁盘大小',
    dataIndex: 'diskSize',
    sorter: true
  },
  {
    title: '使用状态',
    dataIndex: 'isUsed',
    scopedSlots: { customRender: 'isUsed' },
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'free',
    scopedSlots: { customRender: 'status' },
    sorter: true
  }
]

const columnsDiskAssignable = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: 'IP',
    dataIndex: 'ip'
  },
  {
    title: '磁盘路径',
    dataIndex: 'path'
  },
  {
    title: '起始位置',
    dataIndex: 'start'
  },
  {
    title: '结束位置',
    dataIndex: 'end'
  },
  {
    title: '分配后使用率',
    dataIndex: 'usageAfter',
    width: '200px',
    scopedSlots: { customRender: 'usageAfter' }
  },
  {
    title: '分配后保留段大小',
    dataIndex: 'remainingSize',
    customRender: ({ text }) => text + 'G'
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  hideOnSinglePage: true
}

export default {
  name: 'TableList',
  components: {
    STable
  },
  data() {
    this.columns = columns
    this.columnsHostPart = columnsHostPart
    this.columnsDiskAssignable = columnsDiskAssignable
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      // create model
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return getHostDiskList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      hostDiskPartDetailData: {},
      detailIdVisible: false,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
      serverTemp: {
        ip: '',
        idc: '',
        ipmiIp: '',
        ipmiUser: '',
        ipmiPassword: '',
        rack: '',
        ansibleIp: '',
        ansiblePort: '',
        buyTime: '',
        supplier: '',
        comment: ''
      },
      diskAssignableVisible: false,
      diskAssignableQuery: {
        type: 'SSD',
        size: 100
      },
      // 加载数据方法 必须为 Promise 对象
      listDiskAssignable: parameter => {
        return listDiskAssignable(this.diskAssignableQuery).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  created() {
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    unUsedSearch() {
      this.queryParam.action = 'unUsed'
      this.$refs.table.refresh()
      this.queryParam.action = ''
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date())
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // 详细信息 相关接口
    handleDetail(record) {
      // this.allData.data.forEach(item => {
      //   if (item.id === record.id) {
      //     this.detailData = item
      //   }
      // })
      getHostDiskInfo(record.id).then(response => {
        this.detailData = response.Data
        if (response.Data && response.Data.hostDiskPart) {
          this.hostDiskPartDetailData = response.Data.hostDiskPart
        }
        this.detailIdVisible = true
      })
    },
    // 详细信息 相关接口
    queryDiskAssignable() {
      if (Math.floor(this.diskAssignableQuery.size / 100) * 100 !== this.diskAssignableQuery.size) {
        alert('磁盘大小应为100的整倍数')
        return
      }
      this.diskAssignableVisible = true
      this.$refs.diskAssignableTable.refresh(true)
    }
  }
}
</script>

<style lang="less" scoped>
.diskPartTable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
