<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="5" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ip" placeholder="IP" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="IDC">
                <a-select v-model:value="queryParam.idc" allowClear placeholder="请选择">
                  <a-select-option value="ucloud-shanghai2-hybrid">ucloud-shanghai2-hybrid</a-select-option>
                  <a-select-option value="sh-office">sh-office</a-select-option>
                  <a-select-option value="ucloud-shanghai1-zhuanqiao">ucloud-shanghai1-zhuanqiao</a-select-option>
                  <a-select-option value="shanghai8-songjiang">shanghai8-songjiang</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="机柜">
                <a-input v-model:value="queryParam.rack" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="资产ID">
                <a-input v-model:value="queryParam.propertyId" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="序列号">
                <a-input v-model:value="queryParam.serialNumber" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model:value="queryParam.status" allowClear placeholder="请选择">
                  <a-select-option value="10">全部</a-select-option>
                  <a-select-option value="1">使用中</a-select-option>
                  <a-select-option value="2">已下线</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button type="primary" style="margin-left: 8px" @click="handleCreate">新增</tx-button>
                <tx-button
                  type="primary"
                  style="margin-left: 8px"
                  icon="download"
                  :loading="downloadLoading"
                  @click="handleDownload"
                >
                  导出
                </tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'status'">
            <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a style="margin-right: 5px" @click="handleDetail(record)">详情</a>
            <a style="margin-right: 5px" @click="handleUpdate(record)">更新</a>
            <a-popconfirm title="确定删除？" @confirm="handleDelete(record)">
              <a style="margin-left: 10px; color: red">删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <a-drawer
        title="详情"
        placement="right"
        :closable="false"
        width="40%"
        :visible="detailIdVisible"
        @close="detailIdVisible = false"
      >
        <a-card :bordered="false" :model="detailData" ref="detailData">
          <a-descriptions :column="2">
            <a-descriptions-item label="IP">{{ detailData.ip }}</a-descriptions-item>
            <a-descriptions-item label="机房">{{ detailData.idc }}</a-descriptions-item>
            <a-descriptions-item label="机柜">{{ detailData.rack }}</a-descriptions-item>
            <a-descriptions-item label="资产Id">{{ detailData.propertyId }}</a-descriptions-item>
            <a-descriptions-item label="序列号">{{ detailData.serialNumber }}</a-descriptions-item>
            <a-descriptions-item label="品牌型号">{{ detailData.brandModel }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ detailData.comment }}</a-descriptions-item>
            <a-descriptions-item label="状态">{{ detailData.status }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-drawer>
      <a-modal
        title="交换机信息更新"
        :visible="updateVisible"
        :confirm-loading="updateLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model
          ref="updateForm"
          :model="detailData"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
        >
          <a-form-model-item label="Ip">
            <a-input v-model:value="detailData.ip" />
          </a-form-model-item>
          <a-form-model-item label="Idc" name="idc">
            <a-input v-model:value="detailData.idc" />
          </a-form-model-item>
          <a-form-model-item label="机柜" name="rack">
            <a-input v-model:value="detailData.rack" />
          </a-form-model-item>
          <a-form-model-item label="资产ID" name="propertyId">
            <a-input v-model:value="detailData.propertyId" />
          </a-form-model-item>
          <a-form-model-item label="序列号" name="serialNumber">
            <a-input v-model:value="detailData.serialNumber" />
          </a-form-model-item>
          <a-form-model-item label="品牌型号" name="brandModel">
            <a-input v-model:value="detailData.brandModel" />
          </a-form-model-item>
          <a-form-model-item label="备注" name="comment">
            <a-input v-model:value="detailData.comment" />
          </a-form-model-item>
          <a-form-model-item label="状态" name="status">
            <a-select v-model:value="detailData.status" placeholder="状态">
              <a-select-option value="1">使用中</a-select-option>
              <a-select-option value="2">已下线</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal
        title="交换机新增"
        :visible="createVisible"
        :confirm-loading="createLoading"
        @ok="handleCreateOk"
        @cancel="handleCreateCancel"
      >
        <a-form-model
          ref="createForm"
          :model="detailData"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
        >
          <a-form-model-item label="Ip">
            <a-input v-model:value="detailData.ip" />
          </a-form-model-item>
          <a-form-model-item label="Idc" name="idc">
            <a-input v-model:value="detailData.idc" />
          </a-form-model-item>
          <a-form-model-item label="机柜" name="rack">
            <a-input v-model:value="detailData.rack" />
          </a-form-model-item>
          <a-form-model-item label="资产ID" name="propertyId">
            <a-input v-model:value="detailData.propertyId" />
          </a-form-model-item>
          <a-form-model-item label="序列号" name="serialNumber">
            <a-input v-model:value="detailData.serialNumber" />
          </a-form-model-item>
          <a-form-model-item label="品牌型号" name="brandModel">
            <a-input v-model:value="detailData.brandModel" />
          </a-form-model-item>
          <a-form-model-item label="备注" name="comment">
            <a-input v-model:value="detailData.comment" />
          </a-form-model-item>
          <a-form-model-item label="状态" name="status">
            <a-select v-model:value="detailData.status" placeholder="状态">
              <a-select-option value="1">使用中</a-select-option>
              <a-select-option value="2">已下线</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { createSwitch, deleteSwitch, getHostSwitchList, updateSwitch } from '@/api/asset'
import CreateForm from '@/views/server/modules/CreateForm.vue'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'IP',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: '机房',
    dataIndex: 'idc',
  },
  {
    title: '机柜',
    dataIndex: 'rack',
  },
  {
    title: '资产ID',
    dataIndex: 'propertyId',
  },
  {
    title: '序列号',
    dataIndex: 'serialNumber',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      createVisible: false,
      updateVisible: false,
      assetIdcList: [],
      // create model
      visible: false,
      confirmLoading: false,
      createLoading: false,
      downloadLoading: false,
      updateLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        this.downloadqueryParam.pageSize = 10
        this.downloadqueryParam.pageNo = 1
        return getHostSwitchList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              this.downloadqueryParam.pageSize = res.Data.totalCount
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      rules: antdFormRulesFormat({
        ip: [{ required: true, message: '请确认物理机IP', trigger: 'change' }],
        idc: [{ required: true, message: '请确认机房', trigger: 'change' }],
        rack: [{ required: true, message: '请确认机柜信', trigger: 'change' }],
        user: [{ required: true, message: '请确认用户', trigger: 'change' }],
        password: [{ required: true, message: '请确认密码', trigger: 'change' }],
      }),
      detailIdVisible: false,
      userpRression: false,
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
  },
  methods: {
    handleDownload() {
      this.downloadLoading = true
      const downloadColumns = [
        {
          title: 'ID',
          dataIndex: 'id',
        },
        {
          title: 'IP',
          dataIndex: 'ip',
        },
        {
          title: '机房',
          dataIndex: 'idc',
        },
        {
          title: '机柜',
          dataIndex: 'rack',
        },
        {
          title: '资产ID',
          dataIndex: 'propertyId',
        },
        {
          title: '序列号',
          dataIndex: 'serialNumber',
        },
        {
          title: '备注',
          dataIndex: 'comment',
        },
        {
          title: '状态',
          dataIndex: 'status',
        },
      ]
      getHostSwitchList(this.downloadqueryParam)
        .then(async res => {
          if (res.Data.hasOwnProperty('data')) {
            const XLSX = await loadXLSX()
            const tableData = this.transData(downloadColumns, res.Data.data)
            const ws = XLSX.utils.aoa_to_sheet(tableData)
            const wb = XLSX.utils.book_new()
            this.downloadLoading = false
            XLSX.utils.book_append_sheet(wb, ws, 'HostSwitch')
            XLSX.writeFile(wb, 'HostSwitch.xlsx')
          } else {
            this.downloadtableList = []
          }
        })
        .catch((this.downloadLoading = false))
    },
    transData(columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map(item => {
        return obj.keys.map(key => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    statusFilter(type) {
      if (type === 1) {
        return '使用中'
      } else if (type === 2) {
        return '已下线'
      }
    },
    statusTypeFilter(type) {
      if (type === 1) {
        return 'success'
      } else {
        return 'error'
      }
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // 详细信息 相关接口
    handleDetail(record) {
      this.allData.data.forEach(item => {
        if (item.id === record.id) {
          this.detailData = item
        }
      })
      this.detailIdVisible = true
      // getHostSwitchInfo(record.id).then(response => {
      //   this.detailData = response.Data
      //   this.detailIdVisible = true
      // })
    },
    handleUpdate(record) {
      this.allData.data.forEach(item => {
        if (item.id === record.id) {
          this.detailData = item
        }
      })
      this.updateVisible = true
    },
    handleUpdateCancel() {
      this.updateVisible = false
    },
    handleUpdateOk() {
      this.createLoading = true

      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          updateSwitch(this.detailData)
            .then(response => {
              if (response === undefined) {
                notification.error({
                  message: '更新失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (response.Code === 400) {
                notification.error({
                  message: '更新失败',
                })
              } else {
                notification.success({
                  message: '更新成功',
                })
              }
              this.updateVisible = false
              this.createLoading = false
            })
            .catch((this.createLoading = false))
        }
      })
    },
    handleCreate(record) {
      this.createVisible = true
    },
    handleCreateCancel() {
      this.createVisible = false
    },
    handleCreateOk() {
      this.createLoading = true
      antdFormValidate(this.$refs.createForm, valid => {
        if (valid) {
          createSwitch(this.detailData)
            .then(response => {
              if (response === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (response.Code === 400) {
                notification.error({
                  message: '创建失败',
                })
              } else {
                notification.success({
                  message: '创建成功',
                })
              }
              this.createVisible = false
              this.$refs.table.refresh(true)
              this.createLoading = false
            })
            .catch((this.createLoading = false))
        }
      })
    },
    handleDelete() {
      deleteSwitch(this.detailData).then(response => {
        if (response === undefined) {
          notification.error({
            message: '删除失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 400) {
          notification.error({
            message: '删除失败',
          })
        } else {
          notification.success({
            message: '删除成功',
          })
        }
        this.$refs.table.refresh(true)
      })
    },
  },
}
</script>
