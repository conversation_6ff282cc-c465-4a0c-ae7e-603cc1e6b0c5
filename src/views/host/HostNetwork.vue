<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ipv4" placeholder="IP" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="MAC地址">
                <a-input
                  v-model:value="queryParam.macAddress"
                  placeholder="MAC地址"
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="物理机IP">
                <a-input v-model:value="queryParam.ip" placeholder="物理机IP" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 6) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button type="primary" style="margin-left: 8px" @click="handleCreate">新增</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'action'">
            <a style="margin-right: 5px" @click="handleDetail(record)">详情</a>
            <a style="margin-right: 5px" @click="handleUpdate(record)">更新</a>
            <a @click="handleDelete(record)">删除</a>
          </template>
        </template>
      </s-table>
      <a-drawer
        title="详情"
        placement="right"
        :closable="false"
        width="40%"
        :visible="detailIdVisible"
        @close="detailIdVisible = false"
      >
        <a-card :bordered="false" :model="detailData" ref="detailData">
          <a-descriptions :column="2">
            <a-descriptions-item label="物理机IP">{{ detailData.ip }}</a-descriptions-item>
            <a-descriptions-item label="Ipv4">{{ detailData.ipv4 }}</a-descriptions-item>
            <a-descriptions-item label="Mac地址">{{ detailData.macAddress }}</a-descriptions-item>
            <a-descriptions-item label="网卡设备">{{ detailData.device }}</a-descriptions-item>
            <a-descriptions-item label="使用状态">{{ detailData.active }}</a-descriptions-item>
            <a-descriptions-item label="其他ip">{{ detailData.ipv4Secondaries }}</a-descriptions-item>
            <a-descriptions-item label="Mtu">{{ detailData.mtu }}</a-descriptions-item>
            <a-descriptions-item label="Speed">{{ detailData.dpeed }}</a-descriptions-item>
            <a-descriptions-item label="网卡类型">{{ detailData.type }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-drawer>
      <a-modal
        title="网卡信息更新"
        :visible="updateVisible"
        :confirm-loading="updateLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model
          ref="updateForm"
          :model="detailData"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
        >
          <a-form-model-item label="物理机IP">
            <a-input v-model:value="detailData.ip" disabled />
          </a-form-model-item>
          <a-form-model-item label="Ip" name="ipv4">
            <a-input v-model:value="detailData.ipv4" />
          </a-form-model-item>
          <a-form-model-item label="Mac地址" name="macAddress">
            <a-input v-model:value="detailData.macAddress" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal
        title="网卡新增"
        :visible="createVisible"
        :confirm-loading="createLoading"
        @ok="handleCreateOk"
        @cancel="handleCreateCancel"
      >
        <a-form-model
          ref="createForm"
          :model="detailData"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
        >
          <a-form-model-item label="物理机IP" name="ip">
            <a-input v-model:value="detailData.ip" />
          </a-form-model-item>
          <a-form-model-item label="Ip" name="ipv4">
            <a-input v-model:value="detailData.ipv4" />
          </a-form-model-item>
          <a-form-model-item label="Mac地址" name="macAddress">
            <a-input v-model:value="detailData.macAddress" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { createNetwork, deleteNetwork, getHostNetworkInfo, getHostNetworkList, updateNetwork } from '@/api/asset'
import CreateForm from '@/views/server/modules/CreateForm.vue'

const columns = [
  {
    title: '物理机IP',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: 'Ipv4地址',
    dataIndex: 'ipv4',
  },
  {
    title: 'Mac地址',
    dataIndex: 'macAddress',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      createVisible: false,
      updateVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      // create model
      visible: false,
      confirmLoading: false,
      createLoading: false,
      updateLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return getHostNetworkList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      rules: antdFormRulesFormat({
        ip: [{ required: true, message: '请确认物理机IP', trigger: 'change' }],
        ipv4: [{ required: true, message: '请确认IP', trigger: 'change' }],
        macAddress: [{ required: true, message: '请确认Mac地址', trigger: 'change' }],
      }),
      detailIdVisible: false,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
      serverTemp: {
        ip: '',
        idc: '',
        ipmiIp: '',
        ipmiUser: '',
        ipmiPassword: '',
        rack: '',
        ansibleIp: '',
        ansiblePort: '',
        buyTime: '',
        supplier: '',
        comment: '',
      },
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(this.localUser.split('@')[0])
  },
  methods: {
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // 详细信息 相关接口
    handleDetail(record) {
      // this.allData.data.forEach(item => {
      //   if (item.id === record.id) {
      //     this.detailData = item
      //   }
      // })
      getHostNetworkInfo(record.id).then(response => {
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
    handleUpdate(record) {
      getHostNetworkInfo(record.id).then(response => {
        this.detailData = response.Data
        this.updateVisible = true
      })
    },
    handleUpdateCancel() {
      this.updateVisible = false
    },
    handleUpdateOk() {
      this.createLoading = true

      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          updateNetwork(this.detailData)
            .then(response => {
              this.updateVisible = false
              this.createLoading = false
            })
            .catch((this.createLoading = false))
        }
      })
    },
    handleCreate(record) {
      this.createVisible = true
    },
    handleCreateCancel() {
      this.createVisible = false
    },
    handleCreateOk() {
      this.createLoading = true
      antdFormValidate(this.$refs.createForm, valid => {
        if (valid) {
          createNetwork(this.detailData)
            .then(response => {
              this.createVisible = false
              this.$refs.table.refresh(true)
              this.createLoading = false
            })
            .catch((this.createLoading = false))
        }
      })
    },
    handleDelete() {
      deleteNetwork(this.detailData).then(response => {
        this.$refs.table.refresh(true)
      })
    },
  },
}
</script>
