<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-12-17 10:56:39
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-02 10:15:14
 * @FilePath: \cloud_web\src\views\middleware\comp\pages.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <!-- <p>
    修改备注请前往 权限管理--模块管理
    <router-link to="/permission/routes">点击前往</router-link>
  </p> -->
  <FlexItem :items="userMiddleWareMenu"></FlexItem>
</template>
<script setup>
import { userMiddleWare } from '@/api/middleWare/index'
import { ref, reactive, defineProps } from 'vue'
import FlexItem from '../comp/flexItems.vue'
const { pageName } = defineProps({
  pageName: String,
})
let userMiddleWareMenu = ref({})
const getMenus = () => {
  userMiddleWare({ menuType: pageName })
    .then(res => {
      console.log(res, 'res')
      if (res.Data) {
        userMiddleWareMenu.value = res.Data
      }
    })
    .catch(err => {
      console.log(err, 'err')
    })
}
getMenus()
</script>
<style lang="less" scoped></style>
