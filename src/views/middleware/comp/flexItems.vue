<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-12-16 11:41:20
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-31 10:25:21
 * @FilePath: \cloud_web\src\views\middleware\comp\flexItems.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <a-card :bordered="false" style="width: 100%; margin-bottom: 16px">
      <template #title>
        <img style="width: 24px; height: 24px" src="/vendor-cdn/@img/online.png" alt="" />
        Online
      </template>
      <template v-for="i in items.onlineEnv">
        <a-card-grid @click="lintTo(i.routeUrl)" class="itemContainer">
          <p style="margin-bottom: 8px">
            <b>{{ i.middlewareName }}</b>
          </p>
          <p class="itemInfo">备注：{{ i.comment }}</p>
        </a-card-grid>
      </template>
    </a-card>
    <a-card :bordered="false" style="width: 100%; margin-bottom: 16px">
      <template #title>
        <img style="width: 24px; height: 24px" src="/vendor-cdn/@img/test.png" alt="" />
        Test
      </template>
      <template v-for="i in items.testEnv">
        <a-card-grid @click="lintTo(i.routeUrl)" class="itemContainer">
          <p style="margin-bottom: 8px">
            <b>{{ i.middlewareName }}</b>
          </p>
          <p class="itemInfo" :title="i.comment">备注： {{ i.comment }}</p>
        </a-card-grid>
      </template>
    </a-card>
  </div>
</template>
<script setup>
import { ref, defineProps } from 'vue'
import { useRouter } from 'vue-router'
const { items } = defineProps({
  items: Object,
})
const router = useRouter()
console.log(router, 'rrrr')
console.log(items, 'iiiiiiiiiiiiii')

const lintTo = path => {
  console.log(path, 'ppp')
  window.open(path)
}
</script>
<style lang="less" scoped>
.itemContainer {
  width: 23%;
  padding: 12px;
  min-height: 80px;
  margin: 8px;
  border-radius: 8px;

  .itemInfo {
    width: 100%;
    overflow: hidden;
    color: #000000d9;
    margin-bottom: 0;
  }
}
/deep/.ant-card-head {
  padding: 0;
}
.ant-card {
  background-color: transparent;
}
.ant-card-grid {
  background-color: #fff;
}
</style>
