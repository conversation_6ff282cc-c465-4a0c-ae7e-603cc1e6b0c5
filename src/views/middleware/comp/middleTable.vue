<template>
  <a-button type="primary" style="float: right; margin-bottom: 14px" @click="modify('add')">
    <template #icon><plus-outlined /></template>
    添加
  </a-button>
  <a-table :pagination="pagination" @change="handleTableChange" :columns="columns" :data-source="data">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <span
          style="display: inline-block; width: 40px; text-align: center; color: #40a9ff; cursor: pointer"
          @click="modify('edit', record)"
        >
          修改
        </span>
        <span style="display: inline-block; width: 40px; text-align: center; color: red; cursor: pointer">
          <a-popconfirm title="确定删除吗?" ok-text="Yes" cancel-text="No" @confirm="modify('del', record)">
            删除
          </a-popconfirm>
        </span>
      </template>
      <template v-if="column.dataIndex === 'users'">
        <a-select
          :disabled="true"
          v-model:value="record.users"
          :options="principalListMini"
          mode="multiple"
          style="width: 100%"
          placeholder="负责人可输入或选择"
        ></a-select>
      </template>
    </template>
  </a-table>
  <a-modal :maskClosable="false" @cancel="closeModal" v-model:visible="editVis" :title="modalTitle" @ok="handleOk">
    <a-form
      ref="formRef"
      name="custom-validation"
      v-bind="layout"
      :model="formState"
      :rules="rules"
      @validate="handleValidate"
    >
      <a-form-item :rules="[{ required: true }]" has-feedback label="名称" name="middlewareName">
        <a-input v-model:value="formState.middlewareName" />
      </a-form-item>
      <a-form-item :rules="[{ required: true }]" has-feedback label="url" name="routeUrl">
        <a-input v-model:value="formState.routeUrl" />
      </a-form-item>
      <a-form-item :rules="[{ required: true }]" has-feedback label="备注" name="comment">
        <a-input v-model:value="formState.comment" />
      </a-form-item>
      <a-form-item :rules="[{ required: true }]" has-feedback label="环境" name="env">
        <a-select v-model:value="formState.env">
          <a-select-option label="测试" value="test">测试</a-select-option>
          <a-select-option label="生产" value="online">生产</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :rules="[{ required: true }]" has-feedback label="权限人员" name="users">
        <a-select
          v-model:value="formState.users"
          :options="principalListMini"
          mode="multiple"
          style="width: 362px"
          placeholder="负责人可输入或选择"
          @search="handleSearch"
        ></a-select>
      </a-form-item>
      <a-form-item :rules="[{ required: true }]" has-feedback label="类型" name="type">
        <a-select v-model:value="formState.type">
          <a-select-option label="icron" value="icron">icron</a-select-option>
          <a-select-option label="irmq" value="irmq">irmq</a-select-option>
          <a-select-option label="logAlert" value="logAlert">logAlert</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { getUserEmailList, getUserList } from '@/api/permission/user'
import { middlewareQuery, createMiddleWare, updateMiddleWare, deleteMiddleWare } from '@/api/middleWare/index'
import { ref, reactive } from 'vue'
let scrollPage = reactive(1)
let treePageSize = reactive(10)
let valueData = reactive('')
const columns = [
  {
    title: '名称',
    dataIndex: 'middlewareName',
    key: 'middlewareName',
    width: 100,
  },
  {
    title: 'URL',
    dataIndex: 'routeUrl',
    key: 'routeUrl',
  },
  {
    title: '备注',
    dataIndex: 'comment',
    key: 'comment',
    width: 160,
  },
  {
    title: '权限用户',
    dataIndex: 'users',
    key: 'users',
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
  },
  {
    title: '环境',
    dataIndex: 'env',
    key: 'env',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
  },
]
const modalTitle = ref('新建中间件')
const formRef = ref()
const principalListMini = ref([])
const principalList = ref([])

const getUserBaseInfo = () => {
  getUserEmailList().then(res => {
    for (var i = 0, len = res.Data.items.length; i < len; i++) {
      const user = {}
      user.value = res.Data.items[i].key
      user.label = res.Data.items[i].key + '(' + res.Data.items[i].value + ')'
      principalList.value.push(user)
      principalListMini.value.push(user)
    }
  })
}
getUserBaseInfo()
const uniqueDicts = arr => {
  const uniqueDictsSet = new Set()
  return arr.filter(item => {
    const dictString = JSON.stringify(item)
    return uniqueDictsSet.has(dictString) ? false : uniqueDictsSet.add(dictString)
  })
}
const handleSearch = val => {
  valueData = val
  if (!val) {
    this.getUserBaseInfo()
  } else {
    principalListMini.value = []
    scrollPage = 1
    principalList.value.forEach(item => {
      if (item.label.indexOf(val) >= 0) {
        if (!principalListMini.value.includes(item.label)) {
          principalListMini.value.push(item)
        }
      }
    })
    principalListMini.value = uniqueDicts(principalListMini.value.slice(0, 50))
  }
}

const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 14,
  },
}
const formState = ref({
  middlewareName: '',
  routeUrl: '',
  comment: '',
  users: [],
  type: '',
  env: '',
})
const pagination = ref({
  pageSize: 10,
  current: 1,
  total: 0,
})
const data = ref([])
const editVis = ref(false)
const modify = (type, r) => {
  switch (type) {
    case 'del':
      console.log(r, 'rrr')
      deleteMiddleWare({
        id: r.id,
      })
        .then(res => {
          console.log(res, 'ressss')
          noc.notice.ok({
            message: '已删除',
          })
          getList()
        })
        .catch(err => {
          noc.notice.warning({
            message: '错误',
          })
          console.log(err, 'err')
        })
      break
    case 'edit':
      editVis.value = true
      modalTitle.value = '修改中间件'
      formState.value = JSON.parse(JSON.stringify(r))
      break
    case 'add':
      editVis.value = true
      modalTitle.value = '新建中间件'
      formState.value = { middlewareName: '', routeUrl: '', comment: '', users: [], type: '', env: '' }
      console.log('add')
      break
  }
}
const getList = () => {
  middlewareQuery({
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
  }).then(res => {
    console.log(res, 'res')
    data.value = res.Data.data
    console.log(pagination.value, 'ppp')
    pagination.value.pageSize = res.Data.pageSize
    pagination.value.current = res.Data.pageNo
    pagination.value.total = res.Data.totalCount
  })
}
const handleTableChange = params => {
  console.log(params, 'ppp')
  console.log(pagination.value, 'vvv')
  pagination.value = params
  getList()
}
getList()
const closeModal = () => {
  formRef.value.resetFields()
  editVis.value = false
}
const handleOk = async () => {
  try {
    const values = await formRef.value.validateFields()
    console.log('Success:', values)
    if (modalTitle.value == '新建中间件') {
      createMiddleWare(formState.value).then(res => {
        console.log(res, '创建success ')
        noc.notice.ok({
          message: '创建成功',
        })
        formRef.value.resetFields()
        editVis.value = false
        getList()
      })
    } else {
      updateMiddleWare(formState.value).then(res => {
        console.log(res, '创建success ')
        noc.notice.ok({
          message: '更新成功',
        })
        formRef.value.resetFields()
        debugger
        editVis.value = false
        getList()
      })
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
</script>
<style lang="less" scoped></style>
