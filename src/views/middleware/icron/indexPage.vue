<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-12-20 16:05:41
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-23 17:54:19
 * @FilePath: \cloud_web\src\views\middleware\icron\indexPage.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <Pages :pageName="'icron'"></Pages>
  </page-header-wrapper>
</template>
<script setup>
import { ref, reactive } from 'vue'
import Pages from '../comp/pages.vue'
</script>
<style lang="less" scoped></style>
