<template>
  <page-header-wrapper>
    <FlexItem :items="userMiddleWareMenu"></FlexItem>
  </page-header-wrapper>
</template>
<script setup>
import { ref, reactive } from 'vue'
import FlexItem from './comp/flexItems.vue'
const test = ref('1111')
let userMiddleWareMenu = ref({})
const getMenus = () => {
  let menus = JSON.parse(localStorage.getItem('menus'))
  function menuToTreeData(menus) {
    if (noc.isArray(menus)) {
      menus.forEach(item => {
        item.label = item.name
        item.value = item.path
        if (item.children) {
          menuToTreeData(item.children)
        }
      })
    }
  }
  menuToTreeData(menus)
  menus.forEach(item => {
    if (item.name == '中间件') {
      userMiddleWareMenu.value = item
    }
  })
  console.log(userMiddleWareMenu.value, 'usermiddleWareMenu')
  console.log(menus, 'mmmm')
}
getMenus()
</script>
<style lang="less" scoped></style>
