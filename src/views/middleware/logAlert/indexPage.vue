<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-12-26 14:37:46
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-31 10:26:07
 * @FilePath: \cloud_web\src\views\middleware\logAlert\indexPage.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <Pages :pageName="'logAlert'"></Pages>
  </page-header-wrapper>
</template>
<script setup>
import { ref, reactive } from 'vue'
import Pages from '../comp/pages.vue'
</script>
<style lang="less" scoped></style>
