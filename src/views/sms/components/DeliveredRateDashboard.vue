<template>
  <div>
    <a-row type="flex">
      <a-col :span="6" >
        <p style="vertical-align: middle;font-size: 20px;font-weight: bold; float: left">账号：</p>
        <a-select default-value="" @change="handleChangeAccount" v-model:value="queryData.account" show-search style="width: 80%" allowClear>
          <a-select-option v-for="i in this.deliveredRateAccountList" :value="i" :key="i">
            {{ i }}
          </a-select-option>
        </a-select>
      </a-col>
      <a-col :span="7">
        <p style="vertical-align: middle;font-size: 20px;font-weight: bold; float: left">模板：</p>
        <a-select default-value="" @change="handleChangeTemplate" v-model:value="queryData.template" show-search style="width: 80%" allowClear>
          <a-select-option v-for="i in this.deliveredRateTemplateList" :value="i" :key="i">
            {{ i }}
          </a-select-option>
        </a-select>
      </a-col>
      <tx-button type="primary" @click="handleChange" style="margin-left: 15px">查询</tx-button>
    </a-row>

    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="drLoading" title="模板到达率">
          <a-skeleton v-if="drLoading"/>
          <DeliveredRateChart v-else :compRef="'deliveredRateData'" :chartData="deliveredRateData"/>
        </a-card>
      </a-col>
    </a-row>
    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="drsLoading" title="模板提交数 ">
          <a-skeleton v-if="drsLoading"/>
          <DeliveredRateChart v-else :compRef="'deliveredRateSubmitData'" :chartData="deliveredRateSubmitData"/>
        </a-card>
      </a-col>
    </a-row>

  </div>
</template>

<script>
import DeliveredRateChart from '@/views/sms/components/charts/DeliveredRateChart.vue'
import {
  DeliveredRateSubmitChart,
  getDeliveredRateAccount,
  getDeliveredRateChart,
  getDeliveredRateTemplate
} from '@/api/sms/deliveredRate'

export default {
  components: {
    DeliveredRateChart,
  },
  name: 'FileSystem',
  props: {
    desktopType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      deliveredRateSubmitData: { data: [], keys: [], title: '' },
      deliveredRateData: { data: [], keys: [], title: '' },
      queryData: { template: '', account: '', carrier: '' },
      deliveredRateAccountList: [],
      deliveredRateTemplateList: [],
      drsLoading: false,
      drLoading: false,
    }
  },
  methods: {
    getDeliveredRateAccountList() {
      getDeliveredRateAccount(this.queryData).then(res => {
        this.deliveredRateAccountList = res.Data.account
      })
    },
    getDeliveredRateTemplateList() {
      getDeliveredRateTemplate(this.queryData).then(res => {
        this.deliveredRateTemplateList = res.Data.template
      })
    },
    getDeliveredRateChartData() {
      let sendData = this.queryData
      if (this.queryData.template === '' && this.queryData.account === '' && this.queryData.carrier === '') {
        sendData = { account: 'huawei_cs_vcode', template: 'ts_cs_register_cn' , carrier: '' }
      }
      this.drLoading = true
      this.deliveredRateData = { data: [], keys: [], title: '' }
      getDeliveredRateChart(sendData).then(res => {
        if (res.Data.data.data !== null) {
          this.deliveredRateData = res.Data.data
        }
        this.drLoading = false
      })
    },
    getDeliveredRateSubmitChartData() {
      let sendData = this.queryData
      if (this.queryData.template === '' && this.queryData.account === '' && this.queryData.carrier === '') {
        sendData = { account: 'huawei_cs_vcode', template: 'ts_cs_register_cn' , carrier: '' }
      }
      this.drsLoading = true
      this.deliveredRateSubmitData = { data: [], keys: [], title: '' }
      DeliveredRateSubmitChart(sendData).then(res => {
        if (res.Data.data.data !== null) {
          this.deliveredRateSubmitData = res.Data.data
        }
        this.drsLoading = false
      })
    },

    handleChange() {
      this.getDeliveredRateChartData()
      this.getDeliveredRateSubmitChartData()
    },
    handleChangeTemplate(val) {
      if (val === undefined) {
        this.queryData.template = ''
      }
      this.getDeliveredRateAccountList()
    },
    handleChangeAccount(val) {
      if (val === undefined) {
        this.queryData.account = ''
      }
      this.getDeliveredRateTemplateList()
    }
  },
  created() {
    this.getDeliveredRateTemplateList()
    this.getDeliveredRateAccountList()
    // this.getActivatedChartData()
  },
  mounted() {
    this.getDeliveredRateChartData()
    this.getDeliveredRateSubmitChartData()
  }
}
</script>

<style scoped lang="less">
.cardcs {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
</style>
