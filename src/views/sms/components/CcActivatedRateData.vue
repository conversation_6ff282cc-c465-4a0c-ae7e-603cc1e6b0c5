<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :md="5" :sm="12">
            <a-form-item label="选择日期">
              <a-date-picker @change="handleChangeDate" format="YYYYMMDD" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item label="国家码">
              <a-select default-value="" v-model:value="queryParam.areaCode" style="width: 50%" show-search>
                <a-select-option v-for="i in this.areaCodeList" :value="i" :key="i">
                  {{ i }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
            <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="(record) => record.id"
      :pagination="pagination"
      :columns="Columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>
  </a-card>
</template>

<script>
import { Ellipsis, STable } from '@/components'
import { getActivatedRateList, getAreaCode } from '@/api/sms/activatedRate'


const Columns = [
  {
    title: '日期',
    dataIndex: 'eventDate',
    sorter: true
  },
  {
    title: '国家码',
    dataIndex: 'areaCode',
    width: 250,
    sorter: true
  },
  {
    title: '注册数',
    dataIndex: 'regTotal',
    width: 160,
    sorter: true
  },
  {
    title: '注册成功数',
    dataIndex: 'activated',
    sorter: true
  },
  {
    title: '注册转化率',
    dataIndex: 'activatedRate',
    sorter: true
  },
]


const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'Lb',
  components: {
    STable,
  },
  data() {
    this.Columns = Columns
    this.pagination = pagination
    return {
      data: [],
      areaCodeList: [],
      advanced: false,
      queryParam: { name: 'cc' },
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getActivatedRateList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      confirmLoadingExport: false,
      exportHistoryVisible: false,
      hasAdminRole: false,
    }
  },
  created() {
    this.getAreaCodeList()
  },
  methods: {
    getAreaCodeList() {
      getAreaCode().then(res => {
        this.areaCodeList = res.Data.areaCode
      })
    },
    handleChangeDate(date, dateString){
      this.queryParam.eventDate = dateString
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

</style>
