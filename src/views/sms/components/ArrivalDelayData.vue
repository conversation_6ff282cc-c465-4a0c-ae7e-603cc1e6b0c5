<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :md="6" :sm="12">
            <a-form-item label="账号">
              <a-select default-value="" v-model:value="queryParam.account" @change="handleChangeAccount" style="width: 80%" show-search allowClear>
                <a-select-option v-for="i in this.deliveredRateAccountList" :value="i" :key="i">
                  {{ i }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item label="模板">
              <a-select default-value="" v-model:value="queryParam.template" style="width: 80%" @change="handleChangeTemplate" show-search allowClear>
                <a-select-option v-for="i in this.deliveredRateTemplateList" :value="i" :key="i">
                  {{ i }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item label="运营商">
              <a-select default-value="" v-model:value="queryParam.carrier" @change="handleChangeCarrier" style="width: 50%" show-search allowClear>
                <a-select-option v-for="i in this.deliveredRateCarrierList" :value="i" :key="i">
                  {{ i }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
          <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="(record) => record.id"
      :pagination="pagination"
      :columns="Columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>
  </a-card>
</template>

<script>
import { Ellipsis, STable } from '@/components'
import { getArrivalDelayAccount, getArrivalDelayCarrier, getArrivalDelayList, getArrivalDelayTemplate } from '@/api/sms/arrivalDelay'


const Columns = [
  {
    title: '日期',
    dataIndex: 'eventDate',
    sorter: true
  },
  {
    title: '账号',
    dataIndex: 'account',
    width: 160,
    sorter: true
  },
  {
    title: '模板',
    dataIndex: 'template',
    width: 250,
    sorter: true
  },
  {
    title: '运营商',
    dataIndex: 'carrier',
    sorter: true
  },
  {
    title: '到达量',
    dataIndex: 'total',
    sorter: true
  },
  {
    title: '5秒内比例',
    dataIndex: 'within5s',
    sorter: true
  },
  {
    title: '10秒内比例',
    dataIndex: 'within10s',
    sorter: true
  },
  {
    title: '30秒内比例',
    dataIndex: 'within30s',
    sorter: true
  },
  {
    title: '60秒内比例',
    dataIndex: 'within60s',
    sorter: true
  },
]


const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'Lb',
  components: {
    STable,
  },
  data() {
    this.Columns = Columns
    this.pagination = pagination
    return {
      data: [],
      deliveredRateAccountList: [],
      deliveredRateTemplateList: [],
      deliveredRateCarrierList: [],
      advanced: false,
      queryParam: { account: 'huawei_cs_vcode', template: 'ts_cs_login_via_vcode_cn', carrier: 'mobile' },
      queryData: { template: '', account: '', carrier: '' },
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getArrivalDelayList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      confirmLoadingExport: false,
      exportHistoryVisible: false,
      hasAdminRole: false,
    }
  },
  created() {
    this.getArrivalDelayAccountList()
    this.getArrivalDelayTemplateList()
    this.getArrivalDelayCarrierList()
  },
  methods: {
    getArrivalDelayAccountList() {
      getArrivalDelayAccount(this.queryData).then(res => {
        this.deliveredRateAccountList = res.Data.account
      })
    },
    getArrivalDelayTemplateList() {
      getArrivalDelayTemplate(this.queryData).then(res => {
        this.deliveredRateTemplateList = res.Data.template
      })
    },
    getArrivalDelayCarrierList() {
      getArrivalDelayCarrier(this.queryData).then(res => {
        this.deliveredRateCarrierList = res.Data.carrier
      })
    },

    handleChangeCarrier(val) {
      this.queryData.carrier = val === undefined ? '' : val
      this.getArrivalDelayAccountList()
      this.getArrivalDelayTemplateList()
    },
    handleChangeTemplate(val) {
      this.queryData.template =  val === undefined ? '' : val
      this.getArrivalDelayAccountList()
      this.getArrivalDelayCarrierList()
    },
    handleChangeAccount(val) {
      this.queryData.account = val === undefined ? '' : val
      this.getArrivalDelayTemplateList()
      this.getArrivalDelayCarrierList()
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

</style>
