<template>
  <div>
    <a-row type="flex">
      <a-col :span="6" >
        <p style="vertical-align: middle;font-size: 20px;font-weight: bold; float: left">账号：</p>
        <a-select default-value="montnet_cc_m3" @change="handleChangeAccount" v-model:value="queryData.account" show-search style="width: 80%" allowClear>
          <a-select-option v-for="i in this.arrivalDelayAccountList" :value="i" :key="i">
            {{ i }}
          </a-select-option>
        </a-select>
      </a-col>
      <a-col :span="7">
        <p style="vertical-align: middle;font-size: 20px;font-weight: bold; float: left">模板：</p>
        <a-select default-value="ts_cc_uid_auto_friend_call_cn" @change="handleChangeTemplate" v-model:value="queryData.template" show-search style="width: 70%" allowClear>
          <a-select-option v-for="i in this.arrivalDelayTemplateList" :value="i" :key="i">
            {{ i }}
          </a-select-option>
        </a-select>
      </a-col>
      <a-col :span="7">
        <p style="vertical-align: middle;font-size: 20px;font-weight: bold; float: left">运营商：</p>
        <a-select default-value="mobile" @change="handleChangeCarrier" v-model:value="queryData.carrier" show-search style="width: 30%" allowClear>
          <a-select-option v-for="i in this.arrivalDelayCarrierList" :value="i" :key="i">
            {{ i }}
          </a-select-option>
        </a-select>
        <tx-button type="primary" @click="handleChange" style="margin-left: 15px">查询</tx-button>
      </a-col>

    </a-row>

    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="drLoading" title="模板到达延时">
          <a-skeleton v-if="drLoading"/>
          <ArrivalDelayChart v-else :compRef="'arrivalDelayData'" :chartData="arrivalDelayData"/>
        </a-card>
      </a-col>
    </a-row>
    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="drsLoading" title="模板到达量">
          <a-skeleton v-if="drsLoading"/>
          <ArrivalDelayTotalChart v-else :compRef="'arrivalDelaySubmitData'" :chartData="arrivalDelaySubmitData"/>
        </a-card>
      </a-col>
    </a-row>

  </div>
</template>

<script>
import ArrivalDelayChart from '@/views/sms/components/charts/ArrivalDelayChart.vue'
import ArrivalDelayTotalChart from '@/views/sms/components/charts/ArrivalDelayTotalChart.vue'
import {
  getArrivalDelayAccount,
  getArrivalDelayCarrier,
  getArrivalDelayChart,
  getArrivalDelayTemplate,
  getArrivalDelayTotalChart,
} from '@/api/sms/arrivalDelay'

export default {
  components: {
    ArrivalDelayChart,
    ArrivalDelayTotalChart,
  },
  name: 'FileSystem',
  props: {
    desktopType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      drLoading: false,
      drsLoading: false,
      arrivalDelaySubmitData: { data: [], keys: [], title: '' },
      arrivalDelayData: { data: [], keys: [], title: '' },
      queryData: { template: '', account: '', carrier: '' },
      arrivalDelayAccountList: [],
      arrivalDelayTemplateList: [],
      arrivalDelayCarrierList: [],
      carrierQuery: {},
      accountQuery: {},
      templateQuery: {},
    }
  },
  methods: {
    getArrivalDelayAccountList() {
      getArrivalDelayAccount(this.queryData).then(res => {
        this.arrivalDelayAccountList = res.Data.account
      })
    },
    getArrivalDelayTemplateList() {
      getArrivalDelayTemplate(this.queryData).then(res => {
        this.arrivalDelayTemplateList = res.Data.template
      })
    },
    getArrivalDelayCarrierList() {
      getArrivalDelayCarrier(this.queryData).then(res => {
        this.arrivalDelayCarrierList = res.Data.carrier
      })
    },
    getArrivalDelayChartData() {
      let sendData = this.queryData
      if (this.queryData.template === '' && this.queryData.account === '' && this.queryData.carrier === '') {
        sendData = { account: 'huawei_cs_vcode', template: 'ts_cs_login_via_vcode_cn', carrier: 'mobile' }
      }
      this.drLoading = true
      this.arrivalDelayData = { data: [], keys: [], title: '' }
      getArrivalDelayChart(sendData).then(res => {
        if (res.Data.data.data !== null) {
          this.arrivalDelayData = res.Data.data
        }
        this.drLoading = false
      })
    },
    getArrivalDelayTotalChartData() {
      let sendData = this.queryData
      if (this.queryData.template === '' && this.queryData.account === '' && this.queryData.carrier === '') {
        sendData = { account: 'huawei_cs_vcode', template: 'ts_cs_login_via_vcode_cn', carrier: 'mobile'  }
      }
      this.drsLoading = true
      this.arrivalDelaySubmitData = { data: [], keys: [], title: '' }
      getArrivalDelayTotalChart(sendData).then(res => {
        if (res.Data.data.data !== null) {
          this.arrivalDelaySubmitData = res.Data.data
        }
        this.drsLoading = false
      })
    },

    handleChange() {
      this.getArrivalDelayTotalChartData()
      this.getArrivalDelayChartData()
    },
    handleChangeCarrier(val) {
      if (val === undefined) {
        this.queryData.carrier = ''
      }
      this.getArrivalDelayAccountList()
      this.getArrivalDelayTemplateList()
    },
    handleChangeTemplate(val) {
      if (val === undefined) {
        this.queryData.template = ''
      }
      this.getArrivalDelayAccountList()
      this.getArrivalDelayCarrierList()
    },
    handleChangeAccount(val) {
      if (val === undefined) {
        this.queryData.account = ''
      }
      this.getArrivalDelayTemplateList()
      this.getArrivalDelayCarrierList()
    }
  },
  created() {
    this.getArrivalDelayTemplateList()
    this.getArrivalDelayAccountList()
    this.getArrivalDelayCarrierList()
  },
  mounted() {
    this.getArrivalDelayTotalChartData()
    this.getArrivalDelayChartData()
  }
}
</script>

<style scoped lang="less">
.cardcs {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
</style>
