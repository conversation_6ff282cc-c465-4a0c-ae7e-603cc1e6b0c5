<template>
  <div>
    <div style="display: flex">
      <p style="vertical-align: middle;font-size: 20px;font-weight: bold">国家码：</p>
      <a-select default-value="86" style="width: 120px" @change="handleChange"  v-model:value="queryData.areaCode" show-search>
        <a-select-option v-for="i in this.areaCodeList" :value="i" :key="i">
          {{ i }}
        </a-select-option>
      </a-select>
    </div>
    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="arLoading" title="注册转化率">
          <a-skeleton v-if="arLoading"/>
          <ActivateRateChart v-else :compRef="'activateRateChartData'" :chartData="activateRateChartData"/>
        </a-card>
      </a-col>
    </a-row>
    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="aLoading" title="注册激活数">
          <a-skeleton v-if="aLoading"/>
          <ActivatedChart v-else :compRef="'activatedChartData'" :chartData="activatedChartData"/>
        </a-card>
      </a-col>
    </a-row>
    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="avgLoading" title="国内外到达率">
          <a-skeleton v-if="avgLoading"/>
          <ActivateRateAvgChart v-else :compRef="'activateRateAvgChartData'" :chartData="activateRateAvgChartData"/>
        </a-card>
      </a-col>
    </a-row>


  </div>
</template>

<script>
import ActivateRateChart from '@/views/sms/components/charts/ActivateRateChart.vue'
import ActivatedChart from '@/views/sms/components/charts/ActivatedChart.vue'
import ActivateRateAvgChart from '@/views/sms/components/charts/ActivateRateAvgChart.vue'
import { getActivatedAvgRateChart, getActivatedChart, getActivatedRateChart, getAreaCode } from '@/api/sms/activatedRate'

export default {
  components: {
    ActivateRateChart,
    ActivatedChart,
    ActivateRateAvgChart,
  },
  name: 'FileSystem',
  props: {
    desktopType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      arLoading: false,
      aLoading: false,
      avgLoading: false,
      activateRateChartData: { data: [], keys: [], title: '' },
      activatedChartData: { data: [], keys: [], title: '' },
      activateRateAvgChartData: { data: [], keys: [], title: '' },
      queryData: { name: 'cs', areaCode: 86, areaTop: 0 },
      areaCodeList: [],
    }
  },
  methods: {
    getAreaCodeList() {
      getAreaCode().then(res => {
        this.areaCodeList = res.Data.areaCode
      })
    },
    getActivatedRateChartData() {
      this.arLoading = true
      getActivatedRateChart(this.queryData).then(res => {
        this.activateRateChartData = res.Data.data
        this.arLoading = false
      })
    },
    getActivatedChartData() {
      this.aLoading = true
      getActivatedChart(this.queryData).then(res => {
        this.activatedChartData = res.Data.data
        this.aLoading = false
      })
    },
    getActivatedAvgRateChartData() {
      this.avgLoading = true
      getActivatedAvgRateChart(this.queryData).then(res => {
        this.activateRateAvgChartData = res.Data.data
        this.avgLoading = false
      })
    },
    handleChange() {
      this.getActivatedRateChartData()
      this.getActivatedChartData()
      this.getActivatedAvgRateChartData()
    }
  },
  created() {
    this.getAreaCodeList()
  },
  mounted() {
    this.getActivatedRateChartData()
    this.getActivatedChartData()
    this.getActivatedAvgRateChartData()
  }
}
</script>

<style scoped lang="less">
.cardcs {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
</style>
