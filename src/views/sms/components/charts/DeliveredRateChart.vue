<template>
  <div class="chartContainer">
    <a-empty style="height: 320px" v-if="!chartData.data.length" />
    <div v-else :ref="compRef" :id="compRef"></div>
  </div>
</template>

<script>
import { Line } from '@antv/g2plot'
export default {
  props: {
    chartData: {
      type: Object,
      default: () => ({ data: [], keys: [], areaCode: 86 }),
    },
    compRef: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 320,
    },
  },
  async mounted() {
    const data = this.chartData.data.flatMap(d => [
      { eventDate: d.eventDate, value: d.mobile, category: 'mobile' },
      { eventDate: d.eventDate, value: d.telecom, category: 'telecom' },
      { eventDate: d.eventDate, value: d.unicom, category: 'unicom' },
    ])
    if (this.chartData.data.length) {
      const line = new Line(this.compRef, {
        data: data,
        title: this.chartData.title,
        xField: 'eventDate',
        yField: 'value',
        seriesField: 'category',
        xAxis: {
          type: 'cat',
        },
        yAxis: {
          nice: true,
        },
        slider: {
          start: 1 - (365 / this.chartData.data.length),
          end: 1,
        },
      })
      line.render()
    }
  },
  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
