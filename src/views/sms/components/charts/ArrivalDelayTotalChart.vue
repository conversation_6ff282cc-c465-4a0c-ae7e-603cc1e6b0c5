<template>
  <div class="chartContainer">
    <a-empty style="height: 320px" v-if="!chartData.data.length" />
    <div v-else :ref="compRef" :id="compRef"></div>
  </div>
</template>

<script>
import { Line } from '@antv/g2plot'
export default {
  props: {
    chartData: {
      type: Object,
      default: () => ({ data: [], keys: [], areaCode: 86 }),
    },
    compRef: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 320,
    },
  },
  async mounted() {
    console.log(this.chartData.data.length)
    if (this.chartData.data.length) {
      const line = new Line(this.compRef, {
        data: this.chartData.data,
        title: this.chartData.title,
        height: this.height,
        padding: [20, 30, 100, 90],
        width: this.$refs[this.compRef].clientWidth,
        xField: 'eventDate',
        yField: 'total',
        xAxis: {
          title: {
            text: this.chartData.keys[0],
            type: 'cat', // 增加这一行
          },
        },
        yAxis: {
          title: {
            text: this.chartData.keys[1],
            style: {
              fontSize: 14, // 调整标题字体大小
              rotate: 1, // 改变标题旋转角度
            },
          },
        },
        legend: {
          position: 'bottom',
          itemHeight: 25,
          // offsetY:10
        },
        slider: {
          start: 1 - (365 / this.chartData.data.length),
          end: 1,
        },
      })
      line.render()
    }
  },
  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
