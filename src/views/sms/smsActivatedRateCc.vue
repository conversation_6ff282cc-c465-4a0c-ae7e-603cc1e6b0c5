<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :tab-list="tabListNoTitle"
      :active-tab-key="noTitleKey"
      @tabChange="key => onTabChange(key, 'noTitleKey')"
    >
      <div v-if="noTitleKey === 'dashboard'">
        <cc-activated-rate-dashboard />
      </div>
      <div v-else-if="noTitleKey === 'data'">
        <cc-activated-rate-data />
      </div>
    </a-card>
  </page-header-wrapper>
</template>
<script>
import { defineComponent } from 'vue'
import CcActivatedRateDashboard from '@/views/sms/components/CcActivatedRateDashboard.vue'
import CcActivatedRateData from '@/views/sms/components/CcActivatedRateData.vue'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { getDesktopUserList } from '@/api/desktop/desktop_user'
import { notification } from 'ant-design-vue'

export default defineComponent({
  name: 'Desktop',
  components: {
    CcActivatedRateDashboard: CcActivatedRateDashboard,
    CcActivatedRateData: CcActivatedRateData,
  },
  data() {
    return {
      verifyLoading: false,
      tabListNoTitle: [
        {
          key: 'dashboard',
          tab: 'Dashboard',
        },
        {
          key: 'data',
          tab: '数据',
        },
      ],
      noTitleKey: 'dashboard',
      connectVisible: false,
      verificationCode: '',
      jumpAssetIp: '',
      visible: false,
      forceLossless: false,
      sessionId: '',
      jumpHostname: '',
      desktopIdc: '',
      jumpHostType: '',
      personalData: [],
      dlpData: [],
      serverData: [],
      data: [],
      currentItem: '',
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetDesktopUserList()
  },
  unmounted() {
    removeWatermark()
  },
  watch: {
    verificationCode: {
      handler: function (val) {
        if (val) {
          if (val.length === 6) {
            this.clickLinkTo()
          } else if (val.length > 6) {
            notification.error({
              message: '输入六位验证码',
            })
          }
        }
      },
    },
  },
  methods: {
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    codeChange(val) {
      // if (this.verificationCode.length >= 6) {
      //   this.clickLinkTo()
      // }
    },
    enterClick() {
      this.clickLinkTo()
    },
    GetDesktopUserList() {
      getDesktopUserList({ email: store.getters.email }).then(res => {
        if (res.Data.hasOwnProperty('data')) {
          if (res && res.Data && res.Data.data && res.Data.data.length > 0) {
            this.data = res.Data.data[0].desktops
            this.data.forEach(item => {
              if (item.status === 1) {
                item.status = '可连接'
              } else {
                item.status = '不可连接'
              }

              if (item.desktopType === 'personal') {
                this.personalData.push(item)
              } else if (item.desktopType === 'dlp') {
                this.dlpData.push(item)
              } else if (item.desktopType === 'server') {
                this.serverData.push(item)
              }
            })
          }
        }
      })
    },
    cloudDesktopApplication() {
      this.$router.push({ path: '/workflow/cloud-desktop' })
    },
    dlpDesktopApplication() {
      this.$router.push({ path: '/workflow/dlp-desktop' })
    },
    serverDesktopApplication() {
      this.$router.push({ path: '/workflow/server-desktop' })
    },
    dbManipulationApplication() {
      this.$router.push({ path: '/workflow/db-manipulation' })
    },
    desktopDestroyApplication() {
      this.$router.push({ path: '/workflow/desktop-destroy' })
    },
    dlpDesktopPolicyApplication() {
      this.$router.push({ path: '/workflow/dlp-desktop-policy' })
    },
    handleStop(item) {
      this.$confirm({
        title: '确认关机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要关机？`,
        onOk() {
          stopDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '关机成功',
                  description: '云桌面关机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleStart(item) {
      this.$confirm({
        title: '确认开机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要开机？`,
        onOk() {
          startDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '开机成功',
                  description: '云桌面开机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleReboot(item) {
      this.$confirm({
        title: '确认重启？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要重启？`,
        onOk() {
          rebootDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '重启成功',
                  description: '云桌面重启成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleCreateImage(item) {
      this.$confirm({
        title: '确认系统备份？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面制作系统镜像用于备份？`,
        onOk() {
          createImageDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '系统备份成功',
                  description: '云桌面系统备份成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleInfo(item) {
      this.currentItem = item
      if (store.getters.email.indexOf('robot_') === 0) {
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
        this.clickLinkTo()
        return
      }
      if (item !== 'repeated') {
        this.verificationCode = ''
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
      }

      let sendData = {}
      sendData.email = store.getters.email
      sendWxCode({
        ip: item.desktopIp,
        type: this.plantform,
      }).then(res => {
        console.log(res, 'resssss')
        // 非neeCheck
        if (res.Data && !res.Data.needCheck) {
          //
          this.clickLinkTo(false)
        } else if (res.Data && res.Data.needCheck) {
          this.connectVisible = true
        }
      })

      // sslDownloadSend(sendData).then(res => {
      //   if (res.Data.message === 'ok') {
      //     notification.success({
      //       message: '已发送验证码',
      //     })
      //   } else if (res.Data.message === 'exist') {
      //     notification.warning({
      //       message: '验证码未过期',
      //     })
      //   }
      // })
    },
    cancelCheck() {
      this.connectVisible = false
    },
    clickLinkTo(needCheck = true) {
      let checkData = {}
      checkData.email = store.getters.email
      checkData.code = this.verificationCode
      // localStorage.removeItem('asset_type')
      // localStorage.removeItem('allow_connect')
      // localStorage.setItem('asset_type', this.jumpHostType)
      // localStorage.setItem('allow_connect', 'true')
      // const routeData = this.$router.resolve({ path: '/desktop/session', query: { asset_ip: this.jumpAssetIp, asset_name: this.jumpHostname, asset_type: this.jumpHostType } })
      // window.open(routeData.href, '_blank')
      // 判断Email是否以robot_开头
      if (store.getters.email.indexOf('robot_') === 0) {
        this.connectVisible = false
        localStorage.removeItem('asset_type')
        localStorage.removeItem('allow_connect')
        localStorage.setItem('asset_type', this.jumpHostType)
        localStorage.setItem('allow_connect', 'true')
        const routeData = this.$router.resolve({
          path: '/desktop/session',
          query: {
            asset_ip: this.jumpAssetIp,
            asset_name: this.jumpHostname,
            asset_type: this.jumpHostType,
            asset_idc: this.desktopIdc,
          },
        })
        window.open(routeData.href, '_blank')
      } else {
        this.verifyLoading = true
        const paramsData = {
          code: this.verificationCode,
          ip: this.currentItem.desktopIp,
          type: this.plantform,
        }
        if (needCheck) {
          checkWxCode(paramsData)
            .then(res => {
              this.verifyLoading = false
              if (res.Data && res.Data.message == 'ok') {
                // 连接跳转
                this.connectVisible = false
                localStorage.removeItem('asset_type')
                localStorage.removeItem('allow_connect')
                localStorage.setItem('asset_type', this.jumpHostType)
                localStorage.setItem('allow_connect', 'true')
                const routeData = this.$router.resolve({
                  path: '/desktop/session',
                  query: {
                    asset_ip: this.jumpAssetIp,
                    asset_name: this.jumpHostname,
                    asset_type: this.jumpHostType,
                    asset_idc: this.desktopIdc,
                    force_lossless: this.forceLossless.toString(),
                  },
                })
                window.open(routeData.href, '_blank')
              } else if (res.Data && res.Data.message == 'error') {
                notification.error({
                  message: '输入验证码错误',
                })
                this.verificationCode = ''
              } else if (res.Data && res.Data.message == 'notExist') {
                this.verificationCode = ''
                notification.warning({
                  message: '验证码已过期，将为您重新发送',
                })
                sendWxCode({
                  ip: this.currentItem.desktopIp,
                  type: this.plantform,
                }).then(res => {})
              }
              console.log(res, 'rrrrrrrrr')
            })
            .finally(() => {})
        } else {
          // 连接跳转
          this.verifyLoading = false
          this.connectVisible = false
          localStorage.removeItem('asset_type')
          localStorage.removeItem('allow_connect')
          localStorage.setItem('asset_type', this.jumpHostType)
          localStorage.setItem('allow_connect', 'true')
          const routeData = this.$router.resolve({
            path: '/desktop/session',
            query: {
              asset_ip: this.jumpAssetIp,
              asset_name: this.jumpHostname,
              asset_type: this.jumpHostType,
              asset_idc: this.desktopIdc,
              force_lossless: this.forceLossless.toString(),
            },
          })
          window.open(routeData.href, '_blank')
        }

        // sslDownloadCheck(checkData)
        //   .then(res => {
        //     if (res.Data.message === 'ok') {
        //       // 连接跳转
        //       this.connectVisible = false
        //       localStorage.removeItem('asset_type')
        //       localStorage.removeItem('allow_connect')
        //       localStorage.setItem('asset_type', this.jumpHostType)
        //       localStorage.setItem('allow_connect', 'true')
        //       const routeData = this.$router.resolve({
        //         path: '/desktop/session',
        //         query: {
        //           asset_ip: this.jumpAssetIp,
        //           asset_name: this.jumpHostname,
        //           asset_type: this.jumpHostType,
        //           asset_idc: this.desktopIdc,
        //           force_lossless: this.forceLossless.toString(),
        //         },
        //       })
        //       window.open(routeData.href, '_blank')
        //     } else if (res.Data.message === 'error') {
        //       notification.error({
        //         message: '输入验证码错误',
        //       })
        //       this.verificationCode = ''
        //       // this.$nextTick(() => {
        //       //   this.$refs.codeVerify.focus()
        //       // })
        //     } else if (res.Data.message === 'notExist') {
        //       this.verificationCode = ''
        //       notification.warning({
        //         message: '验证码已过期，将为您重新发送',
        //       })
        //       sslDownloadSend({
        //         email: store.getters.email,
        //       }).then(res => {
        //         if (res.Data.message === 'ok') {
        //           notification.success({
        //             message: '已发送验证码',
        //           })
        //         } else if (res.Data.message === 'exist') {
        //           notification.warning({
        //             message: '验证码未过期',
        //           })
        //         }
        //       })
        //     }
        //   })
        //   .finally(() => {
        //     this.verifyLoading = false
        //   })
      }
    },
    onTabChange(key, type) {
      this[type] = key
    },
  },
})
</script>
