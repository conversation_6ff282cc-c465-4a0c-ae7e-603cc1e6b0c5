<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :tab-list="tabListNoTitle"
      :active-tab-key="noTitleKey"
      @tabChange="key => onTabChange(key, 'noTitleKey')"
    >
      <div v-if="noTitleKey === 'dashboard'">
        <arrival-delay-dashboard/>
      </div>
      <div v-else-if="noTitleKey === 'data'">
        <arrival-delay-data/>
      </div>
    </a-card>
  </page-header-wrapper>
</template>
<script>
import { defineComponent } from 'vue'
import ArrivalDelayDashboard from '@/views/sms/components/ArrivalDelayDashboard.vue'
import ArrivalDelayData from '@/views/sms/components/ArrivalDelayData.vue'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { notification } from 'ant-design-vue'


export default defineComponent({
  name: 'ArrivalDelay',
  components: {
    ArrivalDelayDashboard: ArrivalDelayDashboard,
    ArrivalDelayData: ArrivalDelayData,
  },
  data() {
    return {
      verifyLoading: false,
      tabListNoTitle: [
        {
          key: 'dashboard',
          tab: 'Dashboard',
        },
        {
          key: 'data',
          tab: '数据',
        },
      ],
      noTitleKey: 'dashboard',
      connectVisible: false,
      verificationCode: '',
      jumpAssetIp: '',
      visible: false,
      forceLossless: false,
      sessionId: '',
      jumpHostname: '',
      desktopIdc: '',
      jumpHostType: '',
      personalData: [],
      dlpData: [],
      serverData: [],
      data: [],
      currentItem: '',
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetDesktopUserList()
  },
  unmounted() {
    removeWatermark()
  },
  watch: {
    verificationCode: {
      handler: function (val) {
        if (val) {
          if (val.length === 6) {
            this.clickLinkTo()
          } else if (val.length > 6) {
            notification.error({
              message: '输入六位验证码',
            })
          }
        }
      },
    },
  },
  methods: {
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    codeChange(val) {
      // if (this.verificationCode.length >= 6) {
      //   this.clickLinkTo()
      // }
    },
    enterClick() {
      this.clickLinkTo()
    },
    onTabChange(key, type) {
      this[type] = key
    },
  },
})
</script>
