<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-08 15:20:41
 * @FilePath: \cloud_web\src\views\ai\components\columnLineChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 100%" v-if="!chartData.length" />

    <div v-if="chartData.length" :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { DualAxes } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
  },
  async mounted() {
    const toFixedTwo = num => {
      return num.toFixed(2).padEnd(4, '0')
    }
    if (this.chartData.length) {
      const dualAxes = new DualAxes(this.compRef, {
        data: [this.chartData, this.chartData],
        xField: 'date',
        yField: ['totalTime', 'usageRate'],
        yAxis: {
          usageRate: {
            nice: true,
            tickCount: 5,
            label: {
              formatter: v => {
                const value = Number(v.split('%')[0])
                return `${value.toFixed(2).padEnd(4, '0')}%`
              },
            },
          },
        },
        meta: {
          totalTime: {
            alias: '总工时',
            formatter: v => {
              return v
            },
          },
          usageRate: {
            alias: '利用率',
            formatter: v => {
              return `${v}%`
            },
          },
        },
        geometryOptions: [
          {
            geometry: 'column',
          },
          {
            geometry: 'line',
            lineStyle: {
              lineWidth: 2,
            },
          },
        ],
        legend: {
          position: 'top',
          offsetY: 5,
        },
      })

      dualAxes.render()
    }
  },
  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
