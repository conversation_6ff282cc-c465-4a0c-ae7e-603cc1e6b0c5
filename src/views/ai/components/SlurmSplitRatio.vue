<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="小组">
              <a-select
                v-model:value="queryParam.group"
                placeholder="请选择小组"
                :options="groupList"
                allowClear>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="月份">
              <a-month-picker style="width: 100%" v-model:value="queryParam.month" valueFormat="YYYY-MM"
                              placeholder="请选择月份" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
            <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
          </a-col>
          <a-col :md="6" :sm="24">
            <tx-button style="float: right" type="primary" @click="handleAdd">新建</tx-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-modal
      title="新建"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      width="35%"
    >
      <a-form
        :model="addForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :rules="rules"
        ref="ruleForm">
        <a-form-item label="月份" name="month">
          <a-month-picker style="width: 100%" v-model:value="addForm.month" valueFormat="YYYY-MM"
                          placeholder="请选择月份"/>
        </a-form-item>
        <a-form-item label="小组" name="group">
          <a-select
            v-model:value="addForm.group"
            placeholder="请选择小组"
            :options="groupList"
            allowClear
            style="width: 100%"
          ></a-select>
        </a-form-item>
        <a-form-item label="CS-CC" name="cc">
          <a-input-number v-model:value="addForm.cc" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="CS" name="cs">
          <a-input-number v-model:value="addForm.cs" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="DG" name="dg">
          <a-input-number v-model:value="addForm.dg" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="总裁办" name="zcb">
          <a-input-number v-model:value="addForm.zcb" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="SSG" name="ssg">
          <a-input-number v-model:value="addForm.ssg" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="AIM" name="aim">
          <a-input-number v-model:value="addForm.aim" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="ACG" name="acg">
          <a-input-number v-model:value="addForm.acg" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-input v-model:value="addForm.remark" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
      :visible="updateVisible"
      :width="650"
      title="更新比例"
      :closable="false">
      <a-form
        :model="updateForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :rules="rules"
        ref="ruleForm">
        <a-form-item label="月份" name="month">
          <a-month-picker style="width: 100%" v-model:value="updateForm.month" valueFormat="YYYY-MM"
                          placeholder="请选择月份"/>
        </a-form-item>
        <a-form-item label="小组" name="group">
          <a-select
            v-model:value="updateForm.group"
            placeholder="请选择小组"
            :options="groupList"
            allowClear
            style="width: 100%"
          ></a-select>
        </a-form-item>
        <a-form-item label="CS-CC" name="cc">
          <a-input-number v-model:value="updateForm.cc" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="CS" name="cs">
          <a-input-number v-model:value="updateForm.cs" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="DG" name="dg">
          <a-input-number v-model:value="updateForm.dg" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="总裁办" name="zcb">
          <a-input-number v-model:value="updateForm.zcb" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="SSG" name="ssg">
          <a-input-number v-model:value="updateForm.ssg" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="AIM" name="aim">
          <a-input-number v-model:value="updateForm.aim" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="ACG" name="acg">
          <a-input-number v-model:value="updateForm.acg" precision="4" :min="0" :max="1" style="width: 100%" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-input v-model:value="updateForm.remark" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
    <div style="position: relative">
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
    </div>
  </a-card>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { STable } from '@/components'
import { setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import {
  listSlurmSplitRatio,
  createSlurmSplitRatio,
  updateSlurmSplitRatio,
  deleteSlurmSplitRatio,
  getSlurmSplitRatio
} from '@/api/ai/slurm'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: '月份',
    dataIndex: 'month'
  },
  {
    title: '小组',
    dataIndex: 'group'
  },
  {
    title: '分摊比例',
    children: [
      {
        title: 'AIM',
        dataIndex: 'aim'
      },
      {
        title: 'CS',
        dataIndex: 'cs'
      },
      {
        title: 'CS-CC',
        dataIndex: 'cc'
      },
      {
        title: 'SSG',
        dataIndex: 'ssg'
      },
      {
        title: 'ACG',
        dataIndex: 'acg'
      },
      {
        title: 'DG',
        dataIndex: 'dg'
      },
      {
        title: '总裁办',
        dataIndex: 'zcb'
      }
    ]
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt'
  },
  {
    title: '备注',
    dataIndex: 'remark'
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' }
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'SlurmSplitRatio',
  components: {
    STable
  },
  props: {
    mysqlInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hasAdminRole: false,
      queryParam: {},
      addForm: {},
      visible: false,
      updateVisible: false,
      updateForm: {},
      confirmLoading: false,
      rules: {
        month: [{ required: true, message: '请选择月份', trigger: 'change' }],
        group: [{ required: true, message: '请选择小组', trigger: 'change' }],
        cc: [{ required: true, message: '请输入比例', trigger: 'change' }],
        cs: [{ required: true, message: '请输入比例', trigger: 'change' }],
        dg: [{ required: true, message: '请输入比例', trigger: 'change' }],
        zcb: [{ required: true, message: '请输入比例', trigger: 'change' }],
        ssg: [{ required: true, message: '请输入比例', trigger: 'change' }],
        aim: [{ required: true, message: '请输入比例', trigger: 'change' }],
        acg: [{ required: true, message: '请输入比例', trigger: 'change' }]
      },
      groupList: [
        { label: 'cv', value: 'cv' }, { label: 'ipt', value: 'ipt' }, { label: 'cs', value: 'cs' },
        { label: 'nlp', value: 'nlp' }, { label: 'acg', value: 'acg' }, { label: 'cc', value: 'cc' }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listSlurmSplitRatio(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  created () {
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('db_user') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    handleAdd () {
      this.visible = true
      this.addForm = {
        cc: 0.0,
        cs: 0.0,
        dg: 0.0,
        zcb: 0.0,
        ssg: 0.0,
        aim: 0.0,
        acg: 0.0
      }
    },
    handleOk (e) {
      console.log('handleOk')
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createSlurmSplitRatio(this.addForm).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel (e) {
      this.visible = false
    },
    handleUpdate (record) {
      getSlurmSplitRatio(record.id).then(res => {
        this.updateForm = res.Data
        this.updateVisible = true
      })
    },
    handleUpdateOk (e) {
      console.log('handleUpdateOk')
      antdFormValidate(this.$refs.ruleForm, valid => {
        console.log('valid')
        if (valid) {
          console.log('1')
          this.confirmLoading = true
          updateSlurmSplitRatio(this.updateForm).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel (e) {
      this.updateVisible = false
    },
    handleDel (record) {
      deleteSlurmSplitRatio(record.id).then(res => {
        if (res.Data.code === 200) {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.confirmLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}
</style>
