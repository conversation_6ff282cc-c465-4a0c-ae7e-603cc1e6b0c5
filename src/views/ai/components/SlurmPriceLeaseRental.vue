<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="GPU类型">
              <a-select
                v-model:value="queryParam.gpuType"
                placeholder="请选择GPU类型"
                :options="gpuTypeList"
                allowClear>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="机房">
              <a-select
                v-model:value="queryParam.idc"
                placeholder="请选择机房"
                :options="idcList"
                allowClear>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
            <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
          </a-col>
          <a-col :md="6" :sm="24">
            <tx-button style="float: right" type="primary" @click="handleAdd">新建</tx-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-modal
      title="新建"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      width="35%"
    >
      <a-form-model ref="ruleForm" :model="addForm" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="GPU类型" name="gpuType">
          <a-select
            v-model:value="addForm.gpuType"
            placeholder="请选择GPU类型"
            :options="gpuTypeList"
            allowClear>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="机房" name="idc">
          <a-select
            v-model:value="addForm.idc"
            placeholder="请选择机房"
            :options="idcList"
            allowClear>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="租赁费(月)" name="cost">
          <a-input-number v-model:value="addForm.cost" placeholder="请输入费用" style="width: 100%" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
      width="35%"
    >
      <a-form-model ref="ruleForm" :model="updateForm" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="ID" name="id">
          <a-input-number v-model:value="updateForm.id" style="width: 100%" disabled />
        </a-form-model-item>
        <a-form-model-item label="GPU类型" name="gpuType">
          <a-select
            v-model:value="updateForm.gpuType"
            placeholder="请选择GPU类型"
            :options="gpuTypeList"
            allowClear>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="机房" name="idc">
          <a-select
            v-model:value="updateForm.idc"
            placeholder="请选择机房"
            :options="idcList"
            allowClear>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="租赁费(月)" name="cost">
          <a-input-number v-model:value="updateForm.cost" style="width: 100%" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <div style="position: relative">
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
    </div>
  </a-card>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { STable } from '@/components'
import { setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { listSlurmPrice, createSlurmPrice, updateSlurmPrice, deleteSlurmPrice } from '@/api/ai/slurm'
import { getSlurmPrice } from '../../../api/ai/slurm'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: 'GPU类型',
    dataIndex: 'gpuType'
  },
  {
    title: '机房',
    dataIndex: 'idc'
  },
  {
    title: '费用(元/月)',
    dataIndex: 'cost'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt'
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' }
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'SlurmPriceLeaseRental',
  components: {
    STable
  },
  props: {
    mysqlInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hasAdminRole: false,
      queryParam: {},
      addForm: {},
      visible: false,
      updateVisible: false,
      updateForm: {},
      confirmLoading: false,
      rules: {
        gpuType: [{ required: true, message: '请选择GPU类型', trigger: 'change' }],
        idc: [{ required: true, message: '请选择机房', trigger: 'change' }],
        cost: [{ required: true, message: '请输入费用', trigger: 'change' }]
      },
      gpuTypeList: [{ label: 'L4', value: 'L4' },{ label: 'H800', value: 'H800' }, { label: 'A800', value: 'A800' },
        { label: 'A100', value: 'A100' }, { label: 'A10', value: 'A10' }, { label: '4090D', value: '4090D' },
        { label: '3090', value: '3090' }, { label: '3080Ti', value: '3080Ti' }, { label: '3080', value: '3080' },
        { label: '2080Ti', value: '2080Ti' }],
      idcList: [{ label: 'shanghai8-songjiang', value: 'shanghai8-songjiang' },
        { label: 'ucloud-shanghai1-zhuanqiao', value: 'ucloud-shanghai1-zhuanqiao' }],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        requestParameters.status = 1
        console.log('loadData', requestParameters)
        return listSlurmPrice(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  created () {
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('db_user') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    handleAdd () {
      this.addForm = {}
      this.visible = true
    },
    handleOk (e) {
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          this.confirmLoading = true
          this.addForm.status = 1
          createSlurmPrice(this.addForm).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel (e) {
      this.visible = false
    },
    handleUpdate (record) {
      getSlurmPrice(record.id).then(res => {
        this.updateForm = res.Data
        this.updateVisible = true
      })
    },
    handleUpdateOk (e) {
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateSlurmPrice(this.updateForm).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel (e) {
      this.updateVisible = false
    },
    handleDel (record) {
      deleteSlurmPrice(record.id).then(res => {
        if (res.Data.code === 200) {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.confirmLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}
</style>
