<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-12 17:29:53
 * @FilePath: \cloud_web\src\views\ai\components\stackColumnChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <p v-if="radioShow">
      <a-radio-group @change="groupChange" v-model:value="groupTask" button-style="solid">
        <a-radio-button value="nlp">NLP</a-radio-button>
        <a-radio-button value="cv">CV</a-radio-button>
        <a-radio-button value="acg">ACG</a-radio-button>
        <a-radio-button value="cs">CS</a-radio-button>
        <a-radio-button value="ipt">IPT</a-radio-button>
      </a-radio-group>
    </p>
    <a-empty style="height: 100%" v-if="!chartData || !chartData.length" />
    <div v-if="chartData.length" :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Column } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
    radioShow: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  async mounted() {
    this.draw()
  },
  data() {
    return {
      groupTask: 'nlp',
      stackedColumnPlot: null,
    }
  },
  methods: {
    groupChange() {
      this.$emit('groupChange', this.groupTask)
    },
    draw() {
      if (this.chartData && this.chartData.length) {
        this.stackedColumnPlot = new Column(this.compRef, {
          data: this.chartData,
          isStack: true,
          xField: this.filedName[0],
          yField: this.filedName[1],
          seriesField: this.filedName[2],
          legend: {
            position: 'top',
            offsetY: 5,
          },
          label: {
            // 可手动配置 label 数据标签位置
            position: 'middle', // 'top', 'bottom', 'middle'
            // 可配置附加的布局方法
            layout: [
              // 柱形图数据标签位置自动调整
              { type: 'interval-adjust-position' },
              // 数据标签防遮挡
              { type: 'interval-hide-overlap' },
              // 数据标签文颜色自动调整
              { type: 'adjust-color' },
            ],
          },
        })

        this.stackedColumnPlot.render()
      }
    },
    rederData() {
      if (this.stackedColumnPlot && this.chartData.length) {
        this.stackedColumnPlot.changeData(this.chartData)
      } else if (!this.stackedColumnPlot) {
        this.draw()
      }
    },
    destoryChart() {
      this.stackedColumnPlot.destroy()
      this.stackedColumnPlot = null
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
