<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-02 11:21:01
 * @FilePath: \cloud_web\src\views\ai\components\lineChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 320px" v-if="!chartData.length" />
    <div v-else :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Line } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
  },
  async mounted() {
    if (this.chartData.length) {
      this.line = new Line(this.compRef, {
        data: this.chartData,
        xField: this.filedName[0],
        yField: this.filedName[1],
        seriesField: this.filedName[2],
        smooth: true,
        legend: {
          position: 'top',
          offsetY: 5
        }
      })

      this.line.render()
    }
  },

  data() {
    return {
      line: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        if (val && val.length) {
          if (this.line) {
            this.line.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    renderChart() {
      this.line.changeData(this.chartData)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
