<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-06-20 15:47:50
 * @FilePath: \cloud_web\src\views\ai\components\pieChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 100%" v-if="!chartData.length" />
    <div v-if="chartData.length" :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Pie } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
  },
  async mounted() {
    if (this.chartData.length) {
      this.pie = new Pie(this.compRef, {
        data: this.chartData,
        angleField: this.filedName[0],
        colorField: this.filedName[1],
        radius: 0.7,
        // height: 320,
        // padding:20,
        label: {
          type: 'outer',
          offset: '30%',
          content: datum => {
            return `${datum[this.filedName[1]]}:${datum[this.filedName[0]].toFixed(0)}%`
          },
          style: {
            fontSize: 14,
            textAlign: 'top',
          },
        },
        interactions: [{ type: 'element-active' }],
        tooltip: {
          formatter: datum => {
            return {
              title: datum[this.filedName[1]],
              name: datum[this.filedName[1]],
              value: `${datum[this.filedName[0]].toFixed(0)}%`,
            }
          },
        },
      })

      this.pie.render()
    }
  },
  data() {
    return {
      pie: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        if (val && val.length) {
          if (this.pie) {
            this.pie.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    renderChart() {
      this.pie.changeData(this.chartData)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
