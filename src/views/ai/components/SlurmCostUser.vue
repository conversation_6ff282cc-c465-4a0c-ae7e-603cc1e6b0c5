<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="5" :sm="24">
            <a-form-item label="维度">
              <a-select
                v-model:value="queryParam.status"
                placeholder="请选择维度"
                :options="statusList"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="24">
            <a-form-item label="月份">
              <a-month-picker style="width: 100%" v-model:value="queryParam.month" valueFormat="YYYY-MM"
                              placeholder="请选择月份" />
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="24">
            <a-form-item label="邮箱">
              <a-input v-model:value="queryParam.email" placeholder="模糊查詢" @pressEnter="$refs.table.refresh()"
                       allowClear></a-input>
            </a-form-item>
          </a-col>
          <!--          <template v-if="advanced">-->
          <a-col :md="5" :sm="24">
            <a-form-item label="GPU类型">
              <a-select
                v-model:value="queryParam.gpuType"
                placeholder="请选择GPU类型"
                :options="gpuTypeList"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <!--          </template>-->
          <!--          <a-col :md="(!advanced && 4) || 24" :sm="24">-->
          <a-col :md="4" :sm="24">
            <!--            <span-->
            <!--              class="table-page-search-submitButtons"-->
            <!--              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"-->
            <!--            >-->
            <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
            <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            <!--              <a @click="toggleAdvanced" style="margin-left: 8px">-->
            <!--                {{ advanced ? '收起' : '展开' }}-->
            <!--                <a-icon :type="advanced ? 'up' : 'down'" />-->
            <!--              </a>-->
            <!--            </span>-->
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operations" v-if="hasAdminRole">
      <tx-button type="primary" class="columnSetting" :loading="confirmLoadingExport"
                 @click="handleExport">
        导出
      </tx-button>
      <tx-button class="columnSetting" @click="showExportHistory" style="margin-left: 3px">
        <a-icon type="unordered-list-outlined" />
      </tx-button>
      <a-modal
        v-model:visible="exportHistoryVisible"
        center
        :footer="null"
        width="50%"
      >
        <s-table
          ref="tableExportHistory"
          :rowKey="(record) => record.id"
          :columns="exportHistoryColumns"
          :data="loadExportHistoryData"
        >
          <template #bodyCell="{column, index, record, text}">
            <template v-if="column.dataIndex === 'resultLink'">
              <div v-if="record.resultLink!==''">
                <a :href="record.resultLink.replace('http:','https:')">下载</a>
              </div>
              <div v-else>
                -
              </div>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}</a-tag>
            </template>
          </template>
        </s-table>
      </a-modal>
    </div>
    <div style="position: relative">
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'gpuType'">
            <a-tag :color="gpuTypeColorFilter(record.gpuType)">{{ gpuTypeFilter(record.gpuType) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag :color="statusColorFilter(record.status)">{{ statusFilter(record.status) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'ratio'">
            <div v-if="record.status === 2">
              {{ (record.ratio * 100).toFixed(2) }}%
            </div>
            <div v-else> --</div>
          </template>
        </template>
      </s-table>
    </div>
  </a-card>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { STable } from '@/components'
import { setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { listSlurmCostMonthStatistical, exportSlurmCostMonthStatistical } from '@/api/ai/slurm'
import { notification } from 'ant-design-vue'
import { getFileTaskList } from '@/api/cost/month'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: '月份',
    dataIndex: 'month'
  },
  {
    title: 'GPU类型',
    dataIndex: 'gpuType',
    scopedSlots: { customRender: 'gpuType' }
  },
  {
    title: '任务数量',
    dataIndex: 'jobNum'
  },
  {
    title: 'GPU卡时长',
    dataIndex: 'gpuRunTimeNum'
  },
  {
    title: '定价费用',
    dataIndex: 'cost'
  },
  {
    title: '实际费用',
    dataIndex: 'costActual'
  },
  {
    title: '邮箱',
    dataIndex: 'email'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  },
  {
    title: '比例',
    dataIndex: 'ratio',
    scopedSlots: { customRender: 'ratio' }
  },
  {
    title: '维度',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' }
  }
]

const gpuTypeMap = {
  'H800': {
    color: '#108ee9',
    text: 'H800'
  },
  'A800': {
    color: '#87d068',
    text: 'A800'
  },
  'A100': {
    color: '#2db7f5',
    text: 'A100'
  },
  'A10': {
    color: 'cyan',
    text: 'A10'
  },
  '3090': {
    color: '#CDAA7D',
    text: '3090'
  }
}

const statusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  2: {
    color: 'blue',
    text: '按用户-分区统计'
  },
  3: {
    color: 'green',
    text: '按用户统计'
  }
}

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt'
  },
  {
    title: '用户',
    dataIndex: 'creator'
  },
  {
    title: '导出状态',
    dataIndex: 'status'
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink'
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'green',
    text: '成功'
  },
  2: {
    color: 'red',
    text: '失败'
  },
  3: {
    color: 'blue',
    text: '运行中'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'SlurmCostUser',
  components: {
    STable
  },
  props: {
    mysqlInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    this.columns = columns
    this.exportHistoryColumns = exportHistoryColumns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hasAdminRole: false,
      queryParam: {
        status: 2
      },
      advanced: false,
      gpuTypeList: [{ label: 'H800', value: 'H800' }, { label: 'A800', value: 'A800' },
        { label: 'A100', value: 'A100' }, { label: 'A10', value: 'A10' }, { label: '4090D', value: '4090D' },
        { label: '3090', value: '3090' }, { label: '3080Ti', value: '3080Ti' }, { label: '3080', value: '3080' },
        { label: '2080Ti', value: '2080Ti' }],
      statusList: [
        { label: '按用户-分区统计', value: 2 },
        { label: '按用户统计', value: 3 }
      ],
      groupList: [
        { label: 'cv', value: 'cv' }, { label: 'ipt', value: 'ipt' }, { label: 'cs', value: 'cs' },
        { label: 'nlp', value: 'nlp' }, { label: 'acg', value: 'acg' }, { label: 'cc', value: 'cc' }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        requestParameters.taskType = 20
        return listSlurmCostMonthStatistical(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      exportHistoryVisible: false,
      confirmLoadingExport: false,
      loadExportHistoryData: (parameter) => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = 20
        return getFileTaskList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            } else {
              return res.Data
            }
          } else {
            return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
          }
        })
      }
    }
  },
  created () {
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('ai_admin') || this.userRoles.includes('admin') || this.userRoles.includes('cost_admin')) {
          this.hasAdminRole = true
        }
      })
    },
    gpuTypeFilter (type) {
      return gpuTypeMap[type]?.text || type
    },
    gpuTypeColorFilter (type) {
      return gpuTypeMap[type]?.color || type
    },
    statusFilter (type) {
      return statusMap[type]?.text || type
    },
    statusColorFilter (type) {
      return statusMap[type]?.color || type
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    handleExport () {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      requestParameters.taskType = 20
      exportSlurmCostMonthStatistical(requestParameters).then((res) => {
        if (res !== undefined && res.Code === 200) {
          notification.success({
            message: '导出成功，点击右侧按钮，查看导出记录和结果'
          })
          this.confirmLoadingExport = false
        } else {
          notification.error({
            message: '导出失败'
          })
          this.confirmLoadingExport = false
        }
      }).catch(() => {
        this.confirmLoadingExport = false
      })
    },
    exportStatusFilter (type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter (type) {
      return exportStatusMap[type]?.color || type
    },
    showExportHistory () {
      this.exportHistoryVisible = true
      this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

.columnSetting {
  position: relative;
  right: 0;
}
</style>
