<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-02 11:01:28
 * @FilePath: \cloud_web\src\views\ai\components\barChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <p v-if="radioShow && chartData.length">
      <a-radio-group @change="groupTimeRadioChange" v-model:value="groupTimeRadio" button-style="solid">
        <a-radio-button value="average">均值</a-radio-button>
        <a-radio-button value="p50">p50</a-radio-button>
        <a-radio-button value="p99">p99</a-radio-button>
      </a-radio-group>
    </p>
    <a-empty style="height: 100%" v-if="!chartData.length" />
    <div :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Bar } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
    radioShow: {
      type: Boolean,
      default: false,
    },
  },
  async mounted() {
    if (this.chartData.length) {
      this.bar = new Bar(this.compRef, {
        data: this.chartData,
        xField: this.filedName[0],
        yField: this.filedName[1],
        seriesField: this.filedName[1],
        legend: {
          position: 'top',
          offsetY: 5
        }
      })
      this.bar.render()
    }
  },
  data() {
    return {
      groupTimeRadio: 'average',
      bar: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        console.log(val, 'vvvv')
        if (val && val.length) {
          if (this.bar) {
            this.bar.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    groupTimeRadioChange(v) {
      this.$emit('groupTimeRadioChange', this.groupTimeRadio)
      this.bar.changeData(this.chartData)
    },
    renderChart(data) {
      if (data.length) {
        this.bar = new Bar(this.compRef, {
          data,
          xField: this.filedName[0],
          yField: this.filedName[1],
          seriesField: this.filedName[1],
        })

        this.bar.render()
      }
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
