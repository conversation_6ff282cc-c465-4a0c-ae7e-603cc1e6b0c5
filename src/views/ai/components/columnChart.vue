<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-02 11:22:09
 * @FilePath: \cloud_web\src\views\ai\components\columnChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 320px" v-if="!chartData.length" />
    <p v-if="radioShow && chartData.length">
      <a-radio-group @change="groupTimeRadioChange" v-model:value="groupTimeRadio" button-style="solid">
        <a-radio-button value="average">均值</a-radio-button>
        <a-radio-button value="p50">p50</a-radio-button>
        <a-radio-button value="p99">p99</a-radio-button>
      </a-radio-group>
    </p>
    <div v-if="chartData.length" :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Column } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
    radioShow: {
      type: Boolean,
      default: false,
    },
    isGroup: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    if (this.chartData.length) {
      this.column = new Column(this.compRef, {
        data: this.chartData,
        xField: this.filedName[0],
        yField: this.filedName[1],
        seriesField: this.filedName[2],
        isGroup: this.isGroup,
        columnStyle: {
          radius: [20, 20, 0, 0],
        },
        legend: {
          position: 'top',
          offsetY: 5
        }
      })
      this.column.render()
    }
  },
  data() {
    return {
      groupTimeRadio: 'average',
      column: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        if (val && val.length) {
          if (this.column) {
            this.column.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    groupTimeRadioChange(v) {
      this.$emit('groupTimeRadioChange', this.groupTimeRadio)
    },
    renderChart(data) {
      if (data.length) {
        this.column = new Column(this.compRef, {
          data: this.chartData,
          xField: this.filedName[0],
          yField: this.filedName[1],
          seriesField: this.filedName[2],
          isGroup: this.isGroup,
          columnStyle: {
            radius: [20, 20, 0, 0],
          },
        })
        this.column.render()
      }
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
