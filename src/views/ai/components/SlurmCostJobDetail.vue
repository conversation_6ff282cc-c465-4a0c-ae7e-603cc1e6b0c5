<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="维度">
              <a-select
                v-model:value="queryParam.status"
                placeholder="请选择维度"
                :options="statusList"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="月份">
              <a-month-picker style="width: 100%" v-model:value="queryParam.month" valueFormat="YYYY-MM"
                              placeholder="请选择月份" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="任务ID">
              <a-input
                v-model:value="queryParam.jobId"
                placeholder="请输入任务ID"
                allowClear
                @pressEnter="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <template v-if="advanced">
            <a-col :md="6" :sm="24">
              <a-form-item label="分区">
                <a-select
                  v-model:value="queryParam.part"
                  placeholder="请选择分区"
                  :options="partList"
                  allowClear
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="所属小组">
                <a-select
                  v-model:value="queryParam.group"
                  placeholder="请选择小组"
                  :options="groupList"
                  allowClear
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="邮箱">
                <a-input v-model:value="queryParam.email" placeholder="模糊查詢" allowClear
                         @pressEnter="$refs.table.refresh()"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="(!advanced && 6) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operations" v-if="hasAdminRole">
      <tx-button type="primary" class="columnSetting" :loading="confirmLoadingExport"
                 @click="handleExport">
        导出
      </tx-button>
      <tx-button class="columnSetting" @click="showExportHistory" style="margin-left: 3px">
        <a-icon type="unordered-list-outlined" />
      </tx-button>
      <a-modal
        v-model:visible="exportHistoryVisible"
        center
        :footer="null"
        width="50%"
      >
        <s-table
          ref="tableExportHistory"
          :rowKey="(record) => record.id"
          :columns="exportHistoryColumns"
          :data="loadExportHistoryData"
        >
          <template #bodyCell="{column, index, record, text}">
            <template v-if="column.dataIndex === 'resultLink'">
              <div v-if="record.resultLink!==''">
                <a :href="record.resultLink.replace('http:','https:')">下载</a>
              </div>
              <div v-else>
                -
              </div>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}</a-tag>
            </template>
          </template>
        </s-table>
      </a-modal>
    </div>
    <div style="position: relative">
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="statusColorFilter(record.status)">{{ statusFilter(record.status) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'part'">
            <a-tag :color="partColorFilter(record.part)">{{ partFilter(record.part) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'group'">
            <div v-if="record.group !== ''">
              <a-tag :color="groupColorFilter(record.group)">{{ groupFilter(record.group) }}</a-tag>
            </div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="ID">{{ record.id }}</a-descriptions-item>
            <a-descriptions-item label="GPU类型">{{ record.gpuType }}</a-descriptions-item>
            <a-descriptions-item label="开始时间">{{ record.startRunTime }}</a-descriptions-item>
            <a-descriptions-item label="结束时间">{{ record.estimatedRunTime }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
    </div>
  </a-card>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { STable } from '@/components'
import { setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { exportSlurmCostMonth, listSlurmCostMonth } from '@/api/ai/slurm'
import { notification } from 'ant-design-vue'
import { getFileTaskList } from '@/api/cost/month'

const columns = [
  {
    title: '月份',
    dataIndex: 'month'
  },
  {
    title: '任务ID',
    dataIndex: 'jobId'
  },
  {
    title: '分区',
    dataIndex: 'part',
    scopedSlots: { customRender: 'part' }
  },
  {
    title: '任务名称',
    dataIndex: 'name'
  },
  {
    title: '用户邮箱',
    dataIndex: 'email'
  },
  {
    title: '用户组',
    dataIndex: 'group',
    scopedSlots: { customRender: 'group' }
  },
  {
    title: '节点数',
    dataIndex: 'nodeNum'
  },
  {
    title: '单节点卡数',
    dataIndex: 'gpuNum'
  },
  {
    title: '当月运行时长(分)',
    dataIndex: 'runTimeNum'
  },
  {
    title: '定价费用',
    dataIndex: 'cost'
  },
  {
    title: '实际费用',
    dataIndex: 'costActual'
  },
  {
    title: '维度',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' }
  }
]

const statusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'blue',
    text: '初始数据'
  },
  2: {
    color: 'green',
    text: '拆分数据'
  },
  11: {
    color: '#bae0ff',
    text: 'API月度计费'
  },
  100: {
    color: '#8c8c8c',
    text: '不统计'
  },
  101: {
    color: '#ffd6e7',
    text: 'UCloud'
  }
}

const partMap = {
  'A800': {
    color: '#87d068',
    text: 'A800'
  },
  'A100': {
    color: '#2db7f5',
    text: 'A100'
  },
  'A10': {
    color: 'cyan',
    text: 'A10'
  },
  'H800': {
    color: '#108ee9',
    text: 'H800'
  },
  'new-H800': {
    color: '#108ee9',
    text: 'new-H800'
  },
  '3090': {
    color: '#CDAA7D',
    text: '3090'
  },
  '5-a100': {
    color: '#f50',
    text: '5-a100'
  },
  'debug': {
    color: 'orange',
    text: 'debug'
  }
}

const groupMap = {
  'acg': {
    color: '#ffd591',
    text: 'acg'
  },
  'nlp': {
    color: '#13c2c2',
    text: 'nlp'
  },
  'ipt': {
    color: '#95de64',
    text: 'ipt'
  },
  'cv': {
    color: '#91caff',
    text: 'cv'
  },
  'cs': {
    color: '#1677ff',
    text: 'cs'
  },
  'cc': {
    color: '#52c41a',
    text: 'cc'
  }
}

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt'
  },
  {
    title: '用户',
    dataIndex: 'creator'
  },
  {
    title: '导出状态',
    dataIndex: 'status'
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink'
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'green',
    text: '成功'
  },
  2: {
    color: 'red',
    text: '失败'
  },
  3: {
    color: 'blue',
    text: '运行中'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'SlurmCostJobDetail',
  components: {
    STable
  },
  props: {
    mysqlInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    this.columns = columns
    this.exportHistoryColumns = exportHistoryColumns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hasAdminRole: false,
      queryParam: {
        status: 1
      },
      addForm: {},
      visible: false,
      updateVisible: false,
      updateForm: {},
      confirmLoading: false,
      advanced: false,
      rules: {
        gpuType: [{ required: true, message: '请选择GPU类型', trigger: 'change' }],
        cost: [{ required: true, message: '请输入费用', trigger: 'change' }]
      },
      gpuTypeList: [{ label: 'H800', value: 'H800' }, { label: 'A800', value: 'A800' },
        { label: 'A100', value: 'A100' }, { label: 'A10', value: 'A10' }, { label: '4090D', value: '4090D' },
        { label: '3090', value: '3090' }, { label: '3080Ti', value: '3080Ti' }, { label: '3080', value: '3080' },
        { label: '2080Ti', value: '2080Ti' }],
      statusList: [
        { label: '初始数据', value: 1 },
        { label: '拆分数据', value: 2 }
        // { label: 'API月度计费', value: 11 },
        // { label: '不统计', value: 100 },
        // { label: 'UCloud', value: 101 }
      ],
      partList: [
        { label: 'H800', value: 'H800' }, { label: 'new-H800', value: 'new-H800' }, { label: 'A800', value: 'A800' },
        { label: '5-a100', value: '5-a100' }, { label: 'A10', value: 'A10' }, { label: 'debug', value: 'debug' },
        { label: '3090', value: '3090' }
      ],
      groupList: [
        { label: 'cv', value: 'cv' }, { label: 'ipt', value: 'ipt' }, { label: 'cs', value: 'cs' },
        { label: 'nlp', value: 'nlp' }, { label: 'acg', value: 'acg' }, { label: 'cc', value: 'cc' }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listSlurmCostMonth(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      exportHistoryVisible: false,
      confirmLoadingExport: false,
      loadExportHistoryData: (parameter) => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = 19
        return getFileTaskList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            } else {
              return res.Data
            }
          } else {
            return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
          }
        })
      }
    }
  },
  created () {
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('ai_admin') || this.userRoles.includes('admin') || this.userRoles.includes('cost_admin')) {
          this.hasAdminRole = true
        }
      })
    },
    statusFilter (type) {
      return statusMap[type]?.text || type
    },
    statusColorFilter (type) {
      return statusMap[type]?.color || type
    },
    partFilter (type) {
      return partMap[type]?.text || type
    },
    partColorFilter (type) {
      return partMap[type]?.color || type
    },
    groupFilter (type) {
      return groupMap[type]?.text || type
    },
    groupColorFilter (type) {
      return groupMap[type]?.color || type
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    handleExport () {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      exportSlurmCostMonth(requestParameters).then((res) => {
        if (res !== undefined && res.Code === 200) {
          notification.success({
            message: '导出成功，点击右侧按钮，查看导出记录和结果'
          })
          this.confirmLoadingExport = false
        } else {
          notification.error({
            message: '导出失败'
          })
          this.confirmLoadingExport = false
        }
      }).catch(() => {
        this.confirmLoadingExport = false
      })
    },
    exportStatusFilter (type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter (type) {
      return exportStatusMap[type]?.color || type
    },
    showExportHistory () {
      this.exportHistoryVisible = true
      this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

.columnSetting {
  position: relative;
  right: 0;
}
</style>
