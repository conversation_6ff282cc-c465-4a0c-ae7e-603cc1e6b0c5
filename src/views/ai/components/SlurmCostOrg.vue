<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="5" :sm="24">
            <a-form-item label="维度">
              <a-select
                v-model:value="queryParam.status"
                placeholder="请选择维度"
                :options="statusList"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="24">
            <a-form-item label="月份">
              <a-month-picker
                style="width: 100%"
                v-model:value="queryParam.month"
                valueFormat="YYYY-MM"
                placeholder="请选择月份"
              />
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="24">
            <a-form-item label="事业部">
              <a-select
                v-model:value="queryParam.secondaryOrganization"
                placeholder="请选择事业部"
                :options="orgList"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <!--          <template v-if="advanced">-->
          <a-col :md="5" :sm="24">
            <a-form-item label="GPU类型">
              <a-select
                v-model:value="queryParam.gpuType"
                placeholder="请选择GPU类型"
                :options="gpuTypeList"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <!--          </template>-->
          <!--          <a-col :md="(!advanced && 4) || 24" :sm="24">-->
          <a-col :md="4" :sm="24">
            <!--            <span-->
            <!--              class="table-page-search-submitButtons"-->
            <!--              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"-->
            <!--            >-->
            <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
            <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            <!--              <a @click="toggleAdvanced" style="margin-left: 8px">-->
            <!--                {{ advanced ? '收起' : '展开' }}-->
            <!--                <a-icon :type="advanced ? 'up' : 'down'" />-->
            <!--              </a>-->
            <!--            </span>-->
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operations" v-if="hasAdminRole">
      <tx-button type="primary" class="columnSetting" :loading="confirmLoadingExport" @click="handleExport">
        导出
      </tx-button>
      <tx-button class="columnSetting" @click="showExportHistory" style="margin-left: 3px">
        <a-icon type="unordered-list-outlined" />
      </tx-button>
      <a-modal v-model:visible="exportHistoryVisible" center :footer="null" width="50%">
        <s-table
          ref="tableExportHistory"
          :rowKey="record => record.id"
          :columns="exportHistoryColumns"
          :data="loadExportHistoryData"
        >
          <template #bodyCell="{ column, index, record, text }">
            <template v-if="column.dataIndex === 'resultLink'">
              <div v-if="record.resultLink !== ''">
                <a :href="record.resultLink.replace('http:', 'https:')">下载</a>
              </div>
              <div v-else>-</div>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}</a-tag>
            </template>
          </template>
        </s-table>
      </a-modal>
    </div>
    <div style="position: relative">
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'gpuType'">
            <a-tag :color="gpuTypeColorFilter(record.gpuType)">{{ gpuTypeFilter(record.gpuType) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag :color="statusColorFilter(record.status)">{{ statusFilter(record.status) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'secondaryOrganization'">
            <div v-if="record.status >= 10 && record.status <= 13">
              <a-tag :color="secondaryOrganizationColorFilter(record.secondaryOrganization)">
                {{ record.secondaryOrganization }}{{ record.tertiaryOrganization }}
              </a-tag>
            </div>
            <div v-else>
              <a-tag :color="secondaryOrganizationColorFilter(record.secondaryOrganization)">
                {{ secondaryOrganizationFilter(record.secondaryOrganization) }}
              </a-tag>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'ratio'">
            <div v-if="record.status === 6">{{ (record.ratio * 100).toFixed(2) }}%</div>
            <div v-else>--</div>
          </template>
        </template>
      </s-table>
    </div>
  </a-card>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { STable } from '@/components'
import { setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { exportSlurmCostMonthStatistical, listSlurmCostMonthStatistical } from '@/api/ai/slurm'
import { getFileTaskList } from '@/api/cost/month'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '月份',
    dataIndex: 'month',
  },
  {
    title: 'GPU类型',
    dataIndex: 'gpuType',
    scopedSlots: { customRender: 'gpuType' },
  },
  {
    title: '任务数量',
    dataIndex: 'jobNum',
  },
  {
    title: 'GPU卡时长',
    dataIndex: 'gpuRunTimeNum',
  },
  {
    title: '定价费用',
    dataIndex: 'cost',
  },
  {
    title: '实际费用',
    dataIndex: 'costActual',
  },
  {
    title: '租赁费用',
    dataIndex: 'costLeaseRental',
  },
  {
    title: '折旧费用',
    dataIndex: 'costGpuAsset',
  },
  {
    title: '二级组织',
    dataIndex: 'secondaryOrganization',
    scopedSlots: { customRender: 'secondaryOrganization' },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '比例',
    dataIndex: 'ratio',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '维度',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
  },
]

const secondaryOrganizationMap = {
  AIM: {
    color: '#ffd591',
    text: 'AIM',
  },
  ACG智能创新: {
    color: '#13c2c2',
    text: 'ACG智能创新',
  },
  CS: {
    color: '#95de64',
    text: 'CS',
  },
  SSG: {
    color: '#91caff',
    text: 'SSG',
  },
  'CS-CC': {
    color: '#1677ff',
    text: 'CS-CC',
  },
  DG: {
    color: '#52c41a',
    text: 'DG',
  },
}

const gpuTypeMap = {
  H800: {
    color: '#108ee9',
    text: 'H800',
  },
  A800: {
    color: '#87d068',
    text: 'A800',
  },
  A100: {
    color: '#2db7f5',
    text: 'A100',
  },
  A10: {
    color: 'cyan',
    text: 'A10',
  },
  3090: {
    color: '#CDAA7D',
    text: '3090',
  },
}

const statusMap = {
  0: {
    color: 'orange',
    text: '未知',
  },
  6: {
    color: 'blue',
    text: '按事业部-分区统计',
  },
  7: {
    color: 'green',
    text: '按事业部统计',
  },
  8: {
    color: '#8c8c8c',
    text: '分摊后按事业部-分区统计',
  },
  9: {
    color: '#bae0ff',
    text: '分摊后按事业部统计',
  },
  10: {
    color: '#b37feb',
    text: '按成本中心-分区统计',
  },
  11: {
    color: '#adc6ff',
    text: '按成本中心统计',
  },
  12: {
    color: '#bae0ff',
    text: '分摊后按成本中心-分区统计',
  },
  13: {
    color: '#9254de',
    text: '分摊后按成本中心统计',
  },
}

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt',
  },
  {
    title: '用户',
    dataIndex: 'creator',
  },
  {
    title: '导出状态',
    dataIndex: 'status',
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink',
  },
  {
    title: '备注',
    dataIndex: 'comment',
  },
]

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知',
  },
  1: {
    color: 'green',
    text: '成功',
  },
  2: {
    color: 'red',
    text: '失败',
  },
  3: {
    color: 'blue',
    text: '运行中',
  },
}

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SlurmCostUser',
  components: {
    STable,
  },
  props: {
    mysqlInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    this.columns = columns
    this.exportHistoryColumns = exportHistoryColumns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hasAdminRole: false,
      queryParam: {
        status: 6,
      },
      advanced: false,
      gpuTypeList: [
        { label: 'H800', value: 'H800' },
        { label: 'A800', value: 'A800' },
        { label: 'A100', value: 'A100' },
        { label: 'A10', value: 'A10' },
        { label: '4090D', value: '4090D' },
        { label: '3090', value: '3090' },
        { label: '3080Ti', value: '3080Ti' },
        { label: '3080', value: '3080' },
        { label: '2080Ti', value: '2080Ti' },
      ],
      statusList: [
        { label: '按事业部-分区统计', value: 6 },
        { label: '按事业部统计', value: 7 },
        { label: '分摊后按事业部-分区统计', value: 8 },
        { label: '分摊后按事业部统计', value: 9 },
        { label: '按成本中心-分区统计', value: 10 },
        { label: '按成本中心统计', value: 11 },
        { label: '分摊后按成本中心-分区统计', value: 12 },
        { label: '分摊后按成本中心统计', value: 13 },
      ],
      orgList: [
        { label: 'AIM', value: 'AIM' },
        { label: 'ACG智能创新', value: 'ACG智能创新' },
        { label: 'CS', value: 'CS' },
        { label: 'SSG', value: 'SSG' },
        { label: 'CS-CC', value: 'CS-CC' },
        { label: 'DG', value: 'DG' },
      ],
      groupList: [
        { label: 'cv', value: 'cv' },
        { label: 'ipt', value: 'ipt' },
        { label: 'cs', value: 'cs' },
        { label: 'nlp', value: 'nlp' },
        { label: 'acg', value: 'acg' },
        { label: 'cc', value: 'cc' },
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        requestParameters.taskType = 22
        return listSlurmCostMonthStatistical(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      exportHistoryVisible: false,
      confirmLoadingExport: false,
      loadExportHistoryData: parameter => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = 22
        return getFileTaskList(requestParameters).then(res => {
          console.log(res)
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  created() {},
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (
          this.userRoles.includes('ai_admin') ||
          this.userRoles.includes('admin') ||
          this.userRoles.includes('cost_admin')
        ) {
          this.hasAdminRole = true
        }
      })
    },
    gpuTypeFilter(type) {
      return gpuTypeMap[type]?.text || type
    },
    gpuTypeColorFilter(type) {
      return gpuTypeMap[type]?.color || type
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusColorFilter(type) {
      return statusMap[type]?.color || type
    },
    secondaryOrganizationFilter(type) {
      return secondaryOrganizationMap[type]?.text || type
    },
    secondaryOrganizationColorFilter(type) {
      return secondaryOrganizationMap[type]?.color || type
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    handleExport() {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      requestParameters.taskType = 22
      exportSlurmCostMonthStatistical(requestParameters)
        .then(res => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '导出成功，点击右侧按钮，查看导出记录和结果',
            })
            this.confirmLoadingExport = false
          } else {
            notification.error({
              message: '导出失败',
            })
            this.confirmLoadingExport = false
          }
        })
        .catch(() => {
          this.confirmLoadingExport = false
        })
    },
    exportStatusFilter(type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter(type) {
      return exportStatusMap[type]?.color || type
    },
    showExportHistory() {
      this.exportHistoryVisible = true
      console.log(this.exportHistoryVisible)
      this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

.columnSetting {
  position: relative;
  right: 0;
}
</style>
