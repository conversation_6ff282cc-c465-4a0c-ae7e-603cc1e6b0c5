<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :bordered="false"
      :tabList="tabListNoTitle"
      :activeTabKey="noTitleKey"
      @tabChange="key => handleTabChange(key, 'noTitleKey')"
    >
      <slurm-report-user v-if="noTitleKey === 'user'"></slurm-report-user>
      <slurm-report-group v-else-if="noTitleKey === 'group'"></slurm-report-group>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import SlurmReportUser from './SlurmJobReportUser.vue'
import SlurmReportGroup from './SlurmJobReportGroup.vue'

export default {
  components: {
    SlurmReportUser,
    SlurmReportGroup,
  },
  data() {
    return {
      tabListNoTitle: [
        {
          key: 'user',
          tab: '按用户统计',
        },
        {
          key: 'group',
          tab: '按组统计',
        },
      ],
      noTitleKey: 'user',
    }
  },
  methods: {
    handleTabChange(key, type) {
      this[type] = key
    },
  },
}
</script>

<style scoped></style>
