<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="4" :sm="24">
            <a-form-item label="分区">
              <a-select
                v-model:value="queryParam.part"
                @pressEnter="$refs.table.refresh()"
                allowClear
                placeholder="请选择"
              >
                <a-select-option value="A10">A10</a-select-option>
                <a-select-option value="A100">A100</a-select-option>
                <a-select-option value="A800">A800</a-select-option>
                <a-select-option value="H800">H800</a-select-option>
                <a-select-option value="new-H800">new-H800</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item label="用户组">
              <a-select
                v-model:value="queryParam.group"
                @pressEnter="$refs.table.refresh()"
                allowClear
                placeholder="请选择"
              >
                <a-select-option value="cv">CV</a-select-option>
                <a-select-option value="nlp">NLP</a-select-option>
                <a-select-option value="ipt">IPT</a-select-option>
                <a-select-option value="acg">ACG</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="7" :sm="24">
            <a-form-item label="日期范围">
              <a-range-picker v-model:value="dateRange" />
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => ((this.queryParam = {}), (this.dateRange = []))">
                重置
              </tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="ID">{{ record.id }}</a-descriptions-item>
          <a-descriptions-item label="完成任务数">{{ record.completedTask }}</a-descriptions-item>
          <a-descriptions-item label="运行任务数">{{ record.runningTask }}</a-descriptions-item>
          <a-descriptions-item label="失败任务数">{{ record.failedTask }}</a-descriptions-item>
          <a-descriptions-item label="取消任务数">{{ record.cancelledTask }}</a-descriptions-item>
          <a-descriptions-item label="排队任务数">{{ record.pendingTask }}</a-descriptions-item>
          <a-descriptions-item label="超时任务数">{{ record.timeoutTask }}</a-descriptions-item>
          <a-descriptions-item label="配置中任务数">{{ record.configuringTask }}</a-descriptions-item>
          <a-descriptions-item label="完成中任务数">{{ record.completingTask }}</a-descriptions-item>
          <a-descriptions-item label="排队时长均值">{{ record.pendingTimeAvg }}分钟</a-descriptions-item>
          <a-descriptions-item label="排队时长P50">{{ record.pendingTime50 }}分钟</a-descriptions-item>
          <a-descriptions-item label="排队时长P99">{{ record.pendingTime99 }}分钟</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'part'">
          <a-tag v-if="text === 'A800'" color="#87d068">{{ text }}</a-tag>
          <a-tag v-else-if="text === 'A100'" color="#2db7f5">{{ text }}</a-tag>
          <a-tag v-else-if="text === 'A10'" color="cyan">{{ text }}</a-tag>
          <a-tag v-else-if="text === 'H800'" color="#108ee9">{{ text }}</a-tag>
          <a-tag v-else-if="text === 'new-H800'" color="#108ee9">{{ text }}</a-tag>
          <a-tag v-else color="#f50">{{ text }}</a-tag>
        </template>
      </template>
    </s-table>
  </a-card>
</template>

<script>
import dayjs from 'dayjs'
import { STable, Ellipsis } from '@/components'
import { slurmJobReportByGroupList } from '@/api/ai/slurm'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const columns = [
  {
    title: '日期',
    dataIndex: 'date',
    sorter: true,
  },
  {
    title: '分区',
    dataIndex: 'part',
    scopedSlots: { customRender: 'part' },
    sorter: true,
  },
  {
    title: '用户组',
    dataIndex: 'group',
  },
  {
    title: '运行时长(分钟)',
    dataIndex: 'runningTime',
    sorter: true,
  },
  {
    title: '费用(元)',
    dataIndex: 'gpuCost',
    sorter: true,
  },
  {
    title: 'GPU使用率(%)',
    dataIndex: 'gpuUtil',
    sorter: true,
  },
  {
    title: 'GPU使用时长',
    dataIndex: 'gpuUsageTime',
    sorter: true,
  },
  {
    title: '总任务数',
    dataIndex: 'totalTask',
    sorter: true,
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SlurmJobReportGroup',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      queryParam: {},
      dateRange: [],
      advanced: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        if (this.dateRange.length > 0) {
          this.queryParam.startDate = dayjs(this.dateRange[0]).format('YYYY-MM-DD')
          this.queryParam.endDate = dayjs(this.dateRange[1]).format('YYYY-MM-DD')
        }
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return slurmJobReportByGroupList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
  },
}
</script>
