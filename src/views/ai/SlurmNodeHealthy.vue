<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ip" placeholder="模糊匹配" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="GPU卡状态">
                <a-input v-model:value="queryParam.gpuStatus" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div
        class="table-operator"
        v-if="
          localUser === '<EMAIL>' ||
          localUser === '<EMAIL>' ||
          localUser === '<EMAIL>' ||
          localUser === '<EMAIL>'
        "
      >
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
        class="slurmNodeHCTable"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'part'">
            <a-tag v-if="text === 'A800'" color="#87d068">{{ text }}</a-tag>
            <a-tag v-else-if="text === 'A100'" color="#2db7f5">{{ text }}</a-tag>
            <a-tag v-else-if="text === 'A10'" color="cyan">{{ text }}</a-tag>
            <a-tag v-else-if="text === 'H800'" color="#108ee9">{{ text }}</a-tag>
            <a-tag v-else-if="text === 'new-H800'" color="#108ee9">{{ text }}</a-tag>
            <a-tag v-else color="#f50">{{ text }}</a-tag>
          </template>

          <template v-if="column.dataIndex === 'juiceFsCheck'">
            <a-tag v-if="text === 1" color="#87d068">是</a-tag>
            <a-tag v-else color="#f50">否</a-tag>
          </template>
          <template v-if="column.dataIndex === 'seaweedFsCheck'">
            <a-tag v-if="text === 1" color="#87d068">是</a-tag>
            <a-tag v-else color="#f50">否</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'newH800MountCheck'">
            <a-tag v-if="text === 1" color="#87d068">是</a-tag>
            <a-tag v-else color="#f50">否</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'beegFsCheck'">
            <a-tag v-if="text === 1" color="#87d068">是</a-tag>
            <a-tag v-else color="#f50">否</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'gpuStatus'">
            <a-tag v-if="text === 'ok'" color="#87d068">{{ text }}</a-tag>
            <a-tag v-else color="#f50">{{ text }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'juiceFsStatus'">
            <a-tag v-if="text === 'ok'" color="#87d068">{{ text }}</a-tag>
            <a-tag v-else color="#f50">{{ text }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'seaweedFsStatus'">
            <a-tag v-if="text === 'ok'" color="#87d068">{{ text }}</a-tag>
            <a-tag v-else color="#f50">{{ text }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'newH800MountStatus'">
            <a-tag v-if="text === 'ok'" color="#87d068">{{ text }}</a-tag>
            <a-tag v-else color="#f50">{{ text }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'beegFsStatus'">
            <a-tag v-if="text === 'ok'" color="#87d068">{{ text }}</a-tag>
            <a-tag v-else color="#f50">{{ text }}</a-tag>
          </template>
          <template
            v-if="
              column.dataIndex == 'action' &&
              (localUser === '<EMAIL>' ||
                localUser === '<EMAIL>' ||
                localUser === '<EMAIL>' ||
                localUser === '<EMAIL>'||
                localUser === '<EMAIL>')
            "
          >
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <a-modal title="新增" :visible="visible" :confirm-loading="confirmLoading" @ok="handleOk" @cancel="handleCancel">
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="form.ip" />
          </a-form-model-item>
          <a-form-model-item label="主机名" name="hostname">
            <a-input v-model:value="form.hostname" />
          </a-form-model-item>
          <a-form-model-item label="分区" name="part">
            <a-select v-model:value="form.part" placeholder="请选择分区">
              <a-select-option v-for="item1 in partList" :key="item1" :value="item1">
                {{ item1 }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="GPU卡数" name="gpuNum">
            <a-input-number v-model:value="form.gpuNum" />
          </a-form-model-item>
          挂载点检查
          <a-divider class="ant-divider-horizonta1l" />
          <a-form-model-item label="Seaweedfs" name="seaweedfsCheck">
            <a-radio-group v-model:value="form.seaweedFsCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="Juicefs" name="juicefsCheck">
            <a-radio-group v-model:value="form.juiceFsCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="NewH800" name="newH800MountCheck">
            <a-radio-group v-model:value="form.newH800MountCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="Beegfs" name="beegFsCheck">
            <a-radio-group v-model:value="form.beegFsCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="form.ip" />
          </a-form-model-item>
          <a-form-model-item label="主机名" name="hostname">
            <a-input v-model:value="form.hostname" />
          </a-form-model-item>
          <a-form-model-item label="分区" name="part">
            <a-select v-model:value="form.part" placeholder="请选择分区">
              <a-select-option v-for="item1 in partList" :key="item1" :value="item1">
                {{ item1 }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="GPU卡数" name="gpuNum">
            <a-input-number v-model:value="form.gpuNum" />
          </a-form-model-item>
          挂载点检查
          <a-divider class="ant-divider-horizonta1l" />
          <a-form-model-item label="Juicefs" name="juicefsCheck">
            <a-radio-group v-model:value="form.juiceFsCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="Seaweedfs" name="seaweedfsCheck">
            <a-radio-group v-model:value="form.seaweedFsCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="NewH800" name="newH800MountCheck">
            <a-radio-group v-model:value="form.newH800MountCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="Beegfs" name="beegFsCheck">
            <a-radio-group v-model:value="form.beegFsCheck" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import {
  createSlurmNodeHealthy,
  deleteSlurmNodeHealthy,
  getSlurmNodeHealthy,
  slurmNodeHealthyList,
  updateSlurmNodeHealthy,
} from '@/api/ai/slurm'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true,
  },
  {
    title: 'IP',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: '主机名',
    dataIndex: 'hostname',
    sorter: true,
  },
  {
    title: '分区',
    dataIndex: 'part',
    sorter: true,
  },
  {
    title: 'GPU',
    children: [
      {
        title: '卡数',
        dataIndex: 'gpuNum',
        sorter: true,
      },
      {
        title: '卡数检查',
        dataIndex: 'gpuNumCheck',
        sorter: true,
      },
      {
        title: '卡状态',
        dataIndex: 'gpuStatus',
        scopedSlots: { customRender: 'gpuStatus' },
        sorter: true,
      },
    ],
  },

  {
    title: '挂载盘检查',
    children: [
      {
        title: 'juicefs',
        dataIndex: 'juiceFsCheck',
        scopedSlots: { customRender: 'juiceFsCheck' },
      },
      {
        title: 'seaweedfs',
        dataIndex: 'seaweedFsCheck',
        scopedSlots: { customRender: 'seaweedFsCheck' },
      },
      {
        title: 'NewH800',
        dataIndex: 'newH800MountCheck',
        scopedSlots: { customRender: 'newH800MountCheck' },
      },
      {
        title: 'beegfs',
        dataIndex: 'beegFsCheck',
        scopedSlots: { customRender: 'beegFsCheck' },
      },
    ],
  },
  {
    title: '挂载盘状态',
    children: [
      {
        title: 'juicefs',
        dataIndex: 'juiceFsStatus',
        scopedSlots: { customRender: 'juiceFsStatus' },
      },
      {
        title: 'seaweedfs',
        dataIndex: 'seaweedFsStatus',
        scopedSlots: { customRender: 'seaweedFsStatus' },
      },
      {
        title: 'NewH800',
        dataIndex: 'newH800MountStatus',
        scopedSlots: { customRender: 'newH800MountStatus' },
      },
      {
        title: 'beegfs',
        dataIndex: 'beegFsStatus',
        scopedSlots: { customRender: 'beegFsStatus' },
      },
    ],
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SlurmNodeHealthy',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      advanced: false,
      localUser: store.getters.email,
      queryParam: {},
      form: {
        ip: undefined,
        hostname: undefined,
        gpuNum: undefined,
        newH800MountCheck: 1,
        seaweedfsCheck: 1,
        juicefsCheck: 1,
        beegFsCheck: 1,
      },
      partList: ['A10', 'A100', 'A800', 'H800', 'new-H800', '其他', "NLP", "IPT", "CV", "CV_Innovation", "ACG"],
      visible: false,
      updateVisible: false,
      confirmLoading: false,
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return slurmNodeHealthyList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        ip: [{ required: true, message: '请填写IP', trigger: 'change' }],
        part: [{ required: true, message: '请选择分区', trigger: 'change' }],
        hostname: [{ required: true, message: '请填写主机名', trigger: 'change' }],
        gpuNum: [{ required: true, message: '请填写GPU卡数', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  unmounted() {
    removeWatermark()
  },
  methods: {
    handleAdd() {
      ;(this.form = {
        ip: undefined,
        hostname: undefined,
        gpuNum: undefined,
        newH800MountCheck: 1,
        seaweedFsCheck: 1,
        juiceFsCheck: 1,
        beegFsCheck: 1,
      }),
        (this.form.gpuNum = 8)
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createSlurmNodeHealthy(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      getSlurmNodeHealthy(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateSlurmNodeHealthy(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteSlurmNodeHealthy(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>

<style lang="less" scoped>
.slurmNodeHCTable {
  /deep/ .ant-table-thead > tr > th {
    background: #fafafa;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    position: relative;
    text-align: center;
    transition: background 0.3s ease;
    border: 2px solid #f7f5f5;
  }
}

.ant-divider-horizonta1l {
  clear: both;
  display: flex;
  margin: 10px 0;
  min-width: 100%;
  width: 100%;
}
</style>
