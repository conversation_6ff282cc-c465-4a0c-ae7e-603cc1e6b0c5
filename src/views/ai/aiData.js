export const columns = [
  {
    title: '任务ID',
    dataIndex: 'jobId',
    sorter: true,
  },
  {
    title: '分区',
    dataIndex: 'part',
    scopedSlots: { customRender: 'part' },
    sorter: true,
  },
  {
    title: '节点数',
    dataIndex: 'nodeNum',
    scopedSlots: { customRender: 'nodeNum' },
    sorter: true,
  },
  {
    title: '作业名',
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: '作业类型',
    dataIndex: 'taskType',
    sorter: true,
  },
  {
    title: '是否插队',
    dataIndex: 'qos',
    scopedSlots: { customRender: 'qos' },
    sorter: true,
  },
  {
    title: '用户名',
    dataIndex: 'user',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
    sorter: true,
    width: '120px',
  },
  {
    title: '已运行时长',
    dataIndex: 'runTime',
    sorter: true,
  },
  {
    title: '费用',
    dataIndex: 'gpuCost',
    sorter: true,
  },
  {
    title: 'GPU SM利用率',
    dataIndex: 'smActive',
    scopedSlots: { customRender: 'smActive' },
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '180px',
    align: 'center',
  },
]
export const myColumns = [
  {
    title: '任务ID',
    dataIndex: 'jobId',
    scopeKey: '&c56',
  },
  {
    title: '分区',
    dataIndex: 'part',
    scopeKey: 't890',
  },
  {
    title: '节点数',
    dataIndex: 'nodeNum',
    scopeKey: 't234',
  },
  {
    title: '作业名',
    dataIndex: 'name',
    scopeKey: '5678',
  },
  {
    title: '作业类型',
    dataIndex: 'taskType',
    scopeKey: 's0123',
  },
  {
    title: '作业时间',
    dataIndex: 'taskTime',
    scopeKey: '4x67',
  },
  { title: '作业次数', dataIndex: 'taskNum', scopeKey: 'w9012' },
]
export const statusMap = {
  PENDING: {
    status: 'warning',
    text: '排队中',
  },
  RUNNING: {
    status: 'processing',
    text: '运行中',
  },
  CANCELLED: {
    status: 'default',
    text: '已取消',
  },
  CONFIGURING: {
    status: 'processing',
    text: '配置中',
  },
  COMPLETING: {
    status: 'processing',
    text: '完成中',
  },
  COMPLETED: {
    status: 'success',
    text: '已完成',
  },
  FAILED: {
    status: 'error',
    text: '已失败',
  },
  TIMEOUT: {
    status: 'error',
    text: '超时',
  },
  SUSPENDED: {
    status: 'warning',
    text: '挂起',
  },
  'NODE FAILURE': {
    status: 'error',
    text: '节点失效',
  },
  'SPECIAL EXIT STATE': {
    status: 'error',
    text: '特殊退出状态',
  },
}

export const statusOptions = [
  {
    value: 'PENDING',
    label: '排队中',
  },
  {
    value: 'RUNNING',
    label: '运行中',
  },
  {
    value: 'CANCELLED',
    label: '已取消',
  },
  {
    value: 'CONFIGURING',
    label: '配置中',
  },
  {
    value: 'COMPLETING',
    label: '完成中',
  },
  {
    value: 'COMPLETED',
    label: '已完成',
  },
  {
    value: 'FAILED',
    label: '已失败',
  },
  {
    value: 'TIMEOUT',
    label: '超时',
  },
  {
    value: 'SUSPENDED',
    label: '挂起',
  },
  {
    value: 'NODE FAILURE',
    label: '节点失效',
  },
  {
    value: 'SPECIAL EXIT STATE',
    label: '特殊退出状态',
  },
]

export const isExpiredOptions = [
  {
    value: 1,
    label: '未结束',
  },
  {
    value: 2,
    label: '已结束',
  },
]
