<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="7" :sm="24">
              <a-form-item label="用户名/邮箱">
                <a-input
                  v-model:value="queryParam.email"
                  placeholder="支持模糊搜索"
                  @pressEnter="$refs.table.refresh()"
                  allowClear
                />
              </a-form-item>
            </a-col>
            <a-col :md="7" :sm="24">
              <a-form-item label="用户组">
                <a-input
                  v-model:value="queryParam.group"
                  placeholder="支持模糊搜索"
                  @pressEnter="$refs.table.refresh()"
                  allowClear
                />
              </a-form-item>
            </a-col>
            <a-col :md="10" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a-popconfirm placement="topRight" @confirm="slurmAuth()">
                  <template #title>
                    <p>请先在Slurm用户页面搜索是否用户是否存在。</p>
                    <p>此入口仅供新的Slurm权限申请使用！！！</p>
                  </template>
                  <template #icon>
                    <a-icon type="question-circle-o" style="color: red" />
                  </template>
                  <tx-button type="primary" style="float: right">Slurm权限申请</tx-button>
                </a-popconfirm>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
          </template>
        </template>
      </s-table>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="用户组" name="group">
            <a-input v-model:value="form.group" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { slurmUserList, getSlurmUserInfo, updateSlurmUser, deleteSlurmUser } from '@/api/ai/slurm'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: '用户名',
    dataIndex: 'user',
    sorter: true,
  },
  {
    title: '用户组',
    dataIndex: 'group',
    sorter: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    sorter: true,
  },
  // {
  //   title: '操作',
  //   dataIndex: 'action',
  //   scopedSlots: { customRender: 'action' },
  //   width: '120px',
  //   align: 'center',
  // },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SlurmUser',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      form: {
        email: undefined,
        group: undefined,
      },
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return slurmUserList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleUpdate(record) {
      getSlurmUserInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateSlurmUser(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteSlurmUser(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    slurmAuth() {
      let routeUrl = this.$router.resolve({ path: '/workflow/slurm-auth', query: {} })
      window.open(routeUrl.href, '_blank')
    },
  },
}
</script>
