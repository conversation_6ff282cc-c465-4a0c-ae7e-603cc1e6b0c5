<template>
  <a-card
    class="WxIdeClass"
    style="width: 100%"
    :bordered="false"
    :tabList="tabListNoTitle"
    :activeTabKey="noTitleKey"
    @tabChange="key => handleTabChange(key, 'noTitleKey')"
  >
    <ai-ide-list v-if="noTitleKey === 'list'"></ai-ide-list>
    <ai-ide-user-gpu v-else-if="noTitleKey === 'quota'"></ai-ide-user-gpu>
    <ai-ide-node v-else-if="noTitleKey === 'node'"></ai-ide-node>
    <ai-ide-image v-else-if="noTitleKey === 'image'"></ai-ide-image>
  </a-card>
</template>

<script>
import AiIdeList from './AiIdeList.vue'
import AiIdeImage from './AiIdeImage.vue'
import AiIdeUserGpu from './AiIdeUserGpu.vue'
import AiIdeNode from '@/views/ai/AiIdeNode.vue'
import { inject } from 'vue'
export default {
  components: {
    AiIdeList,
    AiIdeImage,
    AiIdeUserGpu,
    AiIdeNode,
  },
  data() {
    return {
      tabListNoTitle: [
        {
          key: 'list',
          tab: 'IDE',
        },
        {
          key: 'quota',
          tab: 'GPU配额',
        },
        {
          key: 'node',
          tab: 'GPU卡资源',
        },
        {
          key: 'image',
          tab: '镜像',
        },
      ],
      noTitleKey: 'list',
    }
  },
  setup(context, props) {
    const toogleFull = inject('toogleFull', () => {})
    return {
      toogleFull,
    }
  },
  mounted() {
    let main = document.getElementsByClassName('WxIdeClass')[0].parentElement
    main.style.margin = 0
    main.style.padding = 8 + 'px'
    this.toogleFull()
  },
  methods: {
    handleTabChange(key, type) {
      this[type] = key
    },
  },
}
</script>

<style scoped></style>
