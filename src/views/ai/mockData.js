export const mockData = {
  //  gpu资源使用率
  usageRate: [
    { type: '分配率', value: 80 },
    { type: '闲置率', value: 20 },
  ],
  //   gpu使用率变化趋势
  usageRateTrade: [
    {
      id: '1',
      totalTime: 350,
      usageRate: 30,
    },
    {
      id: '2',
      totalTime: 220,
      usageRate: 20,
    },
    {
      id: '3',
      totalTime: 370,
      usageRate: 52,
    },
    {
      id: '4',
      totalTime: 150,
      usageRate: 76,
    },
    {
      id: '5',
      totalTime: 250,
      usageRate: 34,
    },
    {
      id: '6',
      totalTime: 850,
      usageRate: 86,
    },
  ],
  //   任务堆积度
  taskStackRate: [
    {
      id: '1991',
      value: 3,
      type: 'wait_task',
    },
    {
      id: '1992',
      value: 4,
      type: 'wait_task',
    },
    {
      id: '1993',
      value: 3.5,
      type: 'wait_task',
    },
    {
      id: '1994',
      value: 5,
      type: 'wait_task',
    },
    {
      id: '1995',
      value: 4.9,
      type: 'wait_task',
    },
    {
      id: '1996',
      value: 6,
      type: 'wait_task',
    },
    {
      id: '1997',
      value: 7,
      type: 'wait_task',
    },
    {
      id: '1998',
      value: 9,
      type: 'wait_task',
    },
    {
      id: '1999',
      value: 13,
      type: 'wait_task',
    },
    {
      id: '1991',
      value: 3,
      type: 'run_task',
    },
    {
      id: '1992',
      value: 4,
      type: 'run_task',
    },
    {
      id: '1993',
      value: 3.5,
      type: 'run_task',
    },
    {
      id: '1994',
      value: 5,
      type: 'run_task',
    },
    {
      id: '1995',
      value: 4.9,
      type: 'run_task',
    },
    {
      id: '1996',
      value: 6,
      type: 'run_task',
    },
    {
      id: '1997',
      value: 7,
      type: 'run_task',
    },
    {
      id: '1998',
      value: 9,
      type: 'run_task',
    },
    {
      id: '1999',
      value: 13,
      type: 'run_task',
    },
  ],
  // 小组GPU使用时长/费用
  groupCosts: [
    { name: 'CS', value: 33 },
    { name: 'ACG', value: 18 },
    { name: 'IPT', value: 17 },
    { name: 'NLP', value: 25 },
    { name: 'CV', value: 17 },
  ],
  //   小组GPU使用时长变化趋势
  groupTimeTrend: [
    {
      time: '20240601',
      value: 3,
      group: 'CS',
    },
    {
      time: '20240602',
      value: 4,
      group: 'CS',
    },
    {
      time: '20240603',
      value: 3.5,
      group: 'CS',
    },
    {
      time: '20240604',
      value: 5,
      group: 'CS',
    },

    {
      time: '20240601',
      value: 3,
      group: 'ACG',
    },
    {
      time: '20240602',
      value: 4,
      group: 'ACG',
    },
    {
      time: '20240603',
      value: 3.5,
      group: 'ACG',
    },
    {
      time: '20240604',
      value: 3.5,
      group: 'ACG',
    },

    {
      time: '20240601',
      value: 3,
      group: 'IPT',
    },
    {
      time: '20240602',
      value: 4,
      group: 'IPT',
    },
    {
      time: '20240603',
      value: 3.5,
      group: 'IPT',
    },
    {
      time: '20240604',
      value: 3.5,
      group: 'IPT',
    },
    {
      time: '20240601',
      value: 3,
      group: 'NLP',
    },
    {
      time: '20240602',
      value: 4,
      group: 'NLP',
    },
    {
      time: '20240603',
      value: 3.5,
      group: 'NLP',
    },
    {
      time: '20240604',
      value: 3.5,
      group: 'NLP',
    },
    {
      time: '20240601',
      value: 3,
      group: 'CV',
    },
    {
      time: '20240602',
      value: 4,
      group: 'CV',
    },
    {
      time: '20240603',
      value: 3.5,
      group: 'CV',
    },
    {
      time: '20240604',
      value: 3.5,
      group: 'CV',
    },
  ],
  //   小组GPU利用率
  groupUsageRate: [
    { name: 'CS', value: 38 },
    { name: 'ACG', value: 52 },
    { name: 'IPT', value: 61 },
    { name: 'NLP', value: 32 },
    { name: 'CV', value: 48 },
  ],
  // 小组GPU利用率变化趋势
  groupUsageTrend: [
    {
      time: '20240601',
      value: 20,
      category: 'CS',
    },
    {
      time: '20240602',
      value: 42,
      category: 'CS',
    },
    {
      time: '20240603',
      value: 63,
      category: 'CS',
    },
    {
      time: '20240604',
      value: 74,
      category: 'CS',
    },
    {
      time: '20240601',
      value: 45,
      category: 'ACG',
    },
    {
      time: '20240602',
      value: 23,
      category: 'ACG',
    },
    {
      time: '20240603',
      value: 62,
      category: 'ACG',
    },
    {
      time: '20240604',
      value: 43,
      category: 'ACG',
    },
    {
      time: '20240601',
      value: 62,
      category: 'IPT',
    },
    {
      time: '20240602',
      value: 23,
      category: 'IPT',
    },
    {
      time: '20240603',
      value: 63,
      category: 'IPT',
    },
    {
      time: '20240604',
      value: 86,
      category: 'IPT',
    },
    {
      time: '20240601',
      value: 31,
      category: 'NLP',
    },
    {
      time: '20240602',
      value: 12,
      category: 'NLP',
    },
    {
      time: '20240603',
      value: 74,
      category: 'NLP',
    },
    {
      time: '20240604',
      value: 45,
      category: 'NLP',
    },
    {
      time: '20240601',
      value: 12,
      category: 'CV',
    },
    {
      time: '20240602',
      value: 53,
      category: 'CV',
    },
    {
      time: '20240603',
      value: 32,
      category: 'CV',
    },
    {
      time: '20240604',
      value: 45,
      category: 'CV',
    },
  ],
  // 小组GPU利用等待时长
  groupWaitTime: {
    average: [
      { name: 'CS', value: 12 },
      { name: 'ACG', value: 42 },
      { name: 'IPT', value: 64 },
      { name: 'NLP', value: 34 },
      { name: 'CV', value: 45 },
    ],
    p50: [
      { name: 'CS', value: 75 },
      { name: 'ACG', value: 75 },
      { name: 'IPT', value: 89 },
      { name: 'NLP', value: 86 },
      { name: 'CV', value: 55 },
    ],
    p99: [
      { name: 'CS', value: 23 },
      { name: 'ACG', value: 42 },
      { name: 'IPT', value: 44 },
      { name: 'NLP', value: 12 },
      { name: 'CV', value: 86 },
    ],
  },
  //   小组等待时长变化趋势
  groupWaitTimeTrend: {
    average: [
      {
        name: 'name1',
        group: 'CS',
        value: 14500,
      },
      {
        name: 'name1',
        group: 'ACG',
        value: 8500,
      },
      {
        name: 'name1',
        group: 'CV',
        value: 10000,
      },
      {
        name: 'name1',
        group: 'NLP',
        value: 7000,
      },
      {
        name: 'name2',
        group: 'CS',
        value: 9000,
      },
      {
        name: 'name2',
        group: 'ACG',
        value: 8500,
      },
      {
        name: 'name2',
        group: 'CV',
        value: 11000,
      },
      {
        name: 'name2',
        group: 'NLP',
        value: 6000,
      },
      {
        name: 'name3',
        group: 'CS',
        value: 16000,
      },
      {
        name: 'name3',
        group: 'ACG',
        value: 5000,
      },
      {
        name: 'name3',
        group: 'CV',
        value: 6000,
      },
      {
        name: 'name3',
        group: 'NLP',
        value: 10000,
      },
      {
        name: 'name4',
        group: 'CS',
        value: 14000,
      },
      {
        name: 'name4',
        group: 'ACG',
        value: 9000,
      },
      {
        name: 'name4',
        group: 'CV',
        value: 10000,
      },
      {
        name: 'name4',
        group: 'NLP',
        value: 9000,
      },
    ],
    p50: [
      {
        name: 'name1',
        group: 'CS',
        value: 13413,
      },
      {
        name: 'name1',
        group: 'ACG',
        value: 5241,
      },
      {
        name: 'name1',
        group: 'CV',
        value: 4231,
      },
      {
        name: 'name1',
        group: 'NLP',
        value: 4632,
      },
      {
        name: 'name2',
        group: 'CS',
        value: 4524,
      },
      {
        name: 'name2',
        group: 'ACG',
        value: 2454,
      },
      {
        name: 'name2',
        group: 'CV',
        value: 56353,
      },
      {
        name: 'name2',
        group: 'NLP',
        value: 12351,
      },
      {
        name: 'name3',
        group: 'CS',
        value: 7645,
      },
      {
        name: 'name3',
        group: 'ACG',
        value: 7556,
      },
      {
        name: 'name3',
        group: 'CV',
        value: 7856,
      },
      {
        name: 'name3',
        group: 'NLP',
        value: 8675,
      },
      {
        name: 'name4',
        group: 'CS',
        value: 5746,
      },
      {
        name: 'name4',
        group: 'ACG',
        value: 4645,
      },
      {
        name: 'name4',
        group: 'CV',
        value: 46467,
      },
      {
        name: 'name4',
        group: 'NLP',
        value: 7454,
      },
    ],
    p99: [
      {
        name: 'name1',
        group: 'CS',
        value: 6565,
      },
      {
        name: 'name1',
        group: 'ACG',
        value: 7867,
      },
      {
        name: 'name1',
        group: 'CV',
        value: 8795,
      },
      {
        name: 'name1',
        group: 'NLP',
        value: 9765,
      },
      {
        name: 'name2',
        group: 'CS',
        value: 9874,
      },
      {
        name: 'name2',
        group: 'ACG',
        value: 6564,
      },
      {
        name: 'name2',
        group: 'CV',
        value: 8778,
      },
      {
        name: 'name2',
        group: 'NLP',
        value: 6678,
      },
      {
        name: 'name3',
        group: 'CS',
        value: 6785,
      },
      {
        name: 'name3',
        group: 'ACG',
        value: 9785,
      },
      {
        name: 'name3',
        group: 'CV',
        value: 9778,
      },
      {
        name: 'name3',
        group: 'NLP',
        value: 7872,
      },
      {
        name: 'name4',
        group: 'CS',
        value: 5578,
      },
      {
        name: 'name4',
        group: 'ACG',
        value: 9656,
      },
      {
        name: 'name4',
        group: 'CV',
        value: 63523,
      },
      {
        name: 'name4',
        group: 'NLP',
        value: 3434,
      },
    ],
  },
  //   小组任务堆积图
  groupTaskStack: {
    ipt: [
      {
        taskType: '运行任务数',
        value: 42,
        m_id: 'GPU1',
      },
      {
        taskType: '运行任务数',
        value: 24,
        m_id: 'GPU2',
      },
      {
        taskType: '运行任务数',
        value: 12,
        m_id: 'GPU3',
      },
      {
        taskType: '等待任务数',
        value: 12,
        m_id: 'GPU1',
      },
      {
        taskType: '等待任务数',
        value: 34,
        m_id: 'GPU2',
      },
      {
        taskType: '等待任务数',
        value: 46,
        m_id: 'GPU3',
      },
    ],
    cs: [
      {
        taskType: '运行任务数',
        value: 24,
        m_id: 'GPU1',
      },
      {
        taskType: '运行任务数',
        value: 12,
        m_id: 'GPU2',
      },
      {
        taskType: '运行任务数',
        value: 76,
        m_id: 'GPU3',
      },
      {
        taskType: '等待任务数',
        value: 34,
        m_id: 'GPU1',
      },
      {
        taskType: '等待任务数',
        value: 64,
        m_id: 'GPU2',
      },
      {
        taskType: '等待任务数',
        value: 12,
        m_id: 'GPU3',
      },
    ],
    cv: [
      {
        taskType: '运行任务数',
        value: 45,
        m_id: 'GPU1',
      },
      {
        taskType: '运行任务数',
        value: 45,
        m_id: 'GPU2',
      },
      {
        taskType: '运行任务数',
        value: 89,
        m_id: 'GPU3',
      },
      {
        taskType: '等待任务数',
        value: 57,
        m_id: 'GPU1',
      },
      {
        taskType: '等待任务数',
        value: 45,
        m_id: 'GPU2',
      },
      {
        taskType: '等待任务数',
        value: 23,
        m_id: 'GPU3',
      },
    ],
    nlp: [
      {
        taskType: '运行任务数',
        value: 32,
        m_id: 'GPU1',
      },
      {
        taskType: '运行任务数',
        value: 23,
        m_id: 'GPU2',
      },
      {
        taskType: '运行任务数',
        value: 79,
        m_id: 'GPU3',
      },
      {
        taskType: '等待任务数',
        value: 12,
        m_id: 'GPU1',
      },
      {
        taskType: '等待任务数',
        value: 53,
        m_id: 'GPU2',
      },
      {
        taskType: '等待任务数',
        value: 32,
        m_id: 'GPU3',
      },
    ],
    acg: [
      {
        taskType: '运行任务数',
        value: 24,
        m_id: 'GPU1',
      },
      {
        taskType: '运行任务数',
        value: 34,
        m_id: 'GPU2',
      },
      {
        taskType: '运行任务数',
        value: 34,
        m_id: 'GPU3',
      },
      {
        taskType: '等待任务数',
        value: 75,
        m_id: 'GPU1',
      },
      {
        taskType: '等待任务数',
        value: 84,
        m_id: 'GPU2',
      },
      {
        taskType: '等待任务数',
        value: 23,
        m_id: 'GPU3',
      },
    ],
  },
  // 用户
  groupUsers: {
    cs: {
      //用户GPU利用率
      userUsageRate: [
        { user: 'huidong', value: 38 },
        { user: 'xuhong', value: 52 },
        { user: 'zhishang', value: 61 },
        { user: 'allen', value: 32 },
        { user: 'dinyin', value: 48 },
      ],
      //用户GPU使用趋势
      userUsageTrend: [
        {
          time: '20240601',
          value: 20,
          category: 'huidong',
        },
        {
          time: '20240602',
          value: 42,
          category: 'huidong',
        },
        {
          time: '20240603',
          value: 63,
          category: 'huidong',
        },
        {
          time: '20240604',
          value: 74,
          category: 'huidong',
        },
        {
          time: '20240601',
          value: 45,
          category: 'xuhong',
        },
        {
          time: '20240602',
          value: 23,
          category: 'xuhong',
        },
        {
          time: '20240603',
          value: 62,
          category: 'xuhong',
        },
        {
          time: '20240604',
          value: 43,
          category: 'xuhong',
        },
        {
          time: '20240601',
          value: 62,
          category: 'zhishang',
        },
        {
          time: '20240602',
          value: 23,
          category: 'zhishang',
        },
        {
          time: '20240603',
          value: 63,
          category: 'zhishang',
        },
        {
          time: '20240604',
          value: 86,
          category: 'zhishang',
        },
        {
          time: '20240601',
          value: 31,
          category: 'allen',
        },
        {
          time: '20240602',
          value: 12,
          category: 'allen',
        },
        {
          time: '20240603',
          value: 74,
          category: 'allen',
        },
        {
          time: '20240604',
          value: 45,
          category: 'allen',
        },
        {
          time: '20240601',
          value: 12,
          category: 'dingyin',
        },
        {
          time: '20240602',
          value: 53,
          category: 'dingyin',
        },
        {
          time: '20240603',
          value: 32,
          category: 'dingyin',
        },
        {
          time: '20240604',
          value: 45,
          category: 'dingyin',
        },
      ],
      //用户GPU市场/费用
      userUsageTimeCost: [],
    },
    cv: {
      //用户GPU利用率
      userUsageRate: [
        { user: 'huidong', value: 13 },
        { user: 'xuhong', value: 24 },
        { user: 'zhishang', value: 21 },
        { user: 'allen', value: 42 },
        { user: 'dinyin', value: 64 },
      ],
      //用户GPU使用趋势
      userUsageTrend: [],
      //用户GPU市场/费用
      userUsageTimeCost: [],
    },
    ipt: {
      userUsageRate: [
        { user: 'huidong', value: 35 },
        { user: 'xuhong', value: 23 },
        { user: 'zhishang', value: 75 },
        { user: 'allen', value: 34 },
        { user: 'dinyin', value: 86 },
      ], //用户GPU利用率
      userUsageTrend: [], //用户GPU使用趋势
      userUsageTimeCost: [], //用户GPU市场/费用
    },
    acg: {
      userUsageRate: [
        { user: 'huidong', value: 12 },
        { user: 'xuhong', value: 42 },
        { user: 'zhishang', value: 64 },
        { user: 'allen', value: 23 },
        { user: 'dinyin', value: 64 },
      ], //用户GPU利用率
      userUsageTrend: [], //用户GPU使用趋势
      userUsageTimeCost: [], //用户GPU市场/费用
    },
    nlp: {
      userUsageRate: [
        { user: 'huidong', value: 24 },
        { user: 'xuhong', value: 86 },
        { user: 'zhishang', value: 62 },
        { user: 'allen', value: 54 },
        { user: 'dinyin', value: 44 },
      ], //用户GPU利用率
      userUsageTrend: [], //用户GPU使用趋势
      userUsageTimeCost: [], //用户GPU市场/费用
    },
  },
}
