<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-06-13 09:56:27
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-19 10:59:19
 * @FilePath: \cloud_web\src\views\ai\SlurmReport.vue
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <a-card>
      <a-radio-group v-model:value="GPUtype" button-style="solid">
        <a-radio-button value="A10">A10</a-radio-button>
        <a-radio-button value="A100">A100</a-radio-button>
        <a-radio-button value="A800">A800</a-radio-button>
        <a-radio-button value="H800">H800</a-radio-button>
        <a-radio-button value="new-H800">new-H800</a-radio-button>
      </a-radio-group>
      <a-range-picker
        style="margin-left: 18px"
        v-model:value="startEndTime"
        :disabled-date="disabledDate"
        :format="dateFormat"
        :ranges="ranges"
      />
      <a-button style="margin-left: 18px" type="primary" @click="queryData">查询</a-button>
    </a-card>
    <a-card style="margin-top: 16px" title="集群">
      <a-row>
        <a-col :span="12">
          <a-spin :spinning="spining">
            <a-card style="width: calc(100% - 16px); height: 420px; text-align: center" title="GPU资源使用率">
              <a-empty style="height: 100%" v-if="!useageRate.length" />
              <Pie
                v-if="useageRate.length"
                :filedName="['value', 'name']"
                :chartData="useageRate"
                :compRef="'useageRate'"
              />
            </a-card>
          </a-spin>
        </a-col>
        <a-col :span="12">
          <a-spin :spinning="spining">
            <a-card style="height: 420px" title="GPU资源使用率变化趋势">
              <a-empty style="height: 100%" v-if="!useageRateTrend.length" />
              <ColumnLine v-if="useageRateTrend.length" :chartData="useageRateTrend" :compRef="'useageRateTrend'" />
            </a-card>
          </a-spin>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-spin :spinning="spining">
            <a-card style="margin-top: 16px" title="任务堆积度">
              <a-empty style="height: 100%" v-if="!taskStackRate.length" />
              <ColumnStack
                v-if="taskStackRate.length"
                :filedName="['date', 'value', 'name']"
                :chartData="taskStackRate"
                :compRef="'taskStackRate'"
              />
            </a-card>
          </a-spin>
        </a-col>
      </a-row>
    </a-card>
    <a-card style="margin-top: 16px" title="小组">
      <a-row>
        <a-col :span="8">
          <a-spin :spinning="spining">
            <a-card style="width: calc(100% - 16px); height: 420px" title="小组GPU使用时长/费用">
              <a-empty style="height: 100%" v-if="!groupCosts.length" />
              <Pie
                v-if="groupCosts.length"
                :filedName="['rate', 'name']"
                :chartData="groupCosts"
                :compRef="'groupCosts'"
              />
            </a-card>
          </a-spin>
        </a-col>
        <a-col :span="16">
          <a-spin :spinning="spining">
            <a-card style="height: 420px" title="小组GPU使用时长变化趋势">
              <a-empty style="height: 100%" v-if="!groupTimeTrend.length" />
              <ColumnStack
                v-if="groupTimeTrend.length"
                :filedName="['date', 'value', 'name']"
                :chartData="groupTimeTrend"
                :compRef="'groupTimeTrend'"
              />
            </a-card>
          </a-spin>
        </a-col>
      </a-row>
      <a-row style="margin-top: 16px">
        <a-col :span="8">
          <a-spin :spinning="spining">
            <a-card style="width: calc(100% - 16px)" title="小组GPU利用率">
              <a-empty style="height: 100%" v-if="!groupUsageRate.length" />
              <Bar
                v-if="groupUsageRate.length"
                :filedName="['value', 'name']"
                :chartData="groupUsageRate"
                :compRef="'groupUsageRate'"
              />
            </a-card>
          </a-spin>
        </a-col>
        <a-col :span="16">
          <a-spin :spinning="spining">
            <a-card title="小组GPU利用率变化趋势">
              <a-empty style="height: 100%" v-if="!groupUsageTrend.length" />
              <LineChart
                v-if="groupUsageTrend.length"
                :filedName="['date', 'value', 'name']"
                :chartData="groupUsageTrend"
                :compRef="'groupUsageTrend'"
              />
            </a-card>
          </a-spin>
        </a-col>
      </a-row>
      <a-row style="margin-top: 16px">
        <a-col :span="8">
          <a-spin :spinning="spining">
            <a-card style="width: calc(100% - 16px)" title="小组GPU利用等待时长">
              <a-empty style="height: 100%" v-if="!groupWaitTime.length" />
              <Bar
                v-if="groupWaitTime.length"
                @groupTimeRadioChange="groupTimeRadioChange"
                :radioShow="true"
                :filedName="['value', 'name']"
                :chartData="groupWaitTime"
                :compRef="'groupWaitTime'"
              />
            </a-card>
          </a-spin>
        </a-col>
        <a-col :span="16">
          <a-spin :spinning="spining">
            <a-card title="小组等待时长变化趋势">
              <a-empty style="height: 100%" v-if="!groupWaitTimeTrend.length" />
              <Column
                v-if="groupWaitTimeTrend.length"
                @groupTimeRadioChange="groupTimeTrendRadioChange"
                :radioShow="true"
                :isGroup="true"
                :filedName="['date', 'value', 'name']"
                :chartData="groupWaitTimeTrend"
                :compRef="'groupWaitTimeTrend'"
              />
            </a-card>
          </a-spin>
        </a-col>
      </a-row>
      <a-row style="margin-top: 16px">
        <a-col :span="24">
          <a-spin :spinning="spining">
            <a-card title="小组任务堆积图">
              <ColumnStack
                v-if="!spining"
                :filedName="['date', 'value', 'name']"
                :radioShow="true"
                :chartData="groupTask"
                :compRef="'groupTask'"
                ref="groupTaskRef"
                @groupChange="groupChange"
              />
            </a-card>
          </a-spin>
        </a-col>
      </a-row>
    </a-card>
    <a-card style="margin-top: 16px" title="用户">
      <a-radio-group v-model:value="group" @change="usersChange" button-style="solid">
        <a-radio-button value="nlp">NLP</a-radio-button>
        <a-radio-button value="cv">CV</a-radio-button>
        <a-radio-button value="acg">ACG</a-radio-button>
        <a-radio-button value="cs">CS</a-radio-button>
        <a-radio-button value="ipt">IPT</a-radio-button>
      </a-radio-group>

      <a-row style="margin-top: 16px">
        <a-col :span="12">
          <a-card style="width: calc(100% - 16px)" title="用户GPU利用率">
            <a-empty style="height: 100%" v-if="!userUsageRate || !userUsageRate.length" />
            <Bar
              v-if="userUsageRate && userUsageRate.length"
              :filedName="['value', 'name']"
              :chartData="userUsageRate"
              ref="userUsageRateRef"
              :compRef="'userUsageRateRef'"
            />
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="用户GPU利用率趋势">
            <a-empty style="height: 100%" v-if="!userTimeCost || !userTimeCost.length" />
            <LineChart
              v-if="userTimeCost && userTimeCost.length"
              :filedName="['date', 'value', 'name']"
              :chartData="userTimeCost"
              :compRef="'userTimeCost'"
              ref="userTimeCostRef"
            />
          </a-card>
        </a-col>
      </a-row>
      <a-row style="margin-top: 16px">
        <a-col :span="24">
          <a-card title=" 用户GPU使用时长/费用">
            <a-empty style="height: 100%" v-if="!userUsageRateTrend || !userUsageRateTrend.length" />
            <Pie
              ref="userUsageRateTrendRef"
              v-if="userUsageRateTrend && userUsageRateTrend.length"
              :filedName="['rate', 'name']"
              :chartData="userUsageRateTrend"
              :compRef="'userUsageRateTrend'"
            />
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </page-header-wrapper>
</template>

<script setup>
import { slurmDashboard } from '@/api/ai/slurm.js'
import { ref, reactive } from 'vue'
import Pie from './components/pieChart.vue'
import ColumnLine from './components/columnLineChart.vue'
import ColumnStack from './components/stackColumnChart.vue'
import Bar from './components/barChart.vue'
import LineChart from './components/lineChart.vue'
import Column from './components/columnChart.vue'
import dayjs from 'dayjs'
import { mockData } from './mockData.js'
const dateFormat = 'YYYY-MM-DD '
const ranges = {
  最近一天: [dayjs().subtract(1, 'day'), dayjs(dayjs(), dateFormat)],
  最近一周: [dayjs().subtract(1, 'week'), dayjs(dayjs(), dateFormat)],
  最近一月: [dayjs().subtract(1, 'month'), dayjs(dayjs(), dateFormat)],
  上周: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week'), dateFormat],
  上月: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month'), dateFormat],
}
const startEndTime = ref([dayjs().subtract(1, 'month'), dayjs(dayjs())])
const disabledDate = current => {
  return current && current > dayjs().endOf('day')
}
const spining = ref(false)
const groupTaskRef = ref(null)
const userUsageRateRef = ref(null)
const userTimeCostRef = ref(null)
const userUsageRateTrendRef = ref(null)
const GPUtype = ref('new-H800')
const group = ref('nlp')
const allData = ref({})
const useageRate = ref([]) //GPU资源使用率
const useageRateTrend = ref([]) //GPU资源使用率变化趋势
const taskStackRate = ref([]) //GPU任务堆积度
const groupCosts = ref([]) //小组GPU使用时长/费用
const groupTimeTrend = ref([]) //小组GPU使用时长/费用
const groupUsageRate = ref([]) //小组GPU利用率
const groupUsageTrend = ref([]) //小组GPU利用率变化趋势
const groupWaitTime = ref([]) //小组GPU利用等待时长
const groupWaitTimeTrend = ref([]) //小组等待时长变化趋势
const groupTask = ref([]) //小组任务堆积图
const userUsageRate = ref([]) //用户GPU利用率
const userTimeCost = ref([]) //用户GPU使用时长/费用
const userUsageRateTrend = ref([]) //用户GPU利用率趋势
// 小组GPU利用等待时长事件
const groupTimeRadioChange = v => {
  allData.value.group.groupWaitTime[v]
    ? (groupWaitTime.value = allData.value.group.groupWaitTime[v])
    : (groupWaitTime.value = [])
}
// 小组等待时长变化趋势
const groupTimeTrendRadioChange = v => {
  allData.value.group.groupWaitTimeTrend[v]
    ? (groupWaitTimeTrend.value = allData.value.group.groupWaitTimeTrend[v])
    : (groupWaitTimeTrend.value = [])
}
// 小组任务堆积图
const groupChange = v => {
  allData.value.group.groupTaskStack[v]
    ? (groupTask.value = allData.value.group.groupTaskStack[v])
    : (groupTask.value = [])
  if (groupTask.value.length == 0) {
    if (groupTaskRef.value) {
      groupTaskRef.value.destoryChart()
    }
  }
  setTimeout(() => {
    if (groupTaskRef.value && groupTask.value.length) {
      groupTaskRef.value.rederData()
    }
  })
}
// 用户change
const usersChange = () => {
  allData.value.user[group.value].userUsageRate
    ? (userUsageRate.value = allData.value.user[group.value].userUsageRate)
    : (userUsageRate.value = [])
  allData.value.user[group.value].groupUsageTrend
    ? (userTimeCost.value = allData.value.user[group.value].groupUsageTrend)
    : (userTimeCost.value = [])
  allData.value.user[group.value].userCost
    ? (userUsageRateTrend.value = allData.value.user[group.value].userCost)
    : (userUsageRateTrend.value = [])
  if (userTimeCostRef.value) {
    userTimeCostRef.value.renderChart()
  }
  if (userUsageRateTrendRef.value) {
    userUsageRateTrendRef.value.renderChart()
  }
}
const queryData = () => {
  useageRate.value = []
  useageRateTrend.value = []
  taskStackRate.value = []
  groupCosts.value = []
  groupTimeTrend.value = []
  groupUsageRate.value = []
  groupUsageTrend.value = []
  groupWaitTime.value = []
  groupWaitTimeTrend.value = []
  userUsageRate.value = []
  userTimeCost.value = []
  group.value = 'nlp'
  spining.value = true
  if (groupTaskRef.value) {
    groupTaskRef.value.groupTask = 'nlp'
  }
  if (startEndTime.value.length > 1) {
    slurmDashboard({
      part: GPUtype.value,
      beginTime: startEndTime.value[0].format(dateFormat).trim(),
      endTime: startEndTime.value[1].format(dateFormat).trim(),
    })
      .then(res => {
        spining.value = false
        allData.value = res.Data
        console.log(allData.value, 'allData')
        allData.value.cluster.usageRate ? (useageRate.value = allData.value.cluster.usageRate) : (useageRate.value = [])
        allData.value.cluster.usageRateTrend
          ? (useageRateTrend.value = allData.value.cluster.usageRateTrend)
          : (useageRateTrend.value = [])
        allData.value.cluster.taskStackRate
          ? (taskStackRate.value = allData.value.cluster.taskStackRate)
          : (taskStackRate.value = [])
        allData.value.group.groupCost ? (groupCosts.value = allData.value.group.groupCost) : (groupCosts.value = [])
        allData.value.group.groupTimeTrend
          ? (groupTimeTrend.value = allData.value.group.groupTimeTrend)
          : (groupTimeTrend.value = [])
        allData.value.group.groupUsageRate
          ? (groupUsageRate.value = allData.value.group.groupUsageRate)
          : (groupUsageRate.value = [])
        allData.value.group.groupUsageTrend
          ? (groupUsageTrend.value = allData.value.group.groupUsageTrend)
          : (groupUsageTrend.value = [])
        allData.value.group.groupWaitTime['average']
          ? (groupWaitTime.value = allData.value.group.groupWaitTime['average'])
          : (groupWaitTime.value = [])
        allData.value.group.groupWaitTimeTrend['average']
          ? (groupWaitTimeTrend.value = allData.value.group.groupWaitTimeTrend['average'])
          : (groupWaitTimeTrend.value = [])
        allData.value.group.groupTaskStack['nlp']
          ? (groupTask.value = allData.value.group.groupTaskStack['nlp'])
          : (groupTask.value = [])
        allData.value.user[group.value].userUsageRate
          ? (userUsageRate.value = allData.value.user[group.value].userUsageRate)
          : (userUsageRate.value = [])
        allData.value.user[group.value].groupUsageTrend
          ? (userTimeCost.value = allData.value.user[group.value].groupUsageTrend)
          : (userTimeCost.value = [])
        allData.value.user[group.value].userCost
          ? (userUsageRateTrend.value = allData.value.user[group.value].userCost)
          : (userUsageRateTrend.value = [])
      })
      .catch(err => {
        console.log(err)
      })
  }
}
queryData()
</script>
