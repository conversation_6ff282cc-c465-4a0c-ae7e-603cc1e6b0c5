<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :bordered="false"
      :tabList="tabListNoTitle"
      :activeTabKey="noTitleKey"
      @tabChange="key => handleTabChange(key, 'noTitleKey')"
    >
      <SlurmPriceLeaseRental v-if="noTitleKey === 'leaseRental'"></SlurmPriceLeaseRental>
      <SlurmPriceGpu v-else-if="noTitleKey === 'slurmGpuPrice'"></SlurmPriceGpu>
      <SlurmSplitRatio v-else-if="noTitleKey === 'slurmSplitRatio'"></SlurmSplitRatio>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import SlurmPriceLeaseRental from './components/SlurmPriceLeaseRental.vue'
import SlurmPriceGpu from './components/SlurmPriceGpu.vue'
import SlurmSplitRatio from './components/SlurmSplitRatio.vue'
import { notification } from 'ant-design-vue'

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'TableList',
  components: {
    STable,
    SlurmPriceLeaseRental,
    SlurmPriceGpu,
    SlurmSplitRatio
  },
  data () {
    return {
      hasAdminRole: false,
      tabListNoTitle: [
        {
          key: 'leaseRental',
          tab: '物理机月租赁费'
        },
        {
          key: 'slurmGpuPrice',
          tab: 'Slurm GPU卡计费标准'
        },
        {
          key: 'slurmSplitRatio',
          tab: 'Slurm分摊比例'
        }
      ],
      noTitleKey: 'leaseRental',
      handleTabChange (key, type) {
        this[type] = key
      },
      mysqlInfo: {}
    }
  },
  created () {
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('ai_admin') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.parameterTable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
