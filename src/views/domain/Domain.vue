<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.domianName" placeholder="域名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="购买来源">
                <a-select
                  v-model:value="queryParam.source"
                  placeholder="请选择域名来源"
                  :options="domainSourceList"
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button v-if="userpRression" type="primary" icon="plus" @click="handleAdd">新增</tx-button>
        <a-modal v-model:visible="addVisible" title="新增域名">
          <template #footer>
            <tx-button key="back" @click="cancelAddDomain">取消</tx-button>
            <tx-button key="submit" type="primary" @click="submitAddDomain">确定</tx-button>
          </template>
          <a-form layout="left" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" class="myform">
            <a-form-model-item label="域名">
              <a-input v-model:value="addQueryParam.domainName" placeholder="" style="width: 220px" />
            </a-form-model-item>
            <a-form-item label="来源">
              <a-select
                v-model:value="addQueryParam.source"
                placeholder="请选择域名来源"
                :options="domainSourceList"
                style="width: 150px"
              ></a-select>
            </a-form-item>
          </a-form>
        </a-modal>
        <tx-button v-if="userpRression" type="primary" icon="download" @click="handleDownload">导出</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        bordered
        rowKey="id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'domainStatus'">
            <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
          </template>
          <template v-else-if="column.dataIndex == 'action'">
            <tx-button v-if="userpRression" type="link" style="margin-left: 8px" @click="handleEdit(record)">
              变更
            </tx-button>
            <a-divider type="vertical" />
            <tx-button v-if="userpRression" :disabled="record.notifyLabel===0" type="link" style="margin-left: 8px" @click="notifyOff(record)">
              取消通知
            </tx-button>
            <!-- <div v-if="record.source == 'aliyun-cci'域名购买-申请内容">
              <a @click="handleInfo(record)">下载</a>
            </div> -->
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="购买来源">{{ record.source }}</a-descriptions-item>
            <a-descriptions-item label="过期状态">
              <a-badge
                :status="expiretMapTypeFilter(record.expirationDateStatus)"
                :text="expiretMapFilter(record.expirationDateStatus)"
              />
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="域名类型">{{ record.domainType }}</a-descriptions-item>
            <a-descriptions-item label="实名认证状态">
              <a-badge
                :status="auditMapTypeFilter(record.domainAuditStatus)"
                :text="auditMapFilter(record.domainAuditStatus)"
              />
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
            <a-descriptions-item label="注册类型">
              <a-badge
                :status="registMapTypeFilter(record.registrantType)"
                :text="registMapFilter(record.registrantType)"
              />
            </a-descriptions-item>
            <a-descriptions-item label="域名归属">{{ record.secondOrganization }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
      <a-modal :visible="downloadVisible" width="350px" :closable="false" title="域名证书下载">
        <a-form layout="inline">
          <a-form-model-item>
            <a-row>
              <a-col :span="18">
                <a-input v-model:value="verificationCode" placeholder="请输入验证码" style="width: 180px" />
              </a-col>
              <a-col :span="6">
                <tx-button @click="handleInfo('repeated')">重新发送</tx-button>
              </a-col>
            </a-row>
          </a-form-model-item>
        </a-form>
        <template #footer>
          <a-row>
            <a-col :span="10">
              <tx-button type="primary" @click="submitCheck">确定</tx-button>
            </a-col>
            <a-col :span="10">
              <tx-button @click="cancelCheck">取消</tx-button>
            </a-col>
          </a-row>
        </template>
      </a-modal>
      <a-modal v-model:visible="editVisible" title="域名解析来源选择">
        <template #footer>
          <tx-button key="back" @click="cancelUpdateDomain">取消</tx-button>
          <tx-button key="submit" type="primary" @click="submitUpdateDomain">确定</tx-button>
        </template>
        <a-form layout="left" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" class="myform">
          <a-form-model-item label="域名">
            <a-input v-model:value="updateQueryParam.domainName" :disabled="true" placeholder="" style="width: 220px" />
          </a-form-model-item>
          <a-form-model-item label="负责人">
            <a-input v-model:value="updateQueryParam.domainUser" placeholder="" style="width: 220px" />
          </a-form-model-item>
          <a-form-item label="域名解析来源">
            <a-select
              v-model:value="updateQueryParam.dnsSource"
              style="width: 220px"
              placeholder="请选择域名解析来源"
              :options="domainDnsSourceList"
            ></a-select>
          </a-form-item>
          <a-form-item label="域名解析归属">
            <a-select v-model:value="updateQueryParam.secondOrganization" mode="multiple" show-search style="width: 220px">
            <a-select-option v-for="item in domainSecondOriginationList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { STable, Ellipsis } from '@/components'
import { getDomainList, getDomainListSource, creatDomain, UpdateDomain,notifyOff } from '@/api/domain/domain'
import { getUserList } from '@/api/permission/user'
import { notification } from 'ant-design-vue'
import store from '@/store'
const columns = [
  {
    title: '域名',
    dataIndex: 'domainName',
    sorter: true,
  },
  {
    title: '备案号',
    dataIndex: 'icpWebNo',
    sorter: true,
  },
  {
    title: '解析来源',
    dataIndex: 'dnsSource',
  },
  {
    title: '事业部',
    dataIndex: 'secondOrganization',
  },
  {
    title: '注册日期',
    dataIndex: 'registrationDate',
    sorter: true,
  },
  {
    title: '到期日期',
    dataIndex: 'expirationDate',
    sorter: true,
  },
  {
    title: '负责人',
    dataIndex: 'domainUser',
  },
  {
    title: '过期倒计时',
    dataIndex: 'expirationCurrDateDiff',
    sorter: true,
    customRender: ({ text }) => text + '天',
  },
  {
    title: '域名状态',
    dataIndex: 'domainStatus',
    scopedSlots: { customRender: 'domainStatus' },
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '170px',
  },
]

const statusMap = {
  2: {
    status: 'processing',
    text: '急需续费',
  },
  3: {
    status: 'success',
    text: '正常',
  },
  1: {
    status: 'error',
    text: '急需赎回',
  },
  6: {
    status: 'warning',
    text: '未知',
  },
}
const auditMap = {
  SUCCEED: {
    status: 'success',
    text: '审核成功',
  },
  NONAUDIT: {
    status: 'error',
    text: '未审核',
  },
}
const expiretMap = {
  1: {
    status: 'success',
    text: '未过期',
  },
  2: {
    status: 'error',
    text: '已过期',
  },
}
const registMap = {
  1: {
    status: 'default',
    text: '企业',
  },
  2: {
    status: 'default',
    text: '个人',
  },
}
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      domainSourceList: [],
      domainDnsSourceList: [
        { label: 'aws', value: 'aws' },
        { label: 'aliyun-qxb', value: 'aliyun-qxb' },
        { label: 'aliyun-cci', value: 'aliyun-cci' },
      ],
      domainSecondOriginationList: ['AIM', 'CS', 'CS-CC', 'DG', 'SSG', 'ACG智能创新', '总裁办'],
      domainInfo: [],
      userRoles: [],
      userRolesWhite: 'domain_admin',
      addQueryParam: {},
      updateQueryParam: {},
      userpRression: false,
      visible: false,
      downloadVisible: false,
      downloadCertName: '',
      addVisible: false,
      editVisible: false,
      confirmLoading: false,
      verificationCode: '',
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        return getDomainList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {
    getDomainListSource().then(res => {
      this.domainSourceList = res.Data.SourceList
    })
    loadXLSX()
  },
  mounted() {
    this.getUserRoles(this.localUser.split('@')[0])
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    auditMapFilter(type) {
      return auditMap[type]?.text || type
    },
    auditMapTypeFilter(type) {
      return auditMap[type]?.status || type
    },
    expiretMapFilter(type) {
      return expiretMap[type]?.text || type
    },
    expiretMapTypeFilter(type) {
      return expiretMap[type]?.status || type
    },
    registMapFilter(type) {
      return registMap[type]?.text || type
    },
    registMapTypeFilter(type) {
      return registMap[type]?.status || type
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    handleAdd() {
      this.addVisible = true
    },
    cancelAddDomain() {
      this.addVisible = false
      this.addQueryParam = {}
    },
    submitAddDomain() {
      creatDomain(this.addQueryParam).then(res => {
        if (res.Data.message === 'ok') {
          notification.success({
            message: '创建成功',
            description: '域名创建成功',
          })
        } else {
          notification.error({
            message: '创建失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        }
      })
      this.cancelAddDomain()
    },
    handleEdit(record) {
      this.editVisible = true
      this.updateQueryParam.domainName = record.domainName
      this.updateQueryParam.id = record.id
      this.updateQueryParam.domainUser=record.domainUser
      this.updateQueryParam.dnsSource = record.dnsSource
      if(record.secondOrganization.length!==0){
        this.updateQueryParam.secondOrganization = JSON.parse(record.secondOrganization)
      }else{
        this.updateQueryParam.secondOrganization=[]
      }
    },
    cancelUpdateDomain() {
      this.editVisible = false
      this.updateQueryParam = {}
    },
    notifyOff(record){
      notifyOff({'domainName':record.domainName}).then(res =>{
        if (res.Data.message === 'done') {
          notification.success({
              message: '取消成功'
            })
          this.$refs.table.refresh(true)
        } else {
          notification.error({
            message: '取消失败',
          })
        }
      })
    },
    submitUpdateDomain() {
      delete this.updateQueryParam.domainName
      UpdateDomain(this.updateQueryParam).then(res => {
        if (res.Data.message === 'ok') {
          notification.success({
              message: '变更成功'
            })
          this.$refs.table.refresh(true)
        } else {
          notification.error({
            message: '变更失败',
           })  
        }
      })
      this.cancelUpdateDomain()
    },
    handleInfo(record) {
      this.$message.info('域名证书下载功能待开发！')
      // if (record !== 'repeated') {
      //     this.downloadCertName = record.certName
      // }
      // if (this.downloadCertName !== '' && this.downloadCertName !== undefined) {
      //   this.downloadVisible = true
      //   var sendData = {}
      //   sendData.email = store.getters.email
      //   sslDownloadSend(sendData).then((res) => {
      //     if (res.Data.message === 'ok') {
      //       notification.success({
      //             message: '已发送验证码'
      //           })
      //     } else if (res.Data.message === 'exist') {
      //       notification.warning({
      //             message: '验证码未过期'
      //           })
      //     }
      //   })
      // } else {
      //   notification.warning({
      //       message: '该域名未绑定证书！'
      //     })
      // }
    },
    cancelCheck() {
      this.downloadVisible = false
      this.verificationCode = ''
      this.this.downloadCertName = ''
    },
    submitCheck() {
      // 发送前端信息到后端  进行校验6位数字
      // var checkData = {}
      // checkData.email = store.getters.email
      // checkData.code = this.verificationCode
      // sslDownloadCheck(checkData).then((res) => {
      //   if (res.Data.message === 'ok') {
      //     // 调取域名证书的下载pem文件
      //      var certData = {}
      //     certData.certName = this.downloadCertName
      //     sslDownload(certData).then((res) => {
      //       // 增加判断
      //       var content = res.Data.certPublicContent + res.Data.certPrivateContent
      //       this.pemFilesOUT(content, this.downloadCertName)
      //       this.cancelCheck()
      //     })
      //   } else if (res.Data.message === 'error') {
      //     notification.error({
      //         message: '输入验证码错误'
      //       })
      //   } else if (res.Data.message === 'notExist') {
      //     notification.warning({
      //         message: '验证码已过期，请重新发送'
      //       })
      //   }
      // })
    },
    handleDownload() {
      const downloadColumns = [
        {
          title: 'ID',
          dataIndex: 'id',
        },
        {
          title: '域名',
          dataIndex: 'domainName',
        },
        {
          title: '备案号',
          dataIndex: 'icpWebNo',
        },
        {
          title: '注册日期',
          dataIndex: 'registrationDate',
        },
        {
          title: '到期日期',
          dataIndex: 'expirationDate',
        },
        {
          title: '过期倒计时',
          dataIndex: 'expirationCurrDateDiff',
        },
        {
          title: '过期时间状态',
          dataIndex: 'expirationDateStatus',
        },
        {
          title: '域名状态',
          dataIndex: 'domainStatus',
        },
        {
          title: '域名类型',
          dataIndex: 'domainType',
        },
        {
          title: '注册类型',
          dataIndex: 'registrantType',
        },
        {
          title: '域名审计状态',
          dataIndex: 'domainAuditStatus',
        },
        {
          title: '来源',
          dataIndex: 'source',
        },
        {
          title: '域名状态',
          dataIndex: 'domainStatus',
        },
      ]
      getDomainList(this.downloadqueryParam).then(async res => {
        if (res.Data.hasOwnProperty('data')) {
          const XLSX = await loadXLSX()
          const tableData = this.transData(downloadColumns, res.Data.data)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'domain')
          XLSX.writeFile(wb, 'Domain.xlsx')
        } else {
          this.downloadtableList = []
        }
      })
    },
    transData(columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map(item => {
        return obj.keys.map(key => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    pemFilesOUT(content, filename) {
      // const content = 'adafdafadf'
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      if ('download' in document.createElement('a')) {
        const elink = document.createElement('a')
        elink.download = filename + '.pem'
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href)
        document.body.removeChild(elink)
        // Message.success('导出成功')
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
  },
}
</script>
<style lang="less" scoped>
.myform {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}
</style>
