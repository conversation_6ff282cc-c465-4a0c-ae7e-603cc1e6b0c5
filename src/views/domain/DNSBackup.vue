<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input @keyup.enter="$refs.table.refresh(true)" v-model:value="queryParam.searchText" placeholder="域名/waf/资源记录/内部ip端" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="精确查询">
                <a-select
                  placeholder="域名精确查询"
                  v-model:value="queryParam.name"
                  :showSearch="true"
                  :allowClear="true"
                  @search="handleSearch"
                >
                  <a-select-option v-for="item1 in domainList" :key="item1.value" :value="item1.value">
                    {{ item1.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="备份时间">
                <a-date-picker v-model:value="queryParam.backupTime" valueFormat="YYYY-MM-DD"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="4" :sm="24">
                <a-form-item label="类型">
                  <a-select v-model:value="queryParam.type" placeholder="请选择域名类型" :options="dnsTypeList" :allowClear="true"></a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 4) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
        :scroll="scrolls"
      >
        <template #bodyCell="{column, record, text}">
          <template v-if="column.dataIndex == 'scope'">
            <a-badge :status="scopeTypeFilter(text)" :text="scopeFilter(text)" />
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions >
            <a-descriptions-item label="地理位置">{{ record.geolocation }}</a-descriptions-item>
            <a-descriptions-item label="TTL">{{ record.ttl }}</a-descriptions-item>
            <a-descriptions-item label="内网Ip端口">{{ record.backendIps }}</a-descriptions-item>
            <a-descriptions-item label="负责人">{{ record.principalName }}</a-descriptions-item>
            <a-descriptions-item label="访问策略">{{ record.accessStrategy }}</a-descriptions-item>
            <a-descriptions-item label="事业部">{{ record.org }}</a-descriptions-item>
            <a-descriptions-item label="唯一标识">{{ record.dnsKey }}</a-descriptions-item>
            <a-descriptions-item label="开放范围">{{ record.scope }}</a-descriptions-item>
            <a-descriptions-item label="部门">{{ record.dep }}</a-descriptions-item>
            <a-descriptions-item label="来源">{{ record.source }}</a-descriptions-item>
            <a-descriptions-item label="第三方供应商服务器">
              <a-badge :status="statusTypeFilter(record.thirdServe)" :text="statusFilter(record.thirdServe)" />
            </a-descriptions-item>
            <a-descriptions-item label="项目标签">{{ record.project }}</a-descriptions-item>
            <a-descriptions-item label="根域">{{ record.hostedZone }}</a-descriptions-item>
            <a-descriptions-item label="waf源站">{{ record.waf }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { getDnsBackupList, getDnsDomainList, getDnsSourceList, getDnsTypeList } from '@/api/domain/dns'

import store from '@/store'

const columns = [
  {
    title: '域名',
    dataIndex: 'name',
    sorter: true,
    width: '300px'
  },
  {
    title: '地理位置',
    dataIndex: 'geolocation',
    sorter: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    sorter: true
  },
  {
    title: '资源记录',
    dataIndex: 'resourceRecords',
    sorter: true
  },
  {
    title: '备份时间',
    dataIndex: 'backupTime',
    sorter: true
  }
]
const statusMap = {
  intsig: {
    status: 'yellow',
    text: '否'
  },
  third: {
    status: 'blue',
    text: '是'
  }
}
const scopeMap = {
  0: {
    status: 'orange',
    text: '未知'
  },
  1: {
    status: 'success',
    text: '外网域名'
  },
  2: {
    status: 'processing',
    text: '内网域名'
  },
  3: {
    status: 'processing',
    text: '内网域名'
  },
  4: {
    status: 'processing',
    text: '内网域名'
  }
}
const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      labelVis: false,
      temp: {},
      OrgList: [],
      DepList: [],
      scrolls: {},
      principalList: [],
      principalNameToEmail: {},
      principalListMini: [],
      dnsTypeList: [],
      domainList: [],
      domainListMini: [],
      scrollPage: 1,
      valueData: '',
      treePageSize: 50,
      dnsSourceList: [],
      domainInfo: [],
      addQueryParam: { },
      visible: false,
      confirmLoading: false,
      userpRression: false,
      userRoles: [],
      userRolesWhite: 'domain_admin',
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        return getDnsBackupList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
            // 在此处对资源记录 这一条进行修改
            for (let i = 0; i < res.Data.data.length; i++) {
              res.Data.data[i].resourceRecords = this.listFilter(res.Data.data[i].resourceRecords)

              if (res.Data.data[i].scope === 0) {
                res.Data.data[i].scope = 0
              }
              if (res.Data.data[i].thirdServe === '') {
                res.Data.data[i].thirdServe = 'intsig'
              }
              if (res.Data.data[i].domainStatus === 0) {
                res.Data.data[i].domainStatus = 0
              }
            }
            this.scrolls = {
              x: this.columns.length * 200
            }
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    getDnsTypeList().then((res) => {
      this.dnsTypeList = res.Data.TypeList
    })
    getDnsSourceList().then((res) => {
      this.dnsSourceList = res.Data.SourceList
    })
    loadXLSX()
  },
  mounted() {
    this.getUserRoles(this.localUser.split('@')[0])
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {

    handleSearch(searchText) {
      this.domainList = []
      if (searchText !== '') {
        getDnsDomainList({ searchText: searchText }).then((response) => {
          this.domainList = response.Data.DomainList
        })
      }
    },
    listFilter(value) {
      if (!value || value === 'null') return ''
      let value1
      value1 = JSON.parse(value)
      let res = ''
      for (let j = 0; j < value1.length; j++) {
        if (j === 0) {
          res = value1[j].Value
        } else {
          res = res + '|' + value1[j].Value
        }
      }
      return res
    },
    scopeFilter(type) {
      return scopeMap[type]?.text || type
    },
    scopeTypeFilter(type) {
      return scopeMap[type]?.status || type
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      console.log(type)
      return statusMap[type]?.status || type
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    }
  }
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
}
</style>
