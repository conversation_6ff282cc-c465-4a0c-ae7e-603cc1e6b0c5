<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input @keyup.enter="$refs.table.refresh(true)" v-model:value.trim="queryParam.searchText" placeholder="域名/waf/资源记录/内部ip端" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="精确查询">
                <!-- <a-select
                  v-model:value="queryParam.name"
                  placeholder="域名精确查询"
                  :filterOption="false"
                  mode="multiple"
                  :options="domainListMini"
                  @popupScroll="handlePopupScroll"
                  @search="handleSearch">
                </a-select> -->
                <a-select
                  placeholder="域名精确查询"
                  v-model:value="queryParam.name"
                  :showSearch="true"
                  :allowClear="true"
                  @search="handleSearch"
                >
                  <a-select-option v-for="item1 in domainList" :key="item1.value" :value="item1.value">
                    {{ item1.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="类型">
                  <a-select v-model:value="queryParam.type" placeholder="请选择域名类型" :options="dnsTypeList" :allowClear="true"></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="来源">
                  <a-select v-model:value="queryParam.source" placeholder="请选择域名来源" :options="dnsSourceList" :allowClear="true"></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="域名状态">
                  <a-select v-model:value="queryParam.domainStatus" placeholder="请选择" >
                    <a-select-option :value="1">公司使用</a-select-option>
                    <a-select-option :value="2">外包使用</a-select-option>
                    <a-select-option :value="3">已下线</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="域名范围">
                  <a-select v-model:value="queryParam.domainScope" placeholder="请选择" >
                    <a-select-option :value="2">内网域名</a-select-option>
                    <a-select-option :value="1">外网域名</a-select-option>
                    <a-select-option :value="3">未知</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="续约">
                  <a-select v-model:value="queryParam.renew" placeholder="请选择" >
                    <a-select-option :value="true">是</a-select-option>
                    <a-select-option :value="false">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
        <tx-button type="primary" icon="download" @click="handleDownload">导出</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
        :scroll="scrolls"
        @expand="dnsHandleExpand"
      >
        <template #bodyCell="{column, record, text}">
          <template v-if="column.dataIndex == 'scope'">
            <a-badge :status="scopeTypeFilter(text)" :text="scopeFilter(text)" />
          </template>
          <template v-else-if="column.dataIndex == 'action'">
            <a-dropdown v-if="record.domainStatus!=0 && !record.comment?.includes('该域名解析为泛域名解析:')">
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="record.domainStatus!==0 "><a @click="handleEdit(record)">域名变更</a></a-menu-item>
                  <a-menu-item v-if="userpRression || record.principalEmails.includes(localUser) || record.costUser.includes(localUser) || localUser.includes('<EMAIL>')"><a @click="labelEdit(record)">标签变更</a></a-menu-item>
                  <a-menu-item v-if="record.domainStatus!==0"><a @click="handleDel(record)">域名注销</a></a-menu-item>
                  <a-menu-item v-if="record.domainStatus!==0 && record.needRenew && (userpRression || record.principalEmails.includes(localUser))"><a @click="handleRenew(record)">到期续签</a></a-menu-item>
                  <a-menu-item v-if="userpRression && record.domainStatus!==0"><a @click="handleCancelMonitor(record)">取消监控</a></a-menu-item>
                </a-menu>
              </template>
              <a>
                变更
                <a-icon type="down" />
              </a>
            </a-dropdown>
            <div v-else-if="record.domainStatus===0">已注销</div>
            <div v-else>泛域名解析禁止操作</div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions >
            <a-descriptions-item label="TTL">{{ record.ttl }}</a-descriptions-item>
            <a-descriptions-item label="内网Ip端口">{{ record.backendIps }}</a-descriptions-item>
            <a-descriptions-item label="负责人" v-if="record.principalName !==''">{{ record.principalName }}</a-descriptions-item>
            <a-descriptions-item label="负责人" v-else>{{ record.principalEmails }}</a-descriptions-item>
            <a-descriptions-item label="费用负责人" >{{ record.costUser }}</a-descriptions-item>
            <a-descriptions-item label="访问策略">{{ record.accessStrategy }}</a-descriptions-item>
            <a-descriptions-item label="事业部">{{ record.org }}</a-descriptions-item>
            <a-descriptions-item label="唯一标识">{{ record.dnsKey }}</a-descriptions-item>
            <a-descriptions-item label="开放范围">
              <a-badge :status="scopeTypeFilter(record.scope )" :text="scopeFilter(record.scope)" />
            </a-descriptions-item>
            <a-descriptions-item label="部门">{{ record.dep }}</a-descriptions-item>
            <a-descriptions-item label="第三方供应商服务器">
              <a-badge :status="statusTypeFilter(record.thirdServe)" :text="statusFilter(record.thirdServe)" />
            </a-descriptions-item>
            <a-descriptions-item label="项目标签">{{ record.project }}</a-descriptions-item>
            <a-descriptions-item label="根域">{{ record.hostedZone }}</a-descriptions-item>
            <a-descriptions-item label="waf源站">{{ record.waf }}</a-descriptions-item>
            <a-descriptions-item label="CDN源站">{{ record.cdnOrigin }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
            <a-descriptions-item label="过期时间">{{ record.expiryDate }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
          <a-descriptions >
            <a-descriptions-item label="业务IP 端口">
              <a-tooltip>
                <template #title>数据来源于访问日志，仅供参考</template>
                <a> {{ record.upAddr }}</a>
              </a-tooltip> </a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
      <!-- <create-form
        ref="createModal"
        :visible="visible"
        :loading="confirmLoading"
        :model="mdl"
        @cancel="handleCancel"
        @ok="handleOk"
      /> -->
      <!-- <step-by-step-modal ref="modal" @ok="handleOk" /> -->
      <a-modal v-model:visible="labelVis" title="标签信息变更" style="width: 700px">
        <template #footer>
          <tx-button key="back" @click="cancelLabel">取消</tx-button>
          <tx-button key="submit" type="primary" @click="submitEditLabel">确定</tx-button>
        </template>
        <a-form-model
          layout="horizontal"
          ref="updateForm"
          :model="temp"
          :rules="Rules"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-form-model-item label="域名" class="my-ant-form-item" name="icpWebNo">
            <a-input v-model:value="temp.name" disabled/>
          </a-form-model-item>
          <a-form-model-item label="负责人" name="principalEmails">
            <a-select
              placeholder="请输入邮箱选择"
              v-model:value="temp.principalEmails"
              style="width: 100%"
              :showSearch="true"
              mode="tags"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                {{ item1 }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="访问策略" class="my-ant-form-item">
            <a-select v-model:value="temp.accessStrategy" style="width: 200px" placeholder="请选择访问策略">
              <a-select-option value="WAF">WAF</a-select-option>
              <a-select-option value="IP白名单">IP白名单</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="费用负责人" name="costUser">
            <a-select
              placeholder="请输入邮箱选择"
              v-model:value="temp.costUser"
              style="width: 90%"
              :showSearch="true"
              @search="searchUserEmailMethod"
              @change="costUserChange"
            >
              <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                {{ item1 }}
              </a-select-option>
            </a-select>
            <a-tooltip>
              <template #title>费用负责人与事业部、部门相关联</template>
              <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
            </a-tooltip>
          </a-form-model-item>
          <a-form-model-item label="事业部/部门" name="organization">
            <a-cascader style="width: 200px" v-model:value="temp.organization" :options="organizationOptions" change-on-select />
          </a-form-model-item>
          <a-form-model-item label="应用标签" class="my-ant-form-item" >
            <a-input v-model:value="temp.application" />
          </a-form-model-item>
          <a-form-model-item label="项目标签" class="my-ant-form-item" name="picName">
            <a-input v-model:value="temp.project"/>
          </a-form-model-item>
          <a-form-model-item label="备注" class="my-ant-form-item" name="organizerName">
            <a-input v-model:value="temp.comment" />
          </a-form-model-item>
          <a-form-model-item label="第三方供应商服务器" class="my-ant-form-item" name="source">
            <a-select v-model:value="temp.thirdServe" style="width: 200px" placeholder="请选择状态">
              <a-select-option value="intsig">否</a-select-option>
              <a-select-option value="third">是</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal v-model:visible="RenewVis" title="域名到期续签" style="width: 700px">
        <template #footer>
          <tx-button key="back" @click="canceRenew">取消</tx-button>
          <tx-button key="submit" type="primary" @click="submitRenew">确定</tx-button>
        </template>
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-form-model-item label="域名" class="my-ant-form-item" name="name">
            <a-input v-model:value="renewForm.name" disabled/>
          </a-form-model-item>
          <a-form-model-item label="当前到期时间" class="my-ant-form-item" name="expiryDate">
            <a-input v-model:value="renewForm.expiryDate" disabled/>
          </a-form-model-item>
          <a-form-model-item label="续签有效期" class="my-ant-form-item" name="newExpiryDate">
            <a-radio-group v-model:value="renewForm.newExpiryDate" button-style="solid">
              <a-radio-button :value="31536000">一年</a-radio-button>
              <a-radio-button :value="63072000">二年</a-radio-button>
              <a-radio-button :value="94608000">三年</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { cancelDnsMonitor, getDnsDomainList, getDnsList, getDnsSourceList, getDnsTypeList, getDnsWafGateway, updateDnsLabel, updateDnsRenew } from '@/api/domain/dns'
import { GetUserOrgAllInfo } from '@/api/asset'
import { getUserList } from '@/api/permission/user'
import { getAssetUserList } from '@/api/cmdb/asset/user'
import { notification } from 'ant-design-vue'
import store from '@/store'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: '80px'
  },
  {
    title: '域名',
    dataIndex: 'name',
    sorter: true,
    width: '300px'
  },
  {
    title: '解析源',
    dataIndex: 'source',
    width: '110px'
  },
  {
    title: '地理位置',
    dataIndex: 'geolocation',
  },
  {
    title: '类型',
    dataIndex: 'type',
    sorter: true
  },
  // {
  //   title: '域名范围',
  //   width: '110px',
  //   dataIndex: 'scope',
  //   scopedSlots: { customRender: 'scope' },
  //   sorter: true
  // },
  {
    title: '资源记录',
    dataIndex: 'resourceRecords',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '90px',
    scopedSlots: { customRender: 'action' },
    align: 'center'
  }
]
const statusMap = {
  intsig: {
    status: 'yellow',
    text: '否'
  },
  third: {
    status: 'blue',
    text: '是'
  }
}
const scopeMap = {
  0: {
    status: 'orange',
    text: '未知'
  },
  1: {
    status: 'success',
    text: '外网域名'
  },
  2: {
    status: 'processing',
    text: '内网域名'
  },
  3: {
    status: 'processing',
    text: '内网域名'
  },
  4: {
    status: 'processing',
    text: '内网域名'
  }
}
const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      labelVis: false,
      temp: {},
      renewForm: {},
      organizationOptions: [],
      OrgList: [],
      DepList: [],
      scrolls: {},
      principalList: [],
      principalNameToEmail: {},
      principalListMini: [],
      dnsTypeList: [],
      domainList: [],
      domainListMini: [],
      scrollPage: 1,
      valueData: '',
      treePageSize: 50,
      dnsSourceList: [],
      domainInfo: [],
      userEmailList: [],
      addQueryParam: { },
      visible: false,
      RenewVis: false,
      confirmLoading: false,
      userpRression: false,
      userRoles: [],
      userRolesWhite: 'domain_admin',
      Rules: antdFormRulesFormat({
        principalEmails: [{ required: true, message: '请选择负责人', trigger: 'change' }],
        costUser: [{ required: true, message: '请选择费用负责人', trigger: 'change' }],
      }),
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        return getDnsList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
            // 在此处对资源记录 这一条进行修改
            for (let i = 0; i < res.Data.data.length; i++) {
              res.Data.data[i].resourceRecords = this.listFilter(res.Data.data[i].resourceRecords)
              if (res.Data.data[i].scope === 0) {
                res.Data.data[i].scope = 0
              }
              if (res.Data.data[i].thirdServe === '') {
                res.Data.data[i].thirdServe = 'intsig'
              }
              if (res.Data.data[i].domainStatus === 0) {
                res.Data.data[i].domainStatus = 0
              }
            }
            this.scrolls = {
              x: this.columns.length * 200
            }
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    getDnsTypeList().then((res) => {
      this.dnsTypeList = res.Data.TypeList
    })
    getDnsSourceList().then((res) => {
      this.dnsSourceList = res.Data.SourceList
    })
    loadXLSX()
    if (this.$route.query.options) {
      this.queryParam.renew = true
    }
  },
  mounted() {
    this.getUserRoles(this.localUser.split('@')[0])
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    handleInfo(record) {
      console.log(record)
    },
    handleDownload() {
      const downloadColumns = [
        {
          title: 'ID',
          dataIndex: 'id'
        },
        {
          title: '域名',
          dataIndex: 'name'
        },
        {
          title: '解析源',
          dataIndex: 'source'
        },
        {
          title: '类型',
          dataIndex: 'type'
        },
        {
          title: '资源记录',
          dataIndex: 'resourceRecords'
        },
        {
          title: '地理位置',
          dataIndex: 'geolocation'
        },
        {
          title: 'TTL',
          dataIndex: 'ttl'
        },
        {
          title: '内网Ip端口',
          dataIndex: 'backendIps'
        },
        {
          title: '负责人',
          dataIndex: 'principalName'
        },
        {
          title: '负责人邮箱',
          dataIndex: 'principalEmails'
        },
        {
          title: '访问策略',
          dataIndex: 'accessStrategy'
        },
        {
          title: '事业部',
          dataIndex: 'org'
        },
        {
          title: '部门',
          dataIndex: 'dep'
        },
        {
          title: '开放范围',
          dataIndex: 'scope'
        },
        {
          title: '来源',
          dataIndex: 'source'
        },
        {
          title: '状态类型',
          dataIndex: 'status'
        },
        {
          title: '项目标签',
          dataIndex: 'project'
        },
        {
          title: '应用标签',
          dataIndex: 'application'
        },
        {
          title: '根域',
          dataIndex: 'hostedZone'
        },
        {
          title: 'waf源站ips',
          dataIndex: 'waf'
        },
        {
          title: '备注',
          dataIndex: 'comment'
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt'
        },
        {
          title: '更新时间',
          dataIndex: 'updatedAt'
        }
      ]
      getDnsList(this.downloadqueryParam).then(async (res) => {
        if (res.Data.hasOwnProperty('data')) {
          const XLSX = await loadXLSX()
          const tableData = this.transData(downloadColumns, res.Data.data)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'domain')
          XLSX.writeFile(wb, 'Domain.xlsx')
        } else {
          this.downloadtableList = []
        }
      })
    },
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then((response) => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    transData(columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map((item) => {
        return obj.keys.map((key) => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    handleOk() {
      const form = this.$refs.createModal.form
      this.confirmLoading = true
      form.validateFields((errors, values) => {
        if (!errors) {
          if (values.id > 0) {
            // 修改 e.g.
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then((res) => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('修改成功')
            })
          } else {
            // 新增
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then((res) => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('新增成功')
            })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    listFilter(value) {
      if (!value || value === 'null') return ''
      let value1
      value1 = JSON.parse(value)
      let res = ''
      for (let j = 0; j < value1.length; j++) {
        if (j === 0) {
          res = value1[j].Value
        } else {
          res = res + '|' + value1[j].Value
        }
      }
      return res
    },
    handleSearch(searchText) {
      this.domainList = []
      if (searchText !== '') {
        getDnsDomainList({ searchText: searchText }).then((response) => {
          this.domainList = response.Data.DomainList
        })
      }
    },
    handlePopupScroll(e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.domainList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.domainList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.domainList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.domainList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.domainListMini = newData
        }
      }
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        let arry = response.Data.data
        for (let i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）fb
    },
    handleDel(record) {
      this.$router.push({ path: '/workflow/dns-del', query: { dnsId: record.id, dns: JSON.stringify(record) } })
    },
    handleEdit(record) {
      /**
       * 此处是 VueRouter3 对象 query 处理不合理，VueRouter4 更正后，传入下一个路由时不能获取 dns
       */
      this.$router.push({ path: '/workflow/dns-edit', query: { dnsId: record.id, dns: JSON.stringify(record) } })
    },
    handleAdd(record) {
      this.$router.push({ path: '/workflow/dns-add', query: {} })
    },
    labelEdit(record) {
      this.temp = {}
      this.labelVis = true
      this.temp = JSON.parse(JSON.stringify(record))
      if (this.temp.principalEmails !== '') {
        this.temp.principalEmails = this.temp.principalEmails.split('|')
      }
      // 费用负责人
      if (record.costUser !== '') {
        this.costUserChange(record.costUser)
        if (record.org !== '') {
          this.temp.organization.push(record.org)
          if (record.dep !== '') {
            this.temp.organization.push(record.dep)
          }
        }
      }
    },
    cancelLabel() {
      this.labelVis = false
      this.temp = {}
    },
    scopeFilter(type) {
      return scopeMap[type]?.text || type
    },
    scopeTypeFilter(type) {
      return scopeMap[type]?.status || type
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      console.log(type)
      return statusMap[type]?.status || type
    },
    submitEditLabel() {
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.temp))
          data.org = data.organization[0]
          data.dep = data.organization[1]
          data.principalEmails = data.principalEmails.join('|')
          updateDnsLabel(data).then((res) => {
            if (res.Data.message === 'ok') {
              this.$refs.table.refresh(true)
              notification.success({
                message: '更新成功'
              })
            } else {
              notification.error({
                message: '更新失败'
              })
            }
          })
          this.labelVis = false
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date())
      }
    },
    costUserChange(useremail) {
      GetUserOrgAllInfo({ email: useremail }).then(response => {
        this.organizationOptions = response.Data.orgCas
        this.organizationOptions.forEach(level1Option => {
          if (level1Option.children) {
            level1Option.children.forEach(level2Option => {
              // 移除第三层数据
              delete level2Option.children
            })
          }
        })
        this.temp.organization = []
        if (this.organizationOptions[0].label !== undefined && this.organizationOptions[0].label !== null) {
          this.temp.organization.push(this.organizationOptions[0].label)
          if (this.organizationOptions[0].children !== null && this.organizationOptions[0].children[0].label !== null) {
            this.temp.organization.push(this.organizationOptions[0].children[0].label)
          }
        }
      })
    },
    getUserBaseInfo() {
      getAssetUserList().then((res) => {
        for (let i = 0, len = res.data.data.length; i < len; i++) {
          let user = {}
          user.value = res.data.data[i].value
          user.label = res.data.data[i].key + '(' + res.data.data[i].value + ')'
          user.key = res.data.data[i].key
          this.principalList.push(user)
          if (i <= 50) {
            this.principalListMini.push(user)
          }
        }
      })
    },
    principalScroll(e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.principalList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.principalList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.principalListMini = newData
        }
      }
    },
    principalSearch(val) {
      this.valueData = val
      if (!val) {
        this.getUserBaseInfo()
      } else {
        this.principalListMini = []
        this.scrollPage = 1
        this.principalList.forEach(item => {
          if (item.label.indexOf(val) >= 0) {
            this.principalListMini.push(item)
          }
        })
        this.principalListMini = this.principalListMini.slice(0, 50)
      }
    },
    dnsHandleExpand(expanded, record) {
      // record的值与table展开的数据具有绑定关系
      if (expanded) {
        getDnsWafGateway({ dnsId: record.id }).then(response => {
          if (response.Data.wafList != null) {
            record.waf = response.Data.wafList.sourceDomain
          }
        })
      }
    },
    handleRenew(record) {
      this.RenewVis = true
      this.renewForm.name = record.name
      this.renewForm.expiryDate = record.expiryDate
      this.renewForm.id = record.id
      this.renewForm.newExpiryDate = 31536000
    },
    canceRenew() {
      this.RenewVis = false
      this.renewForm = {}
    },
    submitRenew() {
      updateDnsRenew(this.renewForm).then((res) => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          notification.success({
            message: '续签成功'
          })
        } else {
          notification.error({
            message: '续签失败'
          })
        }
      })
      this.canceRenew()
    },
    handleCancelMonitor(record) {
      this.$confirm({
        title: '确认取消监控?',
        content: `是否确认取消对域名 ${record.name} 的监控?`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
        // 这里需要调用取消监控的API
          cancelDnsMonitor({
            id: record.id,
            name: record.name,
            resourceRecords: record.resourceRecords
          }).then(res => {
            if (res.Data.message === 'ok') {
              this.$notification.success({
                message: '取消监控成功'
              })
              this.$refs.table.refresh(true)
            }
          }).catch(err => {
            this.$notification.error({
              message: '取消监控失败',
              description: err.message
            })
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
}
</style>
