<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.domain" placeholder="请输入域名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="来源">
                <a-select v-model:value="queryParam.source" placeholder="请选择域名来源" :options="SourceList"></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleIcpAdd">新增</tx-button>
        <tx-button type="primary" icon="download" @click="handleDownload">导出</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions >
            <a-descriptions-item label="主办单位名称">{{ record.organizerName }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{column, record}">
        <template v-if="column.dataIndex == 'action'">
          <span v-if="record.source != 'ucloud'">
            <tx-button type="link" style="width: 40px" @click="handleEdit(record)">更新</tx-button>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </span>
        </template>
        </template>
      </s-table>
      <a-modal v-model:visible="addVisible" title="新增域名备案">
        <template #footer>
          <tx-button key="back" @click="cancelAddIcp">取消</tx-button>
          <tx-button v-if="!editVisble" key="submit" type="primary" @click="submitAddIcp">确定</tx-button>
          <tx-button v-if="editVisble" key="submit" type="primary" @click="submitEditIcp">确定</tx-button>
        </template>
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :model="addQueryParam"
          :rules="Rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-form-model-item v-if="!editVisble" label="备案号" class="my-ant-form-item" name="icpWebNo">
            <a-input v-model:value="addQueryParam.icpWebNo" placeholder="沪ICP备12002324号-1"/>
          </a-form-model-item>
          <a-form-model-item v-if="editVisble" label="备案号" class="my-ant-form-item" name="icpWebNo">
            <a-input v-model:value="addQueryParam.icpWebNo" placeholder="沪ICP备12002324号-1" disabled/>
          </a-form-model-item>
          <a-form-model-item label="网页名称" class="my-ant-form-item" name="webName">
            <a-input v-model:value="addQueryParam.webName" placeholder="上海合合信息科技股份有限公司"/>
          </a-form-model-item>
          <a-form-model-item label="网站首站" class="my-ant-form-item" name="url">
            <a-input v-model:value="addQueryParam.url" placeholder="www.intsig.net"/>
          </a-form-model-item>
          <a-form-model-item label="域名" class="my-ant-form-item" name="domain">
            <a-select
              v-model:value="addQueryParam.domain"
              placeholder="请选择域名"
              :filterOption="false"
              mode="multiple"
              :options="domainListMini"
              style="width: 200px"
              @popupScroll="handlePopupScroll"
              @search="handleSearch">
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="ip" class="my-ant-form-item" name="ip">
            <a-input v-model:value="addQueryParam.ip" placeholder="*************"/>
          </a-form-model-item>
          <a-form-model-item label="网站负责人" class="my-ant-form-item" name="picName">
            <a-input v-model:value="addQueryParam.picName" placeholder="李四"/>
          </a-form-model-item>
          <a-form-model-item label="主办单位名称" class="my-ant-form-item" name="organizerName">
            <a-input v-model:value="addQueryParam.organizerName" placeholder="上海合合信息科技股份有限公司"/>
          </a-form-model-item>
          <a-form-model-item label="来源" class="my-ant-form-item" name="source">
            <a-input v-model:value="addQueryParam.source" placeholder="aws"/>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { STable, Ellipsis } from '@/components'
import { getIcpList, getIcpSourceList, creatIcp, updateIcp, deleteIcp } from '@/api/domain/icp'
import { getDnsDomainList } from '@/api/domain/dns'
import { notification } from 'ant-design-vue'
const columns = [
  {
    title: '备案号',
    dataIndex: 'icpWebNo',
    sorter: true
  },
  {
    title: '网站名称',
    dataIndex: 'webName',
    sorter: true
  },
  {
    title: '网站首页',
    dataIndex: 'url',
    sorter: true
  },
  {
    title: '域名',
    dataIndex: 'domain',
    width: '200px',
    sorter: true
  },
  {
    title: 'ip',
    dataIndex: 'ip',
    width: '170px',
    sorter: true
  },
  {
    title: '备案注册人',
    dataIndex: 'picName',
    sorter: true
  },
  {
    title: '来源',
    dataIndex: 'source',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    scopedSlots: { customRender: 'action' }
  }
]
const pagination = {
  showTotal: total => `共 ${total} 条`
}
export default {
  name: 'IcpList',
  components: {
    STable,
    Ellipsis
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      dnsTypeList: [],
      domainList: [],
      domainListMini: [],
      scrollPage: 1,
      valueData: '',
      treePageSize: 50,
      SourceList: [],
      domainInfo: [],
      addDomain: [],
      addQueryParam: { 'url': '' },
      visible: false,
      addVisible: false,
      editVisble: false,
      confirmLoading: false,
      Rules: {
        'icpWebNo': [{ required: true, message: '请输入域名备案号', trigger: 'blur' }],
        'webName': [{ required: true, message: '请输入网页名称', trigger: 'blur' }],
        'url': [{ required: true, message: '请输入首页地址', trigger: 'change' }],
        'domain': [{ required: true, message: '请选择相关域名', trigger: 'change' }],
        'ip': [{ required: true, message: '请输入ip', trigger: 'change' }],
        'picName': [{ required: true, message: '请输入备案负责人', trigger: 'change' }],
        'source': [{ required: true, message: '请输入来源', trigger: 'change' }]
      },
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        return getIcpList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
             return res.Data
         } else {
             return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
         }
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created () {
    getIcpSourceList().then((res) => {
        this.SourceList = res.Data.SourceList
        console.log(this.SourceList)
    })
    this.getDomainInfor()
    loadXLSX()
  },
  methods: {
    getDomainInfor () {
      getDnsDomainList().then((res) => {
      this.domainList = res.Data.DomainList
      this.domainListMini = res.Data.DomainList.slice(0, 50)
     })
    },
    handleIcpAdd () {
      this.addVisible = true
    },
    handleInfo (record) {
      console.log(record)
    },
    handleDownload () {
      const downloadColumns = [
          {
            title: 'ID',
            dataIndex: 'id'
          },
          {
            title: '备案号',
            dataIndex: 'icpWebNo'
          },
          {
            title: '网站名称',
            dataIndex: 'webName'
          },
          {
            title: '网站首页',
            dataIndex: 'url'
          },
          {
            title: '域名',
            dataIndex: 'domain'
          },
          {
            title: 'ip',
            dataIndex: 'ip'
          },
          {
            title: '负责人',
            dataIndex: 'picName'
          },
          {
            title: '来源',
            dataIndex: 'source'
          },
          {
            title: '主办单位名称',
            dataIndex: 'organizerName'
          },
          {
            title: '创建时间',
            dataIndex: 'createdAt'
          },
          {
            title: '更新时间',
            dataIndex: 'updatedAt'
          }
        ]
      getIcpList(this.downloadqueryParam).then(async (res) => {
         if (res.Data.hasOwnProperty('data')) {
          const XLSX = await loadXLSX()
          const tableData = this.transData(downloadColumns, res.Data.data)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'ICP')
          XLSX.writeFile(wb, '域名备案.xlsx')
         } else {
          this.downloadtableList = []
         }
        })
    },
    transData (columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map((item) => {
        return obj.keys.map((key) => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    listFilter (value) {
      if (!value) return ''
      var value1
      value1 = JSON.parse(value)
      var res = ''
      for (var j = 0; j < value1.length; j++) {
        if (j === 0) {
          res = value1[j].Value
        } else {
          res = res + '|' + value1[j].Value
        }
      }
      return res
    },
    handleSearch (val) {
      this.valueData = val
      if (!val) {
        this.getDomainInfor()
      } else {
        this.domainListMini = []
        this.scrollPage = 1
        this.domainList.forEach(item => {
          if (item.label.indexOf(val) >= 0) {
            this.domainListMini.push(item)
          }
        })
        this.domainListMini = this.domainListMini.slice(0, 50)
      }
    },
    handlePopupScroll (e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.domainList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.domainList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.domainList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.domainList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.domainListMini = newData
        }
      }
    },
    handleCancel () {
      this.visible = false
      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    cancelAddIcp () {
      this.addVisible = false
      this.editVisble = false
      this.addQueryParam = {}
    },
     submitAddIcp () {
      antdFormValidate(this.$refs.addForm, (valid) => {
        if (valid) {
          this.addQueryParam.domain = JSON.stringify(this.addQueryParam.domain)
          creatIcp(this.addQueryParam).then((res) => {
            if (res.Data.message === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '创建成功',
                    description: '域名备案创建成功'
                  })
            } else if (res.Data.message === 'exist') {
                notification.error({
                  message: '创建失败',
                  description: '域名备案号已存在！'
                })
            }
          })
          this.cancelAddIcp()
        }
      })
    },
    handleEdit (record) {
      this.addQueryParam = record
      this.addQueryParam.domain = JSON.parse(record.domain)
      this.editVisble = true
      this.addVisible = true
    },
    submitEditIcp () {
      antdFormValidate(this.$refs.addForm, (valid) => {
        if (valid) {
          this.addQueryParam.domain = JSON.stringify(this.addQueryParam.domain)
          updateIcp(this.addQueryParam).then((res) => {
            if (res.Data.message === 'ok') {
                this.$refs.table.refresh(true)
                notification.success({
                    message: '更新成功'
                  })
            } else if (res.Data.message === 'exist') {
                notification.error({
                  message: '更新失败'
                })
            }
          })
          this.cancelAddIcp()
        }
      })
    },
    handleDel (record) {
      deleteIcp(record.id).then((res) => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          notification.success({
              message: '删除成功'
            })
        } else {
            notification.error({
              message: '删除失败'
            })
        }
      })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    resetSearchForm () {
      this.queryParam = {
        date: moment(new Date())
      }
    }
  }
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
}
</style>
