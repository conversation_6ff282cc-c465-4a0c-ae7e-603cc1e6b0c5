<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="证书名称">
                <a-input v-model:value="queryParam.certName" placeholder="请输入证书名称" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="域名">
                <a-input v-model:value="queryParam.domain" placeholder="请输入域名" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator" v-if="userpRression">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
        <tx-button type="primary" icon="download" @click="handleDownload">导出</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions >
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
            <a-descriptions-item label="告警联系人">{{ record.monitorUser }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{column, record}">
          <template v-if="column.dataIndex == 'action'">
            <tx-button type="link" style="width: 40px" @click="handleInfo(record)">下载</tx-button>
            <tx-button type="link" v-if="userpRression" style="width: 40px" @click="handleUpdate(record)">更新</tx-button>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a v-if="userpRression">删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <create-form
        ref="createModal"
        :visible="visible"
        :loading="confirmLoading"
        :model="mdl"
        @cancel="handleCancel"
        @ok="handleOk"
      />
      <step-by-step-modal ref="modal" @ok="handleOk" />
      <a-modal :visible="downloadVisible" width="350px" :closable="false" title="域名证书下载" @ok="handleOk">
        <a-form layout="inline">
          <a-form-model-item >
            <a-row>
              <a-col :span="18">
                <a-input v-model:value="verificationCode" placeholder="请输入验证码" style="width: 180px"/>
              </a-col>
              <a-col :span="6">
                <tx-button key="back" @click="handleInfo('repeated')">重新发送</tx-button>
              </a-col>
            </a-row>
          </a-form-model-item>
        </a-form>
        <template #footer>
          <a-row>
            <a-col :span="10">
              <tx-button key="submit" type="primary" @click="submitCheck">确定</tx-button>
            </a-col>
            <a-col :span="10">
              <tx-button key="back" @click="cancelCheck">取消</tx-button>
            </a-col>
          </a-row>
        </template>
      </a-modal>
      <a-modal v-model:visible="addVisible" title="新增Ssl证书" :width="1000">
        <template #footer>
          <tx-button key="back" @click="cancelAddSsl">取消</tx-button>
          <tx-button key="submit" type="primary" @click="submitAddSsl">确定</tx-button>
        </template>
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :model="addQueryParam"
          :rules="Rules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-model-item label="证书名称" name="certName">
            <a-input v-model:value="addQueryParam.certName" placeholder="请输入Ssl证书名"/>
          </a-form-model-item>
          <a-form-model-item label="证书内容" name="certPublicContent">
            <a-textarea
              v-model:value="addQueryParam.certPublicContent"
              placeholder=""
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-model-item>
          <a-form-model-item label="证书私钥内容" name="certPrivateContent">
            <a-textarea
              v-model:value="addQueryParam.certPrivateContent"
              placeholder=""
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal v-model:visible="updateVisible" title="更新Ssl证书" :width="1000">
        <template #footer>
          <tx-button key="back" @click="cancelUpdateSsl">取消</tx-button>
          <tx-button key="submit" type="primary" @click="submitUpdateSsl">确定</tx-button>
        </template>
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :model="updateQueryParam"
          :rules="Rules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-model-item label="证书名称" name="certName">
            <a-input v-model:value="updateQueryParam.certName" disabled/>
          </a-form-model-item>
          <a-form-model-item label="证书内容" name="certPublicContent">
            <a-textarea
              v-model:value="updateQueryParam.certPublicContent"
              placeholder=""
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-model-item>
          <a-form-model-item label="证书私钥内容" name="certPrivateContent">
            <a-textarea
              v-model:value="updateQueryParam.certPrivateContent"
              placeholder=""
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { loadJSZip, loadXLSX } from '@/utils/vendorLoader'
import FileSaver from 'file-saver'
import { STable, Ellipsis } from '@/components'
import { getUserList } from '@/api/permission/user'
import { getSslList, uploadSsl, deleteSsl, updateSsl } from '@/api/domain/ssl'
import { sslDownloadSend, sslDownloadCheck, sslDownload } from '@/api/domain/domain'
import { notification } from 'ant-design-vue'
import store from '@/store'

const columns = [
  {
    title: '证书名称',
    dataIndex: 'certName',
    sorter: true
  },
  {
    title: '开始时间',
    dataIndex: 'beginDate',
    sorter: true
  },
  {
    title: '过期时间',
    dataIndex: 'expireDate',
    sorter: true
  },
  {
    title: '域名',
    dataIndex: 'domain',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '170px',
    align: 'center',
    scopedSlots: { customRender: 'action' }
  }
]
const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      dnsTypeList: [],
      domainList: [],
      dnsSourceList: [],
      domainInfo: [],
      addQueryParam: { },
      updateQueryParam: { },
      localUser: store.getters.email,
      userpRression: false,
      downloadVisible: false,
      downloadCertName: '',
      visible: false,
      verificationCode: '',
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      userRoles: [],
      userRolesWhite: 'domain_admin',
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { },
      Rules: {
        'certName': [{ required: true, message: '请输入ssl证书名称', trigger: 'change' }],
        'certPublicContent': [{ required: true, message: '请输入证书内容', trigger: 'change' }],
        'certPrivateContent': [{ required: true, message: '请输入证书私钥', trigger: 'change' }]
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        return getSslList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            // 在此处对资源记录 这一条进行修改
            for (var i = 0; i < res.Data.data.length; i++) {
              res.Data.data[i].resourceRecords = this.listFilter(res.Data.data[i].resourceRecords)
            }
             return res.Data
         } else {
             return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
         }
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  mounted() {
    this.getUserRoles(this.localUser.split('@')[0])
    loadJSZip()
    loadXLSX()
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles (userEmail) {
        getUserList({ searchText: userEmail }).then((response) => {
          this.userRoles = response.Data.data[0].roles
          if (this.userRoles.includes(this.userRolesWhite)) {
             this.userpRression = true
          }
        })
      },
    handleAdd () {
      this.addQueryParam = {}
      this.addVisible = true
    },
    handleUpdate (record) {
      this.addQueryParam = {}
      this.updateQueryParam.certName = record.certName
      this.updateVisible = true
    },
    cancelAddSsl () {
      this.addVisible = false
      this.addQueryParam = {}
    },
    cancelUpdateSsl () {
      this.updateVisible = false
      this.updateQueryParam = {}
    },
    submitAddSsl () {
      antdFormValidate(this.$refs.addForm, (valid) => {
        if (valid) {
          uploadSsl(this.addQueryParam).then((res) => {
            if (res.Data.message === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '创建成功'
                  })
            } else if (res.Data.message === 'exist') {
                notification.error({
                  message: '创建失败',
                  description: 'ssl证书已存在!'
                })
            } else if (res.Data.message === 'fmtError') {
                notification.error({
                  message: '创建失败',
                  description: 'ssl证书格式信息错误!'
                })
            }
          })
          this.cancelAddSsl()
        }
      })
    },
    submitUpdateSsl () {
      antdFormValidate(this.$refs.addForm, (valid) => {
        if (valid) {
          updateSsl(this.updateQueryParam).then((res) => {
            if (res.Data.message === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '更新成功',
                    description: '证书更新成功'
                  })
            } else if (res.Data.message === 'parse err') {
                notification.error({
                  message: '更新失败',
                  description: '密钥文件解析错误!'
                })
            } else if (res.Data.message === 'update err') {
                notification.error({
                  message: '更新失败',
                  description: '数据更新失败!'
                })
            }
          })
          this.cancelUpdateSsl()
        }
      })
    },
    handleDownload () {
      const downloadColumns = [
          {
            title: 'ID',
            dataIndex: 'id'
          },
          {
            title: '证书名称',
            dataIndex: 'certName'
          },
          {
            title: '开始时间',
            dataIndex: 'beginDate'
          },
          {
            title: '过期时间',
            dataIndex: 'expireDate'
          },
          {
            title: '域名',
            dataIndex: 'domain'
          },
          {
            title: '告警联系人',
            dataIndex: 'monitorUser'
          },
          {
            title: '私钥',
            dataIndex: 'certPrivateContent'
          },
          {
            title: '证书',
            dataIndex: 'certPublicContent'
          },
          {
            title: '上传时间',
            dataIndex: 'createdAt'
          },
          {
            title: '更新时间',
            dataIndex: 'updatedAt'
          }
        ]
      getSslList(this.downloadqueryParam).then(async (res) => {
         if (res.Data.hasOwnProperty('data')) {
          const XLSX = await loadXLSX()
          const tableData = this.transData(downloadColumns, res.Data.data)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'ssl')
          XLSX.writeFile(wb, '域名证书.xlsx')
         } else {
          this.downloadtableList = []
         }
        })
    },
    transData (columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map((item) => {
        return obj.keys.map((key) => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    handleOk () {
      const form = this.$refs.createModal.form
      this.confirmLoading = true
      form.validateFields((errors, values) => {
        if (!errors) {
          if (values.id > 0) {
            // 修改 e.g.
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then((res) => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('修改成功')
            })
          } else {
            // 新增
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then((res) => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('新增成功')
            })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    listFilter (value) {
      if (!value) return ''
      var value1
      value1 = JSON.parse(value)
      var res = ''
      for (var j = 0; j < value1.length; j++) {
        if (j === 0) {
          res = value1[j].Value
        } else {
          res = res + '|' + value1[j].Value
        }
      }
      return res
    },
     handleInfo (record) {
      if (record !== 'repeated') {
          this.downloadCertName = record.certName
      }
      if (this.downloadCertName !== '' && this.downloadCertName !== undefined) {
        this.downloadVisible = true
        var sendData = {}
        sendData.email = store.getters.email
        sslDownloadSend(sendData).then((res) => {
          if (res.Data.message === 'ok') {
            notification.success({
                  message: '已发送验证码'
                })
          } else if (res.Data.message === 'exist') {
            notification.warning({
                  message: '验证码未过期'
                })
          }
        })
      }
    },
    cancelCheck () {
      this.downloadVisible = false
      const form = this.$refs.addForm.form
      form.resetFields()
    },
    submitCheck () {
         // 发送前端信息到后端  进行校验6位数字
        var checkData = {}
        checkData.email = store.getters.email
        checkData.code = this.verificationCode
        sslDownloadCheck(checkData).then((res) => {
          if (res.Data.message === 'ok') {
            // 调取域名证书的下载pem文件
             var certData = {}
            certData.certName = this.downloadCertName
            sslDownload(certData).then(async (res) => {
              // 增加判断
              const JSZip = await loadJSZip()
              const zip = new JSZip()
              const blob1 = new Blob([res.Data.certPublicContent], { type: 'text/plain;charset=utf-8' })
              const blob2 = new Blob([res.Data.certPrivateContent], { type: 'text/plain;charset=utf-8' })
              zip.file(certData.certName + '证书.pem', blob1, { binary: true })
              zip.file(certData.certName + '私钥.pem', blob2, { binary: true })
              zip.generateAsync({ type: 'blob' }).then(function (content) {
                FileSaver.saveAs(content, certData.certName + '.zip')
              })
              this.cancelCheck()
            })
          } else if (res.Data.message === 'error') {
            notification.error({
                message: '输入验证码错误'
              })
          } else if (res.Data.message === 'notExist') {
            notification.warning({
                message: '验证码已过期，请重新发送'
              })
          }
        })
    },
    handleCancel () {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    handleDel (record) {
      deleteSsl(record.id).then((res) => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          notification.success({
              message: '删除成功'
            })
        } else {
            notification.error({
              message: '删除失败'
            })
        }
      })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    resetSearchForm () {
      this.queryParam = {
        date: moment(new Date())
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .myform {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
}
</style>
