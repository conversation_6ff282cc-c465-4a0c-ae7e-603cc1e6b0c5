<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-form layout="inline">
        <a-form-item label="集群名称">
          <a-select style="width: 180px" allowClear showSearch :options="clusterList"
            v-model:value="actForm.clusterUuid" placeholder="请选择集群"></a-select>
        </a-form-item>
        <a-form-item>
          <a-input-search placeholder="搜索Set" enter-button @search="onSearch" />
        </a-form-item>
        <a-form-item>
          <tx-button type="primary" @click="SetHandler('{}', 'addRoot')">新增Set</tx-button>
          <tx-button style="margin-left: 8px;" icon="reload" @click="refreshFilerStatus">Filer状态</tx-button>
          <tx-button v-if="$route.query.clusterUuid" style="margin-left: 8px;" @click="backToClusterManage">
            返回集群管理
          </tx-button>
        </a-form-item>
      </a-form>
      <a-table :pagination="pagination" :columns="columns" rowKey="idKey" :data-source="SetData"
        @change="handleTableChange">
        <template #bodyCell="{ column, record: eachInfos }">
          <template v-if="column.dataIndex == 'action'">
            <tx-button type="primary" @click="SetHandler(eachInfos, 'stats')">Stats</tx-button>
            <tx-button style="margin-left: 8px; background-color: #67c23a; color: aliceblue" @click="SetHandler(eachInfos, 'syncfiler')">同步Filer</tx-button>
            <tx-button style="margin-left: 8px;" type="primary" @click="SetHandler(eachInfos, 'details')">详情</tx-button>
            <tx-button style="margin-left: 8px; background-color: #67c23a; color: aliceblue"
              @click="SetHandler(eachInfos, 'update')">
              更新
            </tx-button>
          </template>
          <template v-else-if="column.dataIndex == 'masters'">
            <div>
              <div v-for="(master, index) in eachInfos.masters.split(',')" :key="index">
                <span v-if="master.endsWith('[L]')" style="color: green;">
                  {{ master }}
                </span>
                <span v-else>
                  {{ master }}
                </span>
              </div>
            </div>
          </template>
          <template v-else-if="column.dataIndex == 'status'"> <!-- 添加自定义渲染 -->
            <a-select style="width: 120px" :value="eachInfos.status" @click="statusChangeHandler(eachInfos, $event)"
              @change="statusChangeHandler2(eachInfos, $event)" placeholder="改变状态">
              <a-select-option v-for="(statusValue, statusKey) in availableStatusOptions" :key="statusKey"
                :value="statusKey">
                {{ statusValue }}
              </a-select-option>
            </a-select>
          </template>
          <template v-else-if="column.dataIndex == 'filersStatus'">
            <div>
              <div v-for="(statusInfo, index) in eachInfos.statusCounts" :key="index">
                {{ statusInfo.status }}: {{ statusInfo.count }}
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>
    <a-modal :title="modalTitle" v-model:visible="modalVisible">
      <template #footer>
        <tx-button :disabled="modalTitle == '查看详情'" key="back" @click="handleCancel">取消</tx-button>
        <tx-button :disabled="modalTitle == '查看详情'" key="submit" type="primary" :loading="loading" @click="handleOk">
          确定
        </tx-button>
      </template>
      <a-form-model v-if="modalVisible" ref="myForm" :model="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }"
        :rules="rules">
        <a-form-model-item v-if="modalTitle !== '新增Set'" label="集群UUID" name="clusterUuid">
          <a-input :disabled="true" v-model:value="form.clusterUuid" />
        </a-form-model-item>

        <a-form-model-item v-if="modalTitle !== '新增Set'" label="集群名称" name="clusterName">
          <a-input :disabled="true" v-model:value="form.clusterName" />
        </a-form-model-item>

        <a-form-model-item v-if="modalTitle == '新增Set'" label="集群名称" name="clusterName">
          <a-select style="width: 180px" allowClear showSearch :options="clusterList"
            v-model:value="this.form.clusterUuid" placeholder="请选择集群"></a-select>
        </a-form-model-item>

        <a-form-model-item label="Set名称" name="name">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.name" />
        </a-form-model-item>

        <a-form-model-item label="权重" name="weight">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.weight" />
        </a-form-model-item>

        <a-form-model-item label="VidLength" name="vidLength">
          <a-input :disabled="modalTitle != '新增Set'" v-model:value="form.vidLength" />
        </a-form-model-item>

        <a-form-model-item label="Masters" name="masters">
          <a-textarea :disabled="modalTitle == '查看详情'" v-model:value="form.masters"
            :auto-size="{ minRows: 1, maxRows: 15 }" />
          <template v-if="modalTitle === '新增Set'">
            <div style="color: #faad14; font-size: 12px; margin-top: 4px;">
              * 填写一个以上 master 时请换行输入
            </div>
          </template>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal :title="confirmModalTitle" v-model:visible="confirmModalVisible">
      <template #footer>
        <tx-button key="back" @click="confirmModalVisible = false">取消</tx-button>
        <tx-button key="submit" type="primary" @click="confirmStatusChange">确定</tx-button>
      </template>
      <p>确定将Set由 {{ currentStatus }} 变为 {{ this.statusMap[selectedStatus] }} 吗？</p>
    </a-modal>
    <a-modal :title="statsModalTitle" v-model:visible="statsModalVisible" :width="1000">
      <template #footer>
        <tx-button key="back" @click="statsModalVisible = false">关闭</tx-button>
      </template>
      <div v-html="statsHtmlContent"></div>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { createSet, deleteSet, getSetTopo, getSets, updateSet, updateSetStatus, listSetsFilerStatus, getMasterleaderForSets, pushSetStatusToFiler } from '@/api/iseafs/setApi'
import { getClusterNames } from '@/api/iseafs/clusterApi'

const columns = [
  {
    title: 'Set ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'Set名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '权重',
    dataIndex: 'weight',
    key: 'weight',
  },
  {
    title: 'VidRange',
    dataIndex: 'vidRange',
    key: 'vidRange',
  },
  {
    title: 'Masters',
    dataIndex: 'masters',
    key: 'masters',
    scopedSlots: { customRender: 'masters' },
  },
  {
    title: 'Filer状态',
    dataIndex: 'filersStatus',
    key: 'filersStatus',
    scopedSlots: { customRender: 'filersStatus' },
  },
  {
    title: '配置状态',
    dataIndex: 'status',
    key: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '30', '40'],
}

export default {
  name: 'SetManage',

  props: {
    // 需要隐藏的item
    hiddenItem: {
      type: Array,
      default: () => {
        return []
      },
    },
    // clusterGroups: {
    //   type: Array,
    //   default: () => {
    //     return []
    //   },
    // },
  },
  data() {
    return {
      SetData: [],
      columns,
      filteredSetIds: [],
      pagination,
      modalTitle: '',
      modalVisible: false,
      loading: false,
      actForm: {
        clusterUuid: '',
      },
      form: {},
      rules: {
        name: [{ required: true, message: '请输入Set名称', trigger: 'blur' }],
      },
      statusMap: {
        0: '离线',
        1: '只读',
        2: '可写',
        3: '删除',
        4: '未知',
      },
      clusterList: [],
      clusterGroups: [],
      selectedStatus: 0,
      confirmModalVisible: false,
      confirmModalTitle: '',
      currentStatus: '',
      currentRecord: {},
      availableStatusOptions: {},
      statsModalVisible: false,
      statsModalTitle: '',
      statsHtmlContent: '',
      filersStatusData: {}, // 新增 filersStatusData 字段
      selectedClusterUuid: '',
      searchText: '',
    }
  },
  mounted() {
    getClusterNames().then(res => {
      this.clusterGroups = res.Data.clusters
      this.clusterList = this.clusterGroups.map(item => ({
        value: item.uuid,
        label: item.name,
      }))
      
      // 检查路由参数中是否有集群UUID
      const routeClusterUuid = this.$route.query.clusterUuid
      if (routeClusterUuid) {
        // 如果路由参数中有集群UUID，使用它
        this.selectedClusterUuid = routeClusterUuid
        this.actForm.clusterUuid = routeClusterUuid
        // 显示来源集群的提示信息
        const clusterName = this.$route.query.clusterName || '未知集群'
        this.$message.success(`已自动选择集群：${clusterName}`)
      } else if (this.clusterList.length > 0) {
        // 否则选择第一个集群
        this.selectedClusterUuid = this.clusterList[0].value
        this.actForm.clusterUuid = this.selectedClusterUuid
      }
      
      this.initPage({ clusterUuid: this.selectedClusterUuid })
    })
  },
  methods: {
    handleTableChange(pagination) {
      this.pagination.current = pagination.current
      this.initPage({
        pageSize: pagination.pageSize,
        pageNo: pagination.current,
      })
    },

    initPage(params = {}) {
      getSets({
        pageSize: 10,
        pageNo: 1,
        ...params,
      }).then(res => {
        if (!res.Data.sets) {
          this.SetData = []
          this.pagination.total = 0
          this.filteredSetIds = []
          return
        }
        this.SetData = res.Data.sets.map(set => {
          return {
            ...set,
            masters: set.masters.join(','), // 将 masters 数组以逗号分割拼接成字符串
            status: this.statusMap[set.status] || '未知状态', // 转换状态
            statusKey: set.status, // 添加 statusKey 字段
          }
        })
        this.pagination.total = res.totalCount
        this.filteredSetIds = this.SetData
          .filter(set => set.statusKey === 1 || set.statusKey === 2)
          .map(set => set.id)

        const setIdsParam = this.filteredSetIds.join(',')
        if (this.filteredSetIds.length > 0) {
          listSetsFilerStatus({ setIds: setIdsParam })
            .then(response => {
              this.filersStatusData = response.Data.setStatus
              this.SetData = this.SetData.map(set => {
                const setStatus = this.filersStatusData[set.id]?.status || {}
                const statusCounts = Object.entries(setStatus).map(([statusKey, count]) => ({
                  status: this.statusMap[parseInt(statusKey)] || '未知状态',
                  count: count
                }))
                return {
                  ...set,
                  statusCounts: statusCounts,
                }
              })
            })
            .catch(error => {
              console.error('Failed to fetch filer statuses:', error)
            })

          getMasterleaderForSets({ setIds: setIdsParam })
            .then(response => {
              const leaderMap = response.Data.leaderMap
              this.SetData = this.SetData.map(set => {
                const mastersArray = set.masters.split(',')
                const updatedMastersArray = mastersArray.map(master => {
                  if (leaderMap[set.id] && master === leaderMap[set.id]) {
                    return `${master}[L]`
                  }
                  return master
                })
                const updatedMasters = updatedMastersArray.join(',')
                return {
                  ...set,
                  masters: updatedMasters
                }
              })
            })
            .catch(error => {
              console.error('Failed to getMasterleaderForSets:', error)
            })
        }
      })
    },

    handleCancel() {
      this.modalVisible = false
    },

    handleOk() {
      this.loading = true
      // somecode
      antdFormValidate(this.$refs.myForm, valid => {
        if (valid) {
          this.modalVisible = false
          // 校验成功后，根据弹框名称来确定要请求的接口
          switch (this.modalTitle) {
            case '新增Set':
              const obj = {
                clusterUuid: this.form.clusterUuid,
                name: this.form.name,
                // 将多行文本转换回以 | 分隔的字符串
                masters: this.form.masters.split('\n').map(master => master.trim()),
                // masters: this.form.masters.split(',').map(master => master.trim()), // 以 | 为分隔符，并去除前后空格
                weight: this.form.weight,
                vidLength: this.form.vidLength,
              }
              createSet(obj)
                .then(() => {
                  this.$message.info('新增成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('新增失败')
                })
              break
            case '更新':
              const param = {
                id: this.form.id,
                name: this.form.name,
                // 将多行文本转换回以 | 分隔的字符串
                masters: this.form.masters.split('\n').map(master => master.trim()),
                // masters: this.form.masters.split(',').map(master => master.trim()), // 以 | 为分隔符，并去除前后空格
                weight: this.form.weight,
              }
              updateSet(param)
                .then(() => {
                  this.$message.info('更新成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('更新失败')
                })
              break
          }
        } else {
          return false
        }
      })
      this.loading = false
    },

    SetHandler(record, action) {
      this.modalVisible = true
      this.form = { ...record }

      switch (action) {
        case 'addRoot':
          this.modalTitle = '新增Set'
          this.form = {
            clusterUuid: this.actForm.clusterUuid,
            weight: 100,
            vidLength: 1000000,
            name: '',
            masters: '',
          }
          break

        case 'details':
          this.modalTitle = '查看详情'
          // 根据 clusterUuid 找到对应的 clusterName
          const clusterForDetails = this.clusterGroups.find(cluster => cluster.uuid === this.form.clusterUuid)
          if (clusterForDetails) {
            this.form.clusterName = clusterForDetails.name
          }
          // 将 masters 从以 | 分隔的字符串转换为多行文本
          this.form.masters = this.form.masters ? this.form.masters.split(',').join('\n') : ''
          break

        case 'update':
          this.modalTitle = '更新'
          // 根据 clusterUuid 找到对应的 clusterName
          const clusterForUpdate = this.clusterGroups.find(cluster => cluster.uuid === this.form.clusterUuid)
          if (clusterForUpdate) {
            this.form.clusterName = clusterForUpdate.name
          }
          // 将 masters 从以 | 分隔的字符串转换为多行文本，并去掉[L]
          this.form.masters = this.form.masters
            ? this.form.masters.split(',').map(m => m.replace(/\[L\]$/, '')).join('\n')
            : ''
          break

        case 'stats':
          this.modalVisible = false
          this.statsModalVisible = true
          this.statsModalTitle = 'Stats'
          getSetTopo({ id: record.id }).then(res => {
            try {
              // 解码base64并转换编码
              const decodedString = decodeURIComponent(escape(atob(res.Data.data)))
              this.statsHtmlContent = decodedString
            } catch (e) {
              this.$message.error('HTML内容解析失败')
              console.error('Base64解码失败:', e)
              this.statsModalVisible = false
            }
          }).catch(err => {
            this.$message.error(`获取Stats失败: ${err.response?.data?.message || err.message}`)
            this.statsModalVisible = false
            console.error('Stats请求失败:', err)
          })
          break

          case 'syncfiler':
          this.modalVisible = false
  const param = {
    id: record.id,
  }
  pushSetStatusToFiler(param)
    .then(() => {
      this.$message.info('同步成功')
      this.refreshFilerStatus()
    })
    .catch(() => {
      this.$message.error('同步失败')
      this.refreshFilerStatus()
    })
  break
        // case 'updateStatus':
        //   this.modalTitle = '更新状态'
        //   this.$confirm({
        //     title: '更新状态',
        //     content: `确定更新Set状态由 ${record.status} 变为 ${this.statusMap[this.selectedStatus]}？`,
        //     onOk: () => {
        //       const param = {
        //         id: record.id,
        //         status: this.selectedStatus
        //       }
        //       updateSetStatus(param).then(this.initPage)
        //     }
        //   })
        //   break

        case 'delete':
          this.$confirm({
            title: '确认删除',
            content: `确定删除Set ${record.name} 吗？`,
            onOk: () => {
              deleteSet({ id: record.id }).then(this.initPage)
            }
          })
          break
      }
    },

    onSearch(value) {
      const selectedClusterUuid = this.actForm.clusterUuid
      this.searchText = value
      this.initPage({ searchText: value, clusterUuid: selectedClusterUuid })
    },

    //     filteredSetIdsForFilerStatus() {
    //   this.filteredSetIds = this.SetData
    //     .filter(set => set.statusKey === 1 || set.statusKey === 2)
    //     .map(set => set.id)
    //   console.log(this.filteredSetIds) // 或者传递给其他方法
    // }

    statusChangeHandler(record) {
      const currentStatusKey = Object.keys(this.statusMap).find(key => this.statusMap[key] === record.status)
      this.currentStatus = record.status
      this.currentRecord = record

      // 根据当前状态生成可用的状态选项
      this.availableStatusOptions = {}
      switch (currentStatusKey) {
        case '0': // 离线
          this.availableStatusOptions = {
            1: '发布',
            3: '删除',
          }
          break
        case '1': // 只读
          this.availableStatusOptions = {
            2: '可写',
          }
          break
        case '2': // 可写
          this.availableStatusOptions = {
            1: '只读',
          }
          break
        case '3': // 删除
          this.availableStatusOptions = {}
          break
        default:
          this.availableStatusOptions = {}
          break
      }

      // console.log(this.availableStatusOptions, 'this.availableStatusOptions')

      // // 显示确认对话框
      // this.confirmModalVisible = true
      // this.confirmModalTitle = '确定更新状态'
    },

    statusChangeHandler2(record, selectedStatusKey) {
      const currentStatusKey = Object.keys(this.statusMap).find(key => this.statusMap[key] === record.status)
      this.currentStatus = record.status
      this.currentRecord = record
      this.selectedStatus = selectedStatusKey
      if (this.selectedStatus === currentStatusKey) {
        this.confirmModalVisible = false
      } else if (this.selectedStatus !== currentStatusKey) {
        this.confirmModalVisible = true
        this.confirmModalTitle = '确定更新状态'
      }
    },

    confirmStatusChange() {
      const param = {
        id: this.currentRecord.id,
        status: this.selectedStatus
      }
      updateSetStatus(param).then(() => {
        // 保留当前的查询条件
        const currentParams = {}
        if (this.actForm.clusterUuid) {
          currentParams.clusterUuid = this.actForm.clusterUuid
        }
        // 如果有搜索条件，也需要保留（这里假设你有保存搜索文本的变量）
        if (this.searchText) {
          currentParams.searchText = this.searchText
        }
        this.initPage(currentParams)
      })
      this.confirmModalVisible = false
    },

    refreshFilerStatus() {
      // 获取当前已加载的 Set ID 列表
      const setIdsParam = this.filteredSetIds.join(',')
      if (!setIdsParam) {
        this.$message.info('暂无可刷新的Set')
        return
      }
      listSetsFilerStatus({ setIds: setIdsParam })
        .then(response => {
          this.filersStatusData = response.Data.setStatus
          // 更新 SetData 中的 filersStatus
          this.SetData = this.SetData.map(set => {
            const setStatus = this.filersStatusData[set.id]?.status || {}
            const statusCounts = Object.entries(setStatus).map(([statusKey, count]) => ({
              status: this.statusMap[parseInt(statusKey)] || '未知状态',
              count: count
            }))
            return {
              ...set,
              statusCounts: statusCounts,
            }
          })
          this.$message.success('Filer状态刷新成功')
        })
        .catch(error => {
          console.error('Failed to fetch filer statuses:', error)
          this.$message.error('Filer状态刷新失败')
        })
    },

    backToClusterManage() {
      // 返回集群管理页面
      this.$router.push('/iseafs/cluster_manage')
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-inline>.ant-row {
  width: auto !important;
}

.ant-table {
  margin-top: 20px;
}
</style>
