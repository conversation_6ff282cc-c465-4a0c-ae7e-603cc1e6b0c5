<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-form layout="inline">
        <a-form-item>
          <a-input-search placeholder="集群名称" enter-button @search="onSearch" />
        </a-form-item>
        <a-form-item>
          <tx-button type="primary" @click="clusterHandler('{}', 'addRoot')">新增集群</tx-button>
        </a-form-item>
      </a-form>
      <a-table
        :pagination="pagination"
        :columns="columns"
        rowKey="idKey"
        :data-source="clusterData"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record: eachInfos }">
          <template v-if="column.dataIndex == 'action'">
            <tx-button type="primary" @click="clusterHandler(eachInfos, 'details')">
              详情
            </tx-button>
            <tx-button
              style="margin-left: 8px; background-color: #67c23a; color: aliceblue"
              @click="clusterHandler(eachInfos, 'update')"
            >
              更新
            </tx-button>
            <tx-button 
              style="margin-left: 8px; background-color: #1890ff; color: white"
              @click="navigateToSetManage(eachInfos)"
            >
              Set管理
            </tx-button>
            <tx-button 
              style="margin-left: 8px; background-color: #722ed1; color: white"
              @click="navigateToFilerManage(eachInfos)"
            >
              Filer管理
            </tx-button>
            <tx-button style="margin-left: 8px" type="danger" @click="clusterHandler(eachInfos, 'delete')">
              删除
            </tx-button>
          </template>
          <template v-else-if="column.dataIndex == 'monitor'">
            <a :href="getMonitorLink(eachInfos)" target="_blank" rel="noopener noreferrer">
              <EyeOutlined />
            </a>
          </template>
          <template v-else-if="column.dataIndex == 'consulToken'">
            <div>
              <span>{{ formatToken(eachInfos.consulToken) }}</span>
              <a-button type="link" @click="copyToClipboard(eachInfos.consulToken)">
                <CopyOutlined />
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>
    <a-modal :title="modalTitle" v-model:visible="modalVisible">
      <template #footer>
        <tx-button :disabled="modalTitle == '查看详情'" key="back" @click="handleCancel">取消</tx-button>
        <tx-button
          :disabled="modalTitle == '查看详情'"
          key="submit"
          type="primary"
          :loading="loading"
          @click="handleOk"
        >
          确定
        </tx-button>
      </template>
      <a-form-model
        v-if="modalVisible"
        ref="myForm"
        :model="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 12 }"
        :rules="rules"
      >
        <a-form-model-item label="集群名称" name="name">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.name" placeholder="集群名称必须唯一"/>
        </a-form-model-item>

        <a-form-model-item label="Consul地址" name="consulAddress">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.consulAddress" placeholder="consul域名或者域名端口"/>
        </a-form-model-item>

        <a-form-model-item label="Consul令牌" name="consulToken">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.consulToken" placeholder="consul颁发的token"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { createCluster, deleteCluster, getClusters, updateCluster } from '@/api/iseafs/clusterApi'
import { CopyOutlined } from '@ant-design/icons-vue'

const columns = [
  {
    title: '集群UUID',
    dataIndex: 'uuid',
    key: 'uuid',
  },
  {
    title: '集群名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Consul配置地址',
    dataIndex: 'consulAddress',
    key: 'consulAddress',
  },
  {
    title: 'Consul令牌',
    dataIndex: 'consulToken',
    key: 'consulToken',
    scopedSlots: { customRender: 'token' },
  },
  {
    title: 'Sets',
    dataIndex: 'sets',
    key: 'sets',
  },
  {
    title: '监控',
    dataIndex: 'monitor',
    key: 'monitor',
    scopedSlots: { customRender: 'monitor' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '30', '40'],
}

export default {
  name: 'ClusterManage',
  data() {
    return {
      clusterData: [],
      columns,
      pagination,
      modalTitle: '',
      modalVisible: false,
      loading: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入集群名称', trigger: 'blur' }],
      }
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    handleTableChange(pagination) {
      this.pagination.current = pagination.current
      this.initPage({
        pageSize: pagination.pageSize,
        pageNo: pagination.current,
      })
    },

    initPage(params = {}) {
      getClusters({
        pageSize: 10,
        pageNo: 1,
        ...params,
      }).then(res => {
        this.clusterData = res.Data.clusters.map(cluster => {
          return {
            ...cluster,
            // agents: cluster.agents.join(','), // 将 agents 数组以逗号分割拼接成字符串
          }
        })
        this.pagination.total = res.totalCount
      })
    },

    handleCancel() {
      this.modalVisible = false
    },

    // 弹框确定
    handleOk() {
      this.loading = true
      // somecode
      antdFormValidate(this.$refs.myForm, valid => {
        if (valid) {
          this.modalVisible = false
          // 校验成功后，根据弹框名称来确定要请求的接口
          switch (this.modalTitle) {
            case '新增集群':
              const obj = {
                name: this.form.name,
                consulAddress: this.form.consulAddress,
                consulToken: this.form.consulToken,
              }
              createCluster(obj)
                .then(() => {
                  this.$message.info('新增成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('新增失败')
                })
              break
            case '更新':
              const param = {
                uuid: this.form.uuid,
                name: this.form.name,
                consulAddress: this.form.consulAddress,
                consulToken: this.form.consulToken,
              }
              updateCluster(param)
                .then(() => {
                  this.$message.info('更新成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('更新失败')
                })
              break
          }
        } else {
          return false
        }
      })
      this.loading = false
    },

    clusterHandler(record, action) {
      this.form = { ...record }
      switch (action) {
        case 'addRoot':
          this.modalTitle = '新增集群'
          this.form = { name: '' }
          this.modalVisible = true

          break
        case 'details':
          this.modalTitle = '查看详情'
          this.modalVisible = true
          break

        case 'update':
          this.modalVisible = true
          this.modalTitle = '更新'
          break

        case 'delete':
          this.$confirm({
            title: '确认删除',
            content: `确定删除集群 ${record.name} 吗？`,
            onOk: () => {
              deleteCluster({ uuid: record.uuid }).then(this.initPage)
            }
          })
          break
      }
    },

    onSearch(value) {
      this.initPage({ searchText: value })
    },

    getMonitorLink(record) {
      // 根据 record 中的信息生成监控链接
      // 这里假设监控链接的格式为 `/monitor/${record.id}`
      return `/monitor/${record.id}`
    },
    copyToClipboard(text) {
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    this.$message.success('令牌已复制到剪贴板')
  },
  formatToken(token) {
    if (!token || token.length < 5) {
      return token
    }
    return token.slice(0, 2) + '******' + token.slice(-2)
  },
  navigateToSetManage(record) {
    // 跳转到Set管理页面，并传递集群UUID参数
    this.$router.push({
      path: '/iseafs/set_manage',
      query: {
        clusterUuid: record.uuid,
        clusterName: record.name
      }
    })
  },
  navigateToFilerManage(record) {
    // 跳转到Filer管理页面，并传递集群UUID参数
    this.$router.push({
      path: '/iseafs/filer_manage',
      query: {
        clusterUuid: record.uuid,
        clusterName: record.name
      }
    })
  }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-inline > .ant-row {
  width: auto !important;
}
.ant-table {
  margin-top: 20px;
}
</style>
