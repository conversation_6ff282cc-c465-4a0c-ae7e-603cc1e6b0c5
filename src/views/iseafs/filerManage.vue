<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-form layout="inline">
        <a-form-item label="集群名称">
          <a-select style="width: 180px" allowClear showSearch :options="clusterList"
            v-model:value="actForm.clusterUuid" placeholder="请选择集群"></a-select>
        </a-form-item>
        <a-form-item>
          <a-input-search placeholder="搜索Filer IP" enter-button @search="onSearch" />
        </a-form-item>
        <a-form-item>
          <tx-button type="primary" @click="SetHandler('{}', 'addRoot')">新增Filer</tx-button>
        </a-form-item>
        <a-form-item>
          <tx-button type="primary" shape="circle" icon="sync" :loading="isRefreshing" @click="HealthStatusHandler()" />
        </a-form-item>
        <!-- <a-form-item v-if="$route.query.clusterUuid">
          <tx-button @click="backToClusterManage">返回集群管理</tx-button>
        </a-form-item> -->
      </a-form>
      <a-table :pagination="pagination" :columns="columns" rowKey="idKey" :data-source="FilerData"
        @change="handleTableChange">
        <template #bodyCell="{ column, record: eachInfos }">
          <template v-if="column.dataIndex == 'action'">
            <tx-button style="margin-left: 8px; background-color: #67c23a; color: aliceblue"
              @click="SetHandler(eachInfos, 'update')">
              更新
            </tx-button>
            <tx-button style="margin-left: 8px; background-color: #1890ff; color: white"
              @click="toggleSettingDisplay(eachInfos)">
              配置
            </tx-button>
            <tx-button style="margin-left: 8px; background-color: #1890ff; color: white"
              @click="deleteFilerHandler(eachInfos)">
              删除
            </tx-button>
          </template>
          <template v-else-if="column.dataIndex == 'status'"> <!-- 添加自定义渲染 -->
            <a-select style="width: 120px" :value="eachInfos.status" @click="statusChangeHandler(eachInfos, $event)"
              @change="statusChangeHandler2(eachInfos, $event)" placeholder="改变状态">
              <a-select-option v-for="(statusValue, statusKey) in availableStatusOptions" :key="statusKey"
                :value="statusKey">
                {{ statusValue }}
              </a-select-option>
            </a-select>
          </template>
          <template v-else-if="column.dataIndex == 'healthStatus'">
            <a-badge :status="healthStatusTypeFilter(eachInfos.healthStatus)"
              :text="healthStatusFilter(eachInfos.healthStatus)" />
          </template>
          <template v-else-if="column.dataIndex == 'setting'">
            <div @click="toggleSettingDisplay(eachInfos)">
              <a-tooltip placement="top">
                <template #title>
                  <span>{{ eachInfos.setting }}</span>
                </template>
                <span>
                  {{
                    eachInfos.setting
                      .split('\n')
                      .slice(0, 2) // 取前两行
                      .join('\n')
                  }}
                  {{ eachInfos.setting.split('\n').length > 2 ? '...' : '' }} <!-- 如果超过两行，添加省略号 -->
                </span>
              </a-tooltip>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>
    <a-modal :title="modalTitle" v-model:visible="modalVisible">
      <template #footer>
        <tx-button :disabled="modalTitle == '查看详情'" key="back" @click="handleCancel">取消</tx-button>
        <tx-button :disabled="modalTitle == '查看详情'" key="submit" type="primary" :loading="loading" @click="handleOk">
          确定
        </tx-button>
      </template>
      <a-form-model v-if="modalVisible" ref="myForm" :model="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }"
        :rules="rules">
        <a-form-model-item v-if="modalTitle !== '新增Filer'" label="集群UUID" name="clusterUuid">
          <a-input :disabled="true" v-model:value="form.clusterUuid" />
        </a-form-model-item>

        <a-form-model-item v-if="modalTitle !== '新增Filer'" label="集群名称" name="clusterName">
          <a-input :disabled="true" v-model:value="form.clusterName" />
        </a-form-model-item>

        <a-form-model-item v-if="modalTitle == '新增Filer'" label="集群名称" name="clusterName">
          <a-select style="width: 180px" allowClear showSearch :options="clusterList"
            v-model:value="this.form.clusterUuid" placeholder="请选择集群"></a-select>
        </a-form-model-item>

        <a-form-model-item label="ip" name="ip">
          <a-input :disabled="modalTitle == '更新'" v-model:value="form.ip" />
        </a-form-model-item>

        <a-form-model-item label="port" name="port">
          <a-input :disabled="modalTitle == '更新'" v-model:value="form.port" />
        </a-form-model-item>

        <a-form-model-item label="S3Port" name="s3Port">
          <a-input :disabled="modalTitle == '更新'" v-model:value="form.s3Port" />
        </a-form-model-item>

        <a-form-model-item label="备注" name="comment">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.comment" />
        </a-form-model-item>

        <!-- <a-form-model-item label="附加配置" name="setting">
          <a-textarea :disabled="modalTitle == '查看详情'" v-model:value="form.setting"
            :auto-size="{ minRows: 3, maxRows: 10 }" placeholder="请输入 TOML 格式的配置" />
        </a-form-model-item> -->

      </a-form-model>
    </a-modal>
    <a-modal :title="confirmModalTitle" v-model:visible="confirmModalVisible">
      <template #footer>
        <tx-button key="back" @click="confirmModalVisible = false">取消</tx-button>
        <tx-button key="submit" type="primary" @click="confirmStatusChange">确定</tx-button>
      </template>
      <p>确定将Set由 {{ currentStatus }} 变为 {{ this.statusMap[selectedStatus] }} 吗？</p>
    </a-modal>
    <a-modal :title="statsModalTitle" v-model:visible="statsModalVisible" :width="1000">
      <template #footer>
        <tx-button key="back" @click="statsModalVisible = false">关闭</tx-button>
      </template>
      <div v-html="statsHtmlContent"></div>
    </a-modal>
    <a-modal :title="settingModalTitle" v-model:visible="settingModalVisible" :width="800">
      <template #footer>
        <tx-button type="primary" @click="copySetting" style="margin-left: 8px;">复制配置</tx-button>
        <tx-button @click="checkSetting"
          style="margin-left: 8px; background-color: #67c23a; color: aliceblue">核对线上配置</tx-button>
        <tx-button key="back" @click="settingModalVisible = false">关闭</tx-button>
      </template>
      <a-textarea :value="selectedSetting" :disabled="false" :auto-size="{ minRows: 5, maxRows: 30 }"
        style="background-color: #f0f0f0; padding: 8px; border-radius: 4px; margin: 0;" />
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { getFilers, createFiler, updateFiler, updateFilerStatus, getFilerSetting, checkFilerOnlineSetting, deleteFiler, batchSyncFilerHealthStatus } from '@/api/iseafs/filerApi'
import { getClusterNames } from '@/api/iseafs/clusterApi'
// import setting from '@/locales/lang/en-US/setting'

const columns = [
  {
    title: 'Filer ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'ip',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: 'port',
    dataIndex: 'port',
    key: 'port',
  },
  {
    title: 's3Port',
    dataIndex: 's3Port',
    key: 's3Port',
  },
  {
    title: '发布状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '健康状态',
    dataIndex: 'healthStatus',
    key: 'healthStatus',
    scopedSlots: { customRender: 'healthStatus' },
  },
  {
    title: '备注',
    dataIndex: 'comment',
    key: 'comment',
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '30', '40'],
}

const scopeMap = {
  unknown: {
    status: 'error',
    text: '未知',
  },
  '': {
    status: 'error',
    text: '未知',
  },
  critical: {
    status: 'error',
    text: 'critical',
  },
  passing: {
    status: 'success',
    text: 'Passing',
  },
  warning: {
    status: 'warning',
    text: 'Warning',
  },
}

export default {
  name: 'FilerManage',

  props: {
    // 需要隐藏的item
    hiddenItem: {
      type: Array,
      default: () => {
        return []
      },
    },
    // clusterGroups: {
    //   type: Array,
    //   default: () => {
    //     return []
    //   },
    // },
  },
  data() {
    return {
      isRefreshing: false, // 控制刷新按钮 loading 状态
      FilerData: [],
      columns,
      filteredSetIds: [],
      pagination,
      modalTitle: '',
      modalVisible: false,
      loading: false,
      actForm: {
        clusterUuid: '',
      },
      form: {
        port: '8888',  // 设置默认值
        s3Port: '8333', // 设置默认值
      },
      rules: {
        ip: [{ required: true, message: '请输入ip', trigger: 'blur' }],
        port: [{ required: true, message: '请输入port', trigger: 'blur' }],
        s3Port: [{ required: true, message: '请输入S3Port', trigger: 'blur' }],
      },
      statusMap: {
        0: '未发布',
        1: '在线',
        2: '离线',
        3: '异常',
      },
      clusterList: [],
      clusterGroups: [],
      selectedStatus: 0,
      confirmModalVisible: false,
      confirmModalTitle: '',
      currentStatus: '',
      currentRecord: {},
      availableStatusOptions: {},
      statsModalVisible: false,
      statsModalTitle: '',
      statsHtmlContent: '',
      filersStatusData: {}, // 新增 filersStatusData 字段
      selectedSetting: '', // 新增 selectedSetting 字段
      settingModalVisible: false, // 新增 settingModalVisible 字段
    }
  },
  mounted() {
    this.initCluster().then(() => {
      // 检查路由参数中是否有集群UUID
      const routeClusterUuid = this.$route.query.clusterUuid
      if (routeClusterUuid) {
        // 如果路由参数中有集群UUID，使用它
        this.actForm.clusterUuid = routeClusterUuid
        // 显示来源集群的提示信息
        const clusterName = this.$route.query.clusterName || '未知集群'
        this.$message.success(`已自动选择集群：${clusterName}`)
      }
      this.initPage({ clusterUuid: this.actForm.clusterUuid })
    })
  },
  methods: {
    HealthStatusHandler() {
    this.isRefreshing = true;

    // 获取当前页所有 Filer ID
    const ids = this.FilerData.map(filer => filer.id);

    // 发起 GET 请求，使用 ids 参数
    batchSyncFilerHealthStatus({ 
        ids: ids
    })
  .then((res) => {
    if (res.Code === 200 && res.Data && Array.isArray(res.Data.filerHealthStatus)) {
      res.Data.filerHealthStatus.forEach(item => {
        // 找到本地数据中对应id的那一项
        const row = this.FilerData.find(row => row.id === item.id)
        if (row) {
          // 只更新healthStatus字段
          row.healthStatus = item.healthStatus
        }
      })
      this.$message.success('健康状态刷新成功')
    }
  })
      .catch(error => {
        console.error('刷新健康状态失败:', error);
        this.$message.error('刷新健康状态失败，请稍后重试');
      })
      .finally(() => {
        this.isRefreshing = false;
      });
  },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current
      this.initPage({
        pageSize: pagination.pageSize,
        pageNo: pagination.current,
      })
    },

    initCluster() {
      return new Promise((resolve) => {
        getClusterNames().then(res => {
          this.clusterGroups = res.Data.clusters
          this.clusterList = this.clusterGroups.map(item => ({
            value: item.uuid,
            label: item.name,
          }))
          // 设置默认选中的集群
          if (this.clusterList.length > 0) {
            this.actForm.clusterUuid = this.clusterList[0].value
            console.log("sdfsds")
            console.log(this.actForm.clusterUuid)
          }
          resolve();
        })
      })
    },

    initPage(params = {}) {
      if (!this.actForm.clusterUuid || !params.clusterUuid) {
        this.initCluster().then(() => {
          if (this.clusterList.length > 0) {
            params.clusterUuid = this.actForm.clusterUuid;
          }
          this.fetchFilers(params);
        });
      } else {
        this.fetchFilers(params);
      }
    },

    fetchFilers(params) {
  getFilers({
    pageSize: 10,
    pageNo: 1,
    ...params,
  }).then(res => {
    // 检查 res.Data.filers 是否为 null 或 undefined
    if (res.Data.filers === null || res.Data.filers === undefined) {
      this.FilerData = [];
    } else {
      this.FilerData = res.Data.filers.map(filer => {
        return {
          ...filer,
          status: this.statusMap[filer.status] || '未知状态', // 转换状态
          statusKey: filer.status, // 添加 statusKey 字段
          showFullSetting: false,
        }
      })
    }
    this.pagination.total = res.totalCount || 0; // 确保 totalCount 不为 undefined
  }).catch(error => {
    console.error('获取 Filer 数据失败:', error);
    this.$message.error('获取 Filer 数据失败，请稍后重试');
  });
},

    handleCancel() {
      this.modalVisible = false
    },

    handleOk() {
      this.loading = true
      // somecode
      antdFormValidate(this.$refs.myForm, valid => {
        if (valid) {
          this.modalVisible = false
          // 校验成功后，根据弹框名称来确定要请求的接口
          switch (this.modalTitle) {
            case '新增Filer':
              const obj = {
                clusterUuid: this.form.clusterUuid,
                ip: this.form.ip,
                port: this.form.port,
                s3Port: this.form.s3Port,
                comment: this.form.comment,
              }
              createFiler(obj)
                .then(() => {
                  this.$message.info('新增成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('新增失败')
                })
              break
            case '更新':
              const param = {
                id: this.form.id,
                ip: this.form.ip,
                port: this.form.port,
                s3Port: this.form.s3Port,
                comment: this.form.comment,
              }
              updateFiler(param)
                .then(() => {
                  this.$message.info('更新成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('更新失败')
                })
              break
          }
        } else {
          return false
        }
      })
      this.loading = false
    },

    SetHandler(record, action) {
      this.modalVisible = true
      this.form = { ...record }

      switch (action) {
        case 'addRoot':
          this.modalTitle = '新增Filer'
          this.form = {
            clusterUuid: this.actForm.clusterUuid,
            port: '8888',
            s3Port: '8333',
          }
          break

        case 'details':
          this.modalTitle = '查看详情'
          // 根据 clusterUuid 找到对应的 clusterName
          const clusterForDetails = this.clusterGroups.find(cluster => cluster.uuid === this.form.clusterUuid)
          if (clusterForDetails) {
            this.form.clusterName = clusterForDetails.name
          }
          // 将 masters 从以 | 分隔的字符串转换为多行文本
          this.form.masters = this.form.masters ? this.form.masters.split(',').join('\n') : ''
          break

        case 'update':
          this.modalTitle = '更新'
          // 根据 clusterUuid 找到对应的 clusterName
          const clusterForUpdate = this.clusterGroups.find(cluster => cluster.uuid === this.form.clusterUuid)
          if (clusterForUpdate) {
            this.form.clusterName = clusterForUpdate.name
          }
          // 将 masters 从以 | 分隔的字符串转换为多行文本
          this.form.masters = this.form.masters ? this.form.masters.split(',').join('\n') : ''
          break

        // case 'updateStatus':
        //   this.modalTitle = '更新状态'
        //   this.$confirm({
        //     title: '更新状态',
        //     content: `确定更新Set状态由 ${record.status} 变为 ${this.statusMap[this.selectedStatus]}？`,
        //     onOk: () => {
        //       const param = {
        //         id: record.id,
        //         status: this.selectedStatus
        //       }
        //       updateSetStatus(param).then(this.initPage)
        //     }
        //   })
        //   break

        case 'delete':
          this.$confirm({
            title: '确认删除',
            content: `确定删除Set ${record.name} 吗？`,
            onOk: () => {
              deleteSet({ id: record.id }).then(this.initPage)
            }
          })
          break
      }
    },

    onSearch(value) {
      const selectedClusterUuid = this.actForm.clusterUuid
      this.initPage({ searchText: value, clusterUuid: selectedClusterUuid })
    },

    //     filteredSetIdsForFilerStatus() {
    //   this.filteredSetIds = this.SetData
    //     .filter(set => set.statusKey === 1 || set.statusKey === 2)
    //     .map(set => set.id)
    //   console.log(this.filteredSetIds) // 或者传递给其他方法
    // }

    statusChangeHandler(record) {
      const currentStatusKey = Object.keys(this.statusMap).find(key => this.statusMap[key] === record.status)
      this.currentStatus = record.status
      this.currentRecord = record

      // 根据当前状态生成可用的状态选项
      this.availableStatusOptions = {}
      switch (currentStatusKey) {
        case '0': // 未发布
          this.availableStatusOptions = {
            1: '发布',
          }
          break
        case '1': // 在线
          this.availableStatusOptions = {
            2: '离线',
          }
          break
        case '2': // 离线
          this.availableStatusOptions = {
            1: '在线',
          }
          break
        case '3': // 异常
          this.availableStatusOptions = {}
          break
        default:
          this.availableStatusOptions = {}
          break
      }

      // console.log(this.availableStatusOptions, 'this.availableStatusOptions')

      // // 显示确认对话框
      // this.confirmModalVisible = true
      // this.confirmModalTitle = '确定更新状态'
    },

    statusChangeHandler2(record, selectedStatusKey) {
      const currentStatusKey = Object.keys(this.statusMap).find(key => this.statusMap[key] === record.status)
      this.currentStatus = record.status
      this.currentRecord = record
      this.selectedStatus = selectedStatusKey
      if (this.selectedStatus === currentStatusKey) {
        this.confirmModalVisible = false
      } else if (this.selectedStatus !== currentStatusKey) {
        this.confirmModalVisible = true
        this.confirmModalTitle = '确定更新状态'
      }
    },

    confirmStatusChange() {
      const param = {
        id: this.currentRecord.id,
        status: this.selectedStatus
      }
      updateFilerStatus(param).then(this.initPage)
      this.confirmModalVisible = false
    },

    toggleSettingDisplay(record) {
      getFilerSetting({ id: record.id })
        .then(response => {
          // 去除每行开头和结尾的空格
          // const lines = response.Data.setting.split('\n').map(line => line.trim());
          // this.selectedSetting = lines.join('\n');
          this.selectedSetting = response.Data.setting;
          this.settingModalTitle = `配置 - Filer ID: ${record.id}`;
          this.settingModalVisible = true;
          this.currentRecord = record;
        })
        .catch(error => {
          console.error('获取配置数据失败:', error);
          this.$message.error('获取配置数据失败，请稍后重试');
        });
    },
    healthStatusFilter(type) {
      return scopeMap[type]?.text || type
    },
    healthStatusTypeFilter(type) {
      return scopeMap[type]?.status || type
    },
    copySetting() {
      // 创建一个临时的 textarea 元素
      const tempTextarea = document.createElement('textarea');
      tempTextarea.value = this.selectedSetting;
      document.body.appendChild(tempTextarea);

      // 选中 textarea 中的内容
      tempTextarea.select();
      tempTextarea.setSelectionRange(0, 99999); // For mobile devices

      // 执行复制操作
      try {
        document.execCommand('copy');
        this.$message.success('配置已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }

      // 移除临时的 textarea 元素
      document.body.removeChild(tempTextarea);
    },

    checkSetting() {
      console.log('checkSetting');
      const param = {
        id: this.currentRecord.id, // 使用传入的 record.id
      };

      checkFilerOnlineSetting(param)
        .then(response => {
          // 处理成功的情况
          console.log('Online setting check response:', response);
          // 可以根据 response 数据进行进一步处理，例如显示检查结果
        })
        .catch(error => {
          // 处理失败的情况
          console.error('Error checking online setting:', error);
          this.$message.error('核对线上配置失败，请稍后重试');
        });
    },

    deleteFilerHandler(record) {
        // 检查 Filer 的状态是否为"未发布"（状态码 0）或"离线"（状态码 2）
  if (record.statusKey !== 0 && record.statusKey !== 2) {
    this.$message.warning('只有状态为"未发布"或"离线"的 Filer 才能被删除');
    return;
  }
  this.$confirm({
    title: '确认删除',
    content: `确定删除Filer ${record.id} 吗？`,
    onOk: () => {
      deleteFiler({ id: record.id })
        .then(() => {
          this.$message.info('删除成功');
          this.initPage();
        })
        .catch(() => {
          this.$message.error('删除失败');
        });
    }
  });
},

    backToClusterManage() {
      // 返回集群管理页面
      this.$router.push('/iseafs/cluster_manage')
    },
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-inline>.ant-row {
  width: auto !important;
}

.ant-table {
  margin-top: 20px;
}
</style>
