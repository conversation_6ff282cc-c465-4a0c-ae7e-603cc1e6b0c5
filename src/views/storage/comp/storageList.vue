<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="存储桶名称">
              <a-input
                v-model:value="queryParam.name"
                placeholder="支持模糊搜索"
                @pressEnter="$refs.table.refresh()"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="区域">
              <a-input
                v-model:value="queryParam.region"
                placeholder="支持模糊搜索"
                @pressEnter="$refs.table.refresh()"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions v-if="!record.supplier.startsWith('阿里云')">
          <a-descriptions-item label="ID">{{ record.id }}</a-descriptions-item>
          <!--          <a-descriptions-item label="负责人">{{ record.principal }}</a-descriptions-item>-->
          <!--          <a-descriptions-item label="使用人邮箱" :span="3">{{ record.userEmails }}</a-descriptions-item>-->
          <a-descriptions-item label="业务">{{ record.business }}</a-descriptions-item>
          <a-descriptions-item label="费用负责人">{{ record.costUser }}</a-descriptions-item>
          <a-descriptions-item label="事业部">{{ record.secondaryOrganization }}</a-descriptions-item>
          <a-descriptions-item label="部门">{{ record.tertiaryOrganization }}</a-descriptions-item>
          <a-descriptions-item label="团队">{{ record.team }}</a-descriptions-item>
          <!--          <a-descriptions-item label="应用">{{ record.application }}</a-descriptions-item>-->
          <a-descriptions-item label="环境">{{ record.env }}</a-descriptions-item>
          <a-descriptions-item label="上月费用">{{ record.costLastMonth }}</a-descriptions-item>
          <a-descriptions-item label="上上月费用">{{ record.costMonthAfterLast }}</a-descriptions-item>
          <a-descriptions-item label="项目">{{ record.project }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions v-else>
          <a-descriptions-item>阿里云暂不支持</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <a @click="handleMonitor(record)">监控</a>
          <!--          <a-divider type="vertical" />-->
          <!--          <a @click="update(record)">更新</a>-->
        </template>
      </template>
    </s-table>
    <a-drawer
      :title="`${monitorData.name} 监控数据`"
      placement="right"
      :closable="false"
      width="40%"
      :visible="monitorVisible"
      @close="monitorVisible = false"
    >
      <a-card :bordered="false" :model="monitorData" ref="monitorData">
        <a-descriptions title="详细指标" />
        <a-range-picker v-model:value="monitorData.rangeTime" show-time :ranges="timeRanges"></a-range-picker>
        <a-divider type="vertical" />
        <a-button type="primary" @click="submitQuery">查询</a-button>
        <a-tabs v-model:activeKey="monitorActiveKey" @change="handleTabChange">-->
          <a-tab-pane tab="存储" key="storage"></a-tab-pane>
          <a-tab-pane tab="请求数" key="request"></a-tab-pane>
        </a-tabs>
        <div :key="renderKey" v-if="renderDiv && monitorData !== undefined && monitorData.data !== null"
             v-for="trend in  monitorData.data">
          <a-card style="height: 300px; margin-bottom: 10px">
            <a-skeleton v-if="loading " />
            <storageMonitorLine v-else :height="275" :compRef="trend.name" :type="monitorActiveKey"
                                :chartData="trend.data" />
          </a-card>
        </div>
      </a-card>
    </a-drawer>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { storageConsoleData, listBucketStatistic } from '@/api/storage/storage_console'
import StorageMonitorLine from './storageMonitor.vue'
import dayjs from 'dayjs'

const columns = [
  {
    title: '存储桶名称',
    dataIndex: 'name',
    sorter: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    sorter: true
  },
  {
    title: '区域',
    dataIndex: 'region',
    sorter: true
  },
  {
    title: '存储用量',
    dataIndex: 'used',
    sorter: true
  },
  {
    title: '读请求数',
    dataIndex: 'read',
    sorter: true
  },
  {
    title: '写请求数',
    dataIndex: 'write',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center'
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'StorageList',
  components: {
    STable,
    Ellipsis,
    StorageMonitorLine
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    const dateFormat = 'YYYY-MM-DD HH:mm:ss'
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      form: {
        email: undefined,
        group: undefined
      },
      updateVisible: false,
      confirmLoading: false,
      monitorVisible: false,
      monitorData: {
        data: []
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return storageConsoleData(requestParameters).then(res => {
          console.log('res', res)
          if (res.hasOwnProperty('data')) {
            if (res.data === undefined || res.data === null || res.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      timeRanges: {
        近7天: [dayjs().subtract(7, 'day'), dayjs(dayjs(), dateFormat)],
        近14天: [dayjs().subtract(14, 'day'), dayjs(dayjs(), dateFormat)],
        近30天: [dayjs().subtract(30, 'day'), dayjs(dayjs(), dateFormat)]
      },
      monitorActiveKey: 'storage',
      loading: false,
      renderDiv: true,
      renderKey: 0
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed () {
    removeWatermark()
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    // 详细信息 相关接口
    handleMonitor (record) {
      this.monitorVisible = true
      this.monitorData.name = record.name
      this.monitorData.region = record.region
      this.monitorData.supplier = record.supplier
      this.monitorData.rangeTime = [dayjs().subtract(14, 'day'), dayjs(dayjs(), 'YYYY-MM-DD HH:mm:ss')]
      this.handleTabChange()
    },
    handleTabChange () {
      console.log(this.monitorActiveKey)
      this.loading = true
      switch (this.monitorActiveKey) {
        case 'storage':
          this.monitorData.type = [1]
          break
        case 'request':
          this.monitorData.type = [2, 3]
          break
      }
      this.submitQuery()
    },
    submitQuery () {
      this.loading = true
      let req = {
        name: this.monitorData.name,
        region: this.monitorData.region,
        supplier: this.monitorData.supplier,
        type: this.monitorData.type
      }
      delete (req['rangeTime'])
      if (this.monitorData.rangeTime !== undefined && this.monitorData.rangeTime !== null && this.monitorData.rangeTime.length > 0) {
        req.beginTime = dayjs(this.monitorData.rangeTime[0]).format('YYYY-MM-DD HH:mm:ss')
        req.endTime = dayjs(this.monitorData.rangeTime[1]).format('YYYY-MM-DD HH:mm:ss')
      }
      listBucketStatistic(req).then(response => {
        this.monitorData.data = response.data
        this.triggerRender()
      }).catch(
        this.loading = false
      )
    },
    triggerRender () {
      this.renderDiv = false
      this.$nextTick(() => {
        this.renderKey++
        this.renderDiv = true
      })
    }
  }
}
</script>

<style scoped>
.hiddenIcon {
  display: none;
}
</style>
