<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-10-10 18:18:38
 * @FilePath: \cloud_web\src\views\cost\comp\charts\companyTrendChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 320px" v-if="!chartData.length" />
    <div v-else :ref="compRef" :id="compRef"></div>
  </div>
</template>
<script>
import {  Line } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    compRef: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  watch: {},
  async mounted () {
    console.log('[compRef]', this.compRef)
    if (this.chartData.length) {
      const line = new Line(this.compRef, {
        data: this.chartData,
        xField: 'date',
        yField: 'number',
        seriesField: 'name',
        height: 275,
        width: this.$refs[this.compRef].clientWidth,
        animate: false,
        legend: {
          position: 'bottom',
          itemHeight: 20
          // offsetY:10
        },
        // padding: [10, 50, 30, 100],
        tooltip: {
          showTitle: false,
          fields: ['name', 'number'],
          formatter: datum => {
            return {
              name: datum.name,
              value: this.type === 'storage' ? `用量：${datum.number.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}G` : this.type === 'request' ? `次数：${datum.number.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}` : `数值：${datum.number.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}`
            }
          }
        },
        yAxis: {
          label: {
            formatter: val => `${String(val).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
          }
        }
      })
      line.render()
    }
  },
  data () {
    return {
      chart: null
    }
  },
  methods: {
    createChart () {
    }
  }
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
