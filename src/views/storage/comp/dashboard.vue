<template>
  <a-row>
    <a-col :span="6">
      <a-card size="small" title="seaweedfs-smb(宝信)" style="margin-right: 10px">
        <dashboard-pie :pieId="'seaweedfs-smb'" :freeData="seaweedfsData.smbFree" :usedData="seaweedfsData.smbUsed" />
      </a-card>
    </a-col>
    <a-col :span="6">
      <a-card size="small" title="seaweedfs-hdd-songjiang(松江)" style="margin-right: 10px">
        <dashboard-pie
          :pieId="'seaweedfs-hdd-songjiang'"
          :freeData="seaweedfsData.hddSjFree"
          :usedData="seaweedfsData.hddSjUsed"
        />
      </a-card>
    </a-col>
    <a-col :span="6">
      <a-card size="small" title="seaweedfs-bigdata-zhuanqiao(颛桥)" style="margin-right: 10px">
        <dashboard-pie
          :pieId="'seaweedfs-bigdata-zhuanqiao-zq'"
          :freeData="seaweedfsData.zqBigdataZqFree"
          :usedData="seaweedfsData.zqBigdataZqUsed"
        />
      </a-card>
    </a-col>
    <a-col :span="6">
      <a-card size="small" title="seaweedfs-bigdata-zhuanqiao(松江)" style="margin-right: 10px">
        <dashboard-pie
          :pieId="'seaweedfs-bigdata-zhuanqiao-sj'"
          :freeData="seaweedfsData.sjBigdataZqFree"
          :usedData="seaweedfsData.sjBigdataZqUsed"
        />
      </a-card>
    </a-col>
  </a-row>
<!--  <a-card title="供应商存储用量概览" style="margin-top: 20px">-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="腾讯云(qixinbao)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="腾讯云(intsig)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="腾讯云(camscanner)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="腾讯云(火种项目)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="AWS(海外)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="AWS临冠(宁夏)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="AWS合合(北京)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="AWS合合(宁夏)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="阿里云(启信宝)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="阿里云(合合)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="UCloud(合合)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--    <a-card-grid style="width: 25%; text-align: center">-->
<!--      <a-card size="small" title="UCloud(生腾)存储用量" :bordered="false">-->
<!--        <h1 style="margin-top: 20px; text-align: center">-->
<!--          1.67-->
<!--          <span style="font-size: 14px">TB</span>-->
<!--        </h1>-->
<!--      </a-card>-->
<!--    </a-card-grid>-->
<!--  </a-card>-->
</template>

<script>
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import DashboardPie from './dashboardPie.vue'
import { getSeaweedfs } from '@/api/monitor/common'
export default {
  name: 'StorageStorageDashboard',
  components: {
    DashboardPie,
  },
  data() {
    return {
      seaweedfsData: {
        smbFree: 0,
        smbUsed: 0,
        hddSjFree: 0,
        hddSjUsed: 0,
        sjBigdataZqFree: 0,
        sjBigdataZqUsed: 0,
        zqBigdataZqFree: 0,
        zqBigdataZqUsed: 0,
      },
    }
  },
  created() {
    this.seaweedfsInfo()
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    seaweedfsInfo() {
      getSeaweedfs().then(res => {
        this.seaweedfsData = res.Data
      })
    },
  },
}
</script>
<style scoped lang="less"></style>
