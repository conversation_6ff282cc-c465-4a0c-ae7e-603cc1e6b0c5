<template>
  <div class="chartContainer">
    <div :id="pieId"></div>
  </div>
</template>

<script>
import { Pie, measureTextWidth } from '@antv/g2plot'
export default {
  props: {
    pieId: String,
    freeData: Number,
    usedData: Number,
  },
  watch: {
    freeData: {
      handler: function (val) {
        if (val) {
          this.renderPie(val, this.usedData)
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    renderPie(freeData, usedData) {
      function renderStatistic(containerWidth, text, style) {
        const textWidth = measureTextWidth(text, style)
        const textHeight = style.lineHeight || style.fontSize
        const R = containerWidth / 2
        // r^2 = (w / 2)^2 + (h - offsetY)^2
        let scale = 1
        if (containerWidth < textWidth) {
          scale = Math.min(
            Math.sqrt(Math.abs(Math.pow(R, 2) / (Math.pow(textWidth / 2, 2) + Math.pow(textHeight, 2)))),
            1
          )
        }
        const textStyleStr = `width:${containerWidth}px;`
        return `<div style="${textStyleStr};font-size:${scale}em;line-height:${
          scale < 1 ? 1 : 'inherit'
        };">${text}</div>`
      }

      const data = [
        { type: '使用容量', value: usedData },
        { type: '剩余容量', value: freeData },
      ]

      const piePlot = new Pie(this.pieId, {
        appendPadding: 10,
        data,
        height: 300,
        angleField: 'value',
        colorField: 'type',
        radius: 0.9,
        innerRadius: 0.6,
        meta: {
          value: {
            formatter: v => `${v} ¥`,
          },
        },
        label: {
          type: 'inner',
          offset: '-50%',
          style: {
            textAlign: 'center',
          },
          autoRotate: false,
          content: '{value}',
        },
        statistic: {
          title: {
            offsetY: -4,
            customHtml: (container, view, datum) => {
              const { width, height } = container.getBoundingClientRect()
              const d = Math.sqrt(Math.pow(width / 2, 2) + Math.pow(height / 2, 2))
              const text = datum ? datum.type : '总容量'
              return renderStatistic(d, text, { fontSize: 16 })
            },
          },
          content: {
            offsetY: 4,
            style: {
              fontSize: '22px',
            },
            customHtml: (container, view, datum, data) => {
              const { width } = container.getBoundingClientRect()

              const text = datum ? `${datum.value} TB` : `${data.reduce((r, d) => r + d.value, 0)} TB`
              return renderStatistic(width, text, { fontSize: 24 })
            },
          },
        },
        interactions: [{ type: 'element-selected' }, { type: 'element-active' }, { type: 'pie-statistic-active' }],
      })
      piePlot.render()
    },
  },
}
</script>

<style scoped lang="less">
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
