<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-09-16 11:24:26
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-09-16 11:26:09
 * @FilePath: \cloud_web\src\views\storage\comp\storageModal.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <a-modal
    title="新建"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
  >
    <a-spin :spinning="loading">
      <a-form :model="form" v-bind="formLayout" ref="formRef" :rules="rules">
        <!-- <a-form-item label="数据库名">
          <a-input v-model:value="form.dbName"/>
        </a-form-item> -->
        <a-form-item label="密码">
          <a-input-password v-model:value="form.password"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import { getMysqlPersonalDbNameList } from '@/api/db/mysql_personal'
  // 表单字段
  const fields = ['dbName', 'password']
  export default {
    props: {
      visible: {
        type: Boolean,
        required: true
      },
      loading: {
        type: Boolean,
        default: () => false
      },
      model: {
        type: Object,
        default: () => null
      }
    },
    data () {
      this.formLayout = {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 13 }
        }
      }
      return {
        dbNameList: [],
        form: this.$form.createForm(this, 'formRef'),
        rules: {
          // dbName: [{ required: true, min: 2, message: '请输入至少两个字符！' },
          //   { validator: (...args) => this.compareToFirstPassword(...args) }],
          password: [{ required: true, min: 8, message: '请输入至少八个字符！' }],
        },
      }
    },
    watch: {
      model: {
        handler() {
          this.model && this.form.setFieldsValue(pick(this.model, fields))
        },
      },
    },
    created () {
      console.log('custom modal created')
      // 防止表单未注册
      // fields.forEach(v => this.form.getFieldDecorator(v))
      // 当 model 发生改变时，为表单设置值
      /*
      this.$watch('model', () => {
        this.model && this.form.setFieldsValue(pick(this.model, fields))
      })
      */
      getMysqlPersonalDbNameList().then((res) => {
        this.dbNameList = res.Data.dbName
      })
    },
    methods: {
      compareToFirstPassword (rule, value, callback) {
        callback = antdFormValidateCallback
        if (value && this.dbNameList.includes(value)) {
          // eslint-disable-next-line standard/no-callback-literal
          return callback('数据库名重复，请填写其他数据库名称!')
        } else {
          return callback()
        }
      }
    }
  }
</script>
