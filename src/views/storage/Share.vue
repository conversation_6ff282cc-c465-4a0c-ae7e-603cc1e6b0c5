<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.searchName"
                  placeholder="名称/负责人姓名/负责人邮箱"
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
            <a-col :md="8" :sm="24" class="text-right">
              <tx-button>
                <router-link to="/workflow/share-storage-create">新增</router-link>
              </tx-button>
              <tx-button style="margin-left: 8px">
                <router-link to="/workflow/share-storage-add">加入已有</router-link>
              </tx-button>
            </a-col>
          </a-row>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record}">
          <template v-if="column.dataIndex == 'action'">
            <!-- <a v-if="record.isOwner" @click="handleUpdate(record)">更新</a> -->
            <a-dropdown v-if="record.isOwner">
              <a class="ant-dropdown-link" @click="e => e.preventDefault()">
                更新
                <a-icon type="down" />
              </a>
              <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a href="javascript:;" @click="handleUpdate(record, 'share')">更新共享人</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="handleUpdate(record, 'head')">更新负责人</a>
                </a-menu-item>
              </a-menu>
              </template>
            </a-dropdown>
          &nbsp;
          <router-link v-if="record.isMember" :to="{ path: '/storage/osp-share-opt', query: { name: record.name } }">文件管理</router-link>
            <a-divider type="vertical" v-if="userRoles.includes('opsAdmin')" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a v-if="userRoles.includes('opsAdmin')">删除</a>
            </a-popconfirm>
          </template>
        <template v-else-if="column.dataIndex == 'users'">
          <a-tooltip>
            <template #title>{{ record.users }}</template>
            <span class="usersTooltip">{{ record.users }}</span>
          </a-tooltip>
        </template>
        </template>
      </s-table>
      <a-modal
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
        :visible="updateVisible"
        :title="modalTitle"
        :maskClosable="false"
      >
        <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item v-if="modalTitle === '更新负责人'" label="负责人" name="principal">
            <!-- <a-input v-model:value="form.principal" /> -->
            <OrgUser :checkValue="form.principal" :multiple="false" @selectChange="selectChange" />
          </a-form-model-item>
          <a-form-model-item v-if="modalTitle === '更新共享人'" label="共享使用人" name="users">
            <!-- <a-input v-model:value="form.users" /> -->
            <OrgUser :checkValue="form.users" :multiple="true" @selectChange="selectChangeUsers" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import {
  ospShare,
  getOspShareInfo,
  updateOspShare,
  deleteOspShare,
  getPrincipal,
  getShareUser,
  modifyPrincipal,
  modifyUser,
  delStorage,
} from '@/api/storage/osp'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { getUserList } from '@/api/permission/user'
import OrgUser from '@/views/comp/allOrgUser/index.vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: '负责人',
    dataIndex: 'principal',
    sorter: true,
  },
  {
    title: '共享使用人列表',
    dataIndex: 'users',
    scopedSlots: { customRender: 'users' },
    width: '150px',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'comment',
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '250px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'OspShare',
  components: {
    STable,
    Ellipsis,
    OrgUser,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      modalTitle: '',
      userRoles: [],
      labelCol: { span: 8 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      form: {
        name: undefined,
        principal: undefined,
        principalEmail: undefined,
        users: [],
        userEmails: undefined,
        comment: undefined,
      },
      currentRow: null,
      updateVisible: false,
      confirmLoading: false,
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return ospShare(requestParameters).then(res => {
          if (res.data === undefined || res.data === null || res.data.length <= 0) {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          } else {
            return res
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    // 用户角色权限隔离
    getUserList({ searchText: store.getters.email }).then(response => {
      if (response.Data.data[0] && response.Data.data[0].roles) {
        this.userRoles = response.Data.data[0].roles
      } else {
        this.userRoles = []
      }
    })
  },
  unmounted() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleUpdate(record, type) {
      this.currentRow = record
      switch (type) {
        case 'share':
          getShareUser(record.id)
            .then(res => {
              this.form.users = res.data.map(item => {
                return JSON.stringify(item)
              })
            })
            .finally(() => {
              this.modalTitle = '更新共享人'
              this.updateVisible = true
            })
          break
        case 'head':
          getPrincipal(record.id)
            .then(res => {
              this.form.principal = JSON.stringify(res.data)
            })
            .catch(() => {
            })
            .finally(() => {
              this.updateVisible = true
              this.modalTitle = '更新负责人'
            })
          break
      }
    },
    selectChange(arg) {
      this.form.principal = arg
    },
    selectChangeUsers(arg) {
      this.form.users = arg
    },

    handleUpdateOk(e) {
      this.confirmLoading = true

      switch (this.modalTitle) {
        case '更新负责人':
          //somecode
          let obj = JSON.parse(this.form.principal)
          let params = {
            id: Number(this.currentRow.id),
            principal: {
              email: obj.email,
              name: obj.name,
              uid: String(obj.uid),
            },
          }
          modifyPrincipal(params)
            .then(() => {
              this.$message.success('修改成功')
              this.updateVisible = false
              // this.loadData()
              this.$refs.table.refresh()
            })
            .catch(() => {
              this.$message.error('修改失败')
            })
            .finally(() => {
              this.confirmLoading = false
            })
          break
        case '更新共享人':
          let arr = this.form.users.map(item => {
            let utem = JSON.parse(item)
            utem.uid = String(utem.uid)
            return utem
          })
          let userparams = {
            id: Number(this.currentRow.id),
            users: arr,
          }
          modifyUser(userparams)
            .then(res => {
              this.$message.success('修改成功')
              this.updateVisible = false
              // this.loadData()
              this.$refs.table.refresh()
            })
            .catch(() => {
              this.$message.error('修改失败')
            })
            .finally(() => {
              this.confirmLoading = false
            })

          break
      }
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      delStorage(record.id).then(() => {
        // if (res.message === 'ok') {
        //   this.$refs.table.refresh(true)
        //   this.$message.success('删除成功')
        // } else {
        //   this.$message.error('删除失败')
        // }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
  },
}
</script>
<style lang="less" scoped>
.usersTooltip {
  display: inline-block;
  width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
</style>
