<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <!-- <div class="table-operator">
        <tx-button type="primary" icon="check-circle" v-if="!currentBucket" @click="bookServe">业务开通</tx-button>
      </div> -->
      <!-- <AllUser /> -->
      <div>
        <div class="table-operator">
          <a-upload
            :show-upload-list="false"
            name="file"
            :multiple="false"
            :customRequest="file => userUploadFile(file)"
            :beforeUpload="beforeFileUpload"
          >
            <tx-button type="primary" icon="file-add">上传文件</tx-button>
          </a-upload>

          <tx-button type="primary" @click="createFolder" icon="folder-add">创建文件夹</tx-button>
          <br />
          <a-input-search
            style="width: 400px; margin-top: 8px"
            v-model:value="searchValue"
            placeholder="按前缀查找当前目录对象"
            enter-button="搜索"
            @search="onSearch"
          />
        </div>
        <a-card :bordered="false">
          <a-breadcrumb>
            <a-breadcrumb-item v-for="i in breadcrumbArr" :key="i.id">
              <span style="cursor: pointer" @click="breadClick(i)">{{ i.name }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
          <a-table
            :rowKey="record => record.name"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="tableData"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'fileName'">
                <span style="cursor: pointer" @click="folderClick(record)">
                  <a-icon type="folder" v-if="record.isDic" />
                  {{ record.name }}
                </span>
              </template>
              <template v-else-if="column.dataIndex == 'opt'">
                <div v-if="!record.disabled">
                <a :href="record.downloadLink" v-if="!record.isDic" @click="downloadFileOpt(record)">下载</a>
                <a-popconfirm title="确定删除吗？" ok-text="删除" cancel-text="取消" @confirm="deleteFIle(record)">
                  <a href="javascript:;" style="margin-left: 8px">删除</a>
                </a-popconfirm>
              </div>
              <a-spin v-if="record.disabled" tip="下载中" />
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-card>
    <a-modal title="创建文件夹" :visible="modalVisable" @ok="handleUpload" @cancel="handleCancel">
      <a-input v-model:value="folderTitle" placeholder="请输入文件夹名称" />
    </a-modal>
  </page-header-wrapper>
</template>
<script>
import { STable, Ellipsis } from '@/components'
// downloadFile  createFolder,
import {
  userBucketInfo,
  createFolder,
  deleteFile,
  userFileList,
  uploadFile,
  createUserBucket,
  downloadFile,
} from '@/api/storage/osp'
// import { allOrgUser } from '@/api/ssoApi'
// import FileSaver from 'file-saver'
import storageModal from './comp/storageModal.vue'
import AllUser from '@/views/comp/allOrgUser/index.vue'
import store from '@/store'
export default {
  name: 'MysqlPersonal',
  components: {
    STable,
    Ellipsis,
    storageModal,
    AllUser,
  },
  data() {
    const columns = [
      {
        title: '文件名',
        dataIndex: 'fileName',
        scopedSlots: { customRender: 'fileName' },
      },
      {
        title: '大小',
        dataIndex: 'size',
      },
      {
        title: '类型',
        dataIndex: 'type',
      },
      {
        title: '更改时间',
        dataIndex: 'modifyTime',
      },
      {
        title: '操作',
        dataIndex: 'opt',
        scopedSlots: { customRender: 'opt' },
      },
    ]
    return {
      modalVisable: false,
      folderTitle: '',
      currentBucket: '',
      columns,
      tableData: [],
      selectedRowKeys: [],
      searchValue: '',
      breadcrumbArr: [],
      applicationFlag: false,
      queryParam: {},
      userInfo: {
        principal: store.getters.name,
        principalEmails: store.getters.email,
      },
    }
  },
  computed: {},
  mounted() {
    if (!this.$route.query.name) {
      this.$message.warning('请选择共享存储桶')
      this.$router.push('/storage/osp-share')
    } else {
      this.initPage()
    }
  },
  methods: {
    beforeFileUpload(file, filelist) {
      console.log(file, 'ffff')
      var this_ = this
      return new Promise((resolve, reject) => {
        const isLt2KB = file.size / 1024 / 1024 < 200
        if (!isLt2KB) {
          this_.$message.error('上传文件大于200MB!')
          reject(false)
        } else {
          resolve(true)
        }
      }).finally(() => {})
    },
    // 上传文件
    userUploadFile(info) {
      const file = info.file
      let currentPath = ''
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title + '/'
      }
      currentPath += `${file.name}`
      var formdata = new FormData()
      formdata.append('file', file)
      formdata.append('bucketName', this.currentBucket)
      formdata.append('fileKey', currentPath)
      formdata.append('isOspShare', '1')
      uploadFile(formdata)
        .then(res => {
          let folderPath = ''
          for (let i = 1; i < this.breadcrumbArr.length; i++) {
            folderPath = folderPath + this.breadcrumbArr[i].title
          }
          this.queryFile(this.currentBucket, folderPath)
          noc.notice.ok({
            message: '上传成功',
          })
        })
        .catch(() => {
          noc.notice.error({
            message: '上传失败',
          })
        })
    },

    // 创建文件夹
    createFolder() {
      this.modalVisable = true
    },
    // 确认创建文件夹
    handleUpload() {
      let currentPath = ''
      console.log(this.breadcrumbArr, 'this.breadcrumbArr')
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      const params = {
        bucketName: this.currentBucket,
        fileKey: currentPath + this.folderTitle + '/',
        isOspShare: '1',
      }
      console.log(currentPath, 'currentPathcurrentPathcurrentPath')
      createFolder(params)
        .then(res => {
          console.log(currentPath, 'currentPath222')
          this.queryFile(this.currentBucket, currentPath)
        })
        .catch(() => {})
        .finally(() => {
          this.modalVisable = false
        })
    },
    handleCancel() {
      this.modalVisable = false
    },
    // 文件夹点击
    folderClick(row) {
      if (row.isDic) {
        this.breadcrumbArr.push({
          title: row.name,
          id: row.name,
          name: row.name.split('/')[0],
        })
        let currentPath = ''
        for (let i = 1; i < this.breadcrumbArr.length; i++) {
          currentPath = currentPath + this.breadcrumbArr[i].title
        }
        this.queryFile(this.currentBucket, currentPath)
      }
    },
    // 下载
    downloadFileOpt(row) {
      // let flag = true
      // if (row.size) {
      //   if (row.size.includes('G') || row.size.includes('T')) {
      //     flag = false
      //   }
      //   if (row.size.includes('MB')) {
      //     let sizeNum = row.size.split('MB')[0]
      //     Number(sizeNum) > 200 ? (flag = false) : (flag = true)
      //   }
      // }
      // if (!flag) {
      //   noc.notice.error({
      //     message: '文件超过200M，请去系统盘下载',
      //   })
      //   return false
      // }
      row.disabled = true
      downloadFile({
        bucketName: this.currentBucket,
        fileKey: row.fullPath,
        isOspShare: '1',
      }).then(res => {
        const href = URL.createObjectURL(res)
        let ele = document.createElement('a')
        ele.target = '_blank'
        ele.href = href
        ele.download = row.name
        ele.click()
        ele = null
        URL.revokeObjectURL(href)
      }) .finally(() => {
          row.disabled = false
        })
    },
    deleteFIle(row) {
      deleteFile({
        bucketName: this.currentBucket,
        fileKey: row.fullPath,
        isDic: row.isDic,
        isOspShare: '1',
      })
        .then(res => {
          let currentPath = ''
          for (let i = 1; i < this.breadcrumbArr.length; i++) {
            currentPath = currentPath + this.breadcrumbArr[i].title + '/'
          }
          this.queryFile(this.currentBucket, currentPath)
          noc.notice.ok({
            message: '删除成功',
          })
        })
        .catch(() => {})
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    // 查找
    queryFile(name, path = '', searchName = '') {
      userFileList({
        bucketName: name,
        path: path,
        searchName: searchName, // 搜索
        lastFileName: '', // 翻页时给当前页最后一个文件名称
        pageSize: '', // 默认100
        isOspShare: '1',
      })
        .then(r => {
          this.tableData = r.files
        })
        .catch(() => {})
    },
    breadClick(item) {
      const len = this.breadcrumbArr.length
      const index = this.breadcrumbArr.indexOf(item)
      let currentPath = ''

      this.breadcrumbArr.splice(index + 1, len - index - 1)
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      if (item !== this.breadcrumbArr[len - 1]) {
        this.queryFile(this.currentBucket, currentPath)
      }
    },
    initPage() {
      this.currentBucket = this.$route.query.name
      this.breadcrumbArr = [
        {
          title: this.$route.query.name,
          id: 'first',
          name: this.$route.query.name,
        },
      ]
      this.queryFile(this.currentBucket)
    },
    bookServe() {
      createUserBucket({
        principalEmails: this.userInfo.principalEmails,
        bucketName: this.userInfo.principalEmails.split('@')[0],
      })
        .then(res => {
          this.initPage()
          noc.notice.ok({
            message: '开通成功',
          })
        })
        .catch(() => {
          noc.notice.error({
            message: '开通失败',
          })
        })
    },
    onSearch() {
      let currentPath = ''
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      this.queryFile(this.currentBucket, currentPath, `${this.searchValue}`)
    },
  },
}
</script>
