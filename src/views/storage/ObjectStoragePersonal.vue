<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-operator">
        <tx-button type="primary" icon="check-circle" v-if="!currentBucket" @click="bookServe">业务开通</tx-button>
      </div>
      <div v-if="currentBucket">
        <a-spin :spinning="spinning">
          <div class="table-operator">
            <a-upload
              :show-upload-list="false"
              name="file"
              :multiple="false"
              :beforeUpload="beforeFileUpload"
              :customRequest="file => userUploadFile(file)"
            >
              <tx-button type="primary" icon="file-add">上传文件</tx-button>
            </a-upload>
            <tx-button type="primary" @click="createFolder" icon="folder-add">创建文件夹</tx-button>
            <tx-button type="primary" @click="shareHistoryList" icon="folder-add">查看分享日志</tx-button>

            <br />
            <a-input-search
              style="width: 400px; margin-top: 8px"
              v-model:value="searchValue"
              placeholder="按前缀查找当前目录对象"
              enter-button="搜索"
              @search="onSearch"
            />
            <tx-button style="width: 60px; margin-top: 8px" type="primary" @click="freshBucket">刷新</tx-button>
          </div>

          <a-card :bordered="false">
            <a-breadcrumb>
              <a-breadcrumb-item v-for="i in breadcrumbArr" :key="i.id">
                <span style="cursor: pointer" @click="breadClick(i)">{{ i.name }}</span>
              </a-breadcrumb-item>
            </a-breadcrumb>
            <a-table
              :rowKey="record => record.name"
              :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              :columns="columns"
              :data-source="tableData"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex == 'fileName'">
                  <span style="cursor: pointer" @click="folderClick(record)">
                    <a-icon type="folder" v-if="record.isDic" />
                    {{ record.name }}
                  </span>
                </template>
                <template v-else-if="column.dataIndex == 'opt'">
                  <div v-if="!record.disabled">
                    <a :href="record.downloadLink" v-if="!record.isDic" @click="downloadFileOpt(record)">下载</a>
                    <template v-if="!(record.name == 'ifile/' || record.name == 'ifile')">
                      <a-popconfirm
                        title="确定删除吗？"
                        ok-text="删除"
                        cancel-text="取消"
                        @confirm="deleteFIle(record)"
                      >
                        <a v-if="!record.delete" href="javascript:;" style="margin-left: 8px">删除</a>
                        <a-spin v-if="record.delete" />
                      </a-popconfirm>
                    </template>
                    <template v-if="!ifileDisabled && !(record.name == 'ifile/' || record.name == 'ifile')">
                      <a @click="fileShare(record)" href="javascript:;" style="margin-left: 8px">分享</a>
                    </template>
                  </div>
                  <a-spin v-if="record.disabled" tip="下载中" />
                </template>
              </template>
            </a-table>
          </a-card>
        </a-spin>
      </div>
    </a-card>
    <a-modal
      :maskClosable="false"
      @cancel="cancelShare"
      :width="600"
      :closable="false"
      title="文件分享"
      :visible="shareVisable"
    >
      <a-form
        ref="formRef"
        :model="shareForm"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        :rules="rules"
      >
        <a-form-item label="分享人" name="username">{{ shareForm.username }}</a-form-item>
        <a-form-item label="文件接收人邮箱" name="acceptUser">
          <!-- :rules="[{ required: true, message: '请选择文件接收人邮箱' }]" -->
          <a-select
            show-search
            :value="shareForm.acceptUser"
            placeholder="请输入邮箱"
            style="width: 368px"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            :not-found-content="null"
            @search="handleSearch"
            @change="handleChange"
            mode="tags"
          >
            <a-select-option v-for="d in data" :key="d.id" :value="d.email">{{ d.name }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="申请理由" name="reason" :rules="[{ required: true, message: '请填写分享理由' }]">
          <a-textarea v-model:value="shareForm.reason" placeholder="请填写分享理由" :rows="4" />
        </a-form-item>
        <!-- 第二部  确认状态ok  有外部邮箱 -->
        <a-form-item v-if="step == 2 && checkStatus && !allCompEmail" label="分享密码" name="sharePwd">
          <a-tag color="blue">{{ shareForm.sharePwd }}</a-tag>
        </a-form-item>
      </a-form>
      <p v-if="step == 1" style="text-align: center">
        <a-tag color="green" v-if="allCompEmail">若分享文件给企业外部人员，系统将自动对文件进行内容安全检测。</a-tag>
        <a-tag color="orange" v-else>识别到外部人员邮箱，对文件内容进行安全检测中……</a-tag>
      </p>
      <p v-if="step == 2" style="text-align: center">
        <a-tag color="green" v-if="checkStatus && !allCompEmail">
          文件目录审核通过，正在打包加密，稍后将发送至对方邮箱，分享密码请自行发送给接收人。
        </a-tag>
        <a-tag color="green" v-if="checkStatus && allCompEmail">
          文件接收人是公司内部成员，已将文件转存到对应人员云存储内并发邮件提醒。
        </a-tag>
        <a-tag v-if="!checkStatus" color="red">
          检测不通过，文件存在敏感信息或文件大小大于500M，请检查并修改后再次申请。
        </a-tag>
      </p>
      <template #footer>
        <a-button @click="cancelShare">关闭</a-button>
        <a-button @click="confirmShare" v-if="step != 2 && !checkStatus" type="primary">确定</a-button>
      </template>
    </a-modal>
    <a-modal title="创建文件夹" :visible="modalVisable" @ok="handleUpload" @cancel="handleCancel">
      <a-input v-model:value="folderTitle" placeholder="请输入文件夹名称" />
    </a-modal>
    <a-modal :width="1000" title="文件分享日志" @cancel="shareHistoryClose" :visible="shareHistoryVis">
      <a-table
        :pagination="pagination"
        @change="handleTableChange"
        :columns="shareHisListColumns"
        :data-source="shareHisData"
      ></a-table>
      <template #footer>
        <a-button @click="shareHistoryClose">关闭</a-button>
      </template>
    </a-modal>
  </page-header-wrapper>
</template>
<script>
import { STable, Ellipsis } from '@/components'
import { getUserList } from '@/api/permission/user'
// downloadFile  createFolder,
import {
  userBucketInfo,
  createFolder,
  deleteFile,
  userFileList,
  uploadFile,
  createUserBucket,
  downloadFile,
  checkFileSafety,
  shareFile,
  shareLog,
} from '@/api/storage/osp'
// import { allOrgUser } from '@/api/ssoApi'
// import FileSaver from 'file-saver'
import storageModal from './comp/storageModal.vue'
import AllUser from '@/views/comp/allOrgUser/index.vue'
import store from '@/store'
let timeout
let currentValue

function fetch(value, callback) {
  if (timeout) {
    clearTimeout(timeout)
    timeout = null
  }
  currentValue = value

  function fake() {
    getUserList({
      searchText: value,
      pageSize: 10,
      pageNo: 1,
    }).then(d => {
      if (currentValue === value) {
        const result = d.Data.data
        console.log(result, 'resultresult')
        const data = []
        result.forEach(r => {
          data.push(r)
        })
        callback(data)
      }
    })
  }

  timeout = setTimeout(fake, 300)
}
export default {
  name: 'MysqlPersonal',
  components: {
    STable,
    Ellipsis,
    storageModal,
    AllUser,
  },
  data() {
    const columns = [
      {
        title: '文件名',
        dataIndex: 'fileName',
        scopedSlots: { customRender: 'fileName' },
      },
      {
        title: '大小',
        dataIndex: 'size',
      },
      {
        title: '更改时间',
        dataIndex: 'modifyTime',
      },
      {
        title: '操作',
        dataIndex: 'opt',
        scopedSlots: { customRender: 'opt' },
      },
    ]
    const shareHisListColumns = [
      {
        title: '文件路径',
        dataIndex: 'FileKey',
        key: 'FileKey',
      },
      {
        title: '文件接收人邮箱',
        dataIndex: 'ReceiverEmail',
        key: 'ReceiverEmail',
      },
      {
        title: '查看人IP',
        dataIndex: 'LastAccessIp',
        key: 'LastAccessIp',
      },
      {
        title: '分享密码',
        dataIndex: 'Password',
        key: 'Password',
      },
      {
        title: '访问次数',
        dataIndex: 'AccessCount',
        key: 'AccessCount',
      },

      {
        title: '分享时间',
        dataIndex: 'shareTime',
        key: 'shareTime',
      },
      {
        title: '理由',
        dataIndex: 'Reason',
        key: 'Reason',
      },
    ]
    let validateEmail = async (_rule, value) => {
      console.log(value, 'vvvvv')
      if (value.length) {
        const regEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/
        for (let i = 0; i < value.length; i++) {
          if (!regEmail.test(value[i])) {
            return Promise.reject('邮箱格式不正确')
          }
        }
      } else {
        return Promise.reject('请输入接受人邮箱')
      }
    }
    const shareHisData = []
    return {
      spinning: false,
      ifileDisabled: false,
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      shareHisListColumns,
      shareFile: null,
      shareHistoryVis: false,
      shareHisData,
      data: [],
      shareForm: {
        username: store.getters.name,
        acceptUser: [],
        reason: '',
        sharePwd: '1111',
      },
      rules: {
        acceptUser: [
          {
            required: true,
            validator: validateEmail,
            trigger: 'change',
          },
        ],
      },
      shareVisable: false, //文件分享
      modalVisable: false,
      folderTitle: '',
      currentBucket: '',
      columns,
      tableData: [],
      selectedRowKeys: [],
      searchValue: '',
      breadcrumbArr: [],
      applicationFlag: false,
      queryParam: {},
      userInfo: {
        principal: store.getters.name,
        principalEmails: store.getters.email,
      },
      allCompEmail: true,
      checkStatus: false,
      step: 1,
      shareFlag: true,
    }
  },
  computed: {},
  mounted() {
    this.initPage()
  },
  watch: {
    breadcrumbArr: {
      handler: function (val) {
        console.log(val, 'vvvvv')
        this.ifileDisabled = false
        if (val) {
          for (let i = 0; i < val.length; i++) {
            if (val[i].name.includes('ifile')) {
              this.ifileDisabled = true
            }
          }
        }
      },
      deep: true,
    },
  },
  methods: {
    // 刷新按钮
    freshBucket() {
      console.log(this.breadcrumbArr, 'breadcrumbArrbreadcrumbArr')
      let currentPath = ''
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      this.queryFile(this.currentBucket, currentPath)
    },
    handleTableChange() {
      console.log(arguments, 'arg')
      const pages = arguments[0]
      this.pagination.current = pages.current
      this.shareHistoryList({ pageNo: pages.current })
    },
    // 查看分享日志
    shareHistoryList({ pageNo = 1, pageSize = 10 }) {
      this.shareHistoryVis = true
      shareLog({
        bucketName: this.currentBucket,
        pageNo,
        pageSize,
      })
        .then(res => {
          console.log(res, 'ressss')
          this.shareHisData = res.logs
          this.pagination.total = res.totalCount
        })
        .catch(err => {
          console.log(err)
        })
    },
    shareHistoryClose() {
      this.shareHistoryVis = false
      this.pagination = {
        total: 0,
        current: 1,
        pageSize: 10,
      }
    },
    cancelShare() {
      this.shareVisable = false
      this.shareForm = {
        username: store.getters.name,
        acceptUser: [],
        reason: '',
        sharePwd: '1111',
      }
      this.allCompEmail = true
      this.checkStatus = false
      this.step = 1
    },
    handleSearch(value) {
      fetch(value, data => (this.data = data))
    },
    handleChange(value) {
      this.shareForm.acceptUser = value
      console.log(this.shareForm.acceptUser, 'shareForm.acceptUsershareForm.acceptUsershareForm.acceptUser')
    },
    fileShare(file) {
      this.shareFile = file
      console.log(this.shareFile, 'shareFile')
      this.shareVisable = true
    },

    // 分享确定
    confirmShare() {
      console.log(this.shareFlag)
      if (this.shareFlag) {
        this.shareFlag = false
        let that = this
        console.log('可以分享')
        this.$refs.formRef
          .validateFields()
          .then(res => {
            console.log(res, 'rrr')
            let innerEmail = []
            let outerEmail = []
            for (var i = 0; i < res.acceptUser.length; i++) {
              let item = res.acceptUser[i]
              let emailFlag = item.split('@')[1]
              if (emailFlag == 'bertadata.com' || emailFlag == 'intsig.net') {
                innerEmail.push(item)
              } else {
                outerEmail.push(item)
              }
            }
            if (outerEmail.length) {
              this.allCompEmail = false
            } else {
              this.allCompEmail = true
            }
            // 全是内部邮箱
            if (this.allCompEmail) {
              shareFile({
                bucketName: this.currentBucket,
                fileKey: this.shareFile.fullPath,
                isDic: this.shareFile.isDic,
                innerEmails: innerEmail,
                outraEmails: outerEmail,
                reason: this.shareForm.reason,
              })
                .then(res => {
                  setTimeout(() => (that.shareFlag = true), 3000)
                  console.log(res, 'r222222')
                  this.checkStatus = true
                  this.step = 2
                  this.shareForm.sharePwd = res.password
                })
                .catch(err => {
                  console.log(err, 'err')
                })
            } else {
              //含有外部邮箱
              checkFileSafety({
                bucketName: this.currentBucket,
                fileKey: this.shareFile.fullPath,
                isDic: this.shareFile.isDic,
              })
                .then(res => {
                  console.log(res, 'res')
                  this.step = 2
                  if (res.isSafe && res.isSizeOK) {
                    this.checkStatus = true
                    shareFile({
                      bucketName: this.currentBucket,
                      fileKey: this.shareFile.fullPath,
                      isDic: this.shareFile.isDic,
                      innerEmails: innerEmail,
                      outraEmails: outerEmail,
                      reason: this.shareForm.reason,
                    })
                      .then(res => {
                        setTimeout(() => (that.shareFlag = true), 3000)
                        console.log(res, 'r222222')
                        this.shareForm.sharePwd = res.password
                      })
                      .catch(err => {
                        console.log(err, 'err')
                      })
                  } else if (!res.isSafe || !res.isSizeOK) {
                    this.checkStatus = false
                  }
                })
                .catch(err => {
                  console.log(err, 'err')
                })
            }
          })
          .catch(err => {
            console.log(err)
          })
      } else {
        console.log('分享中，请勿重复分享')
        noc.notice.warning({
          message: '分享中，请勿重复分享',
        })
      }
    },
    beforeFileUpload(file, filelist) {
      var this_ = this
      return new Promise((resolve, reject) => {
        const isLt2KB = file.size / 1024 / 1024 < 200
        if (!isLt2KB) {
          this_.$message.error('上传文件大于200MB!')
          reject(false)
        } else {
          resolve(true)
        }
      }).finally(() => {})
    },
    // 上传文件
    userUploadFile(info) {
      const file = info.file
      let currentPath = ''
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      currentPath += `${file.name}`
      var formdata = new FormData()

      formdata.append('file', file)
      formdata.append('bucketName', this.currentBucket)
      formdata.append('fileKey', currentPath)
      this.spinning = true
      uploadFile(formdata)
        .then(res => {
          let folderPath = ''
          for (let i = 1; i < this.breadcrumbArr.length; i++) {
            folderPath = folderPath + this.breadcrumbArr[i].title
          }
          this.queryFile(this.currentBucket, folderPath)
          noc.notice.ok({
            message: '上传成功',
            duration: 3,
          })
        })
        .catch(() => {
          noc.notice.error({
            message: '上传失败',
            duration: 3,
          })
        })
        .finally(() => {
          this.spinning = false
        })
    },

    // 创建文件夹
    createFolder() {
      this.modalVisable = true
    },
    handleUpload() {
      let currentPath = ''
      console.log(this.breadcrumbArr, 'this.breadcrumbArr')
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      const params = {
        bucketName: this.currentBucket,
        fileKey: currentPath + this.folderTitle + '/',
      }
      createFolder(params)
        .then(res => {
          this.queryFile(this.currentBucket, currentPath)
        })
        .catch(() => {})
        .finally(() => {
          this.modalVisable = false
        })
    },
    handleCancel() {
      this.modalVisable = false
    },
    // 文件夹点击
    folderClick(row) {
      if (row.isDic) {
        this.breadcrumbArr.push({
          title: row.name,
          id: row.name,
          name: row.name.split('/')[0],
        })
        let currentPath = ''
        for (let i = 1; i < this.breadcrumbArr.length; i++) {
          currentPath = currentPath + this.breadcrumbArr[i].title
        }
        this.queryFile(this.currentBucket, currentPath)
      }
    },
    // 下载
    downloadFileOpt(row) {
      row.disabled = true
      downloadFile({
        bucketName: this.currentBucket,
        fileKey: row.fullPath,
      })
        .then(res => {
          const href = URL.createObjectURL(res)
          let ele = document.createElement('a')
          ele.target = '_blank'
          ele.href = href
          ele.download = row.name
          ele.click()
          ele = null
          URL.revokeObjectURL(href)
        })
        .finally(() => {
          row.disabled = false
        })
    },
    deleteFIle(row) {
      row.delete = true
      deleteFile({
        bucketName: this.currentBucket,
        fileKey: row.fullPath,
        isDic: row.isDic,
      })
        .then(res => {
          let currentPath = ''
          for (let i = 1; i < this.breadcrumbArr.length; i++) {
            currentPath = currentPath + this.breadcrumbArr[i].title
          }
          this.queryFile(this.currentBucket, currentPath)
          noc.notice.ok({
            message: '删除成功',
          })
        })
        .catch(() => {})
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    queryFile(name, path = '', searchName = '') {
      userFileList({
        bucketName: name,
        path: path,
        searchName: searchName, // 搜索
        lastFileName: '', // 翻页时给当前页最后一个文件名称
        pageSize: '', // 默认100
      })
        .then(r => {
          this.tableData = r.files
        })
        .catch(() => {})
    },
    breadClick(item) {
      const len = this.breadcrumbArr.length
      const index = this.breadcrumbArr.indexOf(item)
      let currentPath = ''

      this.breadcrumbArr.splice(index + 1, len - index - 1)
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      if (item !== this.breadcrumbArr[len - 1]) {
        this.queryFile(this.currentBucket, currentPath)
      }
    },
    initPage() {
      userBucketInfo({
        principalEmails: this.userInfo.principalEmails,
      })
        .then(res => {
          this.currentBucket = res.bucketAccesses[0].bucketName
          if (res.bucketAccesses.length) {
            this.breadcrumbArr = [
              {
                title: res.bucketAccesses[0].bucketName,
                id: 'first',
                name: res.bucketAccesses[0].bucketName,
              },
            ]
            this.queryFile(res.bucketAccesses[0].bucketName)
          }
        })
        .catch(() => {})
    },
    bookServe() {
      createUserBucket({
        principalEmails: this.userInfo.principalEmails,
        bucketName: this.userInfo.principalEmails.split('@')[0],
      })
        .then(res => {
          this.initPage()
          noc.notice.ok({
            message: '开通成功',
          })
        })
        .catch(() => {
          noc.notice.error({
            message: '开通失败',
          })
        })
    },
    onSearch() {
      let currentPath = ''
      for (let i = 1; i < this.breadcrumbArr.length; i++) {
        currentPath = currentPath + this.breadcrumbArr[i].title
      }
      this.queryFile(this.currentBucket, currentPath, `${this.searchValue}`)
    },
  },
}
</script>
