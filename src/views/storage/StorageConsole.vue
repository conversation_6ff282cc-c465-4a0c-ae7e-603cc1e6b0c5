<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :bordered="false"
      :tabList="tabListNoTitle"
      :activeTabKey="noTitleKey"
      @tabChange="key => handleTabChange(key, 'noTitleKey')"
    >
      <dashboard v-if="noTitleKey === 'dashboard'"></dashboard>
      <storage-list v-else-if="noTitleKey === 'list'"></storage-list>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import Dashboard from './comp/dashboard.vue'
import StorageList from './comp/storageList.vue'

export default {
  components: {
    Dashboard,
    StorageList,
  },
  data() {
    return {
      tabListNoTitle: [
        {
          key: 'dashboard',
          tab: '概览',
        },
        {
          key: 'list',
          tab: '存储桶列表',
        },
      ],
      noTitleKey: 'dashboard',
    }
  },
  methods: {
    handleTabChange(key, type) {
      this[type] = key
    },
  },
}
</script>

<style scoped></style>
