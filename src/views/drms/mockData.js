//最外层表头
export const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: 'UUID',
    dataIndex: 'uuid',
    sorter: true,
  },
  {
    title: '一级备份地址',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: '备份类型',
    dataIndex: 'backupType',
    sorter: true,
  },
  {
    title: '数据类型',
    dataIndex: 'dbType',
    sorter: true,
  },
  {
    title: '一级备份地址',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: '负责人',
    dataIndex: 'principalEmail',
    sorter: true,
  }
]
// 内层表格表头
export const innerColumns = [
  { title: '日期',     dataIndex: 'date'},
  { title: '备份密钥', dataIndex: 'backupKey',  scopedSlots: { customRender: 'backupKey' } },
  { title: '备份大小', dataIndex: 'volume' },
  { title: '费用', dataIndex: 'cost' },
  { title: '文件路径', key: 'filePath', dataIndex: 'filePath' },
  { title: '一级备份区域',    dataIndex: 'firstRegion' },
  { title: '一级备份状态',    dataIndex: 'firstStatus',    scopedSlots: { customRender: 'firstStatus' } },
  { title: '二级备份区域',    dataIndex: 'secondRegion' },
  { title: '二级备份状态',    dataIndex: 'secondStatus',   scopedSlots: { customRender: 'secondStatus' } },
  { title: '三级备份区域',    dataIndex: 'thirdRegion' },
  { title: '三级备份状态',    dataIndex: 'thirdStatus',    scopedSlots: { customRender: 'thirdStatus' }  },
]
export const innerTableData = [
  {
    date: '20230528',
    name: 'test1',
    sorNum: '9',
    firstTime: '2023-04-17 14:00',
    secondZoom: '苏州',
    secondTime: '2023-04-17 14:00',
    thirdZoom: '北京',
    thirdTime: '2023-04-17 14:00',
    type: 'code',
    aKey: '4096',
    createTime: 'q41SudJVYDK0W40lG4s2D3yR',
    updateTime: '2023-05-29 10:57:32',
  },
]

// 表格数据
export const mockData = [
  {
    uuid: '1111',
    date: '20230530',
    name: '测试测试',
    dbType: 'test',
    ip: '*********',
    firstRegion: 'test',
    firstStatus: 'ok',
    secondStatus: 'error',
    thirdStatus: 'other',
    principalEmail: '111',
    // 内层表格数据
    innerData: [
      {
        date: '20230528',
        name: 'test1',
        sorNum: '9',
        firstTime: '2023-04-17 14:00',
        secondZoom: '苏州',
        secondTime: '2023-04-17 14:00',
        thirdZoom: '北京',
        thirdTime: '2023-04-17 14:00',
        type: 'code',
        aKey: '4096',
        createTime: 'q41SudJVYDK0W40lG4s2D3yR',
        updateTime: '2023-05-29 10:57:32',
      },
    ],
  },
]
