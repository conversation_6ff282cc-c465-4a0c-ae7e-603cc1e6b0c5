<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="5" :sm="24">
              <a-form-item label="备份类型">
                <a-select v-model:value="queryParam.backupType" allowClear placeholder="请选择">
                  <a-select-option value="db">数据备份</a-select-option>
                  <a-select-option value="image">镜像备份</a-select-option>
                  <a-select-option value="code">代码仓库备份</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="名称">
                <a-input v-model:value="queryParam.name" placeholder="名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ip" placeholder="IP" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新建</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions >
            <a-descriptions-item label="物理机Ip">{{ record.physicalIp }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{column, record}">
        <template v-if="column.dataIndex == 'backupType'">
          <a-tag :color="backupTypeColorFilter(record.backupType)">{{ backupTypeFilter(record.backupType) }}</a-tag>
        </template>
          <template v-else-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>

      <a-modal
        title="新建"
        :visible="visible"
        :confirm-loading="confirmLoading"
        @ok="handleOk"
        @cancel="handleCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="名称" name="name">
            <a-input v-model:value="form.name"/>
          </a-form-model-item>
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="form.ip"/>
          </a-form-model-item>
          <a-form-model-item label="物理机IP" name="physicalIp">
            <a-input v-model:value="form.physicalIp"/>
          </a-form-model-item>
          <a-form-model-item label="备份类型" name="backupType">
            <a-select v-model:value="form.backupType" >
              <a-select-option value="db">数据备份</a-select-option>
              <a-select-option value="image">镜像备份</a-select-option>
              <a-select-option value="code">代码仓库备份</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="数据类型" name="dbType">
            <a-input v-model:value="form.dbType"/>
          </a-form-model-item>
          <a-form-model-item label="负责人" name="principalEmail">
            <a-input v-model:value="form.principalEmail"/>
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="名称" name="name">
            <a-input v-model:value="form.name"/>
          </a-form-model-item>
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="form.ip"/>
          </a-form-model-item>
          <a-form-model-item label="物理机IP" name="physicalIp">
            <a-input v-model:value="form.physicalIp"/>
          </a-form-model-item>
          <a-form-model-item label="备份类型" name="backupType">
            <a-select v-model:value="form.backupType" >
              <a-select-option value="db">数据备份</a-select-option>
              <a-select-option value="image">镜像备份</a-select-option>
              <a-select-option value="code">代码仓库备份</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="数据类型" name="dbType">
            <a-input v-model:value="form.dbType"/>
          </a-form-model-item>
          <a-form-model-item label="负责人" name="principalEmail">
            <a-input v-model:value="form.principalEmail"/>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
  import moment from 'moment'
  import { STable, Ellipsis } from '@/components'
  import { getBackupList, createBackupList, getBackupInfo, updateBackupList, deleteBackupList } from '@/api/db/drms'
  import store from '@/store'
  import { removeWatermark, setWaterMark } from '@/utils/watermark'

  const backupTypeMap = {
    'db': {
      color: 'green',
      text: '数据备份'
    },
    'image': {
      color: 'cyan',
      text: '镜像备份'
    },
    'code': {
      color: 'blue',
      text: '代码仓库备份'
    }
  }

  const columns = [
    {
      title: 'UUID',
      dataIndex: 'id',
      sorter: true
    },
    {
      title: '名称',
      dataIndex: 'name',
      sorter: true
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      sorter: true
    },
    {
      title: '备份类型',
      dataIndex: 'backupType',
      sorter: true,
      scopedSlots: { customRender: 'backupType' }
    },
    {
      title: '数据类型',
      dataIndex: 'dbType',
      sorter: true
    },
    {
      title: '负责人',
      dataIndex: 'principalEmail',
      sorter: true
    },
    {
      title: '操作',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' },
      width: '120px',
      align: 'center'
    }
  ]

  const pagination = {
    showTotal: total => `共 ${total} 条`
  }

  export default {
    name: 'DrmsBackupList',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.pagination = pagination
      return {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
        orderTypeList: [],
        queryParam: {},
        advanced: false,
        form: {
          name: undefined,
          backupType: 'db',
          dbType: undefined,
          ip: undefined,
          principalEmail: store.getters.email
        },
        visible: false,
        updateVisible: false,
        confirmLoading: false,
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return getBackupList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
                return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
              } else {
                return res.Data
              }
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: [],
        rules: {
          'name': [{ required: true, message: '请填写名称', trigger: 'change' }],
          'backupType': [{ required: true, message: '请选择备份类型', trigger: 'change' }],
          'dbType': [{ required: true, message: '请选择数据类型', trigger: 'change' }],
          'ip': [{ required: true, message: '请填写IP', trigger: 'change' }],
          'principalEmail': [{ required: true, message: '请填写负责人邮箱', trigger: 'change' }]
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
    },
    destroyed () {
      removeWatermark()
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    methods: {
      backupTypeFilter (type) {
        return backupTypeMap[type]?.text || type
      },
      backupTypeColorFilter (type) {
        return backupTypeMap[type]?.color || type
      },
      handleAdd () {
        this.visible = true
      },
      handleOk (e) {
        this.confirmLoading = true
        antdFormValidate(this.$refs.ruleForm, valid => {
          if (valid) {
            this.form.principalEmail= this.form.principalEmail.trim()
            createBackupList(this.form).then(res => {
              if (res === undefined) {
                this.confirmLoading = false
                this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
              } else {
                this.visible = false
                this.confirmLoading = false
                this.$refs.table.refresh()
                this.$message.success('创建成功')
              }
            })
          } else {
            this.$message.error('创建失败')
            this.confirmLoading = false
          }
        })
      },
      handleCancel (e) {
        this.visible = false
      },
      handleUpdate (record) {
        getBackupInfo(record.id).then((res) => {
          this.form = res.Data
        })
        this.updateVisible = true
      },
      handleUpdateOk (e) {
        this.confirmLoading = true
        antdFormValidate(this.$refs.ruleForm, valid => {
          if (valid) {
            updateBackupList(this.form).then(res => {
              if (res === undefined) {
                this.confirmLoading = false
                this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
              } else {
                this.updateVisible = false
                this.confirmLoading = false
                this.$refs.table.refresh()
                this.$message.success('更新成功')
              }
            })
          } else {
            this.$message.error('更新失败')
            this.confirmLoading = false
          }
        })
      },
      handleUpdateCancel (e) {
        this.updateVisible = false
      },
      handleDel (record) {
        deleteBackupList(record.id).then((res) => {
          if (res.Data.message === 'ok') {
            this.$refs.table.refresh(true)
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        })
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      resetSearchForm () {
        this.queryParam = {
          date: moment(new Date())
        }
      }
    }
  }
</script>
