<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="5" :sm="24">
              <a-form-item label="备份类型">
                <a-select v-model:value="queryParam.backupType" placeholder="请选择">
                  <a-select-option value="code">代码仓库备份</a-select-option>
                  <a-select-option value="db">数据备份</a-select-option>
                  <a-select-option value="image">镜像备份</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="一级备份状态">
                <a-select v-model:value="queryParam.firstStatus" allowClear placeholder="请选择">
                  <a-select-option value="ok">正常</a-select-option>
                  <a-select-option value="error">失败</a-select-option>
                  <a-select-option value="other">未知</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="二级备份状态">
                <a-select v-model:value="queryParam.secondStatus" allowClear placeholder="请选择">
                  <a-select-option value="ok">正常</a-select-option>
                  <a-select-option value="error">失败</a-select-option>
                  <a-select-option value="other">未知</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="三级备份状态">
                <a-select v-model:value="queryParam.thirdStatus" allowClear placeholder="请选择">
                  <a-select-option value="ok">正常</a-select-option>
                  <a-select-option value="error">失败</a-select-option>
                  <a-select-option value="other">未知</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="6" :sm="24">
                <a-form-item label="IP">
                  <a-select
                    v-model:value="queryParam.ip"
                    placeholder="请选择"
                    allowClear
                    showSearch
                    :options="ipList"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="名称">
                  <a-select
                    v-model:value="queryParam.name"
                    placeholder="请选择"
                    allowClear
                    showSearch
                    :options="nameList"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="数据类型">
                  <a-select
                    v-model:value="queryParam.dbType"
                    placeholder="请选择"
                    allowClear
                    showSearch
                    :options="dbTypeList"
                  ></a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table ref="table" size="default" :rowKey="record => record.uuid" :pagination="pagination" :columns="columns" :data="loadData">
        <template #expandedRowRender="{ record }">
          <a-table :columns="innerColumns" :data-source="record.innerData" :pagination="false" :rowKey="record => record.id">
            <template #expandedRowRender="{ record }">
              <a-descriptions >
                <a-descriptions-item label="一级备份时间">{{ record.firstTime }}</a-descriptions-item>
                <a-descriptions-item label="二级备份时间">{{ record.secondTime }}</a-descriptions-item>
                <a-descriptions-item label="三级备份时间">{{ record.thirdTime }}</a-descriptions-item>
                <a-descriptions-item label="事业部">{{ record.organization }}</a-descriptions-item>
                <a-descriptions-item label="部门">{{ record.department }}</a-descriptions-item>
                <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
                <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
                <a-descriptions-item label="备注信息">{{ record.comment }}</a-descriptions-item>
              </a-descriptions>
            </template>
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'backupKey'">
                <a-input-password style="width:230px" :value="text" />
              </template>
              <template v-if="column.dataIndex == 'firstStatus'">
                <a-tag :color="statusColorFilter(record.firstStatus)">{{ statusFilter(record.firstStatus) }}</a-tag>
              </template>
              <template v-else-if="column.dataIndex == 'secondStatus'">
                <a-tag :color="statusColorFilter(record.secondStatus)">{{ statusFilter(record.secondStatus) }}</a-tag>
              </template>
              <template v-else-if="column.dataIndex == 'thirdStatus'">
                <a-tag :color="statusColorFilter(record.thirdStatus)">{{ statusFilter(record.thirdStatus) }}</a-tag>
              </template>
            </template>
          </a-table>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { columns, innerColumns } from './mockData'
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import { getDrmsList, getDrmsIpList, getDrmsNameList, getDrmsDbTypeList } from '@/api/db/drms'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const statusMap = {
  ok: {
    color: 'green',
    text: '正常',
  },
  error: {
    color: 'red',
    text: '失败',
  },
  other: {
    color: 'orange',
    text: '未知',
  },
}

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'DrmsCodeBackup',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.innerColumns = innerColumns
    this.pagination = pagination
    return {
      ipList: [],
      nameList: [],
      dbTypeList: [],
      queryParam: { backupType: 'code' },
      advanced: false,
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getDrmsList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getConditionInfo()
  },
  destroyed() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    getConditionInfo() {
      getDrmsIpList().then(res => {
        if (!res.Data.ip) return
        for (var i = 0, len = res.Data.ip.length; i < len; i++) {
          var label = {}
          label.value = res.Data.ip[i]
          label.label = res.Data.ip[i]
          this.ipList.push(label)
        }
      })
      getDrmsNameList().then(res => {
        if (!res.Data.name) return
        for (var i = 0, len = res.Data.name.length; i < len; i++) {
          var label = {}
          label.value = res.Data.name[i]
          label.label = res.Data.name[i]
          this.nameList.push(label)
        }
      })
      getDrmsDbTypeList().then(res => {
        if (!res.Data.dbType) return
        for (var i = 0, len = res.Data.dbType.length; i < len; i++) {
          var label = {}
          label.value = res.Data.dbType[i]
          label.label = res.Data.dbType[i]
          this.dbTypeList.push(label)
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusColorFilter(type) {
      return statusMap[type]?.color || type
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
  },
}
</script>
