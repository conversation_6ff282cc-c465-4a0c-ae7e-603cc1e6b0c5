<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-11-27 14:16:23
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-26 14:56:27
 * @FilePath: \cloud_web\src\views\server\HostManager.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <a-tabs default-active-key="0" tab-position="left">
      <a-tab-pane key="0" tab="Dashboard" force-render type="card">
        <dashboard />
      </a-tab-pane>
      <a-tab-pane key="1" tab="物理机" type="card">
        <host-list />
      </a-tab-pane>
      <a-tab-pane key="2" tab="磁盘" type="card">
        <host-disk />
      </a-tab-pane>
      <!--      <a-tab-pane key="3" tab="分区" type="card">-->
      <!--        <host-disk-part />-->
      <!--      </a-tab-pane>-->
      <a-tab-pane key="4" tab="网卡" type="card">
        <host-network />
      </a-tab-pane>
      <a-tab-pane key="5" tab="交换机" type="card">
        <host-switch />
      </a-tab-pane>
      <a-tab-pane key="6" tab="Dhcp未使用IP" type="card">
        <dhcp-unused />
      </a-tab-pane>
    </a-tabs>
  </page-header-wrapper>
</template>

<script>
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { openTinyWin } from '@/utils/window'
import store from '@/store'
import Dashboard from '@/views/host/Dashboard.vue'
import HostDisk from '@/views/host/HostDisk.vue'
import HostDiskPart from '@/views/server/components/HostDiskPart.vue'
import HostList from '@/views/server/HostList.vue'
import HostNetwork from '@/views/host/HostNetwork.vue'
import HostSwitch from '@/views/host/HostSwitch.vue'
import DhcpUnused from '@/views/host/DhcpUnused.vue'

export default {
  name: 'TableList',
  components: {
    Dashboard,
    HostDisk: HostDisk,
    HostDiskPart: HostDiskPart,
    HostList: HostList,
    HostNetwork: HostNetwork,
    HostSwitch: HostSwitch,
    DhcpUnused: DhcpUnused,
  },
  data() {
    this.downloadtableList = null
    return {}
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  methods: {
    openWindow(str) {
      openTinyWin(str)
    },
  },
}
</script>
