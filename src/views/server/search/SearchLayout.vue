<template>
  <page-header-wrapper
    :tab-list="tabList"
    :tab-active-key="tabActiveKey"
    :tab-change="handleTabChange"
  >
    <template v-slot:content>
      <div class="ant-pro-page-header-search">
        <a-input-search size="large" style="width: 80%; max-width: 522px;">
          <template v-slot:enterButton>
            搜索
          </template>
        </a-input-search>
      </div>
    </template>
    <router-view />
  </page-header-wrapper>
</template>

<script>
const getActiveKey = (path) => {
  switch (path) {
    case '/list/search/article':
      return '1'
    case '/list/search/project':
      return '2'
    case '/list/search/application':
      return '3'
    default:
      return '1'
  }
}
export default {
  name: 'SearchLayout',
  data () {
    return {
      tabList: [
        { key: '1', tab: '文章' },
        { key: '2', tab: '项目' },
        { key: '3', tab: '应用' }
      ],
      tabActiveKey: '1',
      search: true
    }
  },
  watch: {
    $route: {
      handler(val) {
        this.tabActiveKey = getActiveKey(val.path)
      },
    },
  },
  created () {
    this.tabActiveKey = getActiveKey(this.$route.path)
    /*
    this.$watch('$route', (val) => {
      this.tabActiveKey = getActiveKey(val.path)
    })
    */
  },
  methods: {
    handleTabChange (key) {
      this.tabActiveKey = key
      switch (key) {
        case '1':
          this.$router.push('/list/search/article')
          break
        case '2':
          this.$router.push('/list/search/project')
          break
        case '3':
          this.$router.push('/list/search/application')
          break
        default:
          this.$router.push('/workplace')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ant-pro-page-header-search {
  text-align: center;
  margin-bottom: 16px;
}
</style>
