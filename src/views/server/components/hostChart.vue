<template>
  <div class="chartContainer">
    <div v-if="chartData.length > 0" :ref="compRef" :id="compRef"></div>
  </div>
</template>
<script>
// import G2 from '@antv/g2'
import { Column } from '@antv/g2plot'
export default {
  props: {
    chartData: {
      type: Array,
    },
    compRef: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      chart: null,
      selectChart: [],
    }
  },
  mounted() {
    if (this.chartData.length) {
        this.selectChart = this.chartData
      console.log(this.chartData)
      let color = '#7DAAFF'
      let xField = 'gpuType'
      if (this.compRef === 'buyTime') {
        color = '#FAE092'
        xField = 'year'
      }
      this.chart = new Column(this.compRef, {
        data: this.selectChart,
        isGroup: true,
        xField: xField,
        yField: 'count',
        limitInPlot: false,
        width: this.$refs[this.compRef].clientWidth,
        xAxis: {
          label: {
            style: {
              fill: 'black',
              opacity: 1,
              fontSize: 15,
            },
          },
        },
        meta: {
          count: {
            alias: '总数',
          },
        },
        color: color,
        yAxis: {
          label: {
            formatter: val => `${String(val).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`,
            style: {
              fill: 'black',
              opacity: 1,
              fontSize: 15,
            },
          },
        },
        geometryOptions: [
          {
            geometry: 'column',
            isGroup: true,
            seriesField: 'describe',
          },
        ],
        legend: {
          itemName: {
            formatter: text => {
              return text
            },
          },
        },
        height: 300,
        minColumnWidth: 30,
        maxColumnWidth: 30,
      })
      this.chart.render()
    }
  },

  methods: {
    typeFilter(type) {
      return typeMap[type]
    },
    chartRender() {},
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
.waveSelect {
  position: absolute;
  right: 0;
  top: 3%;
}
</style>
