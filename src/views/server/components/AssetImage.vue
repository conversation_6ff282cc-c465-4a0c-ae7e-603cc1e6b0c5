<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="4" :sm="24">
            <a-form-item label="IP">
              <a-input v-model:value="queryParam.assetIp" placeholder="IP" @pressEnter="$refs.table.refresh()" />
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="12">
            <a-form-item label="镜像类型">
              <a-select v-model:value="queryParam.backupType" allowClear placeholder="请选择">
                <a-select-option value="2">一次性</a-select-option>
                <a-select-option value="1">自动</a-select-option>
                <a-select-option value="3">手动</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="12">
            <a-form-item label="镜像模板">
              <a-select v-model:value="queryParam.imageTemp" allowClear placeholder="请选择">
                <a-select-option value="centos">centos</a-select-option>
                <a-select-option value="ubuntu">ubuntu</a-select-option>
                <a-select-option value="windows">windows</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="12">
            <a-form-item label="用户">
              <a-input v-model:value="queryParam.creator" placeholder="" @pressEnter="$refs.table.refresh()" />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 6) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              <tx-button v-if="userpRression" type="primary" style="margin-left: 8px" @click="createImage">
                创建镜像
              </tx-button>
              <!--                <tx-button type="primary" style="margin-left: 8px" @click="handleCreate">新增</tx-button>-->
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'status'">
          <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
        </template>
        <template v-if="column.dataIndex === 'backupType'">
          <span>{{ typeStatusFilter(text) }}</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a v-if="record.status === 1" style="margin-right: 5px" @click="handleRestore(record)">恢复</a>
          <a-divider v-if="record.status === 1" type="vertical" />
          <a-popconfirm
            v-if="record.status === 1"
            title="您确定要删除吗?"
            ok-text="确认"
            cancel-text="取消"
            @confirm.stop="handleDelete(record)"
          >
            <a href="#">删除</a>
          </a-popconfirm>
          <a v-if="record.status === 2" style="margin-right: 5px" @click="handleCreateRetry(record)">重试</a>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="镜像路径" :span="3">{{ record.imagePath }}</a-descriptions-item>
          <a-descriptions-item label="镜像大小" :span="3">{{ record.imageSize }}</a-descriptions-item>
          <a-descriptions-item label="备注" :span="3">{{ record.comment }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>
  </a-card>
  <a-modal
    title="镜像备份"
    :visible="createAssetImagedVisible"
    width="800px"
    @cancel="createAssetImagedVisible = false"
  >
    <a-alert message="您创建的镜像将保留30天后删除!" style="margin-bottom: 15px" type="info" :show-icon="true" />
    <a-form-model
      ref="orderForm"
      :rules="assetImageRules"
      :model="assetImageTemp"
      :label-col="{ span: 3 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-model-item label="服务器" name="assetIp">
        <a-select
          v-model:value="assetImageTemp.assetIp"
          style="width: 100%"
          @popupScroll="handlePopupScroll"
          @search="handleSearchIp"
          @change="handleChangeIp"
          placeholder="选择IP"
          show-search
          allowClear
        >
          <a-select-option v-for="i in ipInfo" :key="i.ip">
            {{ i.ip + '(' + i.hostname + ')' }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="镜像名" name="imageName">
        <a-input v-model:value="assetImageTemp.imageName" />
      </a-form-model-item>
      <a-form-model-item label="备注">
        <a-textarea v-model:value="assetImageTemp.comment" placeholder="备注信息" />
      </a-form-model-item>
      <a-form-model-item label="一次性备份">
        <a-radio-group v-model:value="assetImageTemp.backupType" button-style="solid">
          <a-radio-button :value="3">否</a-radio-button>
          <a-radio-button :value="2">是</a-radio-button>
        </a-radio-group>
        <a-tooltip :overlayStyle="{ width: '300px' }">
          <template #title>
            <span v-html="'一次性备份的镜像将不会被自动删除</span>'" />
          </template>
          <a-icon style="margin-left: 8px; font-size: 20px; color: blue" theme="filled" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
    </a-form-model>
    <template #footer>
      <a-button key="back" @click="createAssetImagedVisible = false">取消</a-button>
      <a-button key="submit" type="primary" :loading="backupLoading" @click="confirmCreateAssetImage">确认</a-button>
    </template>
  </a-modal>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import {
  createAssetImages,
  createAssetImagesRetry,
  deleteAssetImages,
  listAssetImages,
  restoreAssetImages,
} from '@/api/asset/assetImages'
import CreateForm from '@/views/server/modules/CreateForm.vue'
import { notification } from 'ant-design-vue'
import { getAllAssetsIp } from '@/api/workflow/asset_automation'
const columns = [
  {
    title: '服务器IP',
    dataIndex: 'assetIp',
    sorter: true,
  },
  {
    title: '镜像名称',
    dataIndex: 'imageName',
  },
  {
    title: '镜像类型',
    dataIndex: 'backupType',
  },
  {
    title: '镜像模板',
    dataIndex: 'imageTemp',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建用户',
    dataIndex: 'creator',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      restoreVisible: false,
      createVisible: false,
      createAssetImagedVisible: false,
      updateVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      // create model
      visible: false,
      confirmLoading: false,
      createLoading: false,
      updateLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      backupLoading: false,
      assetImageRules: antdFormRulesFormat({
        imageName: [{ required: true, message: '填写镜像名', trigger: 'blur' }],
        assetIp: [{ required: true, message: '选择服务器', trigger: 'blur' }],
      }),
      assetImageTemp: {
        backupType: 3,
      },
      ipInfo: [],
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      request: {
        pageNo: 1,
        isAdmin: false,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return listAssetImages(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      rules: antdFormRulesFormat({
        ip: [{ required: true, message: '请确认物理机IP', trigger: 'change' }],
        ipv4: [{ required: true, message: '请确认IP', trigger: 'change' }],
        macAddress: [{ required: true, message: '请确认Mac地址', trigger: 'change' }],
      }),
      detailIdVisible: false,
      userpRression: false,
      serverTemp: {},
      userRoles: [],
    }
  },
  created() {
    this.GetAssetIps()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    typeStatusFilter(type) {
      if (type === 1) {
        return '自动备份'
      } else if (type === 2) {
        return '一次性备份'
      } else {
        return '手动备份'
      }
    },
    statusFilter(type) {
      if (type === 1) {
        return '成功'
      } else if (type === 2) {
        return '失败'
      } else {
        return '备份中'
      }
    },
    statusTypeFilter(type) {
      if (type === 1) {
        return 'success'
      } else if (type === 2) {
        return 'error'
      } else {
        return 'processing'
      }
    },
    // 页面功能隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('admin')) {
          this.userpRression = true
        }
      })
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // handleCreate(record) {
    //   this.createVisible = true
    // },
    // handleCreateCancel() {
    //   this.createVisible = false
    // },
    // handleCreateOk() {
    //   this.createLoading = true
    //   antdFormValidate(this.$refs.createForm, valid => {
    //     if (valid) {
    //       createNetwork(this.detailData).then(response => {
    //         this.createVisible = false
    //         this.$refs.table.refresh(true)
    //         this.createLoading = false
    //       }).catch(
    //         this.createLoading = false
    //       )
    //     }
    //   })
    // },
    handleCreateRetry(record) {
      createAssetImagesRetry({ id: record.id }).then(response => {
        this.$refs.table.refresh(true)
      })
    },
    handleDelete(record) {
      deleteAssetImages(record.id).then(response => {
        if (response === undefined) {
          notification.error({
            message: '删除失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 400) {
          notification.error({
            message: '删除失败',
          })
        } else {
          notification.success({
            message: '删除成功',
          })
        }
        this.$refs.table.refresh(true)
      })
    },
    handleRestore(record) {
      this.$confirm({
        title: '确定恢复镜像?',
        content: '镜像恢复时服务器无法访问。',
        okText: '是',
        okType: 'danger',
        cancelText: '否',
        onOk() {
          restoreAssetImages({ id: record.id }).then(response => {
            this.$refs.table.refresh(true)
          })
        },
        onCancel() {
          console.log('Cancel')
        },
      })
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value) {
      console.log(111)
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    handleChangeIp(value) {
      const foundObject = this.ipInfo.find(obj => obj.ip === value)
      if (foundObject !== undefined) {
        this.assetImageTemp.assetUuid = foundObject.uuid
        this.assetImageTemp.assetIp = foundObject.ip
      }
    },
    createImage() {
      this.createAssetImagedVisible = true
      this.backupLoading = false
    },
    confirmCreateAssetImage() {
      this.backupLoading = true
      createAssetImages(this.assetImageTemp)
        .then(response => {
          this.backupLoading = false
          this.createAssetImagedVisible = false
          if (response === undefined) {
            notification.error({
              message: '创建失败',
              description: '后端接口错误，请联系运维开发排查~',
            })
          } else if (response.Code === 400) {
            notification.error({
              message: '创建失败',
            })
          } else {
            notification.success({
              message: '创建成功',
            })
          }
        })
        .catch(error => {
          console.log(error)
          this.backupLoading = false
        })
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
  },
}
</script>
