<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="模糊查询">
              <a-input v-model:value="queryParam.searchKey" placeholder="IP" @pressEnter="$refs.table.refresh()" />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button type="primary"  style="margin-left: 8px" @click="unUsedSearch">未使用资源查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'size'">
          <a-badge :text="sizeFilter(text)" />
        </template>
        <template v-if="column.dataIndex == 'diskSize'">
          <a-badge :text="sizeFilter(text)" />
        </template>
        <template v-if="column.dataIndex == 'action'">
          <a @click="handleDetail(record)">详情</a>
        </template>
      </template>

    </s-table>
    <a-drawer
      title="详情"
      placement="right"
      :closable="false"
      width="40%"
      :visible="detailIdVisible"
      @close="detailIdVisible = false"
    >
      <a-card :bordered="false" :model="detailData" ref="detailData">
        <a-descriptions :column="2" >
          <a-descriptions-item label="物理机IP">{{ detailData.ip }}</a-descriptions-item>
          <a-descriptions-item label="分区路径">{{ detailData.path }}</a-descriptions-item>
          <a-descriptions-item label="分区大小">{{ detailData.size }}</a-descriptions-item>
          <a-descriptions-item label="分区ID">{{ detailData.uuid }}</a-descriptions-item>
          <a-descriptions-item label="磁盘大小">{{ detailData.diskSize }}</a-descriptions-item>
          <a-descriptions-item label="磁盘状态">{{ detailData.free }}</a-descriptions-item>
          <a-descriptions-item label="使用状态">{{ detailData.isUsed }}</a-descriptions-item>
          <a-descriptions-item label="开始位置">{{ detailData.start }}</a-descriptions-item>
          <a-descriptions-item label="结束位置">{{ detailData.end }}</a-descriptions-item>
          <a-descriptions-item label="sector_size">{{ detailData.sectorSize }}</a-descriptions-item>
          <a-descriptions-item label="usage">{{ detailData.usage }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-drawer>
  </a-card>

</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { getHostDiskPartInfo, getHostDiskPartList } from '@/api/asset'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '物理机IP',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: '分区路径',
    dataIndex: 'path',
  },
  {
    title: '分区ID',
    dataIndex: 'uuid',
  },
  {
    title: '分区大小',
    dataIndex: 'size',
    sorter: true,
  },
  {
    title: '磁盘大小',
    dataIndex: 'diskSize',
    sorter: true,
  },
  {
    title: '使用状态',
    dataIndex: 'isUsed',
    scopedSlots: { customRender: 'isUsed' },
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'free',
    scopedSlots: { customRender: 'status' },
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      // create model
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return getHostDiskPartList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {
      },
      detailIdVisible: false,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
      serverTemp: {
        ip: '',
        idc: '',
        ipmiIp: '',
        ipmiUser: '',
        ipmiPassword: '',
        rack: '',
        ansibleIp: '',
        ansiblePort: '',
        buyTime: '',
        supplier: '',
        comment: '',
      },
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    unUsedSearch() {
      this.queryParam.action = "unUsed"
      this.$refs.table.refresh()
      this.queryParam.action = ""
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // 详细信息 相关接口
    handleDetail(record) {
      // this.allData.data.forEach(item => {
      //   if (item.id === record.id) {
      //     this.detailData = item
      //   }
      // })
      getHostDiskPartInfo(record.id).then(response => {
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
  },
}
</script>
