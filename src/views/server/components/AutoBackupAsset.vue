<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="IP">
              <a-input v-model:value="queryParam.assetIp" placeholder="IP" @pressEnter="$refs.table.refresh()" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="物理机IP">
              <a-input v-model:value="queryParam.hostIp" placeholder="物理机IP" @pressEnter="$refs.table.refresh()" />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              <tx-button v-if="userpRression" type="primary" style="margin-left: 8px" @click="createAsset">
                新增
              </tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'status'">
          <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a style="margin-right: 5px" @click="handleUpdate(record)">更新</a>
          <a-divider type="vertical" />
          <a-popconfirm title="您确定要删除吗?" ok-text="确认" cancel-text="取消" @confirm.stop="handleDelete(record)">
            <a href="#">删除</a>
          </a-popconfirm>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="备注" :span="3">{{ record.comment }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>

    <a-modal
      title="新增"
      :visible="createAutoBackupAssetVisible"
      width="800px"
      @cancel="createAutoBackupAssetVisible = false"
    >
      <a-form-model
        ref="orderForm"
        :rules="assetRules"
        :model="assetTemp"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="服务器" name="assetIp">
          <a-select
            v-model:value="assetTemp.assetIp"
            style="width: 100%"
            @popupScroll="handlePopupScroll"
            @search="handleSearchIp"
            @change="handleChangeIp"
            placeholder="选择IP"
            show-search
            allowClear
          >
            <a-select-option v-for="i in ipInfo" :key="i.ip">
              {{ i.ip + '(' + i.hostname + ')' }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="备注">
          <a-textarea v-model:value="assetTemp.comment" placeholder="备注信息" />
        </a-form-model-item>
      </a-form-model>
      <template #footer>
        <a-button key="back" @click="createAutoBackupAssetVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="createLoading" @click="confirmCreate">确认</a-button>
      </template>
    </a-modal>
    <a-modal title="更新" :visible="updateVisible" width="800px" @cancel="updateVisible = false">
      <a-form-model
        ref="orderForm"
        :rules="assetRules"
        :model="updateTemp"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="服务器" name="assetIp">
          <a-input v-model:value="updateTemp.assetIp" :disabled="true" />
        </a-form-model-item>
        <a-form-model-item label="备注">
          <a-textarea v-model:value="updateTemp.comment" placeholder="备注信息" />
        </a-form-model-item>
        <a-form-model-item label="状态">
          <a-select v-model:value="updateTemp.status" allowClear placeholder="请选择">
            <a-select-option value="2">停止</a-select-option>
            <a-select-option value="1">开始</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
      <template #footer>
        <a-button key="back" @click="updateVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="createLoading" @click="confirmUpdate">确认</a-button>
      </template>
    </a-modal>
  </a-card>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import {
  autoBackupAssetCreate,
  autoBackupAssetDelete,
  autoBackupAssetList,
  autoBackupAssetUpdate,
} from '@/api/asset/assetImages'
import CreateForm from '@/views/server/modules/CreateForm.vue'
import { notification } from 'ant-design-vue'
import { getAllAssetsIp } from '@/api/workflow/asset_automation'
const columns = [
  {
    title: '服务器IP',
    dataIndex: 'assetIp',
    sorter: true,
  },
  {
    title: '服务器名称',
    dataIndex: 'assetName',
  },
  {
    title: '物理机IP',
    dataIndex: 'hostIp',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      restoreVisible: false,
      createVisible: false,
      createAutoBackupAssetVisible: false,
      updateVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      // create model
      visible: false,
      confirmLoading: false,
      createLoading: false,
      updateLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      backupLoading: false,
      assetRules: antdFormRulesFormat({
        assetIp: [{ required: true, message: '选择服务器', trigger: 'blur' }],
      }),
      assetImageTemp: {
        backupType: 3,
      },
      ipInfo: [],
      // 查询参数
      queryParam: { status: '1' },
      allData: [],
      request: {
        pageNo: 1,
        isAdmin: false,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return autoBackupAssetList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      rules: antdFormRulesFormat({
        ip: [{ required: true, message: '请确认物理机IP', trigger: 'change' }],
        ipv4: [{ required: true, message: '请确认IP', trigger: 'change' }],
        macAddress: [{ required: true, message: '请确认Mac地址', trigger: 'change' }],
      }),
      detailIdVisible: false,
      userpRression: false,
      assetTemp: {},
      updateTemp: {},
      userRoles: [],
    }
  },
  created() {
    this.GetAssetIps()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    typeStatusFilter(type) {
      if (type === 1) {
        return '自动备份'
      } else if (type === 2) {
        return '一次性备份'
      } else {
        return '手动备份'
      }
    },
    statusFilter(type) {
      if (type === 1) {
        return '开启'
      } else if (type === 2) {
        return '停止'
      } else {
        return '未知'
      }
    },
    statusTypeFilter(type) {
      if (type === 1) {
        return 'success'
      } else {
        return 'error'
      }
    },
    // 页面功能隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('admin') || this.userRoles.includes('image_admin')) {
          this.userpRression = true
          this.request.isAdmin = true
        }
      })
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,

    handleCreateRetry(record) {
      createAssetImagesRetry({ id: record.id }).then(response => {
        this.$refs.table.refresh(true)
      })
    },
    handleDelete(record) {
      autoBackupAssetDelete(record.id).then(response => {
        if (response === undefined) {
          notification.error({
            message: '删除失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 400) {
          notification.error({
            message: '删除失败',
          })
        } else {
          notification.success({
            message: '删除成功',
          })
        }
        this.$refs.table.refresh(true)
      })
    },
    handleUpdate(record) {
      this.updateTemp = record
      this.updateVisible = true
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    handleChangeIp(value) {
      const foundObject = this.ipInfo.find(obj => obj.ip === value)
      if (foundObject !== undefined) {
        this.assetTemp.assetUuid = foundObject.uuid
        this.assetTemp.assetIp = foundObject.ip
        this.assetTemp.hostIp = foundObject.hostIp
        this.assetTemp.assetName = foundObject.hostname
      }
    },
    createAsset() {
      this.createAutoBackupAssetVisible = true
      this.createLoading = false
    },
    confirmCreate() {
      this.createLoading = true
      autoBackupAssetCreate(this.assetTemp)
        .then(response => {
          this.createLoading = false
          this.createAutoBackupAssetVisible = false
          if (response === undefined) {
            notification.error({
              message: '创建失败',
              description: '后端接口错误，请联系运维开发排查~',
            })
          } else if (response.Code === 400) {
            notification.error({
              message: '创建失败',
            })
          } else {
            notification.success({
              message: '创建成功',
            })
          }
        })
        .catch(error => {
          console.log(error)
          this.createLoading = false
        })
    },
    confirmUpdate() {
      this.updateLoading = true
      autoBackupAssetUpdate(this.updateTemp)
        .then(response => {
          this.updateLoading = false
          this.updateVisible = false
          if (response === undefined) {
            notification.error({
              message: '更新失败',
              description: '后端接口错误，请联系运维开发排查~',
            })
          } else if (response.Code === 400) {
            notification.error({
              message: '更新失败',
            })
          } else {
            notification.success({
              message: '更新成功',
            })
          }
        })
        .catch(error => {
          console.log(error)
          this.createLoading = false
        })
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
  },
}
</script>
