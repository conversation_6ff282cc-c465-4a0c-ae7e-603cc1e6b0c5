<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="10" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.search"
                  placeholder="IP/域名"
                  @pressEnter="$refs.table.refresh()"
                  allowClear
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="邮箱">
                <a-select
                  v-model:value="queryParam.email"
                  :options="principalListMini"
                  :filter-option="filterOption"
                  style="width: 100%"
                  placeholder="邮箱可输入或选择"
                  @popupScroll="handlePopupScroll"
                  @search="handleSearch"
                  showSearch
                  allowClear
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24">
              <a-form-item label="服务器状态">
                <a-select
                  ref="select"
                  v-model:value="queryParam.status"
                  @focus="focus"
                >
                  <a-select-option :value="100">全部</a-select-option>
                  <a-select-option :value="1">运行中</a-select-option>
                  <a-select-option :value="2">已关机</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operations">
        <a-button :loading="confirmLoadingExport" type="primary" @click="handleExport">
          <template #icon>
            <DownloadOutlined/>
          </template>
          导出
        </a-button>
        <a-button style="margin-left: 5px" @click="showExportHistory">
          <template #icon>
            <UnorderedListOutlined/>
          </template>
          导出记录
        </a-button>
        <a-modal v-model:visible="exportHistoryVisible" center :footer="null" width="50%">
          <s-table
            ref="tableExportHistory"
            :rowKey="record => record.id"
            :columns="exportHistoryColumns"
            :data="loadExportHistoryData"
          >
            <template #bodyCell="{ column, index, record, text }">
              <template v-if="column.dataIndex === 'resultLink'">
                <div v-if="record.resultLink !== ''">
                  <a :href="record.resultLink.replace('http:', 'https:')">下载</a>
                </div>
                <div v-else>-</div>
              </template>
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}</a-tag>
              </template>
            </template>
          </s-table>
        </a-modal>
      </div>
      <a-spin :spinning="spinning" tip="数据查询中，请稍等...">
        <s-table
          ref="table"
          size="default"
          :rowKey="record => record.id"
          :pagination="pagination"
          :columns="columns"
          :data="loadData"
        >
          <template #expandedRowRender="{ record }">
            <a-descriptions>
              <a-descriptions-item label="ID">{{ record.id }}</a-descriptions-item>
              <a-descriptions-item label="IP">{{ record.ip }}</a-descriptions-item>
              <a-descriptions-item label="资产名称(编号)" v-if="record.idc === '办公室'">{{ record.hostname }}</a-descriptions-item>
              <a-descriptions-item label="主机名称" v-else>{{ record.hostname }}</a-descriptions-item>
              <a-descriptions-item label="公网IP">{{ record.publicIp }}</a-descriptions-item>
              <a-descriptions-item label="出口IP">{{ record.exportIp }}</a-descriptions-item>
              <a-descriptions-item label="物理机IP" v-if="record.attribute === '服务器'">{{ record.hostIp }}</a-descriptions-item>
              <a-descriptions-item label="机房">{{ record.idc }}</a-descriptions-item>
              <a-descriptions-item label="UUID" v-if="record.idc === '办公室'">{{ record.uuid }}</a-descriptions-item>
              <a-descriptions-item label="环境">{{ record.env }}</a-descriptions-item>
              <a-descriptions-item label="状态">{{ record.status }}</a-descriptions-item>
              <a-descriptions-item label="域名" v-if="record.attribute === '域名' || record.attribute === 'API网关'">{{ record.domain }}</a-descriptions-item>
              <a-descriptions-item label="IPv6" v-if="record.attribute === '服务器'">{{ record.domain }}</a-descriptions-item>
              <a-descriptions-item label="负责人邮箱">{{ record.principalEmails }}</a-descriptions-item>
              <a-descriptions-item label="CPU" v-if="record.attribute === '服务器' || record.attribute === '办公网服务器'">{{ record.cpu }}</a-descriptions-item>
              <a-descriptions-item label="内存" v-if="record.attribute === '服务器' || record.attribute === '办公网服务器'">{{ record.memory }}</a-descriptions-item>
              <a-descriptions-item label="逻辑位置">{{ record.location }}</a-descriptions-item>
              <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
              <a-descriptions-item label="网络可达范围" v-if="record.attribute === '服务器'">{{ record.networkCoverage }}</a-descriptions-item>
              <a-descriptions-item label="出网状态" v-if="record.attribute === '服务器'">{{ record.outboundStatus }}</a-descriptions-item>
              <a-descriptions-item label="网络映射拓扑链路" v-if="record.attribute === '域名'">{{ record.domainNetworkMap }}</a-descriptions-item>
              <a-descriptions-item label="开放范围" v-if="record.attribute === '域名'">{{ record.domainOpenSource }}</a-descriptions-item>
              <a-descriptions-item label="MAC地址" v-if="record.idc === '办公室'">{{ record.uuid }}</a-descriptions-item>
              <a-descriptions-item label="网段信息" v-if="record.idc === '办公室'">{{ record.subnet }}</a-descriptions-item>
              <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
            </a-descriptions>
          </template>
        </s-table>
      </a-spin>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import {STable} from '@/components'
import {exportCombatPlatform, listCombatPlatform} from '@/api/asset/combatPlatform'
import store from '@/store'
import {removeWatermark, setWaterMark} from '@/utils/watermark'
import {getFileTaskList} from '@/api/cost/month'
import {notification} from 'ant-design-vue'
import {filterLabelValue} from "@aim/helper";
import {getUserEmailList} from "@/api/permission/user";

const columns = [
  {
    title: '资产属性',
    dataIndex: 'attribute'

  },
  {
    title: '资产',
    dataIndex: 'asset'
  },
  {
    title: '负责人',
    dataIndex: 'principal'
  },
  {
    title: '事业部',
    dataIndex: 'org'
  },
  {
    title: '部门',
    dataIndex: 'dep'
  },
  {
    title: '安全设备状态',
    dataIndex: 'securityStatus'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'green',
    text: '成功'
  },
  2: {
    color: 'red',
    text: '失败'
  },
  3: {
    color: 'blue',
    text: '运行中'
  }
}

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt'
  },
  {
    title: '用户',
    dataIndex: 'creator'
  },
  {
    title: '导出状态',
    dataIndex: 'status'
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink'
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

export default {
  name: 'CombatPlatform',
  components: {
    STable
  },
  data() {
    this.exportHistoryColumns = exportHistoryColumns
    this.columns = columns
    this.pagination = pagination
    return {
      spinning: true,
      labelCol: {span: 6},
      wrapperCol: {span: 14},
      queryParam: {
        status: 1
      },
      advanced: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.spinning = true
        return listCombatPlatform(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              this.spinning = false
              return {data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0}
            } else {
              this.spinning = false
              return res.Data
            }
          } else {
            this.spinning = false
            return {data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0}
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      principalListMini: [],
      principalList: [],
      creator: '',
      exportHistoryVisible: false,
      confirmLoadingExport: false,
      loadExportHistoryData: parameter => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = 18
        return getFileTaskList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return {data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0}
            } else {
              return res.Data
            }
          } else {
            return {data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0}
          }
        })
      }
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.creator = email
    this.getUserBaseInfo()
  },
  unmounted() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    handleSearch(val) {
      this.valueData = val
      if (!val) {
        this.getUserBaseInfo()
      } else {
        this.principalListMini = []
        this.scrollPage = 1
        this.principalList.forEach(item => {
          if (item.label.indexOf(val) >= 0) {
            this.principalListMini.push(item)
          }
        })
        this.principalListMini = this.principalListMini.slice(0, 50)
      }
    },
    getUserBaseInfo() {
      getUserEmailList().then(res => {
        for (let i = 0, len = res.Data.items.length; i < len; i++) {
          const user = {}
          user.value = res.Data.items[i].key
          user.label = res.Data.items[i].key + '(' + res.Data.items[i].value + ')'
          this.principalList.push(user)
          if (i <= 50) {
            this.principalListMini.push(user)
          }
        }
      })
    },
    handlePopupScroll(e) {
      const {target} = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.principalList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.principalList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.principalListMini = newData
        }
      }
    },
    filterOption: filterLabelValue,
    showExportHistory() {
      this.exportHistoryVisible = true
      this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
    },
    handleExport() {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      exportCombatPlatform(requestParameters)
        .then(res => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '导出成功，点击右侧导出记录按钮，查看导出结果'
            })
            this.confirmLoadingExport = false
          } else {
            notification.error({
              message: '导出失败'
            })
            this.confirmLoadingExport = false
          }
        })
        .catch((error) => {
          let res = error.response.data.message
          notification.error({
            message: '导出失败',
            description: res
          })
          this.confirmLoadingExport = false
        })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    exportStatusFilter(type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter(type) {
      return exportStatusMap[type]?.color || type
    }
  }
}
</script>
