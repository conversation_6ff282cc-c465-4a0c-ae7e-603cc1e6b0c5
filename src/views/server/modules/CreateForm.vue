<template>
  <a-modal
    title="新建/详情"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
  >
    <a-spin :spinning="loading">
      <a-form :model="form" v-bind="formLayout" ref="formRef" :rules="rules">
        <!-- 检查是否有 id 并且大于0，大于0是修改。其他是新增，新增不显示主键ID -->
        <a-form-item v-show="model && model.id > 0" label="ID">
          <a-input v-model:value="form.id" disabled />
        </a-form-item>
        <a-form-item label="服务器名">
          <a-input v-model:value="form.hostname" />
        </a-form-item>
        <a-form-item label="UUID">
          <a-input v-model:value="form.uuid" />
        </a-form-item>
        <a-form-item label="IDC">
          <a-input v-model:value="form.idc" />
        </a-form-item>
        <a-form-item label="区域">
          <a-input v-model:value="form.region" />
        </a-form-item>
        <a-form-item label="系统">
          <a-input v-model:value="form.os" />
        </a-form-item>
        <a-form-item label="系统版本">
          <a-textarea v-model:value="form.osVersion" auto-size/>
        </a-form-item>
        <a-form-item label="内网IP">
          <a-input v-model:value="form.ip" />
        </a-form-item>
        <a-form-item label="公网IP">
          <a-input v-model:value="form.publicIp" />
        </a-form-item>
        <a-form-item label="出口IP">
          <a-input v-model:value="form.exportIp" />
        </a-form-item>
        <a-form-item label="物理机IP">
          <a-input v-model:value="form.hostIp" />
        </a-form-item>
        <a-form-item label="CPU核数">
          <a-input v-model:value="form.cpu" />
        </a-form-item>
        <a-form-item label="内存(G)">
          <a-input v-model:value="form.memory" />
        </a-form-item>
        <a-form-item label="磁盘信息">
          <a-textarea v-model:value="form.disks" auto-size/>
        </a-form-item>
        <a-form-item label="实例类型">
          <a-input v-model:value="form.instanceType" />
        </a-form-item>
        <a-form-item label="状态">
          <a-input v-model:value="form.status" />
        </a-form-item>
        <a-form-item label="事业部">
          <a-input v-model:value="form.org" />
        </a-form-item>
        <a-form-item label="部门">
          <a-input v-model:value="form.dep" />
        </a-form-item>
        <a-form-item label="环境">
          <a-input v-model:value="form.env" />
        </a-form-item>
        <a-form-item label="创建时间">
          <a-input v-model:value="form.createdAt" />
        </a-form-item>
        <a-form-item label="更新时间">
          <a-input v-model:value="form.updatedAt" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from 'lodash.pick'

// 表单字段
const fields = ['id', 'hostname', 'uuid', 'idc', 'region', 'os', 'osVersion', 'ip', 'publicIp', 'exportIp', 'hostIp', 'cpu', 'memory', 'disks', 'instanceType', 'status', 'org', 'dep', 'env', 'createdAt', 'updatedAt']

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    loading: {
      type: Boolean,
      default: () => false
    },
    model: {
      type: Object,
      default: () => null
    }
  },
  data () {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    }
    return {
      form: this.$form.createForm(this, 'formRef', {
        id: 0,
      }),
      rules: {
        hostname: [{ required: true, min: 5, message: '请输入至少五个字符！' }],
        uuid: [{ required: true, min: 5, message: '请输入至少五个字符！' }],
      },
    }
  },
  watch: {
    model: {
      handler() {
        this.model && this.form.setFieldsValue(pick(this.model, fields))
      },
    },
  },
  created () {
    // console.log('custom modal created')

    // 防止表单未注册
    // fields.forEach(v => this.form.getFieldDecorator(v))

    // 当 model 发生改变时，为表单设置值
    /*
    this.$watch('model', () => {
      this.model && this.form.setFieldsValue(pick(this.model, fields))
    })
    */
  }
}
</script>
