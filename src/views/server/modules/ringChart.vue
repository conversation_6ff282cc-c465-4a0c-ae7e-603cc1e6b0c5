<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-12 13:13:21
 * @FilePath: \cloud_web\src\views\list\modules\ringChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 100%" v-if="!chartData.length" />
    <div v-if="chartData.length" :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Pie } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
    customText: {
      type: String,
      default: '',
    },
  },
  async mounted() {
    let num = 0
    for (let i = 0; i < this.chartData.length; i++) {
      num += this.chartData[i][this.filedName[0]]
    }
    if (this.chartData.length) {
      this.pie = new Pie(this.compRef, {
        appendPadding: 10,
        data: this.chartData,
        angleField: this.filedName[0],
        colorField: this.filedName[1],
        radius: 0.75,
        innerRadius: 0.6,
        legend: 'bottom',
        label: {
          type: 'outer',
          offset: '-50%',
          content: '{value}',
          style: {
            textAlign: 'center',
            fontSize: 14,
          },
        },
        interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
        statistic: {
          // title: false,
          title: {
            offsetY: -8,
            content: this.customText,
            style: {
              fontSize: 16,
              fontWeight: '400',
            },
          },
          content: {
            offsetY: -4,
            content: JSON.stringify(num),
            style: {
              fontSize: 16,
              fontWeight: '400',
            },
          },
          // content: {
          //   style: {
          //     whiteSpace: 'pre-wrap',
          //     textOverflow: 'ellipsis',
          //   },
          //   content: this.customText + num,
          //   style: {
          //     fontSize: 16,
          //     fontWeight: '400',
          //   },
          // },
        },
      })

      this.pie.render()
    }
  },
  data() {
    return {
      pie: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        if (val && val.length) {
          if (this.pie) {
            this.pie.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    renderChart() {
      this.pie.changeData(this.chartData)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
