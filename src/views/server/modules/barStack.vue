<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-08 10:42:36
 * @FilePath: \cloud_web\src\views\list\modules\barStack.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <a-empty style="height: 100%" v-if="!chartData.length" />
    <div v-if="chartData.length" :ref="compRef" :id="compRef" style="height: 320px"></div>
  </div>
</template>
<script>
import { Column } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
    customText: {
      type: String,
      default: '',
    },
  },
  async mounted() {
    if (this.chartData.length) {
      this.bar = new Column(this.compRef, {
        isStack: true,
        data: this.chartData.reverse(),
        xField: this.filedName[1],
        yField: this.filedName[0],
        seriesField: this.filedName[2],
        label: {
          // 可手动配置 label 数据标签位置
          position: 'middle', // 'left', 'middle', 'right'
          // 可配置附加的布局方法
          layout: [
            // 柱形图数据标签位置自动调整
            { type: 'interval-adjust-position' },
            // 数据标签防遮挡
            { type: 'interval-hide-overlap' },
            // 数据标签文颜色自动调整
            { type: 'adjust-color' },
          ],
        },
      })

      this.bar.render()
    }
  },
  data() {
    return {
      bar: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        if (val && val.length) {
          if (this.bar) {
            this.bar.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    renderChart() {
      this.bar.changeData(this.chartData)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
