/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-06-28 14:06:17
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-07-02 17:04:38
 * @FilePath: \cloud_web\src\views\list\modules\assetCheckData.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
export const storageColumns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '主存储分配率',
    dataIndex: 'allocateRate',
    key: 'allocateRate',
  },
  {
    title: '主存储使用率',
    dataIndex: 'usageRate',
    key: 'usageRate',
  },
  {
    title: '主存储使用率GAP值',
    dataIndex: 'gapRate',
    key: 'gapRate',
  },
]
export const cpuColumns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: 'cpu分配率',
    dataIndex: 'allocateRate',
    key: 'allocateRate',
  },
  {
    title: 'cpu使用率',
    dataIndex: 'usageRate',
    key: 'usageRate',
  },
  {
    title: 'cpu使用率GAP值',
    dataIndex: 'gapRate',
    key: 'gapRate',
  },
]
export const memColumns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '内存分配率',
    dataIndex: 'allocateRate',
    key: 'allocateRate',
  },
  {
    title: '内存使用率',
    dataIndex: 'usageRate',
    key: 'usageRate',
  },
  {
    title: '内存使用率GAP值',
    dataIndex: 'gapRate',
    key: 'gapRate',
  },
]
export const ssdColumns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '固态硬盘分配率',
    dataIndex: 'allocateRate',
    key: 'allocateRate',
  },
]
export const hddColumns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '机械硬盘分配率',
    dataIndex: 'allocateRate',
    key: 'allocateRate',
  },
]
export const netColumns = [
  {
    title: '物理机',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '上传(kb/s)',
    dataIndex: 'networkUpload',
    key: 'networkUpload',
  },
  {
    title: '下载(kb/s)',
    dataIndex: 'networkDownload',
    key: 'networkDownload',
  },
]
