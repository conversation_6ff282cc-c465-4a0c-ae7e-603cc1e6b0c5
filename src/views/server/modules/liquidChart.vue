<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-06-27 15:54:11
 * @FilePath: \cloud_web\src\views\list\modules\liquidChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <div v-if="chartData.rate" :ref="compRef" :id="compRef" style="height: 260px"></div>
  </div>
</template>
<script>
import { Liquid } from '@antv/g2plot'

export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
        return {}
      },
    },
    compRef: {
      type: String,
      default: '',
    },
    filedName: {
      type: Array,
      default: () => [],
    },
    customText: {
      type: String,
      default: '',
    },
  },
  async mounted() {
    if (this.chartData.rate) {
      this.liquidPlot = new Liquid(this.compRef, {
        percent: this.chartData.rate / 100,
        outline: {
          border: 2,
          distance: 4,
        },
        radius: 0.7,
        wave: {
          length: 128,
        },
        statistic: {
          title: {
            formatter: () => '已使用',
            style: ({ percent }) => {
              console.log(percent)
              return {
                fill: 'rgba(0,0,0,.85)',
              }
            },
          },
          content: {
            style: ({ percent }) => ({
              fontSize: 28,
              fill: 'rgba(0,0,0,.85)',
            }),
          },
        },
      })

      this.liquidPlot.render()
    }
  },
  data() {
    return {
      liquidPlot: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (val, v2) {
        if (val && val.length) {
          if (this.liquidPlot) {
            this.liquidPlot.changeData(val)
          } else {
            this.renderChart(val)
          }
        }
      },
    },
  },
  methods: {
    renderChart() {
      this.liquidPlot.changeData(this.chartData)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
