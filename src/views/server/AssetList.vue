<template>
  <page-header-wrapper>
    <!-- <template v-slot:content>
      <tx-button icon="solution" size="small">
        <a
          href="https://doc.intsig.net/pages/viewpage.action?pageId=899285338"
          target="_blank"
          style="text-decoration: none"
        >
          参考文档
        </a>
      </tx-button>
    </template> -->
    <a-card :bordered="false">
      <!-- <div class="table-page-search-wrapper" style="margin-bottom: 16px">
        筛选：
        <a-select show-search v-model:value="filterType" allowClear style="width: 160px" placeholder="查询项">
          <a-select-option v-for="i in filterOptions" :key="i.dataIndex" :value="JSON.stringify(i)">
            {{ i.title }}
          </a-select-option>
        </a-select>
        <a-select v-model:value="conditions" style="width: 120px" placeholder="条件">
          <a-select-option v-for="i in conditionOptions" :key="i.value" :value="JSON.stringify(i)">
            {{ i.label }}
          </a-select-option>
        </a-select>
        <a-input style="width: 200px" @pressEnter="handleSearch" v-model:value="filterContent" placeholder="查询内容" />
        <a-select v-model:value="serverStatus" style="width: 140px" placeholder="服务器状态">
          <a-select-option v-for="i in statusArr" :key="i.value" :value="i.value">
            {{ i.label }}
          </a-select-option>
        </a-select>
        <a-button @click="handleSearch" type="primary" style="margin-left: 8px">查询</a-button>
        <a-button @click="reset" type="primary" style="margin-left: 8px">重置</a-button>
      </div> -->
      <!-- <div style="margin-bottom: 16px">
        <span v-if="filter.length">当前查询条件：</span>
        <template v-for="i in filter">
          <a-tag closable v-if="filter.length" @close.prevent="closeTag(i)">
            <b>{{ i.key.title }}</b>
            &nbsp {{ i.relation.sign }} &nbsp{{ i.value }}
          </a-tag>
        </template>
      </div> -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.searchKey"
                  placeholder="服务器名/IP/公网IP/物理机IP/UUID"
                  @pressEnter="handleSearch"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="IDC">
                <a-select
                  v-model:value="queryParam.idc"
                  placeholder="请选择IDC"
                  allowClear
                  showSearch
                  :options="assetIdcList"
                ></a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="状态">
                  <a-select v-model:value="queryParam.status" allowClear placeholder="请选择">
                    <a-select-option value="0">全部</a-select-option>
                    <a-select-option value="1">运行中</a-select-option>
                    <a-select-option value="2">已关闭</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="事业部">
                  <a-select
                    v-model:value="queryParam.org"
                    allowClear
                    showSearch
                    placeholder="请选择事业部"
                    :options="assetOrgList"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="部门">
                  <a-select
                    v-model:value="queryParam.dep"
                    allowClear
                    showSearch
                    placeholder="请选择部门"
                    :options="assetDepList"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="负责人模糊查询">
                  <a-input
                    v-model:value="queryParam.principalEmail"
                    placeholder="邮箱/姓名"
                    @pressEnter="handleSearch"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="IP">
                  <a-input v-model:value="queryParam.ip" placeholder="精确匹配" @pressEnter="handleSearch"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="物理机IP">
                  <a-input v-model:value="queryParam.hostIp" placeholder="精确匹配" @pressEnter="handleSearch"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="费用负责人">
                  <a-select
                    v-model:value="queryParam.costUser"
                    allowClear
                    showSearch
                    placeholder="请选择"
                    :options="assetCostUserList"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="显卡类型">
                  <a-select
                    v-model:value="queryParam.gpuType"
                    allowClear
                    showSearch
                    placeholder="请选择"
                    :options="assetGpuTypeList"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="HIDS状态">
                  <a-select v-model:value="queryParam.hidsStatus" allowClear placeholder="请选择">
                    <a-select-option value="10">在线</a-select-option>
                    <a-select-option value="11">离线</a-select-option>
                    <a-select-option value="12">停用</a-select-option>
                    <a-select-option value="2">未安装</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="系统类型">
                  <a-select v-model:value="queryParam.os" allowClear placeholder="请选择">
                    <a-select-option value="linux">Linux</a-select-option>
                    <a-select-option value="windows">Windows</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="handleSearch">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
        <tx-button v-if="userpRression" type="primary" icon="plus" @click="handleAddEntry">手动录入</tx-button>
        <tx-button type="primary" icon="download" :loading="downloadLoading" @click="handleDownload">导出</tx-button>
        <tx-button style="float: right" icon="setting" @click="customColumn">自定义列</tx-button>
        <a-dropdown v-action:edit v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="handleDelMore">
                <a-icon type="delete"/>
                注销
              </a-menu-item>
            </a-menu>
          </template>
          <tx-button style="margin-left: 8px">
            批量操作
            <a-icon type="down"/>
          </tx-button>
        </a-dropdown>
      </div>
      <a-modal v-model:visible="setColumnsVisible" width="400px" title="自定义表格列" @ok="confirmColumns">
        <ul style="width: 300px">
          <li
            style="width: 100%; display: flex; justify-content: space-between; margin-bottom: 8px"
            v-for="i in allColumns"
            :key="i.dataIndex"
            :value="i.dataIndex"
          >
            <span>{{ i.title }}</span>

            <p style="margin-right: 0" class="toRight">
              <a-switch v-model:checked="i.isChecked"/>
              <span
                @click="adjustPos('down', i)"
                style="margin-right: 8px; cursor: pointer; font-size: 16px; margin-left: 8px"
              >
                <arrow-down-outlined/>
              </span>
              <span @click="adjustPos('up', i)" style="cursor: pointer; font-size: 16px">
                <arrow-up-outlined/>
              </span>
            </p>
          </li>
        </ul>
      </a-modal>
      <!-- <p>服务器负责人可直接在表格中修改“环境”、“应用”、“项目”、“备注”</p> -->
      <a-table
        id="myAssetTable"
        :rowKey="record => record.id"
        :columns="userColumns"
        :data-source="assetData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'hostname' && record.grafanaUrl != ''">
            {{ record.hostname }}
            <router-link style="color: #1890ff" target="_blank" :to="`/db/server-monitor?id=${record.id}&type=1`">
              <area-chart-outlined/>
            </router-link>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)"/>
          </template>
          <template v-if="column.dataIndex == 'project'">
            <div class="editable-cell" v-if="record.principalEmails.includes(localUser) && record.deletedAt == ''">
              <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
                <!-- <a-input v-model:value="record.project" @pressEnter="upDateSelfAsset(record)" /> -->
                <x-select
                  @change="projectChange($event, record)"
                  mode="tags"
                  v-model:value="record.project"
                  :options="projectList"
                  placeholder="项目可输入或选择"
                  style="width: 140px"
                ></x-select>
                <check-outlined class="editable-cell-icon-check" @click="upDateSelfAsset(record)"/>
              </div>
              <div v-if="!editableData[record.id]" class="editable-cell-text-wrapper">
                {{ record.project || ' ' }}
                <edit-outlined class="editable-cell-icon" @click="edit(record.id)"/>
              </div>
            </div>
            <span v-if="!record.principalEmails.includes(localUser)">{{ record.project }}</span>
          </template>
          <template v-if="column.dataIndex === 'env'">
            <div class="editable-cell" v-if="record.principalEmails.includes(localUser) && record.deletedAt == ''">
              <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
                <!-- <a-input v-model:value="record.env" @pressEnter="upDateSelfAsset(record)" /> -->
                <a-select ref="select" v-model:value="record.env" style="width: 120px">
                  <a-select-option value="test">测试环境</a-select-option>
                  <a-select-option value="online">生产环境</a-select-option>
                  <a-select-option value="pre">预发布环境</a-select-option>
                  <a-select-option value="dev">开发环境</a-select-option>
                </a-select>
                <check-outlined class="editable-cell-icon-check" @click="upDateSelfAsset(record)"/>
              </div>
              <div v-if="!editableData[record.id]" class="editable-cell-text-wrapper">
                {{ envList[record.env] || ' ' }}
                <edit-outlined class="editable-cell-icon" @click="edit(record.id)"/>
              </div>
            </div>
            <span v-if="!record.principalEmails.includes(localUser)">{{ envList[record.env] }}</span>
          </template>
          <template v-if="column.dataIndex === 'application'">
            <div class="editable-cell" v-if="record.principalEmails.includes(localUser) && record.deletedAt == ''">
              <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
                <a-tooltip>
                  <template #title>不同应用请使用“|”分隔</template>
                  <question-circle-outlined/>
                </a-tooltip>
                <a-input
                  style="width: 90%; margin-left: 4px"
                  v-model:value="record.application"
                  @pressEnter="upDateSelfAsset(record)"
                />
                <check-outlined class="editable-cell-icon-check" @click="upDateSelfAsset(record)"/>
              </div>
              <div v-if="!editableData[record.id]" class="editable-cell-text-wrapper">
                {{ record.application || ' ' }}
                <edit-outlined class="editable-cell-icon" @click="edit(record.id)"/>
              </div>
            </div>
            <span v-if="!record.principalEmails.includes(localUser)">{{ record.application }}</span>
          </template>
          <template v-if="column.dataIndex === 'comment'">
            <div class="editable-cell" v-if="record.principalEmails.includes(localUser) && record.deletedAt == ''">
              <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
                <a-input v-model:value="record.comment" @pressEnter="upDateSelfAsset(record)"/>
                <check-outlined class="editable-cell-icon-check" @click="upDateSelfAsset(record)"/>
              </div>
              <div v-if="!editableData[record.id]" class="editable-cell-text-wrapper">
                {{ record.comment || ' ' }}
                <edit-outlined class="editable-cell-icon" @click="edit(record.id)"/>
              </div>
            </div>
            <span v-if="!record.principalEmails.includes(localUser)">{{ record.comment }}</span>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a @click="handleDetail(record)">详情</a>
            <a-divider
              v-if="
                (userpRression ||
                  record.costUser.includes(localUser) ||
                  record.userEmails.includes(localUser) ||
                  record.principalEmails.includes(localUser)) &&
                record.status === 2
              "
              type="vertical"
            />
            <a-dropdown
              v-if="
                (userpRression ||
                  record.costUser.includes(localUser) ||
                  record.userEmails.includes(localUser) ||
                  record.principalEmails.includes(localUser)) &&
                record.status === 2
              "
            >
              <template #overlay>
                <a-menu>
                  <a-menu-item><a @click="handleEditTag(record)">标签变更</a></a-menu-item>
                </a-menu>
              </template>
              <a>
                变更
                <a-icon type="down"/>
              </a>
            </a-dropdown>
            <a-divider type="vertical"/>
            <a-dropdown
              v-if="
                (userpRression ||
                  record.costUser.includes(localUser) ||
                  record.userEmails.includes(localUser) ||
                  record.principalEmails.includes(localUser)) &&
                record.status !== 2
              "
            >
              <template #overlay>
                <a-menu>
                  <a-menu-item><a @click="handleEditConfig(record)">配置变更</a></a-menu-item>
                  <a-menu-item><a @click="handleEditTag(record)">标签变更</a></a-menu-item>
                  <a-menu-item><a @click="handleDel(record)">注销</a></a-menu-item>
                </a-menu>
              </template>
              <a>
                变更
                <a-icon type="down"/>
              </a>
            </a-dropdown>
            <a-divider
              v-if="
                record.userEmails.includes(localUser) &&
                (record.idc === 'ucloud-shanghai2-hybrid' ||
                  record.idc === 'aliyun-qxb-cn-hangzhou' ||
                  record.idc.startsWith('aws'))
              "
              type="vertical"
            />
            <a-dropdown
              :trigger="['click']"
              :visible="dropdownVisible[record.id]"
              @visible-change="changeDropdownVisible($event, record.id)"
              ref="assetDropdown"
              v-if="
                record.userEmails.includes(localUser) &&
                (record.idc === 'ucloud-shanghai2-hybrid' ||
                  record.idc === 'aliyun-qxb-cn-hangzhou' ||
                  record.idc.startsWith('aws'))
              "
            >
              <template #overlay>
                <a-menu>
                  <a-menu-item :disabled="record.status !== 2">
                    <a-popconfirm
                      title="您确定要开机吗?"
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm.stop="handleSubmitKvmOperate(record, 'start', false)"
                    >
                      <a @click.stop="keepVisible(record.id)" href="#">开机</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item :disabled="record.status !== 1">
                    <a-popconfirm
                      title="您确定要关机吗?"
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm="handleSubmitKvmOperate(record, 'shutdown', false)"
                    >
                      <a @click="keepVisible(record.id)" href="#">关机</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item :disabled="record.status !== 1">
                    <a-popconfirm
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm="handleSubmitKvmOperate(record, 'shutdown', true)"
                    >
                      <template #title>
                        <span style="color: red; font-weight: bold">您确定要强制关机吗?</span>
                      </template>
                      <a @click="keepVisible(record.id)" href="#">强制关机</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item :disabled="record.status === 2">
                    <a-popconfirm
                      title="您确定要重启吗?"
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm="handleSubmitKvmOperate(record, 'reboot', false)"
                    >
                      <a @click="keepVisible(record.id)" href="#">重启</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item :disabled="record.status === 2">
                    <a-popconfirm
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm="handleSubmitKvmOperate(record, 'reboot', true)"
                    >
                      <template #title>
                        <span style="color: red; font-weight: bold">您确定要强制重启吗?</span>
                      </template>
                      <a @click="keepVisible(record.id)" href="#">强制重启</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-sub-menu
                    key="backup"
                    title="快照与镜像"
                    v-if="record.userEmails.includes(localUser) && record.idc === 'ucloud-shanghai2-hybrid'"
                  >
                    <a-menu-item>
                      <a @click="handleCreateAssetImage(record)" href="#">创建镜像</a>
                    </a-menu-item>
                    <a-menu-item disabled>创建快照</a-menu-item>
                  </a-sub-menu>
                </a-menu>
              </template>
              <a>
                操作
                <a-icon type="down"/>
              </a>
            </a-dropdown>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="区域">{{ record.region }}</a-descriptions-item>
            <a-descriptions-item label="物理机IP">{{ record.hostIp }}</a-descriptions-item>
            <a-descriptions-item label="IPV6">{{ detailData.ipv6 }}</a-descriptions-item>
            <a-descriptions-item label="唯一标识">{{ record.uuid }}</a-descriptions-item>
            <a-descriptions-item label="系统类型">{{ record.os }}</a-descriptions-item>
            <a-descriptions-item label="系统版本">{{ record.osVersion }}</a-descriptions-item>
            <a-descriptions-item label="实例类型">{{ record.instanceType }}</a-descriptions-item>
            <a-descriptions-item label="GPU类型">{{ record.gpuType }}</a-descriptions-item>
            <a-descriptions-item label="磁盘信息">{{ record.disks }}</a-descriptions-item>
            <a-descriptions-item label="是否需要监控">{{ record.prometheusStatus }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
            <a-descriptions-item label="负责人">{{ record.principal }}</a-descriptions-item>
            <a-descriptions-item label="费用负责人">{{ record.costUser }}</a-descriptions-item>
            <a-descriptions-item label="费用承担">{{ record.business }}</a-descriptions-item>
            <a-descriptions-item label="费用类型">{{ costTypeStatusFilter(record.business, record.dgDataAsset) }}</a-descriptions-item>
            <a-descriptions-item label="使用人邮箱" :span="3">{{ record.userEmails }}</a-descriptions-item>
            <a-descriptions-item label="事业部">{{ record.org }}</a-descriptions-item>
            <a-descriptions-item label="部门">{{ record.dep }}</a-descriptions-item>
            <a-descriptions-item label="项目">{{ record.project }}</a-descriptions-item>
            <a-descriptions-item label="团队">{{ record.team }}</a-descriptions-item>
            <a-descriptions-item label="应用">{{ record.application }}</a-descriptions-item>
            <a-descriptions-item label="环境">{{ record.env }}</a-descriptions-item>
            <a-descriptions-item label="上月费用">{{ record.costLastMonth }}</a-descriptions-item>
            <a-descriptions-item label="上上月费用">{{ record.costMonthAfterLast }}</a-descriptions-item>
            <a-descriptions-item label="HIDS状态">{{ hidsStatusFilter(record.hidsStatus) }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
            <a-descriptions-item v-if="record.status === 2" label="关机时间">
              {{ record.dateShutdown }}
            </a-descriptions-item>
            <a-descriptions-item v-if="record.status === 2" label="下线时间">
              {{ record.offlineTime }}
            </a-descriptions-item>
            <a-descriptions-item label="产品线" :span="3">{{ record.productLine }}</a-descriptions-item>
            <a-descriptions-item label="DG数据资产">{{ record.dgDataAsset }}</a-descriptions-item>
            <a-descriptions-item label="资产归属">{{ record.ownershipAsset }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </a-table>
      <create-form
        ref="createModal"
        :visible="visible"
        :loading="confirmLoading"
        :model="mdl"
        @cancel="handleCancel"
        @ok="handleOk"
      />
      <step-by-step-modal ref="modal" @ok="handleOk"/>
    </a-card>
    <a-drawer
      title="详情"
      placement="right"
      :closable="false"
      width="40%"
      :visible="detailIdVisible"
      @close="detailIdVisible = false"
    >
      <a-card :bordered="false" :model="detailData" ref="detailData">
        <a-descriptions :column="2" title="基础信息">
          <a-descriptions-item label="服务器名">{{ detailData.hostname }}</a-descriptions-item>
          <a-descriptions-item label="IDC">{{ detailData.idc }}</a-descriptions-item>
          <a-descriptions-item label="公网IP">{{ detailData.publicIp }}</a-descriptions-item>
          <a-descriptions-item label="IP">{{ detailData.ip }}</a-descriptions-item>
          <a-descriptions-item label="IPV6">{{ detailData.ipv6 }}</a-descriptions-item>
          <a-descriptions-item label="区域">{{ regionFilter(detailData.region) }}</a-descriptions-item>
          <a-descriptions-item label="物理机IP">{{ detailData.hostIp }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge :status="statusTypeFilter(detailData.status)" :text="statusFilter(detailData.status)"/>
          </a-descriptions-item>
          <a-descriptions-item label="出口IP">{{ detailData.exportIp }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions>
          <a-descriptions-item label="唯一标识">{{ detailData.uuid }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="2">
          <a-descriptions-item label="创建时间">{{ detailData.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ detailData.updatedAt }}</a-descriptions-item>
          <a-descriptions-item v-if="detailData.status === 2" label="关机时间">
            {{ detailData.dateShutdown }}
          </a-descriptions-item>
          <a-descriptions-item v-if="detailData.status === 2" label="下线时间">
            {{ detailData.offlineTime }}
          </a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 12px"/>
        <a-descriptions :column="2" title="配置信息">
          <a-descriptions-item label="CPU">{{ detailData.cpu }} 核</a-descriptions-item>
          <a-descriptions-item label="实例类型">{{ detailData.instanceType }}</a-descriptions-item>
          <a-descriptions-item label="内存">{{ detailData.memory }} GB</a-descriptions-item>
          <a-descriptions-item label="显卡数量">{{ detailData.graphicsCardCount }}</a-descriptions-item>
          <a-descriptions-item label="系统类型">{{ detailData.os }}</a-descriptions-item>
          <a-descriptions-item label="系统版本">{{ detailData.osVersion }}</a-descriptions-item>
          <a-descriptions-item label="磁盘信息">{{ detailData.disks }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 12px"/>
        <a-descriptions :column="2" title="费用信息">
          <a-descriptions-item label="费用负责人">{{ detailData.costUser }}</a-descriptions-item>
          <a-descriptions-item label="费用承担业务方">{{ detailData.business }}</a-descriptions-item>
          <a-descriptions-item label="费用类型">{{ costTypeStatusFilter(detailData.business, detailData.dgDataAsset) }}</a-descriptions-item>
          <a-descriptions-item label="上月费用">￥{{ detailData.costLastMonth }}</a-descriptions-item>
          <a-descriptions-item label="上上月费用">￥{{ detailData.costMonthAfterLast }}</a-descriptions-item>
          <a-descriptions-item label="产品线">{{ detailData.productLine }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 12px"/>
        <a-descriptions :column="2" title="标签信息">
          <a-descriptions-item label="事业部">{{ detailData.org }}</a-descriptions-item>
          <a-descriptions-item label="部门">{{ detailData.dep }}</a-descriptions-item>
          <a-descriptions-item label="项目">{{ detailData.project }}</a-descriptions-item>
          <a-descriptions-item label="团队">{{ detailData.team }}</a-descriptions-item>
          <a-descriptions-item label="环境">{{ envFilter(detailData.env) }}</a-descriptions-item>
          <a-descriptions-item label="应用">{{ detailData.application }}</a-descriptions-item>
          <a-descriptions-item label="资产归属">{{ detailData.ownershipAsset }}</a-descriptions-item>
          <a-descriptions-item label="负责人">{{ detailData.principal }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="1">
          <a-descriptions-item label="使用人">{{ detailData.userEmails }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 12px"/>
        <a-divider style="margin-bottom: 12px"/>
        <a-descriptions :column="2" title="其他信息">
          <a-descriptions-item label="镜像备份">
            <div v-if="detailData.imageBackup">
              <a-badge status="success" text="是"/>
            </div>
            <div v-else>
              <a-badge status="error" text="否"/>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="数据库备份">
            <div v-if="detailData.dbBackup">
              <a-badge status="success" text="是"/>
            </div>
            <div v-else>
              <a-badge status="error" text="否"/>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="是否需要监控">
            <div v-if="detailData.prometheusStatus">
              <a-badge status="success" text="是"/>
            </div>
            <div v-else>
              <a-badge status="error" text="否"/>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="是否为GPU机器">
            <div v-if="detailData.isGpu">
              <a-badge status="success" text="是"/>
            </div>
            <div v-else>
              <a-badge status="error" text="否"/>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="云终端二次验证">
            <div v-if="detailData.mfa === 2">
              <a-badge status="error" text="关闭"/>
            </div>
            <div v-else>
              <a-badge status="success" text="开启"/>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="连接方式">
            <div v-if="detailData.accessMethod === 1">
              <a-badge status="warning" text="公网"/>
            </div>
            <div v-else>
              <a-badge status="success" text="内网"/>
            </div>
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions>
          <a-descriptions-item label="备注">{{ detailData.comment }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 12px"/>
      </a-card>
    </a-drawer>
    <a-modal
      title="服务器手动录入"
      :visible="serverAddEntryVisible"
      @ok="confirmServerAddEntry"
      width="800px"
      @cancel="serverAddEntryVisible = false"
    >
      <a-form-model
        ref="orderForm"
        :rules="serverRules"
        :model="serverTemp"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="服务器名" name="hostname">
          <a-input v-model:value="serverTemp.hostname"/>
        </a-form-model-item>
        <a-form-model-item label="服务器IP" name="ip">
          <a-input v-model:value="serverTemp.ip"/>
        </a-form-model-item>
        <a-form-model-item label="机房" name="idc">
          <a-select v-model:value="serverTemp.idc" placeholder="请选择机房">
            <a-select-option value="ucloud-shanghai2-hybrid">UCloud混合云</a-select-option>
            <a-select-option value="ucloud-shanghai2-public">UCloud云主机</a-select-option>
            <a-select-option value="ucloud-shanghai1-zhuanqiao">UCloud颛桥</a-select-option>
            <a-select-option value="shanghai8-songjiang">松江机房</a-select-option>
            <a-select-option value="aws-cn-north-1">AWS北京(cn-north-1)</a-select-option>
            <a-select-option value="aws-cn-northwest-1">AWS宁夏(cn-northwest-1)</a-select-option>
            <a-select-option value="aws-eu-west-1">AWS欧洲(爱尔兰)(eu-west-1)</a-select-option>
            <a-select-option value="aws-us-west-1">AWS美国西部(加利福尼亚北部)(us-west-1)</a-select-option>
            <a-select-option value="aws-us-west-2">AWS美国西部(俄勒冈)(us-west-2)</a-select-option>
            <a-select-option value="aws-ap-east-1">AWS亚太地区(香港)(ap-east-1)</a-select-option>
            <a-select-option value="aws-ap-south-1">AWS亚太地区(孟买)(ap-south-1)</a-select-option>
            <a-select-option value="aws-ap-southeast-1">AWS亚太地区(新加坡)(ap-southeast-1)</a-select-option>
            <a-select-option value="aws-ap-northeast-1">AWS亚太地区(东京)(ap-northeast-1)</a-select-option>
            <a-select-option value="aliyun-hehe-cn-hangzhou">阿里云(合合)</a-select-option>
            <a-select-option value="sh-office">上海云立方</a-select-option>
            <a-select-option value="aliyun-qxb-cn-hangzhou">阿里云(启信宝)</a-select-option>
            <a-select-option value="tencent-shanghai">腾讯云上海区域</a-select-option>
            <a-select-option value="tencent-tokyo">腾讯云东京区域</a-select-option>
            <a-select-option value="azure-cn-chinaeast2">微软云上海2</a-select-option>
            <a-select-option value="huawei-intsig">华为云(合合)</a-select-option>
            <a-select-option value="huawei-camscanner">华为云(CS)</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="服务器ID" name="key">
          <a-input v-model:value="serverTemp.key"/>
        </a-form-model-item>
        <a-form-model-item label="系统" name="os">
          <a-select v-model:value="serverTemp.os" placeholder="请选择系统">
            <a-select-option value="windows">windows</a-select-option>
            <a-select-option value="linux">linux</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="CPU" name="cpu">
          <a-input v-model:value="serverTemp.cpu"/>
        </a-form-model-item>
        <a-form-model-item label="内存(G)" name="memory">
          <a-input v-model:value="serverTemp.memory"/>
        </a-form-model-item>
        <a-form-model-item label="物理机IP">
          <a-input v-model:value="serverTemp.hostIp"/>
        </a-form-model-item>
        <a-form-model-item label="负责人" name="principalEmails">
          <a-input v-model:value="serverTemp.principalEmails"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="镜像备份"
      :visible="createAssetImagedVisible"
      width="800px"
      @cancel="createAssetImagedVisible = false"
    >
      <a-alert message="您创建的镜像将保留30天后删除!" style="margin-bottom: 15px" type="info" :show-icon="true"/>
      <a-form-model
        ref="orderForm"
        :rules="assetImageRules"
        :model="assetImageTemp"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="镜像名" name="imageName">
          <a-input v-model:value="assetImageTemp.imageName"/>
        </a-form-model-item>
        <a-form-model-item label="备注">
          <a-textarea v-model:value="assetImageTemp.comment" placeholder="备注信息"/>
        </a-form-model-item>
        <a-form-model-item label="一次性备份">
          <a-radio-group v-model:value="assetImageTemp.backupType" button-style="solid">
            <a-radio-button :value="3">否</a-radio-button>
            <a-radio-button :value="2">是</a-radio-button>
          </a-radio-group>
          <a-tooltip :overlayStyle="{ width: '300px' }">
            <template #title>
              <span v-html="'一次性备份的镜像将不会被自动删除</span>'"/>
            </template>
            <a-icon style="margin-left: 8px; font-size: 20px; color: blue" theme="filled" type="question-circle"/>
          </a-tooltip>
        </a-form-model-item>
      </a-form-model>
      <template #footer>
        <a-button key="back" @click="createAssetImagedVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="backupLoading" @click="confirmCreateAssetImage">确认</a-button>
      </template>
    </a-modal>

<!--    二次认证-->
    <a-modal v-model:visible="captchaVisible" width="30%" title="二次验证" centered :footer="null">
      <div style="justify-content: center;display: flex; align-items: center;"><p style="float:left;color: red">敏感操作需要二次验证</p></div>
      <SecondaryVerification v-if="captchaVisible" @verification-success="confirmKvmOperate()"/>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import {filterLabelValue} from '@aim/helper'
import moment from 'moment'
import {getAssetListProject} from '@/api/asset'

import {loadXLSX} from '@/utils/vendorLoader'
import {Ellipsis, STable} from '@/components'
import {removeWatermark, setWaterMark, waterMarkVs} from '@/utils/watermark'
import store from '@/store'
import StepByStepModal from './modules/StepByStepModal.vue'
import CreateForm from './modules/CreateForm.vue'
import {
  assetMonitor,
  createServerAddEntry,
  getAssetInfo,
  getAssetList,
  getAssetListCostUser,
  getAssetListDepartment,
  getAssetListIdc,
  getAssetListOrganization,
  kvmOperate,
  getAssetListGpuType,
  assetFilter,
  columnUpdate,
  columnList,
  customUserAssets,
} from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'
import {approveOrder} from '@/api/workflow/order'
import {notification} from 'ant-design-vue'
import {getUserList} from '@/api/permission/user'
import {MessageOutlined} from '@ant-design/icons-vue'
import {createAssetImages} from '@/api/asset/assetImages'
import {Modal} from 'ant-design-vue'
import SecondaryVerification from "@/components/Verification/verify.vue";
import {globalOtp} from "@/api/commonVerify";
import {myColumns} from "@/views/ai/aiData";


const operation = ['CC', 'CS-CC', 'CS', 'DG', 'SSG', '搜索(DG)', '大数据', '市场', '招聘项目组', '营销云', '战略合作', '总裁办']
const statusMap = {
  0: {
    status: 'processing',
    text: '关闭',
  },
  1: {
    status: 'success',
    text: '运行中',
  },
  2: {
    status: 'error',
    text: '已关机',
  },
  3: {
    status: 'processing',
    text: '关机中',
  },
  4: {
    status: 'processing',
    text: '开机中',
  },
  5: {
    status: 'processing',
    text: '重启中',
  },
  10: {
    status: 'processing',
    text: '准备中',
  },
}

const envMap = {
  dev: '开发环境',
  test: '测试环境',
  pre: '预发布环境',
  online: '生产环境',
}
const regionMap = {
  'cn-hangzhou': '中国-杭州',
  'cn-shanghai': '中国-上海',
  'cn-hongkong': '中国-香港',
  'ap-shanghai-1': '上海一区',
  'ap-shanghai-2': '上海二区',
  'ap-shanghai-3': '上海三区',
  'ap-shanghai-4': '上海四区',
  'ap-shanghai-5': '上海五区',
  'ap-east-1': '亚太-香港',
  'ap-northeast-1': '亚太-东京',
  'ap-south-1': '亚太-孟买',
  'ap-southeast-1': '亚太-新加坡',
  'cn-north-1': '中国-北京',
  'cn-northeast-1': '中国-宁夏',
  'eu-west-1': '欧洲-爱尔兰',
  'sa-east-1': '南美-圣保罗',
  'us-west-1': '美西-加利福尼亚',
  'us-west-2': '美西-俄亥俄',
}
const statusArr = [
  {label: '运行中', value: '1'},
  {label: '已关机', value: '2'},
  {label: '关机中', value: '3'},
  {label: '开机中', value: '4'},
  {label: '重启中', value: '5'},
  {label: '全部', value: '0'},
]
const envList = {
  test: '测试环境',
  online: '生产环境',
  pre: '预发布环境',
  dev: '开发环境',
}
const conditionOptions = [
  {label: '等于', value: 'equal', sign: '='},
  {label: '包含', value: 'like', sign: ':'},
  {label: '不等于', value: 'notEqual', sign: '!='},
  {label: '不包含', value: 'notLike', sign: '!:'},
]

export default {
  name: 'TableList',
  components: {
    SecondaryVerification,
    Ellipsis,
    CreateForm,
    StepByStepModal,
    MessageOutlined,
  },
  data() {
    this.downloadtableList = null
    this.operation = operation
    return {
      envList,
      projectList: [],
      editableData: {},
      allColumns: [],
      setColumnsVisible: false,
      captchaVisible: false,
      statusArr,
      serverStatus: '1',
      filter: [],
      filterContent: '',
      conditionOptions,
      conditions: JSON.stringify({label: '包含', value: 'like', sign: ':'}),
      filterOptions: [],
      userColumns: [],
      filterType: null,
      idcArr: ['ucloud-shanghai2-hybrid'],
      localUser: store.getters.email,
      serverAddEntryVisible: false,
      pagination: {},
      dropdownVisible: {},
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      assetGpuTypeList: [],
      assetData: [],
      // create model
      visible: false,
      loading: false,
      confirmLoading: false,
      backupLoading: false,
      downloadLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {pageNo: 1, pageSize: 10, status: '1', searchKey: undefined},
      // 加载数据方法 必须为 Promise 对象
      selectedRowKeys: [],
      selectedRows: [],
      assetImageDetail: {},
      detailData: {
        id: '',
        hostname: '',
        uuid: '',
        idc: '',
        region: '',
        os: '',
        osVersion: '',
        ip: '',
        ipv6: '',
        publicIp: '',
        exportIp: '',
        hostIp: '',
        cpu: '',
        memory: '',
        disks: '',
        instanceType: '',
        status: 0,
        org: '',
        dep: '',
        env: '',
        principal: '',
        principalEmails: '',
        mfa: '',
        createdAt: '',
        updatedAt: '',
      },
      detailIdVisible: false,
      createAssetImagedVisible: false,
      userpRression: false,
      userRoles: [],
      userRolesWhite: 'opsAdmin',
      serverRules: antdFormRulesFormat({
        hostname: [{required: true, message: '填写服务器名', trigger: 'blur'}],
        ip: [{required: true, message: '填写服务器IP', trigger: 'blur'}],
        idc: [{required: true, message: '请选择机房信息', trigger: 'blur'}],
        key: [{required: true, message: '填写服务器ID', trigger: 'blur'}],
        os: [{required: true, message: '请选择操作系统', trigger: 'blur'}],
        cpu: [{required: true, message: '请填写CPU核数', trigger: 'change'}],
        memory: [{required: true, message: '请填写内存大小', trigger: 'change'}],
        principalEmails: [{required: true, message: '请填负责人', trigger: 'blur'}],
      }),
      assetImageRules: antdFormRulesFormat({
        imageName: [{required: true, message: '填写镜像名', trigger: 'blur'}],
      }),
      serverTemp: {
        ip: '',
        hostname: '',
        cpu: 0,
        memory: 0,
        hostIp: '',
        os: '',
        key: '',
        idc: '',
        principalEmails: '',
      },
      assetImageTemp: {
        backupType: 3,
      },
      KvmOperate: {
        record: undefined,
        operation: undefined,
        force: undefined,
      },
      authOtpKey: {},
      timerId: undefined,
    }
  },
  created() {
    this.loadAssetData({})
    this.getInitUsersStatus()
    getAssetListIdc().then(res => {
      for (let i = 0, len = res.Data.idc.length; i < len; i++) {
        let idc = {}
        idc.value = res.Data.idc[i]
        idc.label = res.Data.idc[i]
        this.assetIdcList.push(idc)
      }
    })
    getAssetListOrganization().then(res => {
      for (let i = 0, len = res.Data.organization.length; i < len; i++) {
        let organization = {}
        organization.value = res.Data.organization[i]
        organization.label = res.Data.organization[i]
        this.assetOrgList.push(organization)
      }
    })
    getAssetListDepartment().then(res => {
      for (let i = 0, len = res.Data.department.length; i < len; i++) {
        let department = {}
        department.value = res.Data.department[i]
        department.label = res.Data.department[i]
        this.assetDepList.push(department)
      }
    })
    getAssetListCostUser().then(res => {
      for (let i = 0, len = res.Data.costUser.length; i < len; i++) {
        let costUser = {}
        costUser.value = res.Data.costUser[i]
        costUser.label = res.Data.costUser[i]
        this.assetCostUserList.push(costUser)
      }
    })
    getAssetListGpuType().then(res => {
      for (let i = 0, len = res.Data.gpuTypeList.length; i < len; i++) {
        let gpuType = {}
        gpuType.value = res.Data.gpuTypeList[i]
        gpuType.label = res.Data.gpuTypeList[i]
        this.assetGpuTypeList.push(gpuType)
      }
    })
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(this.localUser.split('@')[0])
    this.loadColumns('mount')
    getAssetListProject().then(res => {
      for (var i = 0, len = res.Data.project.length; i < len; i++) {
        var project = {}
        project.value = res.Data.project[i]
        project.label = res.Data.project[i]
        this.projectList.push(project)
      }
    })
  },
  unmounted() {
    removeWatermark()
  },
  methods: {
    costTypeStatusFilter(business, dgDataAsset) {
      if (business === ''){
        return ""
      }
      if (this.operation.includes(business, dgDataAsset)) {
        if (business === 'DG' && dgDataAsset.indexOf('是') > -1) {
          return '开发支出'
        }
        return '运营成本'
      }else {
        return '研发成本'
      }
    },
    adjustPos(pos, i) {
      let index = this.allColumns.indexOf(i)
      switch (pos) {
        case 'up':
          this.$set(this.allColumns, index, this.allColumns[index - 1])
          this.$set(this.allColumns, index - 1, i)
          break
        case 'down':
          this.$set(this.allColumns, index, this.allColumns[index + 1])
          this.$set(this.allColumns, index + 1, i)
          break
      }
      console.log(this.allColumns, 'this.allColumns')
    },
    projectChange(e, record) {
      if (e.length > 1) {
        record.project = [e[e.length - 1]]
      }
    },

    edit(key) {
      console.log(key, 'key')
      console.log(this.assetData, 'this.assetData')
      this.editableData[key] = cloneDeep(this.assetData.filter(item => key == item.id)[0])
      console.log()
    },
    upDateSelfAsset(r) {
      console.log(r, 'rrr')
      customUserAssets({
        id: r.id,
        env: r.env,
        application: r.application,
        comment: r.comment,
        project: r.project instanceof Array ? r.project[0] : r.project,
      }).then(res => {
        console.log(res, '修改结果')
        if (res && res.Data.message == 'success') {
          notification.success({
            message: '修改成功',
          })
          this.loadAssetData(this.queryParam)
        } else {
          notification.warning({
            message: res.Data.message,
          })
        }
        Object.assign(this.assetData.filter(item => r.id === item.id)[0], this.editableData[r.id])
        delete this.editableData[r.id]
      })
    },
    customColumn() {
      this.setColumnsVisible = true
      this.loadColumns()
    },
    confirmColumns() {
      let params = []
      const len = 0
      this.allColumns.forEach(item => {
        if (item.isChecked) {
          params.push({
            dataIndex: item.dataIndex,
            title: item.title,
          })
        }
      })
      if (params.length == 0) {
        notification.warning({
          message: '最少保留一列',
        })
        return
      }
      console.log(this.filterOptions, 'filterOptions')
      columnUpdate({userColumns: params, columns: this.allColumns}).then(res => {
        console.log(res, 'resssss')
        this.loadColumns()
      })
      this.setColumnsVisible = false
    },
    closeTag(i) {
      console.log(this.filter.indexOf(i), 'index')
      console.log(this.filter, 'fff')
      this.filter.splice(this.filter.indexOf(i), 1)
      this.handleSearch()
    },
    loadColumns(type) {
      columnList().then(res => {
        this.filterOptions = res.Data.columns
        this.allColumns = res.Data.columns
        this.userColumns = res.Data.userColumns
        let flag = true
        for (let i = 0; i < this.userColumns.length; i++) {
          if (this.userColumns[i].dataIndex == 'action') {
            flag = false
          }
          this.userColumns[i].sorter = true
        }
        if (flag) {
          this.userColumns.push({title: '操作', dataIndex: 'action', key: 'action'})
        }
        console.log(this.filterOptions, 'this.filterOptions')
        if (type == 'mount') {
          for (let i = 0; i < this.filterOptions.length; i++) {
            if (this.filterOptions[i].dataIndex == 'ip') {
              this.filterType = JSON.stringify(this.filterOptions[i])
            }
          }
        }
      })
    },
    getFilterParams() {
      let filterList = []
      console.log(this.filter, 'this.filter')
      this.filter.forEach(item => {
        filterList.push({key: item.key.dataIndex, relation: item.relation.value, value: item.value})
      })
      filterList.push({key: 'status', relation: 'equal', value: this.serverStatus})
      return filterList
    },
    reset() {
      for (let i = 0; i < this.filterOptions.length; i++) {
        if (this.filterOptions[i].dataIndex == 'ip') {
          this.filterType = JSON.stringify(this.filterOptions[i])
        }
      }
      // this.filterType = null
      this.conditions = JSON.stringify({label: '包含', value: 'like', sign: ':'})
      this.filter = []
      this.handleSearch()
    },
    handleMonitor(record) {
      this.$router.push({
        path: '/db/server-monitor',
        query: {
          id: record.id,
        },
      })
      console.log('监控')
      console.log(record, 'rrrrrrr')
    },
    hidsStatusFilter(type) {
      if (type === 10) {
        return '在线'
      } else if (type === 11) {
        return '离线'
      } else if (type === 12) {
        return '停用'
      } else {
        return '未安装'
      }
    },
    handleSearch() {
      this.queryParam.pageNo = 1
      this.queryParam.pageSize = 10
      if (this.queryParam.searchKey != '' && this.queryParam.searchKey != null ) {
        this.queryParam.searchKey = this.queryParam.searchKey.trim()
      }
      if (this.filterType == null || this.conditions == null || !this.filterContent.length) {
        // notification.warning({
        //   message: '请补全筛选信息',
        // })
      } else {
        this.filter.push({
          key: JSON.parse(this.filterType),
          relation: JSON.parse(this.conditions),
          value: this.filterContent,
        })

        for (let i = 0; i < this.filterOptions.length; i++) {
          if (this.filterOptions[i].dataIndex == 'ip') {
            this.filterType = JSON.stringify(this.filterOptions[i])
          }
        }
        this.conditions = JSON.stringify({label: '包含', value: 'like', sign: ':'})
        this.filterContent = ''
      }

      this.loadAssetData(this.queryParam)
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = {...this.pagination}
      pager.current = pagination.current
      this.pagination = pager
      this.queryParam.pageSize = pagination.pageSize
      this.queryParam.pageNo = pagination.current
      this.queryParam.sortField = sorter.field
      this.queryParam.sortOrder = sorter.order
      this.loadAssetData(this.queryParam)
    },
    loadAssetData(queryParam) {
      if (Object.keys(queryParam).length === 0) {
        queryParam = this.queryParam
      }
      this.downloadqueryParam = JSON.parse(JSON.stringify(this.queryParam))
      this.downloadqueryParam.pageNo = 1
      this.downloadqueryParam.pageSize = 0
      this.loading = true
      // getAssetList
      console.log(queryParam, 'queryParamqueryParamqueryParam')
      // this.queryParam.filters = this.getFilterParams()
      // assetFilter(queryParam).then(res => {
      //   console.log(res, 'ressss')
      //   this.dropdownVisible = {}
      //   let pagination = { ...this.pagination }
      //   if (res.Data.hasOwnProperty('data')) {
      //     if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
      //       this.assetData = []
      //       pagination.total = 0
      //       pagination.current = 1
      //       pagination.pageSize = 10
      //       this.pagination = pagination
      //       this.loading = false
      //     } else {
      //       res.Data.data.forEach(item => {
      //         this.dropdownVisible[item.id] = false
      //       })
      //       this.assetData = res.Data.data
      //       this.downloadqueryParam.pageSize = res.Data.totalCount
      //       if (res.Data.totalCount <= 10) {
      //         pagination = false
      //         this.pagination = pagination
      //         this.loading = false
      //       } else {
      //         pagination.total = res.Data.totalCount
      //         this.pagination = pagination
      //         console.log(this.pagination, ' this.pagination')
      //         this.loading = false
      //         this.pagination.pageSize = res.Data.pageSize
      //         this.pagination.total = res.Data.totalCount
      //         this.pagination.current = res.Data.pageNo
      //         if (this.pagination.showTotal === undefined) {
      //           this.$set(this.pagination, 'showTotal', total => `共 ${total} 条`)
      //         }
      //       }
      //     }
      //   } else {
      //     this.assetData = []
      //     pagination.total = 0
      //     pagination.pageNo = 1
      //     pagination.pageSize = 10
      //     this.pagination = pagination
      //     this.loading = false
      //   }
      // })

      getAssetList(queryParam).then(res => {
        console.log(res, 'ressss')
        this.dropdownVisible = {}
        let pagination = {...this.pagination}
        if (res.Data.hasOwnProperty('data')) {
          if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
            this.assetData = []
            pagination.total = 0
            pagination.current = 1
            pagination.pageSize = 10
            this.pagination = pagination
            this.loading = false
          } else {
            res.Data.data.forEach(item => {
              this.dropdownVisible[item.id] = false
            })
            this.assetData = res.Data.data
            this.downloadqueryParam.pageSize = res.Data.totalCount
            if (res.Data.totalCount <= 10) {
              pagination = false
              this.pagination = pagination
              this.loading = false
            } else {
              pagination.total = res.Data.totalCount
              this.pagination = pagination
              console.log(this.pagination, ' this.pagination')
              this.loading = false
              this.pagination.pageSize = res.Data.pageSize
              this.pagination.total = res.Data.totalCount
              this.pagination.current = res.Data.pageNo
              if (this.pagination.showTotal === undefined) {
                this.$set(this.pagination, 'showTotal', total => `共 ${total} 条`)
              }
            }
          }
        } else {
          this.assetData = []
          pagination.total = 0
          pagination.pageNo = 1
          pagination.pageSize = 10
          this.pagination = pagination
          this.loading = false
        }
      })
    },
    updateAssetStatus(recordId, status) {
      if (status !== undefined) {
        this.assetData.forEach(item => {
          if (item.id === recordId) {
            item.status = status
          }
        })
      } else {
        getAssetInfo(recordId).then(response => {
          const newStatus = response.Data.status
          this.assetData.forEach(item => {
            if (item.id === recordId) {
              item.status = newStatus
            }
          })
        })
      }
    },
    changeDropdownVisible(dropdownVisible, recordId) {
      this.dropdownVisible[recordId] = dropdownVisible
    },
    keepVisible(recordId) {
      this.dropdownVisible[recordId] = true
    },
    // 二次认证

    getInitUsersStatus()  {
      this.authOtpKey.value = ''
      myColumns.forEach(item => {
        this.authOtpKey.value += item.scopeKey
      })
      this.authOtpKey.value = waterMarkVs + this.authOtpKey.value
    },
    async authToStr(text, a) {
      const importedKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(a),
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      )
      const textData = atob(text)
      const dataArray = new Uint8Array(textData.split('').map(char => char.charCodeAt(0)))

      const nonceSize = 12
      const nonce = dataArray.slice(0, nonceSize)
      const ciphertext = dataArray.slice(nonceSize)
      try {
        const myData = await crypto.subtle.decrypt({ name: 'AES-GCM', iv: nonce }, importedKey, ciphertext)
        return new TextDecoder().decode(myData)
      } catch (error) {
        return null
      }
    },
    checkStatus() {
      globalOtp().then(res => {
        this.authToStr(res.Data.network, this.authOtpKey.value).then(r => {
          this.authToStr(res.Data.otpToken, this.authOtpKey.value).then(res1 => {
            if (res1) {
              localStorage.setItem('globalToken', res.Data.otpToken)
              this.captchaVisible = false
              this.confirmKvmOperate()
            } else {
              this.captchaVisible = true
            }
          })
        })
      })
    },
    handleSubmitKvmOperate(record, operation, force){
      this.KvmOperate.record = record
      this.KvmOperate.operation = operation
      this.KvmOperate.force = force
      this.checkStatus()
      this.dropdownVisible[record.id] = false
    },
    // kvm虚拟机操作
    confirmKvmOperate() {
      this.captchaVisible = false
      const record = this.KvmOperate.record
      const operation = this.KvmOperate.operation
      const force = this.KvmOperate.force
      const originStatus = record.status
      const sendData = {
        operation: operation,
        hostname: record.hostname,
        uuid: record.uuid,
        ip: record.ip,
        hostIp: record.hostIp,
        status: record.status,
      }
      if (force) {
        sendData.force = true
      }
      this.updateAssetStatus(record.id, 10)
      kvmOperate(sendData).then(response => {
        const msg = response.Data.message
        if (msg === 'success') {
          notification.success({
            message: '操作成功',
          })
          // 修改状态
          if (operation === 'shutdown') {
            this.updateAssetStatus(record.id, 3)
          } else if (operation === 'start') {
            this.updateAssetStatus(record.id, 4)
          } else {
            this.updateAssetStatus(record.id, 5)
          }
          let statusInterval = setInterval(() => {
            this.assetData.forEach(item => {
              if (item.id === record.id) {
                if (item.status === 1 || item.status === 2) {
                  clearInterval(statusInterval)
                }
              }
            })
            this.updateAssetStatus(record.id)
          }, 15000)
          // 开始循环获取数据
        } else {
          this.updateAssetStatus(record.id, originStatus)
          notification.error({
            message: '操作失败:' + msg,
          })
        }
      })
      this.dropdownVisible[record.id] = false
    },
    envFilter(type) {
      return envMap[type]
    },
    regionFilter(type) {
      return regionMap[type]
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    handleAdd() {
      this.$router.push({path: '/workflow/createWorkflow'})
    },
    // 页面功能隔离
    getUserRoles(userEmail) {
      getUserList({searchText: userEmail}).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite) || this.userRoles.includes('admin')) {
          this.userpRression = true
        }
      })
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    confirmServerAddEntry() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.serverAddEntryVisible = false
          createServerAddEntry(this.serverTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code === 400) {
              notification.error({
                message: '创建失败',
              })
            } else if (response && response.Data && response.Data.message != 'error') {
              notification.success({
                message: '创建成功',
              })
            }
          })
        }
      })
    },
    handleInfo(record) {
      this.$router.push({path: '/server/asset-info', query: {id: record.id}})
    },
    handleEditTag(record) {
      let routeUrl = this.$router.resolve({path: '/workflow/server-modify-tag', query: {assetId: record.id}})
      window.open(routeUrl.href, '_blank')
    },
    handleEditConfig(record) {
      let routeUrl = ''
      if (this.idcArr.includes(record.idc)) {
        routeUrl = this.$router.resolve({path: '/workflow/vm-asset-modify-config', query: {assetId: record.id}})
      } else {
        routeUrl = this.$router.resolve({path: '/workflow/server-modify-config', query: {assetId: record.id}})
      }

      window.open(routeUrl.href, '_blank')
    },
    handleDownload() {
      this.downloadLoading = true
      const downloadColumns = [
        {
          title: 'ID',
          dataIndex: 'id',
        },
        {
          title: '服务器名',
          dataIndex: 'hostname',
        },
        {
          title: '机房',
          dataIndex: 'idc',
        },
        {
          title: 'IP',
          dataIndex: 'ip',
        },
        {
          title: '公网IP',
          dataIndex: 'publicIp',
        },
        {
          title: '物理机IP',
          dataIndex: 'hostIp',
        },
        {
          title: '出口IP',
          dataIndex: 'exportIp',
        },
        {
          title: 'CPU',
          dataIndex: 'cpu',
        },
        {
          title: '内存',
          dataIndex: 'memory',
        },
        {
          title: '系统类型',
          dataIndex: 'os',
        },
        {
          title: '实例类型',
          dataIndex: 'instanceType',
        },
        {
          title: '系统版本',
          dataIndex: 'osVersion',
        },
        {
          title: '磁盘信息',
          dataIndex: 'disks',
        },
        {
          title: '区域',
          dataIndex: 'region: ',
        },
        {
          title: '状态',
          dataIndex: 'status',
        },
        {
          title: '唯一标识',
          dataIndex: 'uuid',
        },
        {
          title: '事业部',
          dataIndex: 'org',
        },
        {
          title: '部门',
          dataIndex: 'dep',
        },
        {
          title: '负责人',
          dataIndex: 'principal',
        },
        {
          title: '环境',
          dataIndex: 'env',
        },
        {
          title: '产品线',
          dataIndex: 'productLine',
        },
        {
          title: '费用负责人',
          dataIndex: 'costUser',
        },
        {
          title: '项目',
          dataIndex: 'project',
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
        },
        {
          title: '更新时间',
          dataIndex: 'updatedAt',
        },
      ]
      this.downloadqueryParam.opType = 'download'
      getAssetList(this.downloadqueryParam).then(async res => {
        if (res.Data.hasOwnProperty('data')) {
          const XLSX = await loadXLSX()
          const tableData = this.transData(downloadColumns, res.Data.data)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          this.downloadLoading = false
          XLSX.utils.book_append_sheet(wb, ws, 'Asset')
          XLSX.writeFile(wb, 'Asset.xlsx')
        } else {
          this.downloadtableList = []
        }
      })
    },
    transData(columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.dataIndex)
        return acc
      }, {})
      const tableBody = tableList.map(item => {
        return obj.keys.map(key => item[key])
      })
      return [obj.titles, ...tableBody]
    },
    handleOk() {
      const form = this.$refs.createModal.form
      this.confirmLoading = true
      form.validateFields((errors, values) => {
        if (!errors) {
          if (values.id > 0) {
            // 修改 e.g.
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then(res => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('修改成功')
            })
          } else {
            // 新增
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then(res => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('新增成功')
            })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    handleDel(record) {
      console.log(record, 'rrrr')
      let routeUrl = ''
      if (this.idcArr.includes(record.idc)) {
        routeUrl = this.$router.resolve({path: '/workflow/vm-asset-del', query: {assetId: record.id}})
      } else {
        routeUrl = this.$router.resolve({path: '/workflow/server-del', query: {assetId: record.id}})
      }
      window.open(routeUrl.href, '_blank')
    },
    handleDelMore() {
      const idData = JSON.stringify(this.selectedRowKeys)
      let routeUrl = this.$router.resolve({path: '/workflow/server-del', query: {assetId: idData}})
      window.open(routeUrl.href, '_blank')
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    // 详细信息 相关接口
    handleDetail(record) {
      getAssetInfo(record.id).then(response => {
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
    // 详细信息 相关接口
    handleCreateAssetImage(record) {
      this.assetImageDetail = record
      this.assetImageTemp.imageName = record.hostname
      this.createAssetImagedVisible = true
      this.backupLoading = false
      this.assetImageTemp.assetIp = record.Ip
      this.assetImageTemp.assetUuid = record.uuid
    },
    confirmCreateAssetImage() {
      this.backupLoading = true
      createAssetImages(this.assetImageTemp)
        .then(response => {
          this.backupLoading = false
          this.createAssetImagedVisible = false
          if (response === undefined) {
            notification.error({
              message: '创建失败',
              description: '后端接口错误，请联系运维开发排查~',
            })
          } else if (response.Code === 400) {
            notification.error({
              message: '创建失败',
            })
          } else {
            notification.success({
              message: '创建成功',
            })
          }
        })
        .catch(error => {
          console.log(error)
          this.backupLoading = false
        })
    },
  },
}
</script>
<style lang="less" scoped>
#myAssetTable {
  .editable-cell {
    position: relative;

    .editable-cell-input-wrapper,
    .editable-cell-text-wrapper {
      padding-right: 24px;
    }

    .editable-cell-text-wrapper {
      padding: 5px 24px 5px 5px;
    }

    .editable-cell-icon,
    .editable-cell-icon-check {
      position: absolute;
      right: 0;
      width: 20px;
      cursor: pointer;
    }

    .editable-cell-icon {
      margin-top: 4px;
      display: none;
    }

    .editable-cell-icon-check {
      line-height: 28px;
    }

    .editable-cell-icon:hover,
    .editable-cell-icon-check:hover {
      color: #108ee9;
    }

    .editable-add-btn {
      margin-bottom: 8px;
    }
  }

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }
}

.toRight {
  float: right;
  margin-right: 20px;

  span {
    visibility: hidden;
  }
}

.toRight:hover {
  span {
    visibility: visible;
  }
}
</style>
