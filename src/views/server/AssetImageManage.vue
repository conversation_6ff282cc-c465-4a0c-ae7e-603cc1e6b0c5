<template>
  <page-header-wrapper>
    <a-tabs default-active-key="0">
      <a-tab-pane key="0" tab="镜像管理" force-render type="card">
        <asset-image />
      </a-tab-pane>
      <a-tab-pane key="1" tab="自动备份管理" force-render type="card">
        <auto-backup-asset />
      </a-tab-pane>
    </a-tabs>
  </page-header-wrapper>
</template>

<script>
import { openTinyWin } from '@/utils/window'
import AssetImage from '@/views/server/components/AssetImage.vue'
import AutoBackupAsset from '@/views/server/components/AutoBackupAsset.vue'

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    AssetImage: AssetImage,
    AutoBackupAsset: AutoBackupAsset,
  },
  data() {
    this.pagination = pagination
    this.downloadtableList = null
    return {
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: 'disconnected' },
      // 加载数据方法 必须为 Promise 对象
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {},
  methods: {
    openWindow(str) {
      openTinyWin(str)
    },
  },
}
</script>
