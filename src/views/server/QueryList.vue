<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-20 14:38:10
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-02-06 11:33:29
 * @FilePath: \cloud_web\src\views\list\QueryList.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <a-card :bordered="false">
    <component @onEdit="handleEdit" @onGoBack="handleGoBack" :record="record" :is="currentComponet"></component>
  </a-card>
</template>

<script>
import { Textarea as ATextarea, Input as AInput } from 'ant-design-vue'
// 动态切换组件
import List from '@/views/server/table/List.vue'
import Edit from '@/views/server/table/Edit.vue'

export default {
  name: 'TableListWrapper',
  components: {
    AInput,
    ATextarea,
    List,
    Edit,
  },
  data() {
    return {
      currentComponet: 'List',
      record: '',
    }
  },
  created() {},
  methods: {
    handleEdit(record) {
      this.record = record || ''
      this.currentComponet = 'Edit'
      console.log(record)
    },
    handleGoBack() {
      this.record = ''
      this.currentComponet = 'List'
    },
  },
  watch: {
    '$route.path'() {
      this.record = ''
      this.currentComponet = 'List'
    },
  },
}
</script>
