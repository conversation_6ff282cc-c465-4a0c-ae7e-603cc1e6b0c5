<template>
  <page-header-wrapper>
    <a-card :bordered="false" :model="temp.content" ref="temp">
      <a-descriptions title="基础信息">
        <a-descriptions-item label="服务器名">{{ temp.hostname }}</a-descriptions-item>
        <a-descriptions-item label="IDC">{{ temp.idc }}</a-descriptions-item>
        <a-descriptions-item label="公网IP">{{ temp.publicIp }}</a-descriptions-item>
        <a-descriptions-item label="IP">{{ temp.ip }}</a-descriptions-item>
        <a-descriptions-item label="区域">{{ regionFilter(temp.region) }}</a-descriptions-item>
        <a-descriptions-item label="物理机IP">{{ temp.hostIp }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-badge :status="statusTypeFilter(temp.status)" :text="statusFilter(temp.status)" />
        </a-descriptions-item>
        <a-descriptions-item label="唯一标识">{{ temp.uuid }}</a-descriptions-item>
        <a-descriptions-item label="出口IP">{{ temp.exportIp }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ temp.createdAt }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ temp.updatedAt }}</a-descriptions-item>
      </a-descriptions>
      <a-descriptions title="配置信息">
        <a-descriptions-item label="CPU">{{ temp.cpu }} 核</a-descriptions-item>
        <a-descriptions-item label="系统类型">{{ temp.os }}</a-descriptions-item>
        <a-descriptions-item label="实例类型">{{ temp.instanceType }}</a-descriptions-item>
        <a-descriptions-item label="内存">{{ temp.memory }} GB</a-descriptions-item>
        <a-descriptions-item label="系统版本" :span="2">{{ temp.osVersion }}</a-descriptions-item>
        <a-descriptions-item label="磁盘信息" :span="3">{{ temp.disks }}</a-descriptions-item>
      </a-descriptions>
      <a-divider style="margin-bottom: 32px"/>
      <a-descriptions title="费用信息" size="small">
        <a-descriptions-item label="上月费用">￥{{ temp.costLastMonth }}</a-descriptions-item>
        <a-descriptions-item label="上上月费用">￥{{ temp.costMonthAfterLast }}</a-descriptions-item>
      </a-descriptions>
      <a-divider style="margin-bottom: 32px"/>
      <a-descriptions title="标签信息">
        <a-descriptions-item label="事业部">{{ temp.org }}</a-descriptions-item>
        <a-descriptions-item label="部门">{{ temp.dep }}</a-descriptions-item>
        <a-descriptions-item label="项目">{{ temp.project }}</a-descriptions-item>
        <a-descriptions-item label="负责人">{{ temp.principal }}</a-descriptions-item>
        <a-descriptions-item label="环境">{{ envFilter(temp.env) }}</a-descriptions-item>
        <a-descriptions-item label="应用">{{ temp.application }}</a-descriptions-item>
      </a-descriptions>
      <a-divider style="margin-bottom: 32px"/>
      <a-descriptions title="监控信息">
        <a-descriptions-item label="远程连接端口" :span="3">{{ temp.sshPort }}</a-descriptions-item>
        <a-descriptions-item label="CPU使用率">{{ temp.cpuUsedMax }}</a-descriptions-item>
        <a-descriptions-item label="内存使用率">{{ temp.memoryUsedMax }}</a-descriptions-item>
        <a-descriptions-item label="eth0下载流量">
          <div v-if="temp.rateUsedMax">{{ temp.rateUsedMax }}Mbps</div>
        </a-descriptions-item>
      </a-descriptions>
      <a-divider style="margin-bottom: 32px"/>
      <a-descriptions title="其他信息">
        <a-descriptions-item label="镜像备份">
          <div v-if="temp.imageBackup">
            <a-badge status="success" text="是" />
          </div>
          <div v-else>
            <a-badge status="error" text="否" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="数据库备份" :span="2">
          <div v-if="temp.dbBackup">
            <a-badge status="success" text="是" />
          </div>
          <div v-else>
            <a-badge status="error" text="否" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="是否需要监控">
          <div v-if="temp.prometheusStatus">
            <a-badge status="success" text="是" />
          </div>
          <div v-else>
            <a-badge status="error" text="否" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="是否为GPU机器" :span="2">
          <div v-if="temp.isGpu">
            <a-badge status="success" text="是" />
          </div>
          <div v-else>
            <a-badge status="error" text="否" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="备注">{{ temp.comment }}</a-descriptions-item>
      </a-descriptions>
      <a-divider style="margin-bottom: 32px"/>
    </a-card>
  </page-header-wrapper>
</template>

<script>
  import { STable } from '@/components'
  import { getAssetInfo } from '@/api/asset'

  const statusMap = {
    0: {
      status: 'processing',
      text: '关闭'
    },
    1: {
      status: 'success',
      text: '运行中'
    },
    2: {
      status: 'error',
      text: '已关机'
    }
  }
  const envMap = {
    dev: '开发环境',
    test: '测试环境',
    pre: '预发布环境',
    online: '生产环境'
  }
  const regionMap = {
    'cn-hangzhou': '中国-杭州',
    'cn-shanghai': '中国-上海',
    'cn-hongkong': '中国-香港',
    'ap-shanghai-1': '上海一区',
    'ap-shanghai-2': '上海二区',
    'ap-shanghai-3': '上海三区',
    'ap-shanghai-4': '上海四区',
    'ap-shanghai-5': '上海五区',
    'ap-east-1': '亚太-香港',
    'ap-northeast-1': '亚太-东京',
    'ap-south-1': '亚太-孟买',
    'ap-southeast-1': '亚太-新加坡',
    'cn-north-1': '中国-北京',
    'cn-northeast-1': '中国-宁夏',
    'eu-west-1': '欧洲-爱尔兰',
    'sa-east-1': '南美-圣保罗',
    'us-west-1': '美西-加利福尼亚',
    'us-west-2': '美西-俄亥俄'
  }

  export default {
    components: {
      STable
    },
    data () {
      return {
        temp: {
          id: '',
          hostname: '',
          uuid: '',
          idc: '',
          region: '',
          os: '',
          osVersion: '',
          ip: '',
          publicIp: '',
          exportIp: '',
          hostIp: '',
          cpu: '',
          memory: '',
          disks: '',
          instanceType: '',
          status: '',
          org: '',
          dep: '',
          env: '',
          principal: '',
          principalEmails: '',
          createdAt: '',
          updatedAt: ''
        }
      }
    },
    computed: {
      title () {
        return this.$route.meta.title
      }
    },
    created () {
      this.getList()
    },
    methods: {
      envFilter (type) {
        return envMap[type]
      },
      regionFilter (type) {
        return regionMap[type]
      },
      statusFilter (type) {
        return statusMap[type]?.text
      },
      statusTypeFilter (type) {
        return statusMap[type]?.status
      },
      getList () {
        if (this.$route.query.id) {
          this.id = this.$route.query.id
          getAssetInfo(this.id).then(response => {
            this.temp = response.Data
          })
        }
      }
    }

  }
</script>

<style lang="less" scoped>
  .title {
    color: rgba(0,0,0,.85);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
</style>
