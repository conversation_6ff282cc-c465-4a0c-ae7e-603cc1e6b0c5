<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="安全组名称">
                <a-input v-model:value="queryParam.securityGroupName" placeholder="安全组名称" @pressEnter="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'left', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="handleSearch">查询</tx-button>
                <tx-button style="margin-left: 8px" type="primary" icon="plus" @click="handleAddEntry">
                  手动录入
                </tx-button>
                <a-upload :before-upload="beforeUpload" :showUploadList="false" accept=".xlsx,.xls">
                  <a-button class="ml-2" :loading="confirmLoading">
                    <a-icon type="upload" />
                    上传
                  </a-button>
                </a-upload>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <a-table
        ref="table"
        :rowKey="record => record.id"
        :columns="columns"
        :data-source="securityGroupData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        @change="handleTableChange"
      >
        <template #expandedRowRender="{ record }">
          <div>
            <a>安全组规则</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-tabs default-active-key="ingress" @change="key => handleTabChange(key, record)">
                  <a-tab-pane key="inbound" tab="入站">
                    <a-table
                      :columns="columnsRules"
                      :data-source="record.ingress || []"
                      :pagination="false"
                      :rowKey="item => item.id"
                      class="kvmAssetTable"
                      style="width: 110%"
                    >
                    </a-table>
                  </a-tab-pane>
                  <a-tab-pane key="egress" tab="出站">
                    <a-table
                      :columns="columnsRules"
                      :data-source="record.egress || []"
                      :pagination="false"
                      :rowKey="item => item.id"
                      class="kvmAssetTable"
                      style="width: 110%"
                    >
                    </a-table>
                  </a-tab-pane>
                </a-tabs>
              </a-col>
            </a-row>
          </div>
        </template>
      </a-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import StepByStepModal from './modules/StepByStepModal.vue'
import CreateForm from './modules/CreateForm.vue'
import { listAssetSecurityGroup } from '@/api/asset/securityGroup'
import { getUserList } from '@/api/permission/user'
import {securityList} from "@/api/cmdb/workflow/asset_automation";

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
  },
  {
    title: '区域',
    dataIndex: 'region',
  },
  {
    title: '安全组名',
    dataIndex: 'securityGroupName',
  },
  {
    title: '安全组Id',
    dataIndex: 'securityGroupId',
  },
  {
    title: '描述',
    dataIndex: 'description',
  }
]

const columnsRules = [
  {
    title: '协议',
    dataIndex: 'protocol',
  },
  {
    title: '端口',
    dataIndex: 'portRange',
  },
  {
    title: 'Ip',
    dataIndex: 'cidrBlock',
  },
  {
    title: 'IpV6',
    dataIndex: 'ipv6CidrBlock',
  },
  {
    title: '策略',
    dataIndex: 'action',
  },
  {
    title: '描述',
    dataIndex: 'description',
  },

]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}
export default {
  name: 'TableList',
  components: {
    CreateForm,
    StepByStepModal,
  },
  data() {
    this.columns = columns
    this.columnsRules = columnsRules
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      serverModifyEntryVisible: false,
      downloadLoading: false,
      dropdownVisible: {},
      assetOrgList: [],
      assetDepList: [],
      createData: { hosts: [] },
      assetCostUserList: [],
      securityGroupData: [],
      // create model
      visible: false,
      confirmLoading: false,
      mdl: null,
      advanced: false,
      queryParam: {pageNo: 1, pageSize: 10 },
      allData: [],
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {
        ipmiUser: '',
        memAvailable: 0,
        processorModel: '',
        id: 0,
        processorCores: 0,
        status: true,
        ipmiPassword: '',
        memBuffer: 0,
        dateShutdown: 0,
        processorCount: 0,
        ip: '',
        needKvm: false,
        hostname: '',
        memCache: 0,
        idc: '',
        processorThreadsPerCore: 0,
        comment: '',
        dnsNameservers: '',
        rack: '',
        processorVcpus: 0,
        ansibleIp: '',
        costLastMonth: 0,
        osDistributionRelease: '',
        hostModel: '',
        ansiblePort: 0,
        memTotal: 0,
        supplier: '',
        productSerial: 0,
        osDistributionVersion: 0,
        systemVendor: 'Inspu',
        memUsed: 0,
        ansibleExtraVars: '',
        buyTime: '',
        osDistributionMajorVersion: 0,
        productName: '',
        ipmiIp: '',
        memFree: 0,
      },
    }
  },
  created() {
    this.loadSecurityGroupData({})
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getUserRoles(this.localUser.split('@')[0])
  },
  unmounted() {
    removeWatermark()
  },
  methods: {
    redirectToGrafana(record) {
      window.open(record.grafanaUrl, '_blank')
    },
    handleSearch() {
      this.queryParam.pageNo = 1
      this.loadSecurityGroupData(this.queryParam)
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination }
      pager.current = pagination.current
      this.pagination = pager
      this.queryParam.pageSize = pagination.pageSize
      this.queryParam.pageNo = pagination.current
      this.queryParam.sortField = sorter.field
      this.queryParam.sortOrder = sorter.order
      this.loadSecurityGroupData(this.queryParam)
    },
    // 加载数据
    loadSecurityGroupData(queryParam) {
      if (Object.keys(queryParam).length === 0) {
        queryParam = this.queryParam
      }
      listAssetSecurityGroup(queryParam).then(res => {
        let pagination = { ...this.pagination }
        if (res.Data.hasOwnProperty('data')) {
          if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
            this.securityGroupData = []
            pagination.total = 0
            pagination.pageNo = 1
            pagination.pageSize = 10
            this.pagination = pagination
            this.loading = false
          } else {
            this.securityGroupData = res.Data.data.map(group => {
              // 将rules根据type字段分类
              const ingress = group.rules.filter(rule => rule.type === 'ingress')
              const egress = group.rules.filter(rule => rule.type === 'egress')
              return {
                ...group,
                ingress,
                egress
              }
            })
            if (res.Data.totalCount <= 10) {
              pagination = false
              this.pagination = pagination
              this.loading = false
            } else {
              pagination.total = res.Data.totalCount
              this.pagination = pagination
              this.loading = false
              if (this.pagination.showTotal === undefined) {
                this.$set(this.pagination, 'showTotal', total => `共 ${total} 条`)
              }
            }
          }
        } else {
          this.securityGroupData = []
          pagination.total = 0
          pagination.pageNo = 1
          pagination.pageSize = 10
          this.pagination = pagination
          this.loading = false
        }
      })
    },
    // 下拉框切换
    changeDropdownVisible(dropdownVisible, recordIp, kvmRecordID) {
      this.dropdownVisible[recordIp][kvmRecordID] = dropdownVisible
    },
    keepVisible(recordIp, kvmRecordID) {
      this.dropdownVisible[recordIp][kvmRecordID] = true
    },

    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      */
    // 详细信息 相关接口
    handleDetail(record) {
      this.securityGroupData.forEach(item => {
        if (item.id === record.id) {
          this.detailData = item
        }
      })
      this.detailIdVisible = true
    },
  },
}
</script>

<style lang="less" scoped>
.kvmAssetTable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 10px;
    position: relative;
  }

  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    text-align: center !important;
  }
  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
