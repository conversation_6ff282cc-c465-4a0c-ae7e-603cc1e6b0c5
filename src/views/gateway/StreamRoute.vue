<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="集群环境">
                <a-select
                  allowClear
                  showSearch
                  :options="envList"
                  v-model:value="queryParam.env"
                  placeholder="请选择集群环境"
                ></a-select>
              </a-form-item>
            </a-col>
            <!-- <a-col :md="4" :sm="24">
              <a-form-item label="服务地址">
                <a-input v-model:value="queryParam.serverAddr" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col> -->
            <a-col :md="4" :sm="24">
              <a-form-item label="端口">
                <a-input v-model:value="queryParam.serverPort" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="nodes">
                <a-input v-model:value="queryParam.upstream" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 4) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button type="primary" v-if="userpRression" style="margin-left: 8px" @click="addStreamRoute('', 'open')">新增</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'size'">
            <a-badge :text="sizeFilter(text)" />
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleDetail(record)">详情</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a style="width: 40px" @click="handleUpdate(record, 'open')">修改</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
                      <template #icon>
                        <a-icon type="question-circle-o" style="color: red" />
                      </template>
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </template>
              <a>
                更多
                <a-icon type="down" />
              </a>
            </a-dropdown>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal v-model:visible="infoVisible" title="StreamRoute详情" :footer="null" :width="600">
      <a-form-model
        layout="horizontal"
        ref="addForm"
        :model="infoParam"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-span>基础信息</a-span>
        <a-divider class="ant-divider-horizonta1l" />
        <a-form-model-item label="网关集群" class="my-ant-form-item">
          <a-input v-model:value="infoParam.env" />
        </a-form-model-item>
        <a-form-model-item label="负责人">
          <a-input v-model:value="infoParam.user" />
        </a-form-model-item>
        <a-form-model-item label="创建时间">
          <a-input v-model:value="infoParam.createAt" />
        </a-form-model-item>
        <a-form-model-item label="更新时间">
          <a-input v-model:value="infoParam.updateAt" />
        </a-form-model-item>
        <a-span>StreamRoute数据</a-span>
        <a-divider class="ant-divider-horizonta1l" />
        <NocEditorJson
          class="myClbClusterBox"
          style="height: 280px, width: 550px"
          v-model="infoParam.apisixDatas"
          :mode="'code'"
          lang="zh"
        ></NocEditorJson>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="addVisible" title="StreamRoute新增" :width="600">
      <template #footer>
        <tx-button key="back" @click="addStreamRoute(record, 'close')">取消</tx-button>
        <tx-button key="submit" type="primary" @click="addStreamRoute(record, 'add')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="addParamForm"
        :model="addParam"
        :rules="Rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="网关集群" class="my-ant-form-item" name="envId">
          <a-select
            showSearch
            :options="envAddList"
            v-model:value="addParam.envId"
            placeholder="请选择集群环境"
          ></a-select>
        </a-form-model-item>
        <!-- <a-form-model-item label="Server地址" class="my-ant-form-item">
          <a-input v-model:value="addParam.serverAddr" />
        </a-form-model-item> -->
        <a-form-model-item label="ServerPort" class="my-ant-form-item" name="serverPort">
          <a-input-number v-model:value="addParam.serverPort" placeholder="8081" />
        </a-form-model-item>
        <a-form-model-item label="RemoteAddr" class="my-ant-form-item" name="remoteAddr">
          <a-input v-model:value="addParam.remoteAddr" placeholder="ipv4/ipv4CRID" />
        </a-form-model-item>
        <a-form-model-item label="Sni" class="my-ant-form-item">
          <a-input v-model:value="addParam.sni" placeholder="intsig.net" />
        </a-form-model-item>
        <a-form-model-item label="Upstream">
          <NocEditorJson
            class="myClbClusterBox1"
            style="height: 280px, width:400px"
            v-model="addParam.upstreams"
            :mode="'code'"
            lang="zh"
          ></NocEditorJson>
        </a-form-model-item>
        <a-form-model-item label="负责人" class="my-ant-form-item" name="user">
          <a-select
            placeholder="请输入邮箱选择"
            v-model:value="addParam.user"
            :showSearch="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
              {{ item1 }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="updateVisible" title="StreamRoute修改" :width="600">
      <template #footer>
        <tx-button key="back" @click="handleUpdate(record, 'close')">取消</tx-button>
        <tx-button key="submit" type="primary" @click="handleUpdate(record, 'add')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="updateForm"
        :model="updateParam"
        :rules="RulesUpdate"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-model-item label="网关集群" class="my-ant-form-item" name="envId">
          <a-select
            disabled
            :options="envAddList"
            v-model:value="updateParam.envId"
            placeholder="请选择集群环境"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item label="ServerPort" class="my-ant-form-item" name="serverPort">
          <a-input-number v-model:value="updateParam.serverPort" disabled placeholder="8081" />
        </a-form-model-item>
        <a-form-model-item label="RemoteAddr" class="my-ant-form-item" name="remoteAddr">
          <a-input v-model:value="updateParam.remoteAddr" placeholder="ipv4/ipv4CRID" />
        </a-form-model-item>
        <a-form-model-item label="Upstream">
          <div class="nodes-container">
            <div v-for="(item, index) in updateParam.nodes" :key="index" class="nodes-container">
              <a-divider class="ant-divider-horizonta1l" />
              <a-row class="custom-row">
                <a-col :span="9">
                  <a-form-model-item label="IP" class="my-ant-form-item" name="">
                    <a-input v-model:value="item.ip" placeholder="" style="width: 140px" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="7">
                  <a-form-model-item label="端口" class="my-ant-form-item" name="">
                    <a-input-number v-model:value="item.port" :min="1" :max="65533" style="width: 60px" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item label="权重" class="my-ant-form-item" name="">
                    <a-input-number v-model:value="item.weight" :min="1" :max="100" style="width: 60px" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="1">
                  <a-form-model-item label="" class="my-ant-form-item" v-if="index === 0">
                    <tx-button
                      type="primary"
                      icon="plus"
                      @click="rowServerAdd"
                      size="small"
                      style="min-height: 24px; line-height: 24px; padding: 0 5px"
                    ></tx-button>
                  </a-form-model-item>
                  <a-form-model-item label="" class="my-ant-form-item" v-else>
                    <tx-button
                      type="danger"
                      icon="minus"
                      @click="rowServerDelete(index)"
                      size="small"
                      style="min-height: 24px; line-height: 24px; padding: 0 5px"
                    ></tx-button>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-model-item>
        <a-form-model-item label="负责人" class="my-ant-form-item" name="users">
          <a-select
            mode="multiple"
            placeholder="请输入邮箱选择"
            v-model:value="updateParam.users"
            :showSearch="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
              {{ item1 }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { groupNames } from '@/api/gateway/groupAndIp'
import { Ellipsis, STable } from '@/components'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import {
  listStreamRoute,
  deleteStreamRoute,
  getStreamRoute,
  createStreamRoute,
  putStreamRoute,
} from '@/api/gateway/streamRoute'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: '集群环境',
    dataIndex: 'env',
  },
  // {
  //   title: '服务地址',
  //   dataIndex: 'serverAddr',
  // },
  {
    title: '服务端口',
    dataIndex: 'serverPort',
  },
  {
    title: 'nodes',
    dataIndex: 'nodes',
  },
  {
    title: '远程地址',
    dataIndex: 'remoteAddr',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '170px',
    align: 'center',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'StreamRoute',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      serverAddEntryVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      userEmailList: [],
      envList: [],
      envAddList: [],
      // create model
      visible: false,
      infoVisible: false,
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      addParam: {},
      infoParam: {},
      updateParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listStreamRoute(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
            for (let i = 0; i < res.Data.data.length; i++) {
              var upstream = JSON.parse(res.Data.data[i].upstream)
              res.Data.data[i].nodes = Object.keys(upstream.nodes).join(', ')
            }
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      eipIdcList: [],
      detailData: {},
      detailIdVisible: false,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
      Rules: {
        envId: [{ required: true, message: '请选择网关集群', trigger: 'blur' }],
        serverPort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }],
        // remoteAddr: [{ required: true, message: '请输入IPv4或者IPv4 CIDR', trigger: 'blur' }],
        user: [{ required: true, message: '请输入负责人邮箱', trigger: 'blur' }],
      },
      RulesUpdate: {
        envId: [{ required: true, message: '请选择网关集群', trigger: 'blur' }],
        serverPort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }],
        // remoteAddr: [{ required: true, message: '请输入IPv4或者IPv4 CIDR', trigger: 'blur' }],
        users: [{ required: true, message: '请输入负责人邮箱', trigger: 'blur' }],
      },
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    this.initPage()

    this.getUserRoles(this.localUser.split('@')[0])
  },
  methods: {
    initPage() {
      groupNames()
        .then(res => {
          this.envGroups = res.Data.clusters
          for (let i = 0, len = res.Data.clusters.length; i < len; i++) {
            let orderType = {}
            orderType.value = res.Data.clusters[i].env
            orderType.label = res.Data.clusters[i].env
            this.envList.push(orderType)
            this.envAddList.push({ value: res.Data.clusters[i].envId, label: res.Data.clusters[i].env })
          }
        })
        .catch(() => {})
    },
    unUsedSearch() {
      this.queryParam.action = 'unUsed'
      this.$refs.table.refresh()
      this.queryParam.action = ''
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        let arry = response.Data.data
        for (let i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    // 详细信息 相关接口
    handleDetail(record) {
      this.infoVisible = true
      getStreamRoute({ id: record.id }).then(res => {
        console.log(res.Data)
        this.detailIdVisible = true
        this.infoParam = res.Data
        this.infoParam.apisixDatas = JSON.parse(res.Data.apisixData, null, 2)
        // alert(JSON.stringify(this.infoParam))
      })
    },
    handleDel(record) {
      deleteStreamRoute({ id: record.id }).then(res => {
        if (res.Data.messageg === 'ok') {
          notification.success({
            message: '删除成功',
          })
          this.$refs.table.refresh(true)
        } else {
          notification.error({
            message: '删除失败',
          })
        }
      })
    },
    addStreamRoute(record, value) {
      switch (value) {
        case 'open':
          this.addVisible = true
          this.addParam = {
            upstreams: {
              nodes: {
                '127.0.0.1:80': 1,
                '127.0.0.1:80': 1,
              },
              type: 'roundrobin',
            },
          }
          break
        case 'close':
          this.addVisible = false
          break
        case 'add':
          antdFormValidate(this.$refs.addParamForm, valid => {
            if (valid) {
              var data = JSON.parse(JSON.stringify(this.addParam))
              data.upstream = JSON.stringify(data.upstreams)
              data.env = this.envAddList.find(item => item.value === data.envId).label
              createStreamRoute(data).then(res => {
                if (res.Data.messageg === 'ok') {
                  notification.success({
                    message: '创建成功',
                  })
                  this.addVisible = false
                  this.$refs.table.refresh(true)
                } else {
                  notification.error({
                    message: '删除失败',
                    description: res.Message,
                  })
                }
              })
            }
          })
      }
    },
    rowServerAdd() {
      this.updateParam.nodes.push({ ip: '127.0.0.1', port: 80, weight: 50 })
    },
    rowServerDelete(index) {
      this.updateParam.nodes.splice(index, 1)
    },
    handleUpdate(record, value) {
      switch (value) {
        case 'open':
          this.updateVisible = true
          this.updateParam = {
            id: record.id,
            env: record.env,
            envId: record.envId,
            serverPort: record.serverPort,
            remoteAddr: record.remoteAddr,
            users: record.user.split('|'),
            upstream: record.upstream,
          }
          var upstreamData = JSON.parse(record.upstream)
          var nodes = upstreamData.nodes
          this.updateParam.nodes = []

          for (var key in nodes) {
            var [ip, port] = key.split(':')
            var weight = nodes[key]
            this.updateParam.nodes.push({
              ip: ip,
              port: parseInt(port),
              weight: weight,
            })
          }

          break
        case 'close':
          this.updateVisible = false
          break
        case 'add':
          antdFormValidate(this.$refs.updateForm, valid => {
            if (valid) {
              this.updateParam.user = this.updateParam.users.join('|')
              var data = JSON.parse(JSON.stringify(this.updateParam))
              let newNodes = {}
              data.nodes.forEach(node => {
                newNodes[`${node.ip}:${node.port}`] = node.weight
              })
              let tempnodes = JSON.parse(data.upstream)
              tempnodes.nodes = newNodes
              delete data.nodes
              delete data.users
              data.upstream = JSON.stringify(tempnodes)
              putStreamRoute(data).then(res => {
                if (res.Data.messageg === 'ok') {
                  notification.success({
                    message: '创建成功',
                  })
                  this.updateVisible = false
                  this.$refs.table.refresh(true)
                } else {
                  notification.error({
                    message: '删除失败',
                    description: res.Message,
                  })
                }
              })
            }
          })
      }
    },
  },
}
</script>

<style lang="less">
.myClbClusterBox {
  height: 280px;
  width: 550px;
  .jsoneditor-vue {
    height: 100% !important;
  }
}
.myClbClusterBox1 {
  height: 280px;
  width: 400px;
  .jsoneditor-vue {
    height: 100% !important;
  }
}
.ant-divider-horizonta1l {
  clear: both;
  display: flex;
  margin: 10px 0;
  min-width: 100%;
  width: 100%;
}
.my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}
.nodes-container {
  max-height: 350px;
  overflow-x: auto; /* 允许垂直方向上的滚动 */
}
</style>
