/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-09-27 16:47:40
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-10 19:10:06
 * @FilePath: \cloud_web\src\views\gateway\apiRoute\comps\mockDatas.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */
const priorityArr = [
  {
    key: '==',
    value: '=='
  },
  {
    key: '~=',
    value: '~='
  },
  {
    key: '>',
    value: '>'
  },
  {
    key: '<',
    value: '<'
  },
  {
    key: '~~',
    value: '~~'
  },
  {
    key: '~*',
    value: '~*'
  },
  {
    key: 'in',
    value: 'in'
  },
  {
    key: 'has',
    value: 'has'
  },
  {
    key: '!',
    value: '!'
  }
]

const formMethods = [
  { name: 'GET', key: 'GET' },
  { name: 'HEAD', key: 'HEAD' },
  { name: 'POST', key: 'POST' },
  { name: 'PUT', key: 'PUT' },
  { name: 'DELETE', key: 'DELETE' },
  { name: 'OPTIONS', key: 'OPTIONS' },
  { name: 'PATCH', key: 'PATCH' }
]
const ranges = [
  { value: 'none', label: '无限制', disabled: false },
  { value: '************/24', label: '云立方(************/24)', disabled: false },
  { value: '********/16', label: '混合云(********/16)', disabled: false },
  { value: '**********/12', label: '内网(**********/12)', disabled: false },
  { value: '192.168.0.0/16', label: '内网(192.168.0.0/16)', disabled: true },
  { value: '10.0.0.0/8', label: '内网(10.0.0.0/8)', disabled: false }]
const discoverytypeOptions = [{
  value: 'consul_external',
  label: 'consul_external'
}, {
  value: 'dns',
  label: 'dns'
}, {
  value: 'consul_kv',
  label: 'consul_kv'
}, {
  value: 'kubernets',
  label: 'kubernets'
}]

export { priorityArr, formMethods, ranges, discoverytypeOptions }
