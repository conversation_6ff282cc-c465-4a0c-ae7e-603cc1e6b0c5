<template>
  <div style="min-height: 400px">
    <NocEditorJson
      :disabled="editorValue.disabled"
      v-model="editorValue"
      style="height: 380px"
      :show-btns="false"
      :mode="'code'"
      lang="zh">
    </NocEditorJson>
  </div>
</template>

<script>
export default {
  components: {
  },
  props: {
    jsonDatas: {
      type: Object,
      default: () => {
        return ''
      }
    }
  },
  data () {
    return {
      editorValue: this.jsonDatas
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.jsoneditor-vue {
  height: 380px !important;
}
</style>
