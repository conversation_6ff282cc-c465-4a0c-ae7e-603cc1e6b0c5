<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-09-26 18:21:57
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-08-08 17:19:53
 * @FilePath: \cloud_web\src\views\gateway\apiRoute\comps\handleRouteForm.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div class="app">
    <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
      <a-form-model-item v-if="!hiddenItem.includes('env')" label="集群环境" name="env">
        <a-select
          show-search
          style="width: 400px"
          :disabled="form.disabled || form.envDIsabled"
          allowClear
          v-model:value="form.envId"
          placeholder="请选择集群环境"
          @change="envChange(...arguments, 'env')"
          :filter-option="customFilter"
        >
          <a-select-option v-for="item in envGroups" :key="item.envId" :value="item.envId" :cluster="item.env">
            {{ item.env }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="hosts" name="hosts">
        <a-select
          style="width: 400px"
          :disabled="form.disabled"
          mode="tags"
          v-model:value="form.hosts"
          placeholder="域名或IP，支持泛域名，如'*.test.com',可以输入多个"
          @change="envChange(...arguments, 'host')"
        ></a-select>
        <a-tooltip>
          <template #title>域名：即第一步中申请的域名</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
      <!-- 路径数据 -->
      <a-form-model-item label="路径" name="uris">
        <a-select
          style="width: 400px"
          :disabled="form.disabled"
          mode="tags"
          v-model:value="form.uris"
          @change="envChange(...arguments, 'path')"
          placeholder="请求路径 ，可输入多个"
        >
          <a-select-option value="/*">/*</a-select-option>
        </a-select>
        <a-tooltip>
          <template #title>路径：URI，比如：/index.html或者/download?picname=z.jpg或者/upload?filename=aa.png</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
      <!-- 范围数据 -->
      <a-form-model-item label="访问范围" name="remote_addrs">
        <a-select
          style="width: 400px"
          mode="tags"
          :disabled="form.disabled"
          v-model:value="form.remote_addrs"
          placeholder="访问范围 ，可输入多个"
          @change="envChange(...arguments, 'remote_addrs')"
        >
          <a-select-option v-for="item in ranges" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-tooltip>
          <template #title>
            访问范围：允许访问该域名的来源IP范围，比如：********/16（网段）或者**************（具体IP）
          </template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
      <!-- Upstream -->
      <a-form-model-item label="Upstream" name="Upstream">
        <span style="margin-right: 8px; font-weight: 700">类型:</span>
        <a-radio-group
          :disabled="form.disabled"
          @change="radioChange('upStreamType')"
          v-model:value="form.upstream.name"
          default-value="nodes"
          button-style="solid"
        >
          <a-radio-button value="nodes">节点</a-radio-button>
          <a-radio-button value="discovery">服务发现</a-radio-button>
          <a-radio-button v-if="!hiddenItem.includes('upstream_id')" value="upstream_id">upstream_id</a-radio-button>
          <a-radio-button v-if="!hiddenItem.includes('none_upstream')" value="none_upstream">无配置</a-radio-button>
        </a-radio-group>
        <a-tooltip>
          <template #title>
            UPSTREAM：此处填写业务服务器的IP以及端口，最后一个是权重，当有多个业务IP，且需要设置权重时，设置该值，数字越大，权重越高
          </template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
        <!-- upstream下面表单 -->
        <a-form-model
          class="mt-2"
          labelAlign="left"
          layout="horizontal"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
          :model="form.upstream"
        >
          <!-- 节点 -->
          <template v-if="form.upstream.name === 'nodes'">
            <a-form-model-item label="" name="" v-for="(i, index) in form.upstream.nodes" :key="index">
              <a-row :gutter="8">
                <a-col :span="4">
                  <a-select v-model:value="form.upstream.scheme">
                    <a-select-option value="http">http</a-select-option>
                    <a-select-option value="https">https</a-select-option>
                    <a-select-option value="grpc">grpc</a-select-option>
                    <a-select-option value="grpcs">grpcs</a-select-option>
                  </a-select>
                </a-col>
                <a-col style="padding: 0; line-height: 32px; text-align: center" :span="1">://</a-col>
                <a-col :span="7">
                  <a-input :disabled="form.disabled" v-model:value="i.ip" placeholder="IP地址" />
                </a-col>
                <a-col :span="4">
                  <a-input :disabled="form.disabled" v-model:value="i.port" placeholder="端口" />
                </a-col>
                <a-col :span="3">
                  <a-input-number
                    :disabled="form.disabled"
                    title="权重"
                    v-model:value="i.weight"
                    :min="0"
                    :max="10000"
                    placeholder="权重"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="5" style="padding-left: 8px">
                  <tx-button :disabled="form.disabled" v-if="index === 0" type="primary" @click="upStreamFormAdd">
                    新增
                  </tx-button>
                  <tx-button :disabled="form.disabled" v-else type="danger" @click="upStreamFormDel(i)">删除</tx-button>
                </a-col>
              </a-row>
            </a-form-model-item>
          </template>
          <!-- 服务发现 -->
          <template v-if="form.upstream.name === 'discovery'">
            <a-form-model-item label="类型：">
              <a-select
                :disabled="form.disabled"
                style="width: 50%"
                v-model:value="form.upstream.discovery_type"
                placeholder="请选择"
              >
                <!-- 域名数据 -->
                <a-select-option v-for="item in discoverytypeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="服务名称：">
              <a-input :disabled="form.disabled" style="width: 50%" v-model:value="form.upstream.service_name" />
            </a-form-model-item>
          </template>
          <!-- upstream_id -->
          <template v-if="form.upstream.name === 'upstream_id'">
            <a-form-model-item label="upStream_id：">
              <a-row :gutter="24">
                <a-col :span="6">
                  <a-select v-model:value="form.upstream.scheme">
                    <a-select-option value="http">http</a-select-option>
                    <a-select-option value="https">https</a-select-option>
                    <a-select-option value="grpc">grpc</a-select-option>
                    <a-select-option value="grpcs">grpcs</a-select-option>
                  </a-select>
                </a-col>
                <a-col style="padding: 0; line-height: 32px" :span="1">://</a-col>
                <a-col :span="10">
                  <a-select
                    mode="tags"
                    :disabled="form.disabled"
                    allowClear
                    v-model:value="form.upstream.upstream_id"
                    @change="upstreamIdChange"
                    placeholder="请选择"
                  >
                    <!-- 域名数据 -->
                    <!-- upStreamIds -->
                    <a-select-option v-for="i in upStreamIds" :key="i" :value="i">{{ i }}</a-select-option>
                  </a-select>
                </a-col>
              </a-row>
            </a-form-model-item>
          </template>
          <!-- scheme -->
          <template v-if="form.upstream.name == 'discovery'">
            <a-form-model-item>
              <span style="margin-right: 8px; font-weight: 700">scheme:</span>
              <a-radio-group
                :disabled="form.disabled"
                v-model:value="form.upstream.scheme"
                default-value="http"
                button-style="solid"
              >
                <a-radio-button value="http">http</a-radio-button>
                <a-radio-button value="https">https</a-radio-button>
                <a-radio-button value="grpc">grpc</a-radio-button>
                <a-radio-button value="grpcs">grpcs</a-radio-button>
              </a-radio-group>
              <a-tooltip>
                <template #title>scheme：业务服务器支持的协议类型，比如：http, https, grpc, grpcs</template>
                <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
              </a-tooltip>
            </a-form-model-item>
          </template>
        </a-form-model>
      </a-form-model-item>
      <!-- <a-form-model-item label="连通性检查" v-if="form.upstream.nodes[0].ip!==''" prop="ack">
        <a-row :gutter="8">
        <a-col :span="12">
          <a-input
          v-model="ackInfo"
          disabled
          />
        </a-col>
        <a-col :span="12">
          <a-button type="primary" @click="getAckInfo">获取结果</a-button>
        </a-col>
      </a-row>
      </a-form-model-item> -->
      <a-form-model-item label="配置信息">
        <a-radio-group v-model:value="configInfos" button-style="solid">
          <a-radio-button value="base">基本配置</a-radio-button>
          <a-radio-button value="senior">高级配置</a-radio-button>
        </a-radio-group>
        <!-- <a-tooltip>
            <template #title> 网关高级配置：
              <br>
              HTTP METHOD：包括GET,POST,PUT,PATCH,OPTIONS,HEAD ; <br>
              WEBSOCKET是否开启 ; <br>
              插件功能，如URL重写等等 ; <br>
              健康检查（主动检查）;  <br>
              TTL（路由配置有效时间，过期后该配置自动下线）。</template>
            <a-icon style="margin-left: 8px;font-size:20px" type="question-circle" />
          </a-tooltip> -->
      </a-form-model-item>
      <!-- 方法 -->
      <a-form-model-item v-if="configInfos === 'senior'" label="方法" name="methods">
        <a-checkbox-group :disabled="form.disabled" v-model:value="form.methods">
          <a-checkbox v-for="it in formMethods" :value="it.key" :name="it.name" :key="it.key">
            {{ it.name }}
          </a-checkbox>
        </a-checkbox-group>
        <a-tooltip>
          <template #title>HTTP METHOD：包括GET,POST,PUT,PATCH,OPTIONS,HEAD</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
      <!-- 超时设置和优先级 - 紧凑布局 -->
      <a-form-model-item v-if="configInfos === 'senior'" label="超时设置">
        <div style="display: flex; gap: 16px; align-items: center">
          <div>
            <span style="margin-right: 8px">连接超时:</span>
            <a-input-number
              :disabled="form.disabled"
              v-model:value="form.timeout.connect"
              :min="0"
              :max="10000"
              style="width: 80px"
            />
          </div>
          <div>
            <span style="margin-right: 8px">发送超时:</span>
            <a-input-number
              :disabled="form.disabled"
              v-model:value="form.timeout.send"
              :min="0"
              :max="10000"
              style="width: 80px"
            />
          </div>
          <div>
            <span style="margin-right: 8px">接收超时:</span>
            <a-input-number
              :disabled="form.disabled"
              v-model:value="form.timeout.read"
              :min="0"
              :max="10000"
              style="width: 80px"
            />
          </div>
        </div>
      </a-form-model-item>
      <!-- 优先级 -->
      <a-form-model-item v-if="configInfos === 'senior'" label="优先级" name="priority">
        <a-input-number
          :disabled="form.disabled"
          v-model:value="form.priority"
          :min="0"
          :max="10"
          style="width: 120px"
        />
      </a-form-model-item>
      <!-- Vars -->
      <template v-if="configInfos === 'senior'">
        <a-form-model-item v-for="(i, index) in form.vars" :label="'Vars' + index" :key="index" name="priority">
          <a-input-group compact>
            <a-input :disabled="form.disabled" v-model:value="i.var_name" style="width: 25%" placeholder="参数名称" />
            <a-select
              :disabled="form.disabled"
              v-model:value="i.var_operator"
              style="width: 20%; margin-left: 8px"
              placeholder="运算符"
            >
              <a-select-option v-for="item in priorityArr" :key="item.key" :value="item.value">
                {{ item.value }}
              </a-select-option>
            </a-select>
            <a-input
              :disabled="form.disabled"
              v-model:value="i.var_value"
              style="width: 25%; margin-left: 8px"
              placeholder="参数值"
            />
            <tx-button :disabled="form.disabled" type="primary" v-if="index === 0" @click="priorityAdd">新增</tx-button>
            <tx-button :disabled="form.disabled" type="danger" v-else @click="priorityDel(i)">删除</tx-button>
          </a-input-group>
        </a-form-model-item>
      </template>
      <a-form-model-item v-if="configInfos === 'senior'" label="函数" name="filter_func">
        <a-textarea :disabled="form.disabled" style="width: 70%" v-model:value="form.filter_func"></a-textarea>
      </a-form-model-item>
      <!-- TTL -->
      <a-form-model-item v-if="configInfos === 'senior'" label="TTL" name="expiry_date">
        <a-input-number
          :disabled="form.disabled"
          id="inputNumber"
          v-model:value="form.expiry_date"
          :min="0"
          :max="9999999999"
        />
        <a-tooltip>
          <template #title>TTL（路由配置有效时间，过期后该配置自动下线。</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
      <!-- WebSocket -->
      <a-form-model-item v-if="configInfos === 'senior'" label="WebSocket" name="enable_websocket">
        <a-radio-group
          :disabled="form.disabled"
          default-value="open"
          v-model:value="form.enable_websocket"
          button-style="solid"
        >
          <a-radio-button :value="false">关闭</a-radio-button>
          <a-radio-button :value="true">开启</a-radio-button>
        </a-radio-group>
        <a-tooltip>
          <template #title>WEBSOCKET是否开启</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
      </a-form-model-item>
      <!-- 插件 -->
      <a-form-model-item v-if="configInfos === 'senior'" label="插件" name="plugins_check">
        <a-radio-group
          :disabled="form.disabled"
          @change="radioChange('plugin')"
          default-value="none"
          v-model:value="form.plugins_check"
          button-style="solid"
        >
          <a-radio-button value="none">无</a-radio-button>
          <!-- <a-radio-button value="proxy_rewrite"> Proxy-Rewrite </a-radio-button> -->
          <a-radio-button value="other">自定义插件</a-radio-button>
        </a-radio-group>
        <a-tooltip>
          <template #title>插件功能，如URL重写等等</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
        <template v-if="form.plugins_check === 'other'">
          <NocEditorJson
            :disabled="form.disabled"
            v-model="form.plugins.other"
            :show-btns="false"
            :mode="'code'"
            lang="zh"
          ></NocEditorJson>
        </template>
      </a-form-model-item>
      <!--健康检查  -->
      <a-form-model-item v-if="configInfos === 'senior'" label="健康检查">
        <a-radio-group
          :disabled="form.disabled"
          @change="radioChange('healthy')"
          default-value="no"
          v-model:value="form.health_check"
          button-style="solid"
        >
          <a-radio-button value="no">不检查</a-radio-button>
          <a-radio-button value="active">Active</a-radio-button>
          <a-radio-button value="activePassive">Active & Passive</a-radio-button>
        </a-radio-group>
        <a-tooltip>
          <template #title>健康检查（主动检查）</template>
          <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
        </a-tooltip>
        <!-- Active -->
        <template v-if="form.health_check != 'no'">
          <a-card title="Active参数配置" style="width: 100%">
            <a-row>
              <a-col :span="12">
                <p>
                  <span class="inlineSpan">类型：</span>
                  <a-radio-group
                    :disabled="form.disabled"
                    @change="radioChange('healthType')"
                    default-value="http"
                    v-model:value="form.upstream.checks.active.type"
                    button-style="solid"
                  >
                    <a-radio-button value="http">HTTP</a-radio-button>
                    <a-radio-button value="https">HTTPS</a-radio-button>
                    <a-radio-button value="tcp">TCP</a-radio-button>
                  </a-radio-group>
                </p>
                <p>
                  <span class="inlineSpan">超时时间(秒)：</span>
                  <a-input-number
                    :disabled="form.disabled"
                    v-model:value="form.upstream.checks.active.timeout"
                    :min="0"
                  />
                </p>
                <p>
                  <span class="inlineSpan">并发数：</span>
                  <a-input-number
                    :disabled="form.disabled"
                    v-model:value="form.upstream.checks.active.concurrency"
                    :min="0"
                  />
                </p>
                <p>
                  <span class="inlineSpan">Host：</span>
                  <a-input
                    :disabled="form.disabled"
                    style="width: 70%"
                    v-model:value="form.upstream.checks.active.host"
                  />
                </p>
                <p>
                  <span class="inlineSpan">Port：</span>
                  <a-input-number :disabled="form.disabled" :min="0" v-model:value="form.upstream.checks.active.port" />
                </p>
                <p v-if="form.upstream.checks.active.type !== 'tcp'">
                  <span class="inlineSpan">HTTP路径：</span>
                  <a-textarea
                    :disabled="form.disabled"
                    style="width: 70%"
                    v-model:value="form.upstream.checks.active.http_path"
                  />
                </p>
                <p v-if="form.upstream.checks.active.type !== 'tcp'">
                  <span class="inlineSpan">Headers：</span>
                  <a-select
                    :disabled="form.disabled"
                    mode="tags"
                    style="width: 200px"
                    v-model:value="form.upstream.checks.active.req_headers"
                    placeholder="headers，可输入多个"
                  ></a-select>
                </p>

                <p v-if="form.upstream.checks.active.type === 'https'">
                  <span>SSL证书检查：</span>
                  <a-switch
                    :disabled="form.disabled"
                    v-model:checked="form.upstream.checks.active.https_verify_certificate"
                  />
                </p>
              </a-col>
              <a-col :span="12">
                <a-card title="健康参数配置">
                  <p>
                    <span class="inlineSpan">间隔时间(秒)：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.active.healthy.interval"
                      :min="0"
                    />
                  </p>
                  <p v-if="form.upstream.checks.active.type != 'tcp'">
                    <span class="inlineSpan">状态码:</span>
                    <a-select
                      :disabled="form.disabled"
                      mode="tags"
                      style="width: 200px"
                      v-model:value="form.upstream.checks.active.healthy.http_statuses"
                      placeholder="状态码，可输入多个"
                    >
                      <!-- <a-select-option value="/*"> /*</a-select-option> -->
                    </a-select>
                    <!-- <a-textarea style="width: 60%" v-model:value="form.upstream.checks.active.healthy.http_statuses" /> -->
                  </p>
                  <p>
                    <span class="inlineSpan">成功次数：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.active.healthy.successes"
                      :min="0"
                    />
                  </p>
                </a-card>
                <a-card title=" 异常参数配置" style="margin-top: 8px">
                  <p>
                    <span class="inlineSpan">间隔时间(秒)：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.active.unhealthy.interval"
                      :min="0"
                    />
                  </p>
                  <p v-if="form.upstream.checks.active.type != 'tcp'">
                    <span class="inlineSpan">状态码:</span>
                    <a-select
                      :disabled="form.disabled"
                      style="width: 200px"
                      mode="tags"
                      v-model:value="form.upstream.checks.active.unhealthy.http_statuses"
                      placeholder="状态码，可输入多个"
                    >
                      <!-- <a-select-option value="/*"> /*</a-select-option> -->
                    </a-select>
                    <!-- <a-textarea style="width: 60%" v-model:value="form.upstream.checks.active.unhealthy.http_statuses" /> -->
                  </p>
                  <p v-if="form.upstream.checks.active.type != 'tcp'">
                    <span class="inlineSpan">HTTP失败次数：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.active.unhealthy.http_failures"
                      :min="0"
                    />
                  </p>
                  <p v-if="form.upstream.checks.active.type === 'tcp'">
                    <span class="inlineSpan">TCP失败次数：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.active.unhealthy.tcp_failures"
                      :min="0"
                    />
                  </p>
                  <p>
                    <span class="inlineSpan">超时时间(秒)：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.active.unhealthy.timeouts"
                      :min="0"
                    />
                  </p>
                </a-card>
              </a-col>
            </a-row>
          </a-card>
        </template>
        <!-- Passive -->
        <template v-if="form.health_check === 'activePassive'">
          <a-card title="Passive参数配置" style="width: 100%; margin-top: 8px">
            <a-row>
              <a-col :span="12">
                <a-card title="健康参数配置">
                  <p v-if="form.upstream.checks.active.type != 'tcp'">
                    <span class="inlineSpan">HTTP状态码:</span>
                    <a-select
                      :disabled="form.disabled"
                      style="width: 200px"
                      mode="tags"
                      v-model:value="form.upstream.checks.passive.healthy.http_statuses"
                      placeholder="状态码，可输入多个"
                    ></a-select>
                    <!-- <a-textarea style="width: 60%" v-model:value="form.upstream.checks.passive.healthy.http_statuses" /> -->
                  </p>
                  <p>
                    <span class="inlineSpan">成功次数：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.passive.healthy.successes"
                      :min="0"
                    />
                  </p>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title=" 异常参数配置" style="margin-left: 8px">
                  <p v-if="form.upstream.checks.active.type != 'tcp'">
                    <span class="inlineSpan">HTTP状态码:</span>
                    <a-select
                      mode="tags"
                      style="width: 200px"
                      v-model:value="form.upstream.checks.passive.unhealthy.http_statuses"
                      placeholder="状态码，可输入多个"
                    >
                      <!-- <a-select-option value=""> </a-select-option> -->
                    </a-select>
                    <!-- <a-textarea style="width: 60%" v-model:value="form.upstream.checks.passive.unhealthy.http_statuses" /> -->
                  </p>
                  <p v-if="form.upstream.checks.active.type != 'tcp'">
                    <span class="inlineSpan">HTTP失败次数：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.passive.unhealthy.http_failures"
                      :min="0"
                    />
                  </p>
                  <p v-if="form.upstream.checks.active.type === 'tcp'">
                    <span class="inlineSpan">TCP失败次数：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.passive.unhealthy.tcp_failures"
                      :min="0"
                    />
                  </p>
                  <p>
                    <span class="inlineSpan">超时时间(秒)：</span>
                    <a-input-number
                      :disabled="form.disabled"
                      v-model:value="form.upstream.checks.passive.unhealthy.timeouts"
                      :min="0"
                    />
                  </p>
                </a-card>
              </a-col>
            </a-row>
          </a-card>
        </template>
      </a-form-model-item>

      <!-- 负责人 -->
      <a-form-model-item label="负责人" v-if="!hiddenItem.includes('principal')">
        <!-- <a-select v-model:value="form.principal" style="width: 120px">
            <a-select-option :disabled="form.disabled" v-for="i in userList" :value="i.email" :key="i.uid">
              {{ i.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item> -->
        <a-input placeholder="请输入邮箱" :disabled="form.disabled" v-model:value="form.principal"></a-input>
        <!-- <AllUser
            :needOrg="false"
            :placeholder="'可按邮箱搜索'"
            :multiple="false"
            :checkValue="form.principal"
            @selectChange="orgChange"
          /> -->
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
import AllUser from '@/views/comp/allOrgUser/index.vue'
import { upstreamids, gatewayConnectivity } from '@/api/gateway/groupAndIp'
import { priorityArr, formMethods, ranges, discoverytypeOptions } from './mockDatas'
import cloneDeep from 'lodash.clonedeep'
export default {
  props: {
    // 需要隐藏的item
    hiddenItem: {
      type: Array,
      default: () => {
        return []
      },
    },
    envGroups: {
      type: Array,
      default: () => {
        return []
      },
    },
    userList: {
      type: Array,
      default: () => {
        return []
      },
    },
    routeInfos: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  components: { AllUser },

  watch: {
    routeInfos: {
      handler: function (val) {
        if (val && Object.keys(val).length) {
          this.form = cloneDeep({ ...this.form, ...val })
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      form: {
        envId: '',
        filter_func: '',
        hosts: [],
        uris: ['/*'], // 路径
        remote_addrs: ['none'], // 访问范围
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        timeout: { connect: 60, send: 60, read: 60 },
        vars: [
          {
            var_name: '',
            var_operator: '',
            var_value: '',
          },
        ],
        priority: 1,
        plugins_check: 'none', // 插件选择
        expiry_date: 0,
        enable_websocket: false,
        health_check: 'no',
        principal: '',
        plugins: {
          other: {},
        },
        upstream: {
          name: 'nodes',
          nodes: [
            {
              ip: '',
              port: '80',
              weight: 1,
            },
          ],
          discovery_type: '', // 服务发现--类型
          service_name: '', // 服务发现--名称
          upstream_id: [], // upstream_id---upstream_id
          scheme: 'http',
          checks: {
            active: {
              type: 'https', //
              timeout: 1, //
              concurrency: 10, //
              http_path: '/', //
              host: '',
              port: '',
              https_verify_certificate: true, //
              req_headers: ["{ 'X-Api-Engine': 'apisix' }"], //
              healthy: {
                interval: 1,
                http_statuses: [200, 302],
                successes: 2,
              },
              unhealthy: {
                interval: 1,
                http_statuses: [429, 404, 500, 501, 502, 503, 504, 505],
                http_failures: 5,
                tcp_failures: 2,
                timeouts: 3,
              },
            },
            passive: {
              healthy: {
                http_statuses: [
                  200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308,
                ],
                successes: '',
              },
              unhealthy: {
                http_statuses: [429, 500, 503],
                tcp_failures: 2,
                timeouts: 7,
                http_failures: 5,
              },
            },
          },
        },
        status: 1,
      },
      configInfos: 'base',
      rules: {},
      formMethods: formMethods,
      ranges: ranges,
      pluginFormMethods: formMethods.slice(0, formMethods.length - 1),
      discoverytypeOptions: discoverytypeOptions,
      upStreamIds: [],
      priorityArr: priorityArr,
      helpTxt: '',
      gatewayIp: '',
      loading: false,
      // ackInfo:''
    }
  },
  methods: {
    orgChange(orgs) {
      this.form.principal = orgs
    },
    onSubmit(value) {
      var targetIp, port
      if (this.form.upstream.nodes.length > 0) {
        targetIp = this.form.upstream.nodes[0].ip
        port = this.form.upstream.nodes[0].port
      }
      this.helpTxt = '正在连接。。。'
      this.loading = true
      gatewayConnectivity({ host: value, target_ip: targetIp, target_port: parseInt(port) })
        .then(res => {
          if (res.Data.message === 'reachable') {
            this.helpTxt = '连接成功！'
          } else {
            this.helpTxt = '连接失败,网络不通！'
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
          this.$message.error('接口错误')
        })
    },
    customFilter(input, option) {
      const env = option.cluster
      return env && env.toLowerCase().includes(input.toLowerCase())
    },
    // 表单radiochange后，change前tab内容要清空
    radioChange(value) {
      switch (value) {
        case 'upStreamType':
          if (typeof this.form.upstream.nodes === 'undefined') {
            // this.form.upstream.nodes = [
            //   {
            //     ip: '',
            //     port: '',
            //     weight: ''
            //   }
            // ]
            // this.$set(this.form.upream, {
            //   ...this.form.upstream,
            //   node: { ip: '', port: '', weight: '' }
            // })
          }
          if (this.form.upstream.name === 'none_upstream') {
            this.form.upstream.scheme = ''
          } else {
            this.form.upstream.scheme = 'http'
          }
          break
        case 'plugin':
          this.form.plugins.other = {}
          break
        case 'healthy':
          if (this.form.health_check === 'no') {
            this.form.upstream.checks = {
              active: {
                type: 'https', //
                timeout: '', //
                concurrency: '', //
                http_path: '/', //
                host: '',
                port: '',
                https_verify_certificate: '', //
                req_headers: [], //
                healthy: {
                  interval: '',
                  http_statuses: [],
                  successes: '',
                },
                unhealthy: {
                  interval: '',
                  http_statuses: [],
                  http_failures: '',
                  tcp_failures: '',
                  timeouts: '',
                },
              },
              passive: {
                healthy: {
                  http_statuses: [],
                  successes: '',
                },
                unhealthy: {
                  http_statuses: [],
                  tcp_failures: '',
                  timeouts: '',
                  http_failures: '',
                },
              },
            }
          } else if (this.form.health_check === 'active') {
            this.form.upstream.checks.active = {
              type: 'https', //
              timeout: 1, //
              concurrency: 10, //
              http_path: '/', //
              host: '',
              port: '',
              https_verify_certificate: true, //
              req_headers: ["{ 'X-Api-Engine': 'apisix' }"], //
              healthy: {
                interval: 1,
                http_statuses: [200, 302],
                successes: 2,
              },
              unhealthy: {
                interval: 1,
                http_statuses: [429, 404, 500, 501, 502, 503, 504, 505],
                http_failures: 5,
                tcp_failures: 2,
                timeouts: 3,
              },
            }
            this.form.upstream.checks.passive = {
              healthy: {
                http_statuses: [],
                successes: '',
              },
              unhealthy: {
                http_statuses: [],
                tcp_failures: '',
                timeouts: '',
                http_failures: '',
              },
            }
          } else if (this.form.health_check === 'activePassive') {
            this.form.upstream.checks.passive = {
              healthy: {
                http_statuses: [
                  200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308,
                ],
                successes: '',
              },
              unhealthy: {
                http_statuses: [429, 500, 503],
                tcp_failures: 2,
                timeouts: 7,
                http_failures: 5,
              },
            }
          }
          break
        case 'healthType':
          if (this.form.upstream.checks.active.type === 'http') {
            this.form.upstream.checks.active.https_verify_certificate = ''
          } else if (this.form.upstream.checks.active.type !== 'tcp') {
            this.form.upstream.checks.active.host = ''
            this.form.upstream.checks.active.port = ''
            this.form.upstream.checks.active.unhealthy.tcp_failures = ''
            this.form.upstream.checks.passive.unhealthy.tcp_failures = ''
          } else if (this.form.upstream.checks.active.type === 'tcp') {
            this.form.upstream.checks.active.https_verify_certificate = ''
            this.form.upstream.checks.passive.unhealthy.http_failures = ''
            this.form.upstream.checks.passive.unhealthy.http_statuses = [429, 500, 503]
            this.form.upstream.checks.passive.healthy.http_statuses = [
              200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308,
            ]
            this.form.upstream.checks.active.unhealthy.http_failures = ''
            this.form.upstream.checks.active.unhealthy.http_statuses = [429, 404, 500, 501, 502, 503, 504, 505]
            this.form.upstream.checks.active.healthy.http_statuses = [200, 302]
          }

        // somecode
      }
    },

    filterForm() {
      switch (this.form.upstream.name) {
        case 'nodes':
          this.form.upstream.discovery_type = ''
          this.form.upstream.service_name = ''
          this.form.upstream.upstream_id = ''
          break
        case 'discovery':
          this.form.upstream.nodes = [
            {
              ip: '',
              port: '',
              weight: '',
            },
          ]
          this.form.upstream.upstream_id = ''
          break
        case 'upstream_id':
          this.form.upstream.nodes = [
            {
              ip: '',
              port: '',
              weight: '',
            },
          ]
          this.form.upstream.discovery_type = ''
          this.form.upstream.service_name = ''
          break
        case 'none_upstream':
          this.form.upstream.nodes = [
            {
              ip: '',
              port: '',
              weight: '',
            },
          ]
          this.form.upstream.discovery_type = ''
          this.form.upstream.service_name = ''
          this.form.upstream.upstream_id = ''
          break
      }
      return this.form
    },
    handleInput(target) {
      if (target.length && target[target.length - 1].includes('|')) {
        let a = target[target.length - 1]
        let arr = a.split('|')
        target.pop()
        for (let i = 0; i < arr.length; i++) {
          target.push(arr[i])
        }
      }
    },
    // getAckInfo(){
    //   this.ackInfo="done"
    // },
    envChange() {
      switch (arguments[arguments.length - 1]) {
        case 'env':
          console.log(this.form.envId, 'this.form.envId')
          console.log(this.form, 'form')
          this.$emit('envChange', this.form.envId)
          break
        case 'host':
          this.handleInput(this.form.hosts)

          break
        case 'path':
          this.handleInput(this.form.uris)
          break
        case 'remote_addrs':
          this.form.remote_addrs = this.form.remote_addrs.map(v => (typeof v === 'string' ? v.trim() : v))
          this.handleInput(this.form.remote_addrs)
          break
      }
    },
    // upStream 表单添加
    upStreamFormAdd() {
      this.form.upstream.nodes.push({
        ip: '',
        port: '80',
        weight: 1,
      })
    },
    // upStream 表单删除
    upStreamFormDel(infos) {
      this.form.upstream.nodes.splice(this.form.upstream.nodes.indexOf(infos), 1)
    },
    priorityAdd() {
      this.form.vars.push({
        var_name: '',
        var_operator: '',
        var_value: '',
      })
    },
    priorityDel(infos) {
      this.form.vars.splice(this.form.vars.indexOf(infos), 1)
    },
    upstreamIdChange() {
      console.log(this.form.upstream.upstream_id)
      let len = this.form.upstream.upstream_id.length
      if (len > 1) {
        this.form.upstream.upstream_id = [this.form.upstream.upstream_id[len - 1]]
      }
      console.log(this.form.upstream.upstream_id, '=====')
    },
  },
  mounted() {
    upstreamids().then(res => {
      this.upStreamIds = res.Data.UpstreamId
    })
  },
}
</script>

<style lang="less" scoped>
.inlineSpan {
  display: inline-block;
  width: 100px;
  // height: 42px;
  // line-height: 42px;
}
</style>
