<template>
    <a-card :bordered="false" style="padding-bottom: 60px">
      <a-spin :spinning="loading">
        <!-- 顶部操作栏 -->
        <a-form :model="actForm" layout="inline">
          <a-form-item>
            <a-form-item label="集群环境">
              <a-select
                style="width: 180px"
                allowClear
                showSearch
                :options="envList"
                v-model:value="actForm.env"
                placeholder="请选择集群环境"
              ></a-select>
            </a-form-item>
          </a-form-item>
          <a-form-item>
            <a-input @keyup.enter="headerAct('query')" placeholder="upstream" v-model:value="actForm.upstream" />
          </a-form-item>
          <a-form-item>
            <div style="display: flex; align-items: center">
            <a-select v-model:value="actForm.hostSearchType" style="width: 80px; margin-right: 8px">
              <a-select-option value="consistent">精确</a-select-option>
              <a-select-option value="fuzzy">模糊</a-select-option>
            </a-select>
            <a-input @keyup.enter="headerAct('query')" placeholder="host/hosts" v-model:value="actForm.hosts" />
          </div>
          </a-form-item>
          <a-form-item>
            <a-input @keyup.enter="headerAct('query')" placeholder="uri/uris" v-model:value="actForm.uris" />
          </a-form-item>
          <a-form-item>
            <a-input @keyup.enter="headerAct('query')" placeholder="plugins" v-model:value="actForm.plugins" />
          </a-form-item>
          <a-form-item>
            <a-select allowClear v-model:value="actForm.status" style="width: 120px">
              <a-select-option key="online" value="1">在线</a-select-option>
              <a-select-option key="offline" value="2">下线</a-select-option>
              <a-select-option key="order_offline" value="3">工单注销</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <tx-button type="primary" @click="headerAct('query')" icon="search">查询</tx-button>
          </a-form-item>
          <a-form-item v-if="userRoles.includes('opsAdmin')">
            <tx-button type="primary" icon="plus" @click="headerAct('add')">新增</tx-button>
          </a-form-item>
          <!-- <a-form-item>
              <tx-button type="primary" icon="download" @click="headerAct('export')"> 导出 </tx-button>
            </a-form-item>
            <a-form-item>
              <tx-button type="primary" icon="share-alt" @click="headerAct('ruleConfig')"> 路由规则配置 </tx-button>
            </a-form-item> -->
          <a-form-item v-if="userRoles.includes('opsAdmin')">
            <tx-button type="primary" icon="swap" @click="headerAct('sync')">同步</tx-button>
          </a-form-item>
        </a-form>
        <!-- 表格 -->
        <a-table
          :scroll="{ x: 'max-content', y: 550 }"
          :pagination="pagination"
          :columns="columns"
          rowKey="id"
          :data-source="tableData"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record: eachInfos }">
            <template v-if="column.dataIndex == 'status'">
              <a-badge v-if="eachInfos.status === 1" status="success" />
              <a-badge v-else status="error" />
            </template>
            <template v-if="column.dataIndex == 'upstreamRow'">
              {{ eachInfos.upstreamRow }}
              <template v-if="eachInfos.status === 1 && getUpstreamNodes(eachInfos).length">
                <a-tooltip title="网关与node连通性检测">
                  <GlobalOutlined
                    style="margin-left: 8px; color: #1890ff; cursor: pointer; font-size: 16px"
                    @click.stop="showUpstreamNodes(eachInfos)"
                  />
                </a-tooltip>
              </template>
            </template>
            <template v-else-if="column.dataIndex == 'action'">
              <a style="margin-left: 8px" @click="actionHandler(eachInfos, 'detail')">详情</a>
              <a-dropdown>
                <a class="ant-dropdown-link">
                  变更
                  <a-icon type="down" />
                </a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <a href="javascript:;" @click="actionHandler(eachInfos, 'edit')">更新</a>
                    </a-menu-item>
                    <!-- <a-menu-item>
                      <a href="javascript:;" @click="actionHandler(eachInfos, 'plugin')">插件选择</a>
                    </a-menu-item> -->
                    <a-menu-item>
                      <a href="javascript:;" @click="actionHandler(eachInfos, 'clone')">克隆</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a
                        href="javascript:;"
                        v-if="eachInfos.status === 1"
                        type="danger"
                        @click="actionHandler(eachInfos, 'line')"
                      >
                        下线
                      </a>
                    </a-menu-item>
                    <a-menu-item>
                      <a
                        href="javascript:;"
                        v-if="!eachInfos.status"
                        type="primary"
                        @click="actionHandler(eachInfos, 'line')"
                      >
                        上线
                      </a>
                    </a-menu-item>
                    <a-menu-item>
                      <a
                        href="javascript:;"
                        v-if="!eachInfos.status"
                        type="danger"
                        @click="actionHandler(eachInfos, 'delete')"
                      >
                        删除
                      </a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </a-table>
        <!-- 弹窗 -->
        <a-modal
          @cancel="closeModal"
          :maskClosable="false"
          :width="1000"
          :title="modalTitle"
          v-model:visible="modalVisible"
        >
          <a-tabs :activeKey="tabKey" default-active-key="form" @change="modalTabChange">
            <a-tab-pane key="form" tab="表单" force-render>
              <routeForm
                v-if="modalVisible"
                :routeInfos="routeInfos"
                :userList="userList"
                :hiddenItem="hiddenItem"
                ref="routeForm"
                :envGroups="envGroups"
              />
            </a-tab-pane>
            <a-tab-pane key="json" :tab="tabName">
              <routeJson ref="routeJson" v-if="tabKey === 'json' && modalVisible" :jsonDatas="jsonInfos" />
            </a-tab-pane>
          </a-tabs>

          <template #footer>
            <tx-button :disabled="modalTitle == '查看详情'" key="back" @click="modalVisible = false">取消</tx-button>
            <tx-button
              :disabled="curentAct === 'detail'"
              key="submit"
              type="primary"
              :loading="loading"
              @click="handleOk"
            >
              确定
            </tx-button>
          </template>
        </a-modal>
      </a-spin>
    </a-card>
    <a-modal
      v-model:visible="showNodesModal"
      title="Upstream节点 (网关与node连通性检测)"
      @cancel="showNodesModal = false"
      :footer="null"
      width="500px"
    >
      <a-table :dataSource="currentUpstreamNodes" rowKey="ip" :pagination="false" size="small">
        <a-table-column title="IP" dataIndex="ip" key="ip" />
        <a-table-column title="端口" dataIndex="port" key="port" />
        <a-table-column title="操作" key="action" v-slot="{ record }">
          <a-button size="small" @click="handleNodeBtnClick(record)">检测</a-button>
        </a-table-column>
        <a-table-column title="结果" dataIndex="result" key="result"></a-table-column>
      </a-table>
    </a-modal>
</template>
<script>
import { groupNames } from '@/api/gateway/groupAndIp'
import {
  changeState,
  createRoute,
  editRoute,
  getDetails,
  getRouteList,
  routeDel,
  syncRoute,
  checkUpstreamNodeNetwork,
} from '@/api/gateway/apiRoute'
import routeForm from './comps/handleRouteForm.vue'
import routeJson from './comps/handleRouteJson.vue'
import pluginForm from '@/views/gateway/plugin.vue'
import cloneDeep from 'lodash.clonedeep'
import { getUserList } from '@/api/permission/user'
import store from '@/store'
import { GlobalOutlined } from '@ant-design/icons-vue'

const columns = [
  {
    title: '集群名称',
    dataIndex: 'Env',
    width: '110px',
    align: 'center',
    key: 'Env',
    fixed: 'left',
  },
  {
    title: 'Upstream',
    dataIndex: 'upstreamRow',
    align: 'center',
    width: '200px',
    key: 'upstreamRow',
    scopedSlots: { customRender: 'upstreamRow' },
  },
  {
    title: 'host/hosts',
    align: 'center',
    dataIndex: 'hosts',
    width: '300px',
    key: 'hosts',
  },
  {
    title: 'uri/uris',
    align: 'center',
    width: '400px',
    dataIndex: 'uris',
    key: 'uris',
  },
  {
    title: 'plugins',
    dataIndex: 'plugins',
    key: 'plugins',
    width: '300px',
  },
  {
    title: '路由状态',
    dataIndex: 'status',
    width: '90px',
    align: 'center',
    key: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '优先级',
    dataIndex: 'priority',

    width: '90px',
    align: 'center',
    key: 'priority',
    scopedSlots: { customRender: 'priority' },
  },
  {
    title: 'Action',
    align: 'center',
    // width: '400px',
    dataIndex: 'action',
    key: 'Action',
    scopedSlots: { customRender: 'action' },
    fixed: 'right',
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ['10', '20', '30', '40'], // 指定每页可以显示多少条
}
export default {
  components: { routeForm, routeJson, GlobalOutlined },
  data() {
    return {
      userRoles: [],
      tabKey: 'form',
      modalTitle: '',
      modalVisible: false,
      pluginVisible: false,
      loading: false,
      actForm: {
        envId: '',
        upstream: '',
        hosts: '',
        uris: '',
        status: '',
        hostSearchType: 'fuzzy',
      },
      showNodesModal: false,
      currentUpstreamEnvId: 0,
      currentUpstreamNodes: [],
      pagination: pagination,
      columns,
      tableData: [],
      envList: [],
      envGroups: [],
      userList: [], // 负责人列表
      routeInfos: {}, // 回显，更新路由
      jsonInfos: {}, // 切换json模式内容
      curentAct: '', // 当前按钮操作
      currentInfo: {}, // 当前操作的哪一条数据
      tabName: 'JSON数据',
      queryParams: {},
    }
  },
  mounted() {
    this.initPage()
    this.pageTableList()
  },
  methods: {
    initPage() {
      groupNames()
        .then(res => {
          this.envGroups = res.Data.clusters
          for (let i = 0, len = res.Data.clusters.length; i < len; i++) {
            let orderType = {}
            orderType.value = res.Data.clusters[i].env
            orderType.label = res.Data.clusters[i].env
            this.envList.push(orderType)
          }
        })
        .catch(() => {})
      // getUserList()
      //   .then((res) => {
      //     this.userList = [...new Set(res.Data.data)]
      //   })
      //   .catch(() => {
      //     this.userList = []
      //   })
      // 用户角色权限隔离
      getUserList({ searchText: store.getters.email }).then(response => {
        if (response.Data.data[0] && response.Data.data[0].roles) {
          this.userRoles = response.Data.data[0].roles
        } else {
          this.userRoles = []
        }
      })
    },
    getUpstreamNodes(info) {
      // 优先用 upstream 字段，解析 nodes
      try {
        if (info.upstream) {
          const obj = typeof info.upstream === 'string' ? JSON.parse(info.upstream) : info.upstream
          if (obj.nodes && Array.isArray(obj.nodes)) {
            return obj.nodes
          } else if (obj.nodes && typeof obj.nodes === 'object') {
            // 旧格式 { "*******:8080": weight }
            return Object.keys(obj.nodes).map(ipport => {
              let ip = ipport,
                port = ''
              if (ipport.indexOf(':') > -1) {
                ;[ip, port] = ipport.split(':')
              }
              return { ip, port, weight: obj.nodes[ipport] }
            })
          }
        }
      } catch {
        return []
      }
      return []
    },

    showUpstreamNodes(info) {
      const nodesArr = this.getUpstreamNodes(info)
      nodesArr.forEach(n => {
        n.result = undefined // 每次弹出先清空结果
        n.checking = false
      })
      this.currentUpstreamNodes = nodesArr
      this.showNodesModal = true
      this.currentUpstreamEnvId = info.EnvId
    },

    async handleNodeBtnClick(node) {
      if (!this.currentUpstreamEnvId) {
        this.$message.warning('未提供当前集群EnvId，无法检测。')
        return
      }
      node.checking = true
      node.result = '检测中...'
      try {
        const { Data } = await checkUpstreamNodeNetwork({
          envId: this.currentUpstreamEnvId,
          ip: node.ip,
          port: node.port,
        })
        node.result = Data.reachable ? '✅ 可连通' : `❌ 不可达：${Data.message || '未知错误'}`
      } catch (e) {
        node.result = '检测失败'
      } finally {
        node.checking = false
      }
    },
    // 页面表格数据
    pageTableList(params = {}) {
      if (Object.keys(params).length === 0) {
        params = {
          pageSize: 10,
          pageNo: 1,
        }
      }

      getRouteList({
        ...params,
        ...this.queryParams,
      })
        .then(res => {
          console.log(res.Data)
          if (res.Data.routes) {
            var cloneArrr = cloneDeep(res.Data.routes)
            cloneArrr.forEach(item => {
              // 数据拿到不能直接用，需要进行处理 优先使用upstreamId，若无使用upstream中nodes数组中的key拼接；再无则赋值空

              if (item.upstreamId && item.upstreamId.length) {
                item.upstreamRow = item.upstreamId
              } else {
                if (item.upstream) {
                  let obj = JSON.parse(item.upstream)
                  item.upstreamRow = ''
                  if (obj.nodes && Object.keys(obj.nodes).length) {
                    for (let i in obj.nodes) {
                      if (item.upstreamRow.length) {
                        item.upstreamRow = `${item.upstreamRow},${i}`
                      } else {
                        item.upstreamRow += i
                      }
                    }
                  } else if (obj.discovery_type && obj.discovery_type.length) {
                    item.upstreamRow = obj.service_name
                  } else {
                    item.upstreamRow = ''
                  }
                } else {
                  item.upstreamRow = ''
                }
              }
            })
            this.tableData = cloneDeep(cloneArrr)
            const pagination = { ...this.pagination }
            pagination.total = res.Data.totalCount
            pagination.current = res.Data.pageNo
            pagination.pageSize = res.Data.pageSize
            this.pagination = pagination
          } else {
            this.tableData = cloneDeep(cloneArrr)
            const pagination = { ...this.pagination }
            pagination.total = res.Data.totalCount
            pagination.current = res.Data.pageNo
            pagination.pageSize = res.Data.pageSize
            this.pagination = pagination
          }
        })
        .catch(() => {})
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination }
      pager.current = pagination.current
      this.pagination = pager
      this.pageTableList({
        pageSize: pagination.pageSize,
        pageNo: pagination.current,
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...filters,
      })
    },
    // 表格操作栏按钮
    actionHandler(info, action) {
      this.curentAct = action
      this.currentInfo = info
      this.tabName = 'JSON数据'
      switch (action) {
        case 'detail':
          //  查看详情

          this.tabName = 'Apisix数据'
          getDetails({ id: info.id })
            .then(res => {
              this.routeInfos = JSON.parse(res.Data.routeData)
              this.routeInfos = { ...this.routeInfos, disabled: true }
              this.jsonInfos = JSON.parse(res.Data.apisixRouteData)
              this.modalVisible = true
              this.modalTitle = 'Api路由详情'
            })
            .catch(() => {
              this.routeInfos = {}
            })
          break
        case 'clone':
          this.curentAct = 'add'
          getDetails({ id: info.id })
            .then(res => {
              this.routeInfos = JSON.parse(res.Data.routeData)
              if (!this.routeInfos.plugins) {
                this.routeInfos = {
                  ...this.routeInfos,
                  plugins: {
                    other: {},
                  },
                }
              }
              delete this.routeInfos.id
              delete this.routeInfos.route_id
              delete this.routeInfos.key
              delete this.routeInfos.createdAt
              delete this.routeInfos.updatedAt
              this.routeInfos.principal = noc.user.getUserInfo().email
              this.modalVisible = true
              this.modalTitle = '新增Api路由'
            })
            .catch(() => {
              this.routeInfos = {}
            })
          break
        case 'line': // 上线下线
          changeState({
            id: info.id,
            state: info.status === 1 ? '0' : '1',
          })
            .then(() => {
              info.status === 1 ? this.$message.info('下线成功') : this.$message.info('上线成功')
            })
            .catch(() => {})
            .finally(() => {
              this.pageTableList()
            })
          break
        case 'delete':
          routeDel({ id: info.id })
            .then(() => {
              this.$message.info('删除成功')
            })
            .catch(() => {
              this.$message.info('删除失败')
            })
            .finally(() => {
              this.pageTableList()
            })
          break
        case 'edit':
          getDetails({ id: info.id })
            .then(res => {
              this.routeInfos = JSON.parse(res.Data.routeData)
              if (!this.routeInfos.plugins) {
                this.routeInfos = {
                  ...this.routeInfos,
                  envDIsabled: true,
                  plugins: {
                    other: {},
                  },
                }
              }
              delete this.routeInfos.id
              delete this.routeInfos.route_id
              delete this.routeInfos.key
              delete this.routeInfos.createdAt
              delete this.routeInfos.updatedAt
              this.modalVisible = true
              this.modalTitle = 'Api路由更新'
              if (this.curentAct !== 'detail') {
                this.$nextTick(() => {
                  this.jsonInfos = this.$refs.routeForm.form
                })
              }
            })
            .catch(() => {
              this.routeInfos = {}
            })
          break
        // case 'plugin':
        //   this.pluginVisible = true
        //   break
      }
    },
    // 页头按钮操作
    headerAct(type) {
      switch (type) {
        case 'add': // 添加
          this.curentAct = 'add'
          this.routeInfos = {}
          this.modalVisible = true
          this.modalTitle = '新增Api路由'
          break
        case 'sync': // 同步
          for (const key in this.envGroups) {
            if (this.actForm.env == this.envGroups[key].env) {
              this.actForm.envId = this.envGroups[key].envId
            }
          }
          if (this.actForm.envId) {
            syncRoute({
              envId: this.actForm.envId,
            })
              .then(() => {
                this.$message.info('同步成功')
              })
              .catch(() => {
                this.$message.info('同步失败')
              })
          } else {
            this.$message.info('请选择集群')
          }
          break
        case 'query':
          let obj = cloneDeep(this.actForm)
          for (const key in obj) {
            if (!obj[key]) {
              delete obj[key]
            }
          }
          this.queryParams = obj
          obj = { ...obj, pageSize: 10, pageNo: 1 }
          this.pageTableList({ ...obj })

          break
      }
    },
    closeModal() {
      this.tabKey = 'form'
    },
    // 切换表单 json
    modalTabChange(val) {
      this.tabKey = val
      console.log('this.tabKey', this.tabKey)
      // if (this.curentAct !== 'detail') {
      //   console.log(2222)
      //   this.jsonInfos = this.$refs.routeForm.form
      // }
    },
    // 弹窗确定
    handleOk() {
      let params = null
      if (this.tabKey === 'form') {
        params = this.$refs.routeForm.filterForm()
      } else if (this.tabKey === 'json') {
        params = this.$refs.routeJson.editorValue
      }

      if (params.hosts && params.hosts.length) {
        for (let i = 0; i < params.hosts.length; i++) {
          params.hosts[i] = params.hosts[i].replace(/\s*/g, '')
        }
      }
      if (params.upstream.nodes && params.upstream.nodes.length) {
        for (let i = 0; i < params.upstream.nodes.length; i++) {
          params.upstream.nodes[i].ip = params.upstream.nodes[i].ip.replace(/\s*/g, '')
        }
      }
      if (
        this.$refs.routeForm.form &&
        (Array.isArray(this.$refs.routeForm.form.principal) || !this.$refs.routeForm.form.principal.length)
      ) {
        this.$message.info('请填写负责人邮箱')
      } else {
        if (this.curentAct === 'add') {
          createRoute({ routeData: JSON.stringify(params) })
            .then(res => {
              if (res.Code === 200) {
                this.$message.info('新增成功')
              }
            })
            .catch(() => {
              this.$message.info('新增失败')
            })
            .finally(() => {
              this.modalVisible = false
              this.pageTableList()
            })
        } else if (this.curentAct === 'edit') {
          delete params.id
          console.log('params', params)
          editRoute({ id: this.currentInfo.id, routeData: JSON.stringify(params) })
            .then(res => {
              if (res.Code === 200) {
                this.$message.info('修改成功')
              }
            })
            .finally(() => {
              this.modalVisible = false
              this.pageTableList()
            })
        }
      }
    },
  },
}
</script>
<style lang="less" scoped>
/deep/.ant-form-inline > .ant-row {
  width: auto !important;
}
</style>
