<template>
    <a-card :bordered="false">
      <a-spin :spinning="loading">
        <!-- 顶部操作栏 -->
          <a-form :model="actForm" layout="inline">
            <a-form-item>
              <a-form-item label="集群环境">
                <a-select allowClear show-search :filter-option="customFilter" v-model:value="actForm.env" style="width: 180px" placeholder="请选择集群环境">
                  <a-select-option v-for="item in envGroups" :cluster="item.env" :key="item.envId" :value="item.env">{{
                    item.env
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-form-item>
            <a-form-item>
              <a-input style="width:150px" placeholder="算法" v-model:value="actForm.algo" />
            </a-form-item>
            <a-form-item>
              <a-input placeholder="节点" style="width:150px" v-model:value="actForm.node" />
            </a-form-item>
            <a-form-item>
              <a-input style="width:150px" placeholder="描述" v-model:value="actForm.desc" />
            </a-form-item>
            <a-form-item>
              <a-input placeholder="upstreamId" style="width:150px" v-model:value="actForm.upstreamId" />
            </a-form-item>
            <a-form-item>
              <tx-button type="primary" icon="search" @click="search"> 查询 </tx-button>
            </a-form-item>
            <a-form-item>
              <tx-button type="primary" icon="plus" @click="addStream"> 新增 </tx-button>
            </a-form-item>
            <a-form-item>
              <tx-button type="primary" icon="download" @click="exports"> 导出 </tx-button>
            </a-form-item>
            <a-form-item>
              <tx-button type="primary" icon="swap" @click="cogradient"> 同步 </tx-button>
            </a-form-item>
          </a-form>
        <!-- 表格 -->
          <a-table
            :pagination="pagination"
            :columns="columns"
            rowKey="id"
            :data-source="streamData"
            :scroll="{ x: 1300 }"
            @change="handleTableChange"
          >
            <template #bodyCell="{column, record: eachInfos}">
            <template v-if="column.dataIndex == 'action'">
              <tx-button style="margin-left: 8px" @click="actionHandler(eachInfos, 'details')"> 详情 </tx-button>
              <tx-button
                style="margin-left: 8px; background-color: #67c23a; color: aliceblue"
                @click="actionHandler(eachInfos, 'update')"
              >
                更新
              </tx-button>
              <tx-button style="margin-left: 8px" type="danger" @click="actionHandler(eachInfos, 'delete')">
                删除
              </tx-button>
            </template>
            </template>
          </a-table>
        <!-- 弹窗 -->
        <a-modal :title="modalTitle" v-model:visible="modalVisible">
          <upStreamForm
            v-if="modalVisible"
            :formInfos="formInfos"
            ref="upStreamForm"
            @modalShow="modalShow"
            :envGroups="envGroups"
          />
          <template #footer>
            <tx-button :disabled="modalTitle == '查看详情'" key="back" @click="handleCancel"> 取消</tx-button>
            <tx-button
              :disabled="modalTitle == '查看详情'"
              key="submit"
              type="primary"
              :loading="loading"
              @click="handleOk"
            >
              确定
            </tx-button>
          </template>
        </a-modal>
      </a-spin>
    </a-card>
</template>
<script>
import { upstreamList, syncDatas, delStream } from '@/api/gateway/upStream'
import { groupNames } from '@/api/gateway/groupAndIp'
import upStreamForm from './comp/upStreanForm.vue'
import { loadXLSX } from '@/utils/vendorLoader'
const columns = [
  {
    title: '集群环境',
    dataIndex: 'env',
    width: '110px',
    key: 'env'
  },
  {
    title: 'ID',
    dataIndex: 'upstreamId',
    width: '200px',
    key: 'upstreamId'
  },
  {
    title: '节点',
    dataIndex: 'nodes',
    width: '450px',
    key: 'nodes'
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: '200px',
    key: 'desc'
  },
  {
    title: '算法',
    dataIndex: 'type',
    width: '110px',
    key: 'type'
  },
  {
    title: 'Action',
    dataIndex: 'action',
    width: '280px',
    key: 'Action',
    scopedSlots: { customRender: 'action' }
  }
]
const pagination = {
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ['10', '20', '30', '40'] // 指定每页可以显示多少条
}
export default {
  inject: ['reload'],
  name: 'PolicyManager',
  components: { upStreamForm },
  data () {
    return {
      actForm: {
        env: '',
        algo: '',
        node: '',
        desc: '',
        upstreamId: ''
      },
      modalTitle: '',
      modalVisible: false,
      streamData: [], // 集群数据
      columns,
      pagination: pagination,
      loading: false,
      currentRow: {},
      envGroups: [],
      formInfos: {}
    }
  },
  mounted () {
    this.init()
    groupNames()
      .then((res) => {
        this.envGroups = res.Data.clusters
      })
      .catch(() => {})
    loadXLSX()
  },
  methods: {
    // 页面初始化
    init () {
      this.loading = true
      const infos = {
        env: this.actForm.env,
        type: this.actForm.algo,
        nodes: this.actForm.node,
        desc: this.actForm.desc,
        upstreamId: this.actForm.upstreamId

      }
      upstreamList(infos)
        .then((res) => {
          this.streamData = res.Data.upstreams
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 同步数据
    cogradient () {
      this.loading = true
      syncDatas()
        .then(() => {
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleTableChange (pagination, filters, sorter) {
      // const pager = { ...this.pagination }
      // pager.current = pagination.current
      // this.pagination = pager
      // this.init({
      //   results: pagination.pageSize,
      //   page: pagination.current,
      //   sortField: sorter.field,
      //   sortOrder: sorter.order,
      //   ...filters,
      // })
    },
    // 搜索
    search () {
      this.init()
    },
    // 新增 Stream
    addStream () {
      this.modalTitle = '新增'
      this.modalVisible = true
      this.formInfos = {}
    },
    // 表格操作栏
    actionHandler (infos, act) {
      this.formInfos = infos
      switch (act) {
        case 'details':
          this.modalVisible = true
          this.modalTitle = '查看详情'
          this.formInfos = {
            ...this.formInfos,
            disabled: true,
            modalTitle: this.modalTitle
          }
          break
        case 'delete':
          const that = this
          this.$confirm({
            title: '删除',
            content: '确定删除吗？',
            okText: '确认',
            cancelText: '取消',
            onOk () {
              // 删除Stream
              delStream({ id: infos.id })
                .then(() => {})
                .catch(() => {})
                .finally(() => {
                  that.init()
                })
            }
          })
          break
        case 'update':
          this.modalVisible = true
          this.formInfos = {
            ...this.formInfos,
            disabled: false,
            nodeDisabled: true
          }
          this.modalTitle = '更新'
      }
    },
    // 导出
    exports () {
      this.loading = true
      const downloadColumns = [
        {
          title: '集群环境',
          dataIndex: 'env',
          key: 'env'
        },
        {
          title: 'ID',
          dataIndex: 'id',
          key: 'id'
        },
        {
          title: '节点',
          dataIndex: 'nodes',
          key: 'nodes'
        },
        {
          title: '描述',
          dataIndex: 'desc',
          key: 'desc'
        },
        {
          title: '算法',
          dataIndex: 'type',
          key: 'type'
        }
      ]
      upstreamList()
        .then(async (res) => {
          if (res.Data.hasOwnProperty('upstreams')) {
            const XLSX = await loadXLSX()
            const tableData = this.transData(downloadColumns, res.Data.upstreams)
            const ws = XLSX.utils.aoa_to_sheet(tableData)
            const wb = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(wb, ws, 'Asset')
            XLSX.writeFile(wb, 'UpStream.xlsx')
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 导出数据转换
    transData (columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.key)
        return acc
      }, {})
      const tableBody = tableList.map((item) => {
        return obj.keys.map((key) => item[key])
      })
      // const tableBody = this.getTableBody(tableList, obj)
      return [obj.titles, ...tableBody]
    },
    handleCancel (e) {
      this.modalVisible = false
    },
    handleOk () {
      switch (this.modalTitle) {
        case '新增':
          this.$refs.upStreamForm.handleAdd('add')
          break
        case '更新':
          this.$refs.upStreamForm.handleAdd('update', this.formInfos)
      }
    },
    modalShow (flag) {
      this.modalVisible = flag
      this.init()
    },
    customFilter(input, option) {
      const env = option.cluster
      return env && env.toLowerCase().includes(input.toLowerCase());
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-inline > .ant-row {
  width: auto !important;
}
</style>
