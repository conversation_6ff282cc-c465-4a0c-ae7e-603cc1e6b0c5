<template>
  <div class="app">
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 10 }">
        <a-form-model-item label="集群环境" name="env">
          <a-select
            :disabled="formInfos.nodeDisabled || formInfos.disabled"
            v-model:value="form.env.env"
            placeholder="请选择集群环境"
            @change="envChange"
          >
            <a-select-option v-for="item in envGroups" :key="item.envId" :value="item.envId">{{
              item.env
            }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="算法" name="algo">
          <a-select
            :disabled="formInfos.nodeDisabled || formInfos.disabled"
            v-model:value="form.algo"
            placeholder="请选择算法"
          >
            <a-select-option v-for="item in typeOptions" :key="item.key" :value="item.key">
              {{ item.display_name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item ref="nodes" label="节点" name="nodes">
          <a-input
            :disabled="formInfos.disabled"
            v-model:value="form.nodes"
            @blur="
              () => {
                $refs.nodes.onFieldBlur()
              }
            "
          />
        </a-form-model-item>
        <a-form-model-item ref="desc" label="描述" name="desc">
          <a-input
            :disabled="formInfos.nodeDisabled || formInfos.disabled"
            v-model:value="form.desc"
            @blur="
              () => {
                $refs.desc.onFieldBlur()
              }
            "
          />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="ID">
          <a-input :disabled="true" v-model:value="formInfos.upstreamId" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="key">
          <a-input :disabled="true" v-model:value="formInfos.key" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="checks">
          <a-input :disabled="true" v-model:value="formInfos.checks" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="retries">
          <a-input :disabled="true" v-model:value="formInfos.retries" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="timeout">
          <a-input :disabled="true" v-model:value="formInfos.timeout" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="hash_on">
          <a-input :disabled="true" v-model:value="formInfos.hashOn" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="name">
          <a-input :disabled="true" v-model:value="formInfos.name" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="pass_host">
          <a-input :disabled="true" v-model:value="formInfos.passHost" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="upstream_host">
          <a-input :disabled="true" v-model:value="formInfos.upstreamHost" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="scheme">
          <a-input :disabled="true" v-model:value="formInfos.scheme" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="创建时间">
          <a-input :disabled="true" v-model:value="formInfos.CreatedAt" />
        </a-form-model-item>
        <a-form-model-item v-if="formInfos.modalTitle && formInfos.modalTitle === '查看详情'" label="更新时间">
          <a-input :disabled="true" v-model:value="formInfos.UpdatedAt" />
        </a-form-model-item>
      </a-form-model>
  </div>
</template>
<script>
import { createStream, updateStream } from '@/api/gateway/upStream'
import { isEmpty } from 'lodash'

export default {
  props: {
    envGroups: {
      type: Array,
      default: () => {
        return []
      }
    },
    formInfos: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      form: {
        env: {
          envId: '',
          env: ''
        },
        algo: '',
        nodes: '',
        desc: ''
      },
      rules: {
        algo: [{ required: true, message: 'Please select Activity zone', trigger: 'change' }],
        env: [{ required: true, message: 'Please select Activity zone', trigger: 'change' }],
        nodes: [{ required: true, message: '请输入节点', trigger: 'blur' }],
        desc: [{ required: true, message: '请输入节点', trigger: 'blur' }]
      },
      // 算法下拉选
      typeOptions: [
        { key: 'roundrobin', display_name: '带权重的roundrobin' },
        { key: 'chash', display_name: '一致性哈希' },
        { key: 'ewma', display_name: '选择延迟最小的节点' },
        { key: 'least_conn', display_name: '选择 (active_conn + 1) / weight 最小的节点' }
      ]
    }
  },
  mounted () {
    if (Object.keys(this.formInfos).length) {
      this.form.env.envId = this.formInfos.id
      this.form.env.env = this.formInfos.env
      this.form.algo = this.formInfos.type
      this.form.nodes = this.formInfos.nodes
      this.form.desc = this.formInfos.desc
    } else {
      this.form = {
        env: {
          envId: '',
          env: ''
        },
        algo: '',
        nodes: '',
        desc: ''
      }
    }
  },
  methods: {
    handleAdd (act, infos) {
      antdFormValidate(this.$refs.ruleForm, (valid) => {
        if (valid) {
          let params = {
            envId: this.form.env.envId,
            env: this.form.env.env,
            type: this.form.algo,
            nodes: this.form.nodes,
            desc: this.form.desc
          }
          switch (act) {
            case 'add':
              createStream(params)
                .then((res) => {
                  // console.log(res, 'reesssss')
                })
                .catch(() => {
                  // console.log(err, 'err')
                })
                .finally(() => {
                  this.$emit('modalShow', false)
                })
              break
            case 'update':
              if (!isEmpty(infos)) {
                params = {
                  ...params,
                  id: infos.id
                }
              }
              updateStream(params)
                .then(() => {
                  // console.log(res, 'reesssss')
                })
                .catch(() => {
                  // console.log(err, 'err')
                })
                .finally(() => {
                  this.$emit('modalShow', false)
                })
              break
          }
        }
      })
    },
    envChange (value) {
      for (let i = 0; i < this.envGroups.length; i++) {
        if (this.envGroups[i].envId === value) {
          this.form.env = this.envGroups[i]
          break
        }
      }
    }
  }
}
</script>

<style></style>
