<template>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="集群环境">
                <a-select
                  show-search
                  :filter-option="customFilter"
                  placeholder="请选择集群"
                  v-model:value="queryParam.envId"
                  :allowClear="true"
                >
                  <a-select-option v-for="item in searchClusterList" :cluster="item.env"  :key="item.envId" :value="item.envId">
                    {{ item.env }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="证书名称">
                <a-input @keyup.enter="$refs.table.refresh(true)" v-model:value="queryParam.certName" placeholder="请输入证书名称" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button type="primary" style="margin-left: 10px" @click="handleAdd">新增</tx-button>
                <tx-button style="margin-left: 8px" @click="syncCluster">同步</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="key"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record, text}">
        <template v-if="column.dataIndex == 'status'">
          <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
        </template>
          <template v-else-if="column.dataIndex == 'action'">
            <tx-button type="link" style="width: 40px" @click="handleInfo(record)">详情</tx-button>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <a-modal v-model:visible="addVisible" title="新增Ssl证书" @cancel="cancelAddSsl" @ok="submitAddSsl">
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :model="addQueryParam"
          :rules="Rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="网关集群" name="envId">
            <a-select
              placeholder="请选择集群"
              v-model:value="addQueryParam.envId"
              :allowClear="true"
            >
              <a-select-option v-for="item in searchClusterList" :key="item.envId" :value="item.envId">
                {{ item.env }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="匹配规则" name="snis">
            <a-select
              placeholder="请选择域名"
              v-model:value="addQueryParam.snis"
              :allowClear="true"
            >
              <a-select-option v-for="item in snisList" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal v-model:visible="infoVisible" title="详情" :footer="null" :width="700">
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :model="infoParam"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-model-item label="网关集群">
            <a-input v-model:value="infoParam.env" />
          </a-form-model-item>
          <a-form-model-item label="证书名称">
            <a-input v-model:value="infoParam.certName" />
          </a-form-model-item>
          <a-form-model-item label="apisixID">
            <a-input v-model:value="infoParam.sslId" />
          </a-form-model-item>
          <a-form-model-item label="匹配规则" >
            <a-input v-model:value="infoParam.snis" />
          </a-form-model-item>
          <a-form-model-item label="证书内容">
            <a-textarea v-model:value="infoParam.cert" :auto-size="{ minRows: 3, maxRows: 8 }"/>
          </a-form-model-item>
          <a-form-model-item label="创建时间" >
            <a-input v-model:value="infoParam.createdAt" />
          </a-form-model-item>
          <a-form-model-item label="更新时间" >
            <a-input v-model:value="infoParam.updatedAt" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { getSslList, deleteSsl, getSnisList, createSsl, syncSsl } from '@/api/gateway/ssl'
import { groupNames } from '@/api/gateway/groupAndIp'
import { notification } from 'ant-design-vue'
// import { resolveOnChange } from 'ant-design-vue/es/input/Input'

const columns = [
  {
    title: '集群环境',
    dataIndex: 'env',
    sorter: true
  },
  {
    title: '证书名称',
    dataIndex: 'certName',
    sorter: true
  },
  {
    title: '匹配规则',
    dataIndex: 'snis',
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '170px',
    align: 'center',
    scopedSlots: { customRender: 'action' }
  }
]
const pagination = {
  showTotal: total => `共 ${total} 条`
}

const statusMap = {
  1: {
    status: 'success',
    text: '启用'
  },
  2: {
    status: 'error',
    text: '停用'
  }
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      searchClusterList: [],
      snisList: [],
      addQueryParam: { },
      infoParam: {},
      downloadVisible: false,
      downloadCertName: '',
      visible: false,
      verificationCode: '',
      addVisible: false,
      infoVisible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { },
      Rules: {
        'envId': [{ required: true, message: '请选择集群', trigger: 'change' }],
        'snis': [{ required: true, message: '请选择域名', trigger: 'change' }]
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getSslList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            } else {
              return res.Data
            }
          } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  mounted () {
    groupNames().then((response) => {
      this.searchClusterList = response.Data.clusters
    })
    getSnisList().then((response) => {
      this.snisList = response.Data.snis
    })
  },
  methods: {
    handleAdd () {
      this.addQueryParam = {}
      this.addVisible = true
    },
    cancelAddSsl () {
      this.addVisible = false
      this.addQueryParam = {}
    },
    statusFilter (type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter (type) {
      return statusMap[type]?.status || type
    },
    submitAddSsl () {
      antdFormValidate(this.$refs.addForm, (valid) => {
        if (valid) {
          createSsl(this.addQueryParam).then((res) => {
            if (res.Data.messageg === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '创建成功'
                  })
            } else {
                notification.error({
                  message: '创建失败'
                })
            }
          })
          this.cancelAddSsl()
        }
      })
    },
    syncCluster () {
      if (this.queryParam.envId !== undefined && this.queryParam.envId !== '') {
        syncSsl(this.queryParam.envId).then((res) => {
            if (res.Data.messageg === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '更新成功'
                  })
            } else {
                notification.error({
                  message: '创建失败'
                })
            }
          })
      } else {
        notification.error({
          message: '选择同步集群！'
        })
      }
    },
    handleCancel () {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    customFilter(input, option) {
      const env = option.cluster
      return env && env.toLowerCase().includes(input.toLowerCase());
    },
    handleInfo (record) {
      this.infoVisible = true
      this.infoParam = record
    },
    handleDel (record) {
      deleteSsl(record.id).then((res) => {
        if (res.Data.messageg === 'ok') {
          this.$refs.table.refresh(true)
          notification.success({
              message: '删除成功'
            })
        } else {
            notification.error({
              message: '删除失败'
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .myform {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
}
</style>
