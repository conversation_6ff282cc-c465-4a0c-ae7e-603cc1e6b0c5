<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-18 14:26:40
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-06-05 16:56:02
 * @FilePath: \cloud_web\src\views\gateway\gatewayEnv.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
    <a-card :bordered="false">
      <a-form layout="inline">
        <a-form-item>
          <a-input-search placeholder="搜索集群和域名" enter-button @search="onSearch" />
        </a-form-item>
        <a-form-item>
          <tx-button type="primary" @click="gatewayHandler('{}', 'addRoot')">新增集群</tx-button>
        </a-form-item>
        <a-form-item>
          <tx-button type="primary" @click="exports">导出</tx-button>
        </a-form-item>
      </a-form>
      <a-table
        :pagination="pagination"
        :columns="columns"
        rowKey="idKey"
        :data-source="routeData"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record: eachInfos }">
          <template v-if="column.dataIndex == 'action'">
            <tx-button type="primary" v-if="eachInfos.type == 'group'" @click="gatewayHandler(eachInfos, 'addChild')">
              新增IP
            </tx-button>
            <tx-button style="margin-left: 8px" @click="gatewayHandler(eachInfos, 'details')">详情</tx-button>
            <tx-button
              style="margin-left: 8px; background-color: #67c23a; color: aliceblue"
              @click="gatewayHandler(eachInfos, 'update')"
            >
              更新
            </tx-button>
            <tx-button style="margin-left: 8px" type="danger" @click="gatewayHandler(eachInfos, 'delete')">
              删除
            </tx-button>
          </template>
        </template>
      </a-table>
    </a-card>
    <a-modal :title="modalTitle" v-model:visible="modalVisible">
      <template #footer>
        <tx-button :disabled="modalTitle == '查看详情'" key="back" @click="handleCancel">取消</tx-button>
        <tx-button
          :disabled="modalTitle == '查看详情'"
          key="submit"
          type="primary"
          :loading="loading"
          @click="handleOk"
        >
          确定
        </tx-button>
      </template>
      <!-- 弹窗表单 -->
      <a-form-model
        v-if="modalVisible"
        ref="myForm"
        :model="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 12 }"
        :rules="rules"
      >
        <a-form-model-item
          v-if="Object.keys(this.form).indexOf('name') != -1"
          ref="name"
          :label="form.type == 'group' ? '集群名称' : 'IP'"
          name="name"
        >
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.name" />
        </a-form-model-item>

        <a-form-model-item v-if="Object.keys(this.form).indexOf('domain') != -1" label="域名" name="domain">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.domain" />
        </a-form-model-item>
        <a-form-model-item v-if="Object.keys(this.form).indexOf('version') != -1" label="版本" name="version">
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.version" />
        </a-form-model-item>
        <a-form-model-item v-if="Object.keys(this.form).indexOf('globalRule') != -1" label="全局规则" name="globalRule">
          <a-switch :disabled="modalTitle == '查看详情'" :checked="form.globalRule === 'enable'" checked-children="开" un-checked-children="关" @change="globalRuleChange(form)" />
        </a-form-model-item>
        <!--  modalTitle !== '更新' -->
        <a-form-model-item
          v-if="Object.keys(this.form).indexOf('apiKey') != -1 && form.type === 'ip'"
          label="apiKey"
          name="apiKey"
        >
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.apiKey" />
        </a-form-model-item>
        <a-form-model-item
          v-if="Object.keys(this.form).indexOf('etcdPwd') != -1 && form.type === 'ip'"
          label="etcdPwd"
          name="etcdPwd"
        >
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.etcdPwd" />
        </a-form-model-item>

        <!-- && form.type == 'ip' -->
        <a-form-model-item
          v-if="Object.keys(this.form).indexOf('envId') != -1 && form.type !== 'ip'"
          label="集群环境"
          name="envId"
        >
          <a-select
            :disabled="modalTitle == '查看详情' || modalTitle === '添加IP'"
            v-model:value="form.envId"
            placeholder="请选择集群环境"
          >
            <a-select-option v-for="item in envGroups" :key="item.envId" :value="item.envId">
              {{ item.env }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          v-if="Object.keys(this.form).indexOf('createdAt') != -1 && modalTitle == '查看详情'"
          label="创建时间"
          name="createdAt"
        >
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.createdAt" />
        </a-form-model-item>
        <a-form-model-item
          v-if="Object.keys(this.form).indexOf('updatedAt') != -1 && modalTitle == '查看详情'"
          label="更新时间"
          name="updatedAt"
        >
          <a-input :disabled="modalTitle == '查看详情'" v-model:value="form.updatedAt" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
</template>

<script>
import {
  createGroup,
  createNode,
  deleteGroup,
  deleteNode,
  groupNames,
  tableInfos,
  updateGroup,
  updateNode,
} from '@/api/gateway/groupAndIp'
import { loadXLSX } from '@/utils/vendorLoader'
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '域名',
    dataIndex: 'domain',
    key: 'domain',
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
  },
  {
    title: '全局规则',
    dataIndex: 'globalRule',
    key: 'globalRule',
  },
  {
    title: 'Action',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ['10', '20', '30', '40'], // 指定每页可以显示多少条
}
export default {
  inject: ['reload'],
  name: 'PolicyManager',
  data() {
    return {
      routeData: [], // 集群数据
      columns,
      pagination: pagination,
      modalTitle: '',
      modalVisible: false,
      loading: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        domain: [{ required: true, message: '请输入域名', trigger: 'blur' }],
        apiKey: [{ required: true, message: '请输入apikey', trigger: 'blur' }],
        envId: [{ required: true, message: '请选择集群环境', trigger: 'change' }],
      },
      currentRow: {},
      envGroups: [],
    }
  },
  mounted() {
    this.initPage()
    groupNames()
      .then(res => {
        this.envGroups = res.Data.clusters
      })
      .catch(() => {})
    loadXLSX()
  },

  methods: {
    // table改变
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination }
      pager.current = pagination.current
      this.pagination = pager
      this.initPage({
        pageSize: pagination.pageSize,
        pageNo: pagination.current,
      })
    },
    // 页面初始化
    initPage(params = {}) {
      if (this.routeData) {
        this.routeData = []
      }
      if (Object.keys(params).length === 0) {
        const pages = {
          pageSize: 10,
          current: 1,
        }
        this.pagination = pages
      }
      tableInfos({
        pageSize: 10,
        pageNo: 1,
        ...params,
      }).then(res => {
        const pagination = { ...this.pagination }
        res.Data.totalCount ? (pagination.total = res.Data.totalCount) : (pagination.total = 0)
        this.routeData = res.Data.clusters
        this.pagination = pagination
      })
    },
    exports() {
      const downloadColumns = [
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: '域名',
          dataIndex: 'domain',
          key: 'domain',
        },
      ]
      tableInfos().then(async res => {
        if (res.Data.hasOwnProperty('clusters')) {
          const XLSX = await loadXLSX()
          const tableData = this.transData(downloadColumns, res.Data.clusters)
          const ws = XLSX.utils.aoa_to_sheet(tableData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Asset')
          XLSX.writeFile(wb, 'Asset.xlsx')
        }
      })
    },
    transData(columns, tableList) {
      const obj = columns.reduce((acc, cur) => {
        if (!acc.titles && !acc.keys) {
          acc.titles = []
          acc.keys = []
        }
        acc.titles.push(cur.title)
        acc.keys.push(cur.key)
        return acc
      }, {})
      // const tableBody = tableList.map((item) => {
      //   return obj.keys.map((key) => item[key])
      // })
      const tableBody = this.getTableBody(tableList, obj)
      return [obj.titles, ...tableBody]
    },

    getTableBody(arr, obj) {
      // const bodys = arr.map((item) => {
      // })
      const bodys = []
      for (let i = 0; i < arr.length; i++) {
        bodys.push(obj.keys.map(key => arr[i][key]))
        if (arr[i].children && arr[i].children.length > 0) {
          bodys.push(...this.getTableBody(arr[i].children, obj))
        }
      }
      return bodys
    },
    handleCancel(e) {
      this.modalVisible = false
    },
    // 弹框确定
    handleOk() {
      this.loading = true
      // somecode
      antdFormValidate(this.$refs.myForm, valid => {
        if (valid) {
          this.modalVisible = false
          // 校验成功后，根据弹框名称来确定要请求的接口
          switch (this.modalTitle) {
            case '添加IP':
              const params = {
                env: this.currentRow.name,
                envId: this.form.envId,
                apiKey: this.form.apiKey,
                ip: this.form.name,
                domain: this.form.domain,
                etcdPwd: this.form.etcdPwd,
              }
              createNode(params)
                .then(res => {
                  this.initPage() // 添加成功重新初始化界面
                })
                .catch(() => {})
              break
            case '更新':
              // somecode
              if (this.form.type === 'group') {
                const params = {
                  env: this.form.name,
                  domain: this.form.domain,
                  id: this.form.id,
                  apiKey: this.form.apiKey,
                  version: this.form.version,
                  globalRule: this.form.globalRule,
                }
                updateGroup(params)
                  .then(res => {
                    this.initPage()
                  })
                  .catch(() => {})
              } else if (this.form.type === 'ip') {
                console.log(this.form, '------')

                const patrams = {
                  // env: this.currentRow.name,
                  // envId: this.form.envId,
                  apiKey: this.form.apiKey,
                  ip: this.form.name,
                  id: this.form.id,
                }
                updateNode(patrams)
                  .then(res => {
                    this.$message.info('更新成功')
                    this.initPage()
                  })
                  .catch(() => {
                    this.$message.error('更新失败')
                  })
              }
              break
            case '新增集群':
              // somecode
              const obj = {
                env: this.form.name,
                domain: this.form.domain,
                version: this.form.version,
              }
              createGroup(obj)
                .then(() => {
                  this.$message.info('新增成功')
                  this.initPage()
                })
                .catch(() => {
                  this.$message.error('新增失败')
                })
              break
          }
        } else {
          return false
        }
      })
      this.loading = false
    },

    // 表格操作栏事件
    gatewayHandler(infos, types) {
      this.currentRow = infos
      this.modalVisible = true
      console.log(this.currentRow, 'this.currentRowthis.currentRowthis.currentRow')
      this.form = this.currentRow
      // console.log(this.form, 'this.form this.form this.form ')
      switch (types) {
        case 'addChild': // 添加ip
          this.modalTitle = '添加IP'
          this.form = {
            type: 'ip',
            name: '',
            envId: infos.id,
            apiKey: '',
            etcdPwd: '',
            domain: infos.domain,
          }
          break
        case 'details': // 查看详情
          this.modalTitle = '查看详情'
          break
        case 'update': // 更新
          this.modalTitle = '更新'
          this.form = {
            ...this.form,
            apiKey: '',
          }
          break
        case 'delete': // 删除
          // this.modalTitle = '删除'
          this.modalVisible = false
          const that = this
          this.$confirm({
            title: '删除',
            content: '确定删除' + this.form.name + '吗？',
            okText: '确认',
            cancelText: '取消',
            onOk() {
              // 删除IP
              if (that.form.type === 'ip') {
                deleteNode({
                  id: that.form.id,
                }).then(res => {
                  that.initPage()
                })
              } else if (that.form.type === 'group') {
                deleteGroup({
                  id: that.form.id,
                })
                  .then(res => {
                    that.initPage()
                  })
                  .catch(() => {})
              }
            },
          })
          break
        case 'addRoot': // 删除
          this.modalTitle = '新增集群'
          this.form = {
            name: '',
            domain: '',
            type: 'group',
            version: '',
          }
          break
      }
    },
    onSearch(value) {
      this.initPage({
        pageSize: 10,
        pageNo: 1,
        searchText: value,
      })
    },
    globalRuleChange(form) {
      if (form.globalRule === 'enable') {
        form.globalRule = 'disable'
      } else {
        form.globalRule = 'enable'
      }
    }
  },
}
</script>

<style lang="less" scoped>
/deep/.ant-form-inline > .ant-row {
  width: auto !important;
}
</style>
