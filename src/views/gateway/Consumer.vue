<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="集群环境">
                <a-select
                  show-search
                  :filter-option="customFilter"
                  placeholder="请选择集群"
                  v-model:value="queryParam.envId"
                  :allowClear="true"
                >
                  <a-select-option v-for="item in searchClusterList" :key="item.envId" :value="item.envId">
                    {{ item.env }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="消费者名称">
                <a-input @keyup.enter="$refs.table.refresh(true)" v-model:value="queryParam.consumerName" placeholder="请输入名称" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button type="primary" style="margin-left: 10px" @click="handleAdd">新增</tx-button>
                <tx-button style="margin-left: 8px" @click="syncCluster">同步</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :pagination="pagination"
        :scroll="scrolls"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record, text}">
        <template v-if="column.dataIndex == 'status'">
          <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
        </template>
          <template v-else-if="column.dataIndex == 'action'">
            <tx-button type="link" style="width: 40px" @click="handleInfo(record)">详情</tx-button>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <a-modal v-model:visible="addVisible" title="新增消费者" @cancel="cancelAdd" @ok="submitAdd" :width="700">
        <a-form-model
          layout="horizontal"
          ref="addForm"
          :model="addQueryParam"
          :rules="Rules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-model-item label="网关集群" name="envId">
            <a-select
              placeholder="请选择集群"
              v-model:value="addQueryParam.envId"
              :allowClear="true"
            >
              <a-select-option v-for="item in searchClusterList" :key="item.envId" :value="item.envId">
                {{ item.env }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="消费者名称" name="consumerName">
            <a-input v-model:value="addQueryParam.consumerName" />
          </a-form-model-item>
          <a-form-model-item label="插件" name="plugins">
            <NocEditorJson
              v-model="addQueryParam.plugins"
              :show-btns="false"
              style="height: 360px"
              :mode="'code'"
              lang="zh"
            >
            </NocEditorJson>
          </a-form-model-item>
          <a-form-model-item label="备注">
            <a-input v-model:value="addQueryParam.desc" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal v-model:visible="infoVisible" title="详情" :footer="null" :width="700">
        <a-form-model
          layout="horizontal"
          :model="infoParam"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-model-item label="网关集群">
            <a-input v-model:value="infoParam.env" />
          </a-form-model-item>
          <a-form-model-item label="消费者名称">
            <a-input v-model:value="infoParam.consumerName" />
          </a-form-model-item>
          <a-form-model-item label="备注" >
            <a-input v-model:value="infoParam.desc" />
          </a-form-model-item>
          <a-form-model-item label="插件详情">
            <NocEditorJson
              v-model="infoParam.plugins"
              :show-btns="false"
              style="height: 360px"
              :mode="'code'"
              lang="zh"
            >
            </NocEditorJson>
          </a-form-model-item>
          <a-form-model-item label="创建时间" >
            <a-input v-model:value="infoParam.createdAt" />
          </a-form-model-item>
          <a-form-model-item label="更新时间" >
            <a-input v-model:value="infoParam.updatedAt" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { createConsumer, getConsumerList, syncConsumer, deleteConsumer } from '@/api/gateway/consumer'
import { groupNames } from '@/api/gateway/groupAndIp'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: '集群环境',
    dataIndex: 'env',
    width: '200px',
    sorter: true
  },
  {
    title: '消费者名称',
    dataIndex: 'consumerName',
    width: '200px',
    sorter: true
  },
  {
    title: '插件',
    dataIndex: 'plugins',
    width: '600px',
    sorter: true
  },
  {
    title: '备注',
    width: '100px',
    dataIndex: 'desc'
  },
  {
    title: '更新时间',
    width: '200px',
    dataIndex: 'updatedAt',
    sorter: true
  },
  {
    title: '操作',
    width: '150px',
    dataIndex: 'action',
    align: 'center',
    scopedSlots: { customRender: 'action' }
  }
]
const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis,
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      searchClusterList: [],
      scrolls: {},
      addQueryParam: { plugins: {} },
      infoParam: {},
      downloadVisible: false,
      downloadCertName: '',
      visible: false,
      verificationCode: '',
      addVisible: false,
      infoVisible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { },
      Rules: {
        'envId': [{ required: true, message: '请选择集群', trigger: 'change' }],
        'consumerName': [{ required: true, message: '请输入消费者名称', trigger: 'change' }],
        'plugins': [{ required: true, message: '请输入JSON格式插件内容', trigger: 'change' }]
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getConsumerList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
             this.scrolls = { x: this.columns.length * 120 }
             return res.Data
         } else {
             return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
         }
        })
      }
    }
  },
  mounted () {
    groupNames().then((response) => {
      this.searchClusterList = response.Data.clusters
    })
  },
  methods: {
    handleAdd () {
      this.addQueryParam = { plugins: {} }
      this.addVisible = true
    },
    cancelAdd () {
      this.addVisible = false
      this.addQueryParam = { plugins: {} }
    },
    statusFilter (type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter (type) {
      return statusMap[type]?.status || type
    },
    customFilter(input, option) {
      const env = option.cluster
      return env && env.toLowerCase().includes(input.toLowerCase());
    },
    submitAdd () {
      antdFormValidate(this.$refs.addForm,(valid) => {
        if (valid) {
          this.addQueryParam.plugins = JSON.stringify(this.addQueryParam.plugins)
          createConsumer(this.addQueryParam).then((res) => {
            if (res.Data.messageg === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '创建成功'
                  })
            } else {
                notification.error({
                  message: '创建失败'
                })
            }
          })
          this.cancelAdd()
        }
      })
    },
    syncCluster () {
      if (this.queryParam.envId !== undefined && this.queryParam.envId !== '') {
        syncConsumer({ id: this.queryParam.envId }).then((res) => {
            if (res.Data.messageg === 'ok') {
               this.$refs.table.refresh(true)
                notification.success({
                    message: '更新成功'
                  })
            } else {
                notification.error({
                  message: '创建失败'
                })
            }
          })
      } else {
        notification.error({
          message: '选择同步集群！'
        })
      }
    },
    handleCancel () {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    handleInfo (record) {
      this.infoVisible = true
      this.infoParam = JSON.parse(JSON.stringify(record))
      this.infoParam.plugins = JSON.parse(record.plugins)
    },
    handleDel (record) {
      deleteConsumer(record.iD).then((res) => {
        if (res.Data.messageg === 'ok') {
          this.$refs.table.refresh(true)
          notification.success({
              message: '删除成功'
            })
        } else {
            notification.error({
              message: '删除失败'
            })
        }
      })
    }
  }
}
</script>

<style lang="less" >
.jsoneditor-vue{
	height: 100%;
}
  .myform {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
}
</style>
