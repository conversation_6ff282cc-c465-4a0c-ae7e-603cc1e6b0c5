<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.searchText" placeholder="历史路由/新路由" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="用户">
                <a-input v-model:value="queryParam.email" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="操作类型">
                <a-select
                  v-model:value="queryParam.recordType"
                  allowClear
                  placeholder="操作类型"
                  :options="recordTypeList"
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 4) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'size'">
            <a-badge :text="sizeFilter(text)" />
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleDetail(record)">详情</a>
          </template>
        </template>

      </s-table>
      <a-drawer
        title="详情"
        placement="right"
        :closable="false"
        width="40%"
        :visible="detailIdVisible"
        @close="detailIdVisible = false"
      >
        <a-card :bordered="false" :model="detailData" ref="detailData">
          <a-descriptions :column="2" >
            <a-descriptions-item label="EIP">{{ detailData.publicIp }}</a-descriptions-item>
            <a-descriptions-item label="绑定资源IP">{{ detailData.ip }}</a-descriptions-item>
            <a-descriptions-item label="绑定资源ID">{{ detailData.instanceId }}</a-descriptions-item>
            <a-descriptions-item label="机房">{{ detailData.idc }}</a-descriptions-item>
            <a-descriptions-item label="UUID">{{ detailData.uuid }}</a-descriptions-item>
            <a-descriptions-item label="interfaceId">{{ detailData.networkInterfaceId }}</a-descriptions-item>
            <a-descriptions-item label="描述">{{ detailData.description }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ detailData.comment }}</a-descriptions-item>
            <a-descriptions-item label="区">{{ detailData.zone }}</a-descriptions-item>
            <a-descriptions-item label="区域">{{ detailData.region }}</a-descriptions-item>
            <a-descriptions-item label="项目ID">{{ detailData.projectId }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ detailData.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-drawer>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import moment from 'moment'
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { getRouteRecord } from '@/api/gateway/apiRoute.js'

const columns = [
  {
    title: '操作类型',
    dataIndex: 'operationType',
    width: '90px',
  },
  {
    title: '新路由内容',
    dataIndex: 'newRoute',
  },
  {
    title: '原来路由内容',
    dataIndex: 'oldRoute',
  },
  {
    title: '操作人',
    dataIndex: 'operationUser',
    width: '200px',
  },
  {
    title: '操作时间',
    dataIndex: 'createdAt',
    width: '120px',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      serverAddEntryVisible: false,
      assetIdcList: [],
      assetOrgList: [],
      assetDepList: [],
      assetCostUserList: [],
      recordTypeList: [{ label: '创建', value: 'create' }, { label: '更新', value: 'update' }, { label: '删除', value: 'delete' }],
      // create model
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getRouteRecord(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      eipIdcList: [],
      detailData: {
      },
      detailIdVisible: false,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    this.getEipIdcList()
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(this.localUser.split('@')[0])
  },
  methods: {
    getEipIdcList() {
      getEipIdc().then(res => {
        for (let i = 0, len = res.Data.idc.length; i < len; i++) {
          let idc = {}
          idc.value = res.Data.idc[i]
          idc.label = res.Data.idc[i]
          this.eipIdcList.push(idc)
        }
      })
    },
    unUsedSearch() {
      this.queryParam.action = 'unUsed'
      this.$refs.table.refresh()
      this.queryParam.action = ''
    },
    sizeFilter(type) {
      return type.toString() + 'G'
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    handleAddEntry() {
      this.serverAddEntryVisible = true
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    filterOption: filterLabelValue,
    // 详细信息 相关接口
    handleDetail(record) {
      getEip(record.id).then(response => {
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
  },
}
</script>
