<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="4" :sm="24" style="padding-left: 10px">
              <a-form-item label="IP">
                <a-input
                  @keyup.enter="$refs.table.refresh(true)"
                  v-model:value="queryParam.ip"
                  placeholder="模糊查询"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" style="padding-left: 10px">
              <a-form-item label="封禁原因">
                <a-input @keyup.enter="$refs.table.refresh(true)" v-model:value="queryParam.reason" placeholder="" />
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24" style="padding-left: 10px">
              <a-form-item label="申请人">
                <a-input @keyup.enter="$refs.table.refresh(true)" v-model:value="queryParam.user" placeholder="" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 6) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" icon="search" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button type="primary" style="margin-left: 10px" @click="handleAdd">新增</tx-button>
                <tx-button type="primary" style="margin-left: 10px" @click="handleDel()">清空</tx-button>
                <tx-button type="primary" style="margin-left: 10px" @click="downloadIps">导出</tx-button>
                <tx-button type="primary" style="margin-left: 10px" @click="handSync">同步</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table ref="table" size="default" rowKey="key" :pagination="pagination" :columns="columns" :data="loadData">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleInfo(record)">详情</a>
            <a-divider type="vertical" />
            <!-- <a @confirm="handleDel(record)">删除</a> -->
            <a-popconfirm title="确定删除该组IP?" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>
      <a-modal v-model:visible="visible" title="封禁IP详情" :footer="null">
        <NocEditorJson
          class="GlobalRuleEdit"
          style="height: 380px"
          v-model="blackIpList"
          :mode="'code'"
          lang="zh"
        ></NocEditorJson>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { Ellipsis, STable } from '@/components'
import { getBlackIps, listGlobalRule, syncGlobalRuleBlackIp } from '@/api/gateway/globalRule'
import { notification } from 'ant-design-vue'
const columns = [
  {
    title: '封禁原因',
    dataIndex: 'reason',
    width: '250px',
    ellipsis: true,
  },
  {
    title: 'IP列表',
    dataIndex: 'blacklist',
    ellipsis: true,
  },
  {
    title: '申请人',
    dataIndex: 'user',
    width: '190px',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    align: 'center',
    scopedSlots: { customRender: 'action' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

const statusMap = {
  1: {
    status: 'success',
    text: '启用',
  },
  2: {
    status: 'error',
    text: '停用',
  },
}

export default {
  name: 'DomainList',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      searchClusterList: [],
      snisList: [],
      blackIpList: [],
      addQueryParam: {},
      infoParam: {},
      visible: false,
      verificationCode: '',
      addVisible: false,
      infoVisible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      Rules: {
        ip: [{ required: true, message: '请集群实例ip', trigger: 'change' }],
        apisixKey: [{ required: true, message: '请输入集群秘钥', trigger: 'change' }],
        clusterName: [{ required: true, message: '请输入集群名称', trigger: 'change' }],
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listGlobalRule(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  mounted() {},
  methods: {
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    handSync() {
      syncGlobalRuleBlackIp().then(res => {
        notification.success({
          message: '同步成功',
          description: '同步成功',
        })
      })
    },
    handleInfo(record) {
      this.visible = true
      this.blackIpList = JSON.parse(record.blacklist)
    },
    downloadIps() {
      getBlackIps().then(res => {
        const data = res.Data.ips
        const blob = new Blob([data], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'ips.txt'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      })
    },
    handleAdd() {
      let routeUrl = this.$router.resolve({ path: '/workflow/ip-ban' })
      window.open(routeUrl.href, '_blank')
    },
    handleDel(record) {
      if (record == undefined) {
        let routeUrl = this.$router.resolve({ path: '/workflow/ip-ban', query: { deleteall: 1 } })
        window.open(routeUrl.href, '_blank')
      } else {
        let routeUrl = this.$router.resolve({ path: '/workflow/ip-ban', query: { delete: record.id } })
        window.open(routeUrl.href, '_blank')
      }
    },
  },
}
</script>

<style lang="less">
.GlobalRuleEdit {
  height: 380px;
  .jsoneditor-vue {
    height: 100% !important;
  }
}
</style>
