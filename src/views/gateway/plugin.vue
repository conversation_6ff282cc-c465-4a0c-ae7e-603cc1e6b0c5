<template>
  <div class="plugin-container">
    <!-- 分类选择 -->
    <a-tabs v-model="activeCategory" @change="handleCategoryChange">
      <a-tab-pane
        v-for="category in pluginCategories"
        :key="category.label"
        :tab="category.label"
      >
        <a-select
          v-model:value="selectedPlugins"
          placeholder="选择插件（可多选）"
          mode="multiple"
          style="width: 100%"
          @change="handlePluginsSelect"
        >
          <a-select-option
            v-for="plugin in category.options"
            :key="plugin.value"
            :value="plugin.value"
          >
            {{ plugin.label }}
          </a-select-option>
        </a-select>
      </a-tab-pane>
    </a-tabs>
  <!-- </div> -->
    <!-- 多插件配置表单 -->
    <div v-for="plugin in activePlugins" :key="plugin.value" class="plugin-form">
      <a-divider orientation="left">{{ plugin.label }} 配置</a-divider>
      <a-form :form="forms[plugin.value]">
        <a-form-item
          v-for="(field, index) in plugin.configFields"
          :key="index"
          :label="field.label"
          :required="field.required"
          :rules="field.rules"
        >
          <a-input
            v-if="field.type === 'string'"
            v-model:value="pluginConfigs[plugin.value][field.model]"
            :placeholder="field.placeholder || ''"
          />
          <div
            v-if="field.type === 'string' && field.description"
            class="field-description"
          >
            <a-icon type="info-circle" />
            {{ field.description }}
          </div>

          <a-switch
            v-else-if="field.type === 'bool'"
            v-decorator="[field.model]"
            :checked="pluginConfigs[plugin.value][field.model]"
            @change="val => pluginConfigs[plugin.value][field.model] = val"
          />
          <div
            v-if="field.type === 'bool' && field.description"
            class="field-description"
          >
            <a-icon type="info-circle" />
            {{ field.description }}
          </div>

          <a-input-number
            v-else-if="field.type === 'integer'"
            v-model:value="pluginConfigs[plugin.value][field.model]"
            :min="0"
            :placeholder="field.placeholder || ''"
          />
          <div
            v-if="field.type === 'integer' && field.description"
            class="field-description"
          >
            <a-icon type="info-circle" />
            {{ field.description }}
          </div>

          <template v-else-if="field.type === 'array'">
            <DynamicArrayForm
              v-model:value="pluginConfigs[plugin.value][field.model]"
              :field="field"
            />
            <div
              v-if="field.description"
              class="field-description"
            >
              <a-icon type="info-circle" />
              {{ field.description }}
            </div>
          </template>

          <template v-else-if="field.type === 'objectArray'">
            <DynamicObjectArrayForm
              v-model:value="pluginConfigs[plugin.value][field.model]"
              :schema="field.schema"
            />
            <div
              v-if="field.description"
              class="field-description"
            >
              <a-icon type="info-circle" />
              {{ field.description }}
            </div>
          </template>
        </a-form-item>
      </a-form>
    </div>
    <div class="save-button-container">
    <a-button type="primary" @click="savePluginConfigs">保存所有配置</a-button>
    </div>
    </div>
</template>

<script>
import DynamicArrayForm from '@/components/Form/DynamicArrayForm.vue';
import DynamicObjectArrayForm from '@/components/Form/DynamicObjectArrayForm.vue';

export default {
  components: {
    DynamicArrayForm,
    DynamicObjectArrayForm
  },
  data() {
    return {
      pluginCategories: [
        {
          label: '身份认证',
          options: [
            {
              value: 'key-auth',
              label: 'Key认证',
              configFields: [
                {
                  label: 'Secret',
                  model: 'key',
                  type: 'string',
                  required: true,
                  description: '用于生成访问密钥的密钥字符串，建议使用高强度随机字符串'
                },
                {
                  label: 'Header名称',
                  model: 'header-field',
                  type: 'string',
                  description: '客户端需要在请求头中携带认证信息的字段名称'
                }
              ]
            },
            {
              value: 'jwt-auth',
              label: 'JWT认证',
              configFields: [
                { label: '公钥', model: 'public_key', type: 'string', required: true },
                { label: 'Header名称', model: 'jwt-header', type: 'string' },
                { label: '算法', model: 'algorithm', type: 'string', placeholder: 'HS256' }
              ]
            },
            {
              value: 'basic-auth',
              label: 'basic-auth',
              configFields: [
                { label: '用户名', model: 'username', type: 'string', required: true },
                { label: '密码', model: 'password', type: 'string', required: true }
              ]
            }
          ]
        },
        {
          label: '安全防护',
          options: [
            {
              value: 'cors',
              label: 'cors',
              configFields: [
                { label: 'allow_origins', model: 'allow_origins', type: 'string' ,description:'允许跨域访问的 Origin，格式为 scheme://host:port，示例如 https://somedomain.com:8081。如果你有多个 Origin，请使用 , 分隔。当 allow_credential 为 false 时，可以使用 * 来表示允许所有 Origin 通过。你也可以在启用了 allow_credential 后使用 ** 强制允许所有 Origin 均通过，但请注意这样存在安全隐患。'},
                { label: 'allow_methods', model: 'allow_methods', type: 'string',description:'允许跨域访问的 Method，比如：GET，POST 等。如果你有多个 Method，请使用 , 分割。当 allow_credential 为 false 时，可以使用 * 来表示允许所有 Method 通过。你也可以在启用了 allow_credential 后使用 ** 强制允许所有 Method 都通过，但请注意这样存在安全隐患。' },
                { label: 'allow_headers', model: 'allow_headers', type: 'string',description:'允许跨域访问时请求方携带哪些非 CORS 规范 以外的 Header。如果你有多个 Header，请使用 , 分割。当 allow_credential 为 false 时，可以使用 * 来表示允许所有 Header 通过。你也可以在启用了 allow_credential 后使用 ** 强制允许所有 Header 都通过，但请注意这样存在安全隐患。' },
                { label: 'expose_headers', model: 'expose_headers', type: 'string',description:'允许跨域访问时响应方携带哪些非 CORS 规范 以外的 Header。如果你有多个 Header，请使用 , 分割。当 allow_credential 为 false 时，可以使用 * 来表示允许任意 Header。如果不设置，插件不会修改 Access-Control-Expose-Headers 头，详情请参考 Access-Control-Expose-Headers - MDN。' },
                { label: 'max_age', model: 'max_age', type: 'integer' ,description:'浏览器缓存 CORS 结果的最大时间，单位为秒。在这个时间范围内，浏览器会复用上一次的检查结果，-1 表示不缓存。请注意各个浏览器允许的最大时间不同，详情请参考 Access-Control-Max-Age - MDN。'},
                { label: 'allow_credential', model: 'allow_credential', type: 'bool' ,description:'是否允许跨域访问的请求方携带凭据（如 Cookie 等）。根据 CORS 规范，如果设置该选项为 true，那么将不能在其他属性中使用 *。'},
                { label: 'allow_origins_by_regex', model: 'allow_origins_by_regex', type: 'array',description:'	使用正则表达式数组来匹配允许跨域访问的 Origin，如 [".*\.test.com$"] 可以匹配任何 test.com 的子域名。如果 allow_origins_by_regex 属性已经指定，则会忽略 allow_origins 属性。' },
                { label: 'allow_origins_by_metadata', model: 'allow_origins_by_metadata', type: 'array' ,description:'	通过引用插件元数据的 allow_origins 配置允许跨域访问的 Origin。比如当插件元数据为 "allow_origins": {"EXAMPLE": "https://example.com"} 时，配置 ["EXAMPLE"] 将允许 Origin https://example.com 的访问。'},
              ]
            },
            {
              value: 'ip-restriction',
              label: 'ip-restriction',
              configFields: [
                { label: 'whitelist', model: 'whitelist', type: 'array',description:'	加入白名单的 IP 地址或 CIDR 范围。' },
                { label: 'blacklist', model: 'blacklist', type: 'array',description:'加入黑名单的 IP 地址或 CIDR 范围。' },
                { label: 'message', model: 'message', type: 'string',description:'在未允许的 IP 访问的情况下返回的信息。' }
              ]
            }
          ]
        },
        {
          label: '流量控制',
          options: [
            {
              value: 'limit-req',
              label: 'limit-req',
              configFields: [
                { label: 'rate', model: 'rate', type: 'integer', required: true ,description:'指定的请求速率（以秒为单位），请求速率超过 rate 但没有超过（rate + burst）的请求会被延时处理。'},
                { label: 'burst', model: 'burst', type: 'integer', required: true ,description:'请求速率超过（rate + burst）的请求会被直接拒绝。'},
                { label: 'key_type', model: 'key_type', type: 'string' ,description:'要使用的用户指定 key 的类型。'},
                { label: 'key', model: 'key', type: 'string',description:'用来做请求计数的依据，当前接受的 key 有：remote_addr（客户端 IP 地址），server_addr（服务端 IP 地址）, 请求头中的 X-Forwarded-For 或 X-Real-IP，consumer_name（Consumer 的 username）。' },
                { label: 'rejected_code', model: 'rejected_code', type: 'integer' ,description:'当超过阈值的请求被拒绝时，返回的 HTTP 状态码。'},
                { label: 'rejected_msg', model: 'rejected_msg', type: 'string',description:'当超过阈值的请求被拒绝时，返回的响应体。' },
                { label: 'nodelay', model: 'nodelay', type: 'bool' ,description:'当设置为 true 时，请求速率超过 rate 但没有超过（rate + burst）的请求不会加上延迟；当设置为 false，则会加上延迟。'},
                { label: 'allow_degradation', model: 'allow_degradation', type: 'bool',description:'当设置为 true 时，如果限速插件功能临时不可用，将会自动允许请求继续。' },
                { label: 'policy', model: 'policy', type: 'string' ,description:'用于检索和增加限制计数的策略。当设置为 local 时，计数器被以内存方式保存在节点本地；当设置为 redis 时，计数器保存在 Redis 服务节点上，从而可以跨节点共享结果，通常用它来完成全局限速；当设置为 redis-cluster 时，使用 Redis 集群而不是单个实例。'},
                { label: 'redis_host', model: 'redis_host', type: 'bool' ,description:'当使用 redis 限速策略时，Redis 服务节点的地址。当 policy 属性设置为 redis 时必选。'},
                { label: 'redis_port', model: 'redis_port', type: 'integer' ,description:'当使用 redis 限速策略时，Redis 服务节点的端口。'},
                { label: 'redis_username', model: 'redis_username', type: 'string' ,description:'若使用 Redis ACL 进行身份验证（适用于 Redis 版本 >=6.0），则需要提供 Redis 用户名。若使用 Redis legacy 方式 requirepass 进行身份验证，则只需将密码配置在 redis_password。当 policy 设置为 redis 时使用。'},
                { label: 'redis_password', model: 'redis_password', type: 'string',description:'当使用 redis 或者 redis-cluster 限速策略时，Redis 服务节点的密码。' },
                { label: 'redis_ssl', model: 'redis_ssl', type: 'bool',description:'当使用 redis 限速策略时，如果设置为 true，则使用 SSL 连接到 redis' },
                { label: 'redis_ssl_verify', model: 'redis_ssl_verify', type: 'bool' ,description:'当使用 redis 限速策略时，如果设置为 true，则验证服务器 SSL 证书的有效性，具体请参考 tcpsock:sslhandshake.'},
                { label: 'redis_database', model: 'redis_database', type: 'integer',description:'	当使用 redis 限速策略时，Redis 服务节点中使用的 database，并且只针对非 Redis 集群模式（单实例模式或者提供单入口的 Redis 公有云服务）生效。' },
                { label: 'redis_timeout', model: 'redis_timeout', type: 'integer' ,description:'当 policy 设置为 redis 或 redis-cluster 时，Redis 服务节点的超时时间（以毫秒为单位）。'},
                { label: 'redis_cluster_nodes', model: 'redis_cluster_nodes', type: 'array' ,description:'当使用 redis-cluster 限速策略时，Redis 集群服务节点的地址列表（至少需要两个地址）。当 policy 属性设置为 redis-cluster 时必选。'},
                { label: 'redis_cluster_name', model: 'redis_cluster_name', type: 'string',description:'当使用 redis-cluster 限速策略时，Redis 集群服务节点的名称。当 policy 设置为 redis-cluster 时必选。' },
                { label: 'redis_cluster_ssl', model: 'redis_cluster_ssl', type: 'bool',description:'	当使用 redis-cluster 限速策略时，如果设置为 true，则使用 SSL 连接到 redis-cluster' },
                { label: 'redis_cluster_ssl_verify', model: 'redis_cluster_ssl_verify', type: 'bool',description:'当使用 redis-cluster 限速策略时，如果设置为 true，则验证服务器 SSL 证书的有效性' },
              ]
            },
            {
              value: 'limit-conn',
              label: 'limit-conn',
              configFields: [
              { label: 'conn', model: 'conn', type: 'integer', required: true ,description:'允许的最大并发请求数。超过配置的限制且低于conn + burst的请求将被延迟。'},
                { label: 'burst', model: 'burst', type: 'integer', required: true ,description:'每秒允许延迟的过多并发请求数。超过限制的请求将被立即拒绝。'},
                { label: 'default_conn_delay', model: 'default_conn_delay', type: 'integer', required: true ,description:'允许超过conn + burst的并发请求的处理延迟（秒），可根据only_use_default_delay设置动态调整。'},
                { label: 'only_use_default_delay', model: 'only_use_default_delay', type: 'bool', required: true ,description:'如果为 false，则根据请求超出conn限制的程度按比例延迟请求。拥塞越严重，延迟就越大。例如，当 conn 为 5、burst 为 3 且 default_conn_delay 为 1 时，6 个并发请求将导致 1 秒的延迟，7 个请求将导致 2 秒的延迟，8 个请求将导致 3 秒的延迟，依此类推，直到达到 conn + burst 的总限制，超过此限制的请求将被拒绝。如果为 true，则使用 default_conn_delay 延迟 burst 范围内的所有超额请求。超出 conn + burst 的请求将被立即拒绝。例如，当 conn 为 5、burst 为 3 且 default_conn_delay 为 1 时，6、7 或 8 个并发请求都将延迟 1 秒。'},
                { label: 'key_type', model: 'key_type', type: 'string',description:'key 的类型。如果key_type 为 var，则 key 将被解释为变量。如果 key_type 为 var_combination，则 key 将被解释为变量的组合。' },
                { label: 'key', model: 'key', type: 'string',description:'用于计数请求的 key。如果 key_type 为 var，则 key 将被解释为变量。变量不需要以美元符号（$）为前缀。如果 key_type 为 var_combination，则 key 会被解释为变量的组合。所有变量都应该以美元符号 ($) 为前缀。例如，要配置 key 使用两个请求头 custom-a 和 custom-b 的组合，则 key 应该配置为 $http_custom_a $http_custom_b。' },
                { label: 'rejected_code', model: 'rejected_code', type: 'integer' ,description:'请求因超出阈值而被拒绝时返回的 HTTP 状态代码。'},
                { label: 'rejected_msg', model: 'rejected_msg', type: 'string',description:'请求因超出阈值而被拒绝时返回的响应主体。' },
                { label: 'allow_degradation', model: 'allow_degradation', type: 'bool',description:'如果为 true，则允许 APISIX 在插件或其依赖项不可用时继续处理没有插件的请求' },
                { label: 'policy', model: 'policy', type: 'string',description:'速率限制计数器的策略。如果是 local，则计数器存储在本地内存中。如果是 redis，则计数器存储在 Redis 实例上。如果是 redis-cluster，则计数器存储在 Redis 集群中。' },
                { label: 'redis_host', model: 'redis_host', type: 'bool',description:'Redis 节点的地址。当 policy 为 redis 时必填。' },
                { label: 'redis_port', model: 'redis_port', type: 'integer',description:'当 policy 为 redis 时，Redis 节点的端口。' },
                { label: 'redis_username', model: 'redis_username', type: 'string',description:'如果使用 Redis ACL，则为 Redis 的用户名。如果使用旧式身份验证方法 requirepass，则仅配置 redis_password。当 policy 为 redis 时使用。' },
                { label: 'redis_password', model: 'redis_password', type: 'string',description: '当 policy 为 redis 或 redis-cluster 时，Redis 节点的密码。'},
                { label: 'redis_ssl', model: 'redis_ssl', type: 'bool' },
                { label: 'redis_ssl_verify', model: 'redis_ssl_verify', type: 'bool',description:'如果为 true，则在 policy 为 redis 时验证服务器 SSL 证书。' },
                { label: 'redis_database', model: 'redis_database', type: 'integer',description:'当 policy 为 redis 时，Redis 中的数据库编号。' },
                { label: 'redis_timeout', model: 'redis_timeout', type: 'integer' ,description:'当 policy 为 redis 或 redis-cluster 时，Redis 超时值（以毫秒为单位）'},
                { label: 'redis_cluster_nodes', model: 'redis_cluster_nodes', type: 'array',description:'具有至少两个地址的 Redis 群集节点列表。当 policy 为 redis-cluster 时必填。' },
                { label: 'redis_cluster_name', model: 'redis_cluster_name', type: 'string' },
                { label: 'redis_cluster_ssl', model: 'redis_cluster_ssl', type: 'bool' ,description:'如果为 true，当 policy 为 redis-cluster时，使用 SSL 连接 Redis 集群。'},
                { label: 'redis_cluster_ssl_verify', model: 'redis_cluster_ssl_verify', type: 'bool',description:'如果为 true，当 policy 为 redis-cluster 时，验证服务器 SSL 证书。' },
              ]
            },            
            {
              value: 'limit-count',
              label: 'limit-count',
              configFields: [
              { label: 'conn', model: 'conn', type: 'integer', required: true ,description:'给定时间间隔内允许的最大请求数。'},
                { label: 'time_window', model: 'time_window', type: 'integer', required: true ,description:'	速率限制 count 对应的时间间隔（以秒为单位）'},
                { label: 'key_type', model: 'key_type', type: 'string' ,description:'	key 的类型。如果key_type 为 var，则 key 将被解释为变量。如果 key_type 为 var_combination，则 key 将被解释为变量的组合。如果 key_type 为 constant，则 key 将被解释为常量。'},
                { label: 'key', model: 'key', type: 'string' ,description:'	用于计数请求的 key。如果 key_type 为 var，则 key 将被解释为变量。变量不需要以美元符号（$）为前缀。如果 key_type 为 var_combination，则 key 会被解释为变量的组合。所有变量都应该以美元符号 ($) 为前缀。例如，要配置 key 使用两个请求头 custom-a 和 custom-b 的组合，则 key 应该配置为 $http_custom_a $http_custom_b。如果 key_type 为 constant，则 key 会被解释为常量值。'},
                { label: 'rejected_code', model: 'rejected_code', type: 'integer' ,description:'请求因超出阈值而被拒绝时返回的 HTTP 状态代码。'},
                { label: 'rejected_msg', model: 'rejected_msg', type: 'string',description:'请求因超出阈值而被拒绝时返回的响应主体。' },
                { label: 'allow_degradation', model: 'allow_degradation', type: 'bool',description:'如果为 true，则允许 APISIX 在插件或其依赖项不可用时继续处理没有插件的请求。' },
                { label: 'policy', model: 'policy', type: 'string',description:'速率限制计数器的策略。如果是 local，则计数器存储在本地内存中。如果是 redis，则计数器存储在 Redis 实例上。如果是 redis-cluster，则计数器存储在 Redis 集群中' },
                { label: 'show_limit_quota_header', model: 'show_limit_quota_header', type: 'bool',description:'如果为 true，则在响应标头中包含 X-RateLimit-Limit 以显示总配额和 X-RateLimit-Remaining 以显示剩余配额。' },
                { label: 'group', model: 'group', type: 'string',description:'插件的 group ID，以便同一 group 的路由可以共享相同的速率限制计数器' },
                { label: 'redis_host', model: 'redis_host', type: 'bool' ,description:'Redis 节点的地址。当 policy 为 redis 时必填。'},
                { label: 'redis_port', model: 'redis_port', type: 'integer',description:'	当 policy 为 redis 时，Redis 节点的端口。' },
                { label: 'redis_username', model: 'redis_username', type: 'string' ,description:'如果使用 Redis ACL，则为 Redis 的用户名。如果使用旧式身份验证方法 requirepass，则仅配置 redis_password。当 policy 为 redis 时使用。'},
                { label: 'redis_password', model: 'redis_password', type: 'string' ,description:'当 policy 为 redis 或 redis-cluster 时，Redis 节点的密码。'},
                { label: 'redis_ssl', model: 'redis_ssl', type: 'bool' ,description:'如果为 true，当 policy 为 redis-cluster时，使用 SSL 连接 Redis 集群。'},
                { label: 'redis_ssl_verify', model: 'redis_ssl_verify', type: 'bool' ,description:'如果为 true，则在 policy 为 redis 时验证服务器 SSL 证书。'},
                { label: 'redis_database', model: 'redis_database', type: 'integer',description:'当 policy 为 redis 时，Redis 中的数据库编号。' },
                { label: 'redis_timeout', model: 'redis_timeout', type: 'integer' ,description:'当 policy 为 redis 或 redis-cluster 时，Redis 超时值（以毫秒为单位）。'},
                { label: 'redis_cluster_nodes', model: 'redis_cluster_nodes', type: 'array',description:'具有至少两个地址的 Redis 群集节点列表。当 policy 为 redis-cluster 时必填' },
                { label: 'redis_cluster_name', model: 'redis_cluster_name', type: 'string' },
                { label: 'redis_cluster_ssl', model: 'redis_cluster_ssl', type: 'bool' ,description:'如果为 true，当 policy 为 redis-cluster时，使用 SSL 连接 Redis 集群。'},
                { label: 'redis_cluster_ssl_verify', model: 'redis_cluster_ssl_verify', type: 'bool' ,description:'如果为 true，当 policy 为 redis-cluster 时，验证服务器 SSL 证书。'},
              ]
            }
          ]
        },
        {
          label: '其他',
          options: [
            {
              value: 'response-rewrite',
              label: 'response-rewrite',
              configFields: [
                { label: 'status_code', model: 'status_code', type: 'integer' ,description:'修改上游返回状态码，默认保留原始响应代码。'},
                { label: 'body', model: 'body', type: 'string',description:'修改上游返回的 body 内容，如果设置了新内容，header 里面的 content-length 字段也会被去掉。' },
                { label: 'body_base64', model: 'body_base64', type: 'bool',description:'当设置时，在写给客户端之前，在body中传递的主体将被解码，这在一些图像和 Protobuffer 场景中使用。注意，这个字段只允许对插件配置中传递的主体进行解码，并不对上游响应进行解码。' },
                { label: 'headers', model: 'headers', type: 'string',description:`例如： 
                "set": {
                    "X-Server-id": 3,
                    "X-Server-status": "on",
                    "X-Server-balancer-addr": "$balancer_ip:$balancer_port"
                }
            ,`},
                { label: 'vars', model: 'vars', type: 'array' ,description:'vars 是一个表达式列表，只有满足条件的请求和响应才会修改 body 和 header 信息，来自 lua-resty-expr。如果 vars 字段为空，那么所有的重写动作都会被无条件的执行。'},
                {
                  label: 'filters',
                  model: 'filters',
                  type: 'objectArray',
                  schema: [
                    { model: 'regex', label: 'regex', type: 'string', required: true },
                    { model: 'scope', label: 'scope', type: 'select',
                      options: [
                        { value: 'once', label: 'once' },
                        { value: 'global', label: 'global' },
                      ]
                    },
                    { model: 'replace', label: 'replace', type: 'string', required: true },
                    { model: 'options', label: 'options', type: 'string' }
                  ],
                  errorMessage: 'regex和replace必须填写',
                  description:'一组过滤器，采用指定字符串表达式修改响应体 [filters.regex]用于匹配响应体正则表达式。[filters.scope]替换范围，"once" 表达式 filters.regex 仅替换首次匹配上响应体的内容，"global" 则进行全局替换。[filters.replace]替换后的内容 [filters.options]正则匹配有效参数，可选项见 ngx.re.match。'
                }
              ]
            },
            {
              value: 'proxy-rewrite',
              label: 'proxy-rewrite',
              configFields: [
                { label: 'uri', model: 'uri', type: 'string',description: '	转发到上游的新 uri 地址。支持 NGINX variables 变量，例如：$arg_name。' },
                { label: 'method', model: 'method', type: 'string',description:'将路由的请求方法代理为该请求方法。' },
                { label: 'regex_uri', model: 'regex_uri', type: 'array',description:'使用正则表达式匹配来自客户端的 uri，如果匹配成功，则使用模板替换转发到上游的 uri，如果没有匹配成功，则将客户端请求的 uri 转发至上游。当同时配置 uri 和 regex_uri 属性时，优先使用 uri。当前支持多组正则表达式进行模式匹配，插件将逐一尝试匹配直至成功或全部失败。例如：["^/iresty/(.*)/(.*)/(.*)", "/$1-$2-$3", ^/theothers/(.*)/(.*)", "/theothers/$1-$2"]，奇数索引的元素代表匹配来自客户端请求的 uri 正则表达式，偶数索引的元素代表匹配成功后转发到上游的 uri 模板。请注意该值的长度必须为偶数值。' },
                { label: 'host', model: 'host', type: 'string',description:'转发到上游的新 host 地址，例如：iresty.com。' },
                { label: 'headers', model: 'headers', type: 'string' },
                { label: 'use_real_request_uri_unsafe', model: 'use_real_request_uri_unsafe', type: 'bool',description:'使用 real_request_uri（nginx 中的原始 $request_uri）绕过 URI 规范化。启用它被认为是不安全的，因为它会绕过所有 URI 规范化步骤。' },
              ]
            },
            {
              value: 'redirect',
              label: 'redirect',
              configFields: [
                { label: 'http_to_https', model: 'http_to_https', type: 'bool',description: '当设置为 true 并且请求是 HTTP 时，它将被重定向具有相同 URI 和 301 状态码的 HTTPS，原 URI 的查询字符串也将包含在 Location 头中。' },
                { label: 'uri', model: 'uri', type: 'string' ,description: '要重定向到的 URI，可以包含 NGINX 变量。例如：/test/index.htm，$uri/index.html，${uri}/index.html，https://example.com/foo/bar。如果你引入了一个不存在的变量，它不会报错，而是将其视为一个空变量。' },
                { label: 'regex_uri', model: 'regex_uri', type: 'string' ,description:'将来自客户端的 URL 与正则表达式匹配并重定向。当匹配成功后使用模板替换发送重定向到客户端，如果未匹配成功会将客户端请求的 URI 转发至上游。和 regex_uri 不可以同时存在。例如：["^/iresty/(.)/(.)/(.*)","/$1-$2-$3"] 第一个元素代表匹配来自客户端请求的 URI 正则表达式，第二个元素代表匹配成功后发送重定向到客户端的 URI 模板。'},
                { label: 'ret_code', model: 'ret_code', type: 'integer',description:'HTTP 响应码' },
                { label: 'encode_uri', model: 'encode_uri', type: 'bool' ,description:'当设置为 true 时，对返回的 Location Header 按照 RFC3986 的编码格式进行编码。'},
                { label: 'append_query_string', model: 'append_query_string', type: 'bool' ,description:'当设置为 true 时，将原始请求中的查询字符串添加到 Location Header。如果已配置 uri 或 regex_uri 已经包含查询字符串，则请求中的查询字符串将附加一个&。如果你已经处理过查询字符串（例如，使用 NGINX 变量 $request_uri），请不要再使用该参数以避免重复。'}
              ]
            },
            {
              value: 'real-ip',
              label: 'real-ip',
              configFields: [
                { label: 'source', model: 'source', type: 'string',description: '内置变量，例如 http_x_forwarded_for 或 arg_realip。变量值应为一个有效的 IP 地址，表示客户端的真实 IP 地址，可选地包含端口。' },
                { label: 'trusted_addresses', model: 'trusted_addresses', type: 'array',description: '已知会发送正确替代地址的可信地址。此配置设置 set_real_ip_from 指令。' },
                { label: 'recursive', model: 'recursive', type: 'bool' ,description: '	如果为 false，则将匹配可信地址之一的原始客户端地址替换为配置的 source 中发送的最后一个地址。如果为 true，则将匹配可信地址之一的原始客户端地址替换为配置的 source 中发送的最后一个非可信地址。' }
              ]
            }
          ]
        }
      ],
      activeCategory: '身份认证',
      selectedPlugins: [],
      pluginConfigs: {},
      forms: {},
      test:''
    };
  },
  computed: {
    activePlugins() {
      const category = this.pluginCategories.find(c => c.label === this.activeCategory);
      if (!category) return [];
      return category.options.filter(p => this.selectedPlugins.includes(p.value));
    }
  },
  methods: {
    handleCategoryChange(category) {
      this.activeCategory = category;
      this.selectedPlugin = null;
      console.log('configArray',this.pluginConfigs)
    },
    handlePluginsSelect(values) {
      console.log('pluginConfigs',this.forms)
      console.log('vvvvvvvvvvvvv',values)
      // 初始化新添加插件的配置
      values.forEach(value => {
        if (!this.pluginConfigs[value]) {
          const plugin = this.pluginCategories
            .flatMap(c => c.options)
            .find(p => p.value === value);
          
          this.$set(this.pluginConfigs, value, plugin.configFields.reduce((config, field) => {
            config[field.model] = field.type === 'array' ? [] :
              field.type === 'bool' ? false :
              field.type === 'objectArray' ? [] :
              (field.default ?? '');
            return config;
          }, {}));
          
          this.$set(this.forms, value, this.$form.createForm(this));
        }
      });
      
      // 移除已取消选择的插件配置
      Object.keys(this.pluginConfigs).forEach(key => {
        if (!values.includes(key)) {
          this.$delete(this.pluginConfigs, key);
          this.$delete(this.forms, key);
        }
      });
    },
    validateIPList(rule, value, callback) {
      if (!value || value.some(ip => !/^(\d{1,3}\.){3}\d{1,3}$/.test(ip))) {
        callback(new Error('请输入有效的IP地址'));
      } else {
        callback();
      }
    },
    async savePluginConfigs() {
      // 深度日志输出用于调试
      console.log('完整插件配置:', JSON.parse(JSON.stringify(this.pluginConfigs)));
      try {
        // 转换配置对象为数组格式
        const configArray = Object.keys(this.pluginConfigs).map(pluginName => ({
          name: pluginName,
          config: this.pluginConfigs[pluginName]
        }));
        await this.$axios.post('/api/save-plugins', configArray);
        this.$message.success('所有配置已保存');
      } catch (error) {
        this.$message.error('保存失败: ' + (error.response?.data?.message || '请检查网络'));
      }
    },
    // formValue(newValue, pluginValue, fieldModel) {
    //   this.$set(this.pluginConfigs[pluginValue], fieldModel, newValue);
    // },
    
    // /**
    //  * 处理数组类型字段更新
    //  * @param {string} fieldPath - 字段路径
    //  * @param {any} newValue - 新值
    //  */
    // handleArrayUpdate(fieldPath, newValue) {
    //   // 安全访问链检查
    //   if (!this.plugin?.value) {
    //     console.warn('Plugin instance is not available');
    //     return;
    //   }

    //   const pluginKey = this.plugin.value;
      
    //   // 初始化嵌套对象结构
    //   this.$set(this.pluginConfigs, pluginKey,
    //     this.pluginConfigs[pluginKey] || {});

    //   // 响应式更新字段值
    //   this.$set(this.pluginConfigs[pluginKey], fieldPath, newValue);
    //   console.log(this.pluginConfigs[pluginKey])
    // }
  }
}
</script>

<style scoped>
.plugin-container { margin: 20px; }
.plugin-form {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}
.save-button-container {
  margin-top: 20px;
  text-align: right;
}

.field-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.5;
  padding-left: 8px;
  /* border-left: 3px solid #eee; */
}
</style>