<template>
  <a-result
    status="403"
    title='无页面权限，请点击下方"权限申请"按钮'
    sub-title="1.请检查浏览器是否开启了代理，导致访问网络属于非公司办公网络，2.或没有该页面权限，3.或该页面不存在，4.在菜单管理中关闭隐藏了该模块菜单，4.如果需要申请页面权限，请点击下方'权限申请'按钮"
  >
    <template #extra>
      <tx-button type="primary" size="large" @click="toPermission">权限申请</tx-button>
      <tx-button type="primary" size="large" @click="toHome" style="margin-left: 16px">返回首页</tx-button>
    </template>
  </a-result>
</template>

<script>
export default {
  name: 'Exception404',
  methods: {
    toHome() {
      this.$router.push({ path: '/' })
    },
    toPermission() {
      this.$router.push({ path: '/workflow/user-route-approve' })
    },
  },
}
</script>
