<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-11-27 14:16:23
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-26 14:52:49
 * @FilePath: \cloud_web\src\views\account\center\page\Article.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <a-list size="large" rowKey="id" :loading="loading" itemLayout="vertical" :dataSource="data">
    <template #renderItem="item">
      <a-list-item :key="item.id">
        <template #actions>
          <icon-text type="star-o" :text="item.star" />
          <icon-text type="like-o" :text="item.like" />
          <icon-text type="message" :text="item.message" />
        </template>
        <a-list-item-meta>
          <template #title>
            <a href="https://vue.ant.design/">{{ item.title }}</a>
          </template>
          <template #description>
            <span>
              <a-tag>Ant Design</a-tag>
              <a-tag>设计语言</a-tag>
              <a-tag>蚂蚁金服</a-tag>
            </span>
          </template>
        </a-list-item-meta>
        <article-list-content
          :description="item.description"
          :owner="item.owner"
          :avatar="item.avatar"
          :href="item.href"
          :updateAt="item.updatedAt"
        />
      </a-list-item>
    </template>
    <template #footer v-if="data.length > 0">
      <div style="text-align: center; margin-top: 16px">
        <tx-button @click="loadMore" :loading="loadingMore">加载更多</tx-button>
      </div>
    </template>
  </a-list>
</template>

<script>
import { ArticleListContent } from '@/components'
import IconText from '@/views/server/search/components/IconText.vue'

export default {
  name: 'Article',
  components: {
    IconText,
    ArticleListContent,
  },
  data() {
    return {
      loading: true,
      loadingMore: false,
      data: [],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.$http.get('/list/article').then(res => {
        console.log('res', res)
        this.data = res.result
        this.loading = false
      })
    },
    loadMore() {
      this.loadingMore = true
      this.$http
        .get('/list/article')
        .then(res => {
          this.data = this.data.concat(res.result)
        })
        .finally(() => {
          this.loadingMore = false
        })
    },
  },
}
</script>

<style scoped></style>
