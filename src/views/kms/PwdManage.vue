<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-08-28 16:36:27
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-09-06 14:20:48
 * @FilePath: \cloud_web\src\views\kms\PwdManage.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <template v-slot:content>
      <tx-button icon="solution" size="small">
        <a
          href="https://doc.intsig.net/pages/viewpage.action?pageId=639402216"
          target="_blank"
          style="text-decoration: none"
        >
          参考文档
        </a>
      </tx-button>
      <!--      <tx-button icon="vertical-align-bottom-outlined" @click="downloadExt" size="small" style="margin-left: 8px">-->
      <!--        <a-tooltip>-->
      <!--          <template #title>下载密码管家-浏览器插件</template>-->
      <!--          <a-->
      <!--            href="https://doc.intsig.net/download/attachments/641925661/%E5%AF%86%E7%A0%81%E7%AE%A1%E5%AE%B6.zip?version=9&modificationDate=1697787591958&api=v2"-->
      <!--          >-->
      <!--            浏览器插件-->
      <!--          </a>-->
      <!--          &lt;!&ndash; <span @click="downloadFile">浏览器插件</span> &ndash;&gt;-->
      <!--        </a-tooltip>-->
      <!--      </tx-button>-->
    </template>
    <a-card
      style="width: 100%"
      :bordered="false"
      :tabList="tabListNoTitle"
      :activeTabKey="noTitleKey"
      @tabChange="key => handleTabChange(key, 'noTitleKey')"
    >
      <pwd-manager ref="pwdManager" v-if="noTitleKey === 'manager'"></pwd-manager>
      <random-pwd v-else-if="noTitleKey === 'random'"></random-pwd>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { notification } from 'ant-design-vue'
import PwdManager from './PwdManager.vue'
import RandomPwd from './RandomPwd.vue'

export default {
  components: {
    PwdManager,
    RandomPwd,
  },
  data() {
    return {
      tabListNoTitle: [
        {
          key: 'manager',
          tab: '密码管家',
        },
        {
          key: 'random',
          tab: '随机密码生成器',
        },
      ],
      noTitleKey: 'manager',
      timeoutLimit: 300000,
      // timeoutLimit: 1000 * 5,
      timeoutId: null,
      activityDetected: false,
    }
  },
  methods: {
    handleTabChange(key, type) {
      this[type] = key
    },
    resetTimer() {
      clearTimeout(this.timeoutId)
      this.activityDetected = false
      if (this.$refs.pwdManager.pwdPass) {
        this.timeoutId = setTimeout(() => {
          this.$refs.pwdManager.pwdPass = false
          this.$refs.pwdManager.visigPwd = ''
          this.activityDetected = true
          this.$notification.warning({
            message: '暂离通知',
            description: '您离开密码管家已超过5分钟，系统自动登出，请重新登录!',
            duration: 0,
          })
        }, this.timeoutLimit)
      }
    },
    handleActivity() {
      this.resetTimer()
    },
  },
  mounted() {
    window.addEventListener('mousemove', this.handleActivity)
    window.addEventListener('keydown', this.handleActivity)
    this.resetTimer() // Initialize the timer when the component mounts
  },
  unmounted() {
    window.removeEventListener('mousemove', this.handleActivity)
    window.removeEventListener('keydown', this.handleActivity)
    clearTimeout(this.timeoutId) // Clear the timer when the component is unmounted
  },
}
</script>

<style scoped></style>
