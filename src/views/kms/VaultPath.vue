<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="业务名称">
                <a-input v-model:value="queryParam.userName" placeholder="模糊查询"
                         @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="供应商">
                <a-select v-model:value="queryParam.supplier" placeholder="请选择" :options="supplierList"
                          allowClear></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="凭证路径">
                <a-input v-model:value="queryParam.credentialPath" placeholder="模糊查询"
                         @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record: row}">
          <template v-if="column.dataIndex === 'comment'">
            <div class="tooltip">
              <a-tooltip placement="left">
                <template #title>
                  <span>{{ row.comment }}</span>
                </template>
                {{ row.comment }}
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdateVaultToken(row)">更新令牌</tx-button>
          </template>
          <template v-else-if="column.dataIndex === 'requestEnv'">
            <a-tag :key="row.requestEnv"
                   :color="row.requestEnv === 'consul_vault_secret' ? 'geekblue' : row.requestEnv === 'consul_vault_secret_test' ? 'purple' : 'orange'">
              {{
                row.requestEnv === 'consul_vault_secret' ? '线上环境' : row.requestEnv === 'consul_vault_secret_test' ?
                  '测试环境' : row.requestEnv
              }}
            </a-tag>
          </template>
        </template>

        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="角色路径">{{ record.rolePath }}</a-descriptions-item>
            <a-descriptions-item label="ConsulKey">{{ record.consulKey }}</a-descriptions-item>
            <a-descriptions-item label="Consul Endpoint">{{ record.consulEndpoint }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
          </a-descriptions>
          <!--          <a-descriptions>-->
          <!--            <a-descriptions-item label="访问令牌">{{ record.token }}</a-descriptions-item>-->
          <!--          </a-descriptions>-->
          <div v-if="record.cloudRolePolicy !== null && record.cloudRolePolicy.length > 0">
            <a>授权策略</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsPolicesChildren"
                  :data-source="record.cloudRolePolicy"
                  :pagination="pagination"
                  :rowKey="record => record.PolicyName"
                  class="policytable"
                >
                  <template #bodyCell="{column, record: row}">
                    <template v-if="column.dataIndex === 'policyDetail'">
                      <a-textarea
                        v-model:value="row.policyDetail"
                        :autoSize="{ minRows: 2, maxRows: 5 }"
                        :bordered="false"
                      ></a-textarea>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </div>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import {STable, Ellipsis} from '@/components'
import {getVaultPathList, updateVaultPath} from '@/api/kms/vault_path'
import {setWaterMark} from '@/utils/watermark'
import {getUserList} from '@/api/permission/user'
import {loadXLSX} from '@/utils/vendorLoader'
import store from '@/store'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    sorter: true
  },
  {
    title: '业务名称',
    dataIndex: 'userName',
    sorter: true
  },
  {
    title: '凭证路径',
    dataIndex: 'credentialPath'
    // scopedSlots: { customRender: 'task_type' }
  },
  {
    title: '环境',
    dataIndex: 'requestEnv',
    scopedSlots: {customRender: 'requestEnv'}
  },
  {
    title: '过期时间',
    dataIndex: 'expiration',
    sorter: true
  },
  {
    title: '创建用户',
    dataIndex: 'email'
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: {customRender: 'action'}
  }
]

const columnsPolicesChildren = [
  {
    title: '策略ID',
    dataIndex: 'policyId'
  },
  {
    title: '策略名称',
    dataIndex: 'policyName'
  },
  {
    title: '策略类型',
    dataIndex: 'policyType'
  },
  {
    title: '策略描述',
    dataIndex: 'description'
  },
  {
    title: '策略详情',
    dataIndex: 'policyDetail',
    width: '60%',
    scopedSlots: {customRender: 'policyDetail'}
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  defaultPageSize: 10,
  hideOnSinglePage: true
}

export default {
  name: 'VaultPath',
  components: {
    STable,
    Ellipsis
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  mounted() {
    const email = store.getters.email
    this.userEmail = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  data() {
    this.columns = columns
    this.columnsPolicesChildren = columnsPolicesChildren
    this.pagination = pagination
    return {
      supplierList: [{label: '阿里云(合合)', value: '阿里云(合合)'}, {label: '阿里云(启信宝)', value: '阿里云(启信宝)'},
        {label: '腾讯云(intsig)', value: '腾讯云(intsig)'}, {label: '腾讯云(camscanner)', value: '腾讯云(camscanner)'},
        {label: '腾讯云(qixinbao)', value: '腾讯云(qixinbao)'}, {label: 'AWS(临冠)', value: 'AWS(临冠)'},
        {label: 'AWS(合合)', value: 'AWS(合合)'}, {label: 'AWS(海外)', value: 'AWS(海外)'},
        {label: 'AWS(海外AI)', value: 'AWS(海外AI)'}, {label: 'AWS(海外AI2)', value: 'AWS(海外AI2)'}],
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getVaultPathList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('vaultPaths') && res.Data.vaultPaths !== null) {
            const rsp = {
              data: res.Data.vaultPaths || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
            return rsp
          } else {
            return {'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0}
          }
        })
      }
    }
  },
  methods: {
    getUserRoles(userEmail) {
      getUserList({searchText: userEmail}).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('kms_admin') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    // 权限变更 相关接口
    handleUpdateVaultToken(record) {
      const that = this
      this.$confirm({
        title: '确认更新',
        content: `确认更新 ${record.userName} 的访问令牌吗？`,
        onOk() {
          updateVaultPath({id: record.id})
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                that.$message.success('更新成功')
                that.$refs.table.refresh()
              } else {
                that.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
              }
            })
            .catch(() => {
            })
        },
        onCancel() {
        }

      })
    }
  }
}
</script>
<style lang="less" scoped>
.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.policytable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
