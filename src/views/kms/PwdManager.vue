<template xmlns="http://www.w3.org/1999/html">
  <template v-if="!hasAccount">
    <a-row>
      <a-col :span="12">
        <img style="width: 80%" src="/vendor-cdn/@img//kms/keyManager.png" />
      </a-col>
      <a-col :span="12">
        <div class="center-absolute">
          <img style="width: 15%" src="/vendor-cdn/@img//kms/keyLogo.png" />
        </div>
        <div style="height: 20%; text-align: center; font-size: 16px; font-weight: bold">
          多设备同步密码文件，便捷安全的密码管理服务
        </div>
        <div style="text-align: center">
          <a-button @click="openPwdService" type="primary" style="background-color: rgba(50, 100, 237, 1)">
            <template #icon><plus-outlined /></template>
            开通密码管家服务
          </a-button>
          <a-button style="margin-left: 30px" @click="getRandomPwd">
            <template #icon><code-sandbox-outlined /></template>
            生成随机密码
          </a-button>
          <a-modal v-model:visible="pwdManageVisible" title="开通密码管家" @ok="confirmOpenPwdService">
            <a-form
              ref="formRef"
              name="custom-validation"
              :model="formState"
              :rules="rules"
              v-bind="layout"
              @validate="handleValidate"
            >
              <a-form-item has-feedback label="密码" name="pass">
                <a-input-password
                  v-model:value="formState.pass"
                  type="password"
                  autocomplete="off"
                  placeholder="请输入您的访问密码"
                />
              </a-form-item>
              <a-form-item has-feedback label="确认密码" name="checkPass">
                <a-input-password
                  v-model:value="formState.checkPass"
                  type="password"
                  autocomplete="off"
                  placeholder="请输入您的访问密码"
                />
              </a-form-item>
              <div style="color: grey">
                <info-circle-two-tone two-tone-color="orange" style="margin-left: 20px" />
                <span>&nbsp;&nbsp;提示</span>
                <p style="margin-left: 20px">
                  1.该密码不可找回，请妥善保管！
                  <br />
                  2.请设置易输入、不易遗忘的密码。建议数字加字母6至8位
                </p>
              </div>
            </a-form>
          </a-modal>
        </div>
      </a-col>
    </a-row>
  </template>
  <template v-if="hasAccount && !pwdPass">
    <a-row>
      <a-col :span="12">
        <img style="width: 90%" src="/vendor-cdn/@img//kms/keyManager.png" />
      </a-col>
      <a-col :span="12">
        <div style="position: relative; text-align: right">
          <a-button style="margin-left: 30px" @click="getRandomPwd" type="link">
            <template #icon><code-sandbox-outlined /></template>
            生成随机密码
          </a-button>
        </div>
        <div style="height: 20%">
          <img style="width: 60px; height: 60px" src="/vendor-cdn/@img//kms/keyLogo.png" />
        </div>
        <div style="height: 12%; text-align: left; font-size: 20px; font-weight: bold">欢迎使用密码管家</div>
        <div>
          <div>密码</div>
          <div style="margin-top: 8px">
            <a-input-password
              type="text"
              @pressEnter="enterPwd"
              v-model:value="visigPwd"
              placeholder="请输入您的访问密码"
            />
          </div>
          <div style="margin-top: 8px">
            <a-button @click="changePwd" type="link" style="color: grey; float: left">
              <template #icon>
                <EditFilled />
              </template>
              修改密码
            </a-button>
            <a-popconfirm placement="topRight" @confirm="forgetPwd">
              <template #title>
                <p style="font-weight: bold">是否确认重置密码？</p>
                <p>
                  出于安全考虑，我们将重置您的密码。
                  <span style="color: orangered">密码管家中存储的所有记录将被清除！</span>
                </p>
              </template>
              <a-button type="link" style="color: grey; float: right">
                <template #icon><QuestionCircleFilled /></template>
                忘记密码
              </a-button>
            </a-popconfirm>
            <a-modal v-model:visible="pwdChangeVisible" title="修改访问密码" @ok="confirmChangePwdService">
              <a-form
                ref="formRef"
                name="custom-validation"
                :model="formChangeState"
                :rules="rules"
                v-bind="layout"
                @validate="handleValidate"
              >
                <a-form-item label="原始密码" name="oldPwd">
                  <a-input-password v-model:value="formChangeState.oldPwd" placeholder="请输入您的原始密码" />
                </a-form-item>
                <a-form-item label="新密码" name="newPwd">
                  <a-input-password v-model:value="formChangeState.newPwd" placeholder="请输入您的新密码" />
                </a-form-item>
                <a-form-item label="确认密码" name="confirmNewPwd">
                  <a-input-password v-model:value="formChangeState.confirmNewPwd" placeholder="请输入您的新密码" />
                </a-form-item>
                <div style="color: grey">
                  <info-circle-two-tone two-tone-color="orange" style="margin-left: 20px" />
                  <span>&nbsp;&nbsp;提示</span>
                  <p style="margin-left: 20px">
                    1.请妥善保管您的新密码，密码不可找回！
                    <br />
                    2.请勿使用浏览器保存访问密码！
                  </p>
                </div>
              </a-form>
            </a-modal>
          </div>
          <div style="margin-top: 70px">
            <a-button @click="enterPwd" type="primary" block style="background-color: rgba(50, 100, 237, 1)">
              登录
            </a-button>
          </div>
        </div>
      </a-col>
    </a-row>
  </template>
  <a-card v-if="pwdPass">
    <div class="pwdContent">
      <div class="left">
        <pwdFolder
          @update="pwdUpdate"
          @chooseFolder="chooseFolder"
          :dbPwd="visigPwd"
          :floderList="folderList"
          :currentFolder="currentFolder"
        ></pwdFolder>
      </div>
      <div class="right">
        <pwdList
          :percent="persent"
          :strokeColor="strokeColor"
          :status="progressStatus"
          @search="listSearch"
          @update="pwdUpdate"
          :dbPwd="visigPwd"
          :pwdArray="currentFolder"
        ></pwdList>
      </div>
    </div>
  </a-card>
</template>

<script>
import { message } from 'ant-design-vue'
import { defineComponent, reactive, ref, watch } from 'vue'
import pwdFolder from './comp/pwdFolder.vue'
import pwdList from './comp/pwdList.vue'
import { userCHeck, pwdLogin, dbCreate, dbLists, userDelete, modifySecretKey } from '@/api/kms'
import { TOTP } from 'jsotp'

export default defineComponent({
  components: {
    pwdFolder,
    pwdList,
  },
  setup() {
    let formRef = ref()
    let formState = reactive({
      pass: '',
      checkPass: '',
    })
    let formChangeState = reactive({
      oldPwd: '',
      newPwd: '',
      confirmNewPwd: '',
    })
    let visigPwd = ref('')
    let hasAccount = ref(false)
    let pwdManageVisible = ref()
    let pwdChangeVisible = ref()
    let pwdPass = ref(false)
    let folderList = ref()
    let currentFolder = ref()

    let validatePass = async (_rule, value) => {
      if (value === '') {
        return Promise.reject('请设置密码')
      } else {
        if (formState.checkPass !== '') {
          formRef.value.validateFields('checkPass')
        }
        return Promise.resolve()
      }
    }
    let validatePass2 = async (_rule, value) => {
      if (value === '') {
        return Promise.reject('请设置密码')
      } else if (value !== formState.pass) {
        return Promise.reject('两次密码不匹配')
      } else {
        return Promise.resolve()
      }
    }
    let validatePass3 = async (_rule, value) => {
      if (value === '') {
        return Promise.reject('请确认密码密码')
      } else if (value !== formChangeState.newPwd) {
        return Promise.reject('两次密码不匹配')
      } else {
        return Promise.resolve()
      }
    }

    const rules = {
      pass: [
        {
          required: true,
          validator: validatePass,
          trigger: 'change',
        },
        {
          required: true,
          min: 6,
          trigger: 'blur',
        },
      ],
      checkPass: [
        {
          validator: validatePass2,
          trigger: 'change',
        },
      ],
      oldPwd: [
        {
          required: true,
          min: 6,
          trigger: 'blur',
        },
      ],
      newPwd: [
        {
          required: true,
          min: 6,
          trigger: 'blur',
        },
      ],
      confirmNewPwd: [
        {
          required: true,
          validator: validatePass3,
          trigger: 'blur',
        },
      ],
    }
    const layout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 16,
      },
    }
    const resetForm = () => {
      formRef.value.resetFields()
    }
    const handleValidate = (...args) => {
      console.log(args)
    }
    // 设置密码弹窗开关
    const openPwdService = () => {
      pwdManageVisible.value = true
    }
    // 修改密码
    const changePwd = () => {
      pwdChangeVisible.value = true
    }
    // 密码设置确认
    const confirmOpenPwdService = () => {
      formRef.value
        .validateFields()
        .then(res => {
          dbCreate({
            email: noc.user.getUserInfo().email,
            password: formState.checkPass,
          }).then(res => {
            hasAccount.value = true
            message.info('密码设置成功')
          })
        })
        .catch(() => {})
    }
    // 修改密码
    const confirmChangePwdService = () => {
      formRef.value
        .validateFields()
        .then(res => {
          modifySecretKey({
            email: noc.user.getUserInfo().email,
            password: formChangeState.oldPwd,
            newPassword: formChangeState.newPwd,
          }).then(res => {
            message.info('密码修改成功')
            pwdChangeVisible.value = false
          })
        })
        .catch(err => {
          message.error('密码修改失败')
        })
    }

    // 密码登录
    const enterPwd = () => {
      if (visigPwd.value && visigPwd.value.length > 5) {
        pwdLogin({
          email: noc.user.getUserInfo().email,
          password: visigPwd.value,
        })
          .then(res => {
            message.success('登录成功')
            pwdPass.value = true
            dbLists({
              secretKey: visigPwd.value,
              email: noc.user.getUserInfo().email,
            }).then(res => {
              folderList.value = res.Data.passwordGroups
            })
          })
          .catch(err => {
            message.error('密码错误')
          })
      } else {
        message.warning('请确认密码')
      }
    }

    const persent = ref(0)
    const secondNow = () => {
      const now = new Date().getSeconds()
      if (now < 30) {
        persent.value = ((30 - now) / 30) * 100
      } else if (now > 30) {
        persent.value = ((60 - now) / 30) * 100
      } else if (now == 30 || now == 60) {
        persent.value = 99.99
      }
    }

    const mfaCode = arr => {
      arr.forEach(item => {
        if (item.otp) {
          item.code = TOTP(item.otp).now()
        }
      })
    }
    // 选中文件夹
    const chooseFolder = folder => {
      currentFolder.value = folder
      console.log(currentFolder.value.passwordRecords, 'currentFolder.value.passwordRecords')
      mfaCode(currentFolder.value.passwordRecords)
      if (new Date().getSeconds()) {
        timeInter = setInterval(() => {
          secondNow()
        }, 500)
      }
    }
    //数据更新
    const pwdUpdate = () => {
      dbLists({
        secretKey: visigPwd.value,
        email: noc.user.getUserInfo().email,
      }).then(res => {
        folderList.value = res.Data.passwordGroups
      })
    }
    const progressStatus = ref('normal')
    const strokeColor = ref('')
    watch(
      persent,
      (newValue, oldValue) => {
        if (newValue < 3.34) {
          setTimeout(() => {
            persent.value = 99.99
            mfaCode(currentFolder.value.passwordRecords)
          }, 1000)
        } else if (newValue < 15) {
          // progressStatus.value = 'exception';
          strokeColor.value = '#FF8D1A'
          // codeStatus.value = 'code  activeCode';
          // codeStatus.value = 'code'
        } else if (newValue > 15) {
          progressStatus.value = 'normal'
          strokeColor.value = '#3975c6'
          // codeStatus.value = 'code'
        }
      },
      { immdiate: true }
    )

    const listSearch = text => {
      if (text == '') {
        pwdUpdate()
      } else {
        dbLists({
          secretKey: visigPwd.value,
          email: noc.user.getUserInfo().email,
          group: currentFolder.value.group,
          title: text,
        }).then(res => {
          folderList.value = res.Data.passwordGroups
        })
      }
    }
    const forgetPwd = () => {
      userDelete(noc.user.getUserInfo().email).then(res => {
        location.reload()
      })
    }
    const getRandomPwd = () => {
      const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
      let password = ''
      for (let i = 0; i < 16; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length)
        password += charset[randomIndex]
      }
      navigator.clipboard
        .writeText(password)
        .then(function () {
          message.success(password + '复制成功')
        })
        .catch(function (error) {
          message.error('复制失败: ', error)
        })
    }

    return {
      strokeColor,
      progressStatus,
      persent,
      mfaCode,
      forgetPwd,
      formState,
      formChangeState,
      visigPwd,
      pwdPass,
      hasAccount,
      pwdManageVisible,
      pwdChangeVisible,
      formRef,
      rules,
      layout,
      currentFolder,
      listSearch,
      resetForm,
      handleValidate,
      openPwdService,
      changePwd,
      confirmOpenPwdService,
      confirmChangePwdService,
      enterPwd,
      chooseFolder,
      pwdUpdate,
      folderList,
      getRandomPwd,
    }
  },
  mounted() {
    userCHeck({
      email: noc.user.getUserInfo().email,
    }).then(res => {
      if (res && res.Data) {
        this.hasAccount = res.Data.isExist
      }
    })
  },
})
</script>

<style lang="less" scoped>
.pwdContent {
  display: flex;
  height: 600px;
  .left {
    width: 200px;
    //border-right: 1px solid #ccc;
  }
  .right {
    width: calc(100% - 200px);
  }
}
.center-absolute {
  position: relative;
  height: 45%;
}
.center-absolute img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
