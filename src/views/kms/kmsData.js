/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-03-23 17:30:11
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-03-24 10:22:05
 * @FilePath: \cloud_web\src\views\kms\kmsData.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const adminColumns = [
  { title: '用户', dataIndex: 'userName', key: 'userName' },
  { title: '服务器Ip', dataIndex: 'serverIp', key: 'serverIp' },
  { title: '密钥引擎', dataIndex: 'secretsEngine', key: 'secretsEngine' },
  { title: '路径', dataIndex: 'secretPath', key: 'secretPath' },
  { title: ' 角色', dataIndex: 'role', key: 'role' },
  { title: '租约Id', dataIndex: 'leaseId', key: 'leaseId' },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
  { title: '结束时间', dataIndex: 'endTime', key: 'endTime' },
  { title: '状态', dataIndex: 'status', key: 'status' },
]
export const userColumns = [
  { title: '用户', dataIndex: 'userName', key: 'userName' },
  { title: '服务器Ip', dataIndex: 'serverIp', key: 'serverIp' },
  { title: '密钥引擎', dataIndex: 'secretsEngine', key: 'secretsEngine' },
  // { title: '路径', dataIndex: 'secretPath', key: 'secretPath' },
  // { title: ' 角色', dataIndex: 'role', key: 'role' },
  { title: '租约Id', dataIndex: 'leaseId', key: 'leaseId' },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
  { title: '结束时间', dataIndex: 'endTime', key: 'endTime' },
  { title: '状态', dataIndex: 'status', key: 'status' },
]
export const mockData = {
  Code: 200,
  Data: {
    secretinfos: [
      {
        userName: '姚旭红',
        serverIp: '********.44,*********',
        secretsEngine: 'aws',
        secretPath: 'xuhong_yao',
        leaseId: '4fc70BaweW42JZ6l2QYcpk9l',
        startTime: '2023-03-20T07:59:08.019100144+08:00',
        endTime: '2023-03-24T07:59:08.019100144+08:00',
        description: {
          policy:
            '{\n\t\t\t"Version": "2012-10-17",\n\t\t\t"Statement": [\n\t\t\t  {\n\t\t\t\t"Effect": "Allow",\n\t\t\t\t"Action": [\n\t\t\t\t  "s3:*"\n\t\t\t\t],\n\t\t\t\t"Resource": [\n\t\t\t\t  "arn:aws:s3:::camscanner",\n\t\t\t\t  "arn:aws:s3:::camscanner/*"\n\t\t\t\t]\n\t\t\t  }\n\t\t\t]\n\t\t  ',
          status: 'yes',
          expiresIn: '2days',
          ttl: '109705',
          issueTime: 'Mar 22, 2023 10:47:00 AM\n\t\t2023-03-22T10:47:00.172301319+08:00',
          lastRenewal: '',
          expirationTime: 'Mar 24, 2023 10:47:00 PM\n\t\t2023-03-24T22:47:00.172309735+08:00',
        },
      },
      {
        userName: '杨慧东',
        serverIp: '********.44,*********',
        secretsEngine: 'aws',
        secretPath: 'huidong_yang',
        leaseId: '4fc70BawdfagaZ6l2QYcpk9l',
        startTime: '2023-03-20T07:59:08.019100144+08:00',
        endTime: '2023-03-24T07:59:08.019100144+08:00',
        description: {
          policy:
            '{\n\t\t\t"Version": "2012-10-17",\n\t\t\t"Statement": [\n\t\t\t  {\n\t\t\t\t"Effect": "Allow",\n\t\t\t\t"Action": [\n\t\t\t\t  "s3:*"\n\t\t\t\t],\n\t\t\t\t"Resource": [\n\t\t\t\t  "arn:aws:s3:::camscanner",\n\t\t\t\t  "arn:aws:s3:::camscanner/*"\n\t\t\t\t]\n\t\t\t  }\n\t\t\t]\n\t\t  ',
          status: 'yes',
          expiresIn: '2days',
          ttl: '109345',
          issueTime: 'Mar 22, 2023 10:47:00 AM\n\t\t2023-03-22T10:47:00.172301319+08:00',
          lastRenewal: '',
          expirationTime: 'Mar 24, 2023 10:47:00 PM\n\t\t2023-03-24T22:47:00.172309735+08:00',
        },
      },
    ],
  },
  Ts: '2023-03-23 18:36:18.0786034 +0800 CST m=+502.317014501',
}
