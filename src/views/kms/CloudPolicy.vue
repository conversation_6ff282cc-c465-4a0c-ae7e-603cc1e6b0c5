<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="策略名称">
                <a-input v-model:value="queryParam.policyName" placeholder="模糊查询"
                         @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="供应商">
                <a-select v-model:value="queryParam.supplier" placeholder="请选择" :options="supplierList"
                          allowClear></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="描述">
                <a-input v-model:value="queryParam.description" placeholder="模糊查询"
                         @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              <tx-button type="primary" style="margin-left: 8px" @click="handleAdd">新增</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record: row, text: task_type}">
          <template v-if="column.dataIndex === 'comment'">
            <div class="tooltip">
              <a-tooltip placement="left">
                <template #title>
                  <span>{{ row.comment }}</span>
                </template>
                {{ row.comment }}
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(row)">更新</tx-button>
          </template>
          <template v-else-if="column.dataIndex === 'policyName'">
            <a-textarea
              class="requestArea"
              v-model:value="row.policyName"
              :autoSize="{ minRows: 3, maxRows: 5 }"
              :bordered="false"
            ></a-textarea>
          </template>
          <template v-else-if="column.dataIndex === 'pattern'">
            <a-textarea
              class="requestArea"
              v-model:value="row.pattern"
              :autoSize="{ minRows: 3, maxRows: 5 }"
              :bordered="false"
            ></a-textarea>
          </template>
        </template>
      </s-table>

      <a-modal :visible="updateVisible" :width="650" title="更新云厂商策略" :closable="false">
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          ref="updateForm"
          :rules="rules">
          <a-form-model-item label="ID" name="id">
            <a-input v-model:value="updateParam.id" placeholder="请输入策略名称" style="width: 450px" readOnly/>
          </a-form-model-item>
          <a-form-model-item label="策略名称" name="policyName">
            <a-textarea v-model:value="updateParam.policyName"
                        class="requestArea"
                        :autoSize="{ minRows: 3, maxRows: 5 }"
                        @input="removeSignalUpdate"
                        placeholder="请输入策略名称(权限文档中的Action方法)，多个方法之间用 , 连接"
                        style="width: 450px"/>
          </a-form-model-item>
          <a-form-model-item label="供应商" name="supplier">
            <a-select v-model:value="updateParam.supplier" placeholder="请选择供应商" :options="supplierList"
                      allowClear style="width: 450px"></a-select>
          </a-form-model-item>
          <a-form-model-item label="描述" name="description">
            <a-input v-model:value="updateParam.description" placeholder="请输入描述" style="width: 450px"/>
          </a-form-model-item>
          <a-form-model-item name="pattern">
            <template #label>
              资源模型
              <a-tooltip placement="right">
                <template #title>
                  <span>例：<br></span>
                  <span>腾讯云：qcs::cos::uid/{#accountId}:{#resourceId}/*<br></span>
                  <span>阿里云：acs:oss:*:{#accountId}:{#resourceId}/*<br></span>
                  <span>AWS：arn:aws:s3::{#accountId}:{#resourceId}/*<br></span>
                </template>
                <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle;"/>
              </a-tooltip>
            </template>
            <a-textarea v-model:value="updateParam.pattern"
                        class="requestArea"
                        :autoSize="{ minRows: 3, maxRows: 5 }"
                        @input="removeSignalUpdate"
                        placeholder="请输入资源模型(权限文档中的Resource字段)，多个资源模型之间用 , 连接"
                        style="width: 450px"/>
          </a-form-model-item>
          <a-form-model-item label="备注" name="comment">
            <a-input v-model:value="updateParam.comment" placeholder="请输入备注" style="width: 450px"/>
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
      </a-modal>

      <a-modal :visible="addVisible" :width="650" title="新增云厂商策略" :closable="false">
        <a-form-model
          :model="addParam"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          ref="addForm"
          :rules="rules">
          <a-form-model-item label="策略名称" name="policyName">
            <a-textarea v-model:value="addParam.policyName"
                        class="requestArea"
                        :autoSize="{ minRows: 3, maxRows: 5 }"
                        @input="removeSignalAdd"
                        placeholder="请输入策略名称(权限文档中的Action方法)，多个方法之间用 , 连接"
                        style="width: 450px"/>
          </a-form-model-item>
          <a-form-model-item label="供应商" name="supplier">
            <a-select v-model:value="addParam.supplier" placeholder="请选择供应商" :options="supplierList"
                      allowClear style="width: 450px"></a-select>
          </a-form-model-item>
          <a-form-model-item label="描述" name="description">
            <a-input v-model:value="addParam.description" placeholder="请输入描述" style="width: 450px"/>
          </a-form-model-item>
          <a-form-model-item name="pattern">
            <template #label>
              资源模型
              <a-tooltip placement="right">
                <template #title>
                  <span>例：<br></span>
                  <span>腾讯云：qcs::cos::uid/{#accountId}:{#resourceId}/*<br></span>
                  <span>阿里云：acs:oss:*:{#accountId}:{#resourceId}/*<br></span>
                  <span>AWS：arn:aws:s3::{#accountId}:{#resourceId}/*<br></span>
                </template>
                <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle;"/>
              </a-tooltip>
            </template>
            <a-textarea v-model:value="addParam.pattern"
                        class="requestArea"
                        :autoSize="{ minRows: 3, maxRows: 5 }"
                        @input="removeSignalAdd"
                        placeholder="请输入资源模型(权限文档中的Resource字段)，多个资源模型之间用 , 连接"
                        style="width: 450px"/>
          </a-form-model-item>
          <a-form-model-item label="备注" name="comment">
            <a-input v-model:value="addParam.comment" placeholder="请输入备注" style="width: 450px"/>
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelAdd">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import {STable, Ellipsis} from '@/components'
import {createCloudPolicy, listCloudPolicy, getCloudPolicy, updateCloudPolicy} from '@/api/kms/cloud_policy'
import {notification} from 'ant-design-vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: '50px'
  },
  {
    title: '供应商',
    dataIndex: 'supplier'
  },
  {
    title: '策略名称',
    dataIndex: 'policyName',
    width: '40%',
    scopedSlots: {customRender: 'policyName'}
  },
  {
    title: '描述',
    dataIndex: 'description'
  },
  {
    title: '资源模型',
    dataIndex: 'pattern',
    width: '20%',
    scopedSlots: {customRender: 'pattern'}
  },
  {
    title: '创建用户',
    dataIndex: 'creator'
  },
  {
    title: '备注',
    dataIndex: 'comment',
    scopedSlots: {customRender: 'comment'}
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: {customRender: 'action'},
    minWidth: '170px'
  }
]

const columnsPolicesChildren = [
  {
    title: '策略ID',
    dataIndex: 'policyId'
  },
  {
    title: '策略名称',
    dataIndex: 'policyName'
  },
  {
    title: '策略类型',
    dataIndex: 'policyType'
  },
  {
    title: '策略描述',
    dataIndex: 'description'
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  defaultPageSize: 10,
  hideOnSinglePage: true
}

export default {
  name: 'VaultPath',
  components: {
    STable,
    Ellipsis
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    this.columns = columns
    this.columnsPolicesChildren = columnsPolicesChildren
    this.pagination = pagination
    return {
      supplierList: [{label: '阿里云(合合)', value: '阿里云(合合)'}, {label: '阿里云(启信宝)', value: '阿里云(启信宝)'},
        {label: '阿里云(临冠)', value: '阿里云(临冠)'},
        {label: '腾讯云(intsig)', value: '腾讯云(intsig)'}, {label: '腾讯云(camscanner)', value: '腾讯云(camscanner)'},
        {label: '腾讯云(qixinbao)', value: '腾讯云(qixinbao)'}, {label: 'AWS(临冠)', value: 'AWS(临冠)'},
        {label: 'AWS(合合)', value: 'AWS(合合)'}, {label: 'AWS(海外)', value: 'AWS(海外)'},
        {label: 'AWS(海外AI)', value: 'AWS(海外AI)'}, {label: 'AWS(海外AI2)', value: 'AWS(海外AI2)'}],
      // 查询参数
      queryParam: {},
      addParam: {},
      updateParam: {},
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      rules: {
        policyName: [{required: true, max: 2048, message: '请输入策略名称，长度不超过2048', trigger: 'blur'}],
        supplier: [{required: true, message: '请输入供应商', trigger: 'blur'}],
        description: [{required: true, message: '请输入描述', trigger: 'blur'}],
        pattern: [{required: true, max: 1024, message: '请输入资源模型，长度不超过1024', trigger: 'blur'}],
        comment: [{required: true, message: '请输入备注', trigger: 'blur'}]
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listCloudPolicy(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('kmsCloudPolicies') && res.Data.kmsCloudPolicies !== null) {
            const rsp = {
              data: res.Data.kmsCloudPolicies || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
            return rsp
          } else {
            return {'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0}
          }
        })
      }
    }
  },
  methods: {
    handleAdd(record) {
      this.addParam = {
        policyName: '',
        supplier: undefined,
        description: '',
        pattern: '',
        comment: ''
      }
      this.addVisible = true
    },
    cancelAdd() {
      this.addVisible = false
      this.confirmLoading = false
    },
    submitAdd() {
      antdFormValidate(this.$refs.addForm, (valid) => {
        this.confirmLoading = true
        createCloudPolicy(this.addParam).then((res) => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '创建成功'
            })
            this.cancelAdd()
            this.$refs.table.refresh()
          } else {
            notification.error({
              message: '创建失败'
            })
            this.confirmLoading = false
          }
        }).catch(() => {
          this.confirmLoading = false
        })
      })
    },
    handleUpdate(row) {
      getCloudPolicy({id: row.id}).then(res => {
        this.updateParam = res.Data
        this.updateVisible = true
      })
    },
    cancelUpdate() {
      this.updateVisible = false
      this.confirmLoading = false
    },
    submitUpdate() {
      antdFormValidate(this.$refs.updateForm, (valid) => {
        this.confirmLoading = true
        updateCloudPolicy(this.updateParam).then((res) => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '更新成功'
            })
            this.cancelUpdate()
            this.$refs.table.refresh()
          } else {
            notification.error({
              message: '更新失败'
            })
            this.confirmLoading = false
          }
        }).catch(() => {
          this.confirmLoading = false
        })
      })
    },
    removeSignalUpdate() {
      this.updateParam.pattern = this.updateParam.pattern.replaceAll(/[\s\t\n\r]/g, '')
      this.addParam.pattern = this.addParam.pattern.replaceAll('${bucketname}', '{#resourceId}')
      this.updateParam.pattern = this.updateParam.pattern.replaceAll('"', '')
      this.updateParam.policyName = this.updateParam.policyName.replaceAll(/[\s\t\n\r]/g, '')
      this.updateParam.policyName = this.updateParam.policyName.replaceAll('"', '')
    },
    removeSignalAdd() {
      this.addParam.pattern = this.addParam.pattern.replaceAll(/[\s\t\n\r]/g, '')
      this.addParam.pattern = this.addParam.pattern.replaceAll('${bucketname}', '{#resourceId}')
      this.addParam.pattern = this.addParam.pattern.replaceAll('"', '')
      this.addParam.policyName = this.addParam.policyName.replaceAll(/[\s\t\n\r]/g, '')
      this.addParam.policyName = this.addParam.policyName.replaceAll('"', '')
    }
  }
}
</script>
<style lang="less" scoped>
.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.policytable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}

.requestArea {
  scrollbar-width: thin; /* 细化滚动条宽度 */
  scrollbar-color: transparent transparent; /* 设置滚动条颜色为透明 */
}
</style>
