<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="业务名称">
                <a-input v-model:value="queryParam.userName" placeholder="模糊查询" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="供应商">
                <a-select v-model:value="queryParam.supplier" placeholder="请选择" :options="supplierList"
                          allowClear></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record: row, text: task_type}">
          <template v-if="column.dataIndex === 'comment'">
            <div class="tooltip">
              <a-tooltip placement="left">
                <template #title>
                  <span>{{ row.comment }}</span>
                </template>
                {{ row.comment }}
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handlePermissionChange(row)">权限变更</tx-button>
          </template>
        </template>

        <template #expandedRowRender="{ record }">
          <div v-if="record.accessKeys.length > 0">
            <a>AKSK</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsAKSKChildren"
                  :data-source="record.accessKeys"
                  :pagination="pagination"
                  :rowKey="record => record.accessKey"
                  class="policytable"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'status'">
                      <a-tag :color="StatusColorFilter(record.status)">{{ StatusFilter(record.status) }}</a-tag>
                    </template>
                    <template v-if="column.dataIndex === 'rotatedStatus'">
                      <a-tag :color="RotatedStatusColorFilter(record.rotatedStatus)">
                        {{ RotatedStatusFilter(record.rotatedStatus) }}
                      </a-tag>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </div>
          <div v-if="record.authPolicies.length > 0">
            <a>授权策略</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsPolicesChildren"
                  :data-source="record.authPolicies"
                  :pagination="pagination"
                  :rowKey="record => record.PolicyName"
                  class="policytable"
                ></a-table>
              </a-col>
            </a-row>
          </div>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { getCloudAccessKeyList } from '@/api/kms/cloud_access_key'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    sorter: true
  },
  {
    title: '用户ID',
    dataIndex: 'userId',
    sorter: true
  },
  {
    title: '业务名称',
    dataIndex: 'userName',
    sorter: true
  },
  {
    title: '轮换过期时间',
    dataIndex: 'rotatedExpiredTime',
    sorter: true
  },
  {
    title: '创建用户',
    dataIndex: 'email'
  },
  // {
  //   title: '上次登录时间',
  //   dataIndex: 'lastLoginTime'
  // },
  {
    title: '备注',
    dataIndex: 'comment',
    scopedSlots: { customRender: 'comment' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    minWidth: '170px'
  }
]

const columnsPolicesChildren = [
  {
    title: '策略ID',
    dataIndex: 'policyId'
  },
  {
    title: '策略名称',
    dataIndex: 'policyName'
  },
  {
    title: '策略类型',
    dataIndex: 'policyType'
  },
  {
    title: '策略描述',
    dataIndex: 'description'
  }
]

const columnsAKSKChildren = [
  {
    title: 'AccessKey',
    dataIndex: 'accessKey'
  },
  {
    title: 'SecretKey',
    dataIndex: 'secretKey'
  },
  {
    title: '创建时间',
    dataIndex: 'createDate'
  },
  {
    title: '上次使用时间',
    dataIndex: 'lastLoginTime'
  },
  {
    title: '当前状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' }
  },
  {
    title: '轮换状态',
    dataIndex: 'rotatedStatus',
    scopedSlots: { customRender: 'rotatedStatus' }
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

const StatusMap = {
  Active: {
    color: 'green',
    text: '激活'
  },
  Inactive: {
    color: 'red',
    text: '禁用'
  }
}

const RotatedStatusMap = {
  1: {
    color: 'blue',
    text: '使用中'
  },
  2: {
    color: 'yellow',
    text: '备用'
  },
  3: {
    color: 'red',
    text: '待回收'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`,
  defaultPageSize: 10,
  hideOnSinglePage: true
}

export default {
  name: 'CloudAccessKey',
  components: {
    STable,
    Ellipsis
  },
  provide () {
    return {
      reload: this.reload
    }
  },
  data () {
    this.columns = columns
    this.columnsAKSKChildren = columnsAKSKChildren
    this.columnsPolicesChildren = columnsPolicesChildren
    this.pagination = pagination
    return {
      supplierList: [{ label: '阿里云(合合)', value: '阿里云(合合)' }, { label: '阿里云(启信宝)', value: '阿里云(启信宝)' },
        { label: '腾讯云(intsig)', value: '腾讯云(intsig)' }, { label: '腾讯云(camscanner)', value: '腾讯云(camscanner)' },
        { label: '腾讯云(qixinbao)', value: '腾讯云(qixinbao)' }, { label: 'AWS(临冠)', value: 'AWS(临冠)' },
        { label: 'AWS(合合)', value: 'AWS(合合)' }, { label: 'AWS(海外)', value: 'AWS(海外)' }],
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getCloudAccessKeyList(requestParameters).then((res) => {
          console.log('[loadData] res', res)
          if (res.Data.hasOwnProperty('kmsRamUserAksks') && res.Data.kmsRamUserAksks !== null) {
            return {
              data: res.Data.kmsRamUserAksks || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
          } else {
            return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
          }
        })
      }
    }
  },
  methods: {
    // 权限变更 相关接口
    handlePermissionChange (record) {
      if (record === '') {
        this.$router.push({ path: '/workflow/cloud-access-vault' })
        return
      }
      let recordData = {
        supplier: record.supplier,
        accountName: record.userName
      }
      // 拼接数据
      let permissions = []
      let permissions_type = []
      console.log('[handlePermissionChange] record.authPolicies', record.authPolicies)
      switch (record.supplier) {
        case '阿里云(启信宝)':
        case '阿里云(合合)':
          for (let i = 0; i < record.authPolicies.length; i++) {
            permissions.push(record.authPolicies[i].policyName)
            let permissions_type1 = {
              policyType: record.authPolicies[i].policyType,
              description: record.authPolicies[i].description,
              key: record.authPolicies[i].policyName
            }
            permissions_type.push(permissions_type1)
          }
          break
        case '腾讯云(intsig)':
        case '腾讯云(camscanner)':
        case '腾讯云(qixinbao)':
          for (let i = 0; i < record.authPolicies.length; i++) {
            permissions.push(record.authPolicies[i].policyId)
            let permissions_type1 = {
              policyType: record.authPolicies[i].policyName,
              description: record.authPolicies[i].description,
              key: record.authPolicies[i].policyId
            }
            permissions_type.push(permissions_type1)
          }
          break
        case 'AWS(海外)':
        case 'AWS(合合)':
        case 'AWS(临冠)':
          for (let i = 0; i < record.authPolicies.length; i++) {
            permissions.push(record.authPolicies[i].policyId)
            let permissions_type1 = {
              policyType: record.authPolicies[i].policyName,
              description: record.authPolicies[i].policyName,
              key: record.authPolicies[i].policyId
            }
            permissions_type.push(permissions_type1)
          }
          break
        default:
          break
      }
      recordData.permissions = permissions
      recordData.permissions_type = permissions_type
      recordData.operation = 2
      recordData.id = record.id
      recordData.method = 'Cloud'
      console.log('[handlePermissionChange] recordData', recordData)
      this.$router.push({ path: '/workflow/cloud-access-vault', query: { record: JSON.stringify(recordData) } })
    },
    StatusFilter (type) {
      return StatusMap[type]?.text || type
    },
    StatusColorFilter (type) {
      return StatusMap[type]?.color || type
    },
    RotatedStatusFilter (type) {
      return RotatedStatusMap[type]?.text || type
    },
    RotatedStatusColorFilter (type) {
      return RotatedStatusMap[type]?.color || type
    }
  }
}
</script>
<style lang="less" scoped>
.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.policytable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
