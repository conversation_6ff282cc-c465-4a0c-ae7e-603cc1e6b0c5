<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.searchKey" @pressEnter="$refs.table.refresh()"
                         placeholder="角色名称/邮箱" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="供应商">
                <a-select v-model:value="queryParam.supplier" placeholder="请选择" :options="supplierList"
                          allowClear></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="角色名称">{{ record.roleName }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
          </a-descriptions>
          <div v-if="record.cloudRolePolicy !== null && record.cloudRolePolicy.length > 0">
            <a>授权策略</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsPolicesChildren"
                  :data-source="record.cloudRolePolicy"
                  :pagination="pagination"
                  :rowKey="record => record.PolicyName"
                  class="policytable"
                ></a-table>
              </a-col>
            </a-row>
          </div>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { setWaterMark } from '@/utils/watermark'
import { getUserList } from '@/api/permission/user'
import { loadXLSX } from '@/utils/vendorLoader'
import store from '@/store'
import {
  listKmsKeycloakUser,
  banKmsKeycloakUserToRole,
  unbanKmsKeycloakUserToRole,
  listKmsKeycloakActionLog
} from '@/api/kms/keycloak'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    sorter: true
  },
  {
    title: '用户',
    dataIndex: 'email',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action'
  },
  {
    title: '操作时间',
    dataIndex: 'createdAt'
  }
]

const columnsPolicesChildren = [
  {
    title: '策略ID',
    dataIndex: 'key',
    width: '35%'
  },
  {
    title: '策略类型',
    dataIndex: 'policyType',
    width: '65%'
  },
  // {
  //   title: '策略描述',
  //   dataIndex: 'description',
  //   width: '45%'
  // }
]

const StatusMap = {
  1: {
    color: 'green',
    text: '开启'
  },
  2: {
    color: 'red',
    text: '禁用'
  },
  3: {
    color: 'orange',
    text: '离职'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`,
  defaultPageSize: 10,
  hideOnSinglePage: true
}

export default {
  name: 'KeycloakActionLog',
  components: {
    STable,
    Ellipsis
  },
  provide () {
    return {
      reload: this.reload
    }
  },
  mounted () {
    const email = store.getters.email
    this.userEmail = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  data () {
    this.columns = columns
    this.columnsPolicesChildren = columnsPolicesChildren
    this.pagination = pagination
    return {
      supplierList: [{ label: '阿里云(合合)', value: '阿里云(合合)' }, { label: '阿里云(启信宝)', value: '阿里云(启信宝)' },
        { label: '腾讯云(intsig)', value: '腾讯云(intsig)' }, { label: '腾讯云(camscanner)', value: '腾讯云(camscanner)' },
        { label: '腾讯云(qixinbao)', value: '腾讯云(qixinbao)' }, { label: 'AWS(临冠)', value: 'AWS(临冠)' },
        { label: 'AWS(合合)', value: 'AWS(合合)' }, { label: 'AWS(海外)', value: 'AWS(海外)' }],
      statusList: [
        { label: '开启', value: '1' },
        { label: '禁用', value: '2' }
        // { label: '注销', value: '3' }
      ],
      // 查询参数
      queryParam: {},
      userEmail: '',
      hasAdminRole: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listKmsKeycloakActionLog(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
            const rsp = {
              data: res.Data.data || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
            return rsp
          } else {
            return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
          }
        })
      }
    }
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('kms_admin') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
  }
}
</script>
<style lang="less" scoped>
.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.policytable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
