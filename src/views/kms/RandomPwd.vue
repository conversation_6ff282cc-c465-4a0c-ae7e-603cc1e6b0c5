<template>
  <a-form :model="formState" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-item label="所含字符：">
      <a-checkbox-group v-model:value="formState.charType" @change="handleSelectChange">
        <a-checkbox value="ss_xx" name="type">a-z</a-checkbox>
        <a-checkbox value="ss_dx" name="type">A-Z</a-checkbox>
        <a-checkbox value="ss_sz" name="type">0-9</a-checkbox>
        <a-checkbox value="ss_fh" name="type">!@#$%^&*</a-checkbox>
      </a-checkbox-group>
    </a-form-item>
    <a-form-item label="密码长度：">
      <a-input-number v-model:value="formState.num" :min="6" :max="64" />
    </a-form-item>
    <a-form-item label="所用字符：">
      <a-input v-model:value="formState.charset" />
    </a-form-item>
    <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
      <a-button type="primary" @click="onSubmit">生成密码</a-button>
    </a-form-item>
    <a-divider v-if="formState.result !== ''" />
    <a-form-item label="生成结果：" v-if="formState.result !== ''">
      <a-textarea v-model:value="formState.result" auto-size style="width: calc(100% - 200px)" />
      <a-button type="primary" style="margin-left: 4px" @click="copyText">复制</a-button>
    </a-form-item>
    <a-form-item label="密码强度" v-if="strength">
      <span v-if="strength == 1" style="color: red">弱</span>
      <span v-if="strength == 2" style="color: #d46b08">中</span>
      <span v-if="strength > 2" style="color: rgb(50, 100, 237)">强</span>
    </a-form-item>
  </a-form>
</template>
<script setup>
import zxcvbn from 'zxcvbn'
import { reactive, toRaw, ref } from 'vue'
import { message } from 'ant-design-vue'
const formState = reactive({
  charType: ['ss_xx', 'ss_dx', 'ss_sz', 'ss_fh'],
  num: 24,
  charset: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*',
  result: '',
})
const handleSelectChange = e => {
  let charset = ''
  for (let i = 0; i < e.length; i++) {
    if (e[i] === 'ss_xx') {
      charset += 'abcdefghijklmnopqrstuvwxyz'
    } else if (e[i] === 'ss_dx') {
      charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    } else if (e[i] === 'ss_sz') {
      charset += '0123456789'
    } else if (e[i] === 'ss_fh') {
      charset += '!@#$%^&*'
    }
  }
  Object.assign(formState, {
    charset: charset,
  })
}
let strength = ref('')
const onSubmit = () => {
  formState.result = ''
  const charset = toRaw(formState.charset)
  let password = ''
  if (charset.length !== 0) {
    for (let i = 0; i < toRaw(formState.num); i++) {
      const randomIndex = Math.floor(Math.random() * charset.length)
      password += charset[randomIndex]
    }
    Object.assign(formState, {
      result: password,
    })
  }
  const results = zxcvbn(password)
  console.log(results)
  strength.value = results.score
  console.log(strength.value, 'strent')
}

const copyText = () => {
  const text = toRaw(formState.result)
  navigator.clipboard
    .writeText(text)
    .then(function () {
      message.success('复制成功')
    })
    .catch(function (error) {
      message.error('复制失败: ', error)
    })
}

const labelCol = {
  style: {
    width: '150px',
  },
}
const wrapperCol = {
  span: 14,
}
</script>
