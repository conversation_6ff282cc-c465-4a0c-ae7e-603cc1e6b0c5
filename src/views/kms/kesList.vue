<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-03-14 14:27:24
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-06-05 15:55:48
 * @FilePath: \cloud_web\src\views\kms\kesList.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <template v-slot:content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=555680136" style="text-decoration: none">
          参考文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false" class="">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="密钥引擎">
          <a-select style="width: 120px" v-model:value="searchForm.secretsEngine">
            <a-select-option value="aws">AWS</a-select-option>
            <!-- <a-select-option value="aliCloud"> 阿里云 </a-select-option>
            <a-select-option value="tencentCloud"> 腾讯云 </a-select-option> -->
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select style="width: 120px" v-model:value="searchForm.status">
            <a-select-option value="0">全部</a-select-option>
            <a-select-option value="1">未过期</a-select-option>
            <a-select-option value="2">已过期</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="搜索">
          <a-input  v-model:value="searchForm.search" placeholder="" />
        </a-form-item>
        <a-form-item>
          <tx-button type="" style="margin-right: 8px" @click="resetForm">重置</tx-button>
          <tx-button type="primary" @click="queryData">查询</tx-button>
        </a-form-item>
      </a-form>
      <a-table rowKey="leaseId" :columns="columns" :data-source="tableData">
        <template #expandedRowRender="{ record }">
          <p class="m-0">
            <!-- {{ record.description }} -->
            <a-row>
              <a-col :span="8">
                <a-form-model
                  layout="horizontal"
                  v-bind="{
                    labelCol: { span: 6 },
                    wrapperCol: { span: 12 },
                  }"
                >
                  <a-form-model-item label=" 策略版本">
                    {{ record.description.policy.Version }}
                  </a-form-model-item>
                  <a-form-model-item label=" 权限范围：">
                    <span v-for="(items, index) in record.description.policy.Statement" :key="index">
                      {{ Object.keys(items)[0] }}:{{ items[Object.keys(items)[0]] }}
                      <br />
                      <span v-for="(u, index) in items.Action" :key="u">
                        {{ u }}
                        <br />
                      </span>
                      <span v-for="(v, index) in items.Resource" :key="v">
                        {{ v }}
                        <br />
                      </span>
                    </span>
                  </a-form-model-item>
                </a-form-model>
              </a-col>
              <a-col :span="8">
                <a-form-model
                  layout="horizontal"
                  v-bind="{
                    labelCol: { span: 6 },
                    wrapperCol: { span: 12 },
                  }"
                >
                  <a-form-model-item label=" 状态">
                    {{ record.description.status }}
                  </a-form-model-item>
                  <a-form-model-item label=" 到期时长">
                    {{ record.description.expiresIn }}
                  </a-form-model-item>
                  <a-form-model-item label=" TTL">
                    {{ record.description.ttl }}
                  </a-form-model-item>
                </a-form-model>
              </a-col>
              <a-col :span="8">
                <a-form-model
                  layout="horizontal"
                  v-bind="{
                    labelCol: { span: 6 },
                    wrapperCol: { span: 12 },
                  }"
                >
                  <a-form-model-item label=" 生成时间">
                    {{ record.description.issueTime }}
                  </a-form-model-item>
                  <a-form-model-item label=" 最近更新">
                    {{ record.description.lastRenewal }}
                  </a-form-model-item>
                  <a-form-model-item label=" 到期时间">
                    {{ record.description.expirationTime }}
                  </a-form-model-item>
                </a-form-model>
              </a-col>
            </a-row>
          </p>
        </template>
      </a-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { getUserList } from '@/api/permission/user'
import { adminColumns, userColumns, mockData } from './kmsData'
import store from '@/store'
import { secretInfo } from '@/api/kms'
export default {
  data() {
    return {
      userRoles: [],
      tableData: [],
      columns: [],
      searchForm: {
        secretsEngine: '',
        status: '',
        search: '',
      },
    }
  },
  methods: {
    // 查询
    queryData() {
      this.getSecretInfo(this.searchForm)
    },
    // 重置
    resetForm() {
      this.searchForm = {
        secretsEngine: '',
        status: '',
        search: '',
      }
      this.getSecretInfo()
    },
    // 获取数据接口
    getSecretInfo(params = {}) {
      secretInfo(params)
        .then(res => {
          this.tableData = res.Data.secretinfos
          this.tableData.forEach(item => {
            item.description.policy = JSON.parse(item.description.policy)
            console.log(item.description.policy, 'policypolicy')
          })
          // console.log(this.tableData,'this.tableData');
        })
        .catch(err => {
          console.log(err, 'errrrrr')
        })
    },

    initPage() {
      // 用户角色权限隔离
      getUserList({ searchText: store.getters.email }).then(response => {
        this.userRoles = response.Data.data[0].roles
        console.log(this.userRoles, 'userRolesuserRoles')
        if (this.userRoles.includes('admin')) {
          this.columns = adminColumns
        } else {
          this.columns = userColumns
        }
      })
      // 接口获取数据
      this.getSecretInfo()
    },
  },
  mounted() {
    this.initPage()
  },
}
</script>

<style lang="less" scoped>
/deep/.ant-form-inline > .ant-row {
  width: auto !important;
}
</style>
