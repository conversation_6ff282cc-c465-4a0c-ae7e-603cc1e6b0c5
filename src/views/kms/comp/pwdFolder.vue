<template>
  <div style="padding-right: 24px; height: 100%">
    <a-row>
      <a-col :span="8">
        <a-tooltip>
          <template #title>删除组</template>
          <a-button type="text" size="small" @click="delGroup">
            <template #icon><delete-filled /></template>
          </a-button>
        </a-tooltip>
      </a-col>
      <a-col :span="4" :offset="12">
        <a-tooltip>
          <template #title>新增组</template>
          <a-button style="margin-left: 8px" type="primary" size="small" @click="addGroup">
            <template #icon><plus-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-col>
    </a-row>

    <ul class="folderList">
      <li
        @click.stop="folderClick(item)"
        v-for="item in groupsData"
        :key="item"
        style="padding-left: 8px; padding-right: 8px; width: 100%; height: 50px; line-height: 50px"
        :class="item && item.active ? 'activeFolder' : 'normalFolder'"
      >
        <a class="folderName">
          <a-typography-text ellipsis style="width: 150px" :content="item.group" />
        </a>
        <a v-if="!showDel" style="float: right"><folder-open-filled /></a>
        <a-popconfirm
          title="此操作不可逆，是否确认删除？组内密码条目将移至【默认】。"
          ok-text="删除"
          cancel-text="取消"
          @confirm="confirmDel(item)"
          @cancel="cancelDel"
        >
          <a v-if="showDel && item.group != '默认'" @click.stop="delIconClick(item)" style="float: right">
            <delete-two-tone two-tone-color="red" />
          </a>
        </a-popconfirm>
      </li>
    </ul>
    <a-modal v-model:visible="addGroupVis" title="新增组" @cancel="groupState.groupName = ''" @ok="handleOk">
      <a-form :model="groupState" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" autocomplete="off">
        <a-form-item label="组名称" :rules="[{ required: true, message: '请输入组名', trigger: 'blur' }]">
          <a-input v-model:value="groupState.groupName" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue'
import { ref, reactive } from 'vue'
import cloneDeep from 'lodash.clonedeep'
import { groupCreate, groupDelete } from '@/api/kms/index'
export default {
  props: {
    floderList: {
      type: Array,
      default: () => {
        return []
      },
    },
    dbPwd: {
      type: String,
      default: () => {
        return ''
      },
    },
    currentFolder: {
      type: Object,
      default: () => {
        return null
      },
    },
  },
  emits: ['chooseFolder'],
  setup(props, ctx) {
    const groupState = reactive({
      groupName: '',
    })
    const addGroupVis = ref(false)
    let groupsData = ref()
    let showDel = ref(false)
    // 新增组
    const addGroup = () => {
      console.log('add')
      addGroupVis.value = true
    }
    // 删除组
    const delGroup = () => {
      console.log('delete')
      showDel.value = !showDel.value
    }
    // 确定删除
    const confirmDel = val => {
      console.log(val, 'vvvvv')
      groupDelete({ group: val.group, secretKey: props.dbPwd })
        .then(res => {
          message.success('删除成功')
        })
        .catch(() => {})
        .finally(() => {
          ctx.emit('update')
        })
    }
    // 确认新增
    const handleOk = () => {
      if (groupState.groupName.trim()) {
        addGroupVis.value = false
        groupCreate({
          group: groupState.groupName.trim(),
          secretKey: props.dbPwd,
        })
          .then(() => {
            ctx.emit('update')
            groupState.groupName = ''
          })
          .catch(() => {})
      } else {
        message.error('请输入组名')
      }
    }
    const delIconClick = () => {
      console.log('delIconClickdelIconClick')
    }
    // 取消删除
    const cancelDel = () => {}
    return {
      addGroup,
      delGroup,
      addGroupVis,
      handleOk,
      cancelDel,
      groupState,
      groupsData,
      confirmDel,
      showDel,
      delIconClick,
    }
  },

  methods: {
    // 文件夹click
    folderClick(item) {
      console.log(1111)
      this.showDel = false
      if (item) {
        this.groupsData.forEach(utem => {
          if (utem.active) {
            utem.active = false
          }
          if (utem.group == item.group) {
            utem.active = true
            this.$emit('chooseFolder', utem)
          }
        })
        // item.active = true
      }
    },
  },
  watch: {
    floderList: {
      handler: function (val) {
        if (val && val.length) {
          this.groupsData = cloneDeep(val)
          console.log(this.currentFolder, 'this.currentFolder')
          this.$nextTick(() => {
            if (this.currentFolder) {
              this.folderClick(this.currentFolder)
            } else {
              // 默认选中第一条文件夹
              this.folderClick(this.groupsData[0])
            }
          })
        }
      },
      deep: true,
    },
  },
}
</script>

<style lang="less" scoped>
.folderList {
  width: 100%;
  margin: 0;
  padding: 0;
  margin-top: 8px;
  height: calc(100% - 40px);
  background-color: #fff;
  .activeFolder {
    color: #1890ff;
  }
  .activeFolder .ant-typography {
    color: #1890ff;
  }
  .normalFolder a {
    color: black;
  }

  li {
    border-bottom: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
  }

  .folderName {
    display: inline-block;
    width: calc(100% - 28px);
  }
}
::-webkit-scrollbar {
  width: 5px;
  background: #eee;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  height: 100px;
  background-color: #ccc;
}
</style>
