<template>
  <div style="padding: 8px; padding-top: 0; height: 100%">
    <a-input-search
      style="width: 260px"
      v-model:value="searchValue"
      placeholder="密码名模糊查询"
      @pressEnter="$refs.table.refresh()"
      enter-button
      @search="onSearch"
    />
    <a-button type="primary" @click="addPwd" style="margin-left: 12px">
      <template #icon><plus-outlined /></template>
      新增密码
    </a-button>
    <a-upload
      :action="''"
      :showUploadList="false"
      :accept="'.csv'"
      v-model:file-list="fileList"
      name="file"
      :before-upload="handleChange"
    >
      <a-button style="margin-left: 12px">
        <template #icon><upload-outlined /></template>
        密码导入
      </a-button>
    </a-upload>
    <!-- <tx-button icon="vertical-align-bottom-outlined" @click="downloadExt" size="small" style="margin-left: 8px">
      <a-tooltip>
        <template #title>下载密码管家-浏览器插件</template>
        密码导入
      </a-tooltip>
    </tx-button> -->
    <a-table class="pwdTable" style="margin-top: 4px" bordered :data-source="dataSource" :columns="columns">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'opt'">
          <span>
            <a @click="editPwd(record)">
              <edit-two-tone />
            </a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="此操作不可逆，是否确认删除？"
              ok-text="删除"
              cancel-text="取消"
              @confirm="confirmDel"
              @cancel="cancelDel"
            >
              <a style="margin-left: 4px" @click="delPwd(record)">
                <delete-two-tone two-tone-color="red" />
              </a>
            </a-popconfirm>
          </span>
        </template>
        <!--        <template v-if="column.key === 'title'">-->
        <!--          <a-typography-paragraph v-if="record.title" style="margin-bottom: 0em" :copyable="{ text: record.title }">-->
        <!--            {{ record.title }}-->
        <!--          </a-typography-paragraph>-->
        <!--        </template>-->
        <template v-if="column.key === 'username'">
          <a-typography-paragraph
            v-if="record.username"
            style="margin-bottom: 0em"
            :copyable="{ text: record.username }"
          >
            {{ record.username }}
            <template #copyableTooltip="{ copied }">
              <span v-if="copied">用户名：复制成功！</span>
              <span v-else>复制</span>
            </template>
          </a-typography-paragraph>
        </template>
        <template v-if="column.key === 'otp'">
          <!-- <span>
            <a @click="goWebSite(record.website)">{{ record.website }}</a>
          </span> -->
          <!--          <a-tag v-if="!record.otp" color="orange">无</a-tag>-->
          <a-typography-paragraph v-if="record.code" style="margin-bottom: 0em" :copyable="{ text: record.code }">
            <p style="display: inline-block; width: 100px; margin-bottom: 0">
              <span :style="{ color: strokeColor }">{{ record.code }}</span>
              <a-progress
                style="display: inline-block; width: 30px; margin-left: 10px"
                v-if="record.code"
                size="small"
                :show-info="false"
                :percent="percent"
                :strokeColor="strokeColor"
                :steps="5"
                :status="status"
              />
            </p>
            <template #copyableTooltip="{ copied }">
              <span v-if="copied">MFA：复制成功！</span>
              <span v-else>复制</span>
            </template>
          </a-typography-paragraph>
          <span v-else>未绑定MFA</span>
          <!-- <a-tag v-else color="blue">
          </a-tag> -->
        </template>
        <template v-if="column.key === 'website'">
          <span v-if="record.website">
            <a @click="goWebSite(record.website)">{{ record.website }}</a>
          </span>
          <span v-else>-</span>
        </template>
        <template v-if="column.key === 'password'">
          <a-typography-paragraph
            v-if="record.password"
            style="margin-bottom: 0em"
            :copyable="{ text: record.password }"
          >
            <template #copyableTooltip="{ copied }">
              <span v-if="copied">密码：复制成功！</span>
              <span v-else>复制</span>
            </template>
            ******
          </a-typography-paragraph>
          <span v-else>-</span>
        </template>
      </template>
    </a-table>
    <a-modal
      :destroyOnClose="true"
      :maskClosable="false"
      v-model:visible="modifyVisable"
      :title="modalTitle"
      @ok="handleOk"
    >
      <p style="color: #bd6868; font-size: 12px">提示：建议不要存储非工作密码</p>
      <a-form
        v-if="modifyVisable"
        :model="listState"
        name=""
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="组名称" name="group" :rules="[{ required: true, message: '请输入组名', trigger: 'blur' }]">
          <a-input :disabled="true" v-model:value="listState.group" />
        </a-form-item>
        <a-form-item
          label="密码名"
          name="title"
          :rules="[{ required: true, message: '请输入密码名', trigger: 'blur' }]"
        >
          <a-input style="width: 280px; margin-right: 8px" v-model:value="listState.title" />
          <a-tooltip>
            <template #title>复制</template>
            <copy-outlined @click="copyPwd(listState.title)" />
          </a-tooltip>
        </a-form-item>
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名', trigger: 'blur' }]"
        >
          <a-input style="width: 280px; margin-right: 8px" v-model:value="listState.username" />
          <a-tooltip>
            <template #title>复制</template>
            <copy-outlined @click="copyPwd(listState.username)" />
          </a-tooltip>
        </a-form-item>
        <a-form-item label="密码" name="password" :rules="[{ required: true, message: '密码', trigger: 'blur' }]">
          <a-input-password
            autocomplete="new-password"
            style="width: 280px; margin-right: 8px"
            v-model:value="listState.password"
          />
          <a-tooltip>
            <template #title>复制</template>
            <copy-outlined @click="copyPwd(listState.password)" />
          </a-tooltip>
          <!-- <a @click="aiPwd" style="cursor: pointer">
            帮你生成
            <thunderbolt-outlined />
          </a> -->
          <a-form :model="pwdInfos" name="" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" autocomplete="off">
            <a-form-item label="长度" name="pwdLength" class="myItem">
              <a-row>
                <a-col :span="18">
                  <a-slider
                    style="width: 180px"
                    @change="pwdLengthChange"
                    v-model:value="pwdInfos.pwdLength"
                    :min="6"
                    :max="32"
                  />
                </a-col>
                <a-col :span="2">
                  <a-tooltip>
                    <template #title>换一个</template>
                    <a @click="pwdLengthChange" style="font-size: 18px; cursor: pointer">
                      <cloud-sync-outlined />
                    </a>
                  </a-tooltip>
                </a-col>
              </a-row>
            </a-form-item>
            <a-form-item v-if="pwdStrength" label="密码强度" name="pwdLength" class="myItem">
              <span v-if="pwdStrength == 1" style="color: red">弱</span>
              <span v-if="pwdStrength == 2" style="color: #d46b08">中</span>
              <span v-if="pwdStrength > 2" style="color: rgb(50, 100, 237)">强</span>
            </a-form-item>
          </a-form>
        </a-form-item>
        <a-form-item label="网站" name="website">
          <a-input style="width: 280px; margin-right: 8px" v-model:value="listState.website" />
          <a-tooltip>
            <template #title>复制</template>
            <copy-outlined @click="copyPwd(listState.website)" />
          </a-tooltip>
        </a-form-item>
        <a-form-item label="MFA密钥" name="otp">
          <a-input v-model:value="listState.otp" />
        </a-form-item>
        <a-form-item label="备注" name="comment">
          <a-textarea v-model:value="listState.comment" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import zxcvbn from 'zxcvbn'
import { loadXLSX } from '@/utils/vendorLoader'
import { defineComponent, ref, computed } from 'vue'
import { pwdColumns } from './mockData'
import { message } from 'ant-design-vue'
import { listCreate, listModify, listDelete, batchCreate } from '@/api/kms/index'
import { deepClone } from '@/utils/util'
import { UploadOutlined } from '@ant-design/icons-vue'
export default defineComponent({
  props: {
    pwdArray: {
      type: Object,
      default: () => {
        return {
          passwordRecords: [],
        }
      },
    },
    percent: {
      type: Number,
      default: () => {
        return 0
      },
    },
    strokeColor: {
      type: String,
      default: () => {
        return ''
      },
    },
    status: {
      type: String,
      default: () => {
        return 'normal'
      },
    },
    dbPwd: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  components: {
    UploadOutlined,
  },
  setup(props, ctx) {
    const aiPwdVis = ref(false)
    const searchValue = ref('')
    const modifyVisable = ref(false)
    const columns = ref(pwdColumns)
    const modalTitle = ref('')
    const dataSource = computed(() => {
      return props.pwdArray.passwordRecords
    })

    const pwdInfos = ref({
      pwdLength: 12,
    })
    let listState = ref({
      group: '',
      title: '',
      username: '',
      password: '',
      website: '',
      otp: '',
      comment: '',
    })
    const handleChange = async file => {
      await handleReadExcel(file)
      return false
    }
    const handleReadExcel = async file => {
      const that = this
      const XLSX = await loadXLSX()
      const fileReader = new FileReader()
      fileReader.onload = async ev => {
        try {
          const fileData = ev.target.result
          const workbook = XLSX.read(fileData, {
            type: 'binary',
          })
          const wsname = workbook.SheetNames[0] // 取第一张表
          const snArr = XLSX.utils.sheet_to_json(workbook.Sheets[wsname]) // 生成json表格内容
          let paramsArr = []
          if (snArr && snArr.length) {
            for (let i = 0; i < snArr.length; i++) {
              paramsArr.push({
                title: String(snArr[i].name),
                username: String(snArr[i].username),
                password: String(snArr[i].password),
                website: String(snArr[i].url),
              })
            }
            batchCreate({
              passwordRecords: paramsArr,
              group: '浏览器密码',
              secretKey: props.dbPwd,
            })
              .then(res => {
                message.success('已导入至“浏览器密码”组')
                ctx.emit('update')
              })
              .catch(err => {
                console.log(err, 'err')
              })
          }
        } catch (e) {
          return false
        }
      }
      fileReader.readAsBinaryString(file)
    }

    const fileList = ref([])
    const goWebSite = val => {
      if (val.includes('http')) {
        window.open(val)
      } else {
        window.open(`https://${val}`)
      }

      //
    }
    // 确定删除
    const confirmDel = e => {
      listDelete({ ...listState.value, secretKey: props.dbPwd })
        .then(res => {
          message.success('删除成功')
        })
        .catch(() => {})
        .finally(() => {
          ctx.emit('update')
        })
    }
    // 取消删除
    const cancelDel = () => {}
    // 搜索pwd
    const onSearch = () => {
      ctx.emit('search', searchValue.value)
    }

    // 删除密码条目
    const delPwd = record => {
      listState.value = deepClone(record)
    }
    // 弹出框ok
    const handleOk = e => {
      switch (modalTitle.value) {
        case '新增密码条目':
          listCreate({
            ...listState.value,
            secretKey: props.dbPwd,
          }).then(res => {
            message.success('新增成功')

            modifyVisable.value = false
            ctx.emit('update')
          })
          break
        case '修改密码条目':
          //
          listModify({
            ...listState.value,
            secretKey: props.dbPwd,
          })
            .then(res => {
              message.success('修改成功')
              modifyVisable.value = false
              ctx.emit('update')
            })
            .catch(() => {})
          break
      }
    }
    // 密码条目修改
    const editPwd = record => {
      listState.value = deepClone(record)
      modifyVisable.value = true
      modalTitle.value = '修改密码条目'
    }
    const pwdStrength = ref('')
    let autpPwd = (len = 32) => {
      let strVals = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678!@#$%^&./' // 剔除了易混淆的字符oOLl,9gq,Vv,Uu,I1
      let maxLen = strVals.length
      let randomStr = ''
      for (var i = 0; i < len; i++) {
        randomStr += strVals.charAt(Math.floor(Math.random() * maxLen))
      }
      const result = zxcvbn(randomStr)
      pwdStrength.value = result.score
      return randomStr
    }
    const aiPwd = () => {
      aiPwdVis.value = !aiPwdVis.value
      if (aiPwdVis.value) {
        listState.value.password = autpPwd(pwdInfos.value.pwdLength)
      }
    }

    const pwdLengthChange = val => {
      listState.value.password = autpPwd(pwdInfos.value.pwdLength)
    }

    return {
      pwdStrength,
      columns,
      handleChange,
      fileList,
      searchValue,
      dataSource,
      modalTitle,
      onSearch,
      pwdInfos,
      aiPwdVis,
      autpPwd,
      delPwd,
      handleOk,
      confirmDel,
      cancelDel,
      editPwd,
      aiPwd,
      pwdLengthChange,
      goWebSite,
      modifyVisable,
      listState,
    }
  },
  methods: {
    // 新增条目
    addPwd() {
      this.listState = {
        group: this.pwdArray.group,
        title: '未命名',
        username: noc.user.getUserInfo().email.split('@')[0],
        password: this.autpPwd(12),
        website: '',
        otp: '',
        comment: '',
      }
      this.modifyVisable = true
      this.modalTitle = '新增密码条目'
    },
    copyPwd(val) {
      const input = document.createElement('input') //不会保留文本格式
      //如果要保留文本格式，比如保留换行符，或者多行文本，可以使用  textarea 标签，再配和模板字符串 ` `
      //const input = document.createElement('textarea')
      // 将想要复制的值
      input.value = val
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      //this.$message.success('复制成功');
      // 复制后移除输入框
      input.remove()
      message.success('复制成功')
    },
  },
})
</script>

<style lang="less" scoped>
.myItem {
  margin-bottom: 0 !important;
  margin-top: 8px;
}
.pwdTable {
  height: calc(100% - 32px);
}
/deep/.ant-table-cell {
  padding: 7px !important;
}
</style>
