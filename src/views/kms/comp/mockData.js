/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-07-25 09:50:15
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-08-28 19:23:49
 * @FilePath: \cloud_web\src\views\kms\comp\mockData.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const passwordGroups = [
  {
    group: '文件夹1',
    key: '1',
    passwordRecords: [
      {
        title: 'baidu',
        username: 'huidong_yang',
        password: '111111',
        otp: 'AE1DedE123',
        website: 'www.baidu.com',
      },
    ],
  },
  {
    group: '文件夹2',
    key: '2',
    passwordRecords: [
      {
        name: 'tencent',
        userName: 'yangHuidong',
        pwd: '22222',
        otp: 'DeiwenW2113WdxS:',
        webSite: 'www.baidu.com',
      },
    ],
  },
]
export const pwdColumns = [
  {
    title: '密码名',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '密码',
    dataIndex: 'password',
    key: 'password',
    // width: '230px',
  },
  {
    title: 'MFA',
    dataIndex: 'otp',
    width: '150px',
    key: 'otp',
  },
  {
    title: '网站',
    dataIndex: 'website',
    key: 'website',
  },
  {
    title: '操作',
    dataIndex: 'opt',
    key: 'opt',
    width: '65px',
  },
]
