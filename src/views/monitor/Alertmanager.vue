<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="匹配标签">
                <a-select v-model:value="queryParam.matchName" allowClear>
                  <a-select-option v-for="item1 in matchNameList" :key="item1.value" :value="item1.value">
                    {{ item1.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="匹配内容">
                <a-input v-model:value="queryParam.matchValue" placeholder="模糊查询" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="接收人">
                <a-input v-model:value="queryParam.monitorUser" placeholder="模糊查询" />
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="6" :sm="24">
                <a-form-item label="告警方式">
                  <a-select v-model:value="queryParam.alertLevel" allowClear>
                    <a-select-option v-for="item1 in alertLevelList" :key="item1.value" :value="item1.value">
                      {{ item1.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 6) || 24" :sm="24">
              <span class="table-page-search-submitButtons" :style="{}">
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>

                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-popconfirm placement="topRight" @confirm="CreatAlertmanager('visible')">
                  <template #title>
                    <p>新建的alertmanager, 会立即生效！！！</p>
                  </template>
                  <template #icon>
                    <a-icon type="question-circle-o" style="color: red" />
                  </template>
                  <tx-button type="primary" style="float: right">添加</tx-button>
                </a-popconfirm>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table ref="table" size="default" rowKey="id" :pagination="pagination" :columns="columns" :data="loadData">
        <template #expandedRowRender="{ record }">
          <a-descriptions :column="3">
            <a-descriptions-item label="Id">{{ record.id }}</a-descriptions-item>
            <a-descriptions-item label="接收人微信">{{ record.monitorUserIds }}</a-descriptions-item>
            <a-descriptions-item label="来源">{{ record.source }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="接收人电话">{{ record.monitorUserTel }}</a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="StatusTypeColorFilter(record.status)">{{ StatusTypeFilter(record.status) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'alertLevel'">
            <a-tag :color="alertTypeColorFilter(record.alertLevel)">
              {{ alertTypeFilter(record.alertLevel) }}
            </a-tag>
          </template>
          <template v-if="column.dataIndex == 'action' && (userpRression || record.monitorUser.includes(localUser))">
            <a @click="updateAlertmanager('visible', record)">更新</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-popconfirm title="确定删除这条Alertmanager数据？" @confirm="handleDel(record)">
                      <template #icon>
                        <a-icon type="question-circle-o" style="color: red" />
                      </template>
                      <a style="width: 40px">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a style="width: 40px" @click="CreatAlertmanager('clone', record)">克隆</a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a>
                更多
                <a-icon type="down" />
              </a>
            </a-dropdown>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal v-model:visible="addVisible" title="新建Alertmanager告警">
      <template #footer>
        <tx-button key="back" @click="CreatAlertmanager('cancel')">取消</tx-button>
        <tx-button type="primary" @click="CreatAlertmanager('add')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="addForm"
        :model="addParam"
        :rules="addRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 17 }"
      >
        <a-form-model-item label="匹配标签" name="matchName" class="my-ant-form-item">
          <a-select v-model:value="addParam.matchName" show-search>
            <a-select-option v-for="item in matchNameList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="匹配内容" name="matchValue" class="my-ant-form-item">
          <a-input v-model:value="addParam.matchValue" />
        </a-form-model-item>
        <a-form-model-item label="告警方式" name="alertLevel" class="my-ant-form-item">
          <a-select v-model:value="addParam.alertLevel">
            <a-select-option v-for="item in alertLevelList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="接收人邮箱" name="monitorUser" class="my-ant-form-item">
          <a-select
            placeholder="输入接收人邮箱:<EMAIL>"
            v-model:value="addParam.monitorUser"
            mode="multiple"
            :showSearch="true"
            :allowClear="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="优先级(1->100)" name="weights" class="my-ant-form-item">
          <a-input v-model:value="addParam.weights" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="updateVisible" title="更新Alertmanager告警">
      <template #footer>
        <tx-button key="back" @click="updateAlertmanager('cancel')">取消</tx-button>
        <tx-button type="primary" @click="updateAlertmanager('update')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="updateForm"
        :model="updateParam"
        :rules="addRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 17 }"
      >
        <a-form-model-item label="匹配标签" name="matchName" class="my-ant-form-item">
          <a-select v-model:value="updateParam.matchName" show-search>
            <a-select-option v-for="item in matchNameList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="匹配内容" name="matchValue" class="my-ant-form-item">
          <a-input v-model:value="updateParam.matchValue" />
        </a-form-model-item>
        <a-form-model-item label="告警方式" name="alertLevel" class="my-ant-form-item">
          <a-select v-model:value="updateParam.alertLevel">
            <a-select-option v-for="item in alertLevelList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="接收人邮箱" name="monitorUser" class="my-ant-form-item">
          <a-select
            placeholder="输入接收人邮箱:<EMAIL>"
            v-model:value="updateParam.monitorUser"
            mode="multiple"
            :showSearch="true"
            :allowClear="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="优先级(1->100)" name="weights" class="my-ant-form-item">
          <a-input v-model:value="updateParam.weights" />
        </a-form-model-item>
        <a-form-model-item label="告警状态" name="status" class="my-ant-form-item">
          <a-select v-model:value="updateParam.status" show-search>
            <a-select-option v-for="item in statusList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { STable } from '@/components'
import { getUserList } from '@/api/permission/user'
import store from '@/store'
import { alertmanagerList, createAlertmanager, deleteAlertmanager, updatAlertmanager} from '@/api/monitor/alertmanager'

const StatusTypeMap = {
  1: {
    color: 'green',
    text: '告警',
  },
  0: {
    color: 'red',
    text: '不告警',
  },
}


const alertTypeMap = {
  0: {
    color: 'blue',
    text: '企业微信',
  },
  1: {
    color: 'red',
    text: '短信',
  },
  2: {
    color: 'orange',
    text: 'kvm重启',
  },
  4: {
    color: 'green',
    text: 'Grafana语音',
  },
  5: {
    color: 'yellow',
    text: 'AlertPlus',
  },
}

const columns = [
  {
    title: '匹配标签',
    dataIndex: 'matchName',
  },
  {
    title: '匹配内容',
    dataIndex: 'matchValue',
    sorter: true,
  },
  {
    title: '接收人',
    dataIndex: 'monitorUser',
    sorter: true,
  },
  {
    title: '告警方式',
    dataIndex: 'alertLevel',
    sorter: true,
  },
  {
    title: '优先级',
    dataIndex: 'weights',
    width: '80px',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '140px',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}
export default {
  name: 'Alertmanager',
  components: { STable },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      scrollPage: 1,
      valueData: '',
      userEmailList: [],
      idcList: [],
      jobList: [],
      typeList: [],
      userRoles: [],
      userRolesWhite: 'opsAdmin',
      userpRression: false,
      statusList: [
        { label: '不告警', value: '0' },
        { label: '告警', value: '1' },
      ],
      alertLevelList: [
        { label: '企业微信(0)', value: 0 },
        { label: '短信(1)', value: 1 },
        { label: 'kvm重启(2)', value: 2 },
        { label: 'Grafana语音(4)', value: 4 },
        { label: 'AlertPlus(5)', value: 5 },
      ],
      matchNameList: [
        { label: '业务/环境(job)', value: 'job' },
        { label: '负责人微信ID(wx)', value: 'wx' },
        { label: '告警策略名称(alertname)', value: 'alertname' },
      ],

      addRules: {
        matchName: [{ required: true, message: '请选择匹配标签', trigger: 'blur' }],
        matchValue: [{ required: true, message: '请输入匹配内容', trigger: 'blur' }],
        alertLevel: [{ required: true, message: '请选择告警方式', trigger: 'blur' }],
        monitorUser: [{ required: true, message: '请输入接收人邮箱，可以选多个接收人', trigger: 'blur' }],
      },
      updateParam: {},
      addParam: {},
      visible: false,
      updateVisible: false,
      addVisible: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)

        return alertmanagerList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {
    this.getUserRoles(this.localUser.split('@')[0])
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite) || this.userRoles.includes('admin')) {
          this.userpRression = true
        }
      })
    },
    StatusTypeFilter(type) {
      return StatusTypeMap[type]?.text || type
    },
    StatusTypeColorFilter(type) {
      return StatusTypeMap[type]?.color || type
    },
    alertTypeFilter(type) {
      return alertTypeMap[type]?.text || type
    },
    alertTypeColorFilter(type) {
      return alertTypeMap[type]?.color || type
    },
    handleUpdate(record) {
      this.updateVisible = true
      this.updateQueryParam = record
    },
    cancelUpdateInit() {
      this.updateVisible = false
      this.updateQueryParam = {}
    },
    CreatAlertmanager(type, record) {
      switch (type) {
        case 'add':
          antdFormValidate(this.$refs.addForm, valid => {
            if (valid) {
              let data = JSON.parse(JSON.stringify(this.addParam))
              data.monitorUser = data.monitorUser.join('|')
              createAlertmanager(data).then(res => {
                if (res.Code == 200) {
                  this.$message.success('添加成功')
                  this.$refs.table.refresh(true)
                }
              })
              this.addVisible = false
            }
          })
          break
        case 'cancel':
          this.addVisible = false
          this.addParam = {}
          break
        case 'visible':
          this.addVisible = true
          this.addParam = {}
          break
        case 'clone':
          this.addVisible = true
          this.addParam = record
          this.addParam.monitorUser = record.monitorUser.split('|')
      }
    },
    updateAlertmanager(type, record) {
      switch (type) {
        case 'update':
          antdFormValidate(this.$refs.updateForm, valid => {
            if (valid) {
              let data = JSON.parse(JSON.stringify(this.updateParam))
              data.monitorUser = data.monitorUser.join('|')
              updatAlertmanager(data).then(res => {
                if (res.Code == 200) {
                  this.$message.success('更新成功')
                  this.$refs.table.refresh(true)
                }
              })
              this.updateVisible = false
            }
          })
          break
        case 'cancel':
          this.updateVisible = false
          this.updateParam = {}
          break
        case 'visible':
          this.updateVisible = true
          this.updateParam = JSON.parse(JSON.stringify(record))
          this.updateParam.monitorUser = record.monitorUser.split('|')
          delete this.updateParam.updatedAt
          delete this.updateParam.createdAt
          break
      }
    },
    handleDel(record) {
      deleteAlertmanager({ id: record.id }).then(res => {
        if (res.Code == 200) {
          this.$message.success('删除成功')
          this.$refs.table.refresh(true)
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}
</style>
