<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="节点">
                <a-select v-model:value="queryParam.nodeId" allowClear>
                  <a-select-option v-for="item1 in nodeList" :key="item1.value" :value="item1.value">
                    {{ item1.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="名称">
                <a-input v-model:value="queryParam.alertRule" placeholder="" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="策略">
                <a-input v-model:value="queryParam.expr" placeholder="" />
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="6" :sm="24">
                <a-form-item label="告警内容">
                  <a-input v-model:value="queryParam.summary" placeholder="" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="状态">
                  <a-select v-model:value="queryParam.status" allowClear>
                    <a-select-option v-for="item1 in statusList" :key="item1.value" :value="item1.value">
                      {{ item1.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 6) || 18" :sm="24">
              <span class="table-page-search-submitButtons" :style="(advanced && {}) || {}">
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-popconfirm placement="topRight" @confirm="PrometheusRule('visible')">
                  <template #title>
                    <p>新建的rule, 会立即生效！！！</p>
                  </template>
                  <template #icon>
                    <a-icon type="question-circle-o" style="color: red" />
                  </template>
                  <tx-button type="primary" style="float: right">添加</tx-button>
                </a-popconfirm>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table ref="table" size="default" rowKey="id" :pagination="pagination" :columns="columns" :data="loadData">
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="节点名称">{{ record.nodeName }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'status'">
            <a-tag :color="StatusTypeColorFilter(record.status)">{{ StatusTypeFilter(record.status) }}</a-tag>
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a @click="updatePrometheusRule('visible', record)">更新</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-popconfirm title="确定删除Export？" @confirm="handleDel(record)">
                      <template #icon>
                        <a-icon type="question-circle-o" style="color: red" />
                      </template>
                      <a style="width: 40px">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a style="width: 40px" @click="PrometheusRule('clone', record)">克隆</a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a>
                更多
                <a-icon type="down" />
              </a>
            </a-dropdown>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal v-model:visible="addVisible" :width="1000" title="新建Prometheus告警策略">
      <template #footer>
        <tx-button key="back" @click="PrometheusRule('cancel')">取消</tx-button>
        <tx-button type="primary" :loading="loading" @click="PrometheusRule('add')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="addForm"
        :model="addParam"
        :rules="addRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="节点" name="nodeId" class="my-ant-form-item">
          <a-select v-model:value="addParam.nodeId">
            <a-select-option v-for="item1 in nodeList" :key="item1.value" :value="item1.value">
              {{ item1.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="策略名称" name="alertRule" class="my-ant-form-item">
          <a-input v-model:value="addParam.alertRule" />
        </a-form-model-item>
        <a-form-model-item label="表达式" name="expr" class="my-ant-form-item">
          <a-textarea v-model:value="addParam.expr" autosize />
        </a-form-model-item>
        <a-form-model-item label="时间间隔" name="timeFor" class="my-ant-form-item">
          <a-input v-model:value="addParam.timeFor" placeholder="60s" />
        </a-form-model-item>
        <a-form-model-item label="告警内容" name="summary" class="my-ant-form-item">
          <a-textarea v-model:value="addParam.summary" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="updateVisible" :width="1000" title="更新Prometheus告警策略">
      <template #footer>
        <tx-button key="back" @click="updatePrometheusRule('cancel')">取消</tx-button>
        <tx-button type="primary" :loading="loading" @click="updatePrometheusRule('update')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="updateForm"
        :model="updateParam"
        :rules="addRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="策略名称" name="alertRule" class="my-ant-form-item">
          <a-input v-model:value="updateParam.alertRule" disabled />
        </a-form-model-item>
        <a-form-model-item label="表达式" name="expr" class="my-ant-form-item">
          <a-textarea v-model:value="updateParam.expr" autosize />
        </a-form-model-item>
        <a-form-model-item label="时间间隔" name="timeFor" class="my-ant-form-item">
          <a-input v-model:value="updateParam.timeFor" placeholder="60s" />
        </a-form-model-item>
        <a-form-model-item label="告警内容" name="summary" class="my-ant-form-item">
          <a-textarea v-model:value="updateParam.summary" />
        </a-form-model-item>
        <a-form-model-item label="策略状态" name="status" class="my-ant-form-item">
          <a-select v-model:value="updateParam.status">
            <a-select-option v-for="item1 in statusList" :key="item1.value" :value="item1.value">
              {{ item1.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { Ellipsis, STable } from '@/components'
import { createRule, deleteRule, getRulesList, updateRule } from '@/api/monitor/rule'

const StatusTypeMap = {
  1: {
    color: 'green',
    text: '生效',
  },
  0: {
    color: 'red',
    text: '未生效',
  },
}

const columns = [
  {
    title: '策略名称',
    dataIndex: 'alertRule',
    sorter: true,
    width: '160px',
  },
  {
    title: '策略表达式',
    dataIndex: 'expr',
    sorter: true,
  },
  {
    title: '时间',
    dataIndex: 'timeFor',
    width: '60px',
  },
  {
    title: '告警内容',
    dataIndex: 'summary',
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '140px',
    align: 'center',
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}
export default {
  name: 'Rule',
  components: { STable },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      scrollPage: 1,
      valueData: '',
      treePageSize: 50,
      loading: false,
      SourceList: [],
      domainInfo: [],
      addDomain: [],
      statusList: [
        { label: '不生效', value: '0' },
        { label: '生效', value: '1' },
      ],
      nodeList: [
        { label: 'Prometheus_混合云', value: 1 },
        { label: 'Prometheus_阿里云', value: 2 },
      ],

      addRules: {
        nodeId: [{ required: true, message: '请选择要添加的Prometheus节点', trigger: 'blur' }],
        alertRule: [
          { required: true, message: '请输入策略名称, 策略名称必须为字母或者下划线', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regex = /^[a-zA-Z_0-9]+$/
              if (!value || regex.test(value)) {
                callback()
              } else {
                callback(new Error('策略名称必须为字母或者下划线'))
              }
            },
            trigger: 'change',
          },
        ],
        expr: [{ required: true, message: '请输入策略表达式', trigger: 'blur' }],
        timeFor: [
          { required: true, message: '请输入时间间隔', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regex = /^\d{1,}s/i
              if (!value || regex.test(value)) {
                callback()
              } else {
                callback(new Error('时间间隔必须为数字s,例如: 60s'))
              }
            },
            trigger: 'change',
          },
        ],
        summary: [{ required: true, message: '请输入告警内容模板', trigger: 'blur' }],
      },
      updateParam: {},
      addParam: {},
      visible: false,
      updateVisible: false,
      addVisible: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)

        return getRulesList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {},
  methods: {
    StatusTypeFilter(type) {
      return StatusTypeMap[type]?.text || type
    },
    StatusTypeColorFilter(type) {
      return StatusTypeMap[type]?.color || type
    },
    handleUpdate(record) {
      this.updateVisible = true
      this.updateQueryParam = record
    },
    cancelUpdateInit() {
      this.updateVisible = false
      this.updateQueryParam = {}
    },
    PrometheusRule(type, record) {
      this.loading = false
      switch (type) {
        case 'add':
          antdFormValidate(this.$refs.addForm, valid => {
            if (valid) {
              this.loading = true
              createRule(this.addParam).then(res => {
                if (res.Code == 200) {
                  this.$message.success('添加成功')
                  this.$refs.table.refresh(true)
                  this.addVisible = false
                  this.loading = false
                }
              })
              setTimeout(() => {
                this.loading = false
              }, 5000)
            }
          })
          break
        case 'cancel':
          this.addVisible = false
          this.addParam = {}
          break
        case 'visible':
          this.addVisible = true
          this.addParam = {}
          break
        case 'clone':
          this.addVisible = true
          this.addParam = JSON.parse(JSON.stringify(record))
      }
    },
    updatePrometheusRule(type, record) {
      this.loading = false
      switch (type) {
        case 'update':
          antdFormValidate(this.$refs.updateForm, valid => {
            if (valid) {
              this.loading = true
              updateRule(this.updateParam).then(res => {
                if (res.Code == 200) {
                  this.$message.success('更新成功')
                  this.$refs.table.refresh(true)
                  this.loading = false
                  this.updateVisible = false
                }
              })
              setTimeout(() => {
                this.loading = false
              }, 5000)
            }
          })
          break
        case 'cancel':
          this.updateVisible = false
          this.updateParam = {}
          break
        case 'visible':
          this.updateVisible = true
          this.updateParam = JSON.parse(JSON.stringify(record))
          delete this.updateParam.updatedAt
          delete this.updateParam.createdAt
          break
      }
    },
    handleDel(record) {
      this.loading = false
      deleteRule({ id: record.id, nodeId: record.nodeId }).then(res => {
        if (res.Code == 200) {
          this.$message.success('删除成功')
          this.$refs.table.refresh(true)
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}
</style>
