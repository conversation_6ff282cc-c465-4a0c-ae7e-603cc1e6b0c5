<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="节点">
                <a-select v-model:value="queryParam.nodeId" allowClear>
                  <a-select-option v-for="item1 in nodeList" :key="item1.value" :value="item1.value">
                    {{ item1.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="IDC">
                <a-select v-model:value="queryParam.idc" allowClear show-search>
                  <a-select-option v-for="item in idcList" :key="item" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="Target">
                <a-input v-model:value="queryParam.target" placeholder="模糊查询" />
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="6" :sm="24">
                <a-form-item label="Job">
                  <a-select v-model:value="queryParam.job" allowClear show-search>
                    <a-select-option v-for="item in jobList" :key="item" :value="item">
                      {{ item }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="Type">
                  <a-select v-model:value="queryParam.type" allowClear show-search>
                    <a-select-option v-for="item in typeList" :key="item" :value="item">
                      {{ item }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="状态">
                  <a-select v-model:value="queryParam.status" allowClear>
                    <a-select-option v-for="item1 in statusList" :key="item1.value" :value="item1.value">
                      {{ item1.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="来源">
                  <a-select v-model:value="queryParam.source" allowClear>
                    <a-select-option v-for="item1 in sourceList" :key="item1.value" :value="item1.value">
                      {{ item1.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 6) || 6" :sm="24">
              <span class="table-page-search-submitButtons" :style="{}">
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>

                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-popconfirm placement="topRight" @confirm="PrometheusExporter('visible')">
                  <template #title>
                    <p>新建的target, 会立即生效！！！</p>
                  </template>
                  <template #icon>
                    <a-icon type="question-circle-o" style="color: red" />
                  </template>
                  <tx-button type="primary" style="float: right">添加</tx-button>
                </a-popconfirm>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table ref="table" size="default" rowKey="id" :pagination="pagination" :columns="columns" :data="loadData">
        <template #expandedRowRender="{ record }">
          <a-descriptions :column="2">
            <a-descriptions-item label="接收人微信">{{ record.monitorUserIds }}</a-descriptions-item>
            <a-descriptions-item label="主机名">{{ record.hostname }}</a-descriptions-item>
            <a-descriptions-item label="来源">
              <a-tag :color="SourceTypeColorFilter(record.source)">{{ SourceTypeFilter(record.source) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="StatusTypeColorFilter(record.status)">{{ StatusTypeFilter(record.status) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="应用标签">{{ record.application }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-switch :checked="record.status === '1'"
                      checked-children="开"
                      un-checked-children="关"
                      class="custom-switch"
                      :loading="switchLoading"
                      @change="checked=> changeMonitorSwitch(checked, record)" />
          </template>
          <template v-if="column.dataIndex == 'action'">
            <a @click="updatePrometheusExporter('visible', record)">更新</a>
            <a-divider type="vertical" v-if="record.source == 'system' || record.deleteShow == true" />
            <a-dropdown v-if="record.source == 'system' || record.deleteShow == true">
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="record.source == 'system' || record.deleteShow == true">
                    <a-popconfirm title="确定删除Export？" @confirm="handleDel(record)">
                      <template #icon>
                        <a-icon type="question-circle-o" style="color: red" />
                      </template>
                      <a style="width: 40px">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item v-if="record.source == 'system'">
                    <a style="width: 40px" @click="PrometheusExporter('clone', record)">克隆</a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a>
                更多
                <a-icon type="down" />
              </a>
            </a-dropdown>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal v-model:visible="addVisible" title="新建Exporter">
      <template #footer>
        <tx-button key="back" @click="PrometheusExporter('cancel')">取消</tx-button>
        <tx-button type="primary" @click="PrometheusExporter('add')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="addForm"
        :model="addParam"
        :rules="addRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="节点" name="nodeId" class="my-ant-form-item">
          <a-select v-model:value="addParam.nodeId">
            <a-select-option v-for="item1 in nodeList" :key="item1.value" :value="item1.value">
              {{ item1.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="Idc" name="idc" class="my-ant-form-item">
          <a-select v-model:value="addParam.idc" show-search>
            <a-select-option v-for="item in idcList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="Target" name="target" class="my-ant-form-item">
          <a-input v-model:value="addParam.target" />
        </a-form-model-item>
        <a-form-model-item label="Type" name="typeName" class="my-ant-form-item">
          <a-input v-model:value="addParam.typeName" />
        </a-form-model-item>
        <a-form-model-item label="Job" name="jobName" class="my-ant-form-item">
          <a-input v-model:value="addParam.jobName" />
        </a-form-model-item>
        <a-form-model-item label="负责人" name="monitorUser" class="my-ant-form-item">
          <a-select
            placeholder="<EMAIL>"
            v-model:value="addParam.monitorUser"
            mode="multiple"
            :showSearch="true"
            :allowClear="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="updateVisible" title="更新Exporter">
      <template #footer>
        <tx-button key="back" @click="updatePrometheusExporter('cancel')">取消</tx-button>
        <tx-button type="primary" @click="updatePrometheusExporter('update')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="updateForm"
        :model="updateParam"
        :rules="addRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <div v-if="updateParam.source == 'system'">
          <a-form-model-item label="Idc" name="idc" class="my-ant-form-item">
            <a-select v-model:value="updateParam.idc" show-search>
              <a-select-option v-for="item in idcList" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="Target" name="target" class="my-ant-form-item">
            <a-input v-model:value="updateParam.target" />
          </a-form-model-item>
          <a-form-model-item label="Type" name="typeName" class="my-ant-form-item">
            <a-input v-model:value="updateParam.typeName" />
          </a-form-model-item>
          <a-form-model-item label="Job" name="jobName" class="my-ant-form-item">
            <a-input v-model:value="updateParam.jobName" />
          </a-form-model-item>
          <a-form-model-item label="负责人" name="monitorUser" class="my-ant-form-item">
            <a-select
              placeholder="<EMAIL>"
              v-model:value="updateParam.monitorUser"
              mode="multiple"
              :showSearch="true"
              :allowClear="true"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
        <a-form-model-item label="策略状态" name="status" class="my-ant-form-item">
          <a-select v-model:value="updateParam.status">
            <a-select-option v-for="item1 in statusList" :key="item1.value" :value="item1.value">
              {{ item1.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { Ellipsis, STable } from '@/components'
import { getUserList } from '@/api/permission/user'
import store from '@/store'
import {
  createExporter,
  deleteExporter,
  getExporterList,
  getFilterList,
  getIdcList,
  updateExporter,
} from '@/api/monitor/exporter'
import { notification } from 'ant-design-vue'

const StatusTypeMap = {
  1: {
    color: 'green',
    text: '监控',
  },
  0: {
    color: 'red',
    text: '未监控',
  },
}

const SourceTypeMap = {
  asset: {
    color: 'green',
    text: '服务器资产',
  },
  system: {
    color: 'red',
    text: '自定义资产',
  },
}

const columns = [
  {
    title: '节点名',
    dataIndex: 'nodeName',
    sorter: true,
    width: '120px',
  },
  {
    title: 'Idc',
    dataIndex: 'idc',
    sorter: true,
    width: '200px',
  },
  {
    title: 'Target',
    dataIndex: 'target',
    sorter: true,
  },
  {
    title: 'Job',
    dataIndex: 'jobName',
    sorter: true,
  },
  {
    title: 'Type',
    dataIndex: 'typeName',
    sorter: true,
  },
  {
    title: '接收人',
    dataIndex: 'monitorUser',
    width: '300px',
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '140px',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
]

const columnsAdmin = [
  {
    title: '节点名',
    dataIndex: 'nodeName',
    sorter: true,
    width: '120px',
  },
  {
    title: 'Idc',
    dataIndex: 'idc',
    sorter: true,
    width: '200px',
  },
  {
    title: 'Target',
    dataIndex: 'target',
    sorter: true,
  },
  {
    title: 'Job',
    dataIndex: 'jobName',
    sorter: true,
  },
  {
    title: 'Type',
    dataIndex: 'typeName',
    sorter: true,
  },
  {
    title: '接收人',
    dataIndex: 'monitorUser',
    width: '300px',
    sorter: true,
  },
  {
    title: '开关',
    dataIndex: 'status',
    width: '80px'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '140px',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}
export default {
  name: 'Exporters',
  components: { STable },
  data() {
    this.pagination = pagination
    this.downloadtableList = null
    return {
      columns: [],
      localUser: store.getters.email,
      scrollPage: 1,
      valueData: '',
      userEmailList: [],
      idcList: [],
      jobList: [],
      typeList: [],
      userRoles: [],
      userRolesWhite: 'opsAdmin',
      userpRression: false,
      statusList: [
        { label: '不监控', value: '0' },
        { label: '监控', value: '1' },
      ],
      sourceList: [
        { label: '服务器资产', value: 'asset' },
        { label: '自定义资产', value: 'system' },
      ],
      nodeList: [
        { label: 'Prometheus_混合云', value: 1 },
        { label: 'Prometheus_阿里云', value: 2 },
      ],

      addRules: {
        nodeId: [{ required: true, message: '请选择要添加的Prometheus节点', trigger: 'blur' }],
        idc: [{ required: true, message: '请选择idc机房', trigger: 'blur' }],
        target: [{ required: true, message: '请输入服务器ip:port信息', trigger: 'blur' }],
        jobName: [{ required: true, message: '请输入Job值', trigger: 'blur' }],
        typeName: [{ required: true, message: '请输入type值', trigger: 'blur' }],
        monitorUser: [{ required: true, message: '请输入负责人邮箱，可以选多个负责人', trigger: 'blur' }],
      },
      updateParam: {},
      addParam: {},
      visible: false,
      updateVisible: false,
      addVisible: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      switchLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)

        return getExporterList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {
    this.IdcList()
    this.FilterList('job')
    this.FilterList('type')
    this.getUserRoles(this.localUser.split('@')[0])
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles == null) {
          if (this.localUser === '<EMAIL>' || this.localUser === '<EMAIL>') {
            this.columns = columnsAdmin
          } else {
            this.columns = columns
          }
          return
        }
        if (this.userRoles.includes(this.userRolesWhite) || this.userRoles.includes('admin')) {
          this.userpRression = true
        }
        if (this.userRoles.includes('admin')) {
          this.columns = columnsAdmin
        } else if (this.localUser === '<EMAIL>' || this.localUser === '<EMAIL>') {
          this.columns = columnsAdmin
        } else {
          this.columns = columns
        }
      })
    },
    StatusTypeFilter(type) {
      return StatusTypeMap[type]?.text || type
    },
    StatusTypeColorFilter(type) {
      return StatusTypeMap[type]?.color || type
    },
    SourceTypeFilter(type) {
      return SourceTypeMap[type]?.text || type
    },
    SourceTypeColorFilter(type) {
      return SourceTypeMap[type]?.color || type
    },
    handleUpdate(record) {
      this.updateVisible = true
      this.updateQueryParam = record
    },
    cancelUpdateInit() {
      this.updateVisible = false
      this.updateQueryParam = {}
    },
    IdcList() {
      getIdcList().then(res => {
        if (res.Code == 200) {
          this.idcList = res.Data.idc
        }
      })
    },
    FilterList(value) {
      getFilterList({ filterValue: value }).then(res => {
        if (res.Code == 200) {
          switch (value) {
            case 'job':
              this.jobList = res.Data.datas
              break
            case 'type':
              this.typeList = res.Data.datas
              break
          }
        }
      })
    },
    PrometheusExporter(type, record) {
      switch (type) {
        case 'add':
          antdFormValidate(this.$refs.addForm, valid => {
            if (valid) {
              let data = JSON.parse(JSON.stringify(this.addParam))
              data.monitorUser = data.monitorUser.join('|')
              createExporter(data).then(res => {
                if (res.Code == 200) {
                  this.$message.success('添加成功')
                  this.$refs.table.refresh(true)
                }
              })
              this.addVisible = false
            }
          })
          break
        case 'cancel':
          this.addVisible = false
          this.addParam = {}
          break
        case 'visible':
          this.addVisible = true
          this.addParam = {
            nodeId: 1,
          }
          break
        case 'clone':
          this.addVisible = true
          this.addParam = record
          this.addParam.monitorUser = record.monitorUser.split('|')
      }
    },
    updatePrometheusExporter(type, record) {
      switch (type) {
        case 'update':
          antdFormValidate(this.$refs.updateForm, valid => {
            if (valid) {
              let data = JSON.parse(JSON.stringify(this.updateParam))
              data.monitorUser = data.monitorUser.join('|')
              updateExporter(data).then(res => {
                if (res.Code == 200) {
                  this.$message.success('更新成功')
                  this.$refs.table.refresh(true)
                }
              })
              this.updateVisible = false
            }
          })
          break
        case 'cancel':
          this.updateVisible = false
          this.updateParam = {}
          break
        case 'visible':
          this.updateVisible = true
          this.updateParam = JSON.parse(JSON.stringify(record))
          this.updateParam.monitorUser = record.monitorUser.split('|')
          delete this.updateParam.updatedAt
          delete this.updateParam.createdAt
          break
      }
    },
    handleDel(record) {
      deleteExporter({ id: record.id, nodeId: record.nodeId }).then(res => {
        if (res.Code == 200) {
          this.$message.success('删除成功')
          this.$refs.table.refresh(true)
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    changeMonitorSwitch (checked, row) {
      this.switchLoading = true
      if (checked) {
        row.status = '1'
      } else {
        row.status = '0'
      }
      row.onlyModifyDb = true
      updateExporter(row)
        .then((res) => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '更新成功'
            })
            this.switchLoading = false
            this.$refs.table.refresh(true)
          }
        })
        .catch(() => {
          this.switchLoading = false
          this.$refs.table.refresh(true)
        })
    }
  },
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}
</style>
