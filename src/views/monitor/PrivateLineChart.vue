<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-04-24 17:13:12
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-10-10 18:32:10
 * @FilePath: \cloud_web\src\views\cost\comp\charts\userCostTrendChart.vue
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="chartContainer">
    <!-- <a-empty style="height: 320px" v-if="!chartData.length" /> -->
    <div :ref="compRef" :id="compRef"></div>
  </div>
</template>
<script>
import { Line } from '@antv/g2plot'
export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 320,
    },
  },
  data() {
    return {
      line: undefined,
    }
  },
  async mounted() {
    this.initLineChart() // 调用 initLineChart 方法初始化图表
  },
  methods: {
    initLineChart() {
      if (this.chartData.length && !this.line) {
        this.line = new Line(this.compRef, {
          data: this.chartData,
          xField: 'time',
          yField: 'data',
          height: this.height,
          seriesField: 'name',
          width: 700,
          xAxis: {
            type: 'time',
            mask: 'hh:mm',
          },
          yAxis: {
            label: {
              formatter: v => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, s => `${s},`),
            },
          },
          legend: {
            position: 'bottom',
            itemHeight: 30,
          },
        })
        this.line.render()
      }
    },
    updateChartData(data) {
      this.line.changeData(data)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
