<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div style="display: flex; justify-content: center;">
        <a-image
          :width="1000"
          src="/vendor-cdn/@img/PrivateLine.png"
          :preview="false"
        />
      </div>
      <div class="table-page-search-wrapper">
        <br/>
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="14" :sm="24">
              <a-form-item label="专线路径查询">
                <a-select v-model:value="flowIN" show-search placeholder="流量入口" style="width: 40%" @change="flowInChange">
                  <a-select-option v-for="it in flowINList" :value="it" :key="it">{{ it }}</a-select-option>
                </a-select>
                <ArrowRightOutlined style="font-size: 20px" />
                <a-select v-model:value="flowOut" show-search placeholder="流量出口" style="width: 40%" @change="flowOutChange">
                  <a-select-option v-for="it in flowOutList" :value="it" :key="it">{{ it }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="路径选择">
                <a-select v-model:value="line" show-search placeholder="线号" style="width: 100%" >
                  <a-select-option v-for="it in lineList" :value="it" :key="it">{{ it }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="3" :sm="24">
              <a-form-item label="时间">
                <a-select v-model:value="searchTime" show-search placeholder="" style="width: 100%" >
                  <a-select-option v-for="it in timeList" :value="it" :key="it">{{ it }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="2" :sm="24">
              <tx-button type="primary" @click="PrivateLineSearch">查询</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>


      <a-card title="丢包率" style="">
        <template #extra> 
          <a style="font-size: 35px;" v-if="grafanaUrl !== ''" :href="grafanaUrl" target="_blank">
          <area-chart-outlined />
        </a>
      </template>
        <a-skeleton v-if="loading" />
        <Chart :compRef="'lostpk'" :chartData="lostpkData" ref="childRef1" />
      </a-card>
      <a-card title="延迟">
        <template #extra> <a style="font-size: 35px;" v-if="grafanaUrl !== ''" :href="grafanaUrl" target="_blank">
          <area-chart-outlined />
        </a></template>
        <Chart :compRef="'rrt'" :chartData="rrtData" ref="childRef2" />
      </a-card>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { cdnRefresh, listCdnRefreshRecord } from '@/api/network/cdn'
import { notification } from 'ant-design-vue'
import Chart from '@/views/monitor/PrivateLineChart.vue'
import { PriveateLineMonitorInfo } from '@/api/monitor/common'


// const columns = [
//   {
//     title: 'url地址',
//     dataIndex: 'url'
//   },
//   {
//     title: '类型',
//     dataIndex: 'urlsType'
//   },
//   {
//     title: '来源',
//     dataIndex: 'urlsBelongs'
//   },
//   {
//     title: '任务ID',
//     dataIndex: 'purgeId'
//   },
//   {
//     title: '任务状态',
//     dataIndex: 'taskStatus'
//   },
//   {
//     title: '刷新时间',
//     dataIndex: 'createdAt'
//   }

// ]
export default {
  name: 'PrivateLine',
  components: {
    Chart
  },
  data() {
    return {
      loading: false,
      lostpkData: [{ name: 'line0', data: 0, time: 1710298890000 }],
      rrtData: [{ name: 'line0', data: 0, time: 1710298890000 }],
      flowIN: '',
      flowOut: '',
      line: '',
      grafanaUrl: '',
      searchTime: '过去6小时',
      timeList: [
        '过去1小时',
        '过去3小时',
        '过去6小时',
        '过去12小时',
        '过去24小时',
      ],
      flowINList: ['宝信(10.2.0.0/16)',
        '云立方office(192.168.32.0/24)',
        '颛桥(10.24.0.0/16|10.26.2.0/24|10.26.3.0/24|10.26.1.0/24)',
        '阿里云(172.16.0.0/16|10.21.0.0/16)',
        'AWS宁夏(10.8.0.0/16 172.31.0.0/16 10.40.0.0/16)',
        'AWS海外(10.3.0.0/16 10.4.0.0/16 10.5.252.0/22 10.6.0.0/16 10.7.0.0/16 10.16.0.0/16 10.17.0.0/16 10.18.0.0/16 10.12.0.0/16)',
        '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)',
        '南京腾讯云(10.34.0.0/16)'],
      flowOutList: [],
      lineList: [],
      lineMap: {
        '宝信(10.2.0.0/16)': {
          out: [
            '云立方office(192.168.32.0/24)',
            '颛桥(10.26.1.0/24)',
            '松江(10.48.0.0/16)',
            '阿里云(172.16.0.0/16|10.21.0.0/16)',
            'AWS宁夏(10.8.0.0/16 172.31.0.0/16 10.40.0.0/16)',
            'AWS海外(10.3.0.0/16 10.4.0.0/16 10.5.252.0/22 10.6.0.0/16 10.7.0.0/16 10.16.0.0/16 10.17.0.0/16 10.18.0.0/16 10.12.0.0/16)',
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)',
            '南京腾讯云(10.34.0.0/16)'
          ], line: {
            '云立方office(192.168.32.0/24)': ['(1/2)--3'],
            '颛桥(10.26.1.0/24)': ['10'],
            '松江(10.48.0.0/16)': ['14--15--16'],
            '阿里云(172.16.0.0/16|10.21.0.0/16)': ['13', '1--12', '2--12'],
            'AWS宁夏(10.8.0.0/16 172.31.0.0/16 10.40.0.0/16)': ['(1/2)--4', '(1/2)--5'],
            'AWS海外(10.3.0.0/16 10.4.0.0/16 10.5.252.0/22 10.6.0.0/16 10.7.0.0/16 10.16.0.0/16 10.17.0.0/16 10.18.0.0/16 10.12.0.0/16)': ['(1/2)--6', '(1/2)--7'],
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)': ['14--15'],
            '南京腾讯云(10.34.0.0/16)': ['17']
          }
        },
        '云立方office(192.168.32.0/24)': {
          out: [
            '颛桥(10.24.0.0/16|10.26.2.0/24|10.26.3.0/24|10.26.1.0/24)',
            '松江(10.48.0.0/16)',
            'AWS宁夏(10.8.0.0/16 172.31.0.0/16 10.40.0.0/16)',
            'AWS海外(10.3.0.0/16 10.4.0.0/16 10.5.252.0/22 10.6.0.0/16 10.7.0.0/16 10.16.0.0/16 10.17.0.0/16 10.18.0.0/16 10.12.0.0/16)',
            '腾讯云(10.31.0.0/16|10.33.0.0/16)'
          ],
          line: {
            '颛桥(10.24.0.0/16|10.26.2.0/24|10.26.3.0/24|10.26.1.0/24)': ['3--(8/9)'],
            '松江(10.48.0.0/16)': ['3--(1/2)-14--15--16'],
            'AWS宁夏(10.8.0.0/16 172.31.0.0/16 10.40.0.0/16)': ['3--(4/5)'],
            'AWS海外(10.3.0.0/16 10.4.0.0/16 10.5.252.0/22 10.6.0.0/16 10.7.0.0/16 10.16.0.0/16 10.17.0.0/16 10.18.0.0/16 10.12.0.0/16)': ['3--6', '3--(1/2)--7'],
            '腾讯云(10.31.0.0/16|10.33.0.0/16)': ['3--(1/2)--14--15']
          }
        },
        '颛桥(10.24.0.0/16|10.26.1.0/24|10.26.2.0/24)': {
          out: ['松江(10.48.0.0/16)',
            '阿里云(172.16.0.0/16|10.21.0.0/16)',
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)'],
          line: {
            '松江(10.48.0.0/16)': ['11--14--15--16'],
            '阿里云(172.16.0.0/16|10.21.0.0/16)': ['11--13', '11--(1/2)-12'],
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)': ['11--14--15']
          }
        },
        '阿里云(172.16.0.0/16|10.21.0.0/16)': {
          out: [
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)',
            '南京腾讯云(10.34.0.0/16)'
          ],
          line: {
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)': ['12--(1/2)--14--15', '13--14--15'],
            '南京腾讯云(10.34.0.0/16)': ['13-17']
          }
        },
        'AWS宁夏(10.8.0.0/16 172.31.0.0/16 10.40.0.0/16)': {
          out: [
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)'
          ],
          line: {
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)': ['(4/5)--(1/2)--14-15']
          }
        },
        'AWS海外(10.3.0.0/16 10.4.0.0/16 10.5.252.0/22 10.6.0.0/16 10.7.0.0/16 10.16.0.0/16 10.17.0.0/16 10.18.0.0/16 10.12.0.0/16)': {
          out: [
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)'
          ],
          line: {
            '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)': ['6--(1/2)--14--15', '7--14--15']
          }
        },
        '腾讯云(10.30.0.0/16|10.31.0.0/16|10.33.0.0/16)': {
          out: [
            '宝信(10.2.0.0/16)'
          ],
          line: {
            '宝信(10.2.0.0/16)': ['15']
          }
        },
        '南京腾讯云(10.34.0.0/16)': {
          out: [
            '宝信(10.2.0.0/16)'
          ],
          line: {
            '宝信(10.2.0.0/16)': ['17']
          }
        },
      },
      activeKey: 1,
      submitStatus: false,
      queryParam: {},
      labelCol: { span: 1 },
      wrapperCol: { span: 8, offset: 1 },
      temp: {
        urlsType: 'Files',
        content: '',
        encode_url: false
      },
      urlsTypeOptions: [
        { key: 'Files', display_name: '文件列表' },
        { key: 'Directories', display_name: '目录列表' }
      ],
    }
  },
  methods: {

    flowInChange(value) {
      this.flowOutList = this.lineMap[value].out
      this.flowOut = ''
      this.line = ''
      this.lineList = []
    },
    flowOutChange(value) {
      this.lineList = this.lineMap[this.flowIN].line[value]
      this.line = ''
    },
    PrivateLineSearch() {
      if (this.line != '') {
        let  data = {
          line: this.line,
          time: this.searchTime
        }
        PriveateLineMonitorInfo(data).then((res) => {
          this.lostpkData = res.Data.lineLostData
          this.rrtData = res.Data.lineRttData
          this.$refs.childRef1.updateChartData(res.Data.lineLostData)
          this.$refs.childRef2.updateChartData(res.Data.lineRttData)
          this.grafanaUrl = res.Data.granafaUrl
        })
      } else {
        notification.error({
          message: '请选择写完整信息',
        })
      }
    }
  }
}
</script>

<style scoped>
.ant-card-head {
    background: transparent;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
    color: rgba(0,0,0,.85);
    font-size: 25px;
    font-weight: 500;
    margin-bottom: -1px;
    min-height: 48px;
    padding: 0 24px;
}
</style>
