<template>
  <page-header-wrapper>
    <a-tabs default-active-key="0" tab-position="left">
      <a-tab-pane key="0" tab="Dashboard" force-render type="card">
        <dashboard />
      </a-tab-pane>
      <a-tab-pane key="1" tab="云桌面管理">
        <desktop-info />
      </a-tab-pane>
      <a-tab-pane key="2" tab="用户管理" type="card">
        <desktop-user />
      </a-tab-pane>
      <a-tab-pane key="3" tab="策略管理" type="card">
        <desktop-permission />
      </a-tab-pane>
      <a-tab-pane key="4" tab="会话监控" type="card">
        <desktop-monitor />
      </a-tab-pane>
      <a-tab-pane key="5" tab="操作日志" type="card">
        <desktop-log />
      </a-tab-pane>
    </a-tabs>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { openTinyWin } from '@/utils/window'
import store from '@/store'
import Dashboard from '@/views/desktop/Dashboard.vue'
import DesktopMonitor from '@/views/desktop/DesktopMonitor.vue'
import DesktopInfo from '@/views/desktop/DesktopInfo.vue'
import DesktopUser from '@/views/desktop/DesktopUser.vue'
import DesktopPermission from '@/views/desktop/DesktopPermission.vue'
import DesktopLog from '@/views/desktop/DesktopLog.vue'
import { getDesktopSessionList } from '@/api/desktop/desktop'

const columns = [
  {
    title: '服务器名',
    dataIndex: 'assetName',
  },
  {
    title: '服务器IP',
    dataIndex: 'assetIp',
  },
  {
    title: '创建用户',
    dataIndex: 'creator',
  },
  {
    title: '连接用户',
    dataIndex: 'username',
  },
  {
    title: '连接时间',
    dataIndex: 'connectedTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TableList',
  components: {
    STable,
    Ellipsis,
    DesktopMonitor: DesktopMonitor,
    DesktopInfo: DesktopInfo,
    DesktopUser: DesktopUser,
    DesktopLog: DesktopLog,
    DesktopPermission: DesktopPermission,
    Dashboard: Dashboard,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: { status: 'disconnected' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return getDesktopSessionList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    openWindow(str) {
      openTinyWin(str)
    },
  },
}
</script>
