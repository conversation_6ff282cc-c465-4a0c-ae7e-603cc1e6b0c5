<template>
  <div :class="keyboardClass"></div>
</template>

<script>
import Keyboard from "simple-keyboard"
import "simple-keyboard/build/css/index.css"

export default {
  name: "SimpleKeyboardComb",
  props: {
    keyboardClass: {
      default: "simple-keyboard",
      type: String
    },
    input: {
      type: String
    }
  },
  data: () => ({
    keyboard: null,
    keyboardLayout: {
      default: [
        "Ctrl+A Ctrl+C Ctrl+V Ctrl+X",
        "Ctrl+S Ctrl+Z Ctrl+E Ctrl+W",
        "Ctrl+Shift Win+R Win+E Win+D",
        "Ctrl+Shift+A Ctrl+Shift+T Ctrl+Alt+Del",
        "返回 关闭"
      ],
      shift: [
        "Ctrl+A Ctrl+C Ctrl+V Ctrl+X",
        "Ctrl+S Ctrl+Z Ctrl+E Ctrl+W",
        "Ctrl+Shift Win+R Win+E Win+D",
        "Ctrl+Shift+A Ctrl+Shift+T Ctrl+Alt+Del",
        "返回 关闭"
      ]
    },
    buttonTheme: [
      {
        class: "hg-red",
        buttons: "关闭"
      }
    ],
  }),
  mounted() {
    this.keyboard = new Keyboard(this.keyboardClass, {
      onChange: this.onChange,
      onKeyPress: this.onKeyPress,
      layout: this.keyboardLayout,
      buttonTheme: this.buttonTheme
    })
  },
  methods: {
    onChange(input) {
      this.$emit("onChange", input)
    },
    onKeyPress(button) {
      this.$emit("onKeyPress", button)

      /**
       * If you want to handle the shift and caps lock buttons
       */
      if (button === "{shift}" || button === "{lock}") this.handleShift()
    },
    handleShift() {
      let currentLayout = this.keyboard.options.layoutName
      let shiftToggle = currentLayout === "default" ? "shift" : "default"

      this.keyboard.setOptions({
        layoutName: shiftToggle
      })
    }
  },
  watch: {
    input(input) {
      this.keyboard.setInput(input)
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.hg-red {
  background: rgba(255, 0, 0, 0.7) !important;
  color: white !important;
}
</style>
