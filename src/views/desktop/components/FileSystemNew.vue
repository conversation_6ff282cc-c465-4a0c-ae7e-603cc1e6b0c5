<template>
  <div>
    <a-card>
      <div class="fs-header">
        <div class="fs-header-left">
          <a-input
            v-model:value="this.currentDirectoryInput"/>
        </div>
        <div class="fs-header-right">
          <a-space>
            <div class="fs-header-right-item">
              <a-tooltip title="创建文件夹">
                <tx-button
                  type="primary"
                  size="small"
                  icon="folder-add"
                  @click="() => {
                    this.mkdirVisible= true
                  }"
                  :ghost="true"/>
              </a-tooltip>
            </div>

            <div class="fs-header-right-item" v-if="!this.disableUpload">
              <a-tooltip title="上传文件">
                <tx-button
                  type="primary"
                  size="small"
                  icon="cloud-upload"
                  @click="this.uploadFile"
                  :ghost="true">
                  <a-input
                    type="file"
                    id="file-upload"
                    style="display: none"
                    @change="this.handleUploadFile"
                    multiple/>
                </tx-button>
              </a-tooltip>
            </div>

            <div class="fs-header-right-item" v-if="!this.disableUpload">
              <a-tooltip title="上传文件夹">
                <tx-button
                  type="primary"
                  size="small"
                  icon="upload"
                  @click="this.uploadDir"
                  :ghost="true">
                  <a-input
                    type="file"
                    id="dir-upload"
                    style="display: none"
                    @change="this.handleUploadDir"
                    webkitdirectory=""
                    multiple/>
                </tx-button>
              </a-tooltip>
            </div>

            <div class="fs-header-right-item">
              <a-tooltip title="刷新">
                <tx-button
                  type="primary"
                  size="small"
                  icon="reload"
                  @click="this.refresh"
                  :ghost="true">
                </tx-button>
              </a-tooltip>
            </div>

            <div class="fs-header-right-item">
              <a-tooltip title="批量删除">
                <tx-button
                  type="danger"
                  size="small"
                  :ghost="true"
                  icon="delete"
                  :disabled="hasSelected"
                  :loading="this.delBtnLoading"
                  @click="this.deleteAll"
                >
                </tx-button>

              </a-tooltip>
            </div>
          </a-space>
        </div>
      </div>
      <a-table
        :rowKey="(record) => record.name"
        :columns="columns"
        :data-source="this.files"
        size="small"
        :pagination="false"
        :loading="this.loading"
        :row-selection="rowSelection"
        :customRow="rowClick"
      >
        <template #bodyCell="{column, record}">
        <template v-if="column.dataIndex == 'opt'">
          <div v-if="!disableDownload">
            <a href="javascript:;" v-if="record.isDir || record.isLink" :disabled="true" @click="downloadFile(record)">下载</a>
            <a href="javascript:;" v-else @click="downloadFile(record)">下载</a>
          </div>
          <a href="javascript:;" style="margin-left: 8px" @click="clickRename(record)">重命名</a>
          <a href="javascript:;" style="margin-left: 8px" @click="rmFile(record)">删除</a>
        </template>
        </template>
      </a-table>
    </a-card>
    <a-modal
      title="创建文件夹"
      :visible="mkdirVisible"
      :confirm-loading="this.confirmLoading"
      :okButtonProps="{form: 'mkdir-form', key: 'submit', htmlType: 'submit'}"
      @ok="handleCreateOk"
      @cancel="() => {this.mkdirVisible = false}"
    >
      <a-form-model :model="mkdirForm" ref="mkdirFormRef" id="mkdir-form">
        <a-form-model-item name="dir" :rules="[{required: true, message: '请输入文件夹名称'}]">
          <a-input v-model:value="mkdirForm.name" autoComplete="off" placeholder="请输入文件夹名称"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="重命名"
      :visible="renameVisible"
      :confirm-loading="this.confirmLoading"
      :okButtonProps="{form: 'mkdir-form', key: 'submit', htmlType: 'submit'}"
      @ok="handleRenameOk"
      @cancel="() => {this.renameVisible = false}"
    >
      <a-form-model :model="renameForm" ref="renameFormRef" id="rename-form">
        <a-form-model-item name="dir" :rules="[{required: true, message: '请输入文件夹名称'}]">
          <a-input v-model:value="renameForm.name" autoComplete="off" placeholder="请输入文件夹名称"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
  import { h } from 'vue'
  import { STable, Ellipsis } from '@/components'
  import { desktopFileRename, desktopFileRm, desktopFileMkdir, desktopFileLs } from '@/api/desktop/desktop_storage'
  import request from '@/utils/desktopRequest'
  import axios from 'axios'
  export default {
    name: 'FileSystem',
    props: {
      ip: {
        type: String,
        default: ''
      },
      disableDownload: {
        type: Boolean,
        default: true
      },
      disableUpload: {
        type: Boolean,
        default: true
      },
      username: {
        type: String,
        default: ''
      }
    },
    components: {
      STable,
      Ellipsis
    },
    data () {
      return {
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            customRender: ({ text, record }) => {
              let icon
              let theme = 'outlined'
              if (record.isDir) {
                icon = 'folder-open'
                theme = 'twoTone'
              } else {
                if (record.isLink) {
                  icon = 'link'
                  theme = 'twoTone'
                } else {
                  const fileExtension = record.name.split('.').pop().toLowerCase()
                  switch (fileExtension) {
                    case 'doc':
                    case 'docx':
                      icon = 'file-word'
                      break
                    case 'xls':
                    case 'xlsx':
                      icon = 'file-xlsx'
                      break
                    case 'bmp':
                    case 'jpg':
                    case 'jpeg':
                    case 'png':
                    case 'tif':
                    case 'gif':
                    case 'pcx':
                    case 'tga':
                    case 'exif':
                    case 'svg':
                    case 'psd':
                    case 'ai':
                    case 'webp':
                    case 'md':
                      icon = 'file-markdown'
                      break
                    case 'pdf':
                      icon = 'file-pdf'
                      break
                    case 'txt':
                      icon = 'file-text'
                      break
                    case 'zip':
                    case 'gz':
                    case 'tar':
                    case 'tgz':
                      icon = 'file-zip'
                      break
                    default:
                      icon = 'file'
                      break
                  }
                }
              }
              // TODO，修正图标展示
              // return <span class='dode'><a-icon type={icon} theme={theme} />&nbsp;&nbsp;{record.name}</span>
              return h('span', { className: 'dode' }, record.name)
            },
            sorter: (a, b) => {
              if (a['key'] === '..') {
                return 0
              }

              if (b['key'] === '..') {
                return 0
              }
              return a.name.localeCompare(b.name)
            },
            sortDirections: ['descend', 'ascend']
          },
          {
            title: '大小',
            dataIndex: 'size',
            key: 'size',
            customRender: ({ text, record }) => {
              if (!record.isDir && !record.isLink) {
                // return <span class='dode'>{this.renderSize(record.size)}</span>
                return h('span', { className: 'dode' }, this.renderSize(record.size))
              }
              // return <span class='dode'/>
              return h('span', { className: 'dode' })
            },
            sorter: (a, b) => {
              if (a['key'] === '..') {
                return 0
              }

              if (b['key'] === '..') {
                return 0
              }
              return a.size - b.size
            }
          },
          {
            title: '修改日期',
            dataIndex: 'modTime',
            key: 'modTime',
            sorter: (a, b) => {
              if (a['key'] === '..') {
                return 0
              }

              if (b['key'] === '..') {
                return 0
              }
              return a.modTime.localeCompare(b.modTime)
            },
            sortDirections: ['descend', 'ascend'],
            customRender: ({ text, record }) => {
              // return <span class='dode'>{record.modTime}</span>
              return h('span', { className: 'dode' }, record.modTime)
            }
          },
          {
            title: '属性',
            dataIndex: 'mode',
            key: 'mode',
            customRender: ({ text, record }) => {
              // return <span class='dode'>{record.mode}</span>
              return h('span', { className: 'dode' }, record.mode)
            }
          },
          {
            title: '操作',
            dataIndex: 'opt',
            scopedSlots: { customRender: 'opt' }
          }
        ],
        mkdirVisible: false,
        renameVisible: false,
        assetType: '',
        createVisible: false,
        confirmLoading: false,
        storageType: undefined,
        renameForm: {},
        mkdirForm: {},
        storageId: undefined,
        currentDirectory: '/',
        currentDirectoryInput: '/',
        files: [
          {
            'name': '12.txt',
            'path': '/12.txt',
            'isDir': false,
            'mode': '-rw-------',
            'isLink': false,
            'modTime': '2023-01-29 14:09:14',
            'size': 0
          },
          {
            'name': 'Download',
            'path': '/Download',
            'isDir': true,
            'mode': 'drwx------',
            'isLink': false,
            'modTime': '2023-01-30 16:20:23',
            'size': 6
          },
          {
            'name': 'cfa-2.5.12-premium-x86-release(1).apk',
            'path': '/cfa-2.5.12-premium-x86-release(1).apk',
            'isDir': false,
            'mode': '-rw-r--r--',
            'isLink': false,
            'modTime': '2023-01-29 14:08:43',
            'size': 11419394
          }
        ],
        loading: false,
        currentFileKey: undefined,
        selectedRowKeys: [],
        selectedRows: [],
        uploading: {},
        callback: undefined,
        minHeight: 280,
        upload: false,
        delBtnLoading: false,
        download: false,
        delete: false,
        rename: false,
        edit: false,
        hasSelected: true,
        editorVisible: false,
        fileName: '',
        uploadKey: 'uploadKey',
        fileContent: ''
      }
    },
    created () {
      this.assetType = this.desktopType
      this.loadFiles('/')
    },
    methods: {
      renderSize (value) {
        if (value == null || value === '' || value === 0) {
          return '0 B'
        }
        const unitArr = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
        const srcSize = parseFloat(value)
        const index = Math.floor(Math.log(srcSize) / Math.log(1024))
        let size = srcSize / Math.pow(1024, index)
        size = size.toFixed(2)
        return size + ' ' + unitArr[index]
      },
      downloadFile (row) {
        let url = 'https://guacamole.intsig.net/desktop/download?username=' + this.username + '&ip=' + this.ip + '&file=' + window.encodeURIComponent(row.path)
        // var path = 'wss://guacamole.intsig.net/desktop-monitor/ws'
        if (process.env.NODE_ENV !== 'production') {
          url = 'https://sonar-test.intsig.net/desktop/download?username=' + this.username + '&ip=' + this.ip + '&file=' + window.encodeURIComponent(row.path)
        }
        const aElement = document.createElement('a')
        aElement.setAttribute('download', '')
        // aElement.setAttribute('target', '_blank')
        aElement.setAttribute('href', url)
        aElement.click()
      },
      deleteAll () {
        const rows = this.selectedRows
        const title = '您确定要删除选中的'
        // const content = <div>{title}<span class='delAll' strong > {rows.length} </span>条记录吗？</div>
        const content = h('div', [
          title,
          h('span', { className: 'delAll' }, rows.length),
          '条记录吗？',
        ])
        this.$confirm({
          content: content,
          okType: 'danger',
          onOk: async () => {
            for (let i = 0; i < rows.length; i++) {
              if (rows[i] === '..') {
                continue
              }
              await this.rmFile(rows[i])
            }
            this.refresh()
          },
          onCancel () {

          }
        })
      },
      rowClick: function (record, index) {
        return {
          on: {
            dblclick: () => {
              if (record.isDir || record.isLink) {
                if (record.path === '..') {
                  // 获取当前目录的上级目录
                  const currentDirectory = this.currentDirectory
                  let parentDirectory = currentDirectory.substring(0, currentDirectory.lastIndexOf('/'))
                  if (parentDirectory === '') {
                    this.currentDirectoryInput = '/'
                    this.currentDirectory = '/'
                    parentDirectory = '/'
                  }
                  this.loadFiles(parentDirectory)
                } else {
                  this.loadFiles(record.path)
                }
              } else {

              }
            }
          }
        }
      },
      genProgressTip(name, currentSize, totalSize, percent) {
        return h('div', [
          h('div', name),
          h('div', [
            this.renderSize(currentSize),
            ' / ',
            this.renderSize(totalSize),
          ]),
          h('a-progress', { percent }),
        ])
      },
      handleCreateOk () {
        this.confirmLoading = true
        const data = { 'ip': this.ip, 'username': this.username }
        if (this.currentDirectory === '/') {
          data.dir = '/' + this.mkdirForm['name']
        } else {
          data.dir = this.currentDirectory + '/' + this.mkdirForm['name']
        }

        desktopFileMkdir(data).then(res => {
          if (res['message'] === 'error') {
            this.mkdirVisible = false
            this.confirmLoading = false
            this.$message.error('获取失败,后端接口错误，请联系运维开发排查~')
          } else {
            this.mkdirVisible = false
            this.confirmLoading = false
            this.$message.success('创建成功')
            this.loadFiles(this.currentDirectoryInput)
          }
        })
      },
      clickRename (record) {
        this.renameVisible = true
        this.currentFileKey = record.name
        this.renameForm = record
      },
      handleRenameOk () {
        let currentDirectory = this.currentDirectory
        if (!currentDirectory.endsWith('/')) {
          currentDirectory += '/'
        }
        const data = {
          'ip': this.ip,
          'username': this.username,
          'oldName': currentDirectory + this.currentFileKey,
          'newName': currentDirectory + this.renameForm['name']
        }
        if (data['oldName'] === data['newName']) {
          this.$message.success('重命名成功')
          this.renameVisible = false
        } else {
          this.confirmLoading = true
          desktopFileRename(data).then(res => {
            this.confirmLoading = false
            if (res['message'] === 'error') {
              this.renameVisible = false
              this.$message.error('获取失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.renameVisible = false
              this.$message.success('重命名成功')
              this.loadFiles(this.currentDirectoryInput)
            }
          })
        }
      },
      sortByName  (a, b) {
        const a1 = a['name'].toUpperCase()
        const a2 = b['name'].toUpperCase()
        if (a1 < a2) {
          return -1
        }
        if (a1 > a2) {
          return 1
        }
        return 0
      },
      loadFiles (filePath) {
        this.loading = true
        const formData = { 'ip': this.ip, 'username': this.username }
        if (!filePath) {
          formData.dir = '/'
        } else {
          formData.dir = filePath
        }
        desktopFileLs(formData).then(res => {
          if (res['message'] === 'error') {
            this.loading = false
            this.$message.error('获取失败,后端接口错误，请联系运维开发排查~')
          } else {
            this.loading = false
            this.selectedRowKeys = []
            const data = res['data']
            // 优先文件夹
            const items = data.map(item => {
              return { 'key': item['path'], ...item }
            })
            const dirs = items.filter(item => item['isDir'] === true)
            dirs.sort(this.sortByName)
            const files = items.filter(item => item['isDir'] === false)
            files.sort(this.sortByName)
            dirs.push(...files)
            if (filePath !== '/') {
              dirs.splice(0, 0, { filePath: '..', name: '..', path: '..', isDir: true, disabled: true })
            }
            this.files = dirs
            this.currentDirectory = filePath
            this.currentDirectoryInput = filePath
          }
        })
      },
      refresh () {
        this.loadFiles(this.currentDirectory)
      },
      handleUploadFile () {
        const files = window.document.getElementById('file-upload').files
        let uploadEndCount = 0
        const increaseUploadEndCount = () => {
          uploadEndCount++
          return uploadEndCount
        }
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          if (!file) {
            return
          }
          this.uploadFileToServer(file, this.currentDirectory, () => {
            if (increaseUploadEndCount() === files.length) {
              this.refresh()
            }
          })
        }
      },
      // 自定义上传
      uploadFileToServer (file, dir, callback) {
        const { name, size } = file
        const formData = new FormData()
        formData.append('ip', this.ip)
        formData.append('username', this.username)
        formData.append('dir', dir)
        formData.append('file', file)
        let cancel
        const CancelToken = axios.CancelToken
        let prevPercent = 0; let percent = 0
        request({
          url: `/desktop/upload`,
          method: 'post',
          data: formData,
          cancelToken: new CancelToken(function executor (c) {
            // executor 函数接收一个 cancel 函数作为参数
            cancel = c
          }),
          onUploadProgress: progressEvent => {
            // let description = <div><div>{name}</div><div>{this.renderSize(progressEvent.loaded)} / {this.renderSize(progressEvent.total)}</div><a-progress percent={99}/></div>
            let description = this.genProgressTip(name, progressEvent.loaded, size, percent)
              if (progressEvent.loaded === progressEvent.total) {
                this.$notification.open({
                  key: this.uploadKey,
                  message: `向目标机器传输中...`,
                  duration: null,
                  description: description,
                  placement: 'bottomRight',
                  onClose: () => {
                    cancel()
                    this.$message.info(`您已取消上传"${name}"`, 10)
                  }
                })
                return
              }
              percent = Math.min(Math.floor(progressEvent.loaded * 100 / progressEvent.total), 99)
              if (prevPercent === percent) {
                return
              }
              // description = <div><div>{name}</div><div>{this.renderSize(progressEvent.loaded)} / {this.renderSize(size)}</div><a-progress percent={percent}/></div>
              description = this.genProgressTip(name, progressEvent.loaded, size, percent)
              this.$notification.open({
                key: this.uploadKey,
                message: `上传中...`,
                duration: null,
                description: description,
                placement: 'bottomRight',
                onClose: () => {
                  cancel()
                  this.$message.info(`您已取消上传"${name}"`, 10)
                }
              })
            prevPercent = percent
          }
        }).then(res => {
          if (res['message'] === 'error') {
            this.uploadEnd(false, res['message'], name, size, callback)
          } else {
            this.uploadEnd(true, res['message'], name, size, callback)
          }
        }).catch(err => {
          console.log(err)
          this.uploadEnd(false, '服务器内部错误' + err, name, size, callback)
        })
      },
      uploadEnd (success, message, name, size, callback) {
        if (success) {
          // const description = <div><div>{name}</div><div>{this.renderSize(size)} / {this.renderSize(size)}</div> <a-progress percent={100}/></div>
          const description = this.genProgressTip(name, size, size, 100)
          this.$notification['success']({
            key: this.uploadKey,
            message: '上传成功',
            duration: 5,
            description: description,
            placement: 'bottomRight'
          })
          if (callback) {
            callback()
          }
        } else {
          // const description = <div><div>{name}</div><text>{message}</text></div>
          const description = h('div', [
            h('div', name),
            h('text', message),
          ])
          this.$notification['error']({
            key: this.uploadKey,
            message: '上传失败',
            duration: 10,
            description: description,
            placement: 'bottomRight'
          })
        }
      },
      handleUploadDir () {
        const files = window.document.getElementById('dir-upload').files
        let uploadEndCount = 0
        const increaseUploadEndCount = () => {
          uploadEndCount++
          return uploadEndCount
        }
        for (let i = 0; i < files.length; i++) {
          const relativePath = files[i]['webkitRelativePath']
          const dir = relativePath.substring(0, relativePath.length - files[i].name.length)
          this.uploadFileToServer(files[i], this.currentDirectory + '/' + dir, () => {
            if (increaseUploadEndCount() === files.length) {
              this.refresh()
            }
          })
        }
      },
      uploadDir () {
        window.document.getElementById('dir-upload').click()
      },
      uploadFile () {
        window.document.getElementById('file-upload').click()
      },
      rmFile (record) {
        let currentDirectory = this.currentDirectory
        if (!currentDirectory.endsWith('/')) {
          currentDirectory += '/'
        }
        const data = { 'ip': this.ip, 'username': this.username, 'file': currentDirectory + record.name, 'dir': this.currentDirectoryInput }
        desktopFileRm(data).then(res => {
          if (res['message'] === 'error') {
            this.$message.error('获取失败,后端接口错误，请联系运维开发排查~')
          } else {
            this.$message.success({ content: '删除成功~', key: 'deleteSucess' })
            this.loadFiles(this.currentDirectoryInput)
          }
        })
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        console.log(this.selectedRows)
      }
    },
    watch: {
      'selectedRowKeys' (val) {
        this.hasSelected = val.length <= 0
      }
      },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    }
  }
</script>

<style scoped>
  .dode {
    -webkit-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
  }

  @-webkit-keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  .popup {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    animation-duration: 0.4s;
    background-clip: padding-box;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.75);
    left: 0;
    list-style-type: none;
    margin: 0;
    outline: none;
    padding: 0;
    position: fixed;
    text-align: left;
    top: 0;
    overflow: hidden;
    -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.75);
  }

  .popup li {
    clear: both;
    /*color: rgba(0, 0, 0, 0.65);*/
    cursor: pointer;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    margin: 0;
    padding: 5px 12px;
    transition: all .3s;
    white-space: nowrap;
    -webkit-transition: all .3s;
  }

  .popup li:hover {
    background-color: #e6f7ff;
  }

  .popup li > i {
    margin-right: 8px;
  }

  .fs-header {
    align-items: center;
    position: relative;
    display: flex;
  }

  .fs-header-left{
    flex: 1 1 0;
  }

  .fs-header-right{
    text-align: right;
    margin-left: 10px;
  }

  .fs-header-right-item {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 100%;
  }
  .delAll {
    color: #1890FF
  }
</style>
