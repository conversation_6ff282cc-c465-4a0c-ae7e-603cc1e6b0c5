<template>
  <div class="chartContainer">
    <a-empty style="height: 200px; width: 100%" v-if="!chartData.length" />
    <a-select class="waveSelect" v-else default-value="all" style="width: 120px" @change="handleChange">
      <a-select-option v-for="i in chartData" :value="i.desktopType" :key="i.desktopType">
        {{ this.typeFilter(i.desktopType) }}
      </a-select-option>
    </a-select>
    <div v-if="chartData.length > 0" style="width: 100%" :ref="compRef" :id="compRef"></div>
  </div>
</template>
<script>
import { Line } from '@antv/g2plot'
const typeMap = {
  all: '全部',
  dlp: 'DLP云桌面',
  remote: '远程办公云桌面',
  server: '服务器云桌面',
  personal: '远程工作站',
}
export default {
  props: {
    chartData: {
      type: Array,
      default: () => {
        return [
          {
            desktopType: '',
            desktopTypesData: [
              {
                date: '',
                times: 0,
              },
            ],
          },
        ]
      },
    },
    compRef: {
      type: String,
      default: '',
    },
  },
  async mounted() {
    this.initChart()
  },
  data() {
    return {
      chart: null,
      selectChart: [],
    }
  },
  methods: {
    typeFilter(type) {
      return typeMap[type]
    },
    chartRender() {},
    handleChange(val) {
      this.chartData.forEach(item => {
        if (item.desktopType === val) {
          this.selectChart = item.desktopTypesData
        }
      })
      this.chart.changeData(this.selectChart)
    },
    initChart() {
      if (this.chartData.length) {
        this.selectChart = this.chartData.filter(obj => obj.desktopType === 'all')[0].desktopTypesData
        this.chart = new Line(this.compRef, {
          data: this.selectChart,
          xField: 'date',
          yField: 'times',
          padding: 'auto',
          height: 300,
          smooth: true,
          width: this.$refs[this.compRef].clientWidth,
          color: '#6E5EFC',
          xAxis: {
            label: {
              style: {
                fill: 'black',
                opacity: 1,
                fontSize: 15,
              },
            },
            range: [0, 1],
          },
          yAxis: {
            label: {
              style: {
                fill: 'black',
                opacity: 1,
                fontSize: 15,
              },
            },
          },
        })
        this.chart.render()
      }
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
.waveSelect {
  position: absolute;
  right: 0;
  top: 3%;
}
</style>
