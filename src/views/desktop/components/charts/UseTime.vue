<template>
  <div class="chartContainer">
    <div v-if="this.compRef !== 'userDep'">
      <a-empty style="height: 300px; width: 100%" v-if="!chartData.length" />
      <a-select class="waveSelect" v-else default-value="all" style="width: 120px" @change="handleChange">
        <a-select-option v-for="i in chartData" :value="i.desktopType" :key="i.desktopType">
          {{ this.typeFilter(i.desktopType) }}
        </a-select-option>
      </a-select>
    </div>

    <div v-if="chartData.length > 0" :ref="compRef" :id="compRef"></div>
  </div>
</template>
<script>
// import G2 from '@antv/g2'
import { Column } from '@antv/g2plot'
const typeMap = {
  all: '全部',
  dlp: 'DLP云桌面',
  remote: '远程办公云桌面',
  server: '服务器云桌面',
  personal: '远程工作站',
}
export default {
  props: {
    chartData: {
      type: Array,
      default: () => {
        return [
          {
            desktopType: '',
            desktopTypesData: [],
          },
        ]
      },
    },
    compRef: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      chart: null,
      selectChart: [],
    }
  },
  mounted() {
    if (this.chartData.length) {
      if (this.compRef === 'userDep') {
        this.selectChart = this.chartData
      } else {
        this.selectChart = this.chartData.filter(obj => obj.desktopType === 'all')[0].desktopTypesData
      }
      let color = '#7DAAFF'
      if (this.compRef === 'useTime') {
        color = '#FAE092'
      } else if (this.compRef === 'userDep') {
        color = '#63DAAB'
      }
      this.chart = new Column(this.compRef, {
        data: this.selectChart,
        isGroup: true,
        xField: 'useTitle',
        yField: 'users',
        limitInPlot: false,
        width: this.$refs[this.compRef].clientWidth,
        xAxis: {
          label: {
            style: {
              fill: 'black',
              opacity: 1,
              fontSize: 15,
            },
          },
        },
        color: color,
        yAxis: {
          label: {
            formatter: val => `${String(val).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`,
            style: {
              fill: 'black',
              opacity: 1,
              fontSize: 15,
            },
          },
        },
        geometryOptions: [
          {
            geometry: 'column',
            isGroup: true,
            seriesField: 'describe',
          },
        ],
        legend: {
          itemName: {
            formatter: text => {
              return text
            },
          },
        },
        height: 300,
        minColumnWidth: 30,
        maxColumnWidth: 30,
      })
      this.chart.render()
    }
  },

  methods: {
    typeFilter(type) {
      return typeMap[type]
    },
    chartRender() {},
    handleChange(val) {
      this.chartData.forEach(item => {
        if (item.desktopType === val) {
          this.selectChart = item.desktopTypesData
        }
      })
      this.chart.changeData(this.selectChart)
    },
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
.waveSelect {
  position: absolute;
  right: 0;
  top: 3%;
}
</style>
