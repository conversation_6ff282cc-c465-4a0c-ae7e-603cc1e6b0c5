<template>
  <div class="chartContainer">
    <div :ref="compRef" :id="compRef"></div>
  </div>
</template>
<script>
import { Area } from '@antv/g2plot'
export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    compRef: {
      type: String,
      default: '',
    },
  },
  watch: {},
  mounted() {
    this.selectChart = this.chartData
    this.chart = new Area(this.compRef, {
      data: this.chartData,
      xField: 'month',
      yField: 'cost',
      yAxis: {
        label: {
          style: {
            fill: 'black',
            opacity: 1,
            fontSize: 15,
          },
        },
      },
      xAxis: {
        range: [0, 1],
        label: {
          style: {
            fill: 'black',
            opacity: 1,
            fontSize: 15,
          },
        },
      },
      height: 300,
      smooth: true,
      areaStyle: () => {
        return {
          fill: '#32b3ed',
          fillOpacity: 0.5,
          // stroke: "32b3ed"
        }
      },
    })
    this.chart.render()
  },
  data() {
    return {
      chart: null,
      selectChart: [],
    }
  },
  methods: {
    createChart() {},
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
