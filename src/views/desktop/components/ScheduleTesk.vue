<template>
  <div>
    <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="定时开机">
        <div style="display: flex; width: 300px; justify-content: space-around">
          <p class="handleBox">
            <span class="time">{{ this.formState.onTime }}</span>
            <span class="time_week">{{ onWeekStr }} 重复</span>
          </p>
          <p style="line-height: 70px; width: 80px">
            <a-switch v-model:checked="formState.onChecked" :disabled="!oneditFlag" @change="onCheckedChange" />
            <a v-if="formState.onChecked">
              <a @click="oneditFlag = false" style="margin-left: 8px" v-if="oneditFlag">修改</a>
              <a @click="offconfirm" style="margin-left: 8px" v-else>确认</a>
            </a>
          </p>
        </div>
        <a-form v-if="!oneditFlag" ref="myForm1" :model="formState" name="basic">
          <a-form-item name="onTime" label="执行时间">
            <a-time-picker
              :allowClear="false"
              format="HH:mm"
              :valueFormat="'HH:mm'"
              :bordered="false"
              v-model:value="formState.onTime"
            />
          </a-form-item>
          <a-form-item name="onWeekdays" label="重复周期">
            <a-checkbox-group @change="onWeekChange" v-model:value="formState.onWeekdays" style="width: 100%">
              <a-row>
                <a-col :span="24" v-for="i in weekDays" :key="i.value">
                  <a-checkbox :value="i.value">{{ i.label }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </a-form-item>
      <a-form-item label="定时关机">
        <div style="display: flex; width: 300px; justify-content: space-around">
          <p class="handleBox">
            <span class="time">{{ this.formState.offTime }}</span>
            <span class="time_week">{{ offWeekStr }} 重复</span>
          </p>
          <p style="line-height: 70px; width: 80px">
            <a-switch v-model:checked="formState.offChecked" :disabled="!offeditFlag" @change="offCheckedChange" />
            <a v-if="formState.offChecked">
              <a @click="offeditFlag = false" style="margin-left: 8px" v-if="offeditFlag">修改</a>
              <a @click="offconfirm" style="margin-left: 8px" v-else>确认</a>
            </a>
          </p>
        </div>
        <a-form v-if="!offeditFlag" ref="myForm" :model="formState" name="basic">
          <a-form-item name="offTime" label="执行时间">
            <a-time-picker
              :allowClear="false"
              format="HH:mm"
              :valueFormat="'HH:mm'"
              @change="timeChange"
              :bordered="false"
              v-model:value="formState.offTime"
            />
          </a-form-item>
          <a-form-item name="offWeekdays" label="重复周期">
            <a-checkbox-group @change="offWeekChange" v-model:value="formState.offWeekdays" style="width: 100%">
              <a-row>
                <a-col :span="24" v-for="i in weekDays" :key="i.value">
                  <a-checkbox :value="i.value">{{ i.label }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { notification } from 'ant-design-vue'
import { getTeskInfo, updateCron } from '@/api/desktop/desktop'
import { defineComponent, reactive, ref } from 'vue'
import { weekDays } from './mockData'
export default defineComponent({
  props: {
    teskData: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  setup() {
    const offeditFlag = ref(true)
    const oneditFlag = ref(true)

    const formState = reactive({
      offChecked: false,
      offTime: null,
      offWeekdays: [],

      onChecked: false,
      onTime: null,
      onWeekdays: [],
    })
    const onFinish = values => {}
    const onFinishFailed = errorInfo => {}

    return {
      onFinish,
      onFinishFailed,
      activeKey: ref('turnOff'),
      formState,
      weekDays,
      offeditFlag,
      oneditFlag,
      offWeekStr: '',
      onWeekStr: '',
    }
  },
  mounted() {
    console.log(this.teskData, 'teskDatateskData')
    if (this.teskData && this.teskData.cronStatus) {
      switch (this.teskData.cronStatus) {
        case 1:
          this.formState.offChecked = true
          this.formState.onChecked = true
          break
        case 2:
          this.formState.offChecked = false
          this.formState.onChecked = false
          break
        case 3:
          this.formState.offChecked = true
          this.formState.onChecked = false
          break
        case 4:
          this.formState.offChecked = false
          this.formState.onChecked = true
          break
      }
      this.initPage()
      // if (!this.formState.offChecked) {
      //   this.formState.offTime = '00:00'
      //   this.formState.offWeekdays = ['1']
      //   this.offWeekStr = this.weekHandle(this.formState.offWeekdays)
      // } else if (!this.formState.onChecked) {
      //   this.formState.onTime = '00:00'
      //   this.formState.onWeekdays = ['1']
      //   this.onWeekStr = this.weekHandle(this.formState.onWeekdays)
      // }
    } else {
      return false
    }
  },
  methods: {
    initPage() {
      getTeskInfo({
        desktopIdKey: this.teskData.id,
        desktopId: this.teskData.desktopId,
        desktopIdc: this.teskData.desktopIdc,
      }).then(res => {
        console.log(res, 'ressss')
        res.Data.offTime ? (this.formState.offTime = res.Data.offTime) : (this.formState.offTime = '18:00')
        res.Data.offWeekdays
          ? (this.formState.offWeekdays = res.Data.offWeekdays)
          : (this.formState.offWeekdays = ['1'])
        this.offWeekStr = this.weekHandle(this.formState.offWeekdays)
        res.Data.onTime ? (this.formState.onTime = res.Data.onTime) : (this.formState.onTime = '09:00')
        res.Data.onWeekdays ? (this.formState.onWeekdays = res.Data.onWeekdays) : (this.formState.onWeekdays = ['1'])
        this.onWeekStr = this.weekHandle(this.formState.onWeekdays)
      })
    },
    offCheckedChange(val) {
      console.log(val, 'vallll')
      switch (val) {
        case true:
          // this.formState.offTime = '00:00'
          // this.formState.offWeekdays = ['1']
          // this.offWeekStr =  this.weekHandle(this.formState.offWeekdays)
          this.submitForm()
          break
        case false:
          this.offeditFlag = true
          this.submitForm()
          break
      }
    },
    onCheckedChange(val) {
      console.log(val, 'vallll')
      switch (val) {
        case true:
          // this.formState.onTime = '09:00'
          // this.formState.onWeekdays = ['1']
          this.submitForm()
          break
        case false:
          this.oneditFlag = true
          this.submitForm()
          break
      }
    },
    timeChange() {
      console.log(this.formState, 'formStateformState')
    },
    async offconfirm() {
      await this.submitForm()
      // this.offWeekStr = this.weekHandle(this.formState.offWeekdays)
      // this.onWeekStr = this.weekHandle(this.formState.onWeekdays)
      // this.offeditFlag = true
    },
    // async onconfirm() {
    //   await this.submitForm()
    //   this.onWeekStr = this.weekHandle(this.formState.onWeekdays)
    //   // this.offeditFlag = true
    // },
    offWeekChange(val) {
      console.log(val, 'val')
      if (!val.length) {
        notification.warning({
          message: '至少选择一天重复',
        })
        this.formState.offWeekdays = ['1']
      }
    },
    onWeekChange(val) {
      console.log(val, 'val')
      if (!val.length) {
        notification.warning({
          message: '至少选择一天重复',
        })
        this.formState.onWeekdays = ['1']
      }
    },
    onEdit() {
      this.offeditFlag = false
    },
    weekHandle(arr) {
      const dict = {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        0: '周日',
      }
      let str = ''
      if (arr.length === 7) {
        str = '每天'
        return str
      } else if (arr.length) {
        str = ''
        for (let i = 0; i < arr.length; i++) {
          str = str + ' ' + dict[arr[i]]
        }
        return str
      } else {
        str = '无'
        return str
      }
    },

    cancel() {
      this.$emit('handleCancel')
    },
    submitForm() {
      this.offWeekStr = this.weekHandle(this.formState.offWeekdays)
      this.onWeekStr = this.weekHandle(this.formState.onWeekdays)
      let cronStatus = 1
      if (this.formState.offChecked && this.formState.onChecked) {
        cronStatus = 1
      } else if (!this.formState.offChecked && !this.formState.onChecked) {
        cronStatus = 2
      } else if (this.formState.offChecked && !this.formState.onChecked) {
        cronStatus = 3
      } else if (!this.formState.offChecked && this.formState.onChecked) {
        cronStatus = 4
      }
      updateCron({
        desktopId: this.teskData.desktopId,
        cronStatus: cronStatus,
        offTime: this.formState.offTime,
        offWeekdays: this.formState.offWeekdays,
        onTime: this.formState.onTime,
        onWeekdays: this.formState.onWeekdays,
        desktopIdKey: this.teskData.id,
        desktopIdc: this.teskData.desktopIdc,
      })
        .then(res => {
          console.log(res, 'resssss')
          notification.success({
            message: '更新成功',
          })
          // this.$emit('handleOk')
        })
        .catch(err => {
          notification.error({
            message: '更新失败',
          })
          console.log(err, 'err')
        })
        .finally(() => {
          this.offeditFlag = true
          this.oneditFlag = true
          this.$emit('handleOk')
        })
    },
  },
})
</script>

<style lang="less" scoped>
.handleBox {
  // display: inline-block;
  width: 200px;
  display: flex;
  flex-direction: column;
  .time {
    display: inline-block;
    text-align: center;
    font-size: 32px;
  }
  .time_week {
    text-align: center;
    display: inline-block;
    font-size: 10px;
  }
}
</style>
