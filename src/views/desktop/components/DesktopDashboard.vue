<template>
  <div>
    <div style="display: flex">
      <p style="vertical-align: middle;font-size: 20px;font-weight: bold">月份选择：</p>
      <a-select :default-value="month" style="width: 120px" @change="handleChange">
        <a-select-option v-for="i in this.monthList" :value="i" :key="i">
          {{ i }}
        </a-select-option>
      </a-select>
    </div>

    <a-row :gutter="24" style="margin-bottom: 15px;margin-right: 40px" class="cardcs">
      <a-col :span="6">
        <a-card title="用户总数" :bordered="false">
          <a-skeleton v-if="loading" />
          <p v-else style="font-size: 40px">{{ this.userTotal }}<p style="font-size: 20px;display: inline">人</p></p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card title="本月活跃用户数" :bordered="false">
          <a-skeleton v-if="loading" />
          <p v-else style="font-size: 40px">{{ this.activeUserTotal }}<p style="font-size: 20px;display: inline">人</p></p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card title="本月用户使用时长" :bordered="false">
          <a-skeleton v-if="loading" />
          <p v-else style="font-size: 40px">{{ this.useTimeTotal }}<p style="font-size: 20px;display: inline">小时</p></p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card title="本月成本" :bordered="false">
          <a-skeleton v-if="loading" />
          <p v-else style="font-size: 40px">{{ this.useCostTotal }}<p style="font-size: 20px;display: inline">元</p></p>
        </a-card>
      </a-col>
    </a-row>
    <a-row style="margin-bottom: 20px;height: 50%;margin-right: 40px" type="flex" justify="space-between" align="bottom">
      <a-col :span="24" >
        <a-card :loading="loading" title="成本使用趋势">
          <a-skeleton v-if="loading" />
          <costChart v-else ref="desktopCost" :compRef="'desktopCost'" :chartData="desktopCostData" />
        </a-card>
      </a-col>
    </a-row>
    <a-row style="margin-bottom: 15px;height: 50%;margin-right: 40px" type="flex" justify="space-between" align="bottom">
      <a-col :span="24" >
        <a-card :loading="loading" title="近60天连接用户趋势">
          <a-skeleton v-if="loading" />
          <userConnect ref="desktopUserConnect" v-else :compRef="'desktopUserConnect'" :chartData="desktopUserConnectData" />
        </a-card>
      </a-col>
    </a-row>
    <a-row style="margin-bottom: 15px;height: 50%;margin-right: 40px" type="flex" justify="space-between" align="bottom">
      <a-col :span="12" >
        <a-card :loading="loading" title="用户连接时长分布" :bordered="false">
          <a-skeleton v-if="loading" />
          <useTime ref="useTime" :compRef="'useTime'" :chartData="useTimeData" />
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card :loading="loading" title="用户单次连接时长分布" :bordered="false">
          <a-skeleton v-if="loading" />
          <useTime ref="onceUseTime" :compRef="'onceUseTime'" :chartData="onceUseTimeData" />
        </a-card>
      </a-col>
    </a-row>
    <a-row style="margin-bottom: 15px;height: 50%;margin-right: 40px" type="flex" justify="space-between" align="bottom">
      <a-col :span="24" >
        <a-card :loading="loading" title="事业部分布">
          <a-skeleton v-if="loading" />
          <useTime :compRef="'userDep'" :chartData="userDepData" />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import costChart from '@/views/desktop/components/charts/Cost.vue'
import userConnect from '@/views/desktop/components/charts/UserConnect.vue'
import useTime from '@/views/desktop/components/charts/UseTime.vue'
import { GetDesktopDashboard } from '@/api/desktop/desktop'
export default {
  components: {
    costChart,
    userConnect,
    useTime,
  },
  name: 'FileSystem',
  props: {
    desktopType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      userTotal: 0,
      activeUserTotal: 0,
      useTimeTotal: 0,
      useCostTotal: 0,
      month: '',
      desktopCostData: [],
      desktopUserConnectData: [],
      useTimeData: [],
      onceUseTimeData: [],
      userDepData: [],
      monthList: []
    }
  },
  methods: {
    getMonthList() {
      const now = new Date()
      for (let i = 0; i < 5; i++) {
        let month = now.getMonth() - i

        const year = now.getFullYear()
        if (month < 0) {
          this.monthList.push((year - 1) + '-' + (12 + month + 1))
        } else {
          month = month + 1
          if (month < 10) {
            month = '0' + month
          }
          this.monthList.push(year + '-' + (month))
        }
      }
    },
    getMonth() {
      // 获取当前日期
      const date = new Date()
      // 设置为上一个月
      date.setMonth(date.getMonth() - 1)
      // 格式化日期为年-月
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      if (month < 10) {
        month = '0' + month
      }
      this.month = `${year}-${month}`
    },
    getDesktopDashboard() {
      this.loading = true
      GetDesktopDashboard({ month: this.month }).then(res => {
        this.desktopCostData = res.Data.desktopCostData
        this.desktopUserConnectData = res.Data.desktopUserConnectData
        this.useTimeData = res.Data.useTimeData
        this.onceUseTimeData = res.Data.onceUseTimeData
        this.userDepData = res.Data.userDepData
        this.activeUserTotal = res.Data.activeUserTotal
        this.useTimeTotal = res.Data.useTimeTotal
        this.userTotal = this.userDepData.reduce((acc, curr) => {
          return acc + curr.users
        }, 0)
        this.desktopCostData.forEach(item => {
          if (item.month === this.month) {
            this.useCostTotal = item.cost
          }
        })
        this.loading = false
      })
    },
    handleChange(val) {
      this.month = val
      this.getDesktopDashboard()
      const defaultType = "all"
      this.$refs["desktopCost"].chart.chart.changeData(this.desktopCostData)


      let desktopUserConnectNewData = []
      this.desktopUserConnectData.forEach(item => {
        if (item.desktopType === defaultType) {
          desktopUserConnectNewData = item.desktopTypesData
        }
      })
      console.log("desktopUserConnect REF")
      console.log(this.$refs["desktopUserConnect"])
      console.log("useTime REF")
      console.log(this.$refs["useTime"])
      this.$refs["desktopUserConnect"].chart.chart.changeData(desktopUserConnectNewData)

      let useTimeNewData = []
      this.useTimeData.forEach(item => {
        if (item.desktopType === defaultType) {
          useTimeNewData = item.desktopTypesData
        }
      })
      this.$refs["useTime"].chart.chart.changeData(useTimeNewData)

      let onceUseTimeNewData = []
      this.onceUseTimeData.forEach(item => {
        if (item.desktopType === defaultType) {
          onceUseTimeNewData = item.desktopTypesData
        }
      })
      this.$refs["onceUseTime"].chart.chart.changeData(onceUseTimeNewData)
    }
  },
  created() {
    this.getMonth()
  },
  mounted() {
    this.getMonthList()
    this.getDesktopDashboard()
    console.log(this.month)

    console.log(this.monthList)
  }
}
</script>

<style scoped lang="less">
.cardcs {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
</style>
