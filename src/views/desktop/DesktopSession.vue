<template>
  <div ref="ff" style="width: 100%; height: 100%">
    <div class="displayLoading" :style="{ 'z-index': index }">
      <a-icon type="loading" style="font-size: 34px; color: white; position: absolute" />
      <!--      <tx-button class="ant-btn" style="color: white; fontWeight: bold; cursor: pointer;background-color: black; type="link" size="large" :loading="true"></tx-button>-->
    </div>
    <a-input ref="input" type="text" :disabled="applyKStatus" style="position: absolute; top: -9999px" />
    <vue-draggable-resizable
      :w="10"
      :h="10"
      :x="x"
      :y="y"
      :axis="'both'"
      :grid="[1, 1]"
      class="ctrlButton"
      :onDrag="onDragCallback"
      @dragging="onDragStartCallback"
      @dragstop="onDragStopCallback"
      :style="{transition: drapTransition}"
    >
      <a-tooltip placement="topLeft" title="设置" arrow-point-at-center>
        <a-dropdown :trigger="['click']" :visible="visible" @visible-change="handleVisibleChange">
          <template v-if="!this.isOnDrag" #overlay>
            <a-menu>
              <a-menu-item key="fullScreen" @click="fullScreen">全屏</a-menu-item>
              <a-menu-item key="losslessState">
                <a-dropdown>
                  <span @click="disableVisibleStop">画面压缩</span>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.force_lossless === 'false' ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeLossless('false')">默认</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.force_lossless === 'true' ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeLossless('true')">无损压缩</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-menu-item>
              <a-menu-item key="showFileSystem" @click="showFileSystem">文件管理</a-menu-item>
              <a-menu-item key="ChooseDpi">
                <a-dropdown>
                  <span @click="disableVisibleStop">DPI分辨率</span>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 72 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(72)">72</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 96 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(96)">96</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 120 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(120)">120</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 144 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(144)">144</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 160 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(160)">160</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 192 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(192)">192</a>
                      </a-menu-item>
                      <a-menu-item
                        :style="{ backgroundColor: this.query.screen_dpi === 240 ? '#d7effb' : 'transparent' }"
                      >
                        <a @click="changeDpi(240)">240</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-menu-item>
              <a-menu-item key="ChooseMouse">
                <a-dropdown>
                  <span @click="disableVisibleStop">鼠标选项</span>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item :style="{ backgroundColor: this.cursorState === true ? '#d7effb' : 'transparent' }">
                        <a @click="changeCursor(true)">使用自身鼠标</a>
                      </a-menu-item>
                      <a-menu-item :style="{ backgroundColor: this.cursorState === false ? '#d7effb' : 'transparent' }">
                        <a @click="changeCursor(false)">使用云桌面内鼠标</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-menu-item>
              <a-menu-item key="OpenKey" @click="toggleKeyboard">打开虚拟键盘</a-menu-item>
              <a-menu-item key="ChooseTouch">
                <a-dropdown>
                  <span @click="disableVisibleStop">移动端触摸选项</span>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item :style="{ backgroundColor: this.useScreen === false ? '#d7effb' : 'transparent' }">
                        <a @click="changeTouch(false)">使用触摸板</a>
                      </a-menu-item>
                      <a-menu-item :style="{ backgroundColor: this.useScreen === true ? '#d7effb' : 'transparent' }">
                        <a @click="changeTouch(true)">使用触摸屏</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-menu-item>
              <a-menu-item key="WindowsShortcut">
                <a-dropdown>
                  <span @click="disableVisibleStop">windows快捷键</span>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="Ctrl+A" @click="sendCombinationKey(['65507', '97'])">
                        Ctrl+A
                      </a-menu-item>
                      <a-menu-item key="Ctrl+C" @click="sendCombinationKey(['65507', '99'])">
                        Ctrl+C
                      </a-menu-item>
                      <a-menu-item key="Ctrl+D" @click="sendCombinationKey(['65507', '100'])">
                        Ctrl+D
                      </a-menu-item>
                      <a-menu-item key="Ctrl+V" @click="sendCombinationKey(['65507', '118'])">
                        Ctrl+V
                      </a-menu-item>
                      <a-menu-item key="Ctrl+X" @click="sendCombinationKey(['65507', '120'])">
                        Ctrl+X
                      </a-menu-item>
                      <a-menu-item key="Ctrl+S" @click="sendCombinationKey(['65507', '115'])">
                        Ctrl+S
                      </a-menu-item>
                      <a-menu-item key="Ctrl+Z" @click="sendCombinationKey(['65507', '122'])">
                        Ctrl+Z
                      </a-menu-item>
                      <a-menu-item key="Ctrl+Alt+Delete" @click="sendCombinationKey(['65507', '65513', '65535'])">
                        Ctrl+Alt+Delete
                      </a-menu-item>
                      <a-menu-item key="Ctrl+Alt+Backspace" @click="sendCombinationKey(['65507', '65513', '65288'])">
                        Ctrl+Alt+Backspace
                      </a-menu-item>
                      <a-menu-item key="Ctrl+Shift+Esc" @click="sendCombinationKey(['65507', '65505', '65307'])">
                        Ctrl+Shift+Esc
                      </a-menu-item>
                      <a-menu-item key="Win+Shift+S" @click="sendCombinationKey(['65515', '65505', '115'])">
                        Win+Shift+S
                      </a-menu-item>
                      <a-menu-item key="Win+D" @click="sendCombinationKey(['65515', '100'])">Win+D</a-menu-item>
                      <a-menu-item key="Win+E" @click="sendCombinationKey(['65515', '101'])">Win+E</a-menu-item>
                      <a-menu-item key="Win+R" @click="sendCombinationKey(['65515', '114'])">Win+R</a-menu-item>
                      <a-menu-item key="Win+X" @click="sendCombinationKey(['65515', '120'])">Win+X</a-menu-item>
                      <a-menu-item key="Win" @click="sendCombinationKey(['65515'])">Win</a-menu-item>
                      <a-menu-item key="Esc" @click="sendCombinationKey(['65307'])">Esc</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-menu-item>
            </a-menu>
          </template>
          <tx-button
            id="ctrlButton"
            icon="setting"
            @click="this.visible = true"
            @mouseover="onMouseOver"
            @mouseleave="onMouseLeave"
            style="border-radius:50%;width: 400%;height: 400%"
            :style="{opacity: buttonOpacity }"
          />
        </a-dropdown>
      </a-tooltip>
    </vue-draggable-resizable>
    <!--    <a-tag class="custom-popover top" closable :visible="tagVisible" @close="closeTag" >更多设置</a-tag>-->

    <!--    <div style="font-size: 34px;color: white;position:fixed;">-->
    <!--      <a-icon type="fullscreen" style="font-size: 34px;color: white;position: absolute;"/>-->
    <!--    </div>-->
    <!--    虚拟键盘-->
    <vue-drag-resize-rotate
      :rotatable="true"
      :resizable="true"
      :draggable="true"
      :r="angle"
      :w="500"
      :h="230"
      class="ctrlKeyboard"
      :style="{ 'z-index': keyboardIndex}"
    >
      <simple-keyboard v-if="this.isKeyboardVisible" @onKeyPress="onKeyPress" :input="input" :style="keyboardStyle"/>
      <simple-keyboard-comb v-if="this.isKeyboardCombVisible" @onKeyPress="onKeyPressComb" :input="inputComb" :style="keyboardStyle"/>

    </vue-drag-resize-rotate>

    <div class="guacamole-container" ref="container" @click="cancelDrag">

      <div ref="viewport" class="viewport" :style="{ cursor: showCursor }">
        <!-- tabindex allows for div to be focused -->
        <div ref="display" id="display" class="display" tabindex="0"></div>
      </div>
    </div>
    <a-drawer
      title="文件管理"
      placement="right"
      width="80%"
      :closable="true"
      :visible="fileSystemVisible"
      @close="onClose"
    >
      <file-system
        :ip="this.query.asset_host"
        :desktopType="this.query.asset_type"
        :disableUpload="this.disableUpload"
        :disableDownload="this.disableDownload"
        :username="this.query.asset_user + '_rdp'"
      />
    </a-drawer>
  </div>
</template>

<script>
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { getPermission } from '@/api/desktop/desktop'
import { getDesktopPermissionByIp } from '@/api/desktop/desktop_permission'
import { checkNetwork } from '@/api/desktop/desktop_storage'
import { CheckOutlined } from '@ant-design/icons-vue'
import { getInfo } from '@/api/login'
import { judgeEnv } from '@/utils/util'
import Guacamole from 'guacamole-common-js'
import states from '@/utils/states'
// import VueDraggableResizable from 'vue-draggable-resizable'
import VueDraggableResizable from 'vue-draggable-resizable/src/components/vue-draggable-resizable.vue'
// import { STable } from '@/components'
import _ from 'lodash'
import clipboard from '@/utils/clipboard'
import { vendorLoad } from '@/utils/vendorLoader'
import FileSystem from '@/views/desktop/components/FileSystem.vue'
import { getAccessToken } from '@/utils/auth'
import { getCloudTermKey } from '@/config'
import CryptoJS from 'crypto-js'
// const worker = new Worker('./setIntervalWorker.js')
import SimpleKeyboard from '@/views/desktop/components/SimpleKeyboard.vue'
import SimpleKeyboardComb from '@/views/desktop/components/CombSimpleKeyboard.vue'
// import 'vue-draggable-resizable/src/components/vue-draggable-resizable.css'
import VueDragResizeRotate from '@gausszhou/vue3-drag-resize-rotate'
import '@gausszhou/vue3-drag-resize-rotate/lib/bundle.esm.css'

const key = 'updatable'

let Base64

vendorLoad('js-base64').then(res => {
  Base64 = res
})

export default {
  name: 'GuacClient',
  components: {
    FileSystem,
    VueDraggableResizable,
    SimpleKeyboard,
    SimpleKeyboardComb,
    VueDragResizeRotate
  },
  data() {
    return {
      drapTransition: 'all 0.3s ease-out',
      borderRadius: '50%',
      buttonOpacity: 0.5,
      // 移动键盘
      angle: 0,
      keyboardIndex: -100,
      isKeyboardVisible: false,
      isOutLand: false,
      isKeyboardCombVisible: false,
      isProcessingKeyPress: false,
      tagVisible: true,
      keyboardStyle: {
        transform: this.none
      },
      // 翻转
      isLandscape: true,
      input: '',
      inputComb: '',
      isMobile: false,
      demo: 'ubuntu',
      isOnDrag: false,
      applyKStatus: false,
      visible: false,
      disableCopy: true,
      disableUpload: true,
      worker: null,
      keys: {},
      //
      cursorState: false,
      disableDownload: true,
      disablePaste: true,
      recording: true,
      enablePrinting: false,
      x: 0,
      y: 0,
      data: [{ hostname: 'test', ip: '**************', status: '可连接' }],
      userInfo: {
        name: '',
        email: '',
      },
      connected: false,
      showCursor: 'none',
      display: null,
      key: key,
      currentAdjustedHeight: null,
      client: null,
      keyboard: null,
      mouse: null,
      sink: null,
      touch: null,
      lastEvent: null,
      fullScreened: false,
      displayStatus: true,
      fullScreenStatus: false,
      fileSystemVisible: false,
      useScreen: false,
      fullScreenIcon: 'fullscreen',
      fullTitle: '全屏',
      index: 1,
      width: window.innerWidth,
      height: window.innerHeight,
      connectionState: states.IDLE,
      errorMessage: '',
      arguments: {},
      query: {
        asset_user: '',
        session_id: '',
        asset_host: '',
        asset_idc: '',
        asset_name: '',
        email: '',
        screen_width: window.innerWidth,
        screen_height: window.innerHeight,
        screen_dpi: 96,
        force_lossless: 'false',
        token: '',
      },
    }
  },
  computed: {
    wsUrl() {
      const loc = window.location
      let newUri
      if (loc.protocol === 'https:') {
        newUri = 'wss:'
      } else {
        newUri = 'ws:'
      }
      // newUri += '//127.0.0.1:8006'
      newUri += '//' + loc.host
      newUri += '/api/desktop/ws'
      return `${newUri}`
    },
  },
  watch: {},
  methods: {
    closeTag() {
      this.tagVisible = false
    },
    onMouseOver() {
      this.tagVisible = false
      const buttonSize = 40
      const halfButtonSize = buttonSize / 2
      const beginY = 20
      const halfWidth = window.innerWidth / 2
      // 角落
      if (this.x === 0 - halfWidth - halfButtonSize && this.y === -halfButtonSize + beginY) {
        // 左上、.
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth
        this.y = beginY
        this.buttonOpacity = 1
        return
      } else if (this.x === 0 - halfWidth - halfButtonSize && this.y === window.innerHeight - buttonSize + halfButtonSize + beginY) {
        // 左下
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth
        this.y = window.innerHeight - buttonSize  + beginY
        this.buttonOpacity = 1
        return
      } else if (this.x === halfWidth - buttonSize + halfButtonSize && this.y === -halfButtonSize + beginY) {
        // 右上
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize
        this.y = beginY
        this.buttonOpacity = 1
        return
      } else if (this.x === halfWidth - buttonSize + halfButtonSize && this.y === window.innerHeight - buttonSize + halfButtonSize + beginY) {
        // 右下
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize
        this.y = window.innerHeight - buttonSize + beginY
        this.buttonOpacity =  1
        return
      }
      if (this.x === 0 - halfWidth - halfButtonSize) {
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth
        this.buttonOpacity = 1
        return
      }
      if (this.x === halfWidth - buttonSize + halfButtonSize) {
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize
        this.buttonOpacity = 1
        return
      }
      if (this.y === -halfButtonSize + beginY) {
        this.drapTransition = 'all 0.3s ease-out'
        this.y = beginY
        this.buttonOpacity = 1
        return
      }
      if (this.y === window.innerHeight - buttonSize + halfButtonSize + beginY) {
        this.drapTransition = 'all 0.3s ease-out'
        this.y = window.innerHeight - buttonSize + beginY
        this.buttonOpacity = 1
      }
    },
    onMouseLeave() {
      const buttonSize = 40
      const halfButtonSize = 40 / 2
      const beginY = 20
      const halfWidth = window.innerWidth / 2
      this.drapTransition = 'none'
      // 角落
      if (this.x === 0 - halfWidth  && this.y === beginY) {
        // 左上、
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth - halfButtonSize
        this.y = -halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      } else if (this.x === 0 - halfWidth && this.y === window.innerHeight - buttonSize + beginY) {
        // 左下
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth - halfButtonSize
        this.y = window.innerHeight - buttonSize + halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      } else if (this.x === halfWidth - buttonSize && this.y === beginY) {
        // 右上
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize + halfButtonSize
        this.y =  -halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      } else if (this.x === halfWidth - buttonSize && this.y === window.innerHeight - buttonSize  + beginY) {
        // 右下
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize + halfButtonSize
        this.y = window.innerHeight - buttonSize + halfButtonSize + beginY
        this.buttonOpacity =  0.5
        return
      }

      if (this.x === 0 - halfWidth) {
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth - halfButtonSize
        this.buttonOpacity = 0.5
        return
      }
      if (this.x === halfWidth - buttonSize) {
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize + halfButtonSize
        this.buttonOpacity = 0.5
        return
      }
      if (this.y === beginY) {
        this.drapTransition = 'all 0.3s ease-out'
        this.y =  -halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      }
      if (this.y === window.innerHeight - buttonSize + beginY) {
        this.drapTransition = 'all 0.3s ease-out'
        this.y = window.innerHeight - buttonSize + halfButtonSize + beginY
        this.buttonOpacity = 0.5
      }
    },
    cancelDrag() {
      this.visible = false
    },
    onDragCallback(left, top) {
      // 初始为顶上
      // 10 * 400%
      const buttonSize = 40
      // 初始y
      const beginY = 20
      const halfWidth = window.innerWidth / 2
      if (left < (0 - halfWidth - beginY) || left > (halfWidth + beginY - buttonSize)) {
        return false
      }
      if (top < 0  || top > window.innerHeight) {
        return false
      }
    },
    onDragStartCallback(left, top) {
      this.y = top
      this.x = left
      this.isOnDrag = true
    },
    onDragStopCallback(left, top) {
      setTimeout(() => {
        this.isOnDrag = false
      }, 0)
      this.drapTransition = 'none'
      // 10 * 400%
      const buttonSize = 40
      const beginY = 20
      const halfButtonSize = buttonSize / 2
      const snapMargin = 8
      const halfWidth = window.innerWidth / 2
      // 角落
      if (left < (0 - halfWidth + snapMargin) && top < snapMargin + beginY) {
        // 左上
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth - halfButtonSize
        this.y = -halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      } else if (left < (0 - halfWidth + snapMargin) && top > window.innerHeight - buttonSize - snapMargin + beginY) {
        // 左下
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth - halfButtonSize
        this.y = window.innerHeight - buttonSize + halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      } else if (left > halfWidth - buttonSize - snapMargin && top < snapMargin + beginY) {
        // 右上
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize + halfButtonSize
        this.y = -halfButtonSize + beginY
        this.buttonOpacity = 0.5
        return
      } else if (left > halfWidth - buttonSize - snapMargin && top > window.innerHeight - buttonSize - snapMargin + beginY) {
        // 右下
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize + halfButtonSize
        this.y = window.innerHeight - buttonSize + halfButtonSize + beginY
        this.buttonOpacity =  0.5
        return
      }
      if (left < (0 - halfWidth + snapMargin)) {
        this.drapTransition = 'all 0.3s ease-out'
        this.x = 0 - halfWidth - halfButtonSize
        this.buttonOpacity = 0.5
        return
      } else if (left > halfWidth - buttonSize - snapMargin) {
        this.drapTransition = 'all 0.3s ease-out'
        this.x = halfWidth - buttonSize + halfButtonSize
        this.buttonOpacity = 0.5
        return
      } else {
        this.x = left
        this.buttonOpacity = 1
      }
      if (top < snapMargin + beginY) {
        this.drapTransition = 'all 0.3s ease-out'
        this.y = -halfButtonSize + beginY
        this.buttonOpacity = 0.5
      } else if (top > window.innerHeight - buttonSize - snapMargin + beginY) {
        this.drapTransition = 'all 0.3s ease-out'
        this.y = window.innerHeight - buttonSize + halfButtonSize + beginY
        this.buttonOpacity = 0.5
      } else {
        this.y = top
        this.buttonOpacity = 1
      }
    },
    test() {
      this.visible = false
      this.applyKStatus = false
      const input = this.$refs.input
      input.focus()
      this.applyKStatus = true
      // input.blur()
    },
    // 发送组合键
    sendCombinationKey(keys) {
      if (!this.client) {
        return
      }
      for (let i = 0; i < keys.length; i++) {
        this.client.sendKeyEvent(1, keys[i])
      }
      for (let j = 0; j < keys.length; j++) {
        this.client.sendKeyEvent(0, keys[j])
      }
      this.$message.success('发送组合键成功')
      this.visible = false
    },
    onClose() {
      this.fileSystemVisible = false
    },
    showFileSystem() {
      if (this.isOnDrag) {
        return
      }
      this.fileSystemVisible = true
    },
    disableVisibleStop() {
      this.visible = true
    },
    handleVisibleChange(visible) {
      if (!visible) {
        // 重置下拉菜单状态
        this.visible = false
      }
    },
    getUserInfo() {
      this.isLandscape = window.innerWidth > window.innerHeight
      this.isMobile = /Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator.userAgent)
      getInfo().then(response => {
        this.userInfo.email = response.Data.email
        this.userInfo.name = response.Data.name
        this.query.asset_user = response.Data.email.split('@')[0]
        this.query.email = response.Data.email
        console.log('是否手机登陆:', this.isMobile)
        if (this.isMobile) {
          this.query.screen_dpi = 72
        }
        if (response.Data.email) {
          setWaterMark(response.Data.email, response.Data.name)
        } else {
          setWaterMark('cloud', 'desktop')
        }
        if (this.$route.query.asset_ip) {
          const allowConnect = localStorage.getItem('allow_connect')
          if (allowConnect === 'true' || this.query.asset_user === 'zhishang_hu') {
            this.query.asset_host = this.$route.query.asset_ip
            this.query.asset_name = this.$route.query.asset_name
            this.query.asset_type = this.$route.query.asset_type
            this.query.session_id = this.$route.query.session_id
            this.query.asset_idc = this.$route.query.asset_idc
            this.query.force_lossless = this.$route.query.force_lossless
            getDesktopPermissionByIp({ ip: this.query.asset_host }).then(async response => {
              if (response.Data.name !== '') {
                // 1为开启2为关闭
                // true为关闭，false为开启
                this.disableCopy = response.Data.disableCopy === 2
                this.disableDownload = response.Data.disableDownload === 2
                console.log('check network')
                checkNetwork().then(netResponse => {
                  // 确认是否为外网
                  this.isOutLand = netResponse.isOutLand
                  if (this.query.asset_type === 'remote' || this.query.asset_type === 'server') {
                    if (netResponse.isOutLand === true) {
                      this.disableCopy = true
                      this.disableDownload = true
                    }
                    console.log('check network ok')
                  }
                  this.disablePaste = response.Data.disablePaste === 2
                  this.disableUpload = response.Data.disableUpload === 2
                  this.enablePrinting = response.Data.enablePrinting === 2
                  this.recording = response.Data.recording === 2
                  localStorage.removeItem('asset_type')
                  localStorage.removeItem('allow_connect')
                  this.query.token = response.Data.auth
                  console.log('doGuacdConnect')
                  this.doGuacdConnect()
                })

              }
            })
          } else {
            this.$error({
              title: '链接已失效，请返回主页重新登录！',
              okText: '返回主页',
              onOk: this.backTop,
            })
            // this.$message.error('链接已失效')
          }
        }
      })
    },
    backTop() {
      if (this.$route.query.asset_platform && this.$route.query.asset_platform == 'wx') {
        this.$router.push({
          path: '/server/wx-remote-desktop',
          query: {
            asset_platform: 'wx',
          },
        })
      } else {
        if (this.query.asset_type === 'remote') {
          this.$router.push({ path: '/server/remote-work' })
        } else {
          this.$router.push({ path: '/server/desktop' })
        }
      }
    },
    paramSerialize(obj) {
      const str = []
      for (const p in obj) {
        if (obj[p]) {
          str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]))
        }
      }
      return str.join('&')
    },
    onError(status) {
      switch (status.code) {
        case 256:
          this.errorMessage = '未支持的访问'
          break
        case 512:
          this.errorMessage = '远程服务异常，请检查目标设备能否正常访问。'
          break
        case 513:
          this.errorMessage = '服务器忙碌'
          break
        case 514:
          this.errorMessage = '服务器连接超时'
          break
        case 515:
          this.errorMessage = '远程服务异常'
          break
        case 516:
          this.errorMessage = '资源未找到'
          break
        case 517:
          this.errorMessage = '资源冲突'
          break
        case 518:
          this.errorMessage = '资源已关闭'
          break
        case 519:
          this.errorMessage = '远程服务未找到'
          break
        case 520:
          this.errorMessage = '远程服务不可用'
          break
        case 521:
          this.errorMessage = '会话冲突'
          break
        case 522:
          this.errorMessage = '会话连接超时'
          break
        case 523:
          this.errorMessage = '会话已关闭'
          break
        case 768:
          this.errorMessage = '网络不可达'
          break
        case 769:
          this.errorMessage = '服务器密码验证失败'
          break
        case 771:
          this.errorMessage = '客户端被禁止'
          break
        case 776:
          this.errorMessage = '客户端连接超时'
          break
        case 781:
          this.errorMessage = '客户端异常'
          break
        case 783:
          this.errorMessage = '错误的请求类型'
          break
        case 800:
          this.errorMessage = '会话不存在'
          break
        case 801:
          this.errorMessage = '创建隧道失败，请检查Guacd服务是否正常。'
          break
        case 802:
          this.errorMessage = '管理员强制关闭了此会话'
          break
        case 803:
          this.errorMessage = '当前网络区域为非可看数据区域,无法连接访问!请前往可看数据区域连接访问'
          break
        case 804:
          this.errorMessage = '服务器端异常，已断开连接~'
          break
        default:
          if (status.message) {
            // guacd 无法处理中文字符，所以进行了base64编码。
            this.errorMessage = Base64.decode(status.message)
          } else {
            this.errorMessage = '未知错误。'
          }
      }
    },
    // 确认是否为外网
    // async CheckNetwork() {
    //   if (this.query.asset_type === 'remote' || this.query.asset_type === 'server') {
    //     checkNetwork().then(response => {
    //       if (response.isOutLand === true) {
    //         this.disableCopy = true
    //         this.disableDownload = true
    //       }
    //       console.log('network checking.')
    //     })
    //   }
    // },
    doGuacdConnect() {
      this._setScreenSize()
      this.startGuacamole()
    },
    send(cmd) {
      if (!this.client) {
        return
      }
      for (const c of cmd.data) {
        this.client.sendKeyEvent(1, c.charCodeAt(0))
      }
    },
    _setScreenSize() {
      const elm = this.$refs.viewport
      if (!elm || !elm.offsetWidth) {
        // resize is being called on the hidden window
        return
      }
      if (this.isLandscape) {
        this.query.screen_width = window.innerWidth
        this.query.screen_height = window.innerHeight
      } else {
        this.query.screen_width = window.innerHeight
        this.query.screen_height = window.innerWidth
      }
      this.height = this.query.screen_height
      this.width = this.query.screen_width
    },
    handleMouseState(mouseState) {
      this.sinkFocus()
      this.resetWorker()
      const scaledMouseState = Object.assign({}, mouseState, {
        x: mouseState.x / this.display.getScale(),
        y: mouseState.y / this.display.getScale(),
      })
      this.client.sendMouseState(scaledMouseState)
    },
    handleTouchState(mouseState) {
      this.sinkFocus()
      this.resetWorker()
      let scaledMouseState

      const scale = this.display.getScale()
      // 调试信息
      // console.log("=========================================================")
      // console.log('Viewport rect:', rect)
      // console.log('MouseState', mouseState)
      // console.log('Display size:', this.display.getWidth(), this.display.getHeight())
      // console.log('Window size:', window.innerWidth, window.innerHeight)
      // console.log('scale:', scale)
      if (!this.isLandscape) {
        // planA转换
        // 限制x,y mouse
        mouseState.y = Math.max(0, Math.min(mouseState.y, window.innerWidth))
        mouseState.x = Math.max(0, Math.min(mouseState.x, window.innerWidth))
        // 视觉x,y轴压缩，缩放率
        const yScale = window.innerHeight / window.innerWidth
        // y调整后坐标
        scaledMouseState = Object.assign({}, mouseState, {
          x: mouseState.y * yScale / scale,
          y: (window.innerWidth - mouseState.x) / scale
        })
        // 限制
        scaledMouseState.y = Math.max(0, Math.min(scaledMouseState.y, this.display.getHeight()))
        scaledMouseState.x = Math.max(0, Math.min(scaledMouseState.x, this.display.getWidth()))

        // planB计算补偿
        // const xScale = window.innerWidth/window.innerHeight
        // const yScale = window.innerHeight/window.innerWidth
        // y调整后坐标
        // const mouseStateY = (window.innerWidth - mouseState.x ) * xScale
        // scaledMouseState = Object.assign({}, mouseState, {
        //   x: mouseState.y * yScale/ scale,
        //   y: mouseStateY / scale ,
        // })
        // 补偿旋转后的偏移
        // scaledMouseState.y = scaledMouseState.y + (this.display.getWidth() - window.innerHeight)
      } else {
        // 横屏模式 - 保持原有逻辑
        scaledMouseState = Object.assign({}, mouseState, {
          x: mouseState.x / scale,
          y: mouseState.y / scale,
        })
      }
      this.client.sendMouseState(scaledMouseState)
    },
    onWindowResize: _.debounce(function () {
      this.index = 1
      this.$nextTick(() => {
        this.isLandscape = window.innerWidth > window.innerHeight
        this.height = window.innerHeight
        this.width = window.innerWidth
        this.displayStatus = ''
        if (this.display.getWidth() !== this.width || this.display.getHeight() !== this.height) {
          // 判断是否横屏
          if (this.isLandscape) {
            this.client.sendSize(this.width, this.height)
          } else {
            this.client.sendSize(this.height, this.width)
          }
        }
        // this.client.sendSize(this.width, this.height)
        setTimeout(() => {
          this.index = -1
          this.height = this.display.getHeight()
          this.width = this.display.getWidth()

          const viewport = this.$refs.viewport
          if (this.isLandscape) {
            this.angle = 0
            this.keyboardStyle.transform = 'none'
            const scale = Math.min(window.innerWidth / this.width, window.innerHeight / this.height)
            this.display.scale(scale)
            viewport.style.width = '100%'
            viewport.style.height = '100%'
            viewport.style.transform = 'none'
            viewport.style.left = '0'
            viewport.style.top = '0'
          } else {
            this.angle = 90
            const scale = Math.min(window.innerHeight / this.width, window.innerWidth / this.height)
            this.display.scale(scale)
            viewport.style.width = `${this.display.getHeight()}px`
            viewport.style.height = `${this.display.getWidth()}px`
            viewport.style.transform = 'rotate(90deg)'
            viewport.style.transformOrigin = 'top left'
            viewport.style.position = 'absolute'
            viewport.style.top = '0'
            viewport.style.left = `${window.innerWidth}px`
            this.keyboardStyle.transform = 'rotate(0deg)!important'
          }
        }, 500)
      })
    }, 500),
    sinkFocus() {
      _.debounce(() => {
        // 处理搜索逻辑
        // if (this.isMobile) {
        //   this.sink.focus()
        // } else {
        //   this.sink.focus()
        // }
      }, 1000)()
    },
    resize() {
      this.onWindowResize()
    },
    firstResize() {
      const scale = Math.min(this.width / this.display.getWidth(), this.height / this.display.getHeight())
      this.display.scale(scale)
    },
    getDpi() {
      let dpr = window.devicePixelRatio || 1
      let screenDpi = 96 * dpr
      return Math.round(screenDpi)
    },
    // 加密
    encrypt(authData) {
      let keyStr = getCloudTermKey()
      let ivStr = keyStr
      let key = CryptoJS.enc.Utf8.parse(keyStr)
      let iv = CryptoJS.enc.Utf8.parse(ivStr)
      let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(authData))
      let encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      })
      return encodeURIComponent(encrypted.toString())
    },
    startGuacamole() {
      // const a = 'wss://cloud-test.intsig.net/api/desktop/ws'
      let onlineUrl = 'wss://guacamole.intsig.net/ws'
      // const testUrl = 'wss://sonar-api-test.intsig.net/ws'
      const testUrl = 'wss://guacamole.intsig.net/ws'
      if (this.query.asset_idc === 'tencent') {
        onlineUrl = 'wss://guacamole-tencent.intsig.net/ws'
      } else if (this.query.asset_idc === 'sh-office') {
        onlineUrl = 'wss://guacamole-sh.intsig.net/ws'
      } else {
        onlineUrl = 'wss://guacamole.intsig.net/ws'
      }
      // const a = 'ws://127.0.0.1:8014/v1/desktop/ws'
      let path = onlineUrl
      const env = judgeEnv()
      if (env !== 'ONLINE') {
        path = testUrl
      }
      // const tunnel = new Guacamole.WebSocketTunnel(this.wsUrl)
      const tunnel = new Guacamole.WebSocketTunnel(path)
      if (this.client) {
        this.display.onresize = function () {
          this.display.scale(
            Math.min(window.innerWidth / this.display.getHeight(), window.innerWidth / this.display.getHeight())
          )
        }
        this.uninstallKeyboard()
      }

      this.client = new Guacamole.Client(tunnel)
      // clipboard.install(this.client)
      // if (this.query.asset_type !== 'dlp') {
      // DLP开启剪切板但是只允许外面复制到里面
      if (!this.disablePaste) {
        clipboard.install(this.client)
      }
      // }
      tunnel.onerror = status => {
        // eslint-disable-next-line no-console
        console.error(`Tunnel failed ${JSON.stringify(status)}`)
        this.connectionState = states.TUNNEL_ERROR
      }
      this.client.onstatechange = clientState => {
        switch (clientState) {
          case 0:
            this.connectionState = states.IDLE
            this.$message.loading({ content: '正在初始化中...', key, duration: 0 })
            break
          case 1:
            this.connectionState = states.CONNECTING
            this.$message.loading({ content: '正在努力连接中...', key, duration: 0 })
            // connecting ignored for some reason?
            break
          case 2:
            this.connectionState = states.WAITING
            this.$message.loading({ content: '正在等待服务器响应...', key, duration: 0,   style: { marginTop: '40px' } })
            break
          case 3:
            this.connectionState = states.CONNECTED
            this.$message.success({ content: '连接成功!', key, duration: 3, style: { marginTop: '40px' } })
            this.resize()
            window.addEventListener('resize', this.resize)
            // if (this.query.asset_type !== 'dlp') {
            // 外复制内多重开启
            if (!this.disablePaste) {
              clipboard.setRemoteClipboard(this.client)
            }
            // }
            break
          // eslint-disable-next-line no-fallthrough
          case 4:
          case 5:
            this.connectionState = states.DISCONNECTED
            this.$message.error({ content: '连接已关闭!', key, duration: 0 })
            // disconnected, disconnecting
            break
        }
      }
      this.client.onerror = error => {
        this.client.disconnect()
        // eslint-disable-next-line no-console
        this.onError(error)
        this.$message.error({ content: this.errorMessage, duration: 0 })
        this.connectionState = states.CLIENT_ERROR
      }
      // 获取主准确的dpi
      // const dpi = this.getDpi()
      // this.query.screen_dpi = dpi\
      // 连接前加密
      const auth = this.encrypt(this.query)
      const newParam = { auth: auth }
      this.client.connect(this.paramSerialize(newParam))
      // this.client.connect(this.paramSerialize(this.query))
      // this.client.connect(qs.stringify(newParam))
      this.client.onsync = () => {}
      this.display = this.client.getDisplay()
      const displayElm = this.$refs.display
      console.log("!this.disableCopy:", !this.disableCopy)
      if (!this.disableCopy) {
        // 内复制外
        this.client.onclipboard = clipboard.onClipboard
      }


      displayElm.appendChild(this.display.getElement())
      // const element = this.client.getDisplay().getElement()
      displayElm.addEventListener('contextmenu', e => {
        e.stopPropagation()
        if (e.preventDefault) {
          e.preventDefault()
        }
        e.returnValue = false
      })
      window.onunload = () => this.client.disconnect()

      // this.sink = new Guacamole.InputSink()
      // displayElm.appendChild(this.sink.getElement())
      // if (this.isMobile) {
      //   this.sink.getElement().setAttribute('readonly', 'readonly')
      // }
      //
      // this.sink.focus()
      // this.mouse = new Guacamole.Mouse(this.client.getDisplay().getElement())
      this.mouse = new Guacamole.Mouse(displayElm)
      // Hide software cursor when mouse leaves display
      this.mouse.onmouseout = () => {
        if (!this.display) return
        this.display.showCursor()
      }

      // allows focusing on the display div so that keyboard doesn't always go to session
      displayElm.onclick = () => {
        displayElm.focus()
      }
      displayElm.onfocus = () => {
        displayElm.className = 'focus'
      }
      displayElm.onblur = () => {
        displayElm.className = ''
      }

      this.keyboard = new Guacamole.Keyboard(displayElm)
      // this.keyboard = new Guacamole.Keyboard(displayElm)
      this.installKeyboard()
      this.mouse.onmousedown = this.mouse.onmouseup = this.mouse.onmousemove = this.handleMouseState
      this.touch = new Guacamole.Mouse.Touchpad(displayElm) // or Guacamole.Touchscreen
      this.touch.onmousedown = this.touch.onmousemove = this.touch.onmouseup = this.handleTouchState
      displayElm.focus()
    },
    installKeyboard() {
      this.keyboard.onkeydown = keysym => {
        // 获取按键状态
        this.resetWorker()
        const modifiers = new Guacamole.Keyboard.ModifierState()
        console.log('onkeydown:', keysym)
        this.keys[keysym] = true

        if (modifiers.meta || keysym === 65511 || keysym ===65515) {
          console.log("Command key pressed:", keysym)
          console.log("modifiers.meta:", modifiers.meta)
          keysym = 65507
        }
        this.client.sendKeyEvent(1, keysym)
      }
      this.keyboard.onkeyup = keysym => {
        this.keys[keysym] = false
        console.log('onkeyup:', keysym)
        if (keysym === 65511 || keysym ===65515) {
          console.log("Command key released:", keysym)
          keysym = 65507
        }
        this.resetWorker()
        this.client.sendKeyEvent(0, keysym)
      }
    },
    uninstallKeyboard() {
      this.keyboard.onkeydown = this.keyboard.onkeyup = () => {}
    },
    copy(cmd) {
      if (!this.client) {
        return
      }
      clipboard.cache = {
        type: 'text/plain',
        data: cmd.data,
      }
      if (!this.disablePaste) {
        clipboard.setRemoteClipboard(this.client)
      }
    },
    getDesktopPermission(ip) {
      getPermission({ ip: ip }).then(res => {
        if (res.Code === 200) {
          this.disableCopy = res.Data.disableCopy === 1
          this.disablePaste = res.Data.disablePaste === 1
        }
      })
    },
    requestFullScreen(element) {
      // 判断各种浏览器，找到正确的方法
      const requestMethod =
        element.requestFullScreen || // W3C
        element.webkitRequestFullScreen || // FireFox
        element.mozRequestFullScreen || // Chrome等
        element.msRequestFullScreen // IE11
      if (requestMethod) {
        requestMethod.call(element)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        // for Internet Explorer
        const wScript = new window.ActiveXObject('WScript.Shell')
        if (wScript !== null) {
          wScript.SendKeys('{F11}')
        }
      }
    },
    // 退出全屏 判断浏览器种类
    exitFull() {
      // 判断各种浏览器，找到正确的方法
      const exitMethod =
        document.exitFullscreen || // W3C
        document.mozCancelFullScreen || // FireFox
        document.webkitExitFullscreen || // Chrome等
        document.webkitExitFullscreen // IE11
      if (exitMethod) {
        exitMethod.call(document)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        // for Internet Explorer
        const wScript = new window.ActiveXObject('WScript.Shell')
        if (wScript !== null) {
          wScript.SendKeys('{F11}')
        }
      }
    },
    isFullScreen() {
      return !!(
        document.fullscreen ||
        document.mozFullScreen ||
        document.webkitIsFullScreen ||
        document.webkitFullScreen ||
        document.msFullScreen
      )
    },
    changeDpi(dpi) {
      this.query.screen_dpi = dpi
      this.client.disconnect()
      // 循环删除所有子节点
      while (this.$refs.display.children.length > 0) {
        this.$refs.display.removeChild(this.$refs.display.children[0])
      }
      this.display = null
      this.client = null
      this.keyboard = null
      this.mouse = null
      this.sink = null
      this.visible = false
      this.doGuacdConnect()
    },
    changeLossless(lossless) {
      this.query.force_lossless = lossless
      this.client.disconnect()
      // 循环删除所有子节点
      while (this.$refs.display.children.length > 0) {
        this.$refs.display.removeChild(this.$refs.display.children[0])
      }
      this.display = null
      this.client = null
      this.keyboard = null
      this.mouse = null
      this.sink = null
      this.visible = false
      this.doGuacdConnect()
    },
    changeCursor(data) {
      this.cursorState = data
      this.showCursor = data ? 'auto' : 'none'
      this.display.showCursor(!data)
      this.visible = false
    },
    changeTouch(data) {
      if (data) {
        this.showCursor = 'none'
        this.display.showCursor(!data)
        this.touch = new Guacamole.Mouse.Touchscreen(this.client.getDisplay().getElement())
        this.touch.onmousedown = this.touch.onmousemove = this.touch.onmouseup = this.handleTouchState
      } else {
        this.touch = new Guacamole.Mouse.Touchpad(this.client.getDisplay().getElement()) // or Guacamole.Touchscreen
        this.touch.onmousedown = this.touch.onmousemove = this.touch.onmouseup = this.handleTouchState
      }
      this.useScreen = data
      this.visible = false
    },
    fullScreen() {
      if (this.isOnDrag) {
        return
      }
      if (this.isFullScreen()) {
        this.exitFull()
        this.fullScreenStatus = false
        this.fullScreenIcon = 'fullscreen'
        this.isOnDrag = false
        this.fullTitle = '全屏'
      } else {
        this.requestFullScreen(document.documentElement)
        this.fullScreenStatus = true
        this.fullScreenIcon = 'fullscreen-exit'
        this.isOnDrag = false
        this.fullTitle = '退出全屏'
      }
    },
    startWorker() {
      this.worker = new Worker(new URL('./setIntervalWorker.js', import.meta.url))
      this.worker.postMessage('stop')
      this.worker.postMessage('start')
      const that = this
      this.worker.onmessage = function (event) {
        let outTime = 30
        if (that.query.asset_type === 'server' && !that.isOutLand){
          outTime = 240
        }
        if (event.data === outTime * 60) {
          that.worker.postMessage('stop')
          that.stopConnect()
        }
      }
    },
    resetWorker() {
      this.worker.postMessage('restart')
    },
    stopConnect() {
      this.client.disconnect()
      this.$error({
        title: '长时间未操作，连接已关闭，请返回主页重新连接！',
        okText: '返回主页',
        onOk: this.backTop,
      })
    },
    //
    toggleKeyboard() {
      this.isKeyboardVisible = true
      this.isKeyboardCombVisible = false
      this.keyboardIndex = 100
    },
    sendKeyToGuacamole(key) {
      const keyCode = this.getKeyCodes(key)
      console.log('sendKeyToGuacamole', keyCode)
      this.keyboard.onkeydown(keyCode)
      this.keyboard.onkeyup(keyCode)
    },
    sendKeyToGuacamoleComb(keys) {
      const keyList = keys.toLowerCase().split('+')
      const keyCodes = keyList.map(key => this.getKeyCodes(key))
      keyCodes.forEach(code => this.keyboard.onkeydown(code))
      keyCodes.slice().reverse().forEach(code => this.keyboard.onkeyup(code))
    },
    getKeyCodes(key) {
      const specialKeys = {
        '{enter}': 65293, '{tab}': 9, space: 32, '{bksp}': 65288, win: 65511, shift: 65505, ctrl: 65507,
        '{shift}': 65505, '{lock}': 20, '{ctrl}': 65507, '{alt}': 65513, del: 65454, alt: 65513,
        '{esc}': 65307, '{arrowup}': 38, '{arrowdown}': 40, '{arrowleft}': 37,
        '{arrowright}': 39, '<': 60, '>': 62, '?': 63, '!': 33, '@': 64, '#': 35,
        $: 36, '%': 37, '^': 94, '&': 38, '*': 42, '(': 40, ')': 41, _: 95,
        '+': 43, '{': 123, '}': 125, '|': 124, '~': 126, ':': 58,
        '"': 34, ',': 44, '.': 46, '/': 47, ';': 59,
        '\'': 39, '[': 91, ']': 93, '\\': 92,
        0: 48, 1: 49, 2: 50, 3: 51, 4: 52, 5: 53, '{space}': 32,
        6: 54, 7: 55, 8: 56, 9: 57, '`': 96, '-': 45, '=': 61,
      }
      if (key in specialKeys) {
        return specialKeys[key]
      } else if (key.length === 1) {
        if (/[a-zA-Z]/.test(key)) {
          if (key === key.toLowerCase()) {
            return key.charCodeAt(0)
          } else {
            return key.toUpperCase().charCodeAt(0)
          }
        }
      }
      return  null
    },
    onKeyPressComb(button) {
      if (button === '关闭') {
        this.isKeyboardVisible = false
        this.isKeyboardCombVisible = false
        this.keyboardIndex = -100
      } else if (button === '返回') {
        this.isKeyboardVisible = true
        this.isKeyboardCombVisible = false
      } else {
        this.sendKeyToGuacamoleComb(button)
      }
    },
    onKeyPress(button) {
      if (button === '关闭') {
        this.isKeyboardVisible = false
        this.isKeyboardCombVisible = false
        this.keyboardIndex = -100
      } else if (button === '组合键') {
        this.isKeyboardVisible = false
        this.isKeyboardCombVisible = true
      } else if (button === '空格') {
        this.sendKeyToGuacamole('{space}')
      } else {
        if (!button.includes('{') && !button.includes('{') && button.split('').length > 1) {
          const buttonList = button.split('')
          for (let i = 0; i < buttonList.length; i++) {
            this.sendKeyToGuacamole(buttonList[i])
          }
        } else {
          this.sendKeyToGuacamole(button)
        }
      }
    },
    onInputChange(input) {
      console.log('onInputChange', input)
      this.input = input.target.value
    },
  },
  created() {
    this.getUserInfo()
  },
  mounted() {
    this.startWorker()
    window.onblur  = () => {
      console.log('onblur', this.keys)
      for (let key in this.keys) {
        if (this.keys.hasOwnProperty(key) && this.keys[key]) {
          this.client.sendKeyEvent(0, key)
        }
      }
      this.keys = {}
    }
  },
  unmounted() {
    removeWatermark()
    this.worker.postMessage('stop')
  },
}
</script>

<style scoped lang="less">
html,
body {
  width: 100%;
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  cursor: none !important;
}
.viewport {
  background-color: black;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  //position: absolute;
  /*cursor: none!important;*/

}
#display > div {
  margin: 0 auto;
  /*cursor: none!important;*/
}
.displayLoading {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1b1b1b;
  position: absolute;
  opacity: 0.7;
}
.ctrlButton {
  z-index: 100 !important;
  color: black;
  position: fixed;
  top: -20px;
  left: 50%;
  /deep/ i {
    /*color:red*/
    font-size: 22px;
  }

}
.fileButton {
  z-index: 100 !important;
  color: black;
  position: fixed;
  top: 15%;
  left: 95%;
  /deep/ i {
    /*color:red*/
    font-size: 22px;
  }
}
.fullButton {
  z-index: 100 !important;
  color: black;
  position: fixed;
  top: 22%;
  left: 95%;
  /deep/ i {
    /*color:red*/
    font-size: 22px;
  }
}
/*#fullButton  i {*/
/*  font-size: 190%;*/
/*  vertical-align: middle;*/
/*}*/
#components-dropdown-demo-placement .ant-btn {
  margin-right: 8px;
  margin-bottom: 8px;
}

.simple-keyboard{
  max-width: 100%;
}
.ctrlKeyboard {
  position: fixed;
}
::v-deep .simple-keyboard .hg-button.hg-standardBtn.hg-red {
  background-color: rgba(255, 0, 0, 0.7) !important;
  color: white !important;
}

.guacamole-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: rgba(0, 0, 0, 0.8);
}
.display {
  width: 100%;
  height: 100%;
}

#ctrlButton :deep(svg) {
  width: 24px;
  height: 24px;
}

.closeButton{
  border-radius: 50%;
  padding: 0;
  width: 20%;
  height: 20%;
  /deep/ svg {
    font-size: 14px;
  }
}
.custom-popover {
  z-index: 99 !important;
  opacity: 0.85;
  color: black;
  position: fixed;
  top: 30px;
  left: 50%;
  background: #fff;
  border-radius: 5px;
  border: 1px solid #f0f0f0;
  padding: 10px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 14px;
}

.custom-popover::before,
.custom-popover::after {
  content: '';
  position: absolute;
  border-style: solid;
}

.custom-popover::before {
  border-width: 7px;
  border-color: transparent;
}

.custom-popover::after {
  border-width: 6px;
  border-color: transparent;
}


.custom-popover.top::before {
  top: -14px;
  left: 20%;
  margin-left: -7px;
  border-bottom-color: #f0f0f0;
}

.custom-popover.top::after {
  top: -12px;
  left: 20%;
  margin-left: -6px;
  border-bottom-color: #fff;
}
</style>
