<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="创建用户">
              <a-input v-model:value="queryParam.searchKey" placeholder="请输入创建用户" @pressEnter="$refs.table.refresh()"/>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="云桌面类型">
              <a-select v-model:value="queryParam.assetType" placeholder="请选择云桌面类型">
                <a-select-option value="personal">
                  远程工作站
                </a-select-option>
                <a-select-option value="dlp">
                  DLP云桌面
                </a-select-option>
                <a-select-option value="server">
                  服务器云桌面
                </a-select-option>
                <a-select-option value="remote">
                  远程办公云桌面
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <s-table
      ref="table"
      size="default"
      :rowKey="(record) => record.connectionId"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{column, record, text}">
      <template v-if="column.dataIndex == 'assetType'">
        <a-tag :color="assetTypeColorFilter(record.assetType)">{{ assetTypeFilter(record.assetType) }}</a-tag>
      </template>
      <template v-else-if="column.dataIndex == 'status'">
        <a-badge :status="text "/>
      </template>
        <template v-else-if="column.dataIndex == 'action'">
          <a v-if="record.status === 'disconnected'" @click="clickLinkToReplay(record)">
            回放
          </a>
          <a v-if="record.status === 'connected' && record.assetType !== 'personal'" @click="clickLinkToMonitor(record)">
            监控
          </a>
          <a-popconfirm
            title="您确定要断开此会话吗?"
            ok-text="确认"
            cancel-text="取消"
            @confirm="confirmDisconnectSession(record)"
          >
            <a v-if="record.status === 'connected'" href="#">断开</a>
          </a-popconfirm>
        </template>
      </template>
    </s-table>

  </a-card>

</template>

<script>
  import { STable, Ellipsis } from '@/components'
  import { removeWatermark, setWaterMark } from '@/utils/watermark'
  import store from '@/store'
  import { disconnectSession, getDesktopSessionList, disconnectSessionTencent } from '@/api/desktop/desktop'
  const assetTypeMap = {
    'personal': {
      text: '远程工作站',
      color: 'orange'
    },
    'dlp': {
      color: 'green',
      text: 'DLP云桌面'
    },
    'server': {
      color: 'cyan',
      text: '服务器云桌面'
    },
    'remote': {
      color: 'purple',
      text: '远程办公云桌面'
    }
  }

  const columns = [
    {
      title: '云桌面类型',
      dataIndex: 'assetType',
      scopedSlots: { customRender: 'assetType' }
    },
    {
      title: '云桌面IP',
      dataIndex: 'assetIp'
    },
    {
      title: '创建用户',
      dataIndex: 'creator'
    },
    {
      title: '连接用户',
      dataIndex: 'username'
    },
    {
      title: '连接时间',
      dataIndex: 'connectedTime'
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '150px',
      scopedSlots: { customRender: 'action' }
    }
  ]

  const pagination = {
    showTotal: total => `共 ${total} 条`
  }

  export default {
    name: 'TableList',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.pagination = pagination
      this.downloadtableList = null
      return {
        visible: false,
        confirmLoading: false,
        mdl: null,
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
          delete this.downloadqueryParam.pageNo
          delete this.downloadqueryParam.pageSize
          delete this.downloadqueryParam.status
          return getDesktopSessionList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
    },
    destroyed () {
      removeWatermark()
    },
    methods: {
      assetTypeFilter (type) {
        return assetTypeMap[type]?.text || type
      },
      assetTypeColorFilter (type) {
        return assetTypeMap[type]?.color || type
      },
      confirmDisconnectSession (item) {
        if (item.assetType === "remote"){
          disconnectSessionTencent({ 'connectionId': item.connectionId }).then((res) => {
            if (res.code === 200) {
              this.$message.success('断开连接成功！')
            } else {
              const content = '断开连接失败！' + res.message
              this.$message.error(content)
            }
            this.$refs.table.refresh(true)
          })
        } else{
          disconnectSession({ 'connectionId': item.connectionId }).then((res) => {
            if (res.code === 200) {
              this.$message.success('断开连接成功！')
            } else {
              const content = '断开连接失败！' + res.message
              this.$message.error(content)
            }
            this.$refs.table.refresh(true)
          })
        }
      },
      clickLinkToMonitor (item) {
        // TODO 用asset_idc代替asset_type
        // this.$router.push({ path: '/desktop/session', query: { asset_ip: item.assetIp, asset_name: item.hostname } })
        const routeData = this.$router.resolve({ path: '/desktop/session-monitor', query: { id: item.connectionId, asset_ip: item.assetIp, asset_user: item.creator, asset_type: item.assetType } })
        window.open(routeData.href, '_blank')
      },
      clickLinkToReplay (item) {
        // TODO 用asset_idc代替asset_type
        // this.$router.push({ path: '/desktop/session', query: { asset_ip: item.assetIp, asset_name: item.hostname } })
        const routeData = this.$router.resolve({ path: '/desktop/session-recoding', query: { id: item.connectionId, asset_type: item.assetType } })
        window.open(routeData.href, '_blank')
      }
    }
  }
</script>
