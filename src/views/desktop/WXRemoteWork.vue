<template>
  <a-card class="remoteWorks" style="width: 100%">
    <div>
      <a-list :grid="{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 4, xxl: 4, xxxl: 4 }" :data-source="remoteData">
        <template #renderItem="{ item }">
          <a-list-item style="min-width: 280px; max-width: 310px">
            <a-card hoverable>
              <template #actions>
                <tx-button v-if="item.status === '可连接'" type="primary" icon="desktop" @click="handleInfo(item)">
                  连接
                </tx-button>
                <tx-button v-else icon="disconnect" disabled>连不上</tx-button>
                <tx-button
                  v-if="item.status === '不可连接' && item.desktopIdc === 'tencent'"
                  icon="play-circle"
                  @click="handleStart(item)"
                >
                  开机
                </tx-button>
                <tx-button
                  v-else-if="item.status === '关机中' && item.desktopIdc === 'tencent'"
                  icon="loading-outlined"
                >
                  关机中...
                </tx-button>
                <tx-button
                  v-else-if="item.status === '开机中' && item.desktopIdc === 'tencent'"
                  icon="loading-outlined"
                >
                  开机中...
                </tx-button>
                <tx-button
                  v-else-if="item.status === '重启中' && item.desktopIdc === 'tencent'"
                  icon="loading-outlined"
                  hover="none"
                >
                  重启中...
                </tx-button>
                <tx-button
                  v-else-if="item.status === '后台处理中' && item.desktopIdc === 'tencent'"
                  icon="loading-outlined"
                  pointer-events="none"
                >
                  处理中...
                </tx-button>
              </template>
              <a-card-meta :title="item.desktopName">
                <template #description v-if="item.desktopPermissionId > 0">
                  {{ item.desktopIp }}
                  <span style="color: #108ee9">({{ item.desktopPermission.name }}场景)</span>
                </template>
                <template #description v-else>
                  {{ item.desktopIp }}
                  <span style="color: #f50">(未关联场景)</span>
                </template>
                <template #avatar>
                  <a-avatar v-if="item.os === 'windows'" shape="square" :src="replace('@/assets/svgs/windows.svg')" />
                  <a-avatar v-else :src="replace('@/assets/svgs/ubuntu.svg')" />
                </template>
              </a-card-meta>
              <a-modal title="手机短信验证码" :visible="connectVisible" @ok="clickLinkTo" @cancel="cancelCheck">
                <a-spin v-if="verifyLoading" />
                <a-input
                  ref="codeVerify"
                  v-else
                  @pressEnter="enterClick"
                  v-model:value="verificationCode"
                  @change="codeChange"
                  placeholder="请输入验证码"
                ></a-input>
              </a-modal>
              <a-modal v-model:visible="captchaVisible" width="30%" title="二次验证" centered :footer="null">
                <SecondaryVerification @verification-success="clickLinkTo()" />
              </a-modal>
            </a-card>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </a-card>
</template>

<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import ScheduleTesk from './components/ScheduleTesk.vue'
import { sslDownloadCheck, sslDownloadSend, sendWxCode, checkWxCode } from '@/api/domain/domain'
import { notification } from 'ant-design-vue'
import { getDesktopUserList, getRemoteDesktopUserList } from '@/api/desktop/desktop_user'
import { rebootDesktop, startDesktop, stopDesktop, createImageDesktop, updateCronStop } from '@/api/desktop/desktop'
import { inject } from 'vue'
import { globalOtp } from '@/api/commonVerify'
import { waterMarkVs } from '@/utils/watermark'
import { myColumns } from '@/views/ai/aiData'

export default {
  components: {
    ScheduleTesk,
    SecondaryVerification,
  },
  data() {
    // var ua = window.navigator.userAgent.toLowerCase()
    // let plantform = 'web'
    // if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
    //   plantform = 'qw'
    // }
    return {
      captchaVisible: false,
      currentItem: '',
      plantform: 'qw',
      tabListNoTitle: [
        {
          key: 'remote',
          tab: '远程办公云桌面',
        },
      ],
      noTitleKey: 'remote',
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      verifyLoading: false,
      connectVisible: false,
      cronStopVisible: false,
      confirmLoading: false,
      jumpAssetIp: '',
      visible: false,
      forceLossless: false,
      sessionId: '',
      jumpHostname: '',
      desktopIdc: '',
      jumpHostType: '',
      remoteData: [],
      data: [],
      form: {},
      rules: {
        cronStopStatus: [{ required: true, message: '请选择是否开启或关闭智能关机', trigger: 'change' }],
      },
      CronStatus: false,
      scheduleVisible: false,
      // 循环调用的两个条件
      needGetDesktopStatus: false,
      intervalId: null,
      teskData: {},
      serverData: [],
      authOtpKey: '',
    }
  },
  setup(context, props) {
    const toogleFull = inject('toogleFull', () => {})
    return {
      toogleFull,
    }
  },
  beforeMount() {},
  mounted() {
    this.getInitUsersStatus()
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetDesktopUserList()
    let main = document.getElementsByClassName('remoteWorks')[0].parentElement
    main.style.margin = 0
    main.style.padding = 8 + 'px'
    this.toogleFull()
    setTimeout(() => {
      const musks = document.getElementsByClassName('ant-drawer-mask')
      if (musks && musks.length > 0) {
        for (let i = 0; i < musks.length; i++) {
          musks[i].click()
        }
      }
      if (this.$route.query.asset_platform && this.$route.query.asset_platform == 'wx') {
        return
      } else {
        if (this.data.length == 1) {
          const targetItem = this.data[0]
          if (targetItem.status == '可连接') {
            this.handleInfo(targetItem)
          }
        }
      }
    }, 1000)
  },
  watch: {
    needGetDesktopStatus: {
      handler: function (val) {
        if (!this.intervalId && val) {
          this.startLoop()
        } else {
          this.stopLoop()
        }
      },
    },
  },
  methods: {
    // 认证相关
    getInitUsersStatus() {
      this.authOtpKey = ''
      myColumns.forEach(item => {
        this.authOtpKey += item.scopeKey
      })
      this.authOtpKey = waterMarkVs + this.authOtpKey
    },
    checkStatus() {
      globalOtp().then(res => {
        console.log(res, 'resss')
        console.log(this.authOtpKey, ' this.authOtpKey')
        this.authToStr(res.Data.network, this.authOtpKey).then(r => {
          this.authToStr(res.Data.otpToken, this.authOtpKey).then(res1 => {
            if (res1) {
              localStorage.setItem('globalToken', res.Data.otpToken)
              this.captchaVisible = false
              // 无需验证触发提交工单
              // this.createData()
              this.clickLinkTo()
            } else {
              this.captchaVisible = true
            }
          })
        })
      })
    },
    async authToStr(text, a) {
      const importedKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(a),
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      )
      const textData = atob(text)
      const dataArray = new Uint8Array(textData.split('').map(char => char.charCodeAt(0)))

      const nonceSize = 12
      const nonce = dataArray.slice(0, nonceSize)
      const ciphertext = dataArray.slice(nonceSize)
      try {
        const myData = await crypto.subtle.decrypt({ name: 'AES-GCM', iv: nonce }, importedKey, ciphertext)
        return new TextDecoder().decode(myData)
      } catch (error) {
        return null
      }
    },
    plabTeskCancel() {
      this.remoteData = []
      this.GetDesktopUserList()
    },
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    codeChange(val) {},
    enterClick() {
      this.clickLinkTo()
    },
    GetDesktopUserList() {
      this.remoteData = []
      getRemoteDesktopUserList({ email: store.getters.email }).then(res => {
        if (res.Data.hasOwnProperty('data')) {
          if (res && res.Data && res.Data.data && res.Data.data.length > 0) {
            this.data = res.Data.data[0].desktops
            console.log(this.data, 'DesktopUserList')
            for (let i = 0; i < this.data.length; i++) {
              if (![1, 2, 6].includes(this.data[i].status)) {
                this.needGetDesktopStatus = true
                break
              } else {
                this.needGetDesktopStatus = false
              }
            }
            this.data.forEach(item => {
              if (item.status === 1) {
                item.status = '可连接'
              } else if (item.status === 2) {
                item.status = '不可连接'
              } else if (item.status === 3) {
                item.status = '关机中'
              } else if (item.status === 4) {
                item.status = '开机中'
              } else if (item.status === 5) {
                item.status = '重启中'
              } else if (item.status === 7) {
                item.status = '后台处理中'
              } else if (item.status === 6) {
                item.status = '状态异常'
              }
              if (item.desktopType === 'remote') {
                this.remoteData.push(item)
              }
            })
          }
        }
      })
    },
    GetServerDesktopList() {
      getDesktopUserList({ email: store.getters.email }).then(res => {
        this.serverData = []
        if (res.Data.hasOwnProperty('data')) {
          if (res && res.Data && res.Data.data && res.Data.data.length > 0) {
            this.data = res.Data.data[0].desktops
            this.data.forEach(item => {
              if (item.status === 1) {
                item.status = '可连接'
              } else {
                item.status = '不可连接'
              }
              console.log(12121)
              console.log(item)
              if (item.desktopType === 'server') {
                this.serverData.push(item)
              }
            })
          }
        }
      })
    },
    remoteDesktopApplication() {
      this.$router.push({ path: '/workflow/remote-desktop' })
    },
    remoteDesktopUrlApplication() {
      this.$router.push({ path: '/workflow/remote-desktop-url' })
    },
    desktopDestroyApplication() {
      this.$router.push({ path: '/workflow/desktop-destroy' })
    },
    startLoop() {
      this.intervalId = setInterval(this.GetDesktopUserList, 10000)
    },
    stopLoop() {
      clearInterval(this.intervalId)
    },
    handleStop(item) {
      this.$confirm({
        title: '确认关机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要关机？`,
        onOk() {
          stopDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '关机成功',
                  description: '云桌面关机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleStart(item) {
      this.$confirm({
        title: '确认开机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要开机？，开机后请等待1分钟以后在进行连接操作。`,
        onOk() {
          startDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '开机成功',
                  description: '云桌面开机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleReboot(item) {
      this.$confirm({
        title: '确认重启？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要重启？，重启后请等待1分钟以后在进行连接操作。`,
        onOk() {
          rebootDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '重启成功',
                  description: '云桌面重启成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleCreateImage(item) {
      this.$confirm({
        title: '确认系统备份？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面制作系统镜像用于备份？`,
        onOk() {
          createImageDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '系统备份成功',
                  description: '云桌面系统备份成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleCronStop(item) {
      this.form = item
      this.cronStopVisible = true
    },
    handleCronStopOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateCronStop(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.cronStopVisible = false
              this.confirmLoading = false
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleCronStopCancel(e) {
      this.cronStopVisible = false
      this.$refs.table.refresh()
      this.$message.success('更新成功')
    },
    handleClose() {
      this.remoteData = []
      this.GetDesktopUserList()
      // this.scheduleVisible = false
    },
    // 定时开关机任务
    handleScheduled(item) {
      this.scheduleVisible = true
      this.teskData = item
    },
    handleInfo(item) {
      this.currentItem = item
      if (item !== 'repeated') {
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
      }
      sendWxCode({
        ip: item.desktopIp,
        type: this.plantform,
      }).then(res => {
        console.log(res, 'resssss')
        // 非neeCheck
        if (res.Data && !res.Data.needCheck) {
          //
          // this.clickLinkTo(false)
          this.checkStatus()
        } else if (res.Data && res.Data.needCheck) {
          this.connectVisible = true
          this.captchaVisible = false
        }
      })
    },
    cancelCheck() {
      this.connectVisible = false
    },
    clickLinkTo(needCheck = true) {
      var checkData = {}
      checkData.email = store.getters.email
      if (store.getters.email.indexOf('robot_') === 0) {
        this.connectVisible = false
        localStorage.removeItem('asset_type')
        localStorage.removeItem('allow_connect')
        localStorage.setItem('asset_type', this.jumpHostType)
        localStorage.setItem('allow_connect', 'true')
        const routeData = this.$router.resolve({
          path: '/desktop/session',
          query: {
            asset_platform: 'wx',
            asset_ip: this.jumpAssetIp,
            asset_name: this.jumpHostname,
            asset_type: this.jumpHostType,
            asset_idc: this.desktopIdc,
          },
        })
        window.open(routeData.href, '_blank')
      } else {
        this.verifyLoading = true
        const paramsData = {
          code: this.verificationCode,
          ip: this.currentItem.desktopIp,
          type: this.plantform,
        }
        if (needCheck) {
          checkWxCode(paramsData)
            .then(res => {
              this.verifyLoading = false
              if (res.Data && res.Data.message == 'ok') {
                this.verifyLoading = true
                // sslDownloadCheck(checkData)
                //   .then(res => {
                //     if (res.Data.message === 'ok') {
                // 连接跳转
                this.connectVisible = false
                localStorage.removeItem('asset_type')
                localStorage.removeItem('allow_connect')
                localStorage.setItem('asset_type', this.jumpHostType)
                localStorage.setItem('allow_connect', 'true')
                const routeData = this.$router.resolve({
                  path: '/desktop/session',
                  query: {
                    asset_platform: 'wx',
                    asset_ip: this.jumpAssetIp,
                    asset_name: this.jumpHostname,
                    asset_type: this.jumpHostType,
                    asset_idc: this.desktopIdc,
                    force_lossless: this.forceLossless.toString(),
                  },
                })
                window.location.href = routeData.href
              } else if (res.Data && res.Data.message == 'error') {
                notification.error({
                  message: '输入验证码错误',
                })
                this.verificationCode = ''
              } else if (res.Data && res.Data.message == 'notExist') {
                this.verificationCode = ''
                notification.warning({
                  message: '验证码已过期，将为您重新发送',
                })
                sendWxCode({
                  ip: this.currentItem.desktopIp,
                  type: this.plantform,
                }).then(res => {})
              }
              console.log(res, 'rrrrrrrrr')
            })
            .finally(() => {
              this.verificationCode = ''
            })
        } else {
          console.log(1111111)
          // 连接跳转
          this.verifyLoading = false
          this.connectVisible = false
          localStorage.removeItem('asset_type')
          localStorage.removeItem('allow_connect')
          localStorage.setItem('asset_type', this.jumpHostType)
          localStorage.setItem('allow_connect', 'true')
          const routeData = this.$router.resolve({
            path: '/desktop/session',
            query: {
              asset_ip: this.jumpAssetIp,
              asset_name: this.jumpHostname,
              asset_type: this.jumpHostType,
              asset_idc: this.desktopIdc,
              force_lossless: this.forceLossless.toString(),
            },
          })
          window.location.href = routeData.href
        }

        // console.log();
        // window.open(routeData.href)
      }
    },
    onTabChange(key, type) {
      this[type] = key
      if (key === 'server') {
        this.GetServerDesktopList()
      }
    },
  },
  destroyed() {
    removeWatermark()
  },
}
</script>
