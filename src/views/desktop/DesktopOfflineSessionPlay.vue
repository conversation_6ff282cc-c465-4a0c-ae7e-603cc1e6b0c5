<template>
  <div style="width: 100vw; height: 100vh;display: flex;alignItems: center;justifyContent: center; backgroundColor: #1b1b1b">
    <div id="player" >
      <div id="display" ref="display">
        <div class="notification-container">
          <div class="seek-notification">
          </div>
        </div>
      </div>

      <div style="color: white; fontWeight: bold; cursor: pointer" @click="handlePlayPauseFirst" v-show="startPlay">点击播放</div>
      :
      <a-row v-show="startShow" justify="center" :gutter="[5,5]" style="margin: auto" align="middle" >
        <a-col :span="1" flex="none">
          <tx-button @click="handlePlayPause()" type="small" :icon="playBtnIcon"/>
        </a-col>
        <a-col :span="17" flex="auto">
          <a-slider :value="percent" :max="max" :tooltipVisible="false" @change="handleProgressChange"/>
        </a-col>
        <a-col :span="2" justify="center" flex="none">
          <a-select v-model:value="speed" size="small" @change="changeSpeed">
            <a-select-option :value="1">1.0倍速</a-select-option>
            <a-select-option :value="1.25" >1.25倍速</a-select-option>
            <a-select-option :value="1.5">1.5倍速</a-select-option>
            <a-select-option :value="1.75" >1.75倍速</a-select-option>
            <a-select-option :value="2.0">2.0倍速</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4" justify="center" flex="none">
          <div style="color: white;">
            <b>{{ this.position }}</b>/ <b>{{ this.duration }}</b>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>

</template>

<script>
  import { removeWatermark, setWaterMark } from '@/utils/watermark'
  import store from '@/store'
  import Guacamole from 'guacamole-common-js'
  import GuacMouse from '@/utils/GuacMouse'
  import states from '@/utils/states'
  import times from '@/utils/times'
  import { STable } from '@/components'
  // eslint-disable-next-line no-unused-vars
  Guacamole.Mouse = GuacMouse.mouse
  const key = 'updatable'
  export default {
    name: 'GuacClient',
    components: {
      STable
    },
    data () {
      return {
        demo: 'ubuntu',
        data: [{ 'hostname': 'test', 'ip': '**************', 'status': '可连接' }],
        connected: false,
        display: null,
        key: key,
        currentAdjustedHeight: null,
        client: null,
        keyboard: null,
        mouse: null,
        sink: null,
        lastEvent: null,
        recording: null,
        playBtnIcon: 'play-circle',
        percent: 0,
        startPlay: true,
        startShow: false,
        max: 0,
        speed: 1.0,
        duration: '00:00',
        position: '00:00',
        timer: null,
        fullScreened: false,
        width: window.innerWidth,
        height: window.innerHeight,
        connectionState: states.IDLE,
        errorMessage: '',
        arguments: {},
        query: {
          connection_id: '',
          asset_type: ''
        }
      }
    },
    computed: {
    },
    watch: {
    },
    methods: {
     handleProgressChange (val) {
        // Request seek
        this.recording.seek(val, () => {
          console.log('complete')
        })
      },
      changeSpeed (value) {
        this.speed = value
        if (value === 1) {
          this.stopSpeedUp()
        } else {
          this.startSpeedUp()
        }
      },
      doGuacdConnect () {
        // this._setScreenSize()
        this.startGuacamoleRecording()
      },
      onWindowResize () {
        const width = this.recording.getDisplay().getWidth()
        const height = this.recording.getDisplay().getHeight()
        const winWidth = document.documentElement.clientWidth
        const winHeight = document.documentElement.clientHeight - 80
        const scaleW = winWidth / width
        const scaleH = winHeight / height
        const scale = Math.min(scaleW, scaleH)
        if (!scale) {
          return
        }
        this.recording.getDisplay().scale(scale)
      },
      startGuacamoleRecording () {
        // const Url = 'https://cloud-test.intsig.net/api/desktop/recoding?connection_id=$e31e016b-5e19-4773-ba19-66cf3dcc8900'
        // const Url = window.location.protocol + '//' + window.location.host + '/api/desktop/recoding?connection_id=' + this.query.connection_id
        let Url = 'https://guacamole.intsig.net/desktop/recoding?session_id=' + this.query.connection_id
        // var path = 'wss://guacamole.intsig.net/desktop-monitor/ws'
        if (this.query.asset_type === "remote"){
          Url = 'https://guacamole-tencent.intsig.net/desktop/recoding?session_id=' + this.query.connection_id
        }
        if (process.env.NODE_ENV !== 'production') {
          Url = 'https://sonar-test.intsig.net/desktop/recoding?session_id=' + this.query.connection_id
        }
        console.log(Url)
        const tunnel = new Guacamole.StaticHTTPTunnel(Url)
        tunnel.onstatechange = state => {
          switch (state) {
            // Connection is being established
            case Guacamole.Tunnel.State.CONNECTING:
              this.connectionState = states.CONNECTING
              break

            // Connection is established / no longer unstable
            case Guacamole.Tunnel.State.OPEN:
              this.connectionState = states.CONNECTED
              break

            // Connection is established but misbehaving
            case Guacamole.Tunnel.State.UNSTABLE:
              // TODO
              break

            // Connection has closed
            case Guacamole.Tunnel.State.CLOSED:
              this.connectionState = states.DISCONNECTED
              break
          }
        }
        this.recording = new Guacamole.SessionRecording(tunnel)
        const recordingDisplay = this.recording.getDisplay()
        const display = this.$refs.display
        console.log(this.$ref)
        console.log(recordingDisplay)
        console.log(recordingDisplay.getElement())
        display.appendChild(recordingDisplay.getElement())
        this.recording.connect()
        // If playing, the play/pause button should read "Pause"
        this.recording.onplay = () => {
          this.playBtnIcon = 'pause-circle'
        }

        // If paused, the play/pause button should read "Play"
        this.recording.onpause = () => {
          this.playBtnIcon = 'play-circle'
        }

        // Toggle play/pause when display or button are clicked
        display.onclick = () => {
          this.handlePlayPause()
        }
        window.addEventListener('resize', this.onWindowResize)
        // Fit display within containing div
        recordingDisplay.onresize = function displayResized (width, height) {
          // Do not scale if display has no width
          if (!width) { return }

          // Scale display to fit width of container
          recordingDisplay.scale(display.offsetWidth / width)
        }

        this.recording.onseek = (millis) => {
          this.percent = millis
          this.position = times.formatTime(millis)
        }
        this.recording.onprogress = (millis) => {
          this.max = millis
          this.duration = times.formatTime(millis)
        }
      },
      installKeyboard () {
        this.keyboard.onkeydown = keysym => {
          this.client.sendKeyEvent(1, keysym)
        }
        this.keyboard.onkeyup = keysym => {
          this.client.sendKeyEvent(0, keysym)
        }
      },
      uninstallKeyboard () {
        this.keyboard.onkeydown = this.keyboard.onkeyup = () => {
        }
      },
      // fullScreen () {
      //   if (this.fullScreened) {
      //     exitFull()
      //     this.fullScreened = false
      //   } else {
      //     requestFullScreen(document.documentElement)
      //     this.fullScreened = true
      //   }
      //   focus()
      // }
      handlePlayPauseFirst () {
        this.startPlay = false
        this.startShow = true
        if (this.percent === this.max) {
          // 重播
          this.percent = 0
          this.recording.seek(0, () => {
            this.recording.play()
            this.startSpeedUp()
          })
        }

        if (!this.recording.isPlaying()) {
          this.recording.play()
          this.startSpeedUp()
        } else {
          this.recording.pause()
          this.stopSpeedUp()
        }
        setTimeout(() => {
          this.onWindowResize()
        }, 500)
      },
      handlePlayPause () {
        if (this.playBtnIcon === 'play-circle') {
          this.playBtnIcon = 'pause-circle'
        } else {
          this.playBtnIcon = 'play-circle'
        }

        if (this.percent === this.max) {
          // 重播
          this.percent = 0
          this.recording.seek(0, () => {
            this.recording.play()
            this.startSpeedUp()
          })
        }

        if (!this.recording.isPlaying()) {
          this.recording.play()
          this.startSpeedUp()
        } else {
          this.recording.pause()
          this.stopSpeedUp()
        }
      },
      startSpeedUp  () {
        this.stopSpeedUp()
        if (this.speed === 1) {
          return
        }
        if (!this.recording.isPlaying()) {
          return
        }
        const addTime = 100
        const delay = 1000 / (1000 / addTime) / (this.speed - 1)

        const max = this.recording.getDuration()
        const current = this.recording.getPosition()
        if (current >= max) {
          return
        }
        this.recording.seek(current + addTime, () => {
          this.timer = setTimeout(this.startSpeedUp, delay)
        })
      },

      stopSpeedUp  () {
        if (this.timer) {
          clearTimeout(this.timer)
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
      // this._setScreenSize()
      // this.startGuacamole()
      if (this.$route.query.id) {
        this.query.connection_id = this.$route.query.id
        this.query.asset_type = this.$route.query.asset_type
        this.doGuacdConnect()
        this.recording.onresize = () => {
          this.onWindowResize()
          return (() => {
          })()
        }
        window.addEventListener('resize', this.recording.onresize)
      }
    },
    destroyed () {
      removeWatermark()
    }
  }
</script>

<style scoped>
  .a-input {
    width: 100% !important;
  }

  .el-select {
    width: 100% !important;
  }

  .el-main {
    padding: 4px;
  }

  .display {
    overflow: hidden;
  }

  /* .viewport {
    background-color: aliceblue;
    border-color: blue;
    border-width: 1px;
    position: relative;
    width: 100%;
    height: 100%;
  } */

  #display > div {
    margin: 0 auto;
  }
</style>
