<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.searchKey" placeholder="服务器IP/创建用户" @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <s-table
        ref="table"
        size="default"
        rowKey="key"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowSelection="rowSelection"
      >
        <template #bodyCell="{column, record, text}">
        <template v-if="column.dataIndex == 'status'">
          <a-badge :status="text" />
        </template>
          <template v-else-if="column.dataIndex == 'action'">
            <!-- <router-link tag="a" target="_blank" :to="{ path: '/desktop/session-monitor',query: {id: record.connectionId, asset_ip: record.assetIp, asset_user: record.creator, }}">
              <a>监听</a>
            </router-link> -->
            <a @click="openWindow('/desktop/session-monitor?id='+record.connectionId+'&asset_ip='+record.assetIp+'&asset_user='+record.creator)">
              监控
            </a>
            <a-popconfirm
              title="您确定要断开此会话吗?"
              ok-text="确认"
              cancel-text="取消"
              @confirm="confirmDisconnectSession(record.connectionId)"
            >
              <a href="#">断开</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>

    </a-card></page-header-wrapper>

</template>

<script>
  import { STable, Ellipsis } from '@/components'
  import { removeWatermark, setWaterMark } from '@/utils/watermark'
  import { openTinyWin } from '@/utils/window'
  import store from '@/store'
  import { getDesktopSessionList, disconnectSession } from '@/api/desktop/desktop'
  // import { getAccountDetailInfo } from '@/api/cost/account_detail'

  const columns = [
    {
      title: '服务器名',
      dataIndex: 'assetName'
    },
    {
      title: '服务器IP',
      dataIndex: 'assetIp'
    },
    {
      title: '创建用户',
      dataIndex: 'creator'
    },
    {
      title: '连接用户',
      dataIndex: 'username'
    },
    {
      title: '连接时间',
      dataIndex: 'connectedTime'
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '150px',
      scopedSlots: { customRender: 'action' }
    }
  ]

  const pagination = {
    showTotal: total => `共 ${total} 条`
  }

  export default {
    name: 'TableList',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.pagination = pagination
      this.downloadtableList = null
      return {
        visible: false,
        confirmLoading: false,
        mdl: null,
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: { 'status': 'connected' },
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
          delete this.downloadqueryParam.pageNo
          delete this.downloadqueryParam.pageSize
          delete this.downloadqueryParam.status
          return getDesktopSessionList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
    },
    destroyed () {
      removeWatermark()
    },
    methods: {
      openWindow (str) {
        openTinyWin(str)
      },
      confirmDisconnectSession (connectionId) {
        disconnectSession({ 'connectionId': connectionId }).then((res) => {
          if (res.Data.code === 200) {
            this.$message.success('断开连接成功！')
          } else {
            const content = '断开连接失败！' + res.Data.message
            this.$message.error(content)
          }
          this.$refs.table.refresh(true)
        })
      }
    }
  }
</script>
