<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="操作用户">
              <a-input v-model:value="queryParam.operator" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="操作">
              <a-input v-model:value="queryParam.operation" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <s-table
      ref="table"
      size="default"
      :rowKey="(record) => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="修改后数据" :span="3">{{ record.newData }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{column, record}">
      <template v-if="column.dataIndex == 'target'">
        {{ targetFilter(record.target) }}
      </template>
      <template v-else-if="column.dataIndex == 'action'">
          <a @click="clickDetail(record)">
            详情
          </a>
      </template>
      </template>
    </s-table>
    <a-drawer
      title="详情"
      placement="right"
      :closable="false"
      width="40%"
      :visible="detailIdVisible"
      @close="detailIdVisible = false"
    >
      <a-card :bordered="false" :model="detailData" ref="detailData">
        <a-descriptions :column="2" title="基础信息">
          <a-descriptions-item label="操作对象">{{ detailData.target }}</a-descriptions-item>
          <a-descriptions-item label="操作用户">{{ detailData.operator }}</a-descriptions-item>
          <a-descriptions-item label="操作">{{ detailData.operation }}</a-descriptions-item>
          <a-descriptions-item label="操作时间">{{ detailData.createdAt }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="1" title="修改前数据">
          <a-descriptions-item>{{ detailData.oldData }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="1" title="修改后数据">
          <a-descriptions-item>{{ detailData.newData }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 32px"/>
      </a-card>
    </a-drawer>

  </a-card>

</template>

<script>
  import { STable, Ellipsis } from '@/components'
  import { removeWatermark, setWaterMark } from '@/utils/watermark'
  import store from '@/store'
  import { getDesktopLog } from '@/api/desktop/desktop_log'
  const columns = [
    {
      title: '操作对象',
      dataIndex: 'target',
      scopedSlots: { customRender: 'target' }
    },
    {
      title: '操作用户',
      dataIndex: 'operator'
    },
    {
      title: '操作',
      dataIndex: 'operation',
      scopedSlots: { customRender: 'operation' }
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt'
    },
    {
      title: '详情',
      dataIndex: 'action',
      width: '150px',
      scopedSlots: { customRender: 'action' }
    }
  ]
  const statusMap = {
    'desktop': {
      text: '云卓面'
    },
    'user': {
      text: '云桌面用户'
    },
    'desktop_permission': {
      text: '云桌面策略'
    }
  }
  const pagination = {
    showTotal: total => `共 ${total} 条`
  }

  export default {
    name: 'TableList',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.pagination = pagination
      this.downloadtableList = null
      return {
        visible: false,
        confirmLoading: false,
        mdl: null,
        detailIdVisible: false,
        detailData: {},
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
          delete this.downloadqueryParam.pageNo
          delete this.downloadqueryParam.pageSize
          delete this.downloadqueryParam.status
          return getDesktopLog(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
    },
    destroyed () {
      removeWatermark()
    },
    methods: {
      targetFilter (type) {
        return statusMap[type.toLowerCase()]?.text || type
      },
      clickDetail (item) {
        this.detailIdVisible = true
        this.detailData = item
      }
    }
  }
</script>
