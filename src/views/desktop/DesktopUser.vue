<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="邮箱">
              <a-input v-model:value="queryParam.email" placeholder="请输入邮箱" @pressEnter="$refs.table.refresh()" />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator">
      <tx-button type="primary" icon="plus" @click="handleAdd()">新增</tx-button>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
      :scroll="{ x: 800 }"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="授权云桌面" :span="3">{{ record.desktopNames }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'action'">
          <a @click="handleUpdate(record)">更新</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
            <template #icon>
              <a-icon type="question-circle-o" style="color: red" />
            </template>
            <a>删除</a>
          </a-popconfirm>
        </template>
      </template>
    </s-table>

    <a-modal
      title="新增用户"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="邮箱" name="email">
          <a-input v-model:value="form.email" />
        </a-form-model-item>
        <a-form-model-item label="用户名" name="cname">
          <a-input v-model:value="form.cname" />
        </a-form-model-item>

        <a-form-model-item label="登录密码" name="password">
          <a-input-password v-model:value="form.password" placeholder="密码" />
        </a-form-model-item>
        <a-form-model-item label="云桌面" name="desktops">
          <a-select v-model:value="form.desktops" placeholder="请选择" mode="multiple" :filter-option="filterOption">
            <a-select-option v-for="item in this.desktops" :key="item.id" :value="item.id">
              {{ item.desktopName }}({{ item.desktopIp }})
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="是否为公共账号" name="isPublic">
          <a-radio-group v-model:value="form.isPublic" button-style="solid">
            <a-radio-button :value="false">否</a-radio-button>
            <a-radio-button :value="true">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="自定义登录用户" name="isPublic">
          <a-radio-group v-model:value="form.isCustomize" button-style="solid">
            <a-radio-button :value="false">否</a-radio-button>
            <a-radio-button :value="true">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item v-if="this.form.isCustomize === true" label="云桌面登录用户" name="desktopUser">
          <a-input v-model:value="form.desktopUser" />
        </a-form-model-item>
        <a-form-model-item label="备注" name="comment">
          <a-input v-model:value="form.comment" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="用户信息更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="邮箱" name="email">
          <a-input v-model:value="form.email" disabled />
        </a-form-model-item>
        <a-form-model-item label="登录密码">
          <a-input-password v-model:value="form.password" placeholder="密码" />
        </a-form-model-item>
        <a-form-model-item label="云桌面" name="desktops">
          <a-select v-model:value="form.desktops" placeholder="请选择" mode="multiple" :filter-option="filterOption">
            <a-select-option v-for="item in this.desktops" :key="item.id" :value="item.id">
              {{ item.desktopName }}({{ item.desktopIp }})
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="是否为公共账号" name="isPublic">
          <a-radio-group v-model:value="form.isPublic" button-style="solid">
            <a-radio-button :value="false" disabled>否</a-radio-button>
            <a-radio-button :value="true" disabled>是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="备注" name="comment">
          <a-input v-model:value="form.comment" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import { STable, Ellipsis } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import {
  getAllDesktopUser,
  getDesktopUserInfo,
  createDesktopUser,
  updateDesktopUser,
  deleteDesktopUser,
} from '@/api/desktop/desktop_user'
import { getDesktopList } from '@/api/desktop/desktop'

const columns = [
  {
    title: '用户名',
    dataIndex: 'cname',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '云桌面用户',
    dataIndex: 'desktopUser',
  },
  {
    title: '备注',
    dataIndex: 'comment',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '120px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'DesktopUser',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      visible: false,
      confirmLoading: false,
      updateVisible: false,
      desktops: [{}],
      form: {
        email: store.getters.email,
        cname: store.getters.name,
        password: '',
        desktops: [],
        comment: '',
        isCustomize: false,
        desktopUser: '',
        isPublic: false,
      },
      rules: {
        email: [{ required: true, message: '请输入邮箱', trigger: 'change' }],
        cname: [{ required: true, message: '请输入名称', trigger: 'change' }],
        password: [{ required: true, message: '请输入密码', trigger: 'change' }],
        desktops: [{ required: true, message: '请选择云桌面', trigger: 'change' }],
        desktopUser: [{ required: true, message: '请选择云桌面登录用户', trigger: 'change' }],
      },
      advanced: false,
      queryParam: {},
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getAllDesktopUser(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            const defaultData = res.Data
            for (const index in defaultData.data) {
              let desktopName = ''
              for (const desktop in defaultData.data[index].desktops) {
                desktopName =
                  desktopName +
                  defaultData.data[index].desktops[desktop].desktopName +
                  '(' +
                  defaultData.data[index].desktops[desktop].desktopIp +
                  ')'
              }
              defaultData.data[index].desktopNames = desktopName
              if (defaultData.data[index].isPublic === undefined) {
                defaultData.data[index].isPublic = false
              }
            }
            return defaultData
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    handleAdd() {
      this.GetDesktopList()
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          const desktops = this.form.desktops
          const desktopIds = []
          for (const i in desktops) {
            desktopIds.push({ id: desktops[i] })
          }
          this.form.desktops = desktopIds
          createDesktopUser(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      this.GetDesktopList()
      getDesktopUserInfo(record.id).then(res => {
        if (res.Data.isPublic === undefined) {
          res.Data.isPublic = false
        }
        this.form = res.Data
        const desktopData = this.form.desktops.map((item, index, array) => {
          return item.id
        })
        this.form.desktops = desktopData
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          const desktops = this.form.desktops
          const desktopIds = []
          for (const i in desktops) {
            desktopIds.push({ id: desktops[i] })
          }
          this.form.desktops = desktopIds
          updateDesktopUser(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteDesktopUser(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    GetDesktopList() {
      getDesktopList().then(res => {
        if (res.Data.hasOwnProperty('data')) {
          this.desktops = noc.promiseArray(res.Data.data)
        } else {
          this.desktops = []
        }
      })
    },
    filterOption: filterLabelValue,
    /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      }
      */
  },
}
</script>
