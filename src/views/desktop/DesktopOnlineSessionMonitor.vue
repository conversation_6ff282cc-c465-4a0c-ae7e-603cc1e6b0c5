<template>

  <div ref="ff" style="width: 100%; height: 100%">
    <div class="displayLoading" :style="{'z-index': index}">
      <a-icon type="loading" style="font-size: 34px;color: white;position: absolute;"/>
      <!--      <tx-button class="ant-btn" style="color: white; fontWeight: bold; cursor: pointer;background-color: black; type="link" size="large" :loading="true"></tx-button>-->
    </div>
    <div ref="viewport" class="viewport">
      <!-- tabindex allows for div to be focused -->
      <div ref="display" id="display" class="display" tabindex="0">
      </div>
    </div>
  </div>
</template>

<script>
  import { removeWatermark, setWaterMark } from '@/utils/watermark'
  import store from '@/store'
import Guacamole from 'guacamole-common-js'
import GuacMouse from '@/utils/GuacMouse'
import states from '@/utils/states'
import { STable } from '@/components'
import clipboard from '@/utils/clipboard'
import { vendorLoad } from '@/utils/vendorLoader'
import _ from 'lodash'
  import { judgeEnv } from '@/utils/util'
Guacamole.Mouse = GuacMouse.mouse

let Base64

vendorLoad('js-base64').then(res => {
  Base64 = res
})

function serialize (obj) {
  const str = []
  for (const p in obj) {
    if (obj[p]) {
      str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]))
    }
  }
  return str.join('&')
}
const key = 'updatable'
export default {
  name: 'GuacClient',
  components: {
    STable
  },
  data () {
    return {
      demo: 'ubuntu',
      data: [{ 'hostname': 'test', 'ip': '**************', 'status': '可连接' }],
      connected: false,
      display: null,
      key: key,
      currentAdjustedHeight: null,
      client: null,
      keyboard: null,
      mouse: null,
      sink: null,
      lastEvent: null,
      index: 1,
      fullScreened: false,
      width: window.innerWidth,
      height: window.innerHeight,
      connectionState: states.IDLE,
      errorMessage: '',
      arguments: {},
      query: {
        asset_protocol: 'rdp',
        asset_user: '',
        asset_host: '',
        screen_width: 100,
        session_id: '',
        screen_height: 100,
        screen_dpi: 96
      }
    }
  },
  computed: {
    wsUrl () {
      const loc = window.location; let newUri
      if (loc.protocol === 'https:') {
        newUri = 'wss:'
      } else {
        newUri = 'ws:'
      }
      // newUri += '//127.0.0.1:8006'
      // new_uri += "//" + loc.host;
      newUri += '//' + loc.host
      newUri += '/api/desktop-monitor/ws'
      return `${newUri}`
    }
  },
  watch: {
    connectionState (state) {
      if (state === 'WAITING') {
        this.$message.loading({ content: '连接中...', key, duration: 0 })
      } else if (state === 'CONNECTED') {
        this.$message.success({ content: '连接成功！', key, duration: 3 })
      } else {
        this.$message.error({ content: '连接失败！', key, duration: 0 })
      }
    }
  },
  methods: {
    onError (status) {
      switch (status.code) {
        case 256:
          this.errorMessage = '未支持的访问'
          break
        case 512:
          this.errorMessage = '远程服务异常，请检查目标设备能否正常访问。'
          break
        case 513:
          this.errorMessage = '服务器忙碌'
          break
        case 514:
          this.errorMessage = '服务器连接超时'
          break
        case 515:
          this.errorMessage = '远程服务异常'
          break
        case 516:
          this.errorMessage = '资源未找到'
          break
        case 517:
          this.errorMessage = '资源冲突'
          break
        case 518:
          this.errorMessage = '资源已关闭'
          break
        case 519:
          this.errorMessage = '远程服务未找到'
          break
        case 520:
          this.errorMessage = '远程服务不可用'
          break
        case 521:
          this.errorMessage = '会话冲突'
          break
        case 522:
          this.errorMessage = '会话连接超时'
          break
        case 523:
          this.errorMessage = '会话已关闭'
          break
        case 768:
          this.errorMessage = '网络不可达'
          break
        case 769:
          this.errorMessage = '服务器密码验证失败'
          break
        case 771:
          this.errorMessage = '客户端被禁止'
          break
        case 776:
          this.errorMessage = '客户端连接超时'
          break
        case 781:
          this.errorMessage = '客户端异常'
          break
        case 783:
          this.errorMessage = '错误的请求类型'
          break
        case 800:
          this.errorMessage = '会话不存在'
          break
        case 801:
          this.errorMessage = '创建隧道失败，请检查Guacd服务是否正常。'
          break
        case 802:
          this.errorMessage = '管理员强制关闭了此会话'
          break
        default:
          if (status.message) {
            // guacd 无法处理中文字符，所以进行了base64编码。
            this.errorMessage = Base64.decode(status.message)
          } else {
            this.errorMessage = '未知错误。'
          }
      }
    },
    doGuacdConnect (sessionId) {
      this._setScreenSize()
      this.startGuacamole(sessionId)
    },
    send (cmd) {
      if (!this.client) {
        return
      }
      for (const c of cmd.data) {
        this.client.sendKeyEvent(1, c.charCodeAt(0))
      }
    },
    copy (cmd) {
      if (!this.client) {
        return
      }
      clipboard.cache = {
        type: 'text/plain',
        data: cmd.data
      }
      clipboard.setRemoteClipboard(this.client)
    },
    _setScreenSize () {
      const elm = this.$refs.viewport
      if (!elm || !elm.offsetWidth) {
        // resize is being called on the hidden window
        return
      }
      const pixelDensity = window.devicePixelRatio || 1
      const width = elm.clientWidth * pixelDensity
      const height = elm.clientHeight * pixelDensity
      this.query.screen_height = Math.ceil(height)
      this.query.screen_width = Math.ceil(width)
    },
    onWindowResize: _.debounce(function () {
      this.index = 1
      this.$nextTick(() => {
        // console.log('dom 完成', this.width, document.body.clientWidth, this.height, document.body.clientHeight)
        this.height = document.documentElement.clientHeight
        this.width = document.documentElement.clientWidth
        this.displayStatus = ''
        // setting timeout so display has time to get the correct size
        // console.log('send', this.width, this.height)
        this.client.sendSize(this.width, this.height)
        setTimeout(() => {
          // console.log(this.width, this.height)
          // console.log('结束 display', this.display.getWidth(), this.display.getHeight())
          // console.log('结束 body', document.body.clientWidth, document.body.clientHeight)
          // console.log('结束 document', document.documentElement.clientWidth, document.documentElement.clientHeight)
          const scale = Math.min(
            document.documentElement.clientWidth / this.display.getWidth(),
            document.documentElement.clientHeight / this.display.getHeight()
          )
          // console.log(scale)
          this.index = -1
          console.log(this.index)
          this.height = this.display.getHeight()
          this.width = this.display.getWidth()
          this.display.scale(scale)
        }, 1500)
      })
    }, 500),
    resize () {
      this.onWindowResize()
    },
    handleMouseState (mouseState) {
      const scaledMouseState = Object.assign({}, mouseState, {
        x: mouseState.x / this.display.getScale(),
        y: mouseState.y / this.display.getScale()
      })
      this.client.sendMouseState(scaledMouseState)
    },
    startGuacamole (sessionId) {
      let onlineUrl = 'wss://guacamole.intsig.net/desktop-monitor/ws'
      const testUrl = 'wss://sonar-test.intsig.net/desktop-monitor/ws'
      if (this.query.asset_type === 'remote') {
        onlineUrl = 'wss://guacamole-tencent.intsig.net/desktop-monitor/ws'
      } else {
        onlineUrl = 'wss://guacamole.intsig.net/desktop-monitor/ws'
      }
      let path = onlineUrl
      const env = judgeEnv()
      if (env !== 'ONLINE') {
        path = testUrl
      }
      // const tunnel = new Guacamole.WebSocketTunnel(this.wsUrl)
      const tunnel = new Guacamole.WebSocketTunnel(path)

      const elm = this.$refs.viewport
      if (this.client) {
        this.display.onresize = function () {
          this.display.scale(Math.min(
            elm.clientHeight / this.display.getHeight(),
            elm.clientWidth / this.display.getHeight()
          ))
        }
        this.uninstallKeyboard()
      }

      this.client = new Guacamole.Client(tunnel)
      // clipboard.install(this.client)
      tunnel.onerror = status => {
        // eslint-disable-next-line no-console
        console.error(`Tunnel failed ${JSON.stringify(status)}`)
        this.connectionState = states.TUNNEL_ERROR
      }
      tunnel.onstatechange = state => {
        switch (state) {
          // Connection is being established
          case Guacamole.Tunnel.State.CONNECTING:
            this.connectionState = states.CONNECTING
            break

          // Connection is established / no longer unstable
          case Guacamole.Tunnel.State.OPEN:
            this.connectionState = states.CONNECTED
            break

          // Connection is established but misbehaving
          case Guacamole.Tunnel.State.UNSTABLE:
            // TODO
            break

          // Connection has closed
          case Guacamole.Tunnel.State.CLOSED:
            this.connectionState = states.DISCONNECTED
            break
        }
      }
      this.client.onstatechange = clientState => {
        switch (clientState) {
          case 0:
            this.connectionState = states.IDLE
            break
          case 1:
            // connecting ignored for some reason?
            break
          case 2:
            this.connectionState = states.WAITING
            break
          case 3:
            this.connectionState = states.CONNECTED
            window.addEventListener('resize', this.resize)

            clipboard.setRemoteClipboard(this.client)

          // eslint-disable-next-line no-fallthrough
          case 4:
          case 5:
            // disconnected, disconnecting
            break
        }
      }
      this.client.onerror = error => {
        this.client.disconnect()
        // eslint-disable-next-line no-console
        this.onError(error)
        this.$message.error({ content: this.errorMessage, duration: 0 })
        this.connectionState = states.CLIENT_ERROR
      }
      const param = serialize(this.query)
      this.client.connect(param)
      this.client.onsync = () => {
      }

      this.display = this.client.getDisplay()
      const displayElm = this.$refs.display
      displayElm.appendChild(this.display.getElement())
      displayElm.addEventListener('contextmenu', e => {
        e.stopPropagation()
        if (e.preventDefault) {
          e.preventDefault()
        }
        e.returnValue = false
      })

      window.onunload = () => this.client.disconnect()

      setTimeout(() => {
        this.resize()
        displayElm.focus()
      }, 1000) // $nextTick wasn't enough
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    // this._setScreenSize()
    // this.startGuacamole()
    if (this.$route.query.id) {
      this.query.session_id = this.$route.query.id
      this.query.asset_host = this.$route.query.asset_ip
      this.query.asset_user = this.$route.query.asset_user
      this.query.asset_type = this.$route.query.asset_type
      this.doGuacdConnect()
    }
  },
  destroyed () {
    removeWatermark()
  }
}
</script>

<style scoped>
  html, body {
    width: 100%;
    height: 100%;
    margin: 0!important;
    padding: 0!important;
    overflow:hidden;
  }
  .viewport {
    background-color: black;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    position: absolute;
  }
  #display > div {
    margin: 0 auto;
  }
  .displayLoading {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1b1b1b;
    position: absolute;
    opacity: 0.7
  }
</style>
