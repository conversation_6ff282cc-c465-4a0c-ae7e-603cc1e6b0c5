<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :tab-list="tabListNoTitle"
      :active-tab-key="noTitleKey"
      @tabChange="key => onTabChange(key, 'noTitleKey')"
    >
      <div v-if="noTitleKey === 'personal'">
        <div class="table-operator">
          <tx-button type="primary" style="margin-bottom: 8px" icon="plus" @click="cloudDesktopApplication">
            远程工作站申请
          </tx-button>
          <tx-button type="primary" style="margin-bottom: 8px" icon="poweroff" @click="desktopDestroyApplication">
            云桌面注销
          </tx-button>
        </div>
        <a-list :grid="{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 4, xxl: 4, xxxl: 4 }" :data-source="personalData">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item>
                <a-card hoverable>
                  <template #cover>
                    <img v-if="item.os === 'windows'" :src="replace('@/assets/svgs/windows.png')" :alt="item.title" />
                    <img v-else :src="replace('@/assets/svgs/ubuntu.png')" :alt="item.title" />
                  </template>
                  <template #actions>
                    <tx-button v-if="item.status === '可连接'" type="primary" icon="desktop" @click="handleInfo(item)">
                      连接
                    </tx-button>
                    <tx-button v-else icon="disconnect" disabled>连不上</tx-button>
                    <tx-button
                      v-if="item.status === '可连接' && item.desktopIdc === 'tencent'"
                      icon="poweroff"
                      @click="handleStop(item)"
                    >
                      关机
                    </tx-button>
                    <tx-button
                      v-else-if="item.status === '不可连接' && item.desktopIdc === 'tencent'"
                      icon="play-circle"
                      @click="handleStart(item)"
                    >
                      开机
                    </tx-button>
                    <tx-button v-else icon="poweroff" disabled>关机</tx-button>
                    <a-dropdown v-if="item.desktopIdc === 'tencent'">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item v-if="item.status === '可连接' && item.desktopIdc === 'tencent'">
                            <a @click="handleReboot(item)">重启</a>
                          </a-menu-item>
                          <a-menu-item>
                            <a @click="handleCreateImage(item)">系统备份</a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <tx-button>
                        更多
                        <a-icon type="down" />
                      </tx-button>
                    </a-dropdown>
                    <tx-button v-else disabled>
                      更多
                      <a-icon type="down" />
                    </tx-button>
                  </template>
                  <a-card-meta :title="item.desktopName">
                    <template #description v-if="item.desktopPermissionId > 0">
                      {{ item.desktopIp }}
                      <span style="color: #108ee9">({{ item.desktopPermission.name }}场景)</span>
                    </template>
                    <template #description v-else>
                      {{ item.desktopIp }}
                      <span style="color: #f50">(未关联场景)</span>
                    </template>
                    <template #avatar>
                      <a-avatar
                        v-if="item.os === 'windows'"
                        shape="square"
                        :src="replace('@/assets/svgs/windows.svg')"
                      />
                      <a-avatar v-else :src="replace('@/assets/svgs/ubuntu.svg')" />
                    </template>
                  </a-card-meta>
                </a-card>
              </a-list-item>
            </a-list-item>
          </template>
        </a-list>
      </div>
      <div v-else-if="noTitleKey === 'dlp'">
        <p>
          DLP云桌面主要应用于数据防泄漏相关业务场景。
          <br />
          数据标注及数据操作创建的云桌面策略：水印（开启），录屏审计（开启），剪切板仅单向允许（外->里），云存储挂载（禁用），公网访问（禁用）。
          <br />
          需定制化新的DLP云桌面业务场景策略，请走DLP云桌面场景流程申请。
        </p>
        <div class="table-operator">
          <tx-button type="primary" icon="plus" @click="dlpDesktopApplication">DLP云桌面申请</tx-button>
          <tx-button type="primary" icon="plus" @click="dbManipulationApplication">数据操作申请</tx-button>
          <tx-button type="primary" icon="poweroff" @click="desktopDestroyApplication">云桌面注销</tx-button>
          <tx-button type="primary" icon="shop" @click="dlpDesktopPolicyApplication">定制DLP云桌面场景</tx-button>
        </div>
        <a-list :grid="{ gutter: 16, column: 4 }" :data-source="dlpData">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item>
                <a-card hoverable>
                  <template #cover>
                    <img v-if="item.os === 'windows'" :src="replace('@/assets/svgs/windows.png')" :alt="item.title" />
                    <img v-else :src="replace('@/assets/svgs/ubuntu.png')" :alt="item.title" />
                  </template>
                  <template #actions>
                    <tx-button v-if="item.status === '可连接'" type="primary" icon="desktop" @click="handleInfo(item)">
                      连接
                    </tx-button>
                    <tx-button v-else icon="disconnect" disabled>连不上</tx-button>
                    <tx-button
                      v-if="item.status === '可连接' && item.desktopIdc === 'tencent'"
                      icon="poweroff"
                      @click="handleStop(item)"
                    >
                      关机
                    </tx-button>
                    <tx-button
                      v-else-if="item.status === '不可连接' && item.desktopIdc === 'tencent'"
                      icon="play-circle"
                      @click="handleStart(item)"
                    >
                      开机
                    </tx-button>
                    <tx-button v-else icon="poweroff" disabled>关机</tx-button>
                    <a-dropdown v-if="item.desktopIdc === 'tencent'">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item v-if="item.status === '可连接' && item.desktopIdc === 'tencent'">
                            <a @click="handleReboot(item)">重启</a>
                          </a-menu-item>
                          <a-menu-item>
                            <a @click="handleCreateImage(item)">系统备份</a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <tx-button>
                        更多
                        <a-icon type="down" />
                      </tx-button>
                    </a-dropdown>
                    <tx-button v-else disabled>
                      更多
                      <a-icon type="down" />
                    </tx-button>
                  </template>
                  <a-card-meta :title="item.desktopName">
                    <template #description v-if="item.desktopPermissionId > 0">
                      {{ item.desktopIp }}
                      <span style="color: #108ee9">({{ item.desktopPermission.name }}场景)</span>
                    </template>
                    <template #description v-else>
                      {{ item.desktopIp }}
                      <span style="color: #f50">(未关联场景)</span>
                    </template>
                    <template #avatar>
                      <a-avatar
                        v-if="item.os === 'windows'"
                        shape="square"
                        :src="replace('@/assets/svgs/windows.svg')"
                      />
                      <a-avatar v-else :src="replace('@/assets/svgs/ubuntu.svg')" />
                    </template>
                  </a-card-meta>
                </a-card>
              </a-list-item>
            </a-list-item>
          </template>
        </a-list>
      </div>
      <div v-else-if="noTitleKey === 'server'">
        <p>使用远程桌面的方式访问线上服务器。水印（开启），录屏审计（开启），剪切板（禁用）</p>
        <div class="table-operator">
          <tx-button type="primary" icon="plus" @click="serverDesktopApplication">服务器云桌面申请</tx-button>
          <tx-button type="primary" icon="poweroff" @click="desktopDestroyApplication">云桌面注销</tx-button>
        </div>
        <a-list :grid="{ gutter: 16, column: 4 }" :data-source="serverData">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item>
                <a-card hoverable>
                  <template #cover>
                    <img v-if="item.os === 'windows'" :src="replace('@/assets/svgs/windows.png')" :alt="item.title" />
                    <img v-else :src="replace('@/assets/svgs/ubuntu.png')" :alt="item.title" />
                  </template>
                  <template #actions>
                    <tx-button v-if="item.status === '可连接'" type="primary" icon="desktop" @click="handleInfo(item)">
                      连接
                    </tx-button>
                    <tx-button v-else icon="disconnect" disabled>连不上</tx-button>
                    <tx-button
                      v-if="item.status === '可连接' && item.desktopIdc === 'tencent'"
                      icon="poweroff"
                      @click="handleStop(item)"
                    >
                      关机
                    </tx-button>
                    <tx-button
                      v-else-if="item.status === '不可连接' && item.desktopIdc === 'tencent'"
                      icon="play-circle"
                      @click="handleStart(item)"
                    >
                      开机
                    </tx-button>
                    <tx-button v-else icon="poweroff" disabled>关机</tx-button>
                    <a-dropdown v-if="item.desktopIdc === 'tencent'">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item v-if="item.status === '可连接' && item.desktopIdc === 'tencent'">
                            <a @click="handleReboot(item)">重启</a>
                          </a-menu-item>
                          <a-menu-item>
                            <a @click="handleCreateImage(item)">系统备份</a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <tx-button>
                        更多
                        <a-icon type="down" />
                      </tx-button>
                    </a-dropdown>
                    <tx-button v-else disabled>
                      更多
                      <a-icon type="down" />
                    </tx-button>
                  </template>
                  <a-card-meta :title="item.desktopName">
                    <template #description v-if="item.desktopPermissionId > 0">
                      {{ item.desktopIp }}
                      <span style="color: #108ee9">({{ item.desktopPermission.name }}场景)</span>
                    </template>
                    <template #description v-else>
                      {{ item.desktopIp }}
                      <span style="color: #f50">(未关联场景)</span>
                    </template>
                    <template #avatar>
                      <a-avatar
                        v-if="item.os === 'windows'"
                        shape="square"
                        :src="replace('@/assets/svgs/windows.svg')"
                      />
                      <a-avatar v-else :src="replace('@/assets/svgs/ubuntu.svg')" />
                    </template>
                  </a-card-meta>
                </a-card>
              </a-list-item>
            </a-list-item>
          </template>
        </a-list>
      </div>
      <template #tabBarExtraContent>
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=430211771">云桌面说明文档</a>
      </template>
      <a-modal title="企业微信(合小云)动态验证码" :visible="connectVisible" @ok="clickLinkTo" @cancel="cancelCheck">
        <a-spin v-if="verifyLoading" />
        <a-input
          ref="codeVerify"
          v-else
          @pressEnter="enterClick"
          v-model:value="verificationCode"
          @change="codeChange"
          placeholder="请输入验证码"
        >
          <!-- @search="handleInfo('repeated')" -->
          <!-- <template #enterButton><tx-button>重新获取</tx-button></template> -->
        </a-input>
        <p style="margin-top: 5%; font-size: 15px">连接选项:</p>
        <p style="margin-top: 3%; margin-left: 5%">
          无损压缩
          <a-tooltip>
            <template #title>开启无损压缩，可以提升云桌面的画质，但是云桌面的操作流畅度会有所损失</template>
            <a-icon type="question-circle" />
          </a-tooltip>
          <a-switch
            style="margin-left: 1%"
            v-model:checked="forceLossless"
            checked-children="开"
            un-checked-children="关"
          />
        </p>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>
<script>
import { defineComponent } from 'vue'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { getDesktopUserList } from '@/api/desktop/desktop_user'
import { createImageDesktop, rebootDesktop, startDesktop, stopDesktop } from '@/api/desktop/desktop'
import { notification } from 'ant-design-vue'
import { sslDownloadCheck, sslDownloadSend, sendWxCode, checkWxCode } from '@/api/domain/domain'

export default defineComponent({
  name: 'Desktop',
  data() {
    var ua = window.navigator.userAgent.toLowerCase()
    let plantform = 'web'
    if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
      plantform = 'qw'
    }
    return {
      verifyLoading: false,
      plantform,
      tabListNoTitle: [
        {
          key: 'personal',
          tab: '远程工作站',
        },
        {
          key: 'dlp',
          tab: 'DLP云桌面',
        },
        {
          key: 'server',
          tab: '服务器云桌面',
        },
      ],
      noTitleKey: 'personal',
      connectVisible: false,
      verificationCode: '',
      jumpAssetIp: '',
      visible: false,
      forceLossless: false,
      sessionId: '',
      jumpHostname: '',
      desktopIdc: '',
      jumpHostType: '',
      personalData: [],
      dlpData: [],
      serverData: [],
      data: [],
      currentItem: '',
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetDesktopUserList()
  },
  destroyed() {
    removeWatermark()
  },
  watch: {
    verificationCode: {
      handler: function (val) {
        if (val) {
          if (val.length === 6) {
            this.clickLinkTo()
          } else if (val.length > 6) {
            notification.error({
              message: '输入六位验证码',
            })
          }
        }
      },
    },
  },
  methods: {
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    codeChange(val) {
      // if (this.verificationCode.length >= 6) {
      //   this.clickLinkTo()
      // }
    },
    enterClick() {
      this.clickLinkTo()
    },
    GetDesktopUserList() {
      getDesktopUserList({ email: store.getters.email }).then(res => {
        if (res.Data.hasOwnProperty('data')) {
          if (res && res.Data && res.Data.data && res.Data.data.length > 0) {
            this.data = res.Data.data[0].desktops
            this.data.forEach(item => {
              if (item.status === 1) {
                item.status = '可连接'
              } else {
                item.status = '不可连接'
              }

              if (item.desktopType === 'personal') {
                this.personalData.push(item)
              } else if (item.desktopType === 'dlp') {
                this.dlpData.push(item)
              } else if (item.desktopType === 'server') {
                this.serverData.push(item)
              }
            })
          }
        }
      })
    },
    cloudDesktopApplication() {
      this.$router.push({ path: '/workflow/cloud-desktop' })
    },
    dlpDesktopApplication() {
      this.$router.push({ path: '/workflow/dlp-desktop' })
    },
    serverDesktopApplication() {
      this.$router.push({ path: '/workflow/server-desktop' })
    },
    dbManipulationApplication() {
      this.$router.push({ path: '/workflow/db-manipulation' })
    },
    desktopDestroyApplication() {
      this.$router.push({ path: '/workflow/desktop-destroy' })
    },
    dlpDesktopPolicyApplication() {
      this.$router.push({ path: '/workflow/dlp-desktop-policy' })
    },
    handleStop(item) {
      this.$confirm({
        title: '确认关机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要关机？`,
        onOk() {
          stopDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '关机成功',
                  description: '云桌面关机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleStart(item) {
      this.$confirm({
        title: '确认开机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要开机？`,
        onOk() {
          startDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '开机成功',
                  description: '云桌面开机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleReboot(item) {
      this.$confirm({
        title: '确认重启？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要重启？`,
        onOk() {
          rebootDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '重启成功',
                  description: '云桌面重启成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleCreateImage(item) {
      this.$confirm({
        title: '确认系统备份？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面制作系统镜像用于备份？`,
        onOk() {
          createImageDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '系统备份成功',
                  description: '云桌面系统备份成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleInfo(item) {
      this.currentItem = item
      if (store.getters.email.indexOf('robot_') === 0) {
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
        this.clickLinkTo()
        return
      }
      if (item !== 'repeated') {
        this.verificationCode = ''
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
      }

      var sendData = {}
      sendData.email = store.getters.email
      sendWxCode({
        ip: item.desktopIp,
        type: this.plantform,
      }).then(res => {
        console.log(res, 'resssss')
        // 非neeCheck
        if (res.Data && !res.Data.needCheck) {
          //
          this.clickLinkTo(false)
        } else if (res.Data && res.Data.needCheck) {
          this.connectVisible = true
        }
      })

      // sslDownloadSend(sendData).then(res => {
      //   if (res.Data.message === 'ok') {
      //     notification.success({
      //       message: '已发送验证码',
      //     })
      //   } else if (res.Data.message === 'exist') {
      //     notification.warning({
      //       message: '验证码未过期',
      //     })
      //   }
      // })
    },
    cancelCheck() {
      this.connectVisible = false
    },
    clickLinkTo(needCheck = true) {
      var checkData = {}
      checkData.email = store.getters.email
      checkData.code = this.verificationCode
      // localStorage.removeItem('asset_type')
      // localStorage.removeItem('allow_connect')
      // localStorage.setItem('asset_type', this.jumpHostType)
      // localStorage.setItem('allow_connect', 'true')
      // const routeData = this.$router.resolve({ path: '/desktop/session', query: { asset_ip: this.jumpAssetIp, asset_name: this.jumpHostname, asset_type: this.jumpHostType } })
      // window.open(routeData.href, '_blank')
      // 判断Email是否以robot_开头
      if (store.getters.email.indexOf('robot_') === 0) {
        this.connectVisible = false
        localStorage.removeItem('asset_type')
        localStorage.removeItem('allow_connect')
        localStorage.setItem('asset_type', this.jumpHostType)
        localStorage.setItem('allow_connect', 'true')
        const routeData = this.$router.resolve({
          path: '/desktop/session',
          query: {
            asset_ip: this.jumpAssetIp,
            asset_name: this.jumpHostname,
            asset_type: this.jumpHostType,
            asset_idc: this.desktopIdc,
          },
        })
        window.open(routeData.href, '_blank')
      } else {
        this.verifyLoading = true
        const paramsData = {
          code: this.verificationCode,
          ip: this.currentItem.desktopIp,
          type: this.plantform,
        }
        if (needCheck) {
          checkWxCode(paramsData)
            .then(res => {
              this.verifyLoading = false
              if (res.Data && res.Data.message == 'ok') {
                // 连接跳转
                this.connectVisible = false
                localStorage.removeItem('asset_type')
                localStorage.removeItem('allow_connect')
                localStorage.setItem('asset_type', this.jumpHostType)
                localStorage.setItem('allow_connect', 'true')
                const routeData = this.$router.resolve({
                  path: '/desktop/session',
                  query: {
                    asset_ip: this.jumpAssetIp,
                    asset_name: this.jumpHostname,
                    asset_type: this.jumpHostType,
                    asset_idc: this.desktopIdc,
                    force_lossless: this.forceLossless.toString(),
                  },
                })
                window.open(routeData.href, '_blank')
              } else if (res.Data && res.Data.message == 'error') {
                notification.error({
                  message: '输入验证码错误',
                })
                this.verificationCode = ''
              } else if (res.Data && res.Data.message == 'notExist') {
                this.verificationCode = ''
                notification.warning({
                  message: '验证码已过期，将为您重新发送',
                })
                sendWxCode({
                  ip: this.currentItem.desktopIp,
                  type: this.plantform,
                }).then(res => {})
              }
              console.log(res, 'rrrrrrrrr')
            })
            .finally(() => {})
        } else {
          // 连接跳转
          this.verifyLoading = false
          this.connectVisible = false
          localStorage.removeItem('asset_type')
          localStorage.removeItem('allow_connect')
          localStorage.setItem('asset_type', this.jumpHostType)
          localStorage.setItem('allow_connect', 'true')
          const routeData = this.$router.resolve({
            path: '/desktop/session',
            query: {
              asset_ip: this.jumpAssetIp,
              asset_name: this.jumpHostname,
              asset_type: this.jumpHostType,
              asset_idc: this.desktopIdc,
              force_lossless: this.forceLossless.toString(),
            },
          })
          window.open(routeData.href, '_blank')
        }

        // sslDownloadCheck(checkData)
        //   .then(res => {
        //     if (res.Data.message === 'ok') {
        //       // 连接跳转
        //       this.connectVisible = false
        //       localStorage.removeItem('asset_type')
        //       localStorage.removeItem('allow_connect')
        //       localStorage.setItem('asset_type', this.jumpHostType)
        //       localStorage.setItem('allow_connect', 'true')
        //       const routeData = this.$router.resolve({
        //         path: '/desktop/session',
        //         query: {
        //           asset_ip: this.jumpAssetIp,
        //           asset_name: this.jumpHostname,
        //           asset_type: this.jumpHostType,
        //           asset_idc: this.desktopIdc,
        //           force_lossless: this.forceLossless.toString(),
        //         },
        //       })
        //       window.open(routeData.href, '_blank')
        //     } else if (res.Data.message === 'error') {
        //       notification.error({
        //         message: '输入验证码错误',
        //       })
        //       this.verificationCode = ''
        //       // this.$nextTick(() => {
        //       //   this.$refs.codeVerify.focus()
        //       // })
        //     } else if (res.Data.message === 'notExist') {
        //       this.verificationCode = ''
        //       notification.warning({
        //         message: '验证码已过期，将为您重新发送',
        //       })
        //       sslDownloadSend({
        //         email: store.getters.email,
        //       }).then(res => {
        //         if (res.Data.message === 'ok') {
        //           notification.success({
        //             message: '已发送验证码',
        //           })
        //         } else if (res.Data.message === 'exist') {
        //           notification.warning({
        //             message: '验证码未过期',
        //           })
        //         }
        //       })
        //     }
        //   })
        //   .finally(() => {
        //     this.verifyLoading = false
        //   })
      }
    },
    onTabChange(key, type) {
      this[type] = key
    },
  },
})
</script>
