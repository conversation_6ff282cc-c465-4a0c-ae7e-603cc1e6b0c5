let timerId = null
let count = 0
function startTimer() {
  if (!timerId) {
    timerId = setInterval(() => {
      count++
      postMessage(count)
    }, 1000)
  }
}

function stopTimer() {
  clearInterval(timerId)
  timerId = null
}



self.addEventListener('message', function (e) {
  if (e.data === 'start') {
    startTimer()
  } else if (e.data === 'stop') {
    stopTimer()
  } else if (e.data === 'restart'){
    count = 0
  }
}, false)