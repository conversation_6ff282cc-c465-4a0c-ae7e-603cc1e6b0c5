<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="云桌面名">
              <a-input
                v-model:value="queryParam.desktopName"
                placeholder="请输入云桌面名"
                @pressEnter="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="云桌面IP">
              <a-input
                v-model:value="queryParam.desktopIp"
                placeholder="请输入云桌面IP"
                @pressEnter="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="云桌面类型">
              <a-select v-model:value="queryParam.desktopType" placeholder="云桌面类型" @pressEnter="$refs.table.refresh()">
                <a-select-option value="personal">远程工作站</a-select-option>
                <a-select-option value="dlp">DLP云桌面</a-select-option>
                <a-select-option value="server">服务器云桌面</a-select-option>
                <a-select-option value="remote">远程办公云桌面</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <template v-if="advanced">
            <a-col :md="6" :sm="24">
              <a-form-item label="系统">
                <a-select v-model:value="queryParam.os"  placeholder="系统类型" @pressEnter="$refs.table.refresh()">
                  <a-select-option value="windows">Windows系统</a-select-option>
                  <a-select-option value="ubuntu">Ubuntu系统</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24">
              <a-form-item label="状态">
                <a-select
                    v-model:value="queryParam.status"
                    placeholder="状态"
                    :options="statusOptions"
                    @pressEnter="$refs.table.refresh()"
                ></a-select>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="(!advanced && 4) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator">
      <tx-button type="primary" icon="plus" @click="handleAdd()">新增</tx-button>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="云桌面ID">{{ record.desktopId }}</a-descriptions-item>
          <a-descriptions-item label="CPU">{{ record.cpu }}</a-descriptions-item>
          <a-descriptions-item label="内存">{{ record.mem }}</a-descriptions-item>
          <a-descriptions-item label="是否公共云桌面">{{ record.isPublic }}</a-descriptions-item>
          <a-descriptions-item label="策略ID">{{ record.desktopPermissionId }}</a-descriptions-item>
          <a-descriptions-item label="策略">{{ record.desktopPermission.name }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'desktopType'">
          <a-badge :status="desktopTypeFilter(text)" :text="desktopFilter(text)" />
        </template>
        <template v-if="column.dataIndex == 'status'">
          <a-tag :color="statusColorFilter(record.status)">{{ statusFilter(record.status) }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex == 'action'">
          <a @click="handleUpdate(record)">更新</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item><a @click="handleReboot(record)">重启</a></a-menu-item>
                <a-menu-item v-if="record.status === 1"><a @click="handleStop(record)">关机</a></a-menu-item>
                <a-menu-item v-else-if="record.status === 2"><a @click="handleStart(record)">开机</a></a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
                    <template #icon>
                      <a-icon type="question-circle-o" style="color: red" />
                    </template>
                    <a>删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </template>
            <a>
              更多
              <a-icon type="down" />
            </a>
          </a-dropdown>
        </template>
      </template>
    </s-table>

    <a-modal
      title="新增云桌面"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="云桌面名称" name="desktopName">
          <a-input v-model:value="form.desktopName" />
        </a-form-model-item>
        <a-form-model-item label="云桌面IP" name="desktopIp">
          <a-input v-model:value="form.desktopIp" />
        </a-form-model-item>
        <a-form-model-item label="云桌面ID" name="desktopId">
          <a-input v-model:value="form.desktopId" />
        </a-form-model-item>
        <a-form-model-item label="云桌面系统" name="os">
          <a-radio-group v-model:value="form.os" button-style="solid">
            <a-radio-button value="ubuntu">Ubuntu</a-radio-button>
            <a-radio-button value="windows">Windows</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="云桌面类型" name="desktopType">
          <a-select v-model:value="form.desktopType">
            <a-select-option value="personal">远程工作站</a-select-option>
            <a-select-option value="dlp">DLP云桌面</a-select-option>
            <a-select-option value="server">服务器云桌面</a-select-option>
            <a-select-option value="remote">远程办公云桌面</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="策略名称" name="desktopPermissionId">
          <a-select v-model:value="form.desktopPermissionId" placeholder="请选择策略">
            <a-select-option v-for="item in policyNameList" :key="item.name" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="代理所在机房" name="desktopIdc">
          <a-radio-group v-model:value="form.desktopIdc" button-style="solid">
            <a-radio-button value="ucloud-shanghai2-hybrid">混合云</a-radio-button>
            <a-radio-button value="tencent">腾讯云</a-radio-button>
            <a-radio-button value="sh-office">云立方</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="公共云桌面" name="desktopType">
          <a-radio-group v-model:value="form.isPublic" button-style="solid">
            <a-radio-button :value="false">否</a-radio-button>
            <a-radio-button :value="true">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="云桌面信息更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="云桌面名称" name="desktopName">
          <a-input v-model:value="form.desktopName" disabled />
        </a-form-model-item>
        <a-form-model-item label="云桌面ID" name="desktopId">
          <a-input v-model:value="form.desktopId" />
        </a-form-model-item>
        <a-form-model-item label="云桌面IP" name="desktopIp">
          <a-input v-model:value="form.desktopIp" disabled />
        </a-form-model-item>
        <a-form-model-item label="云桌面系统" name="os">
          <a-radio-group v-model:value="form.os" button-style="solid">
            <a-radio-button value="ubuntu">Ubuntu</a-radio-button>
            <a-radio-button value="windows">Windows</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="云桌面类型" name="desktopType">
          <a-select v-model:value="form.desktopType">
            <a-select-option value="personal">远程工作站</a-select-option>
            <a-select-option value="dlp">DLP云桌面</a-select-option>
            <a-select-option value="server">服务器云桌面</a-select-option>
            <a-select-option value="remote">远程办公云桌面</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="策略名称" name="desktopPermissionId">
          <a-select v-model:value="form.desktopPermissionId" placeholder="请选择策略">
            <a-select-option v-for="item in policyNameList" :key="item.name" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="代理所在机房" name="desktopType">
          <a-radio-group v-model:value="form.desktopIdc" button-style="solid">
            <a-radio-button value="ucloud-shanghai2-hybrid">混合云</a-radio-button>
            <a-radio-button value="tencent">腾讯云</a-radio-button>
            <a-radio-button value="sh-office">云立方</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="公共云桌面" name="desktopType">
          <a-radio-group v-model:value="form.isPublic" button-style="solid">
            <a-radio-button :value="false">否</a-radio-button>
            <a-radio-button :value="true">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-drawer
      title="详情"
      placement="right"
      :closable="false"
      width="40%"
      :visible="detailIdVisible"
      @close="detailIdVisible = false"
    >
      <a-card :bordered="false" :model="detailData" ref="detailData">
        <a-descriptions :column="2" title="基础信息">
          <a-descriptions-item label="云桌面名称">{{ detailData.desktopName }}</a-descriptions-item>
          <a-descriptions-item label="云桌面IP">{{ detailData.desktopIp }}</a-descriptions-item>
          <a-descriptions-item label="云桌面系统">{{ detailData.os }}</a-descriptions-item>
          <a-descriptions-item label="云桌面类型">{{ detailData.desktopType }}</a-descriptions-item>
          <a-descriptions-item label="代理">{{ detailData.desktopIdc }}</a-descriptions-item>
          <a-descriptions-item label="是否为公共云桌面">{{ detailData.isPublic }}</a-descriptions-item>
        </a-descriptions>
        <a-divider style="margin-bottom: 32px" />
      </a-card>
    </a-drawer>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import {
  getDesktopList,
  getDesktopInfo,
  createDesktop,
  updateDesktop,
  deleteDesktop,
  rebootDesktop,
  stopDesktop,
  startDesktop,
} from '@/api/desktop/desktop'
import { getDesktopPermissionNameList } from '@/api/desktop/desktop_permission'
import { notification } from 'ant-design-vue'

const statusMap = {
  0: {
    color: 'orange',
    text: '未知',
  },
  1: {
    color: 'green',
    text: '运行中',
  },
  2: {
    color: 'red',
    text: '已关机',
  },
}


const statusOptions = [
  {
    value: 1,
    label: '运行中',
  },
  {
    value: 2,
    label: '已关机',
  },
]

const columns = [
  {
    title: '云桌面ID',
    dataIndex: 'id',
  },
  {
    title: '云桌面名',
    dataIndex: 'desktopName',
  },
  {
    title: '云桌面IP',
    dataIndex: 'desktopIp',
  },
  {
    title: '云桌面系统',
    dataIndex: 'os',
  },
  {
    title: '云桌面类型',
    dataIndex: 'desktopType',
    scopedSlots: { customRender: 'desktopType' },
  },
  {
    title: '代理',
    dataIndex: 'desktopIdc',
    scopedSlots: { customRender: 'desktopIdc' },
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const desktopTypeMap = {
  personal: {
    status: 'success',
    text: '远程工作站',
  },
  dlp: {
    status: 'error',
    text: 'DLP云桌面',
  },
  server: {
    status: 'processing',
    text: '服务器云桌面',
  },
  remote: {
    status: 'default',
    text: '远程办公云桌面',
  },
}

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'DesktopInfo',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      visible: false,
      confirmLoading: false,
      detailIdVisible: false,
      updateVisible: false,
      statusOptions,
      advanced: false,
      detailData: {},
      policyNameList: [],
      form: {
        desktopName: undefined,
        desktopIp: undefined,
        desktopId: undefined,
        os: 'ubuntu',
        desktopType: 'personal',
        isPublic: false,
        desktopIdc: 'ucloud-shanghai2-hybrid',
        desktopPermissionId: undefined,
      },
      rules: {
        desktopName: [{ required: true, message: '请输入云桌面名称', trigger: 'change' }],
        desktopIp: [{ required: true, message: '请输入云桌面IP', trigger: 'change' }],
        desktopId: [{ required: true, message: '请输入云桌面UUID', trigger: 'change' }],
        os: [{ required: true, message: '请选择系统类型', trigger: 'change' }],
        desktopType: [{ required: true, message: '请选择云桌面类型', trigger: 'change' }],
      },
      queryParam: {},
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        return getDesktopList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              res.Data.data.forEach(v => {
                if (v.isPublic === undefined) {
                  v.isPublic = false
                }
              })
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getPolicyNameListInfo()
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusColorFilter(type) {
      return statusMap[type]?.color || type
    },
    desktopFilter(type) {
      return desktopTypeMap[type]?.text || type
    },
    desktopTypeFilter(type) {
      return desktopTypeMap[type]?.status || type
    },
    getPolicyNameListInfo() {
      getDesktopPermissionNameList().then(res => {
        for (var i = 0, len = res.Data.permissionNameList.length; i < len; i++) {
          this.policyNameList = res.Data.permissionNameList
        }
      })
    },
    handleStop(record) {
      this.$confirm({
        title: '确认关机？',
        content: `${record.desktopName} ${record.desktopIp} 云桌面确认要关机？`,
        onOk() {
          stopDesktop({ desktopId: record.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '关机成功',
                  description: '云桌面关机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleStart(record) {
      this.$confirm({
        title: '确认开机？',
        content: `${record.desktopName} ${record.desktopIp} 云桌面确认要开机？，开机后请等待1分钟以后在进行连接操作。`,
        onOk() {
          startDesktop({ desktopId: record.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '开机成功',
                  description: '云桌面开机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleReboot(record) {
      this.$confirm({
        title: '确认重启？',
        content: `${record.desktopName} ${record.desktopIp} 云桌面确认要重启？，重启后请等待1分钟以后在进行连接操作。`,
        onOk() {
          rebootDesktop({ desktopId: record.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '重启成功',
                  description: '云桌面重启成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleAdd() {
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createDesktop(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      getDesktopInfo(record.id).then(res => {
        if (res.Data.isPublic === undefined) {
          res.Data.isPublic = false
        }
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateDesktop(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteDesktop(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    // 详细信息 相关接口
    handleDetail(record) {
      getDesktopInfo(record.id).then(response => {
        if (response.Data.isPublic === undefined) {
          response.Data.isPublic = false
        }
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
  },
}
</script>
