<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :tab-list="tabListNoTitle"
      :active-tab-key="noTitleKey"
      @tabChange="key => onTabChange(key, 'noTitleKey')"
    >
      <template #content>
        <tx-button icon="solution" size="small">
          <a href="https://doc.intsig.net/pages/viewpage.action?pageId=576192633" style="text-decoration: none">
            说明文档
          </a>
        </tx-button>
      </template>
      <div v-if="noTitleKey === 'remote'">
        <p>
          公司办公网络内访问：剪切板（复制，粘贴都允许），文件上传（允许），文件下载（允许）
          <br />
          非公司办公网络内访问：剪切板（复制禁用，粘贴允许），文件上传（允许），文件下载（禁用）
          <br />
          <b style="color: orangered">关机不收费</b>
          ，建议即用即开
        </p>
        <div class="table-operator">
          <tx-button type="primary" style="margin-bottom: 8px" icon="plus" @click="remoteDesktopApplication">
            远程办公云桌面申请
          </tx-button>
          <tx-button type="primary" style="margin-bottom: 8px" icon="plus" @click="remoteDesktopUrlApplication">
            远程办公URL白名单申请
          </tx-button>
          <tx-button type="primary" style="margin-bottom: 8px" icon="poweroff" @click="desktopDestroyApplication">
            云桌面注销
          </tx-button>
        </div>
        <a-list :grid="{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 4, xxl: 4, xxxl: 4 }" :data-source="remoteData">
          <template #renderItem="{ item }">
            <a-list-item style="min-width: 280px; max-width: 310px">
              <a-card hoverable>
                <template #cover>
                  <img v-if="item.os === 'windows'" :src="replace('@/assets/svgs/windows.png')" :alt="item.title" />
                  <img v-else :src="replace('@/assets/svgs/ubuntu.png')" :alt="item.title" />
                </template>
                <template #actions>
                  <tx-button v-if="item.status === '可连接'" type="primary" icon="desktop" @click="handleInfo(item)">
                    连接
                  </tx-button>
                  <tx-button v-else icon="disconnect" disabled>连不上</tx-button>
                  <tx-button
                    v-if="item.status === '可连接' && item.desktopIdc === 'tencent'"
                    icon="poweroff"
                    @click="handleStop(item)"
                  >
                    关机
                  </tx-button>
                  <tx-button
                    v-else-if="item.status === '不可连接' && item.desktopIdc === 'tencent'"
                    icon="play-circle"
                    @click="handleStart(item)"
                  >
                    开机
                  </tx-button>
                  <tx-button
                    v-else-if="item.status === '关机中' && item.desktopIdc === 'tencent'"
                    icon="loading-outlined"
                  >
                    关机中...
                  </tx-button>
                  <tx-button
                    v-else-if="item.status === '开机中' && item.desktopIdc === 'tencent'"
                    icon="loading-outlined"
                  >
                    开机中...
                  </tx-button>
                  <tx-button
                    v-else-if="item.status === '重启中' && item.desktopIdc === 'tencent'"
                    icon="loading-outlined"
                    hover="none"
                  >
                    重启中...
                  </tx-button>
                  <tx-button
                    v-else-if="item.status === '后台处理中' && item.desktopIdc === 'tencent'"
                    icon="loading-outlined"
                    pointer-events="none"
                  >
                    处理中...
                  </tx-button>
                  <tx-button v-else icon="poweroff" disabled>关机</tx-button>
                  <a-dropdown v-if="item.desktopIdc === 'tencent'">
                    <template #overlay>
                      <a-menu>
                        <a-menu-item v-if="item.desktopIdc === 'tencent'">
                          <a @click="handleReboot(item)">重启</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a @click="handleCreateImage(item)">系统备份</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a @click="handleCronStop(item)">
                            智能关机
                            <span v-if="item.cronStopStatus === 1">（开启）</span>
                            <span v-else>（关闭）</span>
                          </a>
                        </a-menu-item>
                        <a-menu-item>
                          <a @click="handleScheduled(item)">
                            计划任务
                            <span v-if="item.cronStatus !== 2">（开启）</span>
                            <span v-else>（关闭）</span>
                          </a>
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <tx-button>
                      更多
                      <a-icon type="down" />
                    </tx-button>
                  </a-dropdown>
                  <tx-button v-else disabled>
                    更多
                    <a-icon type="down" />
                  </tx-button>
                </template>

                <a-card-meta :title="item.desktopName">
                  <template #description v-if="item.desktopPermissionId > 0">
                    {{ item.desktopIp }}
                    <span style="color: #108ee9">({{ item.desktopPermission.name }}场景)</span>
                  </template>
                  <template #description v-else>
                    {{ item.desktopIp }}
                    <span style="color: #f50">(未关联场景)</span>
                  </template>
                  <template #avatar>
                    <a-avatar v-if="item.os === 'windows'" shape="square" :src="replace('@/assets/svgs/windows.svg')" />
                    <a-avatar v-else :src="replace('@/assets/svgs/ubuntu.svg')" />
                  </template>
                </a-card-meta>
              </a-card>
            </a-list-item>
          </template>
        </a-list>
      </div>
      <div v-else-if="noTitleKey === 'server'">
        <p>使用远程桌面的方式访问线上服务器。水印（开启），录屏审计（开启），剪切板（禁用）</p>
        <a-list :grid="{ gutter: 16, column: 4 }" :data-source="serverData">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item>
                <a-card hoverable>
                  <template #cover>
                    <img v-if="item.os === 'windows'" :src="replace('@/assets/svgs/windows.png')" :alt="item.title" />
                    <img v-else :src="replace('@/assets/svgs/ubuntu.png')" :alt="item.title" />
                  </template>
                  <template #actions>
                    <tx-button v-if="item.status === '可连接'" type="primary" icon="desktop" @click="handleInfo(item)">
                      连接
                    </tx-button>
                    <tx-button v-else icon="disconnect" disabled>连不上</tx-button>
                    <tx-button
                      v-if="item.status === '可连接' && item.desktopIdc === 'tencent'"
                      icon="poweroff"
                      @click="handleStop(item)"
                    >
                      关机
                    </tx-button>
                    <tx-button
                      v-else-if="item.status === '不可连接' && item.desktopIdc === 'tencent'"
                      icon="play-circle"
                      @click="handleStart(item)"
                    >
                      开机
                    </tx-button>
                    <tx-button v-else icon="poweroff" disabled>关机</tx-button>
                    <a-dropdown v-if="item.desktopIdc === 'tencent'">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item v-if="item.status === '可连接' && item.desktopIdc === 'tencent'">
                            <a @click="handleReboot(item)">重启</a>
                          </a-menu-item>
                          <a-menu-item>
                            <a @click="handleCreateImage(item)">系统备份</a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <tx-button>
                        更多
                        <a-icon type="down" />
                      </tx-button>
                    </a-dropdown>
                    <tx-button v-else disabled>
                      更多
                      <a-icon type="down" />
                    </tx-button>
                  </template>
                  <a-card-meta :title="item.desktopName">
                    <template #description v-if="item.desktopPermissionId > 0">
                      {{ item.desktopIp }}
                      <span style="color: #108ee9">({{ item.desktopPermission.name }}场景)</span>
                    </template>
                    <template #description v-else>
                      {{ item.desktopIp }}
                      <span style="color: #f50">(未关联场景)</span>
                    </template>
                    <template #avatar>
                      <a-avatar
                        v-if="item.os === 'windows'"
                        shape="square"
                        :src="replace('@/assets/svgs/windows.svg')"
                      />
                      <a-avatar v-else :src="replace('@/assets/svgs/ubuntu.svg')" />
                    </template>
                  </a-card-meta>
                </a-card>
              </a-list-item>
            </a-list-item>
          </template>
        </a-list>
      </div>
      <a-modal
        :maskClosable="false"
        :width="600"
        @cancel="plabTeskCancel"
        v-model:visible="scheduleVisible"
        title="计划任务"
      >
        <template #footer>
          <!-- <a-button html-type="submit" @click="scheduleVisible = false">取消</a-button> -->
        </template>
        <!-- @handleOk="handleClose" -->
        <ScheduleTesk v-if="scheduleVisible" :teskData="teskData" @handleCancel="scheduleVisible = false" />
      </a-modal>
      <a-modal title="企业微信(合小云)动态验证码" :visible="connectVisible" @ok="clickLinkTo" @cancel="cancelCheck">
        <a-spin v-if="verifyLoading" />
        <a-input
          ref="codeVerify"
          v-else
          @pressEnter="enterClick"
          v-model:value="verificationCode"
          @change="codeChange"
          placeholder="请输入验证码"
        ></a-input>
        <p style="margin-top: 5%; font-size: 15px">连接选项:</p>
        <p style="margin-top: 3%; margin-left: 5%">
          无损压缩
          <a-tooltip>
            <template #title>开启无损压缩，可以提升云桌面的画质，但是云桌面的操作流畅度会有所损失</template>
            <a-icon type="question-circle" />
          </a-tooltip>
          <a-switch
            style="margin-left: 1%"
            v-model:checked="forceLossless"
            checked-children="开"
            un-checked-children="关"
          />
        </p>
      </a-modal>
      <a-modal
        title="智能关机(30分钟以上未使用自动关机)"
        :visible="cronStopVisible"
        :confirm-loading="confirmLoading"
        @ok="handleCronStopOk"
        @cancel="handleCronStopCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item name="desktopName" label="名称">
            <a-input v-model:value="form.desktopName" disabled />
          </a-form-item>
          <a-form-item name="desktopIp" label="IP">
            <a-input v-model:value="form.desktopIp" disabled />
          </a-form-item>
          <a-form-item name="cronStopStatus" label="智能关机">
            <a-radio-group v-model:value="form.cronStopStatus" button-style="solid">
              <a-radio-button :value="1">开启</a-radio-button>
              <a-radio-button :value="2">关闭</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { judgeEnv } from '@/utils/util'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import ScheduleTesk from './components/ScheduleTesk.vue'
import { sslDownloadCheck, sslDownloadSend, sendWxCode, checkWxCode } from '@/api/domain/domain'
import { notification } from 'ant-design-vue'
import { getDesktopUserList, getRemoteDesktopUserList } from '@/api/desktop/desktop_user'
import { rebootDesktop, startDesktop, stopDesktop, createImageDesktop, updateCronStop } from '@/api/desktop/desktop'
export default {
  components: {
    ScheduleTesk,
  },
  data() {
    var ua = window.navigator.userAgent.toLowerCase()
    let plantform = 'web'
    if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
      plantform = 'qw'
    }
    return {
      plantform,
      currentItem: '',
      tabListNoTitle: [
        {
          key: 'remote',
          tab: '远程办公云桌面',
        },
        {
          key: 'server',
          tab: '服务器云桌面',
        },
      ],
      noTitleKey: 'remote',
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      verifyLoading: false,
      connectVisible: false,
      cronStopVisible: false,
      confirmLoading: false,
      verificationCode: '',
      jumpAssetIp: '',
      visible: false,
      forceLossless: false,
      sessionId: '',
      jumpHostname: '',
      desktopIdc: '',
      jumpHostType: '',
      remoteData: [],
      data: [],
      form: {},
      rules: {
        cronStopStatus: [{ required: true, message: '请选择是否开启或关闭智能关机', trigger: 'change' }],
      },
      CronStatus: false,
      scheduleVisible: false,
      // 循环调用的两个条件
      needGetDesktopStatus: false,
      intervalId: null,
      teskData: {},
      serverData: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetDesktopUserList()
  },
  watch: {
    verificationCode: {
      handler: function (val) {
        if (val) {
          if (val.length === 6) {
            this.clickLinkTo()
          } else if (val.length > 6) {
            notification.error({
              message: '输入六位验证码',
            })
          }
        }
      },
    },
    needGetDesktopStatus: {
      handler: function (val) {
        if (!this.intervalId && val) {
          this.startLoop()
        } else {
          this.stopLoop()
        }
      },
    },
  },
  methods: {
    plabTeskCancel() {
      // console.log(11111111111111111);
      this.remoteData = []
      this.GetDesktopUserList()
    },
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    codeChange(val) {},
    enterClick() {
      this.clickLinkTo()
    },
    GetDesktopUserList() {
      this.remoteData = []
      getRemoteDesktopUserList({ email: store.getters.email }).then(res => {
        if (res.Data.hasOwnProperty('data')) {
          if (res && res.Data && res.Data.data && res.Data.data.length > 0) {
            this.data = res.Data.data[0].desktops
            for (let i = 0; i < this.data.length; i++) {
              if (![1, 2, 6].includes(this.data[i].status)) {
                this.needGetDesktopStatus = true
                break
              } else {
                this.needGetDesktopStatus = false
              }
            }
            console.log(this.needGetDesktopStatus)
            this.data.forEach(item => {
              if (item.status === 1) {
                item.status = '可连接'
              } else if (item.status === 2) {
                item.status = '不可连接'
              } else if (item.status === 3) {
                item.status = '关机中'
              } else if (item.status === 4) {
                item.status = '开机中'
              } else if (item.status === 5) {
                item.status = '重启中'
              } else if (item.status === 7) {
                item.status = '后台处理中'
              } else if (item.status === 6) {
                item.status = '状态异常'
              }
              if (item.desktopType === 'remote') {
                this.remoteData.push(item)
              }
            })
          }
        }
      })
    },
    GetServerDesktopList() {
      getDesktopUserList({ email: store.getters.email }).then(res => {
        this.serverData = []
        if (res.Data.hasOwnProperty('data')) {
          if (res && res.Data && res.Data.data && res.Data.data.length > 0) {
            this.data = res.Data.data[0].desktops
            this.data.forEach(item => {
              if (item.status === 1) {
                item.status = '可连接'
              } else {
                item.status = '不可连接'
              }
              console.log(12121)
              console.log(item)
              if (item.desktopType === 'server') {
                this.serverData.push(item)
              }
            })
          }
        }
      })
    },
    remoteDesktopApplication() {
      this.$router.push({ path: '/workflow/remote-desktop' })
    },
    remoteDesktopUrlApplication() {
      this.$router.push({ path: '/workflow/remote-desktop-url' })
    },
    desktopDestroyApplication() {
      this.$router.push({ path: '/workflow/desktop-destroy' })
    },
    startLoop() {
      this.intervalId = setInterval(this.GetDesktopUserList, 10000)
    },
    stopLoop() {
      clearInterval(this.intervalId)
    },
    handleStop(item) {
      this.$confirm({
        title: '确认关机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要关机？`,
        onOk() {
          stopDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '关机成功',
                  description: '云桌面关机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleStart(item) {
      this.$confirm({
        title: '确认开机？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要开机？，开机后请等待1分钟以后在进行连接操作。`,
        onOk() {
          startDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '开机成功',
                  description: '云桌面开机成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleReboot(item) {
      this.$confirm({
        title: '确认重启？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面确认要重启？，重启后请等待1分钟以后在进行连接操作。`,
        onOk() {
          rebootDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '重启成功',
                  description: '云桌面重启成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleCreateImage(item) {
      this.$confirm({
        title: '确认系统备份？',
        content: `${item.desktopName} ${item.desktopIp} 云桌面制作系统镜像用于备份？`,
        onOk() {
          createImageDesktop({ desktopId: item.desktopId })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '系统备份成功',
                  description: '云桌面系统备份成功',
                })
                setTimeout(() => {
                  location.reload()
                }, 1000)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    handleCronStop(item) {
      this.form = item
      this.cronStopVisible = true
    },
    handleCronStopOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateCronStop(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.cronStopVisible = false
              this.confirmLoading = false
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleCronStopCancel(e) {
      this.cronStopVisible = false
      this.$refs.table.refresh()
      this.$message.success('更新成功')
    },
    handleClose() {
      this.remoteData = []
      this.GetDesktopUserList()
      // this.scheduleVisible = false
    },
    // 定时开关机任务
    handleScheduled(item) {
      this.scheduleVisible = true
      this.teskData = item
    },
    handleInfo(item) {
      this.currentItem = item
      if (store.getters.email.indexOf('robot_') === 0) {
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
        this.clickLinkTo()
        return
      }
      if (item !== 'repeated') {
        this.verificationCode = ''
        this.jumpAssetIp = item.desktopIp
        this.jumpHostname = item.desktopName
        this.jumpHostType = item.desktopType
        if (item.desktopIdc === null) {
          this.desktopIdc = ''
        } else {
          this.desktopIdc = item.desktopIdc
        }
      }
      this.connectVisible = true
      var sendData = {}
      sendData.email = store.getters.email
      sendWxCode({
        ip: item.desktopIp,
        type: this.plantform,
      }).then(res => {
        console.log(res, 'resssss')
        // 非neeCheck
        if (res.Data && !res.Data.needCheck) {
          //
          this.clickLinkTo(false)
        } else if (res.Data && res.Data.needCheck) {
          this.connectVisible = true
        }
      })
      // sslDownloadSend(sendData).then(res => {
      //   if (res.Data.message === 'ok') {
      //     notification.success({
      //       message: '已发送验证码',
      //     })
      //   } else if (res.Data.message === 'exist') {
      //     notification.warning({
      //       message: '验证码未过期',
      //     })
      //   }
      // })
    },
    cancelCheck() {
      this.connectVisible = false
    },
    clickLinkTo(needCheck = true) {
      var checkData = {}
      checkData.email = store.getters.email
      checkData.code = this.verificationCode
      if (store.getters.email.indexOf('robot_') === 0) {
        this.connectVisible = false
        localStorage.removeItem('asset_type')
        localStorage.removeItem('allow_connect')
        localStorage.setItem('asset_type', this.jumpHostType)
        localStorage.setItem('allow_connect', 'true')
        const routeData = this.$router.resolve({
          path: '/desktop/session',
          query: {
            asset_ip: this.jumpAssetIp,
            asset_name: this.jumpHostname,
            asset_type: this.jumpHostType,
            asset_idc: this.desktopIdc,
          },
        })
        var link = document.createElement('a')
        const env = judgeEnv()
        switch (env) {
          case 'DEV':
            link.href = `http://localhost:8080/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
              this.jumpHostname
            }&asset_type=${this.jumpHostType}&asset_idc=${
              this.desktopIdc
            }&force_lossless=${this.forceLossless.toString()}`
            break
          case 'TEST':
            link.href = `https://cloud-test.intsig.net/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
              this.jumpHostname
            }&asset_type=${this.jumpHostType}&asset_idc=${
              this.desktopIdc
            }&force_lossless=${this.forceLossless.toString()}`
            break
          case 'ONLINE':
            link.href = `https://cloud.intsig.net/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
              this.jumpHostname
            }&asset_type=${this.jumpHostType}&asset_idc=${
              this.desktopIdc
            }&force_lossless=${this.forceLossless.toString()}`
            break
        }
        link.target = '_blank'
        link.rel = 'noopener noreferrer'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // window.open(routeData.href, '_blank')
      } else {
        this.verifyLoading = true

        const paramsData = {
          code: this.verificationCode,
          ip: this.currentItem.desktopIp,
          type: this.plantform,
        }
        if (needCheck) {
          checkWxCode(paramsData)
            .then(res => {
              this.verifyLoading = false
              if (res.Data && res.Data.message == 'ok') {
                this.connectVisible = false
                localStorage.removeItem('asset_type')
                localStorage.removeItem('allow_connect')
                localStorage.setItem('asset_type', this.jumpHostType)
                localStorage.setItem('allow_connect', 'true')
                // const routeData = this.$router.resolve({
                //   path: '/desktop/session',
                //   query: {
                //     asset_ip: this.jumpAssetIp,
                //     asset_name: this.jumpHostname,
                //     asset_type: this.jumpHostType,
                //     asset_idc: this.desktopIdc,
                //     force_lossless: this.forceLossless.toString(),
                //   },
                // })
                // window.open(routeData.href, '_blank')

                var link = document.createElement('a')
                const env = judgeEnv()
                switch (env) {
                  case 'DEV':
                    link.href = `http://localhost:8080/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
                      this.jumpHostname
                    }&asset_type=${this.jumpHostType}&asset_idc=${
                      this.desktopIdc
                    }&force_lossless=${this.forceLossless.toString()}`
                    break
                  case 'TEST':
                    link.href = `https://cloud-test.intsig.net/desktop/session?asset_ip=${
                      this.jumpAssetIp
                    }&asset_name=${this.jumpHostname}&asset_type=${this.jumpHostType}&asset_idc=${
                      this.desktopIdc
                    }&force_lossless=${this.forceLossless.toString()}`
                    break
                  case 'ONLINE':
                    link.href = `https://cloud.intsig.net/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
                      this.jumpHostname
                    }&asset_type=${this.jumpHostType}&asset_idc=${
                      this.desktopIdc
                    }&force_lossless=${this.forceLossless.toString()}`
                    break
                }
                link.target = '_blank'
                link.rel = 'noopener noreferrer'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
              } else if (res.Data && res.Data.message == 'error') {
                notification.error({
                  message: '输入验证码错误',
                })
                this.verificationCode = ''
              } else if (res.Data && res.Data.message == 'notExist') {
                this.verificationCode = ''
                notification.warning({
                  message: '验证码已过期，将为您重新发送',
                })
                sendWxCode({
                  ip: this.currentItem.desktopIp,
                  type: this.plantform,
                }).then(res => {})
              }
              console.log(res, 'rrrrrrrrr')
            })
            .finally(() => {})
        } else {
          // 连接跳转
          this.verifyLoading = false
          this.connectVisible = false
          localStorage.removeItem('asset_type')
          localStorage.removeItem('allow_connect')
          localStorage.setItem('asset_type', this.jumpHostType)
          localStorage.setItem('allow_connect', 'true')
          const routeData = this.$router.resolve({
            path: '/desktop/session',
            query: {
              asset_ip: this.jumpAssetIp,
              asset_name: this.jumpHostname,
              asset_type: this.jumpHostType,
              asset_idc: this.desktopIdc,
              force_lossless: this.forceLossless.toString(),
            },
          })

          var link = document.createElement('a')
          const env = judgeEnv()
          switch (env) {
            case 'DEV':
              link.href = `http://localhost:8080/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
                this.jumpHostname
              }&asset_type=${this.jumpHostType}&asset_idc=${
                this.desktopIdc
              }&force_lossless=${this.forceLossless.toString()}`
              break
            case 'TEST':
              link.href = `https://cloud-test.intsig.net/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
                this.jumpHostname
              }&asset_type=${this.jumpHostType}&asset_idc=${
                this.desktopIdc
              }&force_lossless=${this.forceLossless.toString()}`
              break
            case 'ONLINE':
              link.href = `https://cloud.intsig.net/desktop/session?asset_ip=${this.jumpAssetIp}&asset_name=${
                this.jumpHostname
              }&asset_type=${this.jumpHostType}&asset_idc=${
                this.desktopIdc
              }&force_lossless=${this.forceLossless.toString()}`
              break
          }
          link.target = '_blank'
          link.rel = 'noopener noreferrer'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          // window.open(routeData.href, '_blank')
        }
      }
    },
    onTabChange(key, type) {
      this[type] = key
      if (key === 'server') {
        this.GetServerDesktopList()
      }
    },
  },
  destroyed() {
    removeWatermark()
  },
}
</script>
