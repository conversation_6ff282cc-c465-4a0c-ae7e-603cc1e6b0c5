<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="策略名称">
              <a-select
                v-model:value="queryParam.name"
                placeholder="请选择策略名称"
                allowClear
                showSearch
                :options="nameList"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator">
      <tx-button type="primary" icon="plus" @click="handleAdd()">新增</tx-button>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="(record) => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
          <a-descriptions-item label="来源IP列表" :span="2">{{ record.sourceIp }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{column, record, text}">
      <template v-if="column.dataIndex == 'recording'">
        <a-badge :status="recordingStatusTypeFilter(text)" :text="recordingStatusFilter(text)"/>
      </template>
      <template v-else-if="column.dataIndex == 'enablePrinting'">
        <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)"/>
      </template>
      <template v-else-if="column.dataIndex == 'disableDownload'">
        <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)"/>
      </template>
      <template v-else-if="column.dataIndex == 'disableUpload'">
        <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)"/>
      </template>
      <template v-else-if="column.dataIndex == 'disableCopy'">
        <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)"/>
      </template>
      <template v-else-if="column.dataIndex == 'disablePaste'">
        <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)"/>
      </template>
      <template v-else-if="column.dataIndex == 'action'">
        <a @click="handleUpdate(record)">更新</a>
        <a-divider type="vertical"/>
        <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
          <template #icon>
            <a-icon type="question-circle-o" style="color: red"/>
          </template>
          <a>删除</a>
        </a-popconfirm>
      </template>
      </template>
    </s-table>

    <a-modal
      title="新增策略"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="策略名称" name="name">
          <a-input v-model:value="form.name"/>
        </a-form-model-item>
        <a-form-model-item label="录屏审计" name="recording">
          <a-radio-group v-model:value="form.recording" button-style="solid">
            <a-radio-button :value="1">开启</a-radio-button>
            <a-radio-button :value="2">关闭</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="文件上传" name="disableUpload">
          <a-radio-group v-model:value="form.disableUpload" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="文件下载" name="disableDownload">
          <a-radio-group v-model:value="form.disableDownload" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="复制操作" name="disableCopy">
          <a-radio-group v-model:value="form.disableCopy" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="粘贴操作" name="disablePaste">
          <a-radio-group v-model:value="form.disablePaste" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="打印操作" name="enablePrinting">
          <a-radio-group v-model:value="form.enablePrinting" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="管理员" name="admin">
          <a-input v-model:value="form.admin"/>
        </a-form-model-item>
        <a-form-model-item label="来源IP列表" name="sourceIp">
          <a-input v-model:value="form.sourceIp"/>
        </a-form-model-item>
        <a-form-model-item label="备注" name="comment">
          <a-input v-model:value="form.comment"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="更新策略"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="策略名称" name="name">
          <a-input v-model:value="form.name" disabled/>
        </a-form-model-item>
        <a-form-model-item label="录屏审计" name="recording">
          <a-radio-group v-model:value="form.recording" button-style="solid">
            <a-radio-button :value="1">开启</a-radio-button>
            <a-radio-button :value="2">关闭</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="文件上传" name="disableUpload">
          <a-radio-group v-model:value="form.disableUpload" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="文件下载" name="disableDownload">
          <a-radio-group v-model:value="form.disableDownload" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="复制操作" name="disableCopy">
          <a-radio-group v-model:value="form.disableCopy" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="粘贴操作" name="disablePaste">
          <a-radio-group v-model:value="form.disablePaste" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="打印操作" name="enablePrinting">
          <a-radio-group v-model:value="form.enablePrinting" button-style="solid">
            <a-radio-button :value="1">允许</a-radio-button>
            <a-radio-button :value="2">不允许</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="管理员" name="admin">
          <a-input v-model:value="form.admin"/>
        </a-form-model-item>
        <a-form-model-item label="来源IP列表" name="sourceIp">
          <a-input v-model:value="form.sourceIp"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

  </a-card>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
  import { STable, Ellipsis } from '@/components'
  import { removeWatermark, setWaterMark } from '@/utils/watermark'
  import store from '@/store'
  import {
    getDesktopPermissionList,
    getDesktopPermissionInfo,
    createDesktopPermission,
    updateDesktopPermission,
    deleteDesktopPermission,
    getDesktopPermissionNameList
  } from '@/api/desktop/desktop_permission'

  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name'
    },
    {
      title: '录屏审计',
      dataIndex: 'recording',
      scopedSlots: { customRender: 'recording' }
    },
    {
      title: '文件上传',
      dataIndex: 'disableUpload',
      scopedSlots: { customRender: 'disableUpload' }
    },
    {
      title: '文件下载',
      dataIndex: 'disableDownload',
      scopedSlots: { customRender: 'disableDownload' }
    },
    {
      title: '复制操作',
      dataIndex: 'disableCopy',
      scopedSlots: { customRender: 'disableCopy' }
    },
    {
      title: '粘贴操作',
      dataIndex: 'disablePaste',
      scopedSlots: { customRender: 'disablePaste' }
    },
    {
      title: '打印',
      dataIndex: 'enablePrinting',
      scopedSlots: { customRender: 'enablePrinting' }
    },
    {
      title: '管理员',
      dataIndex: 'admin'
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '120px',
      scopedSlots: { customRender: 'action' }
    }
  ]

  const statusMap = {
    2: {
      status: 'error',
      text: '不允许'
    },
    1: {
      status: 'success',
      text: '允许'
    }
  }

  const recordingStatusMap = {
    2: {
      status: 'error',
      text: '关闭'
    },
    1: {
      status: 'success',
      text: '开启'
    }
  }

  const pagination = {
    showTotal: total => `共 ${total} 条`
  }

  export default {
    name: 'DesktopPermission',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.pagination = pagination
      return {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        nameList: [],
        visible: false,
        confirmLoading: false,
        updateVisible: false,
        desktops: [{}],
        form: {
          name: '',
          enablePrinting: 2,
          disableDownload: 2,
          disableUpload: 2,
          disableCopy: 2,
          disablePaste: 2,
          recording: 1,
          admin: '',
          sourceIp: '',
          comment: ''
        },
        rules: {
          'name': [{ required: true, message: '请输入策略名称', trigger: 'change' }],
          'enablePrinting': [{ required: true, message: '请选择', trigger: 'blur' }],
          'disableDownload': [{ required: true, message: '请选择', trigger: 'blur' }],
          'disableUpload': [{ required: true, message: '请选择', trigger: 'blur' }],
          'disableCopy': [{ required: true, message: '请选择', trigger: 'blur' }],
          'disablePaste': [{ required: true, message: '请选择', trigger: 'blur' }],
          'recording': [{ required: true, message: '请选择', trigger: 'blur' }],
          'admin': [{ required: true, message: '请输入管理员邮箱', trigger: 'blur' }],
          'sourceIp': [{ required: true, message: '请输入来源IP列表', trigger: 'blur' }]
        },
        advanced: false,
        queryParam: {},
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return getDesktopPermissionList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
      this.getNameListInfo()
    },
    destroyed () {
      removeWatermark()
    },
    methods: {
      recordingStatusFilter (type) {
        return recordingStatusMap[type]?.text || type
      },
      recordingStatusTypeFilter (type) {
        return recordingStatusMap[type]?.status || type
      },
      statusFilter (type) {
        return statusMap[type]?.text || type
      },
      statusTypeFilter (type) {
        return statusMap[type]?.status || type
      },
      getNameListInfo () {
        getDesktopPermissionNameList().then((res) => {
          for (var i = 0, len = res.Data.permissionNameList.length; i < len; i++) {
            var name = {}
            name.value = res.Data.permissionNameList[i].name
            name.label = res.Data.permissionNameList[i].name
            this.nameList.push(name)
          }
        })
      },
      handleAdd () {
        this.visible = true
      },
      handleOk (e) {
        this.confirmLoading = true
        antdFormValidate(this.$refs.ruleForm, valid => {
          if (valid) {
            createDesktopPermission(this.form).then(res => {
              if (res === undefined) {
                this.confirmLoading = false
                this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
              } else {
                this.visible = false
                this.confirmLoading = false
                this.$refs.table.refresh()
                this.$message.success('创建成功')
              }
            })
          } else {
            this.$message.error('创建失败')
            this.confirmLoading = false
          }
        })
      },
      handleCancel (e) {
        this.visible = false
      },
      handleUpdate (record) {
        getDesktopPermissionInfo(record.id).then((res) => {
          this.form = res.Data
        })
        this.updateVisible = true
      },
      handleUpdateOk (e) {
        this.confirmLoading = true
        antdFormValidate(this.$refs.ruleForm, valid => {
          if (valid) {
            updateDesktopPermission(this.form).then(res => {
              if (res === undefined) {
                this.confirmLoading = false
                this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
              } else {
                this.updateVisible = false
                this.confirmLoading = false
                this.$refs.table.refresh()
                this.$message.success('更新成功')
              }
            })
          } else {
            this.$message.error('更新失败')
            this.confirmLoading = false
          }
        })
      },
      handleUpdateCancel (e) {
        this.updateVisible = false
      },
      handleDel (record) {
        deleteDesktopPermission(record.id).then((res) => {
          if (res.Data.message === 'ok') {
            this.$refs.table.refresh(true)
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        })
      },
      filterOption: filterLabelValue,
      /*
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      }
      */
    }
  }
</script>
