<template>
  <a-space class="position-relative">
    <FilterSelect
      v-model="model.app_name"
      label="应用名称"
      :options="appNameOptions"
      :multiple="true"
      placeholder="请选择"
      @change="itemChange"
    />
    <FilterTree
      v-model="model.model"
      label="模型"
      :options="modelOptions"
      :multiple="true"
      placeholder="请选择"
      @change="changeModel"
    />
    <FilterUser
      v-if="isAdminOrSec"
      v-model="model.applicant_email"
      label="申请人"
      :multiple="true"
      placeholder="请选择"
      @change="itemChange"
    />
    <FilterUser
      v-model="model.department"
      label="使用部门"
      mode="department"
      :multiple="true"
      placeholder="请选择"
      @change="itemChange"
    />
  </a-space>
</template>

<script lang="ts">
import FilterSelect from './FilterSelect.vue'
import FilterTree from './FilterTree.vue'
import FilterUser from './FilterUser.vue'
import store from '@/store'
export default {
  name: 'AnalysisFilter',
  props: {
    model: {
      type: Object,
      default: () => ({}),
    },
    appNameOptions: {
      type: Array,
      default: () => [],
    },
    modelOptions: {
      type: Array,
      default: () => [],
    },
    isAdminOrSec: {
      type: Boolean,
    },
  },
  components: {
    FilterSelect,
    FilterTree,
    FilterUser,
  },
  data() {
    const { userTree, departTree } = store.state.orgData
    return {
      userOptions: userTree,
      departOptions: departTree,
    }
  },
  mounted() {
    // this.initData()
  },
  methods: {
    // async initData() {
    //   if (!this.userOptions || !this.userOptions.length) {
    //     await store.dispatch('GetAllOrgUser')
    //     this.$nextTick(() => {
    //       const { userTree, departTree } = store.state.orgData
    //       this.userOptions = userTree
    //       this.departOptions = departTree
    //     })
    //   }
    // },
    itemChange() {
      this.$emit('change', this.model)
    },
    changeAppName(value) {
      this.model.app_name = value
      this.$emit('change', this.model)
    },
    // changeUser(value) {
    //   console.log('user', value)
    //   this.model.applicant_email = value
    //   this.$emit('change', this.model)
    // },
    // changeDepart(value) {
    //   console.log('department', value)
    //   this.model.department = value
    //   this.$emit('change', this.model)
    // },
    changeModel(value) {
      this.model.model = value
      this.$emit('change', this.model)
    },
  },
}
</script>
