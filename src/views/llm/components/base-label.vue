<template>
  <span
    :class="{
      'base-label': true,
      focus: focus,
      active: active,
    }"
    @click="changePopover"
  >
    {{ label }}
    <span class="base-label-icon">
      <slot name="icon">
        <a-icon class="icon-arrow" type="caret-down" />
      </slot>
    </span>
  </span>
</template>

<script lang="ts">
export default {
  name: 'BaseLabel',
  props: {
    focus: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
  },
  methods: {
    changePopover() {
      this.$emit('change')
    },
  },
}
</script>
<style lang="less">
.base-label {
  padding: 0 12px;
  display: inline-block;
  white-space: nowrap;
  line-height: 32px;
  border-radius: 8px;
  color: #515977;
  cursor: pointer;
  transition: all 0.2s linear;
  &:hover {
    background: #f1f4fa;
  }
  &-icon {
    padding: 0 2px;
    color: #becae4;
  }
  &.active,
  &.active &-icon {
    color: #4070ff;
  }

  &-icon .icon-arrow {
    transition: all 0.2s linear;
  }
  &.focus .icon-arrow {
    transform: rotate(-180deg);
  }
}
</style>
