<template>
  <a-popover
    v-model:visible="data.visible"
    trigger="click"
    placement="bottomRight"
    overlayClassName="filter-select-panel"
    :getPopupContainer="popupParent"
  >
    <template #content>
      <div
        class="filter-select-content"
        :style="{
          width: width + 'px',
        }"
      >
        <a-spin v-if="data.loading" :spinning="true" tip="加载中..."></a-spin>
        <a-tree-select
          v-else
          :value="data.value"
          show-search
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :placeholder="placeholder"
          allow-clear
          tree-checkable
          tree-default-expand-all
          :multiple="multiple"
          :tree-data="options"
          :open="data.dropdownVisible"
          :maxTagCount="5"
          :getPopupContainer="popupParent"
          @change="change"
        ></a-tree-select>
      </div>
    </template>
    <BaseLabel :label="label" :focus="data.visible" :active="isActive" @change="visibleToggle" />
  </a-popover>
</template>

<script setup>
import { computed, reactive, watch } from 'vue'
import { popupParent } from '@aim/helper'
import BaseLabel from './base-label.vue'

const emits = defineEmits(['change', 'update:modelValue'])
const props = defineProps({
  label: noc.propStringVoid(),
  modelValue: noc.propAny(),
  multiple: noc.propBoolean(),
  options: noc.propArray(),
  placeholder: noc.propString(),
  width: noc.propNumber(280),
})
const data = reactive({
  dropdownVisible: false,
  loading: true,
  value: props.modelValue,
  visible: false,
})
const isActive = computed(() => {
  return props.multiple ? (props.modelValue || []).length > 0 : !!props.modelValue
})

watch(
  () => props.modelValue,
  value => {
    data.value = value
  }
)

watch(
  () => data.visible,
  visible => {
    if (visible) {
      data.loading = true
      const timer = setTimeout(() => {
        if (data.visible) {
          data.dropdownVisible = true
          data.loading = false
        } else {
          clearTimeout(timer)
        }
      }, 10)
    } else {
      data.dropdownVisible = false
    }
  }
)

function change(value) {
  console.log(value, 'value')
  emits('update:modelValue', value)
  emits('change', value)
}

function visibleToggle() {
  data.visible = !data.visible
}
</script>
<style lang="less" scoped>
.filter-select-content {
  padding: 0;
  /* padding: 12px 16px; */
  ::v-deep .ant-input,
  ::v-deep .ant-input-number,
  ::v-deep .ant-calendar-picker,
  ::v-deep .ant-select {
    display: inline-block;
    width: 100% !important;
    border-radius: 8px;
  }
  ::v-deep .ant-select-selection {
    border-radius: 8px;
  }
  :deep(.ant-select-selector ~ div) {
    margin-top: 4px;
    top: 100% !important;
  }

  :deep(.ant-select-dropdown-placement-bottomLeft) {
    left: 0 !important;
    top: 0 !important;
  }
}
</style>

<style lang="less">
.filter-select-panel {
  .ant-popover-inner-content {
    padding: 0;
  }
}
</style>
