<template>
  <a-tree-select
    ref="tree"
    :dropdown-class-name="'user-select-dropdown'"
    :class="{
      'user-select': true,
      'user-select--multiple': multiple,
    }"
    :value="selectValue"
    :tree-data="treeData"
    :open="openVisible"
    allow-clear
    show-search
    :multiple="multiple"
    :placeholder="placeholder"
    :filterTreeNode="filterTreeNode"
    :treeCheckStrictly="multiple"
    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-checkable="multiple"
    :labelInValue="true"
    :replaceFields="fieldNames"
    :getPopupContainer="triggerNode => triggerNode.parentNode"
    @change="onChange"
    @focus="onFocus"
    @blur="onBlur"
  >
    <template #tagRender="{ label, closable, onClose, option }">
      <template v-if="uniqueValues.includes(option.value)">
        <a-tag v-if="option.isDepart" :closable="closable" style="margin-right: 3px" @close="onClose">
          <template #icon>
            <img src="./depart.png" loading="lazy" class="avatar-img" alt="" />
          </template>
          <span class="short-label">{{ option.name }}</span>
          <span class="full-label">{{ option.fullLabel }}</span>
        </a-tag>
        <a-tag v-else :closable="closable" style="margin-right: 3px" @close="onClose">
          <template #icon>
            <img v-if="option.avatar" :src="option.avatar" loading="lazy" class="avatar-img" alt="" />
            <UserOutlined v-else style="margin-right: 5px" />
          </template>
          <span>{{ option.name }}</span>
        </a-tag>
      </template>
    </template>
    <template #title="{ name, fullLabel, isDepart, avatar }">
      <span v-if="isDepart">
        <img src="./depart.png" loading="lazy" alt="" />
        <span class="short-label">{{ name }}</span>
        <span class="full-label">{{ fullLabel }}</span>
      </span>
      <span v-else>
        <img v-if="avatar" :src="avatar" loading="lazy" alt="" />
        <UserOutlined v-else style="margin-right: 5px" />
        <span>{{ name }}</span>
      </span>
    </template>
  </a-tree-select>
</template>

<script>
import store from '@/store'
import { pinyin } from '@aim/helper/x/pinyin'
import { UserOutlined } from '@ant-design/icons-vue'
export default {
  name: 'UserSelect',
  components: {
    UserOutlined,
  },
  props: {
    value: {
      type: String,
    },
    mode: {
      type: String,
      default: 'user',
    },
    multiple: {
      type: Boolean,
    },
    placeholder: {
      type: String,
    },
    treeData: {
      type: Array,
      default: [],
    },
    dropdownVisible: {
      type: Boolean,
    },
  },
  computed: {
    fieldNames() {
      return {
        children: 'children',
        label: this.mode === 'user' ? 'email' : 'fullLabel',
        key: 'id',
        value: 'value',
      }
    },
    uniqueValues() {
      const wrap = [this.selectValue ?? []].flat(1)
      const temp = {}
      return wrap.filter(item => {
        const uid = isNumber(item) ? `${item}` : (item?.split('/').pop() ?? '')
        const result = !temp[uid]
        temp[uid] = true
        return result
      })
    },
  },
  data() {
    return {
      openVisible: false,
      selectValue: '',
    }
  },
  watch: {
    dropdownVisible: {
      handler() {
        this.openVisible = this.dropdownVisible
      },
      immediate: true,
    },
    value: {
      handler() {
        if (this.value && this.value.length) {
          const { allDepart, allUser } = store.state.orgData
          let obj = {}
          if (this.mode === 'user') {
            const userArr = Object.values(allUser)
            obj = userArr.find(item => item.email === this.value)
          } else {
            const departArr = Object.values(allDepart)
            obj = departArr.find(item => item.fullLabel === this.value)
          }
          this.selectValue = obj.value || undefined
        } else {
          this.selectValue = undefined
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    onChange(obj) {
      this.$emit('change', obj)
      this.onBlur()
      // 手动失焦防止再次onFocus不生效
      const inputEl = this.$refs.tree?.$el?.querySelector('input, .ant-select-selection-search-input')
      inputEl?.blur()
    },
    filterTreeNode(inputVal, data) {
      if (!inputVal) return true
      return !!pinyin.includes(data.name, inputVal) || !!pinyin.includes(data.email, inputVal)
    },
    onFocus() {
      this.openVisible = true
    },
    onBlur() {
      this.openVisible = false
    },
  },
}
</script>
<style lang="less" scoped>
.user-select-dropdown,
.user-select {
  .ant-select-selection__choice__content,
  .ant-select-selection-selected-value,
  .ant-select-selection-item,
  .ant-select-tree-title {
    span:first-child {
      display: flex;
      align-items: center;
      img {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 5px;
      }
      .anticon {
        margin-left: 5px;
      }
    }
  }
  .ant-select-selection--multiple {
    max-height: 100px;
    overflow: auto;
  }
}

.user-select {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  .short-label {
    display: none;
  }
  .full-label {
    display: inline-block;
  }
}
.user-select-dropdown {
  .full-label {
    display: none;
  }
  .short-label {
    display: inline-block;
  }
}

.user-select--readonly {
  .ant-select-selection {
    background-color: unset;
    color: rgba(0, 0, 0, 0.65);
    border-width: 0px;
    cursor: default;
  }
  .ant-select-arrow {
    display: none;
  }
  .ant-select-selection--multiple {
    border-width: 0 !important;
  }

  .ant-select-selection__choice__content {
    color: rgba(0, 0, 0, 0.65);
  }
}

.user-select-dropdown {
  .ant-select-tree-node-content-wrapper-normal {
    vertical-align: middle;
  }
}
.avatar-img {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 5px;
}
</style>
