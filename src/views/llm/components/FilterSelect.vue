<template>
  <a-popover
    v-model:visible="data.visible"
    :getPopupContainer="popupParent"
    overlayClassName="filter-select-panel"
    placement="bottomRight"
    trigger="click"
  >
    <template #content>
      <div
        class="filter-select-content"
        :style="{
          width: width + 'px',
        }"
      >
        <a-spin v-if="data.loading" :spinning="true" tip="加载中..." />
        <a-select
          v-else
          :value="data.value"
          allowClear
          class="filter-select-input position-relative"
          :filterOption="filterLabelValuePinyin"
          :maxTagCount="5"
          :getPopupContainer="popupParent"
          :mode="multiple ? 'multiple' : undefined"
          :open="data.dropdownVisible"
          placeholder="请选择"
          showSearch
          @change="change"
        >
          <a-select-option v-for="item in options" :key="item.id" :value="item.id">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <BaseLabel :label="label" :focus="data.visible" :active="isActive" @change="visibleToggle" />
  </a-popover>
</template>

<script lang="ts" setup>
import { computed, reactive, watch } from 'vue'
import { popupParent } from '@aim/helper'
import { filterLabelValuePinyin } from '@aim/helper/x/antd'
import BaseLabel from './base-label.vue'

const emits = defineEmits(['change', 'update:modelValue'])
const props = defineProps({
  label: noc.propStringVoid(),
  modelValue: noc.propAny(),
  multiple: noc.propBoolean(),
  options: noc.propArray(),
  placeholder: noc.propString(),
  width: noc.propNumber(280),
})

const data = reactive({
  dropdownVisible: false,
  loading: true,
  value: props.modelValue,
  visible: false,
})

const isActive = computed(() => {
  return props.multiple ? (props.modelValue || []).length > 0 : !!props.modelValue
})

watch(
  () => props.modelValue,
  value => {
    data.value = value
  }
)

watch(
  () => data.visible,
  visible => {
    if (visible) {
      data.loading = true
      const timer = setTimeout(() => {
        if (data.visible) {
          data.dropdownVisible = true
          data.loading = false
        } else {
          clearTimeout(timer)
        }
      }, 10)
    } else {
      data.dropdownVisible = false
    }
  }
)

function change(value) {
  emits('update:modelValue', value)
  emits('change', value)
}

function visibleToggle() {
  data.visible = !data.visible
}
</script>

<style lang="less" scoped>
.filter-select-content {
  padding: 0;
  /* padding: 12px 16px; */

  ::v-deep .ant-input,
  ::v-deep .ant-input-number,
  ::v-deep .ant-calendar-picker,
  ::v-deep .ant-select {
    display: inline-block;
    width: 100% !important;
    border-radius: 8px;
  }

  ::v-deep .ant-select-selection {
    border-radius: 8px;
  }

  :deep(.ant-select-selector ~ div) {
    margin-top: 4px;
    top: 100% !important;
  }

  :deep(.ant-select-dropdown-placement-bottomLeft) {
    left: 0 !important;
    top: 0 !important;
  }
}
</style>

<style lang="less">
.filter-select-panel {
  .ant-popover-inner-content {
    padding: 0;
  }

  .ant-spin {
    width: 100%;
    padding: 10px;
  }
}
</style>
