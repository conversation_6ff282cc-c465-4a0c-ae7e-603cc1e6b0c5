<template>
  <a-popover
    v-model:visible="data.visible"
    :getPopupContainer="popupParent"
    overlayClassName="filter-select-panel"
    placement="bottomRight"
    trigger="click"
  >
    <template #content>
      <div
        class="filter-select-content"
        :style="{
          width: width + 'px',
        }"
      >
        <a-spin v-if="data.loading" :spinning="true" tip="加载中..." />
        <!-- 选择部门名称 -->
        <ItUserSelect
          v-else-if="mode === 'department'"
          v-model="data.valueIgnore"
          v-model:selectedList="data.valueList"
          :getPopupContainer="popupParent"
          :mode="mode"
          :multiple="multiple"
          :open="data.dropdownVisible"
          :leaderVisible="true"
          :placeholder="placeholder"
          @update:selectedList="listChange"
        />
        <!-- 选择用户ID -->
        <ItUserSelect
          v-else
          v-model="data.valueIgnore"
          v-model:selectedList="data.valueList"
          :getPopupContainer="popupParent"
          :mode="mode"
          :multiple="multiple"
          :open="data.dropdownVisible"
          :placeholder="placeholder"
          @update:selectedList="listChange"
        />
      </div>
    </template>
    <BaseLabel :label="label" :focus="data.visible" :active="isActive" @change="visibleToggle" />
  </a-popover>
</template>

<script lang="ts" setup>
import { computed, reactive, watch } from 'vue'
import { popupParent } from '@aim/helper'
import BaseLabel from './base-label.vue'

const emits = defineEmits(['change', 'update:modelValue'])
const props = defineProps({
  label: noc.propStringVoid(),
  mode: noc.propString<'all' | 'department' | 'user'>('user'),
  modelValue: noc.propAny(),
  multiple: noc.propBoolean(),
  placeholder: noc.propString(),
  width: noc.propNumber(280),
})

const data = reactive({
  dropdownVisible: false,
  loading: true,
  valueIgnore: undefined,
  valueList: [],
  visible: false,
})

const isActive = computed(() => {
  return props.multiple ? (props.modelValue || []).length > 0 : !!props.modelValue
})

watch(
  () => data.visible,
  visible => {
    if (visible) {
      data.loading = true
      const timer = setTimeout(() => {
        if (data.visible) {
          data.dropdownVisible = true
          data.loading = false
        } else {
          clearTimeout(timer)
        }
      }, 10)
    } else {
      data.dropdownVisible = false
    }
  }
)

function listChange(list: any[]) {
  let values = []
  if (props.mode === 'department') {
    list.forEach(item => {
      if (item.isDepartment && item.fullName) {
        values.push(item.fullName)
      }
    })
  } else {
    list.forEach(item => {
      if (item.email && !values.includes(item.email)) {
        values.push(item.email)
      }
    })
  }
  let value = props.multiple ? values : values[0]
  emits('update:modelValue', value)
  emits('change', value)
}

function visibleToggle() {
  data.visible = !data.visible
}
</script>

<style lang="less" scoped>
.filter-select {
  &-content {
    padding: 0;
    /* padding: 12px 16px; */

    ::v-deep .ant-input,
    ::v-deep .ant-input-number,
    ::v-deep .ant-calendar-picker,
    ::v-deep .ant-select {
      display: inline-block;
      width: 100% !important;
      border-radius: 8px;
    }

    ::v-deep .ant-select-selection {
      border-radius: 8px;
    }

    :deep(.ant-select-selector ~ div) {
      margin-top: 4px;
      top: 100% !important;
    }

    :deep(.ant-select-dropdown-placement-bottomLeft) {
      left: 0 !important;
      top: 0 !important;
    }
  }
}
</style>

<style lang="less">
.filter-select-panel {
  .ant-popover-inner-content {
    padding: 0;
  }
}
</style>
