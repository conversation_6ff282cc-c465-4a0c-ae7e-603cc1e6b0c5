<template>
  <div class="analysis-dashboard">
    <div class="dashboard-title">调用分析</div>
    <div class="dashboard-filter">
      <div>
        <a-radio-group v-model:value="queryForm.timeType" @change="search">
          <a-radio-button value="今天">今天</a-radio-button>
          <a-radio-button value="昨天">昨天</a-radio-button>
          <a-radio-button value="近3天">近3天</a-radio-button>
          <a-radio-button value="近7天">近7天</a-radio-button>
          <a-radio-button value="custom">自定义</a-radio-button>
        </a-radio-group>
        <TxDateRangePicker
          v-if="queryForm.timeType === 'custom'"
          v-model="queryForm.dateRange"
          class="ml-2"
          :quickEnable="false"
          :rangeMax="90"
          style="width: 230px"
          @change="search"
        />
      </div>
      <div>
        <AnalysisFilter
          :model="queryForm"
          :isAdminOrSec="isAdminOrSec"
          :appNameOptions="appNameOptions"
          :modelOptions="modelOptions"
          @change="searchFilter"
        />
      </div>
    </div>
    <a-spin :spinning="statsLoading" tip="加载中...">
      <div class="data-card">
        <div class="card-title">监控数据</div>
        <div class="monitor-content">
          <div v-for="data in monitorData" :key="data.value">
            <div class="item-label">{{ data.label }}</div>
            <div class="item-value">
              {{ statsDetail[data.value] || 0 }}
              <span class="item-unit">{{ data.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="data-card">
        <div class="card-title">
          <span>模型列表</span>
          <div class="download-btn" @click="download">
            <DownloadOutlined />
            导出数据
          </div>
        </div>
        <div>
          <a-table :columns="columns" :data-source="list" :pagination="false" rowKey="key">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'app_name'">
                <div v-if="record.children">{{ record.model_name }}</div>
                <div v-else class="app-name-content">
                  <a-tooltip overlayClassName="app-info-tooltips" placement="right">
                    <template #title>
                      <div>
                        <a-row :gutter="6">
                          <a-col :span="14">应用名称：{{ record.app_name }}</a-col>
                          <a-col :span="10">使用部门：{{ record.department }}</a-col>
                        </a-row>
                        <a-row :gutter="6">
                          <a-col :span="14">申请人：{{ record.applicant_email }}</a-col>
                          <a-col :span="10">使用范围：{{ getScopeStr(record.scope) }}</a-col>
                        </a-row>
                        <a-row :gutter="6">
                          <a-col :span="14">申请时间：{{ record.created_at }}</a-col>
                          <a-col :span="10">状态：{{ record.status === 'active' ? '已启用' : '已禁用' }}</a-col>
                        </a-row>
                      </div>
                    </template>
                    <span>{{ record.app_name }}</span>
                  </a-tooltip>
                  <span class="sub-app_info">（{{ record.applicant_email }} | {{ record.department }}）</span>
                </div>
              </template>
              <template v-if="column.dataIndex === 'failed_requests'">
                <span v-if="record.failed_requests" class="fail-text">{{ record.failed_requests }}</span>
                <span v-else>{{ record.failed_requests }}</span>
              </template>
              <template v-if="column.dataIndex === 'failure_rate'">
                <span>{{ record.failure_rate }} %</span>
              </template>
              <template v-if="column.dataIndex === 'avg_duration_seconds'">
                <span>{{ record.avg_duration_seconds }} s</span>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-spin>
    <div class="data-card">
      <div class="card-title">用量监控</div>
      <a-spin :spinning="chartLoading" tip="加载中...">
        <div class="canvas-box">
          <div class="canvas-header">
            <span>
              调用次数
              <question-circle-outlined />
            </span>
          </div>
          <div ref="canvas1"></div>
        </div>
        <div class="canvas-box">
          <div class="canvas-header">
            <div>
              调用量（单位：tokens）
              <question-circle-outlined />
            </div>
            <a-radio-group v-model:value="usageType" @change="changeUsage">
              <a-radio-button value="all">全部</a-radio-button>
              <a-radio-button value="input">输入</a-radio-button>
              <a-radio-button value="output">输出</a-radio-button>
            </a-radio-group>
          </div>
          <div ref="canvas2"></div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts">
import { Line } from '@antv/g2plot'
import { Columns, monitorData } from './constant'
import {
  getApiKeyStats,
  getApiKeyModels,
  getApiKeyApplications,
  getApiKeyCharts,
  exportApiKeyExcel,
  getApiKeyScopes,
} from '@/api/llm/apiKey'
import store from '@/store'
import dayjs from 'dayjs'
import cloneDeep from 'lodash.clonedeep'
import AnalysisFilter from '../components/analysisFilter.vue'
import { getUserList } from '@/api/permission/user'

const datetimeFormatBegin = 'YYYY-MM-DD 00:00:00'
const datetimeFormatEnd = 'YYYY-MM-DD 23:59:59'
const dateRangeMap = {
  今天: [dayjs().format(datetimeFormatBegin), dayjs().format(datetimeFormatEnd)],
  昨天: [dayjs().subtract(1, 'day').format(datetimeFormatBegin), dayjs().subtract(1, 'day').format(datetimeFormatEnd)],
  近3天: [dayjs().subtract(3, 'day').format(datetimeFormatBegin), dayjs().format(datetimeFormatEnd)],
  近7天: [dayjs().subtract(7, 'day').format(datetimeFormatBegin), dayjs().format(datetimeFormatEnd)],
}

export default {
  name: 'AnalysisDashboardComp',
  components: {
    AnalysisFilter,
  },
  data() {
    return {
      lineRequest: null,
      lineToken: null,
      columns: Columns,
      list: [],
      monitorData,
      usageType: 'all',
      queryForm: {
        timeType: '今天',
        dateRange: [],
        app_name: [],
        model: [],
        applicant_email: '',
        department: '',
      },
      computeDate: [],
      modelOptions: [],
      appNameOptions: [],
      scopesOptions: [],
      statsDetail: {},
      tokenChart: {
        all: [],
        input: [],
        output: [],
      },
      isAdminOrSec: false,
      userRoles: [],
      chartLoading: false,
      statsLoading: false,
    }
  },
  created() {
    const { app_name } = this.$route.query
    this.queryForm.app_name = app_name ? [app_name] : []
    this.getUserRoles()
    this.getStats()
    this.getModels()
    this.getApplications()
    this.getChartsData()
    this.getScopesList()
  },
  beforeMount() {
    document.addEventListener('paste', this.pasteHandler, { passive: false })
  },
  beforeUnmount() {
    document.removeEventListener('paste', this.pasteHandler)
  },
  methods: {
    pasteHandler(e) {
      const pastedText = e.clipboardData.getData('text/plain').trim()
      e.preventDefault() // 阻止默认粘贴
      document.execCommand('insertText', false, pastedText) // 插入处理后的文本
    },
    onCalendarChange(value) {
      this.computeDate = value
    },
    disabledDate(current) {
      if (!this.computeDate || this.computeDate.length === 0) {
        return false
      }
      const startDate = this.computeDate[0]
      const endDate = this.computeDate[1]
      // 如果只选了 startDate，限制 endDate 不超过 3 个月
      if (startDate && !endDate) {
        const maxDate = dayjs(startDate).add(3, 'month')
        const minDate = dayjs(startDate).subtract(3, 'month')
        return current > maxDate || current < minDate
      }

      // 如果只选了 endDate，限制 startDate 不超过 3 个月
      if (endDate && !startDate) {
        const maxDate = dayjs(endDate).add(3, 'month')
        const minDate = dayjs(endDate).subtract(3, 'month')
        return current > maxDate || current < minDate
      }
      return false
    },
    getUserRoles() {
      const email = store.getters.email
      getUserList({ searchText: email }).then(response => {
        this.userRoles = response.Data.data[0].roles
        this.isAdminOrSec =
          this.userRoles.includes('llm_apikey_sec') ||
          this.userRoles.includes('admin') ||
          this.userRoles.includes('llm_apikey_admin')
      })
    },
    // 获取路由path
    getScopesList() {
      getApiKeyScopes()
        .then(res => {
          this.scopesOptions = res?.data?.scopes || []
        })
        .catch(err => {
          this.$message.error(`${err}`)
        })
    },
    // 使用范围
    getScopeStr(scope) {
      return this.scopesOptions.find(item => item.enum === scope)?.text || '-'
    },
    changeUsage() {
      const data = this.tokenChart[this.usageType]
      this.drawLineToken(data)
    },
    download() {
      const requestParameters = this.formatQueryParam()
      exportApiKeyExcel(requestParameters).then(res => {
        const href = URL.createObjectURL(res)
        let ele = document.createElement('a')
        ele.target = '_blank'
        ele.href = href
        ele.download = `模型列表${dayjs().format('YYYY-MM-DD日HH时mm分')}`
        ele.click()
        ele = null
        URL.revokeObjectURL(href)
      })
    },
    searchFilter(query) {
      const { app_name, model, applicant_email, department } = query
      this.queryForm = {
        ...this.queryForm,
        app_name,
        model,
        applicant_email,
        department,
      }
      this.getStats()
      this.getChartsData()
    },
    search() {
      const { timeType, dateRange } = this.queryForm
      if (timeType === 'custom' && (!dateRange || !dateRange.length)) {
        return
      }
      this.getStats()
      this.getChartsData()
    },
    async initData() {
      const { userTree } = store.state.orgData
      if (!userTree || !userTree.length) {
        await store.dispatch('GetAllOrgUser')
      }
    },
    formatQueryParam() {
      const { timeType, dateRange, app_name, model, applicant_email, department } = this.queryForm
      const range =
        timeType === 'custom'
          ? [dayjs(dateRange[0]).format(datetimeFormatBegin), dayjs(dateRange[1]).format(datetimeFormatEnd)]
          : dateRangeMap[timeType]
      const resultModel = []
      ;(model || []).map(item => {
        const modelArr = item.split(':').filter(key => key)
        const model_provider = modelArr[0]
        const models_item = modelArr[1] || undefined
        const providerIndex = resultModel.findIndex(sub => sub.model_provider === model_provider)
        if (providerIndex !== -1) {
          resultModel[providerIndex].models.push(models_item)
        } else {
          resultModel.push({
            model_provider,
            models: models_item ? [models_item] : [],
          })
        }
      })
      return {
        app_name,
        // applicant_email、department 实际是数组
        applicant_email: applicant_email || undefined,
        department: department || undefined,
        models: resultModel,
        start_time: range[0],
        end_time: range[1],
      }
    },
    getStats() {
      this.statsLoading = true
      const requestParameters = this.formatQueryParam()
      getApiKeyStats(requestParameters)
        .then(res => {
          this.statsDetail = res?.data || {}
          const { stats } = this.statsDetail
          this.list = stats.map(item => {
            return {
              ...item,
              key: `${item.model_name}/${item.model_provider}`,
              children: item.applications.map(sub => {
                return {
                  ...sub,
                  key: `${item.model_name}/${item.model_provider}/${sub.api_key_id}`,
                }
              }),
            }
          })
        })
        .finally(() => {
          this.statsLoading = false
        })
    },
    getChartsData() {
      this.chartLoading = true
      const requestParameters = this.formatQueryParam()
      getApiKeyCharts(requestParameters)
        .then(res => {
          const { request_counts, token_usages, input_token_usage, output_token_usage } = res?.data || {}
          const { timeType } = this.queryForm
          const formatDate = timeType === '今天' ? 'HH:mm' : 'MM-DD HH:mm'
          const requestData = request_counts.map(item => {
            return {
              time: dayjs.unix(item.timestamp).format(formatDate),
              value: item.value,
            }
          })
          const tokenData = token_usages.map(item => {
            return {
              time: dayjs.unix(item.timestamp).format(formatDate),
              value: item.value,
            }
          })
          const inputToken = input_token_usage.map(item => {
            return {
              time: dayjs.unix(item.timestamp).format(formatDate),
              value: item.value,
            }
          })
          const outputToken = output_token_usage.map(item => {
            return {
              time: dayjs.unix(item.timestamp).format(formatDate),
              value: item.value,
            }
          })
          this.tokenChart = {
            all: tokenData,
            input: inputToken,
            output: outputToken,
          }
          this.drawLineRequest(requestData)
          this.drawLineToken(tokenData)
        })
        .finally(() => {
          this.chartLoading = false
        })
    },
    getModels() {
      getApiKeyModels().then(res => {
        const data = res?.data || []
        this.modelOptions = data.map(item => {
          const children = (item.models || []).map(key => {
            return {
              label: key,
              value: `${item.model_provider}:${key}`,
            }
          })
          return {
            label: item.model_provider,
            value: item.model_provider,

            children: cloneDeep(children),
          }
        })
      })
    },
    getApplications() {
      getApiKeyApplications().then(res => {
        const data = res?.data || []
        this.appNameOptions = data.map(key => {
          return {
            id: key,
            name: key,
          }
        })
      })
    },
    drawLineRequest(lineData) {
      if (this.lineRequest) {
        this.lineRequest.destroy()
      }
      const canvasDom = this.$refs.canvas1
      this.lineRequest = new Line(canvasDom, {
        data: lineData,
        xField: 'time',
        yField: 'value',
      })
      this.lineRequest.render()
    },
    drawLineToken(lineData) {
      if (this.lineToken) {
        this.lineToken.destroy()
      }
      const canvasDom = this.$refs.canvas2
      this.lineToken = new Line(canvasDom, {
        data: lineData,
        xField: 'time',
        yField: 'value',
      })
      this.lineToken.render()
    },
  },
}
</script>

<style lang="less" scoped>
.analysis-dashboard {
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  .dashboard-title {
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 14px;
  }
  .dashboard-filter {
    display: flex;
    justify-content: space-between;
    padding: 24px 10px;
  }
  .data-card {
    padding: 20px;
    border-top: 1px solid #ccc;
    .fail-text {
      color: red;
    }
  }
  .card-title {
    border-left: 2px solid #1890ff;
    padding-left: 10px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    line-height: 16px;
    display: flex;
    justify-content: space-between;
    .download-btn {
      color: #1890ff;
      font-weight: 500;
      font-size: 15px;
      cursor: pointer;
    }
  }
  .app-name-content {
    text-align: left;
    font-size: 12px;
  }
  .sub-app_info {
    color: #515977;
  }
  .monitor-content {
    display: flex;
    justify-content: space-between;
    .item-label {
      font-size: 14px;
      margin-bottom: 8px;
    }
    .item-value {
      font-size: 20px;
      font-weight: 600;
    }
    .item-unit {
      font-size: 12px;
      font-weight: 400;
    }
  }
  .canvas-box {
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 20px;
    .canvas-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }
}
</style>
<style lang="less">
.app-info-tooltips {
  max-width: 600px;
}
</style>
