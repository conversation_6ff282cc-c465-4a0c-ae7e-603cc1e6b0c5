<template>
  <a-card :bordered="false">
    <div class="page-header">
      <div class="title-name">密钥管理</div>
      <tx-button type="primary" icon="plus" @click="handleCreate">新建</tx-button>
    </div>
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="20">
          <a-col :md="6" :sm="5">
            <a-form-item label="应用名称">
              <a-input v-model:value="queryParam.app_name" placeholder="请输入" />
            </a-form-item>
          </a-col>
          <a-col v-if="isOnlySec || isAdmin" :md="6" :sm="5">
            <a-form-item label="申请人">
              <ItUserSelect
                v-model:selectedEmail="queryParam.applicant_email"
                :leaderVisible="true"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="5">
            <a-form-item label="使用范围">
              <a-select
                v-model:value="queryParam.scope"
                @pressEnter="$refs.table.refresh()"
                allowClear
                placeholder="请选择使用范围"
              >
                <a-select-option v-for="scopes in scopesOptions" :value="scopes.enum" :key="scopes.enum">
                  {{ scopes.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="5">
            <span class="table-page-search-submitButtons">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px; margin-right: 20px" @click="reset">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      :rowKey="item => item.id"
      :pagination="pagination"
      :columns="columns"
      :pageSize="20"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex == 'applicant'">
          <ItUserInfo :email="record.applicant" :needAllUser="true" :key="`${record.applicant}_${index}`" />
        </template>
        <template v-if="column.dataIndex == 'status'">
          <a-badge v-if="record.status === 'active'" color="#87d068" text="已启用" />
          <a-badge v-else status="red" text="已禁用" />
        </template>
        <template v-if="column.dataIndex == 'scope'">
          {{ getScopeStr(record) }}
        </template>
        <template v-if="column.dataIndex == 'apply_time'">
          <a-tooltip>
            <template #title>{{ record.apply_time }}</template>
            {{ formatDate(record.apply_time) }}
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex == 'action'">
          <tx-button type="link" size="small" :disabled="!record.editable" @click="viewKey(record)">查看 Key</tx-button>
          <tx-button
            v-if="record.status === 'active'"
            :disabled="!record.editable"
            type="link"
            size="small"
            :style="record.editable ? 'color: red' : ''"
            @click="disableRecord(record)"
          >
            禁用
          </tx-button>
          <tx-button v-else type="link" size="small" :disabled="!record.editable" @click="enableRecord(record)">
            启用
          </tx-button>
          <tx-button type="link" size="small" @click="handleAnalysis(record)">调用分析</tx-button>
          <tx-button
            type="link"
            size="small"
            :disabled="!record.editable"
            :style="record.editable ? 'color: red' : ''"
            @click="handleDelete(record)"
          >
            删除
          </tx-button>
        </template>
      </template>
    </s-table>

    <!-- 新增和编辑敏感数据 -->
    <a-modal
      v-if="dialogCreated"
      :title="dialogTitle"
      :confirmLoading="confirmLoading"
      :destroyOnClose="true"
      :width="600"
      :visible="dialogVisible"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model
        ref="apikeyForm"
        :model="apikeyForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="负责人" name="applicant_email">
          <ItUserSelect
            v-model:selectedEmail="apikeyForm.applicant_email"
            :leaderVisible="true"
            mode="user"
            placeholder="请选择负责人"
          />
        </a-form-model-item>
        <a-form-model-item label="使用部门" name="department">
          <ItUserSelect
            v-model:selectedDepartment="apikeyForm.department"
            :leaderVisible="true"
            mode="department"
            placeholder="请选择使用部门"
          />
        </a-form-model-item>
        <a-form-model-item label="使用范围" name="scope">
          <a-select v-model:value="apikeyForm.scope" allowClear placeholder="请选择使用范围">
            <a-select-option v-for="scopes in scopesOptions" :value="scopes.enum" :key="scopes.enum">
              {{ scopes.text }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="应用名称" name="app_name">
          <a-input v-model:value="apikeyForm.app_name" placeholder="请输入应用名称" />
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea auto-size placeholder="请输入申请理由" v-model:value="apikeyForm.reason"></a-textarea>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!-- 查看API-Key -->
    <a-modal
      :visible="apiKeyInfo.visible"
      title="查看API-Key"
      :footer="null"
      @ok="handleOk"
      @cancel="() => (apiKeyInfo.visible = false)"
    >
      <div v-if="!isVerifyCode" class="verify-code-content">
        <div class="verify-code-tips">为保障API-Key使用安全，请先进行身份校验:</div>
        <a-input-search v-model:value="verificationCode" placeholder="请输入" @search="getVerifyCode">
          <template #enterButton>
            <a-button type="primary">获取验证码</a-button>
          </template>
        </a-input-search>
        <a-button class="verify-btn" type="primary" @click="checkoutCode">验证</a-button>
      </div>
      <div v-else class="api-key-content">
        <div>{{ apiKeyInfo.api_key }}</div>
        <a-button type="primary" class="api-key-btn" @click="copyOrViewKey">
          {{ apiKeyInfo.is_secret ? '查看密钥' : '复制密码' }}
        </a-button>
      </div>
    </a-modal>
  </a-card>
</template>

<script lang="ts">
import { STable } from '@/components'
import { getSsoPlatformId } from '@/config'
import { getUserList } from '@/api/permission/user'
import dayjs from 'dayjs'
import {
  getApiKeyScopes,
  getApiKeyList,
  checkAppName,
  createApiKey,
  getApiKeySecret,
  disableApiKey,
  enableApiKey,
  deleteApiKey,
} from '@/api/llm/apiKey'
import store from '@/store'
// 二次校验
import { waterMarkVs } from '@/utils/watermark'
import { myColumns } from '@/views/ai/aiData.js'
import { globalOtp, sendCOde, CheckCode } from '@/api/commonVerify/index'

const columns = [
  {
    title: '应用名称',
    dataIndex: 'app_name',
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '使用部门',
    dataIndex: 'department',
  },
  {
    title: '使用范围',
    dataIndex: 'scope',
    scopedSlots: { customRender: 'scope_str' },
  },
  {
    title: '申请时间',
    dataIndex: 'apply_time',
    sorter: {
      compare: (a, b) => dayjs(a.apply_time).valueOf() - dayjs(b.apply_time).valueOf(),
    },
  },
  {
    title: '近一周调用量',
    dataIndex: 'api_calls_last_week',
    sorter: {
      compare: (a, b) => a.api_calls_last_week - b.api_calls_last_week,
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '100px',
    align: 'center',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '申请理由',
    dataIndex: 'apply_reason',
    width: '240px',
    customRender: ({ text }) => {
      return text || '-'
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    minWidth: '250px',
    scopedSlots: { customRender: 'action' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  inject: ['reload'],
  name: 'KeyManage',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      dialogCreated: false,
      dialogVisible: false,
      dialogType: 'create',
      dialogTitle: '新建大模型密钥',
      confirmLoading: false,
      // 二次校验
      isVerifyCode: false,
      verificationCode: '',
      authOtpKey: '',
      plantform: 'web',
      currentEmail: store.getters.email,
      // 查看API-Key
      apiKeyInfo: {
        visible: false,
        api_key: '',
        api_key_id: '',
        is_secret: true,
      },
      // 列表查询参数
      queryParam: {
        applicant_email: '',
        app_name: '',
        scope: null,
      },
      //  编辑新建详情
      apikeyForm: {
        department: undefined,
        scope: undefined,
        app_name: undefined,
        reason: '',
        applicant_email: undefined,
      },
      // 组件设置
      ssoPlatformId: getSsoPlatformId(),
      ssoToken: noc.user.getToken(),
      // 使用范围
      scopesOptions: [],
      // userOptions: [],
      // departOptions: [],
      userRoles: [],
      isOnlySec: false, // 是否安全人员
      isAdmin: false, // 是否管理员
      debouncePasteHandler: null,
      // 规则
      rules: {
        applicant_email: [{ required: true, message: '请选择负责人', trigger: 'blur' }],
        department: [{ required: true, message: '请选择使用部门', trigger: 'blur' }],
        scope: [{ required: true, message: '请选择使用范围', trigger: 'blur' }],
        app_name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { validator: this.validateAppName, trigger: 'blur' },
        ],
        reason: [{ required: true, message: '请输入申请理由', trigger: 'blur' }],
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const { pageSize, pageNo } = parameter
        const requestParameters = Object.assign(
          {},
          {
            page_size: pageSize,
            page_no: pageNo,
          },
          this.queryParam
        )
        return getApiKeyList(requestParameters).then(res => {
          if (res.data.hasOwnProperty('items')) {
            const { items, page_no, page_size, total } = res.data
            return {
              data: items || [],
              pageNo: page_no,
              pageSize: page_size,
              totalCount: total,
              totalPage: total / page_size,
            }
          } else {
            return {
              data: [],
              pageNo: 1,
              pageSize: 10,
              totalCount: 0,
              totalPage: 0,
            }
          }
        })
      },
    }
  },
  created() {
    this.getUserRoles()
    this.getScopesList()
    this.getInitUsersStatus()
    this.plantFormType()
  },
  beforeMount() {
    document.addEventListener('paste', this.pasteHandler, { passive: false })
  },
  beforeUnmount() {
    document.removeEventListener('paste', this.pasteHandler)
  },
  methods: {
    pasteHandler(e) {
      const pastedText = e.clipboardData.getData('text/plain').trim()
      e.preventDefault() // 阻止默认粘贴
      document.execCommand('insertText', false, pastedText) // 插入处理后的文本
    },
    formatDate(time) {
      return dayjs(time).format('YYYY-MM-DD')
    },
    getUserRoles() {
      const email = store.getters.email
      getUserList({ searchText: email }).then(response => {
        this.userRoles = response.Data.data[0].roles
        this.isAdmin = this.userRoles.includes('admin') || this.userRoles.includes('llm_apikey_admin')
        this.isOnlySec = this.userRoles.includes('llm_apikey_sec') && !this.isAdmin
      })
    },
    reset() {
      this.queryParam = {
        applicant_email: '',
        app_name: '',
        scope: null,
      }
      this.reload()
    },
    async validateAppName(_rule, value) {
      if (!value) {
        return Promise.reject()
      }
      const res = await checkAppName({ app_name: value })
      const { available } = res?.data || {}
      if (available) {
        return Promise.resolve(true)
      } else {
        return Promise.reject('应用名称重复！')
      }
    },
    // 获取路由path
    getScopesList() {
      getApiKeyScopes()
        .then(res => {
          this.scopesOptions = res?.data?.scopes || []
        })
        .catch(err => {
          this.$message.error(`${err}`)
        })
    },
    // 使用范围
    getScopeStr(record) {
      return this.scopesOptions.find(item => item.enum === record.scope)?.text || '-'
    },
    // 新增敏感权限
    handleCreate() {
      if (this.isAdmin) {
        this.dialogCreated = true
        this.dialogVisible = true
        this.dialogType = 'create'
        this.dialogTitle = '新建大模型密钥'
        this.apikeyForm = {
          department: undefined,
          scope: undefined,
          app_name: undefined,
          reason: '',
          applicant_email: undefined,
        }
      } else {
        this.$router.push({ path: '/workflow/api-key' })
      }
    },
    // 提交弹框
    handleOk() {
      antdFormValidate(this.$refs.apikeyForm, valid => {
        if (valid) {
          let params = {}
          this.confirmLoading = true
          params = { ...this.apikeyForm }
          createApiKey(params)
            .then(res => {
              this.$message.success('创建成功！')
              this.reload()
            })
            .catch(() => {})
            .finally(() => {
              this.confirmLoading = false
              this.handleCancel()
            })
        }
      })
    },
    // 取消弹框
    handleCancel() {
      this.dialogVisible = false
    },
    // 点击查看Key
    async viewKey(record) {
      if (!this.isVerifyCode) {
        this.verificationCode = ''
        await this.checkStatus()
      }
      const { api_key, api_key_id } = record
      this.apiKeyInfo.visible = true
      this.apiKeyInfo.api_key = api_key
      this.apiKeyInfo.api_key_id = api_key_id
      this.apiKeyInfo.is_secret = true
    },
    copyText(val) {
      const input = document.createElement('input') // 不会保留文本格式
      // 如果要保留文本格式，比如保留换行符，或者多行文本，可以使用  textarea 标签，再配和模板字符串 ` `
      // const input = document.createElement('textarea')
      // 将想要复制的值
      input.value = val
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 复制后移除输入框
      input.remove()
      // 弹出复制成功信息
      this.$message.success(`复制成功!`)
    },
    // 查看API-Key操作
    copyOrViewKey() {
      const { is_secret, api_key_id } = this.apiKeyInfo
      if (is_secret) {
        getApiKeySecret({
          api_key_id: api_key_id,
        })
          .then(res => {
            if (res !== undefined && res.code === 200) {
              this.apiKeyInfo.api_key = res?.data?.secret
              this.apiKeyInfo.is_secret = false
              this.$message.success('查看成功！')
            } else {
              this.$message.error('查看失败！')
            }
          })
          .catch(() => {})
      } else {
        this.copyText(this.apiKeyInfo.api_key)
      }
    },
    // 删除数据
    handleDelete(record) {
      this.$confirm({
        title: '你将要删除以下应用的API-Key?',
        content: `应用名称 ${record.app_name}， 你要继续吗？`,
        onOk: () => {
          deleteApiKey({
            api_key_id: record.api_key_id,
          })
            .then(() => {
              this.$message.success('删除成功！')
              this.reload()
            })
            .catch(() => {
              this.$message.error('删除失败！')
            })
        },
        onCancel() {},
      })
    },
    // 监测
    handleAnalysis(record) {
      this.$router.push({
        path: `/llm/analysis_dashboard`,
        query: {
          app_name: record.app_name,
        },
      })
    },
    // 禁用
    disableRecord(record) {
      this.$confirm({
        title: '禁用后Key将无法使用!',
        content: `应用名称 ${record.app_name}， 你要继续吗？`,
        onOk: () => {
          disableApiKey({
            api_key_id: record.api_key_id,
          })
            .then(res => {
              if (res !== undefined && res.code === 200) {
                this.$message.success('禁用成功！')
                this.reload()
              } else {
                this.$message.error('禁用失败！')
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    // 启用
    enableRecord(record) {
      this.$confirm({
        title: '你将要启用以下应用的API-Key',
        content: `应用名称 ${record.app_name}， 你要继续吗？`,
        onOk: () => {
          enableApiKey({
            api_key_id: record.api_key_id,
          })
            .then(res => {
              if (res !== undefined && res.code === 200) {
                this.$message.success('启用成功！')
                this.reload()
              } else {
                this.$message.error('启用失败！')
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    // 二次校验code
    // 判断页面是否需要二次验证 （过期状态）
    checkStatus() {
      globalOtp().then(res => {
        this.authToStr(res.Data.network, this.authOtpKey).then(r => {
          this.authToStr(res.Data.otpToken, this.authOtpKey).then(res1 => {
            if (res1) {
              localStorage.setItem('globalToken', res.Data.otpToken)
              this.isVerifyCode = true
            } else {
              this.isVerifyCode = false
            }
          })
        })
      })
    },
    getVerifyCode() {
      sendCOde(this.plantform)
        .then(res => {
          noc.notice.ok({
            message: '发送成功',
          })
        })
        .catch(() => {
          noc.notice.error({
            message: '发送失败',
          })
        })
    },
    getInitUsersStatus() {
      this.uthOtpKey = ''
      myColumns.forEach(item => {
        this.authOtpKey += item.scopeKey
      })
      this.authOtpKey = waterMarkVs + this.authOtpKey
      console.log(this.authOtpKey, 'this.authOtpKey')
    },
    // 加解密
    async authToStr(text, a) {
      const importedKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(a),
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      )
      const textData = atob(text)
      const dataArray = new Uint8Array(textData.split('').map(char => char.charCodeAt(0)))

      const nonceSize = 12
      const nonce = dataArray.slice(0, nonceSize)
      const ciphertext = dataArray.slice(nonceSize)
      try {
        const myData = await crypto.subtle.decrypt({ name: 'AES-GCM', iv: nonce }, importedKey, ciphertext)
        return new TextDecoder().decode(myData)
      } catch (error) {
        return null
      }
    },
    // 提交校验
    checkoutCode() {
      CheckCode({
        code: this.verificationCode,
      }).then(res => {
        if (res.Data.message == 'passCheck') {
          console.log(this.authOtpKey)
          this.authToStr(res.Data.otpToken, this.authOtpKey)
            .then(res1 => {
              if (res1) {
                localStorage.setItem('globalToken', res.Data.otpToken)
                this.isVerifyCode = true
              } else {
                this.$message.error('校验失败')
              }
            })
            .catch(err => {
              console.log(err, 'err')
              this.isVerifyCode = true
            })
        } else {
          this.isVerifyCode = false
          noc.notice.error({
            message: '认证失败',
            duration: 3,
          })
        }
      })
    },
    plantFormType() {
      var ua = window.navigator.userAgent.toLowerCase()
      if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
        this.plantform = 'qw'
      } else {
        this.plantform = 'web'
      }
    },
  },
}
</script>
<style lang="less" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 14px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 14px;
  .title-name {
    font-size: 20px;
  }
}
.api-key-content {
  padding: 20px;
  text-align: center;
  background: #f0f0f0;
  .api-key-btn {
    margin-top: 20px;
  }
}
.verify-code-content {
  .verify-code-tips {
    margin-bottom: 20px;
  }
  .verify-btn {
    margin-top: 20px;
  }
}
</style>
