export const Columns = [
  {
    title: '模型名称',
    dataIndex: 'app_name',
    key: 'app_name',
    customCell: (record, index) => {
      if (!record.children) {
        return { colSpan: 2 }
      } else {
        return {}
      }
    },
  },
  {
    title: '模型供应商',
    dataIndex: 'model_provider',
    key: 'model_provider',
    customCell: (record, index) => {
      if (!record.children) {
        return { colSpan: 0 }
      } else {
        return {}
      }
    },
  },
  {
    title: '调用总量',
    dataIndex: 'total_requests',
    key: 'total_requests',
    sorter: {
      compare: (a, b) => a.total_requests - b.total_requests,
    },
  },
  {
    title: '调用失败量',
    dataIndex: 'failed_requests',
    key: 'failed_requests',
    sorter: {
      compare: (a, b) => a.failed_requests - b.failed_requests,
    },
  },
  {
    title: '失败率',
    dataIndex: 'failure_rate',
    key: 'failure_rate',
    sorter: {
      compare: (a, b) => a.failure_rate - b.failure_rate,
    },
  },
  {
    title: '平均调用时长',
    dataIndex: 'avg_duration_seconds',
    key: 'avg_duration_seconds',
    sorter: {
      compare: (a, b) => a.avg_duration_seconds - b.avg_duration_seconds,
    },
  },
  {
    title: '调用Token总量',
    dataIndex: 'token_usage',
    key: 'token_usage',
    sorter: {
      compare: (a, b) => a.token_usage - b.token_usage,
    },
  },
]

export const mockList = [
  {
    name: '22222',
    model: '222211',
    total: 10,
    fail_count: 2,
    fail_rate: '10%',
    average_time: '1.9s',
    token_count: 111111,
    children: [
      {
        name: '应用名称1',
        total: 10,
        fail_count: 2,
        fail_rate: '10%',
        average_time: '1.9s',
        token_count: 111111,
      },
      {
        name: '应用名称2',
        total: 10,
        fail_count: 2,
        fail_rate: '10%',
        average_time: '1.9s',
        token_count: 111111,
      },
    ],
  },
]

export const lineData = [
  { year: '1991', value: 3 },
  { year: '1992', value: 4 },
  { year: '1993', value: 3.5 },
  { year: '1994', value: 5 },
  { year: '1995', value: 4.9 },
  { year: '1996', value: 6 },
  { year: '1997', value: 7 },
  { year: '1998', value: 9 },
  { year: '1999', value: 13 },
  { year: '1999', value: 8 },
]
export const monitorData = [
  {
    label: '模型总量',
    value: 'total_models',
    unit: '个',
  },
  {
    label: '总调用次数',
    value: 'total_requests',
    unit: '次',
  },
  {
    label: '总失败次数',
    value: 'failed_requests',
    unit: '次',
  },
  {
    label: '平均调用时长',
    value: 'avg_duration_seconds',
    unit: 's',
  },
]
