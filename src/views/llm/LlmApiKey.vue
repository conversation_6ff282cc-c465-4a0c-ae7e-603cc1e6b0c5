<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="名称">
                <a-input
                  v-model:value="queryParam.apiName"
                  placeholder="名称"
                  @pressEnter="$refs.table.refresh()"
                  allowClear
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, text}">
          <template v-if="column.dataIndex == 'sensitiveId'">
            <a-typography-paragraph copyable>{{ text }}</a-typography-paragraph>
          </template>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script lang="ts">
import { STable } from '@/components'
import { listLlmApiKey } from '@/api/llm/llm'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const columns = [
  {
    title: '名称',
    dataIndex: 'apiName',

  },
  {
    title: 'Key',
    dataIndex: 'sensitiveId',
    scopedSlots: { customRender: 'sensitiveId' },
  },
  {
    title: '使用人',
    dataIndex: 'name',
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SecurityGroupRecode',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listLlmApiKey(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  unmounted() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    isExpiredChanged(text) {
      switch (text) {
        case 'long':
          return '长期'
        case 'short':
          return '临时'
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
  },
}
</script>
