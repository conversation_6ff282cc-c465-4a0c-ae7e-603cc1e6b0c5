<template>
  <div>

    <div style="display: flex">
      <p style="vertical-align: middle;font-size: 20px;font-weight: bold">域名选择：</p>
      <a-select default-value="ALL" style="width: 180px" @change="handleChangeDomain" v-model:value="queryData.domain">
        <a-select-option v-for="i in this.domainList" :value="i" :key="i">
          {{ i }}
        </a-select-option>
      </a-select>
    </div>
    <a-row
      :style="{ marginBottom: '12px' }"
      type="flex"
      justify="space-between"
      align="bottom"
      class="cardcs"
    >
      <a-col :span="24" class="billContainerCard">
        <a-card :loading="drLoading">
          <a-skeleton v-if="drLoading"/>
          <CloudflareChart v-else :compRef="'CloudflareChart'" :chartData="cloudflareData"/>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import CloudflareChart from '@/views/network/components/chart/clouflareChart.vue'
import {
  getDomainList,
  getCloudflareChartDataList
} from '@/api/network/clouflare'

export default {
  components: {
    CloudflareChart,
  },
  name: 'FileSystem',
  props: {
    desktopType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      drLoading: false,
      drsLoading: false,
      cloudflareData: {data: [], keys: [], title: ''},
      queryData: {domain: 'ALL'},
      domainList: [],
      domainQuery: {},
    }
  },
  methods: {
    getDomainList() {
      getDomainList(this.queryData).then(res => {
        this.domainList = res.Data.domain
        this.domainList.unshift("ALL")
      })
    },
    handleChangeDomain(val) {
      if (val === undefined) {
        this.queryData.domain = 'ALL'
      }
      this.getCloudflareChartData()
    },
    getCloudflareChartData() {
      let sendData = this.queryData
      this.drLoading = true
      this.cloudflareData = {data: [], keys: [], title: ''}
      getCloudflareChartDataList(sendData).then(res => {
        if (res.Data.data.data !== null) {
          this.cloudflareData = res.Data.data
        }
        this.drLoading = false
      })
    },

    handleChange() {
      this.getCloudflareChartData()
    }
  },
  created() {
    this.getDomainList()
  },
  mounted() {
    this.getCloudflareChartData()
  }
}
</script>

<style scoped lang="less">
.cardcs {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
</style>
