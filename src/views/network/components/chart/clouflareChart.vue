<template>
  <div class="chartContainer">
    <a-empty style="height: 320px" v-if="!chartData.data.length" />
    <div v-else :ref="compRef" :id="compRef"></div>
  </div>
</template>

<script>
import { Line } from '@antv/g2plot'
export default {
  props: {
    chartData: {
      type: Object,
      default: () => ({ data: [], keys: [], areaCode: 86 }),
    },
    compRef: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 320,
    },
  },
  async mounted() {
    const data = this.chartData.data.flatMap(d => [
      { date: d.date, value: d.bytes },
    ])
    if (this.chartData.data.length) {
      const line = new Line(this.compRef, {
        data: data,
        title: this.chartData.title,
        xField: 'date',
        yField: 'value',
        smooth: true,      // 启用圆滑曲线
        xAxis: {
          type: 'cat',
        },
        slider: {
          start: 1 - (365 / this.chartData.data.length),
          end: 1,
        },
        tooltip: {
          formatter: (datum) => {
            return { name: '带宽', value: datum.value + this.chartData.keys.at(-1)}
          },
        },
        yAxis: {
          title: {
            text: this.chartData.keys[1],
            style: {
              fontSize: 14, // 调整标题字体大小
              rotate: 1, // 改变标题旋转角度
            },
          },

          label: {
            formatter: v => `${v}` + this.chartData.keys.at(-1),
          },
        },

      })
      line.render()
    }
  },
  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
