<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :md="5" :sm="12">
            <a-form-item label="选择日期">
              <a-date-picker :default-value="yesterday" v-model:value="queryParam.date" @change="handleChangeDate" format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item label="域名">
              <a-select default-value="" v-model:value="queryParam.domain" style="width: 80%" show-search allowClear>
                <a-select-option v-for="i in this.domainList" :value="i" :key="i">
                  {{ i }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
          <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="(record) => record.id"
      :pagination="pagination"
      :columns="Columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>
  </a-card>
</template>

<script>
import { Ellipsis, STable } from '@/components'
import {  getClientCountryNameList, getDomainList, getCloudflareBytesDataList } from '@/api/network/clouflare'
import moment from 'moment'


const Columns = [
  {
    title: '日期',
    dataIndex: 'date',
    sorter: true
  },
  {
    title: '域名',
    dataIndex: 'domain',
    width: 160,
    sorter: true
  },
  {
    title: '区域代码',
    dataIndex: 'clientCountryName',
    width: 250,
    sorter: true
  },
  {
    title: '带宽(字节)',
    dataIndex: 'bytes',
    sorter: true
  },
]


const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'Lb',
  components: {
    STable,
  },
  data() {
    this.Columns = Columns
    this.pagination = pagination
    return {
      data: [],
      advanced: false,
      domainList: [],
      yesterday: moment().subtract(1, 'days'),
      queryParam: {},
      queryData: {},
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getCloudflareBytesDataList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      confirmLoadingExport: false,
      exportHistoryVisible: false,
      hasAdminRole: false,
    }
  },
  created() {
    this.getCloudflareDomainList()
  },
  methods: {
    getCloudflareDomainList() {
      getDomainList(this.queryData).then(res => {
        this.domainList = res.Data.domain
      })
    },
    handleChangeDate(date, dateString) {
      this.queryParam.date = dateString
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

</style>
