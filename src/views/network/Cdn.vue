<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-alert message="通过刷新功能，您可以删除CDN节点上已经缓存的资源，并强制CDN节点回源站获取最新资源，适用于源站资源更新和发布、违规资源清理、域名配置变更等" style="margin-bottom: 15px" type="info" :show-icon="true"/>
      <a-tabs type="card" default-active-key="1">
        <a-tab-pane key="1" tab="刷新">
          <a-form-model
            ref="dataForm"
            :model="temp.content"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            labelAlign="left"
          >
            <a-form-model-item label="类型" name="urlsType">
              <a-select v-model:value="temp.urlsType" class="filter-item" placeholder="请选择">
                <a-select-option
                  v-for="item in urlsTypeOptions"
                  :key="item.key"
                  :value="item.key"
                >{{ item.display_name }}</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="URL地址列表" name="content">
              <a-textarea
                v-model:value="temp.content"
                :auto-size="{ minRows: 15, maxRows: 80}"
                placeholder="* 默认格式：一行为一条路径地址 *"
              />
            </a-form-model-item>
            <a-form-model-item label="URL编码">
              <a-switch v-model:checked="temp.encode_url" />
              <span style="margin-left: 10px">开启后，将自动对带有特殊字符的url进行urlencode</span>
            </a-form-model-item>
          </a-form-model>
          <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
            <tx-button type="primary" @click="onSubmit" :loading="submitStatus">
              提交
            </tx-button>
          </a-form-model-item>
        </a-tab-pane>
        <a-tab-pane key="2" tab="操作记录" force-render>
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col :md="8" :sm="24">
                  <a-form-item label="模糊查询">
                    <a-input v-model:value="queryParam.url" placeholder="请输入url地址" />
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <tx-button type="primary" @click="handleSearch" >查询</tx-button>

                </a-col>
              </a-row>
            </a-form>
            <a-table
              ref="table"
              size="default"
              :rowKey="(record) => record.id"
              :columns="columns"
              :data-source="recordData"
              :pagination="pagination"
              :loading="loading"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, text }">
                <template v-if="column.dataIndex === 'taskStatus'">
                  <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
                </template>
                <template v-if="column.dataIndex === 'urlsBelongs'">
                  <a-badge :text="supplierFilter(text)" />
                </template>
              </template>
            </a-table>

          </div></a-tab-pane>
      </a-tabs>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable } from '@/components'
import { UpdateRefreshStatus, cdnRefresh, listCdnRefreshRecord } from '@/api/network/cdn'
import { notification } from 'ant-design-vue'
const columns = [
  {
    title: 'url地址',
    dataIndex: 'url'
  },
  {
    title: '类型',
    dataIndex: 'urlsType'
  },
  {
    title: '来源',
    dataIndex: 'urlsBelongs'
  },
  {
    title: '任务ID',
    dataIndex: 'purgeId'
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus'
  },
  {
    title: '刷新时间',
    dataIndex: 'createdAt'
  }

]
const statusMap = {
  Success: {
    status: 'success',
    text: '成功',
  },
  Failed: {
    status: 'error',
    text: '失败',
  },
  Refreshing: {
    status: 'processing',
    text: '刷新中',
  },
}
export default {
  name: 'Cdn',
  data() {
    return {
      columns,
      activeKey: 1,
      submitStatus: false,
      loading: false,
      queryParam: {},
      pagination: {},
      recordData: [],
      refreshData: [],
      labelCol: { span: 1 },
      wrapperCol: { span: 8, offset: 1 },
      temp: {
        urlsType: 'Files',
        content: '',
        encode_url: false
      },
      urlsTypeOptions: [
        { key: 'Files', display_name: '文件列表' },
        { key: 'Directories', display_name: '目录列表' }
      ],
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listCdnRefreshRecord(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  created() {
    this.loadCdnRecordData({ pageNo: 1, pageSize: 10 })
  },
  watch: {
    refreshData: {
      handler(newValue) {
        if (newValue.length > 0) {
          this.refreshCdnData(newValue)
        }
      },
      deep: true
    }
  },
  methods: {
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    supplierFilter(type) {
      switch (type) {
        case 'aliyun-hehe':
          return '阿里云(合合)'
        case 'aliyun-qxb':
          return '阿里云(启信宝)'
        case 'azure-cn':
          return '微软云(国内)'
        case 'azure-global':
          return '微软云(海外)'
        case 'cloudflare':
          return 'Cloudflare'
        case 'huawei-camscanner':
          return '华为云(CS)'
        case 'huawei-intsig':
          return '华为云(合合)'
        case 'tencent-camscanner':
          return '腾讯云(CS)'
        case 'tencent-intsig':
          return '腾讯云(合合)'
        case 'aliyun-cs':
          return '阿里云(CS)'
        case 'akamai':
          return 'Akamai'
        default:
          return '未知(' + type + ')'
      }
    },
    loadCdnRecordData(queryParam) {
      this.refreshData = []
      listCdnRefreshRecord(queryParam).then(res => {
        let pagination = { ...this.pagination }
        if (res.Data.hasOwnProperty('data')) {
          if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
            pagination.total = 0
            pagination.pageNo = 1
            pagination.pageSize = 10
            this.pagination = pagination
            this.loading = false
          } else {
            this.recordData = res.Data.data
            res.Data.data.forEach(item => {
              if (item.taskStatus === 'Refreshing') {
                this.refreshData.push(item.id)
              }
            })
            if (res.Data.totalCount <= 10) {
              pagination = false
              this.pagination = pagination
              this.loading = false
            } else {
              pagination.total = res.Data.totalCount
              this.pagination = pagination
              this.loading = false
              if (this.pagination.showTotal === undefined) {
                this.$set(this.pagination, 'showTotal', total => `共 ${total} 条`)
              }
            }
          }
        } else {
          this.assetData = []
          pagination.total = 0
          pagination.pageNo = 1
          pagination.pageSize = 10
          this.pagination = pagination
          this.loading = false
        }
      })
    },
    handleSearch() {
      this.queryParam.pageNo = 1
      this.queryParam.pageSize = 10
      this.loadCdnRecordData(this.queryParam)
    },
    getRecordData(recordId) {
      console.log(recordId)
      let statusInterval = setInterval(() => {
        UpdateRefreshStatus({ id: recordId }).then(res => {
          if (res.Data === undefined || res.Data === null) {
            console.log(res.Data)
          } else {
            this.recordData.forEach(item => {
              if (item.id === res.Data.id) {
                if (res.Data.taskStatus !== 'Refreshing') {
                  clearInterval(statusInterval)
                  item.taskStatus = res.Data.taskStatus
                }
              }
            })
          }
        })
      }, 30000)
    },
    refreshCdnData(newValue) {
      for (let i = 0, len = newValue.length; i < len; i++) {
        this.getRecordData(newValue[i])
      }
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination }
      pager.current = pagination.current
      this.pagination = pager
      this.queryParam.pageSize = pagination.pageSize
      this.queryParam.pageNo = pagination.current
      this.queryParam.sortField = sorter.field
      this.queryParam.sortOrder = sorter.order
      this.loadCdnRecordData(this.queryParam)
    },
    onSubmit() {
      if (this.temp.encode_url) {
        this.temp.content = encodeURIComponent(this.temp.content)
      }
      this.submitStatus = true
      cdnRefresh(this.temp).then((res) => {
        setTimeout(() => {
          this.submitStatus = false
          notification.success({
            message: res.Data.msg,
            description: '详情请查看刷新记录'
          })
        }, 3 * 1000)
      })
    }
  }
}
</script>

<style scoped>

</style>
