<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.vlan"
                  placeholder="Vlan"
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>

        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="备注">{{ record.ext }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
      <a-modal
        title="新增Vlan网段"
        :visible="visible"
        :confirm-loading="confirmLoading"
        width="40%"
        @ok="handleOk"
        @cancel="handleCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="网段" name="vlan">
            <a-input v-model:value="form.vlan" />
          </a-form-model-item>
          <a-form-model-item label="机房信息" name="idcInfo">
            <a-input v-model:value="form.idcInfo" />
          </a-form-model-item>
          <a-form-model-item label="区域" name="region">
            <a-input v-model:value="form.region" />
          </a-form-model-item>
          <a-form-model-item label="IDC" name="idc">
            <a-input v-model:value="form.idc" />
          </a-form-model-item>
          <a-form-model-item label="备注信息" name="ext">
            <a-input v-model:value="form.ext" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        width="40%"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="网段" name="vlan">
            <a-input v-model:value="form.vlan" />
          </a-form-model-item>
          <a-form-model-item label="机房信息" name="idcInfo">
            <a-input v-model:value="form.idcInfo" />
          </a-form-model-item>
          <a-form-model-item label="区域" name="region">
            <a-input v-model:value="form.region" />
          </a-form-model-item>
          <a-form-model-item label="IDC" name="idc">
            <a-input v-model:value="form.idc" />
          </a-form-model-item>
          <a-form-model-item label="备注信息" name="ext">
            <a-input v-model:value="form.ext" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { createVlanPlan, deleteVlanPlan, updateVlanPlan, vlanPlanList } from '@/api/network/vlan_plan'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '网段',
    dataIndex: 'vlan',
  },
  {
    title: '机房信息',
    dataIndex: 'idcInfo',
  },
  {
    title: 'Idc',
    dataIndex: 'idc',
  },
  {
    title: '区域',
    dataIndex: 'region',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Vlan',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      advanced: false,
      queryParam: {},
      form: {
        idc: undefined,
        vlan: undefined,
        region: undefined,
        idcInfo: undefined,
        ext: undefined,
      },
      visible: false,
      updateVisible: false,
      confirmLoading: false,
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return vlanPlanList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        vlan: [{ required: true, message: '请填写网段', trigger: 'change' }],
        idcInfo: [{ required: true, message: '请填写机房信息', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  methods: {
    handleAdd() {
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createVlanPlan(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      this.form = record
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateVlanPlan(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteVlanPlan(record.id).then(res => {
        if (res.Data.message === 'success') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>
