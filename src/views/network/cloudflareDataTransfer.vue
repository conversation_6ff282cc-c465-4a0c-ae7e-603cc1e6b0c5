<template>
  <page-header-wrapper>
    <a-card
      style="width: 100%"
      :tab-list="tabListNoTitle"
      :active-tab-key="noTitleKey"
      @tabChange="key => onTabChange(key, 'noTitleKey')"
    >
      <div v-if="noTitleKey === 'dashboard'">
        <cloudflare-data-transfer-dashboard/>
      </div>
      <div v-else-if="noTitleKey === 'data'">
        <cloudflare-data-transfer-data/>
      </div>
    </a-card>
  </page-header-wrapper>
</template>
<script>
import { defineComponent } from 'vue'
import cloudflareDataTransferDashboard from '@/views/network/components/cloudflareDataTransferDashboard.vue'
import cloudflareDataTransferData from '@/views/network/components/cloudflareDataTransferData.vue'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { notification } from 'ant-design-vue'


export default defineComponent({
  name: 'ArrivalDelay',
  components: {
    cloudflareDataTransferDashboard: cloudflareDataTransferDashboard,
    cloudflareDataTransferData: cloudflareDataTransferData,
  },
  data() {
    return {
      verifyLoading: false,
      tabListNoTitle: [
        {
          key: 'dashboard',
          tab: 'Dashboard',
        },
        {
          key: 'data',
          tab: '数据',
        },
      ],
      noTitleKey: 'dashboard',
      connectVisible: false,
      verificationCode: '',
      jumpAssetIp: '',
      visible: false,
      forceLossless: false,
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetDesktopUserList()
  },
  unmounted() {
    removeWatermark()
  },
  watch: {
  },
  methods: {
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    enterClick() {
      this.clickLinkTo()
    },
    onTabChange(key, type) {
      this[type] = key
    },
  },
})
</script>
