<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="机房">
                <a-select
                  v-model:value="queryParam.idc"
                  placeholder="请选择机房"
                  :options="idcList"
                  allowClear
                  showSearch
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="出口IP地址">
                <a-input
                  v-model:value="queryParam.ips"
                  placeholder="请输入出口IP地址"
                  allowClear
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operations">
        <tx-button type="primary" :loading="confirmLoadingExport" @click="exportIpExport">导出
        </tx-button>
        <tx-button @click="showExportHistory" style="margin-left: 3px">
          <a-icon type="unordered-list-outlined" />
        </tx-button>
        <tx-button type="primary" style="margin-left: 20px" @click="showAddModel">新增</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="Columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, index, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(record)">更新</tx-button>
            <tx-button type="link" size="small" @click="handleDelete(record)">删除</tx-button>
          </template>
        </template>
      </s-table>
      <a-modal
        :visible="addVisible"
        :width="650"
        title="新增出口IP"
        :confirm-loading="confirmLoading"
        :closable="false"
      >
        <a-form-model
          :model="addParam"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="addRules"
          ref="addForm"
        >
          <a-form-model-item label="机房" name="idc">
            <a-input v-model:value="addParam.idc" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="出口IP地址" name="ips">
            <a-input v-model:value="addParam.ips" style="width: 80%" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelAdd">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
        </template>
      </a-modal>
      <a-modal
        :visible="updateVisible"
        :width="650"
        title="更新出口IP"
        :confirm-loading="confirmLoading"
        :closable="false"
      >
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="rules"
          ref="updateForm"
        >
          <a-form-model-item label="机房" name="idc">
            <a-input v-model:value="updateParam.idc" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="出口IP地址" name="ips">
            <a-input v-model:value="updateParam.ips" style="width: 80%" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
      </a-modal>
      <a-modal v-model:visible="exportHistoryVisible" center :footer="null" width="50%">
        <s-table
          ref="tableExportHistory"
          :rowKey="record => record.id"
          :columns="exportHistoryColumns"
          :data="loadExportHistoryData"
        >
          <template #bodyCell="{ column, index, record, text }">
            <template v-if="column.dataIndex === 'resultLink'">
              <div v-if="record.resultLink !== ''">
                <a :href="record.resultLink.replace('http:', 'https:')">下载</a>
              </div>
              <div v-else>-</div>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}
              </a-tag>
            </template>
          </template>
        </s-table>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import {
  getFileTaskList
} from '@/api/cost/month'
import { notification } from 'ant-design-vue'
import { getUserList } from '@/api/permission/user'
import {
  listNetworkExportIp,
  listExportIpWhich,
  createNetworkExportIp,
  updateNetworkExportIp,
  exportNetworkExportIp,
  deleteNetworkExportIp,
  getNetworkExportIp
} from '@/api/network/export_ip'

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt'
  },
  {
    title: '用户',
    dataIndex: 'creator'
  },
  {
    title: '导出状态',
    dataIndex: 'status'
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink'
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

const Columns = [
  {
    title: '机房',
    dataIndex: 'idc',
    sorter: true
  },
  {
    title: '出口IP地址',
    dataIndex: 'ips',
    sorter: true
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    sorter: true
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' }
  }
]

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'green',
    text: '成功'
  },
  2: {
    color: 'red',
    text: '失败'
  },
  3: {
    color: 'blue',
    text: '运行中'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'ExportIp',
  components: {
    STable,
    Ellipsis
  },
  data () {
    const currentMonth = moment().subtract(1, 'months')
    this.exportHistoryColumns = exportHistoryColumns
    this.Columns = Columns
    this.pagination = pagination
    return {
      data: [],
      idcList: [],
      exportType: 12,
      advanced: false,
      queryParam: {},
      rules: {
        idc: [{ required: true, message: '请输入机房', trigger: 'change' }],
        ips: [{ required: true, message: '请输入出口IP地州', trigger: 'change' }]
      },
      updateParam: {},
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listNetworkExportIp(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      confirmLoadingExport: false,
      exportHistoryVisible: false,
      hasAdminRole: false,
      addVisible: false,
      addParam: {
        month: currentMonth.format('YYYY-MM'),
        supplier: '',
        total: ''
      },
      addRules: {
        idc: [{ required: true, message: '请输入机房', trigger: 'change' }],
        ips: [{ required: true, message: '请输入出口IP地州', trigger: 'change' }]
      },
      loadExportHistoryData: parameter => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = this.exportType
        return getFileTaskList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getIdcListInfo()
    this.checkAdminRole()
  },
  destroyed () {
    removeWatermark()
  },
  methods: {
    checkAdminRole () {
      getUserList({ searchText: store.getters.email }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('admin') || this.userRoles.includes('cost_admin') || this.userRoles.includes('sms_supplier_admin')) {
          this.hasAdminRole = true
        }
      })
    },
    exportStatusFilter (type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter (type) {
      return exportStatusMap[type]?.color || type
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    resetSearchForm () {
      this.queryParam = {
        date: moment(new Date())
      }
    },
    handleUpdate (row) {
      console.log(row)
      getNetworkExportIp(row.id).then(res => {
        this.updateParam = res.Data
        this.updateVisible = true
      })
    },
    cancelUpdate () {
      this.updateVisible = false
      this.confirmLoading = false
    },
    submitUpdate () {
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateNetworkExportIp(this.updateParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '修改成功'
                })
                this.$refs.table.refresh()
                this.cancelUpdate()
              } else {
                notification.error({
                  message: '修改失败'
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete (row) {
      var that = this
      this.$confirm({
        title: '确认删除',
        content: `确认删除 ${row.idc} 出口IP地址吗？`,
        onOk () {
          deleteNetworkExportIp(row.id)
            .then((res) => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功'
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {
            })
        },
        onCancel () {
        }
      })
    },
    exportIpExport () {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      exportNetworkExportIp(requestParameters)
        .then(res => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '导出成功，点击右侧按钮，查看导出记录和结果'
            })
            this.confirmLoadingExport = false
          } else {
            notification.error({
              message: '导出失败'
            })
            this.confirmLoadingExport = false
          }
        })
        .catch(() => {
          this.confirmLoadingExport = false
        })
    },
    downloadFile (url) {
      url = 'https' + url.substr(4)
      fetch(url, {
        headers: {
          'Content-Type': 'application/octet-stream'
        },
        redirect: 'manual'
      })
        .then(response => response.blob())
        .then(blob => {
          const objectUrl = URL.createObjectURL(blob)
          const anchor = document.createElement('a')
          anchor.href = objectUrl
          anchor.click()
          URL.revokeObjectURL(objectUrl)
        })
        .catch(error => {
          console.error(error)
        })
    },
    async showExportHistory () {
      await this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
      this.exportHistoryVisible = true
    },
    showAddModel () {
      this.addVisible = true
    },
    cancelAdd () {
      this.addVisible = false
      this.confirmLoading = false
      this.addParam = {
        idc: '',
        ips: ''
      }
    },
    submitAdd () {
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createNetworkExportIp(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功'
                })
                this.$refs.table.refresh()
                this.cancelAdd()
              } else {
                notification.error({
                  message: '创建失败'
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    getIdcListInfo () {
      listExportIpWhich({ which: 'idc' }).then(res => {
        if (res.Data && res.Data.resList) {
          for (var i = 0, len = res.Data.resList.length; i < len; i++) {
            var item = {}
            item.value = res.Data.resList[i]
            item.label = res.Data.resList[i]
            this.idcList.push(item)
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

</style>
