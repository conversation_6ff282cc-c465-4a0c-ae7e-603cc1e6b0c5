<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="4" :sm="24">
              <a-form-item label="机房">
                <a-select v-model:value="queryParam.idc">
                  <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
                  <a-select-option value="shanghai8-songjiang">松江</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.virtualIp" placeholder="模糊查询" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="描述">
                <a-input v-model:value="queryParam.description" placeholder="模糊查询" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 6) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button style="margin-left: 8px" type="primary" icon="plus" @click="showAddModel">新增</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <!--            <tx-button type="link" style="width: 45px" @click="handleDetail(record)">详情</tx-button>-->
            <tx-button type="link" v-if="hasAdminRole" style="width: 45px" @click="handleUpdate(record)">更新</tx-button>
            <tx-button type="link" v-if="hasAdminRole" style="width: 45px" @click="handleDelete(record)">删除</tx-button>
          </template>
        </template>

      </s-table>
      <a-modal
        :visible="addVisible"
        :width="650"
        title="新增出口IP"
        :confirm-loading="confirmLoading"
        :closable="false"
      >
        <a-form-model
          :model="addParam"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="rules"
          ref="addForm"
        >
          <a-form-model-item label="IP" name="virtualIp">
            <a-input v-model:value="addParam.virtualIp" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="机房" name="idc">
            <a-select v-model:value="addParam.idc">
              <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
              <a-select-option value="shanghai8-songjiang">松江</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="描述" name="description">
            <a-input v-model:value="addParam.description" style="width: 80%" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelAdd">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
        </template>
      </a-modal>
      <a-modal
        :visible="updateVisible"
        :width="650"
        title="更新VIP"
        :confirm-loading="confirmLoading"
        :closable="false"
      >
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="rules"
          ref="updateForm"
        >
          <a-form-model-item label="ID" name="id">
            <a-input v-model:value="updateParam.id" style="width: 80%" disabled />
          </a-form-model-item>
          <a-form-model-item label="IP" name="virtualIp">
            <a-input v-model:value="updateParam.virtualIp" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="Idc" name="Idc">
            <a-input v-model:value="updateParam.idc" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="描述" name="description">
            <a-input v-model:value="updateParam.description" style="width: 80%" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { getVip, listVip, createVip, deleteVip, updateVip } from '@/api/network/vip'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: '100px'
  },
  {
    title: 'IP',
    dataIndex: 'virtualIp',
    sorter: true
  },
  {
    title: '机房',
    dataIndex: 'idc',
  },
  {
    title: '描述',
    dataIndex: 'description'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '180px',
    scopedSlots: { customRender: 'action' }
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'TableList',
  components: {
    STable
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      visible: false,
      confirmLoading: false,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {"idc": "ucloud-shanghai2-hybrid"},
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return listVip(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      detailIdVisible: false,
      hasAdminRole: false,
      addVisible: false,
      addParam: {
        virtualIp: '',
        description: ''
      },
      rules: {
        virtualIp: [{ required: true, message: '请输入IP', trigger: 'change' }],
        description: [{ required: true, message: '请输入描述', trigger: 'change' }]
      },
      updateParam: {},
      updateVisible: false
    }
  },
  created () {
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('dhcp_admin') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    resetSearchForm () {
      this.queryParam = {}
    },
    // 详细信息 相关接口
    handleDetail (record) {
      getVip(record.id).then(response => {
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
    showAddModel () {
      this.addVisible = true
    },
    cancelAdd () {
      this.addVisible = false
      this.confirmLoading = false
      this.addParam = {
        idc: '',
        ips: ''
      }
    },
    submitAdd () {
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createVip(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功'
                })
                this.$refs.table.refresh()
                this.cancelAdd()
              } else {
                notification.error({
                  message: '创建失败'
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    handleUpdate (row) {
      console.log(row)
      getVip(row.id).then(res => {
        this.updateParam = res.Data
        this.updateVisible = true
      })
    },
    cancelUpdate () {
      this.updateVisible = false
      this.confirmLoading = false
    },
    submitUpdate () {
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateVip(this.updateParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '修改成功'
                })
                this.$refs.table.refresh()
                this.cancelUpdate()
              } else {
                notification.error({
                  message: '修改失败'
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete (row) {
      var that = this
      this.$confirm({
        title: '确认删除',
        content: `确认删除 ${row.virtualIp} 吗？`,
        onOk () {
          deleteVip(row.id)
            .then((res) => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功'
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {
            })
        },
        onCancel () {
        }
      })
    }
  }
}
</script>
