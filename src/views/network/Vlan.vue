<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.search"
                  placeholder="机房/业务/Vlan网段/Vlan编号"
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
        :row-selection="rowSelection"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
          <template v-if="column.dataIndex === 'dhcpType'">
            <a-tag v-if="text === 'dhcp'" color="#f50">动态</a-tag>
            <a-tag v-else color="#2db7f5">静态</a-tag>
          </template>
        </template>
      </s-table>
      <a-modal
        title="新增Vlan网段"
        :visible="visible"
        :confirm-loading="confirmLoading"
        width="40%"
        @ok="handleOk"
        @cancel="handleCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="机房" name="idc">
            <a-input v-model:value="form.idc" />
          </a-form-model-item>
          <a-form-model-item label="业务" name="bu">
            <a-select ref="select" v-model:value="form.bu" style="width: 120px" @focus="focus">
              <a-select-option value="搜索(DG)">搜索(DG)</a-select-option>
              <a-select-option value="OCR">OCR</a-select-option>
              <a-select-option value="ACG">ACG</a-select-option>
              <a-select-option value="ACG智能创新">ACG智能创新</a-select-option>
              <a-select-option value="AI开放平台">AI开放平台</a-select-option>
              <a-select-option value="总裁办">总裁办</a-select-option>
              <a-select-option value="CS">CS</a-select-option>
              <a-select-option value="CS-CC">CS-CC</a-select-option>
              <a-select-option value="SSG">SSG</a-select-option>
              <a-select-option value="战略合作">战略合作</a-select-option>
              <a-select-option value="DG">DG</a-select-option>
              <a-select-option value="AIM图像算法">AIM图像算法</a-select-option>
              <a-select-option value="AIM机器视觉算法">AIM机器视觉算法</a-select-option>
              <a-select-option value="AIM自然语言算法">AIM自然语言算法</a-select-option>
              <a-select-option value="AIM平台架构">AIM平台架构</a-select-option>
              <a-select-option value="AIM运维">AIM运维</a-select-option>
              <a-select-option value="大数据">大数据</a-select-option>
              <a-select-option value="AIM企业信息化">AIM企业信息化</a-select-option>
              <a-select-option value="AIM安全与合规">AIM安全与合规</a-select-option>
              <a-select-option value="橘子兼职">橘子兼职</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="环境" name="env">
            <a-radio-group v-model:value="form.env" button-style="solid">
              <a-radio-button value="online">生产环境</a-radio-button>
              <a-radio-button value="pre">预发布环境</a-radio-button>
              <a-radio-button value="test">测试环境</a-radio-button>
              <a-radio-button value="dev">开发环境</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="Vlan网段" name="vlan">
            <a-input v-model:value="form.vlan" />
          </a-form-model-item>
          <a-form-model-item label="Vlan编号" name="vlan">
            <a-input-number v-model:value="form.vlanId" />
          </a-form-model-item>
          <a-form-model-item label="Vlan类型" name="dhcpType">
            <a-radio-group v-model:value="form.dhcpType" button-style="solid">
              <a-radio-button value="dhcp">动态</a-radio-button>
              <a-radio-button value="static">静态</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        width="40%"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="机房" name="idc">
            <a-input v-model:value="form.idc" />
          </a-form-model-item>
          <a-form-model-item label="业务" name="bu">
            <a-select ref="select" v-model:value="form.bu" style="width: 120px" @focus="focus">
              <a-select-option value="搜索(DG)">搜索(DG)</a-select-option>
              <a-select-option value="OCR">OCR</a-select-option>
              <a-select-option value="ACG">ACG</a-select-option>
              <a-select-option value="ACG智能创新">ACG智能创新</a-select-option>
              <a-select-option value="AI开放平台">AI开放平台</a-select-option>
              <a-select-option value="总裁办">总裁办</a-select-option>
              <a-select-option value="CS">CS</a-select-option>
              <a-select-option value="CS-CC">CS-CC</a-select-option>
              <a-select-option value="SSG">SSG</a-select-option>
              <a-select-option value="战略合作">战略合作</a-select-option>
              <a-select-option value="DG">DG</a-select-option>
              <a-select-option value="AIM图像算法">AIM图像算法</a-select-option>
              <a-select-option value="AIM机器视觉算法">AIM机器视觉算法</a-select-option>
              <a-select-option value="AIM自然语言算法">AIM自然语言算法</a-select-option>
              <a-select-option value="AIM平台架构">AIM平台架构</a-select-option>
              <a-select-option value="AIM运维">AIM运维</a-select-option>
              <a-select-option value="大数据">大数据</a-select-option>
              <a-select-option value="AIM企业信息化">AIM企业信息化</a-select-option>
              <a-select-option value="AIM安全与合规">AIM安全与合规</a-select-option>
              <a-select-option value="橘子兼职">橘子兼职</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="环境" name="env">
            <a-radio-group v-model:value="form.env" button-style="solid">
              <a-radio-button value="online">生产环境</a-radio-button>
              <a-radio-button value="pre">预发布环境</a-radio-button>
              <a-radio-button value="test">测试环境</a-radio-button>
              <a-radio-button value="dev">开发环境</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="Vlan网段" name="vlan">
            <a-input v-model:value="form.vlan" />
          </a-form-model-item>
          <a-form-model-item label="Vlan编号" name="vlan">
            <a-input-number v-model:value="form.vlanId" />
          </a-form-model-item>
          <a-form-model-item label="Vlan类型" name="dhcpType">
            <a-radio-group v-model:value="form.dhcpType" button-style="solid">
              <a-radio-button value="dhcp">动态</a-radio-button>
              <a-radio-button value="static">静态</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { createVlan, deleteVlan, getVlan, updateVlan, vlanList } from '@/api/network/vlan'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '机房',
    dataIndex: 'idc',
    sorter: true,
  },
  {
    title: '业务',
    dataIndex: 'bu',
  },
  {
    title: '环境',
    dataIndex: 'env',
  },
  {
    title: 'Vlan网段',
    dataIndex: 'vlan',
  },
  {
    title: 'Vlan编号',
    dataIndex: 'vlanId',
  },
  {
    title: '可用数量',
    dataIndex: 'availableNum',
  },
  {
    title: 'Vlan类型',
    dataIndex: 'dhcpType',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Vlan',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      advanced: false,
      queryParam: {},
      form: {
        idc: undefined,
        bu: undefined,
        env: undefined,
        vlan: undefined,
        vlanId: undefined,
        dhcpType: 'dhcp',
      },
      visible: false,
      updateVisible: false,
      confirmLoading: false,
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return vlanList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        idc: [{ required: true, message: '请填写机房', trigger: 'change' }],
        bu: [{ required: true, message: '请填写业务', trigger: 'change' }],
        env: [{ required: true, message: '请填写环境', trigger: 'change' }],
        vlan: [{ required: true, message: '请填写Vlan网段', trigger: 'change' }],
        vlanId: [{ required: true, message: '请填写Vlan编号', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    handleAdd() {
      this.form.idc = ''
      this.form.bu = 'CS'
      this.form.env = 'online'
      this.form.vlan = ''
      this.form.vlanId = 0
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createVlan(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      getVlan(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateVlan(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteVlan(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>
