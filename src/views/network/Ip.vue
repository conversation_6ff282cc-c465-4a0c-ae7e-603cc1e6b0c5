<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="4" :sm="24">
              <a-form-item label="机房">
                <a-select v-model:value="queryParam.idc">
                  <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
                  <a-select-option value="shanghai8-songjiang">松江</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="IP">
                <a-input
                  v-model:value="queryParam.ip"
                  placeholder="请输入IP"
                  allowClear
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="服务器名">
                <a-input
                  v-model:value="queryParam.hostname"
                  placeholder="请输入服务器名"
                  allowClear
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="4" :sm="24">
                <a-form-item label="MAC地址">
                  <a-input
                    v-model:value="queryParam.macAddress"
                    placeholder="请输入MAC地址"
                    allowClear
                    @pressEnter="$refs.table.refresh()"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="网关">
                  <a-input
                    v-model:value="queryParam.gateway"
                    placeholder="请输入网关"
                    allowClear
                    @pressEnter="$refs.table.refresh()"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="4" :sm="24">
                <a-form-item label="Vlan">
                  <a-input
                    v-model:value="queryParam.vlan"
                    placeholder="请输入Vlan"
                    allowClear
                    @pressEnter="$refs.table.refresh()"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="ID">
                  <a-input v-model:value="queryParam.id" placeholder="请输入ID" />
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <tx-button style="margin-left: 8px" type="primary" icon="plus" @click="showAddModel">新增</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <tx-button type="link" style="width: 45px" @click="handleDetail(record)">详情</tx-button>
            <tx-button type="link" v-if="hasAdminRole" style="width: 45px" @click="handleUpdate(record)">更新</tx-button>
            <tx-button type="link" v-if="hasAdminRole" style="width: 45px" @click="handleDelete(record)">删除</tx-button>
          </template>
        </template>

      </s-table>
      <a-drawer
        title="详情"
        placement="right"
        :closable="false"
        width="40%"
        :visible="detailIdVisible"
        @close="detailIdVisible = false"
      >
        <a-card :bordered="false" :model="detailData" ref="detailData">
          <a-descriptions :column="2">
            <a-descriptions-item label="域名">{{ detailData.domainName }}</a-descriptions-item>
            <a-descriptions-item label="域名服务器">{{ detailData.domainNameServer }}</a-descriptions-item>
            <a-descriptions-item label="是否双网卡">{{ detailData.dualNic }}</a-descriptions-item>
            <a-descriptions-item label="虚拟IP的ID">{{ detailData.virtualIpId }}</a-descriptions-item>
            <a-descriptions-item label="端口">{{ detailData.port }}</a-descriptions-item>
            <a-descriptions-item label="子网掩码">{{ detailData.subnetMask }}</a-descriptions-item>
            <a-descriptions-item label="描述">{{ detailData.description }}</a-descriptions-item>
            <a-descriptions-item label="网关">{{ detailData.gateway }}</a-descriptions-item>
            <a-descriptions-item label="无类别静态路由">{{ detailData.classlessRoute }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-drawer>
      <a-modal
        :visible="addVisible"
        :width="650"
        title="新增DHCP Item"
        :confirm-loading="confirmLoading"
        :closable="false"
      >
        <a-form-model
          :model="addParam"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="rules"
          ref="addForm"
        >
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="addParam.ip" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="MAC地址" name="macAddress">
            <a-input v-model:value="addParam.macAddress" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="机房" name="idc">
              <a-select v-model:value="addParam.idc">
                <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
                <a-select-option value="shanghai8-songjiang">松江</a-select-option>
              </a-select>
          </a-form-model-item>
          <a-form-model-item label="服务器名" name="hostname">
            <a-input v-model:value="addParam.hostname" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="子网掩码" name="subnetMask">
            <a-input v-model:value="addParam.subnetMask" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="网关" name="gateway">
            <a-input v-model:value="addParam.gateway" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="VLAN" name="vlan">
            <a-input v-model:value="addParam.vlan" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="域名" name="domainName">
            <a-input v-model:value="addParam.domainName" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="域名服务器" name="domainNameServer">
            <a-input v-model:value="addParam.domainNameServer" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="无类别静态路由" name="classlessRoute">
            <a-input v-model:value="addParam.classlessRoute" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="描述" name="description">
            <a-input v-model:value="addParam.description" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="双网卡" name="dualNic">
            <a-radio-group v-model:value="addParam.dualNic" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="端口" name="port">
            <a-input v-model:value="addParam.port" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="虚拟IP的ID" name="virtualIpId">
            <a-input v-model:value="addParam.virtualIpId" style="width: 80%" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelAdd">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
        </template>
      </a-modal>
      <a-modal
        :visible="updateVisible"
        :width="650"
        title="更新DHCP Item"
        :confirm-loading="confirmLoading"
        :closable="false"
      >
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="rules"
          ref="updateForm"
        >
          <a-form-model-item label="ID" name="id">
            <a-input v-model:value="updateParam.id" style="width: 80%" disabled />
          </a-form-model-item>
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="updateParam.ip" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="机房" name="idc">
            <a-input v-model:value="updateParam.idc" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="MAC地址" name="macAddress">
            <a-input v-model:value="updateParam.macAddress" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="服务器名" name="hostname">
            <a-input v-model:value="updateParam.hostname" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="子网掩码" name="subnetMask">
            <a-input v-model:value="updateParam.subnetMask" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="网关" name="gateway">
            <a-input v-model:value="updateParam.gateway" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="VLAN" name="vlan">
            <a-input v-model:value="updateParam.vlan" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="域名" name="domainName">
            <a-input v-model:value="updateParam.domainName" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="域名服务器" name="domainNameServer">
            <a-input v-model:value="updateParam.domainNameServer" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="无类别静态路由" name="classlessRoute">
            <a-input v-model:value="updateParam.classlessRoute" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="描述" name="description">
            <a-input v-model:value="updateParam.description" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="双网卡" name="dualNic">
            <a-radio-group v-model:value="updateParam.dualNic" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="端口" name="port">
            <a-input v-model:value="updateParam.port" style="width: 80%" />
          </a-form-model-item>
          <a-form-model-item label="虚拟IP的ID" name="virtualIpId">
            <a-input v-model:value="updateParam.virtualIpId" style="width: 80%" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { loadXLSX } from '@/utils/vendorLoader'
import { Ellipsis, STable } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { getDhcpItem, listDhcpItem, createDhcpItem, deleteDhcpItem, updateDhcpItem } from '@/api/network/ip'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: '100px'
  },
  {
    title: 'IP',
    dataIndex: 'ip',
    sorter: true
  },
  {
    title: 'MAC地址',
    dataIndex: 'macAddress'
  },
  {
    title: 'Idc',
    dataIndex: 'idc'
  },
  {
    title: '服务器名',
    dataIndex: 'hostname'
  },
  {
    title: 'Vlan',
    dataIndex: 'vlan'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '180px',
    scopedSlots: { customRender: 'action' }
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'TableList',
  components: {
    STable
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      visible: false,
      confirmLoading: false,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {"idc": "ucloud-shanghai2-hybrid"},
      allData: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        delete this.downloadqueryParam.pageNo
        delete this.downloadqueryParam.pageSize
        delete this.downloadqueryParam.status
        return listDhcpItem(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              this.allData = res.Data
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      detailData: {},
      detailIdVisible: false,
      hasAdminRole: false,
      addVisible: false,
      addParam: {
        dualNic: 0
      },
      rules: {
        ip: [{ required: true, message: '请输入IP', trigger: 'change' }],
        macAddress: [{ required: true, message: '请输入MAC地址', trigger: 'change' }],
        subnetMask: [{ required: true, message: '请输入子网掩码', trigger: 'change' }],
        gateway: [{ required: true, message: '请输入网关', trigger: 'change' }],
        hostname: [{ required: true, message: '请输入域名', trigger: 'change' }],
        vlan: [{ required: true, message: '请输入VLAN', trigger: 'change' }]
      },
      updateParam: {},
      updateVisible: false
    }
  },
  created () {
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    loadXLSX()
    this.getUserRoles(email)
  },
  methods: {
    getUserRoles (userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('dhcp_admin') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    resetSearchForm () {
      this.queryParam = {}
    },
    // 详细信息 相关接口
    handleDetail (record) {
      getDhcpItem(record.id).then(response => {
        this.detailData = response.Data
        this.detailIdVisible = true
      })
    },
    showAddModel () {
      this.addVisible = true
    },
    cancelAdd () {
      this.addVisible = false
      this.confirmLoading = false
      this.addParam = {
        dualNic: 0
      }
    },
    submitAdd () {
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createDhcpItem(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功'
                })
                this.$refs.table.refresh()
                this.cancelAdd()
              } else {
                notification.error({
                  message: '创建失败'
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    handleUpdate (row) {
      getDhcpItem(row.id).then(res => {
        this.updateParam = res.Data
        this.updateVisible = true
      })
    },
    cancelUpdate () {
      this.updateVisible = false
      this.confirmLoading = false
    },
    submitUpdate () {
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateDhcpItem(this.updateParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '修改成功'
                })
                this.$refs.table.refresh()
                this.cancelUpdate()
              } else {
                notification.error({
                  message: '修改失败'
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete (row) {
      var that = this
      this.$confirm({
        title: '确认删除',
        content: `确认删除 ${row.ip} 吗？`,
        onOk () {
          deleteDhcpItem(row.id)
            .then((res) => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功'
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {
            })
        },
        onCancel () {
        }
      })
    }
  }
}
</script>
