<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="机房">
                <a-select
                  v-model:value="queryParam.idc"
                  placeholder="请选择机房"
                  :options="idcList"
                  allowClear
                  showSearch
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="负载均衡IP">
                <a-input
                  v-model:value="queryParam.ip"
                  placeholder="请输入出口IP地址"
                  allowClear
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operations">
        <tx-button type="primary" :loading="confirmLoadingExport" @click="exportIpExport">导出
        </tx-button>
        <tx-button @click="showExportHistory" style="margin-left: 3px">
          <a-icon type="unordered-list-outlined" />
        </tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="Columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="实例端口">{{ record.instancePort }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
            <a-descriptions-item label="实例名称">{{ record.instanceNames }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
      <a-modal v-model:visible="exportHistoryVisible" center :footer="null" width="50%">
        <s-table
          ref="tableExportHistory"
          :rowKey="record => record.id"
          :columns="exportHistoryColumns"
          :data="loadExportHistoryData"
        >
          <template #bodyCell="{ column, index, record, text }">
            <template v-if="column.dataIndex === 'resultLink'">
              <div v-if="record.resultLink !== ''">
                <a :href="record.resultLink.replace('http:', 'https:')">下载</a>
              </div>
              <div v-else>-</div>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}
              </a-tag>
            </template>
          </template>
        </s-table>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'
import {
  getFileTaskList
} from '@/api/cost/month'
import { notification } from 'ant-design-vue'
import {
  listNetworkLb,
  listNetworkLbWhich,
  exportNetworkLb,
  getNetworkLb
} from '@/api/network/lb'

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt'
  },
  {
    title: '用户',
    dataIndex: 'creator'
  },
  {
    title: '导出状态',
    dataIndex: 'status'
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink'
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

const Columns = [
  {
    title: '名称',
    dataIndex: 'name',
    sorter: true
  },
  {
    title: '负载均衡ID',
    dataIndex: 'lbId',
    width: 250,
    sorter: true
  },
  {
    title: '机房',
    dataIndex: 'idc',
    width: 160,
    sorter: true
  },
  {
    title: '负载均衡IP',
    dataIndex: 'ip',
    sorter: true
  },
  {
    title: '端口',
    dataIndex: 'lbPort',
    sorter: true
  },
  {
    title: '实例IP',
    dataIndex: 'instanceIps',
    width: 300,
    sorter: true
  },
  {
    title: '实例ID',
    dataIndex: 'instanceIds',
    width: 250,
    sorter: true
  }
]

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'green',
    text: '成功'
  },
  2: {
    color: 'red',
    text: '失败'
  },
  3: {
    color: 'blue',
    text: '运行中'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'Lb',
  components: {
    STable,
    Ellipsis
  },
  data () {
    const currentMonth = moment().subtract(1, 'months')
    this.exportHistoryColumns = exportHistoryColumns
    this.Columns = Columns
    this.pagination = pagination
    return {
      data: [],
      idcList: [],
      exportType: 13,
      advanced: false,
      queryParam: {},
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listNetworkLb(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      confirmLoadingExport: false,
      exportHistoryVisible: false,
      hasAdminRole: false,
      loadExportHistoryData: parameter => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = this.exportType
        return getFileTaskList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getIdcListInfo()
  },
  destroyed () {
    removeWatermark()
  },
  methods: {
    exportStatusFilter (type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter (type) {
      return exportStatusMap[type]?.color || type
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    resetSearchForm () {
      this.queryParam = {
        date: moment(new Date())
      }
    },
    exportIpExport () {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      exportNetworkLb(requestParameters)
        .then(res => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '导出成功，点击右侧按钮，查看导出记录和结果'
            })
            this.confirmLoadingExport = false
          } else {
            notification.error({
              message: '导出失败'
            })
            this.confirmLoadingExport = false
          }
        })
        .catch(() => {
          this.confirmLoadingExport = false
        })
    },
    downloadFile (url) {
      url = 'https' + url.substr(4)
      fetch(url, {
        headers: {
          'Content-Type': 'application/octet-stream'
        },
        redirect: 'manual'
      })
        .then(response => response.blob())
        .then(blob => {
          const objectUrl = URL.createObjectURL(blob)
          const anchor = document.createElement('a')
          anchor.href = objectUrl
          anchor.click()
          URL.revokeObjectURL(objectUrl)
        })
        .catch(error => {
          console.error(error)
        })
    },
    async showExportHistory () {
      await this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
      this.exportHistoryVisible = true
    },
    getIdcListInfo () {
      listNetworkLbWhich({ which: 'idc' }).then(res => {
        if (res.Data && res.Data.resList) {
          for (var i = 0, len = res.Data.resList.length; i < len; i++) {
            var item = {}
            item.value = res.Data.resList[i]
            item.label = res.Data.resList[i]
            this.idcList.push(item)
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table-operations {
  position: relative;
  height: 40px;
}

</style>
