<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div style="font-weight: bold; margin-bottom: 8px">用户列表</div>
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="邮箱">
                <a-input v-model:value="userEmail" placeholder="请输入用户邮箱" />
              </a-form-item>
            </a-col>
            <a-col :md="16" :sm="24">
              <tx-button style="margin-left: 8px" type="primary" @click="queryData">查询</tx-button>
            </a-col>
          </a-row>
        </a-form>
        <a-table :pagination="pagination" :dataSource="dataSource" :columns="columns" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key == 'name'">
              <span>{{ record.name }} &nbsp; {{ record.email }}</span>
            </template>
            <template v-if="column.key === 'status'">
              <span>
                <a-tag
                  :key="record.status"
                  :color="
                    record.status == 0
                      ? 'processing'
                      : record.status == 1
                        ? 'green'
                        : record.status == 2
                          ? 'red'
                          : 'warning'
                  "
                >
                  {{
                    record.status == 0
                      ? '生成中'
                      : record.status == 1
                        ? '生成成功'
                        : record.status == 2
                          ? '生成失败'
                          : '未生成'
                  }}
                </a-tag>
              </span>
            </template>
            <template v-if="column.key === 'isBlocked'">
              <span>
                <a-tag :key="record.isBlocked" :color="record.isBlocked == 0 ? 'green' : 'red'">
                  {{ record.isBlocked == 0 ? '未禁用' : '已禁用' }}
                </a-tag>
              </span>
            </template>
            <template v-if="column.key == 'whiteUrl'">
              {{ record.whiteUrl ? record.whiteUrl.join() : '未配置资产' }}
            </template>
            <template v-if="column.key == 'opt'">
              <span style="color: #1890ff; cursor: pointer" @click="useUpdate(record)">更新</span>
              <span style="color: #1890ff; cursor: pointer; margin-left: 8px" @click="codeUnblock(record)">
                验证码解禁
              </span>
              <a-popconfirm
                title="删除后用户将无法使用，确认删除吗?"
                ok-text="Yes"
                cancel-text="No"
                @confirm="confirmDel(record)"
                @cancel="cancel"
              >
                <span style="color: #1890ff; cursor: pointer">删除用户</span>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
        <a-modal v-model:visible="visible" title="用户更新" @ok="handleOk">
          <a-form
            :model="formState"
            name="basic"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            autocomplete="off"
          >
            <a-form-item label="用户状态" name="isBlocked">
              <a-radio-group v-model:value="formState.isBlocked" button-style="solid">
                <a-radio-button :value="1">已禁用</a-radio-button>
                <a-radio-button :value="0">未禁用</a-radio-button>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="用户资产" name="whiteUrl">
              <a-textarea style="margin-bottom: 8px" v-model:value="formState.whiteUrl" />
              <span style="color: #faad14">用户资产用 , 分隔</span>
            </a-form-item>
          </a-form>
        </a-modal>
      </div>
    </a-card>
  </page-header-wrapper>
</template>

<script setup>
import { ref } from 'vue'
import { notification } from 'ant-design-vue'
import { getList, updateUserAsset, unBlockUser, deleteUser } from '@/api/security/extensionAsset.js'
const userEmail = ref('')
const visible = ref(false)
const columns = [
  {
    title: '用户',
    dataIndex: 'name',
    key: 'name',
    width: 300,
  },
  {
    title: '用户状态',
    dataIndex: 'isBlocked',
    key: 'isBlocked',
    width: 110,
  },
  {
    title: '用户资产',
    dataIndex: 'whiteUrl',
    key: 'whiteUrl',
  },
  {
    title: '插件状态',
    dataIndex: 'status',
    key: 'status',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'opt',
    key: 'opt',
    width: 220,
  },
]
const formState = ref({
  isBlocked: '',
  whiteUrl: '',
})
let pagination = ref({
  total: null,
  current: 1,
  pageSize: 10,
})
let dataSource = ref([])
const handleTableChange = (pag, filters, sorter) => {
  console.log(pag, filters, sorter)
  console.log(pagination.value, 'v')
  pagination.value = pag
  getUser()
}
const getUser = email => {
  getList({
    pageNo: pagination.value.current,
    email,
  }).then(res => {
    console.log(res.Data)
    if (res.Data) {
      dataSource.value = res.Data.data
      pagination.value.total = res.Data.totalCount
      pagination.value.current = res.Data.pageNo
      pagination.value.pageSize = 10
    }
  })
}
getUser()
const queryData = () => {
  pagination.value.current = 1
  getUser(userEmail.value)
}
const useUpdate = record => {
  visible.value = true

  formState.value = record
  console.log(formState.value, 'formState.value')
}
const handleOk = () => {
  console.log(formState.value, 'vvv')
  const params = {
    id: formState.value.id,
    whiteUrl: formState.value.whiteUrl && formState.value.whiteUrl.length ? formState.value.whiteUrl.split(',') : [],
    isBlocked: formState.value.isBlocked,
  }
  updateUserAsset(params)
    .then(res => {
      console.log(res, 'res')
      getUser(userEmail.value)
    })
    .catch(err => {
      console.log(err)
    })
  visible.value = false
}
const codeUnblock = record => {
  unBlockUser({
    id: record.id,
  })
    .then(res => {
      console.log(res, 'res')
      notification.success({ message: '验证码解禁成功' })
    })
    .catch(err => {
      notification.error({ message: '错误' })
    })
}
const confirmDel = r => {
  console.log(r, 'rr')
  deleteUser({
    id: r.id,
  })
    .then(res => {
      console.log(res, 'res')
      getUser()
    })
    .catch(err => {
      console.log(err, 'err')
    })
}
</script>

<style lang="less" scoped></style>
