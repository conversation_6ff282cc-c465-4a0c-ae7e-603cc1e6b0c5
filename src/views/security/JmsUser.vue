<template>
  <page-header-wrapper>
    <template #content>堡垒机用户账号管理, Web端连接资产请使用云终端服务。</template>
    <a-row :gutter="48">
      <a-col :md="7" :sm="24">
        <a-card :bordered="false">
          <div style="font-weight: bold">个人信息</div>

          <a-divider />
          <div class="jms_user_avatar">
            <img :src="user.avatar" />
          </div>
          <div class="jms_user-type">{{ userType }}</div>
          <p></p>
          <p></p>
          <p></p>
          <a-form layout="left" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
            <a-form-item label="用户名" class="jms_user_my-ant-form-item">
              {{ userInfomation.username }}
            </a-form-item>
            <a-form-item label="邮箱" class="jms_user_my-ant-form-item">
              {{ userInfomation.email }}
            </a-form-item>
            <a-form-item label="密码更新时间">
              {{ userInfomation.UserPasswordLastUpdated }}
            </a-form-item>
          </a-form>
          <p></p>
          <a-divider />
          <p></p>
          <a-row>
            <a-form layout="inline">
              <a-form-item label="重置堡垒机密码">
                <a-tooltip>
                  <template #title>堡垒机密码超过90天, 需要重新设置密码。</template>
                  <a-icon type="question-circle" />
                </a-tooltip>
                <div style="float: right">
                  <tx-button @click="PasswordReset('visible', userInfomation)">重置</tx-button>
                </div>
              </a-form-item>
              <p></p>
              <a-form-item label="重置MFA并绑定">
                <div style="text-align: right">
                  <tx-button @click="ResetMfa('visible', userInfomation)">重置</tx-button>
                </div>
              </a-form-item>
              <p></p>
              <a-form-item label="重置并下载SSH">
                <div style="float: right">
                  <tx-button @click="JmsUserSSH('visible', userInfomation)">重置</tx-button>
                </div>
              </a-form-item>
            </a-form>
          </a-row>
          <a-divider />
          <tx-button style="width: 100%" type="primary" @click="rederitCloudTermina">
            连接服务器
            <LinkOutlined />
          </tx-button>
        </a-card>
      </a-col>
      <a-col :md="17" :sm="24">
        <a-card :bordered="false">
          <a-tabs v-model:activeKey="activeKey" @change="tabChange">
            <a-tab-pane v-if="rightTitleContent == '用户列表'" key="userList" tab="用户列表">
              <a-form layout="inline">
                <a-row :gutter="48">
                  <a-col :md="8" :sm="24">
                    <a-form-item label="邮箱">
                      <a-input
                        v-model:value="queryParam.searchText"
                        placeholder="email"
                        @pressEnter="$refs.table.refresh()"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :md="16" :sm="24">
                    <tx-button style="margin-left: 8px" type="primary" @click="$refs.table.refresh(true)">
                      查询
                    </tx-button>
                  </a-col>
                </a-row>
              </a-form>
            </a-tab-pane>
            <a-tab-pane v-if="rightTitleContent !== '用户列表'" key="userAsset" tab="资产列表" force-render>
              <a-form layout="inline">
                <a-row :gutter="48">
                  <a-col :md="8" :sm="24">
                    <a-form-item label="授权资产IP">
                      <a-input
                        v-model:value="queryParam.searchText"
                        placeholder="*********"
                        @pressEnter="$refs.table.refresh()"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :md="16" :sm="24">
                    <tx-button style="margin-left: 8px" type="primary" @click="$refs.table.refresh(true)">
                      查询
                    </tx-button>
                  </a-col>
                </a-row>
              </a-form>
            </a-tab-pane>
            <a-tab-pane v-if="rightTitleContent == '用户列表'" key="bastonAsset" tab="堡垒机资产">
              <a-form layout="inline">
                <a-row :gutter="48">
                  <a-col :md="8" :sm="24">
                    <a-form-item label="资产IP">
                      <a-input
                        v-model:value="queryAssetParam.ip"
                        placeholder="*********"
                        @pressEnter="$refs.table.refresh()"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :md="16" :sm="24">
                    <tx-button style="margin-left: 8px" type="primary" @click="$refs.table.refresh(true)">
                      查询
                    </tx-button>
                  </a-col>
                </a-row>
              </a-form>
            </a-tab-pane>
          </a-tabs>
          <a-divider />
          <s-table
            ref="table"
            size="default"
            :rowKey="record => record.id"
            :pagination="pagination"
            :columns="
              rightTitleContent == '用户列表' ? (activeKey == 'bastonAsset' ? columnsAsset : columnsAdmin) : columns
            "
            :data="loadData"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'isActive' && activeKey == 'userList'">
                <a-tag :color="record.isActive ? 'green' : 'red'">
                  {{ record.isActive ? '激活' : '未激活' }}
                </a-tag>
              </template>
              <template v-else-if="column.dataIndex == 'loginBlocked' && activeKey == 'userList'">
                <a-tag :color="record.loginBlocked ? 'red' : 'green'">
                  {{ record.loginBlocked ? '锁定' : '解锁' }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex == 'action' && activeKey == 'userList'">
                <a @click="userInformationIsUpToDate(record)"><RedoOutlined /></a>
                <a-divider type="vertical" />
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="userpRression">
                        <a style="width: 40px" @click="enableUser('active', record)">激活</a>
                      </a-menu-item>
                      <a-menu-item v-if="userpRression">
                        <a style="width: 40px" @click="enableUser('unlock', record)">解锁</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a style="width: 40px" @click="PasswordReset('visible', record)">重置密码</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a style="width: 40px" @click="JmsUserSSH('visible', record)">下载SSH密钥</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a style="width: 40px" @click="ResetMfa('visible', record)">重置MFA</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a>
                    更多
                    <a-icon type="down" />
                  </a>
                </a-dropdown>
              </template>
              <template v-else-if="column.dataIndex == 'action' && activeKey == 'bastonAsset'">
                <a @click="assetConnectTest(record)">连接测试</a>
              </template>
            </template>
          </s-table>
        </a-card>
      </a-col>
    </a-row>
    <a-modal v-model:visible="PasswordVisible" title="堡垒机用户密码重置">
      <template #footer>
        <tx-button key="back" @click="PasswordReset('cancel', '')">取消</tx-button>
        <tx-button type="primary" @click="PasswordReset('confirm', '')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="PasswordForm"
        :model="PasswordParam"
        :rules="PasswordRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="密码" name="password1" class="jms_user_my-ant-form-item">
          <a-input-password v-model:value="PasswordParam.password1" v-model:visible="passwordvisible1" />
        </a-form-model-item>
        <a-form-model-item label="确认密码" name="password2" class="jms_user_my-ant-form-item">
          <a-input-password v-model:value="PasswordParam.password2" v-model:visible="passwordvisible2" />
        </a-form-model-item>
        <a-form-model-item label="验证码" class="jms_user_my-ant-form-item">
          <a-row>
            <a-col :span="17">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="JmsUserSSHVisible" title="重置并下载SSH密钥">
      <template #footer>
        <tx-button key="back" @click="JmsUserSSH('close', '')">取消</tx-button>
        <tx-button type="primary" @click="JmsUserSSH('add', record)">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="MFAForm"
        :model="DeleteAccountParam"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="验证码" class="jms_user_my-ant-form-item">
          <a-row>
            <a-col :span="18">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="MFAVisible" title="账号MFA重置">
      <template #footer>
        <tx-button key="back" @click="ResetMfa('cancel')">取消</tx-button>
        <tx-button type="primary" @click="ResetMfa('confirm')" :loading="loading">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="MFAForm"
        :model="MFAParam"
        :rules="MFARules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="验证码" class="jms_user_my-ant-form-item">
          <a-row>
            <a-col :span="18">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="MFABindVisible" title="堡垒机用户账号MFA绑定">
      <template #footer>
        <tx-button key="back" @click="MFABind('cancel')">取消</tx-button>
        <tx-button type="primary" @click="MFABind('confirm')">确定</tx-button>
      </template>
      请扫描二维码绑定MFA, 并输入动态码。
      <!-- <span style="color: red">二个连续</span>
      的认证码。(请在
      <span style="color: red">3分钟</span>
      内绑定!) -->
      <a-row :gutter="48">
        <a-col :md="12" :sm="24">
          <img :src="imageUrl" alt="Image description" />
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-model
            layout="horizontal"
            ref="MFABindForm"
            :model="MFABindParam"
            :rules="MFABindRules"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <br />
            <br />
            <br />
            <br />
            <a-form-model-item label="动态码" name="mfaCode1" class="jms_user_my-ant-form-item">
              <a-input v-model:value="MFABindParam.mfaCode1" />
            </a-form-model-item>
          </a-form-model>
        </a-col>
      </a-row>
    </a-modal>
    <a-modal
      v-model:visible="AssetTestVisible"
      title="任务执行结果"
      :mask-closable="false"
      center
      :footer="null"
      style="width: 800px"
    >
      任务ID: {{ assetTestId }}
      <a style="float: right; font-size: 20px" @click="getJmsTaskResult"><RedoOutlined /></a>
      <br />
      <br />
      <div ref="logTerm" class="terminal-container"></div>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { Terminal } from 'xterm'
import 'xterm/css/xterm.css'
import { FitAddon } from 'xterm-addon-fit'
import { Ellipsis, STable } from '@/components'
import {
  jmsUserList,
  jmsUserSSH,
  jmsUserPw,
  jmsUserMfaBond,
  jmsUserUnblock,
  jmsAssetList,
  jmsAssetTest,
  jmsUserUpdate,
  jmsAssetTaskLog,
} from '@/api/security/jms_user'
import { sslDownloadCheck, sslDownloadSend } from '@/api/domain/domain'
import { getUserList } from '@/api/permission/user'
import { LinkOutlined, RedoOutlined } from '@ant-design/icons-vue'

import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import user from '@/store/modules/user'
import { notification } from 'ant-design-vue'
let Base64

vendorLoad('js-base64').then(res => {
  Base64 = res
})
const columnsAdmin = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '用户名',
    dataIndex: 'username',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '密码更新日期',
    dataIndex: 'UserPasswordLastUpdated',
  },
  {
    title: '激活状态',
    dataIndex: 'isActive',
  },
  {
    title: '用户状态',
    dataIndex: 'loginBlocked',
  },
  {
    title: '数据更新日期',
    dataIndex: 'updatedAt',
  },
  {
    title: '用户操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]
const columnsAsset = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '资产名',
    dataIndex: 'assetName',
  },
  {
    title: '网关',
    dataIndex: 'domainName',
  },
  {
    title: '类型',
    dataIndex: 'assetType',
  },
  {
    title: '数据更新日期',
    dataIndex: 'updatedAt',
  },
  {
    title: '用户操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]
const columnsUser = [
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '服务器名',
    dataIndex: 'hostname',
  },
  {
    title: '机房',
    dataIndex: 'idc',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Jms',
  components: {
    STable,
    Ellipsis,
    LinkOutlined,
  },
  data() {
    this.columns = columnsUser
    this.columnsAdmin = columnsAdmin
    this.columnsAsset = columnsAsset
    this.pagination = pagination
    return {
      terminal: null,
      fitAddon: null,
      labelCol: { span: 40 },
      wrapperCol: { span: 8 },
      user: {},
      userType: '用户',
      activeKey: 'userAsset',
      rightTitleContent: '资产列表',
      localUser: store.getters.email,
      userpRression: false,
      userRolesWhite: 'opsAdmin',
      queryParam: {},
      queryAssetParam: {},
      PasswordParam: {},
      userInfomation: {},
      temp: {
        founderEmail: user.state.email,
        content: {
          hostList: '',
          role: 'root',
          isExpired: 'long-term',
          isHrApprove: 'false',
          validTime: '3600',
        },
      },
      cancelTemp: {
        founderEmail: user.state.email,
        content: {
          hostList: '',
          role: 'root',
        },
      },
      PasswordRules: {
        password1: [
          { required: true, message: '请输入用户密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              // 包含所有常见特殊符号 ! " # $ % & ' ( ) * + , - . / : ; < = > ? @ [ \ ] ^ _ ` { | } ~
              const regex =
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!\"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])[A-Za-z\d!\"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{8,}$/
              if (!value || regex.test(value)) {
                callback()
              } else {
                callback(
                  new Error(
                    '密码必须8位以上且包含大写字母、小写字母、数字、特殊符号（!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~）'
                  )
                )
              }
            },
            trigger: 'blur',
          },
        ],
        password2: [
          {
            required: true,
            message: '输入密码不一致',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value === this.PasswordParam.password1) {
                callback()
              } else {
                callback(new Error('两次输入的密码不一致'))
              }
            },
          },
        ],
      },
      advanced: false,
      showTips: false,
      loading: false,
      dealyButton: false,
      JmsUserSSHVisible: false,
      cancelJmsAuthVisible: false,
      jmsAuthTempVisible: false,
      PasswordVisible: false,
      passwordvisible1: false,
      passwordvisible2: false,
      MFAVisible: false,
      MFABindVisible: false,
      AssetTestVisible: false,
      assetTestId: 'xxx-xxx-xxx-xxx',
      assetTestInfo: '任务执行中请等待.....',
      VerificationCode: '',
      imageUrl: undefined,
      serialNumber: undefined,
      paramsData: {},
      MFABindParam: {},
      MFABindRules: {
        mfaCode1: [{ required: true, message: '请输入6位认证码', trigger: 'change' }],
      },
      loadData: parameter => {
        if (this.activeKey == 'bastonAsset') {
          const requestParameters = Object.assign({}, parameter, this.queryAssetParam)
          return jmsAssetList(requestParameters).then(res => {
            if (res.Data.hasOwnProperty('data')) {
              if (res.Data.dataUser === null && res.Data.dataAsset === null) {
                return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
              } else {
                return res.Data
              }
            }
          })
        } else {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return jmsUserList(requestParameters).then(res => {
            if (res.Data.hasOwnProperty('dataUser')) {
              if (res.Data.dataUser === null && res.Data.dataAsset === null) {
                this.userInfomation = res.Data.userInfo
                return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
              } else if (res.Data.dataAsset === null) {
                res.Data.data = res.Data.dataUser
                if (res.Data.dataUser.length > 0) {
                  this.userType = '管理员'
                  this.rightTitleContent = '用户列表'
                  this.activeKey = 'userList'
                }
                delete res.Data.dataUser
                this.userInfomation = res.Data.userInfo
                return res.Data
              } else {
                res.Data.data = res.Data.dataAsset
                if (res.Data.dataAsset.length > 0) {
                  this.userType = '堡垒机用户'
                  this.rightTitleContent = '资产列表'
                  this.activeKey = 'userAsset'
                }
                delete res.Data.dataAsset
                this.userInfomation = res.Data.userInfo
                return res.Data
              }
            }
          })
        }
      },
      rules: antdFormRulesFormat({
        founderEmail: [{ required: true, message: '请选择用户', trigger: 'change' }],
      }),
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getUserRoles(this.localUser.split('@')[0])
  },
  unmounted() {
    removeWatermark()
  },
  computed: {
    userInfo() {
      return user.state
    },
  },
  created() {
    this.user = this.userInfo
  },
  methods: {
    initTerminal() {
      if (!this.terminal) {
        // 初始化终端
        this.terminal = new Terminal({
          tabStopWidth: 4,
          lineHeight: 1.2,
          fontSize: 14,
          fontFamily: 'Consolas',
        })
        this.fitAddon = new FitAddon()
        this.terminal.loadAddon(this.fitAddon)

        // 打开终端
        console.log(this.$refs.logTerm)
        this.terminal.open(this.$refs.logTerm)

        // 添加窗口 resize 事件监听
        const that = this
        window.addEventListener(
          'resize',
          function () {
            if (that.AssetTestVisible && that.fitAddon) {
              that.fitAddon.fit()
            }
          },
          false
        )
      }
    },
    userInformationIsUpToDate(record) {
      jmsUserUpdate({ userId: record.jmsId }).then(res => {
        if (res.Data.message == 'ok') {
          notification.success({
            message: '更新成功',
          })
          this.$refs.table.refresh(true)
        }
      })
    },

    assetConnectTest(record) {
      this.AssetTestVisible = true
      this.assetTestId = 'xxx-xxx-xxx-xxx'
      this.assetTestInfo = '任务执行中请等待.....'
      this.$nextTick(() => {
        this.initTerminal()
        this.terminal.clear()
        this.terminal.reset()
        this.terminal.write(this.assetTestInfo)
        this.fitAddon.fit()
      })
      jmsAssetTest({ asset_id: record.assetId }).then(res => {
        if (res.Data.message !== '') {
          notification.success({
            message: '触发成功',
          })
          this.assetTestId = res.Data.message
        }
      })
    },
    getJmsTaskResult() {
      if (this.AssetTestVisible && this.assetTestId !== 'xxx-xxx-xxx-xxx' && this.assetTestId !== '') {
        jmsAssetTaskLog({ taskId: this.assetTestId }).then(res => {
          if (res.Data.result !== '') {
            this.assetTestInfo = res.Data.result
            this.$nextTick(() => {
              this.initTerminal()
              this.terminal.clear()
              this.terminal.reset()
              this.terminal.write(this.assetTestInfo)
              this.fitAddon.fit()
            })
          }
        })
      }
    },
    tabChange(key) {
      this.$refs.table.refresh(true)
    },
    rederitCloudTermina() {
      let routeUrl = this.$router.resolve({ path: '/devops/terminal' })
      window.open(routeUrl.href, '_blank')
    },
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite)) {
          this.userpRression = true
        }
      })
    },
    sendCode(record) {
      this.dealyButton = true
      let sendData = {}
      sendData.email = store.getters.email
      sslDownloadSend(sendData).then(res => {
        if (res.Data.message === 'ok') {
          notification.success({
            message: '已发送验证码',
          })
        } else if (res.Data.message === 'exist') {
          notification.warning({
            message: '验证码未过期',
          })
        }
      })
      setTimeout(() => {
        this.dealyButton = false
      }, 120000)
    },
    async checkCode() {
      if (this.VerificationCode == '') {
        notification.warning({
          message: '请输入验证码',
        })
        this.loading = false
        return false
      }
      this.dealyButton = false
      // 确认验证码校验通过
      let checkData = {}
      checkData.email = store.getters.email
      checkData.code = this.VerificationCode
      checkData.realTime = this.realTime
      let result = await sslDownloadCheck(checkData).then(res => {
        if (res.Data.message === 'ok') {
          return true
        } else if (res.Data.message === 'error') {
          notification.error({
            message: '输入验证码错误',
          })
          return false
        } else if (res.Data.message === 'notExist') {
          notification.warning({
            message: '验证码已过期，请重新获取',
          })
          return false
        }
      })
      return result
    },
    enableUser(value, record) {
      jmsUserUnblock({ jmsId: record.jmsId, type: value }).then(res => {
        if (res.Data.message == 'ok') {
          notification.success({
            message: '成功',
          })
        }
        this.$refs.table.refresh(true)
      })
    },
    async PasswordReset(value, record) {
      switch (value) {
        case 'visible':
          this.PasswordVisible = true
          this.VerificationCode = ''
          this.PasswordParam = {
            jmsId: record.jmsId,
            password1: '',
            password2: '',
          }
          break
        case 'cancel':
          this.PasswordVisible = false
          break
        case 'confirm':
          if (await this.checkCode()) {
            let data = {
              jmsId: this.PasswordParam.jmsId,
              jmsPw: this.PasswordParam.password1,
            }
            jmsUserPw(data).then(res => {
              if (res.Data.message == 'ok') {
                this.PasswordVisible = false
                notification.success({
                  message: '密码重置成功',
                })
              }
            })
          }
          break
      }
    },
    async JmsUserSSH(value, record) {
      this.dealyButton = false
      switch (value) {
        case 'visible':
          this.JmsUserSSHVisible = true
          this.VerificationCode = ''
          this.paramsData = {
            jmsId: record.jmsId,
          }

          break
        case 'close':
          this.JmsUserSSHVisible = false
          break
        case 'add':
          if (await this.checkCode()) {
            jmsUserSSH(this.paramsData).then(res => {
              this.JmsUserSSHVisible = false
              let data = Base64.decode(res.Data.data)
              const blob = new Blob([data], { type: 'application/x-pem-file' })
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = store.getters.email + '.pem'
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              window.URL.revokeObjectURL(url)
            })
          }
      }
    },
    MFABind(type) {
      switch (type) {
        case 'cancel':
          this.MFABindVisible = false
          this.MFABindParam = {}
          break
        case 'confirm':
          this.MFAParam.key = this.serialNumber
          this.MFAParam.code = this.MFABindParam.mfaCode1
          jmsUserMfaBond(this.MFAParam).then(res => {
            if (res.Data.message == 'ok') {
              notification.success({
                message: 'MFA绑定成功',
              })
              this.MFABindVisible = false
            }
          })
      }
    },
    async ResetMfa(type, record) {
      this.dealyButton = false
      switch (type) {
        case 'visible':
          this.MFAVisible = true
          this.MFAParam = { jmsId: record.jmsId }
          this.VerificationCode = ''
          break
        case 'cancel':
          this.MFAVisible = false
          break
        case 'confirm':
          if (await this.checkCode()) {
            this.MFABindVisible = true
            this.MFAVisible = false
            this.serialNumber = this.generateRandomString(16)
            this.imageUrl = 'https://cloud.intsig.net/api/asset/jms_user/bond?key=' + this.serialNumber
            break
          }
      }
    },
    generateRandomString(length) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      const numbers = '234567'
      const allCharacters = letters + numbers
      let result =
        letters.charAt(Math.floor(Math.random() * letters.length)) +
        numbers.charAt(Math.floor(Math.random() * numbers.length))

      for (let i = 2; i < length; i++) {
        result += allCharacters.charAt(Math.floor(Math.random() * allCharacters.length))
      }
      result = result
        .split('')
        .sort(() => 0.5 - Math.random())
        .join('')

      return result
    },
  },
}
</script>

<style lang="less" scoped>
.jms_user-type {
  text-align: center;
  font-weight: bold;
}
.jms_user_avatar {
  margin: 0 auto;
  width: 104px;
  height: 104px;
  margin-bottom: 20px;
  border-radius: 50%;
  overflow: hidden;
  img {
    height: 100%;
    width: 100%;
  }
}
.jms_user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

.terminal-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
}

/* 确保 xterm.js 容器填充整个空间 */
.terminal.xterm {
  height: 100%;
  padding: 8px;
}
</style>
