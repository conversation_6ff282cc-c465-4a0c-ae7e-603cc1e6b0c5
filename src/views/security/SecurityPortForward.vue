<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊搜索">
                <a-input
                  v-model:value="queryParam.searchText"
                  placeholder="请输入源IP/源端口/中转IP/目的IP/目的端口/邮箱/备注"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model:value="queryParam.status" button-style="solid" allowClear>
                  <a-select-option :value="1">启用</a-select-option>
                  <a-select-option :value="2">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span>
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
        <a-modal :visible="addVisible" title="新增端口转发" :closable="false">
          <a-form-model
            :model="addParam"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
            class="myform"
            ref="addForm"
            :rules="rules"
          >
            <a-form-model-item label="源IP地址" name="sourceIp">
              <a-input v-model:value="addParam.sourceIp" placeholder="请输入源IP地址" />
            </a-form-model-item>
            <a-form-model-item label="源端口" name="sourcePort">
              <a-input v-model:value="addParam.sourcePort" placeholder="请输入源端口" />
            </a-form-model-item>
            <a-form-model-item label="中转IP" name="transferIp">
              <a-input v-model:value="addParam.transferIp" placeholder="请输入中转IP" />
            </a-form-model-item>
            <a-form-model-item label="目标IP" name="destinationIp">
              <a-input v-model:value="addParam.destinationIp" placeholder="请输入目标IP" />
            </a-form-model-item>
            <a-form-model-item label="目标端口" name="destinationPort">
              <a-input v-model:value="addParam.destinationPort" placeholder="请输入目标端口" />
            </a-form-model-item>
            <a-form-model-item label="协议" name="protocol">
              <a-input v-model:value="addParam.protocol" placeholder="请输入协议,如:tcp" />
            </a-form-model-item>
            <a-form-model-item label="邮箱" name="email">
              <a-input v-model:value="addParam.email" placeholder="请输入邮箱" />
            </a-form-model-item>
            <a-form-model-item label="状态" name="status">
              <a-radio-group v-model:value="addParam.status" button-style="solid">
                <a-radio-button :value="1">启用</a-radio-button>
                <a-radio-button :value="2">禁用</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="备注" name="remark">
              <a-input v-model:value="addParam.remark" placeholder="请输入备注" />
            </a-form-model-item>
          </a-form-model>
          <template #footer>
            <tx-button key="back" @click="cancelAdd">取消</tx-button>
            <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
          </template>
        </a-modal>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record: row, text }">
          <template v-if="column.dataIndex === 'remark'">
            <div class="tooltip">
              <a-tooltip placement="left">
                <template #title>
                  <span>{{ row.remark }}</span>
                </template>
                {{ row.remark }}
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(row)">更新</tx-button>
            <tx-button type="link" size="small" @click="handleDelete(row)">删除</tx-button>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag color="green" v-if="row.status === 1">启用</a-tag>
            <a-tag color="red" v-if="row.status === 2">禁用</a-tag>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions :column="3">
            <a-descriptions-item label="协议">{{ record.protocol }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
      <a-modal :visible="updateVisible" title="更新端口转发" ref="updateModal" :closable="false">
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
          ref="updateForm"
          class="myform"
        >
          <a-form-model-item label="ID" name="id">
            <a-input v-model:value="updateParam.id" disabled />
          </a-form-model-item>
          <a-form-model-item label="源IP地址" name="sourceIp">
            <a-input v-model:value="updateParam.sourceIp" placeholder="请输入源IP地址" />
          </a-form-model-item>
          <a-form-model-item label="源端口" name="sourcePort">
            <a-input v-model:value="updateParam.sourcePort" placeholder="请输入源端口" />
          </a-form-model-item>
          <a-form-model-item label="中转IP" name="transferIp">
            <a-input v-model:value="updateParam.transferIp" placeholder="请输入中转IP" />
          </a-form-model-item>
          <a-form-model-item label="目标IP" name="destinationIp">
            <a-input v-model:value="updateParam.destinationIp" placeholder="请输入目标IP" />
          </a-form-model-item>
          <a-form-model-item label="目标端口" name="destinationPort">
            <a-input v-model:value="updateParam.destinationPort" placeholder="请输入目标端口" />
          </a-form-model-item>
          <a-form-model-item label="协议" name="protocol">
            <a-input v-model:value="updateParam.protocol" placeholder="请输入协议" />
          </a-form-model-item>
          <a-form-model-item label="邮箱" name="email">
            <a-input v-model:value="updateParam.email" placeholder="请输入邮箱" />
          </a-form-model-item>
          <a-form-model-item label="状态" name="status">
            <a-radio-group v-model:value="updateParam.status" button-style="solid">
              <a-radio-button :value="1">启用</a-radio-button>
              <a-radio-button :value="2">禁用</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="备注" name="remark">
            <a-input v-model:value="updateParam.remark" placeholder="请输入备注" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { notification } from 'ant-design-vue'
import {
  createSecurityPortForward,
  deleteSecurityPortForward,
  getSecurityPortForward,
  listSecurityPortForward,
  updateSecurityPortForward,
} from '@/api/security/port_forward'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true,
  },
  {
    title: '源IP',
    dataIndex: 'sourceIp',
    sorter: true,
    width: '400px',
  },
  {
    title: '源端口',
    dataIndex: 'sourcePort',
    sorter: true,
  },
  {
    title: '中转IP',
    dataIndex: 'transferIp',
    sorter: true,
  },
  {
    title: '目标IP',
    dataIndex: 'destinationIp',
    sorter: true,
  },
  {
    title: '目标端口',
    dataIndex: 'destinationPort',
    sorter: true,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    scopedSlots: { customRender: 'comment' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SecurityPortForward',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      addParam: {},
      updateParam: {},
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      rules: {
        sourceIp: [{ required: true, message: '请输入源IP', trigger: 'change' }],
        sourcePort: [{ required: true, message: '请输入源端口', trigger: 'change' }],
        email: [{ required: true, message: '请输入邮箱', trigger: 'change' }],
        transferIp: [{ required: true, message: '请输入中转IP', trigger: 'change' }],
        destinationIp: [{ required: true, message: '请输入目标IP', trigger: 'change' }],
        destinationPort: [{ required: true, message: '请输入目标端口', trigger: 'change' }],
        protocol: [{ required: true, message: '请输入协议', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
      },
      emailList: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listSecurityPortForward(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
            return {
              data: res.Data.data,
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage,
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  mounted() {},
  methods: {
    handleAdd() {
      this.addParam = {
        isOcr: false,
      }
      this.addVisible = true
    },
    cancelAdd() {
      this.addVisible = false
      this.confirmLoading = false
    },
    submitAdd() {
      var that = this
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createSecurityPortForward(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功',
                })
                this.cancelAdd()
                that.$refs.table.refresh()
              } else {
                notification.error({
                  message: '创建失败',
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleUpdate(row) {
      getSecurityPortForward({ id: row.id }).then(res => {
        this.updateParam = res.Data
        console.log()
      })
      this.updateVisible = true
    },
    cancelUpdate() {
      this.updateVisible = false
      this.confirmLoading = false
    },
    submitUpdate() {
      var that = this
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateSecurityPortForward(this.updateParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '修改成功',
                })
                that.$refs.table.refresh()
                this.cancelUpdate()
              } else {
                notification.error({
                  message: '修改失败',
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete(row) {
      var that = this
      this.$confirm({
        title: '确认删除',
        content: `确认删除该映射吗？`,
        onOk() {
          deleteSecurityPortForward({ id: row.id })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功',
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>
<style lang="less" scoped>
.myform {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
