<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.searchText"
                  placeholder="TokenId/TokenName/PublicKey"
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="状态">
                <a-select v-model:value="queryParam.checkStatus" allowClear placeholder="请选择">
                  <a-select-option value="1">检测</a-select-option>
                  <a-select-option value="0">不检测</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record}">
          <template v-if="column.dataIndex == 'checkStatus'">
            <a-tag :color="statusColorFilter(record.checkStatus)">{{ statusFilter(record.checkStatus) }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDelete(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a >删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="状态" name="checkStatus">
            <a-select v-model:value="form.checkStatus" allowClear placeholder="请选择">
              <a-select-option value="0">不检测</a-select-option>
              <a-select-option value="1">检测</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { Ellipsis, STable } from '@/components'
import { ufileTokenDelete, ufileTokenList, ufileTokenPut } from '@/api/security/ufile_token'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const statusMap = {
  1: {
    color: 'green',
    text: '检测'
  },
  0: {
    color: 'red',
    text: '不检测'
  }
}

const columns = [
  {
    title: '项目名称',
    dataIndex: 'project',
    sorter: true
  },
  {
    title: 'TokenId',
    dataIndex: 'tokenId',
    sorter: true
  },
  {
    title: 'TokenName',
    dataIndex: 'tokenName',
    sorter: true
  },
  {
    title: 'PublicKey',
    dataIndex: 'publicKey',
    sorter: true
  },
  {
    title: '过期时间',
    dataIndex: 'expireTime',
    sorter: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'checkStatus',
    sorter: true
  }, {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center'
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'SecurityRam',
  components: {
    STable,
    Ellipsis
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 8 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      form: {      },
      updateVisible: false,
      confirmLoading: false,
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return ufileTokenList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        checkStatus: [{ required: true, message: '请填写状态', trigger: 'change' }],
      }
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  unmounted() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    handleUpdate(record) {
      this.form.id = record.id
      this.form.checkStatus = record.checkStatus
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          ufileTokenPut(this.form).then(res => {
            if (res.Data.message === 'success') {
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            } else {
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            }
          })
        } else {
          this.$message.error('更新失败')
        }
        this.updateVisible = false
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusColorFilter(type) {
      return statusMap[type]?.color || type
    },
    handleDelete(record) {
      ufileTokenDelete({ id: record.id }).then(res => {
        if (res.Data.message === 'success') {
          this.$refs.table.refresh()
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败,后端接口错误，请联系运维开发排查~')
        }
      })
    }
  }
}
</script>
