<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=656998433" style="text-decoration: none">
          云厂商RAM用户管理
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input v-model:value="queryParam.searchName" placeholder="用户名/负责人" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="云厂商">
                <a-select v-model:value="queryParam.supplier" allowClear showSearch>
                  <a-select-option v-for="item in supplierList" :key="item" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="登录状态">
                <a-select v-model:value="queryParam.status" allowClear>
                  <a-select-option v-for="item in statusList" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 5) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <a-popconfirm
                  placement="topRight"
                  @confirm="changeAccountPermission('')"
                >
                  <template #title>
                    <p>此入口仅供新账号的申请使用！！！</p>
                    <p>如果已有账号，请点击对应账号的 权限变更。</p>
                  </template>
                  <template #icon>
                    <a-icon type="question-circle-o" style="color: red" />
                  </template>
                  <tx-button type="primary" style="float: right">新账号申请</tx-button>
                </a-popconfirm>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table ref="table" size="default" rowKey="id" :pagination="pagination" :columns="columns" :data="loadData">
        <template #expandedRowRender="{ record }">
          <div v-if="record.accessKeys.length > 0">
            <a>AKSK</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsAKSKChildren"
                  :data-source="record.accessKeys"
                  :pagination="pagination"
                  :rowKey="record => record.AccessKeyId"
                  class="mytable"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex == 'Status'">
                      <a-tag :color="StatusColorFilter(record.Status)">{{ StatusFilter(record.Status) }}</a-tag>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </div>
          <div v-if="record.authPolicies.length > 0">
            <a>授权策略</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsPolicesChildren"
                  :data-source="record.authPolicies"
                  :pagination="pagination"
                  :rowKey="record => record.PolicyName"
                  class="mytable"
                ></a-table>
              </a-col>
            </a-row>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'hasMfa'">
            <a-tag :color="MFAColorFilter(record.hasMfa)">{{ MFAFilter(record.hasMfa) }}</a-tag>
          </template>
          <template v-if="column.dataIndex == 'consoleLogin'">
            <a-tag :color="WebLogColorFilter(record.consoleLogin)">{{ WebLogilter(record.consoleLogin) }}</a-tag>
          </template>
          <template v-if="column.dataIndex == 'action' && record.consoleLogin != 3">
            <a v-if="record.consoleLogin === 1 || record.accountType === 2" @click="changeAccountPermission(record)">权限变更</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="record.consoleLogin == 2">
                    <a v-if="userpRression || record.supplier === 'Aliyun(启信宝)' || record.supplier === 'AWS(海外)' || record.supplier === 'AWS(CS)' || record.accountType === 2" style="width: 40px" @click="DisableWeb('visible', record, 'enable')">解禁</a>
                    <a v-else style="width: 40px" @click="toKeycloak(record)">已迁移至Keycloak</a>
                  </a-menu-item>
                  <a-menu-item v-if="record.consoleLogin == 1">
                    <a style="width: 40px" @click="PasswordReset('visible', record)">密码重置</a>
                  </a-menu-item>
                  <a-menu-item v-if="record.consoleLogin == 1">
                    <a style="width: 40px" @click="ResetMfa('visible', record)">MFA重置</a>
                  </a-menu-item>
                  <a-menu-item v-if="record.consoleLogin == 1">
                    <a v-if="userpRression" style="width: 40px" @click="DisableWeb('visible', record)">禁用</a>
                  </a-menu-item>
                  <!-- <a-menu-item><a v-if="userpRression" style="width: 40px" @click="DeleteAccount('visible', record)">注销</a></a-menu-item> -->
                </a-menu>
              </template>
              <a>
                更多
                <a-icon type="down" />
              </a>
            </a-dropdown>
          </template>
          <template v-if="column.dataIndex == 'action' && record.consoleLogin == 3">
            <tx-button type="link" @click="changeAccountPermission(record)">再次申请</tx-button>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal v-model:visible="PasswordVisible" title="密码重置">
      <template #footer>
        <tx-button key="back" @click="PasswordReset('cancel')">取消</tx-button>
        <tx-button type="primary" @click="PasswordReset('confirm')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="PasswordForm"
        :model="PasswordParam"
        :rules="PasswordRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="密码" name="password1" class="my-ant-form-item">
          <a-input-password v-model:value="PasswordParam.password1" v-model:visible="passwordvisible1" />
        </a-form-model-item>
        <a-form-model-item label="确认密码" name="password2" class="my-ant-form-item">
          <a-input-password v-model:value="PasswordParam.password2" v-model:visible="passwordvisible2" />
        </a-form-model-item>
        <a-form-model-item label="验证码" class="my-ant-form-item">
          <a-row>
            <a-col :span="17">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="MFAVisible" title="账号MFA重置">
      <template #footer>
        <tx-button key="back" @click="ResetMfa('cancel')">取消</tx-button>
        <tx-button type="primary" @click="ResetMfa('confirm')" :loading="loading">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="MFAForm"
        :model="MFAParam"
        :rules="MFARules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="验证码" class="my-ant-form-item">
          <a-row>
            <a-col :span="18">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="MFABindVisible" title="AWS账号MFA绑定">
      <template #footer>
        <tx-button key="back" @click="MFABind('cancel')">取消</tx-button>
        <tx-button type="primary" @click="MFABind('confirm')">确定</tx-button>
      </template>
      请扫描二维码绑定MFA, 并输入
      <span style="color: red">二个连续</span>
      的认证码。(请在
      <span style="color: red">3分钟</span>
      内绑定!)
      <a-row :gutter="48">
        <a-col :md="12" :sm="24">
          <img :src="imageUrl" alt="Image description" />
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-model
            layout="horizontal"
            ref="MFABindForm"
            :model="MFABindParam"
            :rules="MFABindRules"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <br />
            <br />
            <br />
            <br />
            <a-form-model-item label="认证码1" name="mfaCode1" class="my-ant-form-item">
              <a-input v-model:value="MFABindParam.mfaCode1" />
            </a-form-model-item>
            <a-form-model-item label="认证码2" name="mfaCode2" class="my-ant-form-item">
              <a-input v-model:value="MFABindParam.mfaCode2" />
            </a-form-model-item>
          </a-form-model>
        </a-col>
      </a-row>
    </a-modal>
    <a-modal
      v-model:visible="DisableWebVisible"
      :title="EnableWebVisible ? '开启账号Web登录权限' : '禁用账号Web登录权限'"
    >
      <template #footer>
        <tx-button key="back" @click="DisableWeb('cancel')">取消</tx-button>
        <tx-button type="primary" @click="DisableWeb('confirm')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="MFAForm"
        :model="DisableWebParam"
        :rules="DisableWebRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="验证码" class="my-ant-form-item">
          <a-row>
            <a-col :span="18">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="DeleteAccountVisible" title="注销账号">
      <template #footer>
        <tx-button key="back" @click="DeleteAccount('cancel')">取消</tx-button>
        <tx-button type="primary" @click="DeleteAccount('confirm')">确定</tx-button>
      </template>
      <a-form-model
        layout="horizontal"
        ref="MFAForm"
        :model="DeleteAccountParam"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="验证码" class="my-ant-form-item">
          <a-row>
            <a-col :span="18">
              <a-input v-model:value="VerificationCode" placeholder="请输入验证码" style="width: 180px" />
            </a-col>
            <a-col :span="6">
              <tx-button key="back" v-if="dealyButton" disabled @click="sendCode()">获取验证码</tx-button>
              <tx-button key="back1" v-else @click="sendCode()">获取验证码</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { Ellipsis, STable } from '@/components'
import { deleteRamUser, getRamUser } from '@/api/security/ram'
import { BindAwsMFA, DisableWebLog, EnableWebLog, MFAReset, PasswordReset } from '@/api/security/account'
import { sslDownloadCheck, sslDownloadSend } from '@/api/domain/domain'
import { notification } from 'ant-design-vue'
import store from '@/store'
import { getUserList } from '@/api/permission/user'
import { judgeEnv } from '@/utils/util'

const columns = [
  {
    title: '云厂商',
    dataIndex: 'supplier',
    sorter: true,
  },
  {
    title: '用户名称',
    dataIndex: 'userName',
    sorter: true,
  },
  {
    title: '控制台',
    dataIndex: 'consoleLogin',
    width: '80px',
  },
  {
    title: '最后登录时间',
    dataIndex: 'lastLoginDate',
  },
  {
    title: 'MFA状态',
    dataIndex: 'hasMfa',
    width: '100px',
    // scopedSlots: { customRender: 'hasMfa' },
    // sorter: true,
  },
  {
    title: '账号创建日期',
    dataIndex: 'accountCreateDate',
  },
  {
    title: '负责人',
    dataIndex: 'comments',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '180px',
    align: 'center',
    scopedSlots: { customRender: 'action' },
  },
]
const columnsAKSKChildren = [
  {
    title: 'AKSK Id',
    dataIndex: 'AccessKeyId',
  },
  {
    title: '创建时间',
    dataIndex: 'CreateDate',
  },
  {
    title: '最后使用时间',
    dataIndex: 'LastUsedDate',
  },
  {
    title: '当前状态',
    dataIndex: 'Status',
    scopedSlots: { customRender: 'Status' },
  },
]
const columnsPolicesChildren = [
  {
    title: '策略Id',
    dataIndex: 'PolicyId',
  },
  {
    title: '策略名称',
    dataIndex: 'PolicyName',
  },
  {
    title: '策略类型',
    dataIndex: 'PolicyType',
  },
  {
    title: '策略描述',
    dataIndex: 'PolicyDescription',
  },
]
const WebLogMap = {
  1: {
    color: 'green',
    text: '开启',
  },
  2: {
    color: 'red',
    text: '禁用',
  },
  3: {
    color: 'gray',
    text: '注销',
  },
}
const MFAMap = {
  true: {
    color: 'green',
    text: '开启',
  },
  false: {
    color: 'red',
    text: '关闭',
  },
}
const StatusMap = {
  Active: {
    color: 'green',
    text: '激活',
  },
  Inactive: {
    color: 'red',
    text: '禁用',
  },
}
const pagination = {
  showTotal: total => `共 ${total} 条`,
  defaultPageSize: 10,
  hideOnSinglePage: true,
}
export default {
  name: 'SupplierUser',
  components: {
    STable,
  },
  data() {
    this.columns = columns
    this.columnsAKSKChildren = columnsAKSKChildren
    this.columnsPolicesChildren = columnsPolicesChildren
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      loading: false,
      domainListMini: [],
      scrollPage: 1,
      valueData: '',
      realTime: '',
      VerificationCode: '',
      treePageSize: 50,
      supplierList: [
        'Aliyun(启信宝)',
        'Aliyun(合合)',
        '腾讯云(合合)',
        '腾讯云(CS)',
        '腾讯云(启信宝)',
        'AWS(海外)',
        'AWS(合合)',
        'AWS(CS)',
      ],
      statusList: [
        { label: '开启', value: '1' },
        { label: '禁用', value: '2' },
        { label: '注销', value: '3' },
      ],
      domainInfo: [],
      addDomain: [],
      addQueryParam: { url: '' },
      userRoles: [],
      userRolesWhite: 'admin',
      userpRression: false,
      visible: false,
      passwordvisible1: false,
      passwordvisible2: false,
      PasswordVisible: false,
      MFAVisible: false,
      MFABindVisible: false,
      MFAParam: {},
      MFABindParam: {},
      imageUrl: undefined,
      PasswordParam: {},
      DisableWebVisible: false,
      DeleteAccountVisible: false,
      DeleteAccountParam: {},
      dealyButton: false,
      serialNumber: '',
      EnableWebVisible: false,
      DisableWebParam: {},
      editVisble: false,
      confirmLoading: false,
      Rules: {
        icpWebNo: [{ required: true, message: '请输入域名备案号', trigger: 'blur' }],
        webName: [{ required: true, message: '请输入网页名称', trigger: 'blur' }],
        url: [{ required: true, message: '请输入首页地址', trigger: 'change' }],
        domain: [{ required: true, message: '请选择相关域名', trigger: 'change' }],
        ip: [{ required: true, message: '请输入ip', trigger: 'change' }],
        picName: [{ required: true, message: '请输入备案负责人', trigger: 'change' }],
        source: [{ required: true, message: '请输入来源', trigger: 'change' }],
      },
      MFABindRules: {
        mfaCode1: [{ required: true, message: '请输入6位认证码', trigger: 'change' }],
        mfaCode2: [{ required: true, message: '请输入6位认证码', trigger: 'change' }],
      },
      PasswordRules: {
        password1: [
          { required: true, message: '请输入用户密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
              if (!value || regex.test(value)) {
                callback()
              } else {
                callback(new Error('密码必须8位以上, 且有大小写(A-Za-z)特殊符号(@$!%*?&)数字(0-9)'))
              }
            },
            trigger: 'blur',
          },
        ],
        password2: [
          {
            required: true,
            message: '输入密码不一致',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value === this.PasswordParam.password1) {
                callback()
              } else {
                callback(new Error('两次输入的密码不一致'))
              }
            },
          },
        ],
      },
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
        return getRamUser(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
            for (let i = 0; i < res.Data.data.length; i++) {
              if (res.Data.data[i].accessKeys !== 'null') {
                res.Data.data[i].accessKeys = JSON.parse(res.Data.data[i].accessKeys)
              } else {
                res.Data.data[i].accessKeys = []
              }
              if (res.Data.data[i].authPolicies !== 'null') {
                res.Data.data[i].authPolicies = JSON.parse(res.Data.data[i].authPolicies)
              } else {
                res.Data.data[i].authPolicies = []
              }
            }
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {
    this.getUserRoles(this.localUser.split('@')[0])
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes(this.userRolesWhite) || this.userRoles.includes("devops_admin")) {
          this.userpRression = true
        }
      })
    },
    MFAFilter(type) {
      return MFAMap[type]?.text || type
    },
    MFAColorFilter(type) {
      return MFAMap[type]?.color || type
    },
    WebLogilter(type) {
      return WebLogMap[type]?.text || type
    },
    WebLogColorFilter(type) {
      return WebLogMap[type]?.color || type
    },
    StatusFilter(type) {
      return StatusMap[type]?.text || type
    },
    StatusColorFilter(type) {
      return StatusMap[type]?.color || type
    },
    handleChange() {
      this.$refs.table.refresh(true)
    },
    changeAccountPermission(record) {
      if (record == '') {
        this.$router.push({ path: '/workflow/person-account-permission' })
        return
      }
      let recordData = {
        supplier: record.supplier,
        accountName: record.userName,
      }
      // 拼接数据
      let permissions = []
      let permissions_type = []
      switch (record.supplier) {
        case 'Aliyun(启信宝)':
        case 'Aliyun(合合)':
          for (let i = 0; i < record.authPolicies.length; i++) {
            permissions.push(record.authPolicies[i].PolicyName)

            let permissions_type1 = {
              policyType: record.authPolicies[i].PolicyType,
              description: record.authPolicies[i].PolicyDescription,
              key: record.authPolicies[i].PolicyName,
            }
            permissions_type.push(permissions_type1)
          }
          break
        case '腾讯云(合合)':
        case '腾讯云(CS)':
        case '腾讯云(启信宝)':
          for (let i = 0; i < record.authPolicies.length; i++) {
            permissions.push(record.authPolicies[i].PolicyId)
            let permissions_type1 = {
              policyType: record.authPolicies[i].PolicyName,
              description: record.authPolicies[i].PolicyDescription,
              key: record.authPolicies[i].PolicyId,
            }
            permissions_type.push(permissions_type1)
          }
          break
        case 'AWS(海外)':
        case 'AWS(合合)':
        case 'AWS(CS)':
          for (let i = 0; i < record.authPolicies.length; i++) {
            permissions.push(record.authPolicies[i].PolicyId)
            let permissions_type1 = {
              policyType: record.authPolicies[i].PolicyName,
              description: record.authPolicies[i].PolicyName,
              key: record.authPolicies[i].PolicyId,
            }
            permissions_type.push(permissions_type1)
          }
          break
        default:
          break
      }
      recordData.permissions = permissions
      recordData.permissions_type = permissions_type
      this.$router.push({ path: '/workflow/person-account-permission', query: { record: JSON.stringify(recordData) } })
    },
    async DeleteAccount(type, record) {
      switch (type) {
        case 'visible':
          this.DeleteAccountVisible = true
          this.DeleteAccountParam = JSON.parse(JSON.stringify(record))
          this.VerificationCode = ''
          this.realTime = ''
          this.dealyButton = false
          break
        case 'cancel':
          this.DeleteAccountVisible = false
          break
        case 'confirm':
          if (await this.checkCode()) {
            deleteRamUser({ id: this.DeleteAccountParam.id }).then(res => {
              if (res.Code == 200) {
                this.$message.success('删除成功', 3)
                this.$refs.table.refresh(true)
                this.DeleteAccountVisible = false
              }
            })
          }
          break
      }
    },
    async PasswordReset(type, record) {
      switch (type) {
        case 'visible':
          this.PasswordVisible = true
          this.PasswordParam = JSON.parse(JSON.stringify(record))
          this.VerificationCode = ''
          this.realTime = ''
          this.dealyButton = false
          break
        case 'cancel':
          this.PasswordVisible = false
          break
        case 'confirm':
          if (await this.checkCode()) {
            let data = JSON.parse(JSON.stringify(this.PasswordParam))
            data.password = data.password1
            delete data.password1
            delete data.password2
            PasswordReset(data).then(res => {
              if (res.Code == 200) {
                this.$message.success('密码重置成功')
                this.PasswordVisible = false
              }
            })
            this.PasswordVisible = false
            break
          }
      }
    },
    MFABindTimeOut() {
      setTimeout(() => {
        this.MFABind('cancel', '')
      }, 180000)
    },
    MFABind(type, record) {
      switch (type) {
        case 'cancel':
          this.MFABindVisible = false
          this.MFABindParam = {}
          break
        case 'confirm':
          this.MFAParam.mfaCode1 = this.MFABindParam.mfaCode1
          this.MFAParam.mfaCode2 = this.MFABindParam.mfaCode2
          this.MFAParam.serialNumber = this.serialNumber
          BindAwsMFA(this.MFAParam).then(res => {
            if (res.Data.code == 200) {
              this.$message.success('绑定成功')
              this.MFABindVisible = false
            }
          })
      }
    },
    sendCode(record) {
      this.dealyButton = true
      this.downloadVisible = true
      let sendData = {}
      sendData.email = store.getters.email
      sendData.type = 'realtime'
      sslDownloadSend(sendData).then(res => {
        if (res.Data.message === 'ok') {
          notification.success({
            message: '已发送验证码',
          })
          this.realTime = res.Data.time
        } else if (res.Data.message === 'exist') {
          notification.warning({
            message: '验证码未过期',
          })
        }
      })
      setTimeout(() => {
        this.dealyButton = false
      }, 120000)
    },
    async checkCode() {
      if (this.VerificationCode == '') {
        notification.warning({
          message: '请输入验证码',
        })
        this.loading = false
        return false
      }
      // 确认验证码校验通过
      let checkData = {}
      checkData.email = store.getters.email
      checkData.code = this.VerificationCode
      checkData.type = 'realtime'
      checkData.realTime = this.realTime
      let result = await sslDownloadCheck(checkData).then(res => {
        if (res.Data.message === 'ok') {
          return true
        } else if (res.Data.message === 'error') {
          notification.error({
            message: '输入验证码错误',
          })
          return false
        } else if (res.Data.message === 'notExist') {
          notification.warning({
            message: '验证码已过期，请重新获取',
          })
          return false
        }
      })
      return result
    },
    async ResetMfa(type, record) {
      this.dealyButton = false
      switch (type) {
        case 'visible':
          this.MFAVisible = true
          this.MFAParam = {}
          this.MFAParam = JSON.parse(JSON.stringify(record))
          this.VerificationCode = ''
          this.realTime = ''
          this.loading = false
          break
        case 'cancel':
          this.MFAVisible = false
          this.MFAParam = {}
          break
        case 'confirm':
          this.loading = true
          if (await this.checkCode()) {
            MFAReset(this.MFAParam).then(res => {
              this.loading = false
              if (res.Data.code == 200) {
                if (
                  this.MFAParam.supplier == 'AWS(海外)' ||
                  this.MFAParam.supplier == 'AWS(合合)' ||
                  this.MFAParam.supplier == 'AWS(CS)'
                ) {
                  this.serialNumber = res.Data.serialNumber
                  const base64ToUint8Array = base64 => {
                    const binaryString = window.atob(base64)
                    const length = binaryString.length
                    const uint8Array = new Uint8Array(length)
                    for (let i = 0; i < length; i++) {
                      uint8Array[i] = binaryString.charCodeAt(i)
                    }
                    return uint8Array
                  }

                  // 将 Base64 转换为 Blob 对象
                  const base64ToBlob = base64 => {
                    const uint8Array = base64ToUint8Array(base64)
                    return new Blob([uint8Array], { type: 'application/octet-stream' })
                  }

                  // 转换 Base64 为 Blob 对象
                  const imageBlob = base64ToBlob(res.Data.message)

                  this.imageUrl = URL.createObjectURL(imageBlob)
                  this.MFABindVisible = true
                  this.MFABindTimeOut()
                  this.MFAVisible = false
                  this.MFABindParam = {}
                } else {
                  this.$message.success('MFA重置成功', 3)
                  this.$refs.table.refresh(true)
                  this.MFAVisible = false
                }
              }
            })
            break
          }
      }
    },
    async DisableWeb(type, record, value) {
      switch (type) {
        case 'visible':
          this.DisableWebVisible = true
          this.DisableWebParam = JSON.parse(JSON.stringify(record))
          if (value == 'enable') {
            this.EnableWebVisible = true
          }
          this.VerificationCode = ''
          this.realTime = ''
          this.dealyButton = false
          break
        case 'cancel':
          this.DisableWebVisible = false
          this.DisableWebParam = {}
          this.EnableWebVisible = false
          break
        case 'confirm':
          if (await this.checkCode()) {
            if (this.EnableWebVisible) {
              EnableWebLog(this.DisableWebParam).then(res => {
                if (res.Code == 200) {
                  this.$message.success('解禁成功', 3)
                  this.DisableWebVisible = false
                  this.$refs.table.refresh(true)
                }
              })
            } else {
              DisableWebLog(this.DisableWebParam).then(res => {
                if (res.Code == 200) {
                  this.$message.success('禁用成功', 3)
                  this.DisableWebVisible = false
                  this.$refs.table.refresh(true)
                }
              })
            }
          }
          break
      }
    },
    toKeycloak () {
      const env = judgeEnv()
      let url = 'https://cloud.intsig.net/kms/keycloak'
      if (env === 'TEST') {
        url = 'https://cloud-test.intsig.net/kms/keycloak'
      } else if (env === 'DEV') {
        url = 'http://localhost:8080/kms/keycloak'
      }
      window.open(url)
    }
  },
  mounted() {},
}
</script>

<style lang="less" scoped>
.my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

//   /deep/.ant-table tfoot>tr>td, .ant-table tfoot>tr>th, .ant-table-tbody>tr>td, .ant-table-thead>tr>th {
//     overflow-wrap: break-word;
//     /* padding: 16px; */
//     position: relative;
// }
.mytable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
