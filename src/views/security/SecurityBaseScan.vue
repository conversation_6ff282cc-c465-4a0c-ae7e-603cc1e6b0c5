<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form>
          <a-tabs v-model:activeKey="activeKey" @change="tabChange">
            <a-tab-pane key="taskList" tab="基线检查项列表">
              <a-row :gutter="48">
                <a-col :md="4" :sm="24">
                  <a-form-item label="安全类别">
                    <a-select v-model:value="queryParam.taskType" allowClear :showSearch="true">
                      <a-select-option v-for="item in TaskTypeList" :key="item" :value="item">
                        {{ item }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="检查项内容">
                    <a-input
                      v-model:value="queryParam.taskName"
                      placeholder="xxxx"
                      @pressEnter="$refs.table.refresh()"
                    />
                  </a-form-item>
                </a-col>
                <a-col :md="14" :sm="24">
                  <span>
                    <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                    <tx-button type="primary" style="float: right" icon="plus" @click="handleAdd">新增</tx-button>
                  </span>
                </a-col>
              </a-row>
            </a-tab-pane>
            <a-tab-pane key="logList" tab="检查项扫描日志" force-render>
              <a-row :gutter="48">
                <a-col :md="4" :sm="24">
                  <a-form-item label="安全类别">
                    <a-select v-model:value="queryParam.taskType" allowClear :showSearch="true">
                      <a-select-option v-for="item in TaskTypeList" :key="item" :value="item">
                        {{ item }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="检查项内容">
                    <a-input
                      v-model:value="queryParam.taskName"
                      placeholder="xxxx"
                      @pressEnter="$refs.table.refresh()"
                    />
                  </a-form-item>
                </a-col>
                <a-col :md="4" :sm="24">
                  <a-form-item label="执行时间">
                    <a-date-picker v-model:value="queryParam.time" />
                  </a-form-item>
                </a-col>
                <a-col :md="10" :sm="24">
                  <span>
                    <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                  </span>
                </a-col>
              </a-row>
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </div>
      <div class="table-operator">
        <a-modal :visible="addVisible" title="新增检查项" :closable="false">
          <a-form-model
            :model="addParam"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
            class="myform"
            ref="addForm"
            :rules="rules"
          >
            <a-form-item label="安全类别" name="taskType">
              <a-select v-model:value="addParam.taskType" :showSearch="true">
                <a-select-option v-for="item in TaskTypeList" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-model-item label="检查项内容" name="taskName">
              <a-input v-model:value="addParam.taskName" placeholder="" />
            </a-form-model-item>
            <a-form-model-item label="检查项详情" name="checkContent">
              <a-input v-model:value="addParam.checkContent" />
            </a-form-model-item>
            <a-form-item label="检查范围" name="assetRange">
              <a-select v-model:value="addParam.assetRange" :showSearch="true">
                <a-select-option v-for="item in TaskRangList" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-form-model>
          <template #footer>
            <tx-button key="back" @click="cancelAdd">取消</tx-button>
            <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
          </template>
        </a-modal>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="activeKey === 'taskList' ? columnTasks : columnLogs"
        :data="loadData"
      >
        <template v-if="activeKey == 'logList'" #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="异常服务器清单">{{ record.assets }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{ column, record: row, text }">
          <template v-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(row)">更新</tx-button>
            <tx-button type="link" size="small" @click="handleDelete(row)">删除</tx-button>
          </template>
        </template>
      </s-table>
      <a-modal :visible="updateVisible" title="更新检查项" ref="updateModal" :closable="false">
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
          ref="updateForm"
          class="myform"
        >
          <a-form-item label="安全类别" name="taskType">
            <a-select v-model:value="updateParam.taskType" :showSearch="true" disabled>
              <a-select-option v-for="item in TaskTypeList" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-model-item label="检查项内容" name="taskName">
            <a-input v-model:value="updateParam.taskName" placeholder="" disabled />
          </a-form-model-item>
          <a-form-model-item label="检查项详情" name="checkContent">
            <a-input v-model:value="updateParam.checkContent" />
          </a-form-model-item>
          <a-form-item label="检查范围" name="assetRange">
            <a-select v-model:value="updateParam.assetRange" :showSearch="true">
              <a-select-option v-for="item in TaskRangList" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import {
  baseTaskList,
  baseTaskHistory,
  baseTaskCreate,
  baseTaskUpdate,
  baseTaskDelete,
} from '@/api/security/security_base_scan'
import { getUserList } from '@/api/permission/user'

import { notification } from 'ant-design-vue'

const columnTasks = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '安全类别',
    dataIndex: 'taskType',
  },
  {
    title: '检查项内容',
    dataIndex: 'taskName',
  },
  {
    title: '检查项详情',
    dataIndex: 'checkContent',
  },
  {
    title: '检查范围',
    dataIndex: 'assetRange',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '120px',
    scopedSlots: { customRender: 'action' },
  },
]
const columnLogs = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '安全类别',
    dataIndex: 'taskType',
  },
  {
    title: '检查项内容',
    dataIndex: 'taskName',
  },
  {
    title: '扫描日期',
    dataIndex: 'runDay',
  },
  {
    title: '异常资产数量',
    dataIndex: 'numbers',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SecurityBaseScan',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columnTasks = columnTasks
    this.columnLogs = columnLogs
    this.pagination = pagination
    return {
      addParam: {},
      updateParam: {
        id: 0,
        password: 0,
      },
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      activeKey: 'taskList',
      // 高级搜索 展开/关闭
      advanced: false,
      switchLoading: false,
      // 查询参数
      queryParam: {
        time: null,
      },
      rules: {
        taskName: [{ required: true, message: '输入检查项内容', trigger: 'blur' }],
        taskType: [{ required: true, message: '选择安全类型', trigger: 'blur' }],
        checkContent: [{ required: true, message: '输入检查项详情信息', trigger: 'blur' }],
        assetRange: [{ required: true, message: '选择检查范围', trigger: 'blur' }],
      },
      userEmailList: [],
      statusList: [
        { value: 1, label: '开启' },
        { value: 2, label: '暂停' },
      ],
      notionMethodList: [
        {
          value: 'call',
          label: '电话',
        },
        {
          value: 'qw',
          label: '合小云',
        },
        {
          value: 'email',
          label: '邮件',
        },
      ],
      TaskTypeList: ['SSH配置', '软件', '文件权限', '账号与密码', '系统', '主机防护', 'webshell配置', 'rsync配置'],
      TaskRangList: ['全量服务器', '生产业务服务器', '数据库服务器'],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        if (this.activeKey === 'logList') {
          console.log(this.queryParam)
          this.queryParam.runDay = ''
          if (this.queryParam.time !== null && this.queryParam.time !== undefined) {
            this.queryParam.runDay = this.queryParam.time.format('YYYY-MM-DD')
          }
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return baseTaskHistory(requestParameters).then(res => {
            if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
              for (let i = 0; i < res.Data.data.length; i++) {
                if (res.Data.data[i].assets !== null && res.Data.data[i].assets !== '') {
                  res.Data.data[i].numbers = res.Data.data[i].assets.split(',').length
                } else {
                  res.Data.data[i].numbers = 0
                }
              }
              return res.Data
            } else {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            }
          })
        } else {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return baseTaskList(requestParameters).then(res => {
            if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
              return res.Data
            } else {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            }
          })
        }
      },
    }
  },
  mounted() {},
  methods: {
    tabChange(key) {
      this.$refs.table.refresh(true)
    },
    changeStatusSwitch(checked, row) {
      this.switchLoading = true
      if (checked) {
        row.taskStatus = 1
      } else {
        row.taskStatus = 2
      }
      updateJmsBaseTask(row)
        .then(res => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '更新成功',
            })
            this.switchLoading = false
            this.$refs.table.refresh(true)
          }
        })
        .catch(() => {
          this.switchLoading = false
          this.$refs.table.refresh(true)
        })
    },
    handleAdd() {
      this.addParam = {
        env: 'online',
      }
      this.addVisible = true
    },
    cancelAdd() {
      this.addVisible = false
      this.confirmLoading = false
    },
    submitAdd() {
      var that = this
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          baseTaskCreate(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功',
                })
                this.cancelAdd()
                that.$refs.table.refresh(true)
              } else {
                notification.error({
                  message: '创建失败',
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleUpdate(row) {
      this.updateParam = {
        id: row.id,
        taskType: row.taskType,
        taskName: row.taskName,
        checkContent: row.checkContent,
        assetRange: row.assetRange,
      }
      this.updateVisible = true
    },
    cancelUpdate() {
      this.updateVisible = false
      this.confirmLoading = false
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    submitUpdate() {
      var that = this
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          baseTaskUpdate(this.updateParam).then(res => {
            if (res !== undefined && res.Code === 200) {
              notification.success({
                message: '修改成功',
              })
              that.$refs.table.refresh(true)
              this.cancelUpdate()
            } else {
              notification.error({
                message: '修改失败',
              })
              this.confirmLoading = false
            }
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete(row) {
      var that = this
      this.$confirm({
        title: `确认删除 ( 检查项:${row.taskName} )`,
        onOk() {
          baseTaskDelete({ id: row.id })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功',
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>
<style lang="less" scoped>
.myform {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
