<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="用户名">
                <a-input v-model:value="queryParam.name" placeholder="用户名" @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ip" placeholder="IP" @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <!--                <tx-button style="margin-left: 8px" type="primary" @click="addJmsAuth">添加权限</tx-button>-->
                <tx-button style="margin-left: 8px" type="primary" @click="addJmsAuthTemp">临时权限</tx-button>
                <tx-button style="margin-left: 8px" type="primary" @click="cancelJmsAuth">取消权限</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'expireTime' && text === '0001-01-01 00:00:00'">--</template>
          <template v-if="column.dataIndex === 'role'">{{ roleFilter(text) }}</template>
          <template v-if="column.dataIndex === 'taskLog' ">
            <div v-if="!record.role.startsWith('cancel')">
              <a  @click="handleJmsAuthDetail(record)">详情</a>
              <a-divider type="vertical"/>
              <a  @click="handleRetry(record)">重试</a>
            </div>
            <div v-else>
            </div>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal
      title="取消服务器权限"
      :visible="cancelJmsAuthVisible"
      width="650px"
      @cancel="cancelJmsAuthVisible = false"
    >
      <a-form-model
        ref="cancelJmsAuthForm"
        :model="cancelTemp.content"
        :label-col="{ span: 6 }"
        :rules="cancelRules"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="用户" name="filterEmail">
          <a-select
            v-model:value="cancelTemp.content.filterEmail"
            :options="principalListMini"
            :filter-option="filterOption"
            style="width: 100%"
            placeholder="负责人可输入或选择"
            @popupScroll="handlePopupScroll"
            @search="handleSearch"
            show-search
            allowClear
          ></a-select>
        </a-form-model-item>
        <a-form-model-item  label="机器列表" name="hostList">
          <a-select
            v-model:value="cancelTemp.content.hostList"
            mode="tags"
            style="width: 100%"
            @change="checkValidate"
            @popupScroll="handleAuthPopupScroll"
            @search="handleIpSearch"
            placeholder="选择服务器IP"
          >
            <a-select-option v-for="i in info" :key="i.ip">
              {{ i.ip + '(' + i.hostname + ')' }}
            </a-select-option>
          </a-select>
          <p style="color: red; font-size: 14px" v-show="showTips">请输入合法的IP</p>
        </a-form-model-item>
        <a-form-model-item name="role" label="权限类型">
          <a-radio-group v-model:value="cancelTemp.content.role" button-style="solid">
            <a-radio-button value="common">普通权限</a-radio-button>
            <a-radio-button value="root">sudo权限</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
      <template #footer>
        <a-button key="submit" @click="cancelJmsAuthVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="loading" :disabled="!isValidHostList" @click="CancelJmsAuthData">确认</a-button>
      </template>
    </a-modal>
    <a-modal
      title="服务器临时授权"
      :visible="jmsAuthTempVisible"
      @cancel="jmsAuthTempVisible = false"
      width="650px"
    >
      <div v-if="!globalCheck">
        <SecondaryVerification @verification-success="updateGlobalCheck(true)"
                               @verification-failure="updateGlobalCheck(false)"/>
      </div>
      <div v-else>
        <a-form-model
          ref="jmsAuthTempForm"
          :model="temp.content"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="授权用户" name="filterEmail">
            <a-select
              v-model:value="temp.content.filterEmail"
              :options="principalListMini"
              :filter-option="filterOption"
              style="width: 100%"
              placeholder="负责人可输入或选择"
              @popupScroll="handlePopupScroll"
              @search="handleSearch"
              show-search
              allowClear
            ></a-select>
          </a-form-model-item>
          <a-form-model-item label="机器列表" name="hostList">
            <a-select
              v-model:value="temp.content.hostList"
              mode="tags"
              style="width: 100%"
              @change="checkValidate"
              @popupScroll="handleAuthPopupScroll"
              @search="handleIpSearch"
              placeholder="选择服务器IP"
            >
              <a-select-option v-for="i in info" :key="i.ip">
                {{ i.ip + '(' + i.hostname + ')' }}
              </a-select-option>
            </a-select>
            <p style="color: red; font-size: 14px" v-show="showTips">请输入合法的IP</p>
          </a-form-model-item>
          <a-form-model-item name="role" label="权限类型">
            <a-radio-group v-model:value="temp.content.role" button-style="solid">
              <a-radio-button value="common">普通权限</a-radio-button>
              <a-radio-button value="root">sudo权限</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="授权时长">
            <a-select v-model:value="temp.content.time" placeholder="请选择" style="width: 120px">
              <a-select-option value="1">1小时</a-select-option>
              <a-select-option value="2">2小时</a-select-option>
              <a-select-option value="4">4小时</a-select-option>
              <a-select-option value="8">8小时</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </div>
      <template #footer>
        <a-button key="submit" @click="jmsAuthTempVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="loading" :disabled="!globalCheck || !isValidHostList"
                  @click="CreateJmsAuthTemp">确认
        </a-button>
      </template>
    </a-modal>
    <a-modal v-model:visible="logVisible" width="55%" title="授权日志" centered :footer="null"
             :afterClose="handleClose">
      <div ref="logTerm" class="terminal-container"></div>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import {Terminal} from 'xterm'
import 'xterm/css/xterm.css'
import {FitAddon} from "xterm-addon-fit"
import {Ellipsis, STable} from '@/components'
import {CancelJmsAuth, CreateJmsAuthByAdmin, getJmsList, getJmsTaskLog} from '@/api/asset'
import store from '@/store'
import {removeWatermark, setWaterMark} from '@/utils/watermark'
import user from '@/store/modules/user'
import {filterLabelValue} from '@aim/helper'
import {getUserEmailList} from '@/api/permission/user'
import {notification} from 'ant-design-vue'
import SecondaryVerification from '@/components/Verification/verify.vue'
import {getAllAssetsIp, retryJmsAuth} from "@/api/workflow/asset_automation";

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '用户名',
    dataIndex: 'name',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: 'IP列表',
    dataIndex: 'ips',
  },
  {
    title: '权限类型',
    dataIndex: 'role',
  },
  {
    title: '工单审批人/管理员',
    dataIndex: 'creator',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '过期时间',
    dataIndex: 'expireTime',
  },
  {
    title: '授权日志',
    dataIndex: 'taskLog',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Jms',
  components: {
    STable,
    Ellipsis,
    SecondaryVerification,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      globalCheck: false,
      terminal: null,
      fitAddon: null,
      queryParam: {},
      temp: {
        founderEmail: '',
        content: {
          filterEmail: user.state.email,
          hostList: [],
          role: 'root',
          isExpired: 'long-term',
          isHrApprove: 'false',
          validTime: '3600',
          time: '1',
        },
      },
      cancelTemp: {
        founderEmail: '',
        content: {
          filterEmail: user.state.email,
          hostList: [],
          role: 'root',
        },
      },
      advanced: false,
      info: [],
      showTips: false,
      loading: false,
      isValidHostList: false,
      jmsAuthVisible: false,
      logVisible: false,
      logContent: "",
      cancelJmsAuthVisible: false,
      jmsAuthTempVisible: false,
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getJmsList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return {data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0}
            } else {
              return res.Data
            }
          } else {
            return {data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0}
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      principalListMini: [],
      principalList: [],
      cancelRules: antdFormRulesFormat({
        'content.filterEmail': [{required: true, message: '请选择用户', trigger: 'change'}],
        'content.hostList': [{required: true, message: '请选择服务器', trigger: 'change'}],
      }),
      rules: antdFormRulesFormat({
        'content.filterEmail': [{required: true, message: '请选择用户', trigger: 'change'}],
        'content.hostList': [{required: true, message: '请选择服务器', trigger: 'change'}],
      }),
      request: {
        pageNo: 1,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
        isAdmin: true,
      },
    }
  },
  created() {
    this.GetAssetIps()
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getUserBaseInfo()
  },
  unmounted() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    roleFilter(role) {
      switch (role){
        case 'root':
          return 'sudo权限'
        case 'common':
          return '普通权限'
        case 'cancel-root':
          return 'sudo权限(取消)'
        case 'cancel-common':
          return '普通权限(取消)'
      }
    },
    // intpu
    handleIpSearch(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.info = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.info.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    handleAuthPopupScroll(e) {
      const {scrollHeight, scrollTop, clientHeight} = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    updateGlobalCheck(status) {
      this.globalCheck = status
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handleSearch(val) {
      this.valueData = val
      if (!val) {
        this.getUserBaseInfo()
      } else {
        this.principalListMini = []
        this.scrollPage = 1
        this.principalList.forEach(item => {
          if (item.label.indexOf(val) >= 0) {
            this.principalListMini.push(item)
          }
        })
        this.principalListMini = this.principalListMini.slice(0, 50)
      }
    },
    getUserBaseInfo() {
      getUserEmailList().then(res => {
        for (let i = 0, len = res.Data.items.length; i < len; i++) {
          const user = {}
          user.value = res.Data.items[i].key
          user.label = res.Data.items[i].key + '(' + res.Data.items[i].value + ')'
          this.principalList.push(user)
          if (i <= 50) {
            this.principalListMini.push(user)
          }
        }
      })
    },
    handlePopupScroll(e) {
      const {target} = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.principalList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.principalList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.principalListMini = newData
        }
      }
    },
    handleServerSearch(val) {
      this.serverOptionMini = []
      this.serverScrollPage = 1
      this.serverOption.forEach(item => {
        if (item.ip.indexOf(val) >= 0 || item.hostname.indexOf(val) >= 0) {
          this.serverOptionMini.push(item)
        }
      })
      this.serverOptionMini = this.serverOptionMini.slice(0, 50)
    },
    isValidIP(ip) {
      const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
      if (!ipRegex.test(ip)) {
        return false;
      }
      const parts = ip.split('.')
      return parts.every(part => {
        const num = parseInt(part, 10)
        return num >= 0 && num <= 255 && part === num.toString()
      })
    },
    checkValidate(arr) {
      this.showTips = false
      this.isValidHostList = arr.every(part => this.isValidIP(part.trim()))
      // if (arr.length === 0){
      //   this.isValidHostList = false
      // }
      if (!this.isValidHostList) {
        this.showTips = true
      }
    },

    handleServerPopupScroll(e) {
      const {target} = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.serverScrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.serverScrollPage = this.serverScrollPage + 1
          const scrollPage = this.serverScrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.serverOption.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.serverOption.length
          }
          // 判断是否有搜索
          if (this.serverValueData) {
            this.serverOption.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.serverOption.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.serverOptionMini = newData
        }
      }
    },
    filterOption: filterLabelValue,
    // CreateJmsAuthData() {
    //   antdFormValidate(this.$refs.jmsAuthForm, valid => {
    //     if (valid) {
    //       this.showTips = false
    //       this.loading = true
    //       this.temp.content.host_list = this.temp.content.host_list.join(',')
    //       CreateJmsAuthByAdmin(this.temp).then(res => {
    //         this.jmsAuthVisible = false
    //         this.loading = false
    //         this.$refs.table.refresh()
    //         notification.success({
    //           message: '提交成功',
    //         })
    //       })
    //     }
    //   })
    // },
    CancelJmsAuthData() {
      antdFormValidate(this.$refs.cancelJmsAuthForm, valid => {
        if (valid) {
          this.showTips = false
          this.loading = true
          this.cancelTemp.content.hostList = this.cancelTemp.content.hostList.join(',')
          this.cancelTemp.founderEmail = this.cancelTemp.content.filterEmail
          CancelJmsAuth(this.cancelTemp).then(res => {
            this.cancelTemp.content.hostList = this.cancelTemp.content.hostList.split(',')
            this.cancelJmsAuthVisible = false
            this.loading = false
            this.$refs.table.refresh()
            notification.success({
              message: '取消授权成功',
            })
          })
        }
      })
    },
    CreateJmsAuthTempData() {
      antdFormValidate(this.$refs.jmsAuthTempForm, valid => {
        if (valid) {
          this.loading = true
          this.showTips = false
          this.temp.content.hostList = this.temp.content.hostList.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          this.temp.founderEmail = this.temp.content.filterEmail
          CreateJmsAuthByAdmin(this.temp).then(res => {
            this.temp.content.hostList = this.temp.content.hostList.split(',')
            this.jmsAuthTempVisible = false
            this.loading = false
            this.$refs.table.refresh()
            notification.success({
              message: '提交成功',
            })
          })
        }
      })
    },
    CreateJmsAuthTemp() {
      this.temp.content.isExpired = 'temporary'
      this.CreateJmsAuthTempData()
    },
    addJmsAuth() {
      this.jmsAuthVisible = true
    },
    cancelJmsAuth() {
      this.cancelJmsAuthVisible = true
    },
    addJmsAuthTemp() {
      this.globalCheck = false
      this.jmsAuthTempVisible = true
    },
    handleJmsAuthDetail(record) {
      getJmsTaskLog({id: record.id}).then(res => {
        let taskLog = res.Data.taskLog
        if (taskLog === "") {
          taskLog = "任务执行中请稍后查看........"
        }
        this.logVisible = true
        this.$nextTick(() => {
          this.initTerminal()
          this.terminal.clear()
          this.terminal.reset()
          this.terminal.write(taskLog)
          this.fitAddon.fit()
        });
      })
    },
    initTerminal() {
      if (!this.terminal) {
        // 初始化终端
        this.terminal = new Terminal({
          tabStopWidth: 4,
          lineHeight: 1.2,
          fontSize: 14,
          fontFamily: "Consolas"
        })
        this.fitAddon = new FitAddon()
        this.terminal.loadAddon(this.fitAddon)

        // 打开终端
        this.terminal.open(this.$refs.logTerm)

        // 添加窗口 resize 事件监听
        const that = this
        window.addEventListener('resize', function () {
          if (that.logVisible && that.fitAddon) {
            that.fitAddon.fit()
          }
        }, false)
      }
    },
    handleClose() {
      this.logContent = ""
      this.logVisible = false
    },
    handleRetry(record) {
      retryJmsAuth({id: record.id}).then(response => {
        if (response === undefined) {
          notification.error({
            message: '重试失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 400) {
          notification.error({
            message: '重试失败',
          })
        } else {
          notification.success({
            message: '重试成功',
          })
        }
      })
    }
  },
}
</script>
<style scoped>
.terminal-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
}

/* 确保 xterm.js 容器填充整个空间 */
.terminal.xterm {
  height: 100%;
  padding: 8px;
}
</style>
