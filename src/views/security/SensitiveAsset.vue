<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="4" :sm="24">
              <a-form-item label="项目">
                <a-select placeholder="" v-model:value="queryParam.project" :showSearch="true" allowClear>
                  <a-select-option v-for="item1 in projectList" :key="item1" :value="item1">
                    {{ item1 }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24">
              <a-form-item label="服务器IP">
                <a-input v-model:value="queryParam.ip" placeholder="IP" @pressEnter="$refs.table.refresh()" />
              </a-form-item>
            </a-col>
            <a-col :md="16" :sm="24">
              <span>
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button type="primary" style="float: right" icon="plus" @click="handleAdd">新增</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-modal :visible="addVisible" title="新增资产" :closable="false">
          <a-form-model
            :model="addParam"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
            class="myform"
            ref="addForm"
            :rules="rules"
          >
            <a-form-model-item label="项目" name="project">
              <a-select placeholder="" v-model:value="addParam.project" :showSearch="true">
                <a-select-option v-for="item1 in projectList" :key="item1" :value="item1">
                  {{ item1 }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item name="env" label="环境">
              <a-radio-group v-model:value="addParam.env" button-style="solid">
                <a-radio value="online">线上</a-radio>
                <a-radio value="test">测试</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="资产" name="ip">
              <a-input v-model:value="addParam.ip" placeholder="请输入ip" />
            </a-form-model-item>
            <a-form-model-item label="负责人" name="users">
              <a-select
                placeholder="<EMAIL>"
                v-model:value="addParam.users"
                mode="multiple"
                :showSearch="true"
                :allowClear="true"
                @search="searchUserEmailMethod"
              >
                <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                  {{ item1 }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-form-model>
          <template #footer>
            <tx-button key="back" @click="cancelAdd">取消</tx-button>
            <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
          </template>
        </a-modal>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.name"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record: row, text: task_type }">
          <template v-if="column.dataIndex === 'comment'">
            <div class="tooltip">
              <a-tooltip placement="left">
                <template #title>
                  <span>{{ row.comment }}</span>
                </template>
                {{ row.comment }}
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(row)">更新</tx-button>
            <tx-button type="link" size="small" @click="handleDelete(row)">删除</tx-button>
          </template>
        </template>
      </s-table>
      <a-modal :visible="updateVisible" title="更新资产" ref="updateModal" :closable="false">
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
          ref="updateForm"
          class="myform"
        >
          <a-form-model-item label="负责人" name="users">
            <a-select
              placeholder="<EMAIL>"
              v-model:value="updateParam.users"
              mode="multiple"
              :showSearch="true"
              :allowClear="true"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                {{ item1 }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import {
  getSensitiveAssetList,
  createSensitiveAsset,
  updateSensitiveAsset,
  deleteSensitiveAsset,
} from '@/api/security/sensitive_asset'
import { getUserList } from '@/api/permission/user'

import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '项目',
    dataIndex: 'project',
  },
  {
    title: '环境',
    dataIndex: 'envString',
  },
  {
    title: '服务器',
    dataIndex: 'ip',
  },
  {
    title: '负责人',
    dataIndex: 'userstring',
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SensitiveAsset',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      addParam: {},
      updateParam: {
        id: 0,
        password: 0,
      },
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      rules: {
        project: [{ required: true, message: '请选择项目', trigger: 'blur' }],
        env: [{ required: true, message: '请选择环境', trigger: 'change' }],
        ip: [{ required: true, message: '请输入资产ip', trigger: 'blur' }],
        users: [{ required: true, message: '请输入负责人邮箱', trigger: 'blur' }],
      },
      userEmailList: [],
      projectList: ['Cloud', 'Consul', 'Jenkins', 'Gitlab', 'Confluence', 'JumpServer', 'CloudTerminal', 'Soc'],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getSensitiveAssetList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
            for (var i = 0, len = res.Data.data.length; i < len; i++) {
              res.Data.data[i].userstring = res.Data.data[i].users.join(',')
              if (res.Data.data[i].env == 'online') {
                res.Data.data[i].envString = '线上'
              } else {
                res.Data.data[i].envString = '测试'
              }
            }
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  mounted() {},
  methods: {
    handleAdd() {
      this.addParam = {
        env: 'online',
      }
      this.addVisible = true
    },
    cancelAdd() {
      this.addVisible = false
      this.confirmLoading = false
    },
    submitAdd() {
      var that = this
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createSensitiveAsset(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功',
                })
                this.cancelAdd()
                that.$refs.table.refresh(true)
              } else {
                notification.error({
                  message: '创建失败',
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleUpdate(row) {
      this.updateParam = { id: row.id, users: row.users }
      this.updateVisible = true
    },
    cancelUpdate() {
      this.updateVisible = false
      this.confirmLoading = false
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    submitUpdate() {
      var that = this
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateSensitiveAsset(this.updateParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '修改成功',
                })
                that.$refs.table.refresh(true)
                this.cancelUpdate()
              } else {
                notification.error({
                  message: '修改失败',
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete(row) {
      var that = this
      this.$confirm({
        title: `确认删除 ${row.ip} 吗？`,
        onOk() {
          deleteSensitiveAsset({ id: row.id })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功',
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>
<style lang="less" scoped>
.myform {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
