<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form>
          <a-tabs v-model:activeKey="activeKey" @change="tabChange">
            <a-tab-pane key="taskList" tab="任务列表">
              <a-row :gutter="48">
                <a-col :md="6" :sm="24">
                  <a-form-item label="任务名称">
                    <a-input
                      v-model:value="queryParam.taskName"
                      placeholder="xxxx"
                      @pressEnter="$refs.table.refresh()"
                    />
                  </a-form-item>
                </a-col>
                <a-col :md="18" :sm="24">
                  <span>
                    <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                    <tx-button type="primary" style="float: right" icon="plus" @click="handleAdd">新增</tx-button>
                  </span>
                </a-col>
              </a-row>
            </a-tab-pane>
            <a-tab-pane key="logList" tab="执行日志" force-render>
              <a-row :gutter="48">
                <a-col :md="6" :sm="24">
                  <a-form-item label="任务名称">
                    <a-input
                      v-model:value="queryParam.taskName"
                      placeholder="xxxx"
                      @pressEnter="$refs.table.refresh()"
                    />
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="执行时间">
                    <a-range-picker v-model:value="queryParam.times" show-time />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <span>
                    <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                  </span>
                </a-col>
              </a-row>
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </div>
      <div class="table-operator">
        <a-modal :visible="addVisible" title="新增任务" :closable="false">
          <a-form-model
            :model="addParam"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
            class="myform"
            ref="addForm"
            :rules="rules"
          >
            <a-form-model-item label="任务名称" name="taskName">
              <a-input v-model:value="addParam.taskName" placeholder="" />
            </a-form-model-item>
            <a-form-model-item label="Cron名称" name="cronName">
              <a-input v-model:value="addParam.cronName" />
            </a-form-model-item>
            <a-form-model-item label="定时策略" name="tactics">
              <a-input v-model:value="addParam.tactics" />
            </a-form-model-item>

            <a-form-model-item label="状态" name="taskStatus">
              <a-select placeholder="" v-model:value="addParam.taskStatus">
                <a-select-option v-for="item1 in statusList" :key="item1.value" :value="item1.value">
                  {{ item1.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="动态基准值" name="baseValue">
              <a-switch
                v-model:checked="addParam.baseValueType"
                checked-children="开"
                un-checked-children="关"
                style="margin-right: 16px"
              />
              <a-input-number
                :disabled="addParam.baseValueType"
                v-model:value="addParam.baseValue"
                :min="0"
                size="small"
              />
            </a-form-model-item>
            <a-form-model-item label="告警方式" name="notionMethod">
              <a-select
                placeholder="<EMAIL>"
                v-model:value="addParam.notionMethod"
                mode="multiple"
                :showSearch="true"
              >
                <a-select-option v-for="item1 in notionMethodList" :key="item1.value" :value="item1.value">
                  {{ item1.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="负责人" name="notionUser">
              <a-select
                placeholder="<EMAIL>"
                v-model:value="addParam.notionUser"
                mode="multiple"
                :showSearch="true"
                @search="searchUserEmailMethod"
              >
                <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                  {{ item1 }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="备注">
              <a-input v-model:value="addParam.comment" />
            </a-form-model-item>
          </a-form-model>
          <template #footer>
            <tx-button key="back" @click="cancelAdd">取消</tx-button>
            <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
          </template>
        </a-modal>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.name"
        :pagination="pagination"
        :columns="activeKey === 'taskList' ? columnTasks : columnLogs"
        :data="loadData"
      >
        <template #bodyCell="{ column, record: row, text }">
          <template v-if="column.dataIndex === 'taskStatus'">
            <a-switch
              :checked="row.taskStatus == 1"
              checked-children="开启"
              un-checked-children="暂停"
              class="custom-switch"
              :loading="switchLoading"
              @change="checked => changeStatusSwitch(checked, row)"
            />
          </template>
          <template v-if="column.dataIndex === 'methodstring'">
            <div v-for="method in row.notionMethod" :key="method">
              <a-tag v-if="method === 'call'" color="#FF9900">电话</a-tag>
              <a-tag v-if="method === 'qw'" color="#99ccff">合小云</a-tag>
              <a-tag v-if="method === 'email'" color="#0099CC">邮件</a-tag>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(row)">更新</tx-button>
            <tx-button type="link" size="small" @click="handleDelete(row)">删除</tx-button>
          </template>
        </template>
      </s-table>
      <a-modal :visible="updateVisible" title="更新基线任务" ref="updateModal" :closable="false">
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
          ref="updateForm"
          class="myform"
        >
          <a-form-model-item label="任务名称" name="taskName">
            <a-input v-model:value="updateParam.taskName" disabled placeholder="" />
          </a-form-model-item>
          <a-form-model-item label="定时策略" name="tactics">
            <a-input v-model:value="updateParam.tactics" />
          </a-form-model-item>
          <a-form-model-item label="动态基准值" name="baseValue">
            <a-switch
              v-model:checked="updateParam.baseValueType"
              checked-children="开"
              un-checked-children="关"
              style="margin-right: 16px"
            />
            <a-input-number
              :disabled="updateParam.baseValueType"
              v-model:value="updateParam.baseValue"
              :min="0"
              size="small"
            />
          </a-form-model-item>
          <a-form-model-item label="告警方式" name="notionMethod">
            <a-select
              placeholder="<EMAIL>"
              v-model:value="updateParam.notionMethod"
              mode="multiple"
              :showSearch="true"
            >
              <a-select-option v-for="item1 in notionMethodList" :key="item1.value" :value="item1.value">
                {{ item1.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="负责人" name="notionUser">
            <a-select
              placeholder="<EMAIL>"
              v-model:value="updateParam.notionUser"
              mode="multiple"
              :showSearch="true"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                {{ item1 }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="备注">
            <a-input v-model:value="updateParam.comment" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import {
  getJmsBaseTaskList,
  getJmsBaseTaskLog,
  createJmsBaseTask,
  updateJmsBaseTask,
  deleteJmsBaseTask,
} from '@/api/security/jms_base_task'
import { getUserList } from '@/api/permission/user'

import { notification } from 'ant-design-vue'

const columnTasks = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: '50px',
  },
  {
    title: '任务名称',
    dataIndex: 'taskName',
  },
  {
    title: 'Cron名称',
    dataIndex: 'cronName',
  },
  {
    title: '定时策略',
    dataIndex: 'tactics',
    width: '100px',
  },
  {
    title: '执行次数',
    dataIndex: 'runCount',
  },
  {
    title: '告警方式',
    dataIndex: 'methodstring',
  },
  {
    title: '负责人',
    dataIndex: 'userstring',
    width: '200px',
  },
  {
    title: '备注',
    dataIndex: 'comment',
    width: '200px',
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]
const columnLogs = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: '50px',
  },
  {
    title: '任务名称',
    dataIndex: 'taskName',
  },
  {
    title: '执行时间',
    dataIndex: 'createdAt',
  },
  {
    title: 'Cron名称',
    dataIndex: 'cronName',
  },
  {
    title: '基准值',
    dataIndex: 'baseValue',
  },
  {
    title: '结果',
    dataIndex: 'result_state',
  },
  {
    title: '结果数量',
    dataIndex: 'resultConut',
  },
  {
    title: '执行详情',
    dataIndex: 'result_info',
    ellipsis: true,
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'JmsBaselineCheck',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columnTasks = columnTasks
    this.columnLogs = columnLogs
    this.pagination = pagination
    return {
      addParam: {},
      updateParam: {
        id: 0,
        password: 0,
      },
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      activeKey: 'taskList',
      // 高级搜索 展开/关闭
      advanced: false,
      switchLoading: false,
      // 查询参数
      queryParam: { times: [] },
      rules: {
        taskName: [{ required: true, message: '输入任务名称', trigger: 'blur' }],
        tactics: [{ required: true, message: '输入定时任务策略 1 12 3 * *', trigger: 'blur' }],
        notionMethod: [{ required: true, message: '选择通知方式', trigger: 'blur' }],
        notionUser: [{ required: true, message: '选择通知人邮箱', trigger: 'blur' }],
        cronName: [{ required: true, message: '输入cron名称', trigger: 'blur' }],
        taskStatus: [{ required: true, message: '选择任务状态', trigger: 'blur' }],
      },
      userEmailList: [],
      statusList: [
        { value: 1, label: '开启' },
        { value: 2, label: '暂停' },
      ],
      notionMethodList: [
        {
          value: 'call',
          label: '电话',
        },
        {
          value: 'qw',
          label: '合小云',
        },
        {
          value: 'email',
          label: '邮件',
        },
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        if (this.activeKey === 'logList') {
          if ( (this.queryParam.times!==null) &&(this.queryParam.times.length === 2)) {
            this.queryParam.startTime = this.queryParam.times[0]
            this.queryParam.endTime = this.queryParam.times[1]
            // 格式化时间
            this.queryParam.startTime = this.queryParam.startTime.format('YYYY-MM-DD HH:mm:ss')
            this.queryParam.endTime = this.queryParam.endTime.format('YYYY-MM-DD HH:mm:ss')
          }else {
            this.queryParam.startTime = ''
            this.queryParam.endTime = ''
          }
          const requestParameters = Object.assign({}, parameter, this.queryParam)

          return getJmsBaseTaskLog(requestParameters).then(res => {
            if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
              return res.Data
            } else {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            }
          })
        } else {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return getJmsBaseTaskList(requestParameters).then(res => {
            if (res.Data.hasOwnProperty('data') && res.Data.data !== null) {
              for (var i = 0, len = res.Data.data.length; i < len; i++) {
                res.Data.data[i].userstring = res.Data.data[i].notionUser.join(',')
              }
              return res.Data
            } else {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            }
          })
        }
      },
    }
  },
  mounted() {},
  methods: {
    tabChange(key) {
      this.$refs.table.refresh(true)
    },
    changeStatusSwitch(checked, row) {
      this.switchLoading = true
      if (checked) {
        row.taskStatus = 1
      } else {
        row.taskStatus = 2
      }
      updateJmsBaseTask(row)
        .then(res => {
          if (res !== undefined && res.Code === 200) {
            notification.success({
              message: '更新成功',
            })
            this.switchLoading = false
            this.$refs.table.refresh(true)
          }
        })
        .catch(() => {
          this.switchLoading = false
          this.$refs.table.refresh(true)
        })
    },
    handleAdd() {
      this.addParam = {
        env: 'online',
      }
      this.addVisible = true
    },
    cancelAdd() {
      this.addVisible = false
      this.confirmLoading = false
    },
    submitAdd() {
      var that = this
      antdFormValidate(this.$refs.addForm, valid => {
        if (valid) {
          this.confirmLoading = true
          createJmsBaseTask(this.addParam)
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '创建成功',
                })
                this.cancelAdd()
                that.$refs.table.refresh(true)
              } else {
                notification.error({
                  message: '创建失败',
                })
                this.confirmLoading = false
              }
            })
            .catch(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleUpdate(row) {
      this.updateParam = {
        id: row.id,
        notionUser: row.notionUser,
        taskName: row.taskName,
        notionMethod: row.notionMethod,
        tactics: row.tactics,
        comment: row.comment,
        baseValue: row.baseValue,
        baseValueType: row.baseValueType,
        taskStatus: row.taskStatus,
      }
      this.updateVisible = true
    },
    cancelUpdate() {
      this.updateVisible = false
      this.confirmLoading = false
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    submitUpdate() {
      var that = this
      antdFormValidate(this.$refs.updateForm, valid => {
        if (valid) {
          this.confirmLoading = true
          updateJmsBaseTask(this.updateParam).then(res => {
            if (res !== undefined && res.Code === 200) {
              notification.success({
                message: '修改成功',
              })
              that.$refs.table.refresh(true)
              this.cancelUpdate()
            } else {
              notification.error({
                message: '修改失败',
              })
              this.confirmLoading = false
            }
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete(row) {
      var that = this
      this.$confirm({
        title: `确认删除 ${row.taskName} 吗？`,
        onOk() {
          deleteJmsBaseTask({ id: row.id })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功',
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
  },
}
</script>
<style lang="less" scoped>
.myform {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
