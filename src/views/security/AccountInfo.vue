<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="精确查询">
                <a-input v-model:value="queryParam.username" placeholder="邮箱" />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="download" @click="handleDownload">导出</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="username"
        :columns="columns"
        :data="loadData"
        showPagination="auto"
      >
        <template #bodyCell="{column, text}">
        <template v-if="column.dataIndex == 'scope'">
          <a-badge :status="scopeTypeFilter(text)" :text="scopeFilter(text)" />
        </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions >
            <a-descriptions-item label="邮箱">{{ record.username }}</a-descriptions-item>
            <a-descriptions-item label="亚马逊账号">{{ record.awsUser }}</a-descriptions-item>
            <a-descriptions-item label="阿里云账号">{{ record.aliyunUser }}</a-descriptions-item>
            <a-descriptions-item label="腾讯云账号">{{ record.tencentUser }}</a-descriptions-item>
            <a-descriptions-item label="微软账号">{{ record.azureUser }}</a-descriptions-item>
            <a-descriptions-item label="状态类型">
              <a-badge :status="statusFilter(record.status)" :text="statusFilter(record.status)" />
            </a-descriptions-item>
          </a-descriptions>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
  import { STable, Ellipsis } from '@/components'
  import { listAccountInfo } from '@/api/security/account'
  import { loadXLSX } from '@/utils/vendorLoader'
  const columns = [
    {
      title: '邮箱',
      width: '300px',
      dataIndex: 'username'
    },
    {
      title: '亚马逊账号',
      dataIndex: 'awsUser'
    },
    {
      title: '阿里云账号',
      dataIndex: 'aliyunUser'
    },
    {
      title: '腾讯云账号',
      dataIndex: 'tencentUser'
    },
    {
      title: '微软账号',
      dataIndex: 'azureUser'
    },
    {
      title: '账号状态',
      width: '200px',
      dataIndex: 'status'
    }
  ]
  const scopeMap = {
    0: {
      status: 'orange',
      text: '未知'
    },
    1: {
      status: 'success',
      text: '外网域名'
    },
    2: {
      status: 'processing',
      text: '内网域名'
    },
    3: {
      status: 'processing',
      text: '内网域名'
    },
    4: {
      status: 'processing',
      text: '内网域名'
    }
  }
  const statusMap = {
    0: {
      status: 'processing',
      text: '账号异常'
    },
    1: {
      status: 'success',
      text: '正常'
    }
  }
  export default {
    name: 'DomainList',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.downloadtableList = null
      return {
        temp: {},
        valueData: '',
        treePageSize: 50,
        dnsSourceList: [],
        domainInfo: [],
        downloadtableList: [],
        addQueryParam: { },
        visible: false,
        confirmLoading: false,
        mdl: null,
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: { },
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          this.downloadqueryParam = JSON.parse(JSON.stringify(requestParameters))
          delete this.downloadqueryParam.pageNo
          delete this.downloadqueryParam.pageSize
          return listAccountInfo(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      loadXLSX()
    },
    methods: {
      handleDownload () {
        const downloadColumns = [
          {
            title: '邮箱',
            dataIndex: 'username'
          },
          {
            title: '亚马逊账号',
            dataIndex: 'awsUser'
          },
          {
            title: '阿里云账号',
            dataIndex: 'aliyunUser'
          },
          {
            title: '腾讯云账号',
            dataIndex: 'tencentUser'
          },
          {
            title: '微软账号',
            dataIndex: 'azureUser'
          }
        ]
        listAccountInfo(this.downloadqueryParam).then(async (res) => {
          if (res.Data.hasOwnProperty('data')) {
            const XLSX = await loadXLSX()
            const tableData = this.transData(downloadColumns, res.Data.data)
            const ws = XLSX.utils.aoa_to_sheet(tableData)
            const wb = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(wb, ws, 'domain')
            XLSX.writeFile(wb, 'Domain.xlsx')
          } else {
            this.downloadtableList = []
          }
        })
      },
      scopeFilter (type) {
        return scopeMap[type]?.text || type
      },
      scopeTypeFilter (type) {
        return scopeMap[type]?.status || type
      },
      statusFilter (type) {
        return statusMap[type]?.text || type
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      transData (columns, tableList) {
        const obj = columns.reduce((acc, cur) => {
          if (!acc.titles && !acc.keys) {
            acc.titles = []
            acc.keys = []
          }
          acc.titles.push(cur.title)
          acc.keys.push(cur.dataIndex)
          return acc
        }, {})
        const tableBody = tableList.map((item) => {
          return obj.keys.map((key) => item[key])
        })
        return [obj.titles, ...tableBody]
      }
    }
  }
</script>

<style lang="less" scoped>
  .my-ant-form-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 14px;
    vertical-align: top;
  }
</style>
