<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="模糊查询">
                <a-input
                  v-model:value="queryParam.searchName"
                  placeholder="用户名/策略名称/策略描述"
                  @pressEnter="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="状态(90天内)">
                <a-select v-model:value="queryParam.status" allowClear placeholder="请选择">
                  <a-select-option value="1">未检测</a-select-option>
                  <a-select-option value="2">权限匹配</a-select-option>
                  <a-select-option value="3">权限不匹配</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="权限策略描述" :span="3">{{ record.description }}</a-descriptions-item>
            <a-descriptions-item label="权限策略类型">{{ record.policyType }}</a-descriptions-item>
            <a-descriptions-item label="服务代码">{{ record.serviceCode }}</a-descriptions-item>
            <a-descriptions-item label="读写权限">{{ record.eventRW }}</a-descriptions-item>
            <a-descriptions-item label="事件数量">{{ record.eventNum }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{column, record}">
        <template v-if="column.dataIndex == 'status'">
          <a-tag :color="statusColorFilter(record.status)">{{ statusFilter(record.status) }}</a-tag>
        </template>
          <template v-else-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
          </template>
        </template>
      </s-table>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">

          <a-form-model-item label="权限策略名称" name="policyName">
            <a-input v-model:value="form.policyName" disabled=""/>
          </a-form-model-item>
          <a-form-model-item label="服务名称" name="serviceName">
            <a-input v-model:value="form.serviceName"/>
          </a-form-model-item>
          <a-form-model-item label="服务代码" name="serviceCode">
            <a-input v-model:value="form.serviceCode"/>
          </a-form-model-item>
          <a-form-model-item label="读写权限" name="eventRW">
            <a-select v-model:value="form.eventRW" allowClear placeholder="请选择">
              <a-select-option value="read">read</a-select-option>
              <a-select-option value="write">write</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
  import moment from 'moment'
  import { STable, Ellipsis } from '@/components'
  import { getRamList, getRamInfo, updateRam } from '@/api/security/ram'
  import store from '@/store'
  import { removeWatermark, setWaterMark } from '@/utils/watermark'

  const statusMap = {
    '1': {
      color: 'orange',
      text: '未检测'
    },
    '2': {
      color: 'green',
      text: '权限匹配'
    },
    '3': {
      color: 'red',
      text: '权限不匹配'
    }
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true
    },
    {
      title: '用户名',
      dataIndex: 'username',
      sorter: true
    },
    {
      title: '名称',
      dataIndex: 'name',
      sorter: true
    },
    {
      title: '最后登陆时间',
      dataIndex: 'lastLoginDate',
      sorter: true
    },
    {
      title: '权限策略名称',
      dataIndex: 'policyName',
      sorter: true
    },
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      sorter: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      sorter: true,
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' },
      width: '70px',
      align: 'center'
    }
  ]

  const pagination = {
    showTotal: total => `共 ${total} 条`
  }

  export default {
    name: 'SecurityRam',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      this.pagination = pagination
      return {
        labelCol: { span: 8 },
        wrapperCol: { span: 14 },
        queryParam: {},
        advanced: false,
        form: {
          policyName: undefined,
          serviceName: undefined,
          serviceCode: undefined,
          eventRW: undefined
        },
        updateVisible: false,
        confirmLoading: false,
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return getRamList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
                return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
              } else {
                return res.Data
              }
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: [],
        rules: {
          'policyName': [{ required: true, message: '请填写权限策略名称', trigger: 'change' }],
          'serviceName': [{ required: true, message: '请填写服务名称', trigger: 'change' }],
          'serviceCode': [{ required: true, message: '请填写服务代码', trigger: 'change' }],
          'eventRW': [{ required: true, message: '请选择读写权限', trigger: 'change' }]
        }
      }
    },
    mounted () {
      const email = store.getters.email
      const name = store.getters.name
      if (email) {
        setWaterMark(email, name)
      }
    },
    destroyed () {
      removeWatermark()
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    methods: {
      handleUpdate (record) {
        getRamInfo(record.id).then((res) => {
          this.form = res.Data
        })
        this.updateVisible = true
      },
      handleUpdateOk (e) {
        this.confirmLoading = true
        antdFormValidate(this.$refs.ruleForm, valid => {
          if (valid) {
            updateRam(this.form).then(res => {
              if (res === undefined) {
                this.confirmLoading = false
                this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
              } else {
                this.updateVisible = false
                this.confirmLoading = false
                this.$refs.table.refresh()
                this.$message.success('更新成功')
              }
            })
          } else {
            this.$message.error('更新失败')
            this.confirmLoading = false
          }
        })
      },
      handleUpdateCancel (e) {
        this.updateVisible = false
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      statusFilter (type) {
        return statusMap[type]?.text || type
      },
      statusColorFilter (type) {
        return statusMap[type]?.color || type
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      resetSearchForm () {
        this.queryParam = {
          date: moment(new Date())
        }
      }
    }
  }
</script>
