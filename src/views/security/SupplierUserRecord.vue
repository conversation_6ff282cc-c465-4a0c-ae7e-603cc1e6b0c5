<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="云厂商">
                <a-select v-model:value="queryParam.supplier" allowClear showSearch>
                  <a-select-option v-for="item in supplierList" :key="item" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="用户名称">
                <a-input
                  v-model:value="queryParam.userName"
                  @pressEnter="$refs.table.refresh()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <div v-if="record.authPolicies.length > 0">
            <a>历史授权策略记录</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-table
                  :columns="columnsPolicesChildren"
                  :data-source="record.authPolicies"
                  :pagination="pagination"
                  :rowKey="record => record.PolicyName"
                  class="mytable"
                ></a-table>
              </a-col>
            </a-row>
          </div>
          <div v-else>
          <a-descriptions>
            <a-descriptions-item label="记录内容" :span="3">{{ record.content }}</a-descriptions-item>
          </a-descriptions>

        </div>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { Ellipsis, STable } from '@/components'
import { GetUserRecord } from '@/api/security/account'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const columns = [
  {
    title: '记录时间',
    dataIndex: 'dateCreated'
  },
  {
    title: '云厂商',
    dataIndex: 'supplier'
  },
  {
    title: '用户名称',
    dataIndex: 'userName'
  },
  {
    title: '记录类型',
    dataIndex: 'infoType'
  }
]
const columnsPolicesChildren = [
  {
    title: '策略Id',
    dataIndex: 'PolicyId',
  },
  {
    title: '策略名称',
    dataIndex: 'PolicyName',
  },
  {
    title: '策略类型',
    dataIndex: 'PolicyType',
  },
  {
    title: '策略描述',
    dataIndex: 'PolicyDescription',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
  defaultPageSize: 10,
  hideOnSinglePage: true,
}

export default {
  name: 'SecurityRam',
  components: {
    STable,
    Ellipsis
  },
  data() {
    this.columns = columns
    this.columnsPolicesChildren = columnsPolicesChildren
    this.pagination = pagination
    return {
      labelCol: { span: 8 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      form: {
        policyName: undefined,
        serviceName: undefined,
        serviceCode: undefined,
        eventRW: undefined
      },
      updateVisible: false,
      confirmLoading: false,
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return GetUserRecord(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              for (let i = 0; i < res.Data.data.length; i++) {
              if (res.Data.data[i].infoType == '权限变更') {
                console.log(res.Data.data[i].authPolicies)
                res.Data.data[i].authPolicies = JSON.parse((JSON.parse(res.Data.data[i].content)).historyAuth)
                console.log(res.Data.data[i].authPolicies)
              } else {
                res.Data.data[i].authPolicies = []
              }
            }
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      supplierList: [
        'Aliyun(启信宝)',
        'Aliyun(合合)',
        '腾讯云(合合)',
        '腾讯云(CS)',
        'AWS(海外)',
        'AWS(合合)',
        'AWS(CS)',
      ],
      rules: {
        policyName: [{ required: true, message: '请填写权限策略名称', trigger: 'change' }],
        serviceName: [{ required: true, message: '请填写服务名称', trigger: 'change' }],
        serviceCode: [{ required: true, message: '请填写服务代码', trigger: 'change' }],
        eventRW: [{ required: true, message: '请选择读写权限', trigger: 'change' }]
      }
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
  },
  computed: {
  },
  methods: {
  }
}
</script>
