<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ip" placeholder="模糊查询" @pressEnter="handleSearch"/>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'left', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="handleSearch">查询</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operations" v-if="hasAdminRole">
        <tx-button type="primary" class="columnSetting" :loading="confirmLoadingExport"
                   @click="handleExport">
          导出
        </tx-button>
        <tx-button class="columnSetting" @click="showExportHistory" style="margin-left: 3px">
          <a-icon type="unordered-list-outlined"/>
        </tx-button>
        <a-button style="margin-left: 15px" type="primary" @click="handleUpload" v-if="isUploadUser">批量上传
        </a-button>
        <a-modal
          v-model:visible="exportHistoryVisible"
          center
          :footer="null"
          width="50%"
        >
          <s-table
            ref="tableExportHistory"
            :rowKey="(record) => record.id"
            :columns="exportHistoryColumns"
            :data="loadExportHistoryData"
          >
            <template #bodyCell="{column, index, record, text}">
              <template v-if="column.dataIndex === 'resultLink'">
                <div v-if="record.resultLink!==''">
                  <a :href="record.resultLink.replace('http:','https:')">下载</a>
                </div>
                <div v-else>
                  -
                </div>
              </template>
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="exportStatusColorFilter(record.status)">{{ exportStatusFilter(record.status) }}</a-tag>
              </template>
            </template>
          </s-table>
        </a-modal>
        <a-modal :visible="uploadVisible" width="30%" title="安全组批量上传" :closable="false">
          <a-form-model
            :model="uploadParam"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
          >
            <a-form-model-item label="文件" name="file">
              <a-upload
                :file-list="fileList"
                @remove="handleRemove"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls">
                <a-button class="ml-2" :loading="confirmLoading">
                  <a-icon type="upload"/>
                  选择上传文件
                </a-button>
              </a-upload>
            </a-form-model-item>
          </a-form-model>
          <template #footer>
            <a-button key="back" @click="cancelUpload">关闭</a-button>
          </template>
        </a-modal>
        <a-modal
          title="更新"
          :visible="updateVisible"
          :confirm-loading="confirmLoading"
          width="70%"
          @ok="handleUpdateOk"
          @cancel="handleUpdateCancel"
        >
          <a-form-model ref="ruleForm" :model="form">
            <a-form-model-item label="服务器IP">
              <a-input v-model:value="form.ip" disabled></a-input>
            </a-form-model-item>
            <a-form-model-item label="通信场景">
              <a-input v-model:value="form.scene" disabled></a-input>
            </a-form-model-item>
            <a-space
              v-for="(r, ruleIndex) in form.rules"
              :key="ruleIndex"
              style="display: flex; margin-bottom: 8px"
              align="baseline"
            >
              <a-form-model-item
                :name="['rules',ruleIndex, 'direction']"
                :rules="[{ required: true, message: '请选择方向' }]"
              >
                <a-select
                  v-model:value="r.direction"
                  style="width: 85px"
                  placeholder="方向"
                >
                  <a-select-option value="in">入方向</a-select-option>
                  <a-select-option value="out">出方向</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex, 'authPolicy']"
                :rules="[{ required: true, message: '请选择授权策略' }]"
              >
                <a-select
                  v-model:value="r.authPolicy"
                  style="width: 80px"
                  placeholder="授权策略"
                >
                  <a-select-option value="allow">允许</a-select-option>
                  <a-select-option value="reject">拒绝</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex,  'priority']"
                :rules="[
            { required: true, message: '请输入优先级' },
            { type: 'number', min: 1, max: 100, message: '优先级范围1-100' }
          ]"
              >
                <a-input-number
                  v-model:value="r.priority"
                  placeholder="优先级"
                  :min="1"
                  :max="100"
                />
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex,  'protocolType']"
                :rules="[{ required: true, message: '请选择协议类型' }]"
              >
                <a-select
                  v-model:value="r.protocolType"
                  style="width: 80px"
                  placeholder="协议类型"
                >
                  <a-select-option value="tcp">TCP</a-select-option>
                  <a-select-option value="udp">UDP</a-select-option>
                  <a-select-option value="icmp">ICMP</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex,  'portRange']"
                :rules="[
            { required: true, message: '请输入端口范围' },
            { validator: validatePortRange }
          ]"
              >
                <a-input
                  v-model:value="r.portRange"
                  style="width: 150px"
                  placeholder="端口范围，如80/80"
                />
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex,'authorizedObject']"
                :rules="[
            { required: true, message: '请输入授权对象' },
            { validator: validateAuthorizedObject }
          ]"
              >
                <a-input
                  v-model:value="r.authorizedObject"
                  style="width: 180px"
                  placeholder="授权对象，如10.0.0.1/32"
                />
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex,'email']"
                :rules="[{ required: true, message: '请输入负责人' }]"
              >
                <a-input
                  v-model:value="r.email"
                  style="width: 150px"
                  placeholder="负责人"
                />
              </a-form-model-item>
              <a-form-model-item
                :name="['rules',ruleIndex,'describe']"
                :rules="[{ required: true, message: '请输入描述' }]"
              >
                <a-input
                  v-model:value="r.describe"
                  style="width: 150px"
                  placeholder="描述"
                />
              </a-form-model-item>
              <a-form-model-item :name="['rules',ruleIndex,'isExpired']">
                <a-radio-group v-model:value="r.isExpired" button-style="solid" size="small">
                  <a-radio-button value="long">长期</a-radio-button>
                  <a-radio-button value="short">临时</a-radio-button>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item v-if="r.isExpired === 'short'" :name="['rules',ruleIndex, 'expiration']">
                <a-date-picker style="width: 120px" v-model:value="r.expiration" valueFormat="YYYY-MM-DD"
                               placeholder="截止日期"/>
              </a-form-model-item>
              <MinusCircleOutlined @click="removeRule(ruleIndex)"/>
            </a-space>
            <a-form-item>
              <a-button type="dashed" style="width: 100%" @click="addRule()">
                <PlusOutlined/>
                添加规则
              </a-button>
            </a-form-item>
          </a-form-model>
        </a-modal>
      </div>
      <a-table
        ref="table"
        :rowKey="record => record.id"
        :columns="columns"
        :data-source="securityGroupData"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #expandedRowRender="{ record }">
          <div>
            <a>安全组规则</a>
            <a-row :gutter="48">
              <a-col :md="1" :sm="24"></a-col>
              <a-col :md="21" :sm="24">
                <a-tabs default-active-key="ingress">
                  <a-tab-pane key="inbound" tab="入站">
                    <a-table
                      :columns="columnsRules"
                      :data-source="record.ingress || []"
                      :pagination="false"
                      :rowKey="item => item.id"
                      class="kvmAssetTable"
                      style="width: 100%"
                    >
                    </a-table>
                  </a-tab-pane>
                  <a-tab-pane key="egress" tab="出站">
                    <a-table
                      :columns="columnsRules"
                      :data-source="record.egress || []"
                      :pagination="false"
                      :rowKey="item => item.id"
                      class="kvmAssetTable"
                      style="width: 100%"
                    >
                    </a-table>
                  </a-tab-pane>
                </a-tabs>
              </a-col>
            </a-row>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <a @click="handleUpdateUser(record)">更新</a>
            <span v-if="isUploadUser">
              <a-divider type="vertical"/>
              <a @click="handleUpdate(record)">更新(online)</a>
            </span>
          </template>
        </template>
      </a-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import {filterLabelValue} from '@aim/helper'
import moment from 'moment'
import {removeWatermark, setWaterMark} from '@/utils/watermark'
import store from '@/store'
import {
  listSecurityGroupRecode,
  batchCreateSecurityGroupRecode,
  exportSecurityGroupRecode,
  getSecurityGroupRecode,
  createSecurityGroupRecode
} from '@/api/security/baseline'
import NocCodeDiff from '@/suite/CodeDiff/index.vue'
import {MinusCircleOutlined, PlusOutlined} from '@ant-design/icons-vue'
import {getUserList} from '@/api/permission/user'
import {uploadFile} from '@/api/cost/contract'
import {notification} from 'ant-design-vue'
import {getFileTaskList} from '@/api/cost/month'
import {STable} from '@/components'
import {InputYmdHis} from '@tx/ui'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id'
  },
  {
    title: 'IP',
    dataIndex: 'ip'
  },
  {
    title: '通信场景',
    dataIndex: 'scene'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt'
  },
  {
    title: '操作',
    dataIndex: 'action'
  }
]

const columnsRules = [
  {
    title: '授权策略',
    dataIndex: 'authPolicy'
  },
  {
    title: '协议',
    dataIndex: 'protocolType'
  },
  {
    title: '端口',
    dataIndex: 'portRange'
  },
  {
    title: '优先级',
    dataIndex: 'priority'
  },
  {
    title: '授权对象',
    dataIndex: 'authorizedObject'
  },
  {
    title: '有效期限',
    dataIndex: 'isExpired'
  },
  {
    title: '过期时间',
    dataIndex: 'expiration'
  },
  {
    title: '描述',
    dataIndex: 'describe'
  },
  {
    title: '负责人',
    dataIndex: 'email'
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt'
  }
]

const exportHistoryColumns = [
  {
    title: '导出时间',
    dataIndex: 'createdAt'
  },
  {
    title: '用户',
    dataIndex: 'creator'
  },
  {
    title: '导出状态',
    dataIndex: 'status'
  },
  {
    title: '导出结果',
    dataIndex: 'resultLink'
  },
  {
    title: '备注',
    dataIndex: 'comment'
  }
]

const exportStatusMap = {
  0: {
    color: 'orange',
    text: '未知'
  },
  1: {
    color: 'green',
    text: '成功'
  },
  2: {
    color: 'red',
    text: '失败'
  },
  3: {
    color: 'blue',
    text: '运行中'
  }
}

const pagination = {
  showTotal: total => `共 ${total} 条`
}
export default {
  name: 'SecurityGroupRecode',
  components: {InputYmdHis, MinusCircleOutlined, PlusOutlined, NocCodeDiff, STable},
  data() {
    this.columns = columns
    this.columnsRules = columnsRules
    this.exportHistoryColumns = exportHistoryColumns
    this.pagination = pagination
    this.downloadtableList = null
    return {
      localUser: store.getters.email,
      securityGroupData: [],
      confirmLoading: false,
      advanced: false,
      queryParam: {pageNo: 1, pageSize: 10},
      selectedRowKeys: [],
      selectedRows: [],
      hasAdminRole: false,
      uploadVisible: false,
      isUploadUser: false,
      uploadParam: {},
      fileList: [],
      confirmLoadingExport: false,
      exportHistoryVisible: false,
      updateVisible: false,
      form: {
        ip: '',
        rule: []
      },
      loadExportHistoryData: (parameter) => {
        const requestParameters = Object.assign({}, parameter)
        requestParameters.creator = this.creator
        requestParameters.taskType = 17
        return getFileTaskList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return {'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0}
            } else {
              return res.Data
            }
          } else {
            return {'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0}
          }
        })
      }
    }
  },
  created() {
    this.loadSecurityGroupData({})
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    const adminList = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    if (adminList.includes(email)) {
      this.isUploadUser = true
    }
    this.getUserRoles(this.localUser.split('@')[0])
  },
  unmounted() {
    removeWatermark()
  },
  methods: {
    getUserRoles(userEmail) {
      getUserList({searchText: userEmail}).then(response => {
        this.userRoles = response.Data.data[0].roles
        if (this.userRoles.includes('safety_admin') || this.userRoles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    handleSearch() {
      this.queryParam.pageNo = 1
      this.loadSecurityGroupData(this.queryParam)
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = {...this.pagination}
      pager.current = pagination.current
      this.pagination = pager
      this.queryParam.pageSize = pagination.pageSize
      this.queryParam.pageNo = pagination.current
      this.queryParam.sortField = sorter.field
      this.queryParam.sortOrder = sorter.order
      this.loadSecurityGroupData(this.queryParam)
    },
    // 加载数据
    loadSecurityGroupData(queryParam) {
      if (Object.keys(queryParam).length === 0) {
        queryParam = this.queryParam
      }
      listSecurityGroupRecode(queryParam).then(res => {
        let pagination = {...this.pagination}
        if (res.Data.hasOwnProperty('data')) {
          if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
            this.securityGroupData = []
            pagination.total = 0
            pagination.pageNo = 1
            pagination.pageSize = 10
            this.pagination = pagination
            this.loading = false
          } else {
            this.securityGroupData = res.Data.data.map(group => {
              // 将rules根据type字段分类
              const ingress = group.rules.filter(rule => rule.direction === 'in')
              const egress = group.rules.filter(rule => rule.direction === 'out')
              return {
                ...group,
                ingress,
                egress
              }
            })
            if (res.Data.totalCount <= 10) {
              pagination = false
              this.pagination = pagination
              this.loading = false
            } else {
              pagination.total = res.Data.totalCount
              this.pagination = pagination
              this.loading = false
              if (this.pagination.showTotal === undefined) {
                this.$set(this.pagination, 'showTotal', total => `共 ${total} 条`)
              }
            }
          }
        } else {
          this.securityGroupData = []
          pagination.total = 0
          pagination.pageNo = 1
          pagination.pageSize = 10
          this.pagination = pagination
          this.loading = false
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date())
      }
    },
    filterOption: filterLabelValue,
    handleUpdateUser(record) {
      let routeUrl = this.$router.resolve({
        path: '/workflow/security-group-release',
        query: {securityGroupId: record.id}
      })
      window.open(routeUrl.href, '_blank')
    },
    handleUpdate(record) {
      getSecurityGroupRecode({id: record.id}).then(res => {
        this.form = res.Data
        this.updateVisible = true
      })
    },
    handleRemove() {
      this.fileList = []
    },
    resetUpload() {
      this.fileList = []
      this.confirmLoading = false
    },
    beforeUpload(file) {
      const arr = file.name.split('.')
      if ((arr[arr.length - 1] !== 'xlsx') && (arr[arr.length - 1] !== 'xls')) {
        this.$message.error('请上传Excel文件')
        return false
      }
      if (this.fileList.length > 0) {
        this.$message.error('只能上传一个文件')
        return false
      }
      this.fileList = [...this.fileList, file]
      var formdata = new FormData()
      formdata.append('file', file)
      formdata.append('name', file.name)
      this.confirmLoading = true
      uploadFile(formdata).then(res => {
        if (res.errcode === 2) {
          var sendBody = {
            url: res.msg,
            fileName: res.fileName
          }
          batchCreateSecurityGroupRecode(sendBody).then(res => {
            if (res !== undefined && res.Code === 200) {
              notification.success({
                message: '上传成功'
              })
              this.resetUpload()
              this.uploadVisible = false
              this.loadSecurityGroupData(this.queryParam)
            } else {
              notification.error({
                message: '上传失败'
              })
              this.resetUpload()
            }
          }).catch(() => {
            this.resetUpload()
          })
        } else {
          this.resetUpload()
        }
      }).catch(() => {
        this.resetUpload()
      })

      return false
    },
    handleUpload() {
      this.uploadVisible = true
    },
    cancelUpload() {
      this.uploadVisible = false
      this.confirmLoading = false
    },
    handleExport() {
      this.confirmLoadingExport = true
      const requestParameters = Object.assign({}, this.queryParam)
      requestParameters.creator = this.creator
      exportSecurityGroupRecode(requestParameters).then((res) => {
        if (res !== undefined && res.Code === 200) {
          notification.success({
            message: '导出成功，点击右侧按钮，查看导出记录和结果'
          })
          this.confirmLoadingExport = false
        } else {
          notification.error({
            message: '导出失败'
          })
          this.confirmLoadingExport = false
        }
      }).catch(() => {
        this.confirmLoadingExport = false
      })
    },
    showExportHistory() {
      this.exportHistoryVisible = true
      this.$nextTick(() => {
        if (this.$refs.tableExportHistory !== undefined) {
          this.$refs.tableExportHistory.refresh(true)
        }
      })
    },
    exportStatusFilter(type) {
      return exportStatusMap[type]?.text || type
    },
    exportStatusColorFilter(type) {
      return exportStatusMap[type]?.color || type
    },
    handleUpdateOk(e) {
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          this.confirmLoading = true
          this.form.action = '更新'
          let requestParameters = {data: [this.form]}
          createSecurityGroupRecode(requestParameters).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.loadSecurityGroupData(this.queryParam)
              this.$message.success('更新成功')
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    removeRule(ruleIndex) {
      const rules = this.form.rules
      if (rules.length > 1) {
        rules.splice(ruleIndex, 1)
      } else {
        this.$message.warning('至少保留一个规则')
      }
    },
    addRule() {
      this.form.rules.push({
        direction: 'in',
        authPolicy: undefined,
        priority: 10,
        protocolType: undefined,
        portRange: undefined,
        authorizedObject: undefined,
        describe: undefined
      })
    },
    // 端口范围校验
    validatePortRange(rule, value, callback) {
      // 返回 Promise
      return new Promise((resolve, reject) => {
        if (!value) {
          reject(new Error('端口范围不能为空'))
          return
        }

        const portRangeRegex = /^(\d+)\/(\d+)$/
        const match = value.match(portRangeRegex)
        if (!match) {
          reject(new Error('端口范围必须是 a/b 格式'))
          return
        }
        const startPort = parseInt(match[1])
        const endPort = parseInt(match[2])
        if (startPort > endPort) {
          reject(new Error('起始端口必须小于等于结束端口'))
          return
        }
        if (startPort < 0 || startPort > 65535 || endPort < 0 || endPort > 65535) {
          reject(new Error('端口范围必须在 0-65535 之间'))
          return
        }
        resolve()
      })
    },

    // 授权对象校验
    validateAuthorizedObject(rule, value, callback) {
      // 返回 Promise
      return new Promise((resolve, reject) => {
        if (!value) {
          reject(new Error('授权对象不能为空'))
          return
        }
        const objectRegex = /^(\d+\.\d+\.\d+\.\d+)\/(\d+)$/
        const match = value.match(objectRegex)
        if (!match) {
          reject(new Error('授权对象必须是 IP/子网掩码 格式'))
          return
        }
        const subnetMask = parseInt(match[2])
        if (subnetMask < 0 || subnetMask > 32) {
          reject(new Error('子网掩码范围必须在 0-32 之间'))
          return
        }
        resolve()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.kvmAssetTable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 10px;
    position: relative;
  }

  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    text-align: center !important;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}

.table-operations {
  position: relative;
  height: 40px;
}

.columnSetting {
  position: relative;
  right: 0;
}
</style>
