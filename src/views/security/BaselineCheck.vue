<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">申请</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :columns="columns"
        :data="loadData"
        showPagination="auto"
      >
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
  import moment from 'moment'
  import { STable, Ellipsis } from '@/components'
  import { getBaselineList } from '@/api/security/baseline'

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      sorter: true
    },
    {
      title: '目标机器',
      dataIndex: 'hosts',
      sorter: true
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      sorter: true
    },
    {
      title: '任务模块',
      dataIndex: 'module',
      sorter: true
    },
    {
      title: '脚本语言',
      dataIndex: 'ansibleType',
      sorter: true
    },
    {
      title: '脚本内容',
      dataIndex: 'content',
      sorter: true
    },
    {
      title: '类型',
      dataIndex: 'trigger',
      sorter: true
    },
    {
      title: 'Crontab',
      dataIndex: 'crontab',
      sorter: true
    },
    {
      title: '通知人',
      dataIndex: 'principalEmails',
      sorter: true
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      sorter: true
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      sorter: true
    }
  ]

  export default {
    name: 'BaselineCheck',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      return {
        queryParam: {},
        advanced: false,
        total: 0,
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return getBaselineList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              this.total = 1
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    methods: {
      handleAdd () {
        this.$router.push({ path: '/workflow/baseline-check-resources' })
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      resetSearchForm () {
        this.queryParam = {
          date: moment(new Date())
        }
      }
    }
  }
</script>
