<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="邮箱">
                <a-select v-model:value="queryParam.email" placeholder="请选择" :options="emailList"
                          showSearch allowClear></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span>
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新增</tx-button>
        <a-modal :visible="addVisible" title="新增FTP账号" :closable="false">
          <a-form-model
            :model="addParam"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
            class="myform"
            ref="addForm"
            :rules="rules">
            <a-form-model-item label="姓名" name="name">
              <a-input v-model:value="addParam.name" placeholder="请输入姓名" />
            </a-form-model-item>
            <a-form-model-item name="isOcr" label="算法FTP">
              <a-radio-group v-model:value="addParam.isOcr" button-style="solid">
                <a-radio :value="false">No</a-radio>
                <a-radio :value="true">Yes</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="邮箱" name="email">
              <a-input v-model:value="addParam.email" placeholder="请输入邮箱" />
            </a-form-model-item>
            <a-form-model-item label="密码" name="password">
              <a-input v-model:value="addParam.password" placeholder="请输入密码" />
            </a-form-model-item>
          </a-form-model>
          <template #footer>
            <tx-button key="back" @click="cancelAdd">取消</tx-button>
            <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitAdd">确定</tx-button>
          </template>
        </a-modal>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.name"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{column, record: row, text: task_type}">
          <template v-if="column.dataIndex === 'comment'">
            <div class="tooltip">
              <a-tooltip placement="left">
                <template #title>
                  <span>{{ row.comment }}</span>
                </template>
                {{ row.comment }}
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <tx-button type="link" size="small" @click="handleUpdate(row)">更新</tx-button>
            <tx-button type="link" size="small" @click="handleDelete(row)">删除</tx-button>
          </template>
        </template>
      </s-table>
      <a-modal :visible="updateVisible" title="更新FTP账号密码" ref="updateModal" :closable="false">
        <a-form-model
          :model="updateParam"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
          ref="updateForm"
          class="myform">
          <a-form-model-item label="密码" name="password">
            <a-input v-model:value="updateParam.password" placeholder="请输入密码" />
          </a-form-model-item>
        </a-form-model>
        <template #footer>
          <tx-button key="back" @click="cancelUpdate">取消</tx-button>
          <tx-button key="submit" type="primary" :loading="confirmLoading" @click="submitUpdate">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>

import { STable, Ellipsis } from '@/components'
import {
  getFtpAccountList,
  createFtpAccount,
  updateFtpAccount,
  deleteFtpAccount,
  listFtpAccountWhich
}
  from '@/api/security/ftp_account'
import { notification } from 'ant-design-vue'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true
  },
  {
    title: '姓名',
    dataIndex: 'name',
    sorter: true
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    sorter: true
  },
  {
    title: '用户名',
    dataIndex: 'username',
    sorter: true
  },
  {
    title: '主机',
    dataIndex: 'host'
  },
  {
    title: '端口',
    dataIndex: 'port'
  },
  {
    title: '备注',
    dataIndex: 'comment',
    scopedSlots: { customRender: 'comment' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' }
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'FtpAccount',
  components: {
    STable,
    Ellipsis
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    return {
      addParam: {},
      updateParam: {
        id: 0,
        password: 0
      },
      addVisible: false,
      updateVisible: false,
      confirmLoading: false,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      rules: {
        'name': [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        'isOcr': [{ required: true, message: '请选择账号类型', trigger: 'change' }],
        'email': [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
        'password': [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },
      emailList: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getFtpAccountList(requestParameters).then((res) => {
          if (res.Data.hasOwnProperty('ftpAccounts') && res.Data.ftpAccounts !== null) {
            return {
              data: res.Data.ftpAccounts,
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
          } else {
            return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
          }
        })
      }
    }
  },
  mounted () {
    this.getEmailList()
  },
  methods: {
    handleAdd () {
      this.addParam = {
        isOcr: false,
      }
      this.addVisible = true
    },
    cancelAdd () {
      this.addVisible = false
      this.confirmLoading = false
    },
    submitAdd () {
      var that = this
      antdFormValidate(this.$refs.addForm, (valid) => {
        if (valid) {
          this.confirmLoading = true
          createFtpAccount(this.addParam).then((res) => {
            if (res !== undefined && res.Code === 200) {
              notification.success({
                message: '创建成功'
              })
              this.cancelAdd()
              that.$refs.table.refresh(true)
            } else {
              notification.error({
                message: '创建失败'
              })
              this.confirmLoading = false
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleUpdate (row) {
      this.updateParam = { 'id': row.id }
      this.updateVisible = true
    },
    cancelUpdate () {
      this.updateVisible = false
      this.confirmLoading = false
    },
    submitUpdate () {
      var that = this
      antdFormValidate(this.$refs.updateForm, (valid) => {
        if (valid) {
          this.confirmLoading = true
          updateFtpAccount(this.updateParam).then((res) => {
            if (res !== undefined && res.Code === 200) {
              notification.success({
                message: '修改成功'
              })
              that.$refs.table.refresh(true)
              this.cancelUpdate()
            } else {
              notification.error({
                message: '修改失败'
              })
              this.confirmLoading = false
            }
          }).catch(() => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleDelete (row) {
      var that = this
      this.$confirm({
        title: '确认删除',
        content: `确认删除 ${row.name} 配置吗？`,
        onOk () {
          deleteFtpAccount({ 'id': row.id })
            .then((res) => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功'
                })
                that.$refs.table.refresh(true)
              }
            })
            .catch(() => {
            })
        },
        onCancel () {
        }
      })
    },
    getEmailList () {
      listFtpAccountWhich({ 'which': 'email' }).then(res => {
        if (res.Data.res === [] || res.Data.res === null) {
          this.emailList = []
          return
        }
        for (var i = 0, len = res.Data.res.length; i < len; i++) {
          var ftpAccount = {}
          ftpAccount.value = res.Data.res[i]
          ftpAccount.label = res.Data.res[i]
          this.emailList.push(ftpAccount)
        }
      })
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    }
  }
}
</script>
<style lang="less" scoped>
.myform {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}

.tooltip {
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
