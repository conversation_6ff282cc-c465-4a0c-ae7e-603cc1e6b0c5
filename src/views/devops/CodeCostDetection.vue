<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">申请</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="(record) => record.id"
        :columns="columns"
        :data="loadData"
        showPagination="auto"
      >
        <template #bodyCell="{column, text}">
        <template v-if="column.dataIndex == 'gitlabProject'">
          <a :href="text">{{ text }}</a>
        </template>
        <template v-else-if="column.dataIndex == 'status'">
          <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
        </template>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
  import moment from 'moment'
  import { STable, Ellipsis } from '@/components'
  import { getCcdList } from '@/api/devops/ccd'

  const columns = [
    {
      title: 'Gitlab仓库地址',
      dataIndex: 'gitlabProject',
      scopedSlots: { customRender: 'gitlabProject' }
    },
    {
      title: 'Gitlab分支',
      dataIndex: 'gitlabBranch',
      scopedSlots: { customRender: 'gitlabBranch' }
    },
    {
      title: '服务器地址',
      dataIndex: 'hosts',
      scopedSlots: { customRender: 'hosts' }
    },
    {
      title: '状态',
      dataIndex: 'status',
      scopedSlots: { customRender: 'status' }
    },
    {
      title: '申请人',
      dataIndex: 'principal'
    }
  ]

  const statusMap = {
    'processing': {
      status: 'processing',
      text: '处理中'
    },
    'success': {
      status: 'success',
      text: '已完成'
    },
    'error': {
      status: 'error',
      text: '分析报告异常'
    }
  }

  export default {
    name: 'CodeCostDetection',
    components: {
      STable,
      Ellipsis
    },
    data () {
      this.columns = columns
      return {
        queryParam: {},
        advanced: false,
        total: 0,
        // 加载数据方法 必须为 Promise 对象
        loadData: (parameter) => {
          const requestParameters = Object.assign({}, parameter, this.queryParam)
          return getCcdList(requestParameters).then((res) => {
            if (res.Data.hasOwnProperty('data')) {
              this.total = 1
              return res.Data
            } else {
              return { 'data': [], 'pageNo': 1, 'pageSize': 10, 'totalCount': 0, 'totalPage': 0 }
            }
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    computed: {
      rowSelection () {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      }
    },
    methods: {
      handleAdd () {
        this.$router.push({ path: '/workflow/code-cost-detection' })
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      statusFilter (type) {
        return statusMap[type]?.text || type
      },
      statusTypeFilter (type) {
        return statusMap[type]?.status || type
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      resetSearchForm () {
        this.queryParam = {
          date: moment(new Date())
        }
      }
    }
  }
</script>
