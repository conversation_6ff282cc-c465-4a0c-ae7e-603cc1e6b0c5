<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=589922500" style="text-decoration: none">
          云IDE文档
        </a>
      </tx-button>
    </template>
    <div class="table-operator">
      <tx-button type="primary" icon="plus" @click="cloudIDEApplication">云IDE申请</tx-button>
    </div>
    <a-list :grid="{ gutter: 16, column: 4 }" :data-source="data">
      <template #renderItem="{ item }">
        <a-list-item>
          <a-card hoverable>
            <template #cover>
              <img :src="replace('@/assets/svgs/ide.png')" :alt="item.ip" />
            </template>
            <a-card-meta :title="item.ip">
              <template #description>
                <a-typography-paragraph :copyable="{ text: item.pwd }">登录口令：******</a-typography-paragraph>
                <!--                <a-input-password :value="item.pwd" />-->
              </template>
            </a-card-meta>
            <template #actions>
              <a :href="item.url" target="_blank">连接</a>
            </template>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
  </page-header-wrapper>
</template>
<script>
import { defineComponent } from 'vue'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { getUserIdeList } from '@/api/devops/coder'

export default defineComponent({
  name: 'IDE',
  data() {
    return {
      data: [],
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.GetIdeUserList()
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    // TODO 改为 import 或 url
    replace(path) {
      return path.replace('@/assets', '/vendor-cdn/@img')
    },
    GetIdeUserList() {
      getUserIdeList().then(res => {
        if (res.Data.hasOwnProperty('data')) {
          this.data = noc.promiseArray(res.Data.data)
        }
      })
    },
    cloudIDEApplication() {
      this.$router.push({ path: '/workflow/cloud-ide' })
    },
  },
})
</script>
