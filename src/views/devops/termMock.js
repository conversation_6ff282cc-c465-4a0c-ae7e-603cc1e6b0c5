/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-10-30 16:14:29
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-11-01 14:23:38
 * @FilePath: \cloud_web\src\views\devops\termMock.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const termsGroups = [
  {
    name: '自定义分组1',
    isCollect: true,
    key: 1,
    hosts: [
      {
        comment: '云平台cloud云平台',
        ip: '*******',
      },
      {
        comment: 'cloud云平台',
        ip: '*******',
      },
    ],
  },
  {
    name: '自定义分组2',
    isCollect: true,
    key: 1,
    hosts: [
      {
        comment: 'CS111111111',
        ip: '*******',
      },
      {
        comment: 'CS222222222',
        ip: '*******',
      },
      {
        comment: 'CS33333333333332',
        ip: '*******',
      },
      {
        comment: 'CS44444444444',
        ip: '*******',
      },
      {
        comment: 'CS5555555555555',
        ip: '*******',
      },
      {
        comment: 'CS666666666666666',
        ip: '*******',
      },
      {
        comment: 'CS7777777777777777',
        ip: '*******',
      },
      {
        comment: 'CS88888888888888888',
        ip: '*******',
      },
    ],
  },
]

export const groups = [
  {
    key: 'test111',
    title: 'test111',
    icon: 'tag-outlined',
    children: [
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-0_tag-0_tag-0_tag-0_tag-0_tag-0_tag-0',
        title: '**********',
      },
    ],
  },
  {
    key: 'a',
    title: 'a',
    icon: 'tag-outlined',
    children: [
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-1_tag-1_tag-1_tag-1_tag-1',
        title: '**********',
      },
    ],
  },
  {
    key: 'jhgtu',
    title: 'jhgtu',
    icon: 'tag-outlined',
    children: [
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-0_tag-0_tag-0_tag-0_tag-0_tag-2',
        title: '**********',
      },
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-0_tag-0_tag-0_tag-1_tag-0_tag-0_tag-2',
        title: '**********',
      },
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-1_tag-0_tag-0_tag-2',
        title: '**********',
      },
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-1_tag-1_tag-0_tag-2',
        title: '**********',
      },
    ],
  },
  {
    key: '31',
    title: '31',
    icon: 'tag-outlined',
    children: [
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-1_tag-1_tag-1_tag-1_tag-1_tag-3',
        title: '**********',
      },
      {
        key: 'ucloud-shanghai2-hybrid_**********_tag-3',
        title: '**********',
      },
    ],
  },
  {
    key: 'ucloud-shanghai2-hybrid',
    title: 'ucloud-shanghai2-hybrid',
    icon: 'laptop-outlined',
    children: [
      {
        key: 'ucloud-shanghai2-hybrid_**********',
        title: '**********',
      },
    ],
  },
  {
    key: 'setting',
    title: '设置',
    icon: 'setting-outlined',
    children: [
      {
        key: 'dashboard',
        title: '仪表盘',
      },
      {
        key: 'fontSetting',
        title: '字体设置',
      },
      {
        key: 'termSetting',
        title: '终端设置',
      },
      {
        key: 'hotKey',
        title: '快捷键',
      },
      {
        key: 'commonCommand',
        title: '快速命令',
      },
    ],
  },
]
