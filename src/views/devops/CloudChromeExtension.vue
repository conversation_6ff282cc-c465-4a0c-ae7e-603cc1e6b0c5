<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-09-03 10:48:27
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-11-13 17:09:51
 * @FilePath: \cloud_web\src\views\devops\CloudChromeExtension.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <template #content>
      <div class="table-operator">
        <tx-button type="primary" v-if="applyStatus == -1" icon="plus" @click="cloudExtensionApply">插件申请</tx-button>
        <tx-button
          style="margin-left: 18px"
          v-if="applyStatus == 1"
          type="primary"
          icon="vertical-align-bottom-outlined"
          @click="cloudExtensionDownload"
        >
          插件下载
        </tx-button>
        <tx-button
          style="margin-left: 18px"
          v-if="applyStatus == 1 || applyStatus == 2"
          type="primary"
          icon="retweet-outlined"
          @click="cloudExtensionUpload"
        >
          插件更新
        </tx-button>
        <p style="margin: 18px" v-if="applyStatus == 0">创建中(约一分钟后请刷新页面)......</p>
        <p style="margin: 18px" v-if="applyStatus == 2">创建失败 请重试......</p>
      </div>
    </template>
  </page-header-wrapper>
  <div style="width: 80%; margin: 0 auto">
    <a-steps style="width: 80%" :current="current">
      <a-step v-for="item in steps" :description="item.description" :key="item.title" :title="item.title" />
    </a-steps>
    <div class="steps-content" style="position: relative">
      <div
        style="
          width: 1029px;
          height: 490px;
          margin: 0 auto;
          text-align: center;
          background-color: #fff;
          border-radius: 8px;
        "
      >
        <!-- v-if="applyStatus == 0 && current == 0" -->
        <img
          v-if="(applyStatus == 1 || applyStatus == 2) && current == 0"
          src="/vendor-cdn/@img/extImg/updateStep1.png"
          style="width: 100%; height: 100%"
          alt="cloud_chrome_extension"
        />
        <img v-else :src="imgs[current]" style="width: 100%; height: 100%" alt="cloud_chrome_extension" />
        <p v-if="(applyStatus == 1 || applyStatus == 2) && current == 0" style="margin-top: 10px; color: #5c5c5c">
          点击“插件更新”按钮后，插件会自动构建，耐心等待一分钟左右，刷新页面
        </p>
        <p v-else style="margin-top: 10px; color: #5c5c5c">{{ steps[current].content }}</p>
        <span
          v-if="current < steps.length - 1"
          @click="next"
          style="
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            display: inline-block;
            font-size: 40px;
            color: #808080;
          "
        >
          <right-outlined />
        </span>

        <span
          v-if="current > 0"
          @click="prev"
          style="
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            display: inline-block;
            font-size: 40px;
            color: #808080;
          "
        >
          <left-outlined />
        </span>
      </div>
    </div>
    <div class="steps-action"></div>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { createExt, statusExt, downloadAssetFile, upDate } from '@/api/devops/cloudChrome.js'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
export default defineComponent({
  name: 'IDE',

  data() {
    return {
      imgs: [
        '/vendor-cdn/@img/extImg/step1.png',
        '/vendor-cdn/@img/extImg/step2.png',
        '/vendor-cdn/@img/extImg/step3.png',
        '/vendor-cdn/@img/extImg/step4.png',
        '/vendor-cdn/@img/extImg/step5.png',
        ,
      ],
      steps: [
        {
          title: 'step1 ',
          description: '申请',
          content: '点击“插件申请”按钮后，插件会自动构建，耐心等待一分钟左右，刷新页面',
        },
        {
          title: 'step2 ',
          description: '下载',
          content: '右上角提示构建成功后，点击“插件下载”按钮，将文件保存至D盘并解压',
        },
        {
          title: 'step3 ',
          description: '安装',

          content: '通过Chrome的chrome://extensions/页面，以开发者模式加载解压后的插件文件夹',
        },
        {
          title: 'step4 ',
          description: '固定',

          content: '将插件图标固定到Chrome浏览器的工具栏上',
        },
        {
          title: 'step5 ',
          description: '认证',
          content: '点击插件图标，获取并填写企业微信验证码认证，完成',
        },
      ],
      current: 0,
      data: [],
      applyStatus: 0,
      browserSecretKey: '',
    }
  },
  async mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.checkStatus()
    function getHardwareParameters() {
      const hardwareParams = {}

      // 屏幕信息
      hardwareParams.screenWidth = screen.width
      hardwareParams.screenHeight = screen.height
      hardwareParams.availWidth = screen.availWidth
      hardwareParams.availHeight = screen.availHeight
      hardwareParams.colorDepth = screen.colorDepth
      hardwareParams.pixelDepth = screen.pixelDepth

      // Navigator信息
      hardwareParams.hardwareConcurrency = navigator.hardwareConcurrency || 'unknown'
      hardwareParams.deviceMemory = navigator.deviceMemory || 'unknown'
      hardwareParams.userAgent = navigator.userAgent

      // Canvas指纹
      try {
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        context.textBaseline = 'top'
        context.font = '14px Arial'
        context.textBaseline = 'alphabetic'
        context.fillStyle = '#f60'
        context.fillRect(125, 1, 62, 20)
        context.fillStyle = '#069'
        context.fillText('Canvas Fingerprint', 2, 15)
        context.fillStyle = 'rgba(102, 204, 0, 0.7)'
        context.fillText('Canvas Fingerprint', 4, 17)
        const canvasData = canvas.toDataURL()
        hardwareParams.canvasFingerprint = canvasData
      } catch (e) {
        hardwareParams.canvasFingerprint = 'not available'
      }

      // WebGL指纹
      try {
        const canvas = document.createElement('canvas')
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
        hardwareParams.webglRenderer = gl.getParameter(gl.RENDERER)
        hardwareParams.webglVendor = gl.getParameter(gl.VENDOR)
        hardwareParams.webglVersion = gl.getParameter(gl.VERSION)
        hardwareParams.webglShadingLanguageVersion = gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
        hardwareParams.webglExtensions = gl.getSupportedExtensions().join(';')
      } catch (e) {
        hardwareParams.webglRenderer = 'not available'
        hardwareParams.webglVendor = 'not available'
        hardwareParams.webglVersion = 'not available'
        hardwareParams.webglShadingLanguageVersion = 'not available'
        hardwareParams.webglExtensions = 'not available'
      }

      return hardwareParams
    }
    async function generateFingerprint() {
      const hardwareParams = getHardwareParameters()
      const paramString = JSON.stringify(hardwareParams)

      // 使用SubtleCrypto接口计算SHA-256哈希值
      const encoder = new TextEncoder()
      const data = encoder.encode(paramString)
      const hashBuffer = await crypto.subtle.digest('SHA-256', data)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      const fingerprint = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      return fingerprint
    }
    generateFingerprint().then(fingerprint => {
      this.browserSecretKey = fingerprint
      console.log('this.browserSecretKey:', this.browserSecretKey)
    })
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    next() {
      this.current++
    },
    prev() {
      this.current--
    },
    checkStatus() {
      statusExt().then(res => {
        console.log(res, 'statusstatus')
        this.applyStatus = res.Data.status
        if (res.Data.status == 1) {
          notification.success({
            message: '已创建成功，可下载',
          })
        } else if (res.Data.status == 0) {
          notification.warning({
            message: '正在创建插件 ，请稍后刷新页面',
          })
        } else if (res.Data.status == 2) {
          notification.error({
            message: '创建失败，请重试',
          })
        }
      })
    },
    cloudExtensionApply() {
      createExt({
        browserSecretKey: this.browserSecretKey,
      }).then(res => {
        console.log(res, 'ressss')
        // this.checkStatus()
        this.applyStatus = 0
      })
    },
    cloudExtensionDownload() {
      downloadAssetFile().then(res => {
        console.log(res, 'ressss')
        const href = URL.createObjectURL(res)
        let ele = document.createElement('a')
        ele.target = '_blank'
        ele.href = href
        ele.download = `${store.getters.email}_cloud_chrome_extension.tar.gz`
        ele.click()
        ele = null
        URL.revokeObjectURL(href)
      })
    },
    cloudExtensionUpload() {
      upDate({
        browserSecretKey: this.browserSecretKey,
      })
        .then(res => {
          notification.success({
            message: '正在更新',
          })
          console.log(res, 'ressss')
          this.applyStatus = 0
          // this.checkStatus()
        })
        .catch(err => {
          notification.error({
            message: '更新失败',
          })
        })
    },
    // TODO 改为 import 或 url
  },
})
</script>
<style scoped>
#divider .ant-divider.ant-divider-horizontal:after,
.ant-divider.ant-divider-horizontal:before {
  border-style: 2px solid #ccc !important;
}

.steps-content {
  /* border: 1px dashed #e9e9e9; */
  border-radius: 6px;
  margin-top: 16px;
  /* background-color: #fafafa; */
  min-height: 200px;
  text-align: center;
  .steps-action {
    margin-top: 24px;
  }
}
/deep/.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: #a8a8a8;
}
/deep/.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: #a8a8a8;
}
/deep/ .ant-steps-item-wait .ant-steps-item-icon {
  background-color: #dbdbdb;
  color: #696969;
}
/deep/.ant-steps-icon {
  color: #696969 !important;
}
/deep/ .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-icon .ant-steps-icon {
  color: #fff !important;
}
/deep/.ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']
  .ant-steps-item-description,
.ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']
  .ant-steps-item-icon
  .ant-steps-icon,
.ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']
  .ant-steps-item-subtitle,
.ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']
  .ant-steps-item-title {
  color: #000;
}
/deep/ .ant-steps-item-title {
  color: #000 !important;
}
</style>
