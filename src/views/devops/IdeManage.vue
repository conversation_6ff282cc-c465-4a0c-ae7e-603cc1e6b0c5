<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="IP">
                <a-input v-model:value="queryParam.ip" placeholder="IP" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="负责人">
                <a-input v-model:value="queryParam.principalEmails" placeholder="负责人" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新建</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #expandedRowRender="{ record }">
          <a-descriptions>
            <a-descriptions-item label="ID">{{ record.id }}</a-descriptions-item>
            <a-descriptions-item label="主机名称">{{ record.hostname }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ record.comment }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
          </a-descriptions>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'pwd'">
            <a-typography-paragraph :copyable="{ text: text }">******</a-typography-paragraph>
            <!--            <a-input-password style="width:230px" :value="text" />-->
          </template>
          <template v-else-if="column.dataIndex == 'url'">
            <a :href="text" target="_blank">{{ text }}</a>
          </template>
          <template v-else-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>

      <a-modal title="新建" :visible="visible" :confirm-loading="confirmLoading" @ok="handleOk" @cancel="handleCancel">
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="form.ip" />
          </a-form-model-item>
          <a-form-model-item label="Port" name="port">
            <a-input v-model:value="form.port" />
          </a-form-model-item>
          <a-form-model-item label="主机名称" name="hostname">
            <a-input v-model:value="form.hostname" />
          </a-form-model-item>
          <a-form-model-item label="URL" name="url">
            <a-input v-model:value="form.url" />
          </a-form-model-item>
          <a-form-model-item label="登陆口令" name="pwd">
            <a-input v-model:value="form.pwd" />
          </a-form-model-item>
          <a-form-model-item label="负责人" name="principalEmails">
            <a-input v-model:value="form.principalEmails" />
          </a-form-model-item>
          <a-form-model-item label="备注" name="comment">
            <a-input v-model:value="form.comment" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
        title="更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="IP" name="ip">
            <a-input v-model:value="form.ip" />
          </a-form-model-item>
          <a-form-model-item label="Port" name="port">
            <a-input v-model:value="form.port" />
          </a-form-model-item>
          <a-form-model-item label="主机名称" name="hostname">
            <a-input v-model:value="form.hostname" />
          </a-form-model-item>
          <a-form-model-item label="URL" name="url">
            <a-input v-model:value="form.url" />
          </a-form-model-item>
          <a-form-model-item label="登陆口令" name="pwd">
            <a-input v-model:value="form.pwd" />
          </a-form-model-item>
          <a-form-model-item label="负责人" name="principalEmails">
            <a-input v-model:value="form.principalEmails" />
          </a-form-model-item>
          <a-form-model-item label="备注" name="comment">
            <a-input v-model:value="form.comment" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import { getIdeList, createIde, getIdeInfo, updateIde, deleteIde } from '@/api/devops/coder'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const columns = [
  {
    title: 'IP',
    dataIndex: 'ip',
    sorter: true,
  },
  {
    title: 'Port',
    dataIndex: 'port',
    sorter: true,
  },
  {
    title: '负责人',
    dataIndex: 'principalEmails',
    sorter: true,
  },
  {
    title: 'URL',
    dataIndex: 'url',
    sorter: true,
    scopedSlots: { customRender: 'url' },
  },
  {
    title: '登陆口令',
    dataIndex: 'pwd',
    sorter: true,
    scopedSlots: { customRender: 'pwd' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'IdeManage',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      queryParam: {},
      advanced: false,
      form: {
        ip: undefined,
        port: '8090',
        hostname: undefined,
        url: undefined,
        pwd: undefined,
        principalEmails: store.getters.email,
        comment: undefined,
      },
      visible: false,
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getIdeList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        ip: [{ required: true, message: '请填写IP', trigger: 'change' }],
        port: [{ required: true, message: '请填写Port', trigger: 'change' }],
        hostname: [{ required: true, message: '请填写主机名称', trigger: 'change' }],
        url: [{ required: true, message: '请填写访问URL', trigger: 'change' }],
        pwd: [{ required: true, message: '请填写登陆口令', trigger: 'change' }],
        principalEmails: [{ required: true, message: '请填写负责人邮箱', trigger: 'change' }],
      },
    }
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleAdd() {
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createIde(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      getIdeInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateIde(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteIde(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
  },
}
</script>
