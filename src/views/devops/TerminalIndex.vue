<template>
  <div :style="termBoxStyle" id="terminalIndexBox" class="terminalIndexBox">
    <!--    <p class="optBox" style="z-index: 2">-->
    <p class="optBox" style="z-index: 9999">
      <a-tooltip placement="bottom">
        <template #title>快速命令</template>
        <span @click="openQuickCommand()" class="splitIcon">
          <img src="/vendor-cdn/@img/term/quickCommand.png" alt="" />
        </span>
      </a-tooltip>
      <a-tooltip v-if="termArr.length > 1" placement="bottom">
        <template #title>关闭所有分屏</template>
        <span @click="splitAttrs('full')" class="splitIcon">
          <img src="/vendor-cdn/@img/term/full.png" alt="" />
        </span>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>四分屏</template>
        <span @click="splitAttrs('four')" class="splitIcon">
          <img src="/vendor-cdn/@img/term/splitFour.png" alt="" />
        </span>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>左右分屏</template>
        <span @click="splitAttrs('horizon')" class="splitIcon">
          <img style="width: 22px; height: 22px" src="/vendor-cdn/@img/term/splitH.png" alt="" />
        </span>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>上下分屏</template>
        <span @click="splitAttrs('vertical')" class="splitIcon">
          <img style="width: 22px; height: 22px" src="/vendor-cdn/@img/term/splitV.png" alt="" />
        </span>
      </a-tooltip>
    </p>
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      @syncCommand="syncCommands"
      v-if="termArr[0].id == 'rootTerm'"
      class="termInstanceBox"
      id="rootTerm"
      ref="rootTerm"
    />
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @syncCommand="syncCommands"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      v-if="termArr[1] && termArr[1].id == 'splitTerm1'"
      class="termInstanceBox"
      id="splitTerm1"
      ref="splitTerm1"
    />
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @syncCommand="syncCommands"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      v-if="termArr[2] && termArr[2].id == 'splitTerm2'"
      class="termInstanceBox"
      id="splitTerm2"
      ref="splitTerm2"
    />
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @syncCommand="syncCommands"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      v-if="termArr[3] && termArr[3].id == 'splitTerm3'"
      class="termInstanceBox"
      id="splitTerm3"
      ref="splitTerm3"
    />
    <div style="width: 100%" class="globalInput" v-if="globalCommandVis">
      <a-input
        @pressEnter="serverCommand"
        v-model:value="runCommand"
        style="width: calc(100% - 180px); margin-right: 8px"
        placeholder="请输入全局命令"
      />
      <a-button type="primary" @click="serverCommand">全部执行</a-button>
      <a-button @click="globalCommandVis = false" style="margin-left: 8px">关闭</a-button>
    </div>
  </div>
  <!--  快速命令-->
  <div>
    <a-drawer
      style="z-index: 10000"
      :destroyOnClose="true"
      title="命令片段"
      placement="right"
      width="25%"
      :visible="quickCommandVisible"
      @close="quickCommandVisible = false"
    >
      <div>
        <a-row>
          <a-col :span="1" />
          <a-col :span="17">
            <a-input-search placeholder="请输入名称/命令" v-model:value="searchText" @search="quickCommandOnSearch" />
          </a-col>
          <a-col :span="5">
            <div style="float: right">
              <a-tooltip>
                <template #title>创建</template>
                <a-button style="margin-right: 8px" @click="handleAdd">
                  <template #icon>
                    <plus-outlined />
                  </template>
                </a-button>
              </a-tooltip>
            </div>
          </a-col>
          <a-col :span="1" />
        </a-row>
      </div>
      <a-card class="groupBox">
        <a-list item-layout="horizontal" :data-source="exampleList">
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a @click="handleCopy(item)">粘贴</a>
                <a @click="handleUpdate(item)">更新</a>
                <a @click="handleDel(item)">删除</a>
                <a @click="handleExec(item)">执行</a>
              </template>
              <a-list-item-meta :description="item.command">
                <template #title>
                  <a href="">{{ item.alias }}</a>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </a-drawer>
  </div>
  <a-modal title="新建" :visible="visible" width="40%" @ok="handleOk" @cancel="handleCancel" style="z-index: 10001">
    <a-form-model ref="ruleForm" :model="form" :rules="rules">
      <a-form-model-item label="别名" name="alias">
        <a-input v-model:value="form.alias" auto-size />
      </a-form-model-item>
      <a-form-model-item label="命令" name="command">
        <a-textarea v-model:value="form.command" auto-size />
      </a-form-model-item>
      <a-form-model-item label="备注" name="comment">
        <a-input v-model:value="form.comment" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>

  <a-modal
    title="更新"
    :visible="updateVisible"
    width="40%"
    @ok="handleUpdateOk"
    @cancel="handleUpdateCancel"
    style="z-index: 10001"
  >
    <a-form-model ref="ruleForm" :model="form" :rules="rules">
      <a-form-model-item label="别名" name="alias">
        <a-input v-model:value="form.alias" auto-size />
      </a-form-model-item>
      <a-form-model-item label="命令" name="command">
        <a-textarea v-model:value="form.command" auto-size />
      </a-form-model-item>
      <a-form-model-item label="备注" name="comment">
        <a-input v-model:value="form.comment" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import { onMounted, ref } from 'vue'
import Terminal from './Terminal.vue'
import {
  termConfigList,
  listTermQuickCommand,
  getTermQuickCCommand,
  createTermQuickCommand,
  updateTermQuickCommand,
  deleteTermQuickCommand,
} from '@/api/devops/term'
import { getTermMenu } from '@/api/asset'
import { notification } from 'ant-design-vue'
import { aliasRefresh } from '@/api/devops/term-proxy'
import { getCloudTermKey } from '@/config'
import CryptoJS from 'crypto-js'

export default {
  components: {
    Terminal,
  },
  name: 'TerminalIndex',
  setup() {
    const rootTerm = ref(null)
    const splitTerm1 = ref(null)
    const splitTerm2 = ref(null)
    const splitTerm3 = ref(null)
    const splitDirection = ref('')
    const runCommand = ref('')
    const globalCommandVis = ref(false)
    const termArr = ref([
      {
        type: 'root',
        id: 'rootTerm',
      },
    ])
    const termBoxStyle = ref({})
    const splitAttrs = t => {
      let termInstance = document.getElementsByClassName('termInstanceBox')
      switch (t) {
        case 'full':
          splitDirection.value = 'full'
          termArr.value = termArr.value.slice(0, 1)
          setTimeout(() => {
            for (let i = 0; i < termInstance.length; i++) {
              termInstance[i].style.height = '100%'
              termInstance[i].style.width = '100%'
            }
          })
          break
        case 'four':
          if (splitDirection.value == 'four') return
          splitDirection.value = 'four'
          if (termArr.value.length == 1) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
              flexWrap: 'wrap',
            }
            termArr.value = [
              ...termArr.value,
              { type: 'four', id: 'splitTerm1' },
              { type: 'four', id: 'splitTerm2' },
              { type: 'four', id: 'splitTerm3' },
            ]
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '50%'
              }
            })
          } else if (termArr.value.length == 2) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
              flexWrap: 'wrap',
            }
            termArr.value[1].type = 'four'
            termArr.value = [...termArr.value, { type: 'four', id: 'splitTerm2' }, { type: 'four', id: 'splitTerm3' }]
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '50%'
              }
            })
          }
          break
        case 'horizon':
          if (splitDirection.value == 'horizon') return
          splitDirection.value = 'horizon'
          if (termArr.value.length == 1) {
            termBoxStyle.value = {
              display: 'flex',
            }
            termArr.value.push({ type: 'four', id: 'splitTerm1' })
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.width = '50%'
                termInstance[i].style.height = '100% '
              }
            })
          } else if (termArr.value.length == 2) {
            termBoxStyle.value = {
              display: 'flex',
            }
            termArr.value[1].type = 'horizon'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.width = '50%'
                termInstance[i].style.height = '100% '
              }
            })
          } else if (termArr.value.length == 4) {
            termArr.value = termArr.value.slice(0, 2)
            termArr.value[1].type = 'horizon'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.width = '50%'
                termInstance[i].style.height = '100% '
              }
            })
          }
          break
        case 'vertical':
          if (splitDirection.value == 'vertical') return
          splitDirection.value = 'vertical'
          if (termArr.value.length == 1) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
            }
            termArr.value.push({
              type: 'colunm',
              // id: String(Math.random() * 99999).split('.')[0],
              id: 'splitTerm1',
            })
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '100%'
              }
            })
          } else if (termArr.value.length == 2) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
            }
            termArr.value[1].type = 'colunm'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '100%'
              }
            })
          } else if (termArr.value.length == 4) {
            termArr.value = termArr.value.slice(0, 2)
            termArr.value[1].type = 'colunm'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '100%'
              }
            })
          }
          break
      }
    }
    const closeItem = i => {
      let index = termArr.value.indexOf(i)
      if (splitDirection.value == 'four') {
        index == 1
          ? termArr.value.splice(2, 1)
          : index == 2
            ? termArr.value.splice(1, 1)
            : termArr.value.splice(index, 1)
      }
    }
    const serverCommand = () => {
      rootTerm.value ? rootTerm.value.executeGlobalCommand(runCommand.value) : ''
      splitTerm1.value ? splitTerm1.value.executeGlobalCommand(runCommand.value) : ''
      splitTerm2.value ? splitTerm2.value.executeGlobalCommand(runCommand.value) : ''
      splitTerm3.value ? splitTerm3.value.executeGlobalCommand(runCommand.value) : ''
      runCommand.value = ''
    }
    const syncCommands = params => {
      rootTerm.value ? rootTerm.value.executeSyncCommand(params) : ''
      splitTerm1.value ? splitTerm1.value.executeSyncCommand(params) : ''
      splitTerm2.value ? splitTerm2.value.executeSyncCommand(params) : ''
      splitTerm3.value ? splitTerm3.value.executeSyncCommand(params) : ''
    }
    const userConfig = ref({})
    let termListData = ref({})
    const userConfigUpdate = () => {
      termConfigList().then(res => {
        if (res.Data.hasOwnProperty('data')) {
          userConfig.value.fontSize = res.Data.data[0].fontSize
          userConfig.value.cursorStyle = res.Data.data[0].cursorStyle
          userConfig.value.scrollBack = res.Data.data[0].scrollBack
          userConfig.value.role = res.Data.data[0].role
          userConfig.value.email = res.Data.data[0].email
        }
      })
    }

    userConfigUpdate()
    const getTermMenuList = () => {
      getTermMenu().then(res => {
        termListData.value = res.Data
      })
    }
    getTermMenuList()
    onMounted(async () => {
      let main = document.getElementsByClassName('terminalIndexBox')[0].parentElement
      main.style.margin = 0
      main.style.padding = 0 + 'px'
    })
    const quickCommandVisible = ref(false)
    const visible = ref(false)
    const updateVisible = ref(false)
    const openQuickCommand = () => {
      quickCommandVisible.value = true
    }
    const quickCommandOnSearch = () => {
      callListTermQuickCommand()
    }
    const handleAdd = () => {
      form.value = {}
      quickCommandVisible.value = false
      visible.value = true
    }
    const handleOk = () => {
      if (form.value.command === '' || form.value.command === undefined) {
        notification.error({
          message: '命令字段不能为空',
        })
        return
      }
      if (form.value.alias === '' || form.value.alias === undefined) {
        notification.error({
          message: '别名字段不能为空',
        })
        return
      }
      createTermQuickCommand(form.value)
        .then(res => {
          if (res === undefined) {
            notification.error({
              message: '创建失败,后端接口错误，请联系运维开发排查~',
            })
          } else {
            visible.value = false
            quickCommandVisible.value = true
            callListTermQuickCommand()
            notification.success({
              message: '创建成功',
            })
            refresh()
          }
        })
        .catch(() => {
          notification.error({
            message: '创建失败',
          })
        })
    }
    const handleCancel = () => {
      visible.value = false
      quickCommandVisible.value = true
    }
    const handleUpdate = record => {
      quickCommandVisible.value = false
      getTermQuickCCommand(record.id).then(res => {
        form.value = res.Data
      })
      updateVisible.value = true
    }
    const handleUpdateOk = () => {
      if (form.value.command === '' || form.value.command === undefined) {
        notification.error({
          message: '命令字段不能为空',
        })
        return
      }
      if (form.value.alias === '' || form.value.alias === undefined) {
        notification.error({
          message: '别名字段不能为空',
        })
        return
      }
      updateTermQuickCommand(form.value)
        .then(res => {
          if (res === undefined) {
            notification.error({
              message: '更新失败,后端接口错误，请联系运维开发排查~',
            })
          } else {
            updateVisible.value = false
            quickCommandVisible.value = true
            callListTermQuickCommand()
            notification.success({
              message: '更新成功',
            })
            refresh()
          }
        })
        .catch(() => {})
    }
    const handleUpdateCancel = () => {
      updateVisible.value = false
      quickCommandVisible.value = true
    }
    const handleDel = record => {
      deleteTermQuickCommand(record.id).then(res => {
        if (res.Data.message === 'ok') {
          callListTermQuickCommand()
          notification.success({
            message: '删除成功',
          })
        } else {
          notification.error({
            message: '删除失败',
          })
        }
        refresh()
      })
    }
    const form = ref({})
    const searchText = ref('')
    const exampleList = ref([])
    const callListTermQuickCommand = () => {
      listTermQuickCommand({ searchText: searchText.value }).then(res => {
        exampleList.value = res.Data.data
      })
    }
    callListTermQuickCommand()
    const rules = ref({
      command: [{ required: true, message: '请填写快速命令内容', trigger: 'change' }],
      alias: [{ required: true, message: '请填写别名', trigger: 'change' }],
    })
    const handleExec = record => {
      aliasCommand('exec', record.command)
    }
    const handleCopy = record => {
      aliasCommand('paste', record.command)
    }
    const aliasCommand = (type, command) => {
      switch (type) {
        case 'exec':
          rootTerm.value.executeGlobalCommand(command)
          break
        case 'paste':
          rootTerm.value.copyAlias(command)
          break
      }
    }
    // 加密
    const encrypt = authData => {
      let keyStr = getCloudTermKey()
      let ivStr = keyStr
      let key = CryptoJS.enc.Utf8.parse(keyStr)
      let iv = CryptoJS.enc.Utf8.parse(ivStr)
      let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(authData))
      let encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      })
      return encodeURIComponent(encrypted.toString())
    }
    const refresh = () => {
      let authData = {
        email: userConfig.value.email,
      }
      const auth = decodeURIComponent(encrypt(authData))
      aliasRefresh({ data: auth }).then(response => {
        if (response) {
          console.log(response)
        }
      })
    }
    return {
      aliasCommand,
      getTermMenuList,
      termListData,
      userConfigUpdate,
      userConfig,
      syncCommands,
      globalCommandVis,
      runCommand,
      splitTerm3,
      splitTerm2,
      splitTerm1,
      rootTerm,
      serverCommand,
      closeItem,
      splitAttrs,
      termArr,
      termBoxStyle,
      splitDirection,
      openQuickCommand,
      quickCommandVisible,
      quickCommandOnSearch,
      exampleList,
      handleAdd,
      handleOk,
      handleCancel,
      handleUpdate,
      handleUpdateOk,
      handleUpdateCancel,
      handleDel,
      handleExec,
      handleCopy,
      visible,
      updateVisible,
      form,
      rules,
      searchText,
    }
  },
}
</script>
<style lang="less">
#terminalIndexBox {
  height: 100%;
  position: relative;

  .splitIcon {
    margin-left: 8px;
    display: inline-block;
    width: 30px;
    height: 30px;

    img {
      width: 25px;
      height: 25px;
    }
  }

  .optBox {
    position: absolute;
    right: 28px;
    top: 4px;
    display: flex;
  }

  .globalInput {
    position: absolute;
    bottom: 0;
  }
}

.closeaPane {
  margin: 0;
  margin-top: 1px;
  width: 25px;
  height: 25px;
  background-color: #fff;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;

  .closePos {
    width: 45%;
    height: 45%;
    display: inline-block;
    background-color: #ccc;
  }

  .closePos:hover {
    background-color: red;
  }
}

.groupBox {
  //background-color: #f5f7fa;
  border: 0 !important;
  margin-bottom: 16px;

  /deep/ .ant-card-meta-title {
    font-size: 14px !important;
    line-height: 24px;
  }

  /deep/ .ant-card-meta-avatar {
    padding-right: 8px !important;
  }
}
</style>
<style>
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}
</style>
