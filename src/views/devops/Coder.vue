<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-operator">
        <tx-button v-if="this.total === 0" type="primary" icon="plus" @click="handleAdd">申请创建</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :columns="columns"
        :data="loadData"
        showPagination="auto"
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex == 'password'">
            <a-typography-paragraph :copyable="{ text: text }">******</a-typography-paragraph>
          </template>
          <template v-else-if="column.dataIndex == 'url'">
            <a :href="text">{{ text }}</a>
          </template>
        </template>
      </s-table>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import { getCoderList } from '@/api/devops/coder'

const columns = [
  {
    title: '用户名',
    dataIndex: 'username',
  },
  {
    title: '登陆密码',
    dataIndex: 'password',
    scopedSlots: { customRender: 'pwd' },
  },
  {
    title: 'URL访问地址',
    dataIndex: 'url',
    scopedSlots: { customRender: 'url' },
  },
  {
    title: '使用人',
    dataIndex: 'principal',
  },
]

export default {
  name: 'Coder',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    return {
      queryParam: {},
      advanced: false,
      total: 0,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getCoderList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            this.total = 1
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleAdd() {
      this.$router.push({ path: '/workflow/coder' })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
  },
}
</script>
