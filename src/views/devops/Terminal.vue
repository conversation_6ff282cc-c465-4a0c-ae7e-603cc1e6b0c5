<template>
  <div class="terminalBox" id="terminalBox">
    <a-layout>
      <template>
        <a-modal v-model:visible="visible" width="600px" :title="customTitle" @ok="handleFolder">
          <a-form
            ref="addForm"
            :model="formState"
            name="basic"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            autocomplete="off"
            @finish="onFinish"
            @finishFailed="onFinishFailed"
          >
            <a-form-item label="目录名称" name="folderName" :rules="[{ required: true, message: '请输入文件夹名称' }]">
              <a-input style="width: 220px" v-model:value="formState.folderName"/>
            </a-form-item>
            <a-form-item label="机器列表" name="list">
              <a-tree-select
                :rules="[{ required: true, message: '请选择服务器', type: 'array' }]"
                :fieldNames="{ children: 'children', label: 'title', key: 'key', value: 'key' }"
                :labelInValue="true"
                v-model:value="machines"
                style="width: 100%"
                :tree-data="selectTreeData"
                tree-checkable
                allow-clear
                :show-checked-strategy="SHOW_PARENT"
                placeholder="请选择机器"
                tree-node-filter-prop="title"
              />
            </a-form-item>
          </a-form>
        </a-modal>
      </template>
      <a-modal
        :maskClosable="false"
        v-model:visible="searchVisible"
        title="搜索"
        @ok="handleSearch"
        :afterClose="searchClose"
      >
        <a-tree-select
          :fieldNames="{ children: 'children', label: 'title', key: 'key', value: 'key' }"
          v-model:value="value"
          :labelInValue="true"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="Please select"
          allow-clear
          tree-default-expand-all
          :tree-data="treeData"
          tree-node-filter-prop="title"
        ></a-tree-select>
      </a-modal>
      <a-layout-content :style="{ background: '#fff', minHeight: '280px' }">
        <a-tabs
          style="height: 100%; overflow-y: scroll; width: 100%"
          v-model:activeKey="activeKey"
          hide-add
          type="editable-card"
          @edit="onEdit"
          size="small"
          @change="clickTab"
        >
          <a-tab-pane style="margin: 0; height: 100%" v-for="pane in panes" :key="pane.key" :closable="pane.closable">
            <template #tab>
              <a-tooltip>
                <template #title>
                  {{
                    pane.content.includes('(')
                      ? pane.content.split('(')[1].slice(0, pane.content.split('(')[1].length - 1)
                      : pane.content
                  }}
                </template>
                {{ pane.title }}
              </a-tooltip>
            </template>
            <div v-if="pane.content === '字体设置'">
              <font-size-config @userConfigUpdate="userConfigUpdate"/>
            </div>
            <div v-else-if="pane.content === '终端设置'">
              <term-config/>
            </div>
            <div v-else-if="pane.content === '快捷键'">
              <hot-key/>
            </div>
            <div v-else-if="pane.content === '仪表盘'">
              <dashboard
                @folderConfig="folderConfig"
                :allGroups="dashBoardData"
                :allData="allData"
                :treeData="treeData"
                :userConfig="userConfig"
                @applyFolder="addFolder"
                @refreshMenu="refreshMenu"
                @menuControl="menuControl"
                :chartId1="'userData' + pane.key"
                :chartId2="'userTop' + pane.key"
              />
            </div>
            <div v-else-if="pane.content === '快速命令'">
              <common-command :email="userEmail"/>
            </div>
            <div style="height: 100%; display: flex; flex-direction: column" justify-content="flex-start" v-else>
              <!-- style="height: 100%" -->
              <div id="termBox" :style="{ height: pane.commandVis ? 'calc(100% - 45px)' : '100%' }">
                <term
                  :plantform="plantform"
                  :tabPane="pane"
                  @splitScreen="splitScreen"
                  :directConnect="pane.directConnect"
                  :ref="setItemRef(pane.key)"
                  :authkey="pane.authkey ? pane.authkey : ''"
                  :fullScreen="isFull"
                  :ip="pane.title"
                  :content="pane.content"
                  :email="userEmail"
                  :id="pane.key"
                  :fontSize="fontSize"
                  :cursorStyle="cursorStyle"
                  :scrollBack="scrollBack"
                  @collapseMenu="collapseMenu"
                  @commandSwitch="commandSwitch"
                  @syncCommand="syncCommand"
                />
              </div>
              <div v-if="pane.commandVis" style="height: 45px; width: 100%; padding: 4px">
                <a-input
                  @pressEnter="executeGlobalCommand(pane)"
                  style="width: calc(100% - 180px); margin-right: 8px"
                  v-model:value="gaobalCommand"
                  placeholder="请输入全局命令"
                />
                <a-button @click="executeGlobalCommand(pane)" type="primary">全部执行</a-button>
                <a-button style="margin-left: 8px" @click="pane.commandVis = false">关闭</a-button>
              </div>
            </div>
          </a-tab-pane>
          <template #leftExtra v-if="isMobile">
            <a-switch v-model:checked="menuHide" size="small"/>
          </template>
          <template
            #rightExtra
            v-if="
              splitScreenTitle !== '' &&splitScreenTitle !== '仪表盘' && splitScreenTitle !== '字体设置' && splitScreenTitle !== '终端设置' && splitScreenTitle !== '快捷键' && splitScreenTitle !== '快速命令'
            "
          ></template>
        </a-tabs>
      </a-layout-content>
      <a-layout-content v-if="isSplitScreen >= 1" style="padding: 12px; background-color: #fff; min-height: 280px">
        <a-tabs
          v-model:activeKey="splitScreenActiveKey"
          hide-add
          type="editable-card"
          @edit="onEditSplitScreen"
          size="small"
        >
          <a-tab-pane v-for="pane in splitScreenPanes" :key="pane.key" :tab="pane.title" :closable="pane.closable">
            <div v-if="pane.content === '字体设置'">
              <font-size-config/>
            </div>
            <div v-else-if="pane.content === '终端设置'">
              <term-config/>
            </div>
            <div v-else-if="pane.content === '快捷键'">
              <hot-key/>
            </div>
            <div v-else-if="pane.content === '仪表盘'">
              <dashboard @applyFolder="addFolder" :chartId1="'userData' + pane.key" :chartId2="'userTop' + pane.key"/>
            </div>
            <div v-else-if="pane.content === '快速命令'">
              <common-command :email="userEmail"/>
            </div>
            <div v-else>
              <term
                :directConnect="pane.directConnect"
                :plantform="plantform"
                :fullScreen="isFull"
                :ip="pane.title"
                :content="pane.content"
                :email="userEmail"
                :id="pane.key"
                :fontSize="fontSize"
                :cursorStyle="cursorStyle"
                :scrollBack="scrollBack"
                @collapseMenu="collapseMenu"
              />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-layout-content>
      <a-modal
        :maskClosable="false"
        :title="plantform == 'qw' ? '手机短信验证码二次验证' : '企业微信合小云二次验证'"
        :visible="connectVisible"
        @ok="okCheck"
        @cancel="cancelCheck"
      >
        <a-spin v-if="verifyLoading"/>
        <a-input v-else @pressEnter="okCheck" v-model:value="verificationCode" placeholder="请输入验证码"></a-input>
      </a-modal>
      <a-modal
        :maskClosable="false"
        title="服务器管理"
        :visible="hostConfigVisible"
        @ok="hostConfigOk"
        @cancel="cancelConfig"
      >
        <a-form
          v-if="hostConfigVisible"
          name="hostConfigForm"
          :model="hostConfigForm"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="服务器备注" name="comment" :rules="[{ required: true, message: '请输入服务器备注!' }]">
            <a-input v-model:value="hostConfigForm.comment"/>
          </a-form-item>
          <a-form-item label="服务器IP" name="ip" :rules="[{ required: true, message: '' }]">
            <a-input :disabled="true" v-model:value="hostConfigForm.ip"/>
          </a-form-item>
        </a-form>
      </a-modal>

      <a-modal :maskClosable="false" title="删除分组" :visible="delVisible" @ok="confirmDel" @cancel="cancelDel">
        <p>确认要删除所选自定义分组吗？删除后不可找回</p>
      </a-modal>
    </a-layout>
  </div>
</template>
<script>
import {createVNode, defineComponent, inject, nextTick, reactive, ref, watch} from 'vue'
import Term from '@/views/devops/term/Term.vue'
import TermConfig from '@/views/devops/term/TermConfig.vue'
import store from '@/store'
import {removeWatermark, setWaterMark} from '@/utils/watermark'
import {
  checkCode,
  createTermMenu,
  deleteTermMenu,
  sendPlantform,
  updateTermMenu,
} from '@/api/devops/term'
import {updateComment} from '@/api/asset'
import FontSizeConfig from '@/views/devops/term/FontSizeConfig.vue'
import Dashboard from '@/views/devops/term/IndexPage.vue'
import CommonCommand from '@/views/devops/term/CommonCommand.vue'
import HotKey from '@/views/devops/term/HotKey.vue'
import {message, notification} from 'ant-design-vue'
import {v4 as uuidv4} from 'uuid'
import {getNetAccess} from '@/api/routePermission'
import {TreeSelect} from 'ant-design-vue'
import {deepClone} from '@/utils/util'
import {componentInstances} from './term/eventBus'
import {ExclamationCircleOutlined} from '@ant-design/icons-vue'
import {Form, Modal} from 'ant-design-vue'

const SHOW_PARENT = TreeSelect.SHOW_PARENT
const useForm = Form.useForm
export default defineComponent({
  name: 'Terminal',
  components: {
    HotKey,
    FontSizeConfig,
    Term,
    TermConfig,
    Dashboard,
    CommonCommand,
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    let that = this
    // document.addEventListener('click', this.rClickFn)
    let main = document.getElementsByClassName('terminalBox')[0].parentElement
    main.style.margin = 0
    main.style.padding = 0 + 'px'
    this.toogleFull()
    setTimeout(() => {
      const musks = document.getElementsByClassName('ant-drawer-mask')
      if (musks && musks.length > 0) {
        for (let i = 0; i < musks.length; i++) {
          musks[i].click()
        }
      }
    }, 1000)
  },
  props: {
    userConfig: {
      type: Object,
      default: () => ({}),
    },
    termListData: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    verificationCode: {
      handler: function (val) {
        // if (val) {
        //   if (val.length === 6) {
        //     this.okCheck()
        //   } else if (val.length > 6) {
        //     notification.error({
        //       message: '输入六位验证码',
        //     })
        //   }
        // }
      },
    },
  },
  unmounted() {
    removeWatermark()
  },
  setup(context, props) {
    const fontSize = ref(16)
    const cursorStyle = ref('bar')
    const scrollBack = ref(100)
    if (Object.keys(context.userConfig).length) {
      fontSize.value = context.userConfig.fontSize
      cursorStyle.value = context.userConfig.cursorStyle
      scrollBack.value = context.userConfig.scrollBack
    }
    watch(context.userConfig, (n, o) => {
      if (n && Object.keys(n).length) {
        fontSize.value = n.fontSize
        cursorStyle.value = n.cursorStyle
        scrollBack.value = n.scrollBack
      }
    })

    const commandSwitch = params => {
      // params.commandVis = !params.commandVis
      gaobalCommand.value = ''
      props.emit('openGlobalCommand')
    }
    const userConfigUpdate = () => {
      props.emit('userConfigUpdate')
    }
    let gaobalCommand = ref('')
    const menuVis = ref(false)
    const itemRefs = ref({})
    // 设置动态的 ref
    const setItemRef = key => el => {
      if (el) {
        // 绑定子组件实例
        itemRefs.value[key] = el
      } else {
        // 当组件卸载时，删除引用
        delete itemRefs.value[key]
      }
    }

    const verificationCode = ref('')
    const plantform = ref('web')
    let ua = window.navigator.userAgent.toLowerCase()
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(window.navigator.userAgent)) {
      plantform.value = 'phone'
    } else if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
      plantform.value = 'qw'
    }
    const formState = ref({
      folderName: '',
      list: [],
      oldName: '',
    })
    const toogleFull = inject('toogleFull', () => {
    })
    const toogleNormal = inject('toogleNormal', () => {
    })
    const machines = ref([])
    const hostConfigVisible = ref(false)
    const hostConfigForm = ref({
      ip: '',
      comment: '',
    })

    const {resetFields, validate, validateInfos} = useForm(
      hostConfigForm,
      reactive({
        ip: [
          {
            required: true,
            message: 'Please input IP',
          },
        ],
        comment: [
          {
            required: true,
            message: 'Please input IP',
          },
        ],
      })
    )

    const value = ref()
    const currentItem = ref(null)
    let treeData = ref([])
    const modifyClick = item => {
    }
    watch(value, () => {
    })
    const authkey = ref('')
    const addForm = ref()
    const visible = ref(false)
    const customTitle = ref('')
    const searchVisible = ref(false)
    const addFolder = () => {
      visible.value = true
      machines.value = []
      formState.value.folderName = ''
      customTitle.value = '新增自定义菜单'
    }
    const searchTerminal = () => {
      searchVisible.value = true
      value.value = ''
    }
    const treeChange = (value, e) => {
      formState.value.list = e.checkedNodes
    }
    const handleFolder = e => {
      addForm.value
        .validate()
        .then(res => {
          switch (customTitle.value) {
            case '新增自定义菜单':
              if (machines.value.length) {
                visible.value = false
                let targetArr = []
                machines.value.forEach(item => {
                  if (item.value.includes('_')) {
                    targetArr.push(item.value)
                  }
                })
                createTermMenu({
                  email: store.getters.email,
                  folderName: formState.value.folderName,
                  folderContent: targetArr,
                }).then(res => {
                  props.emit('getTermMenuList')
                })
              } else {
                message.error('请选择服务器')
              }
              break
            case '管理':
              // someCode
              if (machines.value.length) {
                let targetArr = []
                machines.value.forEach(item => {
                  if (item.value.includes('_')) {
                    targetArr.push(item.value)
                  }
                })
                updateTermMenu({
                  email: store.getters.email,
                  folderContent: targetArr,
                  oldName: formState.value.oldName,
                  newName: formState.value.folderName,
                })
                  .then(res => {
                    props.emit('getTermMenuList')
                  })
                  .catch(() => {
                  })
                  .finally(() => {
                    visible.value = false
                  })
              } else {
                message.error('请选择服务器')
              }
              break
          }
        })
        .catch(err => {
        })
    }
    const checkIp = ref('')
    const checkKey = ref('')
    const connectVisible = ref(false)
    const handleSearch = e => {
      let item = value.value
      // 获取当前打开tab<4
      let lastIndex = 0
      panes.value.forEach((pane, i) => {
        if (
          pane.title !==
          ('仪表盘' ||
            pane.title == '字体设置' ||
            pane.title == '终端设置' ||
            pane.title == '快捷键' ||
            pane.title == '快速命令')
        ) {
          lastIndex++
        }
      })
      getNetAccess().then(res => {
        if (res.status) {
          // true 为外网环境
          checkIp.value = item.label.split('(')[0]
          checkKey.value = item.value
          sendPlantform({
            net: res.status,
            ip: item.label.split('(')[0],
            type: plantform.value,
          }).then(r => {
            if (r.Data.needCheck) {
              if (r.Data.message == 'ok') {
                notification.success({
                  message: '已发送验证码',
                })
              } else if (r.Data.message == 'exist') {
                notification.success({
                  message: '验证码未过期，请勿重复点击',
                })
              }
              connectVisible.value = true
              verificationCode.value = ''
            } else {
              if (r.Data.authkey) {
                authkey.value = r.Data.authkey
                add({title: item.label.split('(')[0], content: item.label, key: item.value, authkey: authkey.value})
              }
            }
          })
          checkIp.value = item.label.split('(')[0]
          checkKey.value = item.value
        } else {
          // 内网
          if (plantform.value == 'qw') {
            add({title: item.label.split('(')[0], content: item.label, key: item.value})
          } else {
            sendPlantform({
              net: res.status,
              ip: item.label.split('(')[0],
              type: plantform.value,
            }).then(r => {
              if (r.Data.needCheck) {
                if (r.Data.message == 'ok') {
                  notification.success({
                    message: '已发送验证码',
                  })
                } else if (r.Data.message == 'exist') {
                  notification.success({
                    message: '验证码未过期，请勿重复点击',
                  })
                }
                connectVisible.value = true
                verificationCode.value = ''
              } else {
                if (r.Data.authkey) {
                  authkey.value = r.Data.authkey
                  add({title: item.label.split('(')[0], content: item.label, key: item.value, authkey: authkey.value})
                }
              }
              checkIp.value = item.label.split('(')[0]
              checkKey.value = item.value
            })
          }
          // 内网环境
        }
      })
      searchVisible.value = false
    }
    const searchClose = e => {
    }

    const getParentKey = (key, tree) => {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
          if (node.children.some(item => item.key === key)) {
            parentKey = node.key
          } else if (getParentKey(key, node.children)) {
            parentKey = getParentKey(key, node.children)
          }
        }
      }
      return parentKey
    }
    const isFull = ref(true)
    const onFinish = values => {
    }
    const onFinishFailed = errorInfo => {
    }
    const panes = ref([
      {
        title: '仪表盘',
        content: '仪表盘',
        key: 'dashboard',
        closable: false,
      },
    ])
    const splitScreenPanes = ref([
      {
        title: '仪表盘',
        content: '仪表盘',
        key: 'dashboardSplit',
      },
    ])
    const selectTreeData = ref([])
    const routers = ref([])
    const manageTree = ref([])

    const activeKey = ref(panes.value[0].key)
    const splitScreenActiveKey = ref(splitScreenPanes.value[0].key)
    const isSplitScreen = ref(0)
    const splitScreenTitle = ref('')
    const menuHide = ref(true)

    const newTabIndex = ref(0)
    const add = ({title, content, key, authkey, directConnect}) => {
      const username = store.getters.email.split('@')[0]
      activeKey.value = `${username}@${title}:${uuidv4()}`
      splitScreenTitle.value = title
      panes.value.push({
        title: title,
        content: content,
        key: activeKey.value,
        authkey,
        commandVis: false,
        directConnect,
      })
      menuHide.value = true
    }
    const addSplitScreen = () => {
      const username = store.getters.email.split('@')[0]
      splitScreenActiveKey.value = `${username}@${splitScreenTitle.value}:${uuidv4()}split${newTabIndex.value++}`
      isSplitScreen.value++
      splitScreenPanes.value.push({
        title: splitScreenTitle.value,
        content: splitScreenTitle.value,
        key: splitScreenActiveKey.value,
      })
    }
    const remove = targetKey => {
      let lastIndex = 0
      panes.value.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1
        }
      })
      panes.value = panes.value.filter(pane => pane.key !== targetKey)
      if (panes.value.length && activeKey.value === targetKey) {
        if (lastIndex >= 0) {
          activeKey.value = panes.value[lastIndex].key
          menuHide.value = false
        } else {
          activeKey.value = panes.value[0].key
        }
      }
      if (activeKey.value === 'dashboard') {
        splitScreenTitle.value = '仪表盘'
      } else {
        const ipInfo = activeKey.value.split(':')[0].split('@')
        if (ipInfo.length === 2) {
          splitScreenTitle.value = ipInfo[1]
        } else {
          splitScreenTitle.value = ''
        }
      }

      if (panes.value.length === 0) {
        menuHide.value = false
      }
    }
    const executeGlobalCommand = code => {
      if (activeKey.value == 'dashboard ') return
      const msgType = 'TERMINAL_DATA'
      const processedIds = new Set() // 用于跟踪已处理的id
      componentInstances.value.forEach(item => {
        let instance = item.instance
        if (item.key == activeKey.value && !processedIds.has(instance.data.id)) {
          instance.data.wsInstanceLocal.send(JSON.stringify({terminalId: instance.data.id, msgType, data: code}))
          instance.data.wsInstanceLocal.send(JSON.stringify({terminalId: instance.data.id, msgType, data: '\r'}))
          processedIds.add(instance.data.id) // 将已处理的id添加到集合中
        }
      })
      // gaobalCommand.value = ''
    }
    const copyAlias = code => {
      if (activeKey.value == 'dashboard ') return
      const processedIds = new Set() // 用于跟踪已处理的id
      const msgType = 'TERMINAL_DATA'
      componentInstances.value.forEach(item => {
        let instance = item.instance
        if (item.key == activeKey.value && !processedIds.has(instance.data.id)) {
          instance.data.wsInstanceLocal.send(JSON.stringify({terminalId: instance.data.id, msgType, data: code}))
        }
      })
    }
    const syncCommand = (code, key, instanceId) => {
      props.emit('syncCommand', {code, key, instanceId})
    }
    const executeSyncCommand = params => {
      let {code, key, instanceId} = params
      const processedIds = new Set() // 用于跟踪已处理的id
      let flag = false
      // ctrl+c 检测是复制还是中断命令，默认为中断
      if (code == '\x03') {
        componentInstances.value.forEach(item => {
          let instance = item.instance

          if (
            item.key == activeKey.value &&
            !processedIds.has(instance.data.id) &&
            instanceId == instance.data.instanceId &&
            instance.data.syncInput
          ) {
            instance.data.copy ? (flag = true) : ''
          }
        })
      }
      componentInstances.value.forEach(item => {
        let instance = item.instance
        let terminalId = instance.data.id
        if (
          item.key == activeKey.value &&
          !processedIds.has(instance.data.id) &&
          instanceId != instance.data.instanceId &&
          instance.data.syncInput
        ) {
          if (code == '\x03') {
            // 执行复制命令
            // debugger
            if (flag) {
              if (instance.data.copy !== undefined) {
                navigator.clipboard.writeText(instance.data.copy)
                instance.data.copy = undefined
              }
            } else {
              // 执行中断命令
              instance.data.xtermInstanceLocal.write('\x03')
              const msgType = 'TERMINAL_DATA'
              instance.data.wsInstanceLocal.send(JSON.stringify({terminalId, msgType, data: code}))
            }
          } else if (code == '\x16') {
            navigator.clipboard.readText().then(clipText => {
              if (clipText !== 'undefined') {
                const msgType = 'TERMINAL_DATA'
                const data = clipText
                instance.data.wsInstanceLocal.send(JSON.stringify({terminalId, msgType, data}))
              }
            })
          } else if (code == '\x0b') {
            // Ctrl+K 清屏
            const msgType = 'TERMINAL_DATA'
            const data = 'clear\r'
            instance.data.wsInstanceLocal.send(JSON.stringify({terminalId, msgType, data}))
          } else {
            // instance.data.xtermInstanceLocal.write(code)
            const msgType = 'TERMINAL_DATA'
            instance.data.wsInstanceLocal.send(JSON.stringify({terminalId, msgType, data: code}))
          }
          processedIds.add(instance.data.id) // 将已处理的id添加到集合中
        }
      })
    }
    const onEdit = targetKey => {
      // 含有快速命令，快捷键 设置等直接关闭
      if (targetKey.includes('设置') || targetKey.includes('快')) {
        remove(targetKey)
        return
      }
      Modal.confirm({
        title: '确认断开当前服务器连接吗？',
        icon: createVNode(ExclamationCircleOutlined),
        onOk() {
          const instance = itemRefs.value[targetKey]
          instance && instance.closeWebSocket ? instance.closeWebSocket(JSON.stringify()) : ''
          remove(targetKey)
        },
        onCancel() {
        },
        class: 'test',
      })
    }
    const splitScreenRemove = targetKey => {
      let lastIndex = 0
      splitScreenPanes.value.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1
        }
      })
      splitScreenPanes.value = splitScreenPanes.value.filter(pane => pane.key !== targetKey)
      if (splitScreenPanes.value.length && splitScreenActiveKey.value === targetKey) {
        if (lastIndex >= 0) {
          splitScreenActiveKey.value = splitScreenPanes.value[lastIndex].key
        } else {
          splitScreenActiveKey.value = splitScreenPanes.value[0].key
        }
      }

      if (splitScreenPanes.value.length === 0) {
        isSplitScreen.value = 0
      }
    }
    const onEditSplitScreen = targetKey => {
      splitScreenRemove(targetKey)
    }
    const dashBoardData = ref([
      {
        children: [],
      },
    ])
    let allData = reactive({data: []})
    const refreshMenu = async () => {
      treeData.value = []
      await props.emit('getTermMenuList')
      getMenus('fresh', context.termListData)
    }
    const menuControl = () => {
      if (plantform.value == 'qw') return
      menuVis.value = !menuVis.value
      if (menuVis.value) {
        toogleNormal()
      } else {
        toogleFull()
      }
    }

    // dashboard过来的方法
    const folderConfig = params => {
      const {item, opt} = params
      switch (opt) {
        case 'collect': // 置顶
          item.favorite ? message.success('收藏成功') : message.success('已取消收藏')
          // getMenus('', context.termListData)
          props.emit('getTermMenuList')
          break
        case 'trans': // 展开收起
          let index = dashBoardData.value.indexOf(item)
          if (item.children.length > 5) {
            dashBoardData.value[index].children = item.children.slice(0, 5)
          } else {
            dashBoardData.value[index].children = allData.data[index].children
          }
          break
        case 'manage': // 管理
          visible.value = true
          customTitle.value = '管理'
          formState.value.folderName = item.title
          formState.value.oldName = item.title
          machines.value = []
          item.children.forEach(item => {
            machines.value.push({
              label: item.title,
              value: item.key,
            })
          })
          break
        case 'del': // 删除
          delVisible.value = true
          currentItem.value = item
          break
        case 'host': // 打开host
          checkMFA(item)
          break
        case 'hostSearch':
          value.value = item
          handleSearch()
          break
        case 'hostOpt':
          hostConfigVisible.value = true
          let handlStr = item.title.split('(')
          nextTick(() => {
            hostConfigForm.value.ip = item.title.includes('(') ? handlStr[0] : item.title
            hostConfigForm.value.comment = item.title.includes('(')
              ? handlStr[1].slice(0, handlStr[1].length - 1)
              : item.title
          })
      }
    }
    const rightClick = (list, e) => {
      let targetClass = e.target.className
      if (targetClass.includes('mySubMenu')) {
        e.preventDefault() // 禁用默认的右键菜单事件
        let menu = document.querySelector('#context-menu')
        // 计算菜单显示位置
        let screenWidth = window.innerWidth // 当前屏幕宽度
        let screenHeight = window.innerHeight // 当前屏幕高度
        let menuWidth = menu.offsetWidth // 菜单宽度
        let menuHeight = menu.offsetHeight // 菜单高度
        let xPos = e.clientX // 鼠标x坐标位置
        let yPos = e.clientY // 鼠标y坐标位置
        // 如果距离右边屏幕距离过近，需要调整菜单x坐标位置
        if (xPos + menuWidth > screenWidth) {
          xPos -= menuWidth
        }
        currentItem.value = list
        formState.value.folderName = list.title
        formState.value.oldName = list.title
        machines.value = []
        list.children.forEach(item => {
          machines.value.push({
            label: item.title,
            value: item.key,
          })
        })
        // 如果距离底部屏幕距离过近，需要调整菜单y坐标位置
        if (yPos + menuHeight > screenHeight) {
          yPos -= menuHeight
        }

        // 显示右键菜单
        menu.style.left = xPos + 'px'
        menu.style.top = yPos + 'px'
        menu.style.display = 'block'
      }
    }
    const hostConfigOk = () => {
      validate()
        .then(res => {
          updateComment({
            ip: hostConfigForm.value.ip,
            comment: hostConfigForm.value.comment,
          })
            .then(r => {
              getMenus('', context.termListData)
            })
            .catch(err => {
            })
            .finally(() => {
              hostConfigVisible.value = false
              resetFields()
            })
        })
        .catch(err => {
        })
    }
    const cancelConfig = () => {
      hostConfigVisible.value = false
      resetFields()
    }
    const delVisible = ref(false)
    const confirmDel = () => {
      // userEmail
      deleteTermMenu({
        email: store.getters.email,
        folderName: currentItem.value.title,
      })
        .then(res => {
          message.success('删除成功')
          getMenus('', context.termListData)
        })
        .catch(err => {
          message.error('删除失败')
        })
        .finally(() => {
          delVisible.value = false
        })
    }
    const cancelDel = () => {
      delVisible.value = false
    }
    const checkedItem = ref()
    const checkMFA = item => {
      checkedItem.value = item
      // add({ title: item.title.split('(')[0], content: item.title, key: item.key, authkey: authkey.value })
      // return
      if (
        item.title === '仪表盘' ||
        item.title === '字体设置' ||
        item.title === '终端设置' ||
        item.title === '快捷键' ||
        item.title === '快速命令'
      ) {
        add({title: item.title.split('(')[0], content: item.title, key: item.key})
      } else {
        // 获取当前打开tab<4
        let lastIndex = 0
        panes.value.forEach((pane, i) => {
          if (
            pane.title !==
            ('仪表盘' ||
              pane.title == '字体设置' ||
              pane.title == '终端设置' ||
              pane.title == '快捷键' ||
              pane.title == '快速命令')
          ) {
            lastIndex++
          }
        })
        getNetAccess().then(res => {
          if (res.status) {
            checkIp.value = item.title.split('(')[0]
            checkKey.value = item.key
            sendPlantform({
              net: res.status,
              ip: item.title.split('(')[0],
              type: plantform.value,
            }).then(r => {
              if (r.Data.needCheck) {
                if (r.Data.message == 'ok') {
                  notification.success({
                    message: '已发送验证码',
                  })
                } else if (r.Data.message == 'exist') {
                  notification.success({
                    message: '验证码未过期，请勿重复点击',
                  })
                }
                connectVisible.value = true
                verificationCode.value = ''
              } else {
                if (r.Data.authkey) {
                  authkey.value = r.Data.authkey
                  add({title: item.title.split('(')[0], content: item.title, key: item.key, authkey: authkey.value})
                }
              }
            })
            checkIp.value = item.title.split('(')[0]
            checkKey.value = item.key
          } else {
            // 内网
            // add({ title: item.title.split('(')[0], content: item.title, key: item.key, directConnect: true })
            // return
            if (plantform.value == 'qw') {
              add({title: item.title.split('(')[0], content: item.title, key: item.key, directConnect: true})
              // add({ title: item.title.split('(')[0], content: item.title, key: item.key })
            } else {
              sendPlantform({
                net: res.status,
                ip: item.title.split('(')[0],
                type: plantform.value,
              }).then(r => {
                if (r.Data.needCheck) {
                  if (r.Data.message == 'ok') {
                    notification.success({
                      message: '已发送验证码',
                    })
                  } else if (r.Data.message == 'exist') {
                    notification.success({
                      message: '验证码未过期，请勿重复点击',
                    })
                  }
                  connectVisible.value = true
                  verificationCode.value = ''
                } else {
                  if (r.Data.authkey) {
                    authkey.value = r.Data.authkey
                    add({title: item.title.split('(')[0], content: item.title, key: item.key, authkey: authkey.value})
                  }
                }
              })
              checkIp.value = item.title.split('(')[0]
              checkKey.value = item.key
            }

            // add({ title: item.title.split('(')[0], content: item.title, key: item.key })
          }
        })
      }
    }
    const getMenus = (f = '', datas) => {
      if (datas.hasOwnProperty('routers')) {
        routers.value = datas.routers
        treeData.value = []
        datas.routers.forEach(item => {
          if (item.key != 'setting') {
            treeData.value.push(item)
          }
        })
        if (f == 'fresh') {
          notification.success({
            message: '数据刷新成功',
          })
        }
        selectTreeData.value = deepClone(datas.routers) // 需要深拷贝
        manageTree.value = deepClone(datas.routers)
        selectTreeData.value.forEach(i => {
          // 新增文件夹 tree父级不能选中
          if (i.children) {
            i.disabled = true
          }
        })
        selectTreeData.value.pop() // 新增 最后一行设置不能选
        manageTree.value.pop() // 管理 最后一行设置不能选
        // 处理top6，默认前六个
        let filterGroup = () => {
          let arr = []
          let group = deepClone(datas.routers)
          group.forEach(i => {
            if (i.title == '设置') {
              i.children = i.children.slice(1, 6)
            } else if (i.children && i.children.length > 5 && i.icon != 'tag-outlined') {
              i.children = i.children.slice(0, 5)
            }
            arr.push(i)
          })
          return arr
        }
        dashBoardData.value = filterGroup()
        allData.data = datas.routers
      }
    }
    if (Object.keys(context.termListData).length) {
      getMenus('', context.termListData)
    }
    watch(
      () => context.termListData,
      (newVal, oldVal) => {
        getMenus('', newVal)
      },
      {
        deep: true,
        immediate: true,
      }
    )
    return {
      itemRefs,
      userConfigUpdate,
      executeSyncCommand,
      syncCommand,
      commandSwitch,
      executeGlobalCommand,
      copyAlias,
      gaobalCommand,
      menuVis,
      menuControl,
      refreshMenu,
      setItemRef,
      plantform,
      toogleFull,
      checkMFA,
      checkedItem,
      folderConfig,
      dashBoardData,
      allData,
      isFull,
      SHOW_PARENT,
      machines,
      treeChange,
      currentItem,
      manageTree,
      confirmDel,
      delVisible,
      cancelDel,
      hostConfigForm,
      validateInfos,
      hostConfigVisible,
      cancelConfig,
      customTitle,
      hostConfigOk,
      selectTreeData,
      routers,
      searchClose,
      userEmail: store.getters.email,
      fontSize,
      cursorStyle,
      scrollBack,
      panes,
      splitScreenPanes,
      splitScreenTitle,
      isSplitScreen,
      activeKey,
      splitScreenActiveKey,
      onEdit,
      onEditSplitScreen,
      addForm,
      treeData,
      value,
      add,
      addSplitScreen,
      getMenus,
      visible,
      searchTerminal,
      searchVisible,
      onFinishFailed,
      modifyClick,
      onFinish,
      formState,
      addFolder,
      handleFolder,
      handleSearch,
      isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
      menuHide,
      selectedKeys: ref(['dashboard']),
      collapsed: ref(true),
      connectVisible,
      verifyLoading: ref(false),
      verificationCode,
      checkIp,
      checkKey,
      rightClick,
    }
  },
  methods: {
    splitScreen(direction) {
    },
    rClickFn(e) {
      let menu = document.querySelector('#context-menu')
      // 如果点击的是菜单项，执行对应的操作
      switch (e.target.className) {
        case 'delete':
          // 点击了“删除”菜单项的操作
          this.delVisible = true
          break
        case 'manage':
          // 点击了“管理”菜单项的操作
          this.visible = true
          this.customTitle = '管理'
          break
      }
      // 隐藏右键菜单
      if (menu) {
      }
      menu ? (menu.style.display = 'none') : ''
    },
    serverAuth() {
      let routeUrl = this.$router.resolve({path: '/workflow/server-permission-operation'})
      routeUrl.href ? window.open(routeUrl.href, '_blank') : ''
    },
    collapseMenu(full) {
      this.isFull = full
      this.collapsed = true
    },
    clickTab(activeKey) {
      if (this.itemRefs && this.itemRefs[activeKey]) {
        this.itemRefs[activeKey].setWaterMark()
      }
      const ipInfo = activeKey.split(':')[0].split('@')
      if (ipInfo.length === 2) {
        this.splitScreenTitle = ipInfo[1]
      } else {
        this.splitScreenTitle = ''
      }
    },
    cancelCheck() {
      this.connectVisible = false
    },
    enterClick() {
      this.okCheck()
    },
    codeChange(val) {
    },
    okCheck() {
      if (this.verificationCode.length == 0) {
        notification.warning({
          message: '请输入六位数验证码',
        })
        return
      }
      const checkData = {}
      checkData.email = store.getters.email
      checkData.code = this.verificationCode
      this.verifyLoading = true
      checkCode({
        code: this.verificationCode,
        ip: this.checkIp,
        type: this.plantform,
      })
        .then(res => {
          if (res.Data.message === 'ok') {
            this.connectVisible = false
            if (res.Data.authkey) {
              this.authkey = res.Data.authkey
              this.add({title: this.checkIp, content: this.checkIp, key: this.checkKey, authkey: this.authkey})
            } else {
              notification.error({
                message: '校验失败',
              })
            }
          } else if (res.Data.message === 'error') {
            notification.error({
              message: '输入验证码错误',
            })
            this.verificationCode = ''
          } else if (res.Data.message === 'notExist') {
            notification.success({
              message: '验证码已过期,已重新发送',
            })
            this.checkMFA(this.checkedItem)
          } else if (res.Data.message === 'exist') {
            notification.warning({
              message: '验证码未过期',
            })
          }
        })
        .finally(() => {
          this.verifyLoading = false
        })
    },
  },
})
</script>
<style scoped lang="less">
/deep/ .ant-menu-title-content {
  height: 40px !important;
}

#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

.terminals {
  display: flex;
}

.site-layout .site-layout-background {
  background: #fff;
}

.ant-tabs-top > .ant-tabs-nav {
  margin: 0 0 0px;
}

.mySlider {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: hidden;
}

.mySlider::-webkit-scrollbar {
  /*滚动条整体样式*/
  height: 8px;
  width: 8px;
}

.mySlider::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background-color: transparent;
  border-radius: 8px;
  outline: none;
}

//滚动条底层颜色!
.mySlider::-webkit-scrollbar-track-piece {
  background-color: #f0f2f5;
}

.mySlider:hover::-webkit-scrollbar-thumb {
  background-color: #556370;
}

.context-menu {
  background-color: #fff;
  border: 1px solid #ccc;
  z-index: 9999;
  position: fixed;
  display: none;
  list-style: none;
  padding: 0;
}

/* 菜谱项样式设置 */
.context-menu li {
  padding: 5px 10px;
  cursor: pointer;
}

.context-menu li:hover {
  background-color: #f2f2f2;
}

.terminalBox {
  height: 100%;

  /deep/ .ant-layout {
    height: 100%;
  }

  /deep/ .ant-layout-sider-collapsed {
    flex: 0 0 50px !important;
    width: 50px !important;
    max-width: 50px !important;
    min-width: 50px !important;
  }
}

.myTabPane {
  margin: 0 !important;
}

/deep/ .ant-tabs-nav {
  margin: 0 !important;
}

/deep/ .ant-tabs-content {
  height: 100%;
}

.splitpanes {
  background-color: #f8f8f8;
}

/deep/ .splitpanes__splitter {
  background-color: #ccc;
  position: relative;
}

/deep/ .splitpanes__splitter:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  transition: opacity 0.4s;
  background-color: rgba(148, 252, 148, 0.3);
  opacity: 0;
  z-index: 1;
}

/deep/ .splitpanes__splitter:hover:before {
  opacity: 1;
}

/deep/ .splitpanes--vertical > .splitpanes__splitter:before {
  left: -10px;
  right: -10px;
  height: 100%;
}

/deep/ .splitpanes--horizontal > .splitpanes__splitter:before {
  top: -10px;
  bottom: -10px;
  width: 100%;
}

::-webkit-scrollbar {
  width: 2px; /* 滚动条宽度 */
}

/* 滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* 轨道颜色 */
  border-radius: 5px; /* 轨道圆角 */
}

/* 滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #888; /* 滑块颜色 */
  border-radius: 5px; /* 滑块圆角 */
}

/* 滑块hover样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555; /* hover时滑块颜色 */
}
</style>
