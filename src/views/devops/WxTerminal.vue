<template>
  <div :style="termBoxStyle" id="terminalIndexBox" class="terminalIndexBox">
    <p class="optBox" style="z-index: 9999">
      <a-tooltip v-if="termArr.length > 1" placement="bottom">
        <template #title>关闭所有分屏</template>
        <span @click="splitAttrs('full')" class="splitIcon">
          <img src="/vendor-cdn/@img/term/full.png" alt=""/>
        </span>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>四分屏</template>
        <span @click="splitAttrs('four')" class="splitIcon">
          <img src="/vendor-cdn/@img/term/splitFour.png" alt=""/>
        </span>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>左右分屏</template>
        <span @click="splitAttrs('horizon')" class="splitIcon">
          <img style="width: 22px; height: 22px" src="/vendor-cdn/@img/term/splitH.png" alt=""/>
        </span>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>上下分屏</template>
        <span @click="splitAttrs('vertical')" class="splitIcon">
          <img style="width: 22px; height: 22px" src="/vendor-cdn/@img/term/splitV.png" alt=""/>
        </span>
      </a-tooltip>
    </p>
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      @syncCommand="syncCommands"
      v-if="termArr[0].id == 'rootTerm'"
      class="termInstanceBox"
      id="rootTerm"
      ref="rootTerm"
    />
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @syncCommand="syncCommands"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      v-if="termArr[1] && termArr[1].id == 'splitTerm1'"
      class="termInstanceBox"
      id="splitTerm1"
      ref="splitTerm1"
    />
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @syncCommand="syncCommands"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      v-if="termArr[2] && termArr[2].id == 'splitTerm2'"
      class="termInstanceBox"
      id="splitTerm2"
      ref="splitTerm2"
    />
    <Terminal
      @getTermMenuList="getTermMenuList"
      @userConfigUpdate="userConfigUpdate"
      :userConfig="userConfig"
      :termListData="termListData"
      @syncCommand="syncCommands"
      @openGlobalCommand="globalCommandVis = !globalCommandVis"
      v-if="termArr[3] && termArr[3].id == 'splitTerm3'"
      class="termInstanceBox"
      id="splitTerm3"
      ref="splitTerm3"
    />
    <div style="width: 100%" class="globalInput" v-if="globalCommandVis">
      <a-input
        @pressEnter="serverCommand"
        v-model:value="runCommand"
        style="width: calc(100% - 180px); margin-right: 8px"
        placeholder="请输入全局命令"
      />
      <a-button type="primary" @click="serverCommand">全部执行</a-button>
      <a-button @click="globalCommandVis = false" style="margin-left: 8px">关闭</a-button>
    </div>
  </div>
</template>
<script>
import {ref, onMounted} from 'vue'
import Terminal from './Terminal.vue'
import {termConfigList} from '@/api/devops/term'
import {getTermMenu} from '@/api/asset'

export default {
  components: {
    Terminal,
  },
  name: 'TerminalIndex',
  setup() {
    const rootTerm = ref(null)
    const splitTerm1 = ref(null)
    const splitTerm2 = ref(null)
    const splitTerm3 = ref(null)
    const splitDirection = ref('')
    const runCommand = ref('')
    const globalCommandVis = ref(false)
    const termArr = ref([
      {
        type: 'root',
        id: 'rootTerm',
      },
    ])
    const termBoxStyle = ref({})
    const splitAttrs = t => {
      let termInstance = document.getElementsByClassName('termInstanceBox')
      switch (t) {
        case 'full':
          splitDirection.value = 'full'
          termArr.value = termArr.value.slice(0, 1)
          setTimeout(() => {
            for (let i = 0; i < termInstance.length; i++) {
              termInstance[i].style.height = '100%'
              termInstance[i].style.width = '100%'
            }
          })
          break
        case 'four':
          if (splitDirection.value == 'four') return
          splitDirection.value = 'four'
          if (termArr.value.length == 1) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
              flexWrap: 'wrap',
            }
            termArr.value = [
              ...termArr.value,
              {type: 'four', id: 'splitTerm1'},
              {type: 'four', id: 'splitTerm2'},
              {type: 'four', id: 'splitTerm3'},
            ]
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '50%'
              }
            })
          } else if (termArr.value.length == 2) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
              flexWrap: 'wrap',
            }
            termArr.value[1].type = 'four'
            termArr.value = [...termArr.value, {type: 'four', id: 'splitTerm2'}, {type: 'four', id: 'splitTerm3'}]
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '50%'
              }
            })
          }
          break
        case 'horizon':
          if (splitDirection.value == 'horizon') return
          splitDirection.value = 'horizon'
          if (termArr.value.length == 1) {
            termBoxStyle.value = {
              display: 'flex',
            }
            termArr.value.push({type: 'four', id: 'splitTerm1'})
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.width = '50%'
                termInstance[i].style.height = '100% '
              }
            })
          } else if (termArr.value.length == 2) {
            termBoxStyle.value = {
              display: 'flex',
            }
            termArr.value[1].type = 'horizon'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.width = '50%'
                termInstance[i].style.height = '100% '
              }
            })
          } else if (termArr.value.length == 4) {
            termArr.value = termArr.value.slice(0, 2)
            termArr.value[1].type = 'horizon'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.width = '50%'
                termInstance[i].style.height = '100% '
              }
            })
          }
          break
        case 'vertical':
          if (splitDirection.value == 'vertical') return
          splitDirection.value = 'vertical'
          if (termArr.value.length == 1) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
            }
            termArr.value.push({
              type: 'colunm',
              id: String(Math.random() * 99999).split('.')[0],
            })
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '100%'
              }
            })
          } else if (termArr.value.length == 2) {
            termBoxStyle.value = {
              display: 'flex',
              flexDirection: 'column',
            }
            termArr.value[1].type = 'colunm'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '100%'
              }
            })
          } else if (termArr.value.length == 4) {
            termArr.value = termArr.value.slice(0, 2)
            termArr.value[1].type = 'colunm'
            setTimeout(() => {
              for (let i = 0; i < termInstance.length; i++) {
                termInstance[i].style.height = '50%'
                termInstance[i].style.width = '100%'
              }
            })
          }
          break
      }
    }
    const closeItem = i => {
      let index = termArr.value.indexOf(i)
      if (splitDirection.value == 'four') {
        index == 1
          ? termArr.value.splice(2, 1)
          : index == 2
            ? termArr.value.splice(1, 1)
            : termArr.value.splice(index, 1)
      }
    }
    const serverCommand = () => {
      rootTerm.value ? rootTerm.value.executeGlobalCommand(runCommand.value) : ''
      splitTerm1.value ? splitTerm1.value.executeGlobalCommand(runCommand.value) : ''
      splitTerm2.value ? splitTerm2.value.executeGlobalCommand(runCommand.value) : ''
      splitTerm3.value ? splitTerm3.value.executeGlobalCommand(runCommand.value) : ''
      runCommand.value = ''
    }
    const syncCommands = params => {
      rootTerm.value ? rootTerm.value.executeSyncCommand(params) : ''
      splitTerm1.value ? splitTerm1.value.executeSyncCommand(params) : ''
      splitTerm2.value ? splitTerm2.value.executeSyncCommand(params) : ''
      splitTerm3.value ? splitTerm3.value.executeSyncCommand(params) : ''
    }
    const userConfig = ref({})
    let termListData = ref({})
    const userConfigUpdate = () => {
      termConfigList().then(res => {
        if (res.Data.hasOwnProperty('data')) {
          userConfig.value.fontSize = res.Data.data[0].fontSize
          userConfig.value.cursorStyle = res.Data.data[0].cursorStyle
          userConfig.value.scrollBack = res.Data.data[0].scrollBack
          userConfig.value.role = res.Data.data[0].role
        }
      })
    }

    userConfigUpdate()
    const getTermMenuList = () => {
      getTermMenu().then(res => {
        termListData.value = res.Data
      })
    }
    getTermMenuList()
    onMounted(() => {
      let main = document.getElementsByClassName('terminalIndexBox')[0].parentElement
      main.style.margin = 0
      main.style.padding = 0 + 'px'
    })
    return {
      getTermMenuList,
      termListData,
      userConfigUpdate,
      userConfig,
      syncCommands,
      globalCommandVis,
      runCommand,
      splitTerm3,
      splitTerm2,
      splitTerm1,
      rootTerm,
      serverCommand,
      closeItem,
      splitAttrs,
      termArr,
      termBoxStyle,
      splitDirection,
    }
  },
}
</script>
<style lang="less">
#terminalIndexBox {
  height: 100%;
  position: relative;

  .splitIcon {
    margin-left: 8px;
    display: inline-block;
    width: 30px;
    height: 30px;

    img {
      width: 25px;
      height: 25px;
    }
  }

  .optBox {
    position: absolute;
    right: 28px;
    top: 4px;
    display: flex;
  }

  .globalInput {
    position: absolute;
    bottom: 0;
  }
}

.closeaPane {
  margin: 0;
  margin-top: 1px;
  width: 25px;
  height: 25px;
  background-color: #fff;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;

  .closePos {
    width: 45%;
    height: 45%;
    display: inline-block;
    background-color: #ccc;
  }

  .closePos:hover {
    background-color: red;
  }
}
</style>
