<template>
  <div class="termBox">
    <p @click="backTo" class="coverBox" v-if="showMobileHost" style="margin-bottom: 16px">
      <span
        class="svgBox boxShadow"
        :style="{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '18px',
        }"
      >
        <left-outlined/>
      </span>
    </p>
    <p class="search" v-if="!showMobileHost">
      <a-tree-select
        @change="hostSelect"
        @search="hostSearch"
        :fieldNames="{ children: 'children', label: 'title', key: 'key', value: 'key' }"
        v-model:value="value"
        :labelInValue="true"
        show-search
        class="termSearch"
        :style="{ width: isMobile ? '100%' : '' }"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        placeholder="请选择机器"
        allow-clear
        tree-default-expand-all
        :tree-data="myTree"
        tree-node-filter-prop="title"
      ></a-tree-select>
      <span v-if="!isMobile" class="svgBox addBox boxShadow" @click="addFn('folder')">
        <a-tooltip>
          <template #title>新增自定义菜单</template>
          <plus-outlined/>
        </a-tooltip>
      </span>
      <span v-if="!isMobile" class="svgBox addBox boxShadow" style="line-height: 27px" @click="addFn('apply')">
        <a-tooltip>
          <template #title>服务器权限申请</template>
          <img style="width: 14px; height: 14px" src="@/assets/svgs/groupAdd.svg" alt=""/>
        </a-tooltip>
      </span>
      <span v-if="!isMobile" class="svgBox addBox boxShadow" @click="addFn('refresh')">
        <a-tooltip>
          <template #title>刷新列表</template>
          <redo-outlined/>
        </a-tooltip>
      </span>
      <span v-if="!isMobile" class="svgBox addBox boxShadow" @click="addFn('menu')">
        <a-tooltip>
          <template #title>菜单控制</template>
          <unordered-list-outlined/>
        </a-tooltip>
      </span>
    </p>
    <div v-if="!isMobile">
      <a-card class="groupBox" v-for="(item, index) in allGroups" :key="index">
        <template #cover>
          <p class="coverBox">
            <span
              class="svgBox boxShadow"
              :style="{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: customColor[index % customColor.length],
              }"
            >
              <a-icon :type="item.icon"/>
            </span>
            <span style="margin-left: 8px; font-size: 18px; line-height: 38px">
              {{ item.title }}
            </span>
            <a-dropdown placement="bottom">
              <span class="checkOutMore svgBox" style="font-size: 24px; right: 24px">
                <ellipsis-outlined/>
              </span>
              <template #overlay>
                <a-menu style="border-radius: 6px">
                  <a-menu-item>
                    <span
                      v-if="item.children && item.children.length < 6"
                      :disabled="item.children && item.children.length > 5"
                      @click="emitOpt(item, 'trans')"
                      class="svgBox"
                    >
                      <a-tooltip>
                        <template #title>展开</template>
                        <down-outlined/>
                      </a-tooltip>
                    </span>
                    <span
                      v-else
                      :disabled="item.children && item.children.length > 6"
                      @click="emitOpt(item, 'trans')"
                      class="svgBox"
                    >
                      <a-tooltip>
                        <template #title>收起</template>
                        <up-outlined/>
                      </a-tooltip>
                    </span>
                  </a-menu-item>
                  <a-menu-item v-if="item.icon == 'tag-outlined'" @click="emitOpt(item, 'manage')">
                    <span class="svgBox">
                      <a-tooltip>
                        <template #title>管理</template>
                        <apartment-outlined/>
                      </a-tooltip>
                    </span>
                  </a-menu-item>
                  <a-menu-item v-if="item.icon == 'tag-outlined'" @click="emitOpt(item, 'del')">
                    <span class="checkOutMore svgBox">
                      <a-tooltip>
                        <template #title>删除</template>
                        <delete-outlined/>
                      </a-tooltip>
                    </span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </p>
        </template>
        <ul class="hostBox">
          <li @click.stop="emitOpt(i, 'host')" v-for="i in item.children" :key="i.title" class="host boxShadow">
            <p class="etc" style="display: inline-block; width: calc(100% - 30px); font-size: 14px">
              <a-tooltip>
                <template #title>
                  {{
                    i.title.includes('(') ? i.title.split('(')[1].slice(0, i.title.split('(')[1].length - 1) : i.title
                  }}
                </template>
                <!-- {{ i.key }} -->
                {{ i.title.includes('(') ? i.title.split('(')[1].slice(0, i.title.split('(')[1].length - 1) : i.title }}
              </a-tooltip>
            </p>
            <span class="etc" style="font-size: 14px; color: #556370">
              <a-tooltip>
                <template #title>{{ i.title.includes('(') ? i.title.split('(')[0] : i.title }}</template>
                {{ i.title.includes('(') ? i.title.split('(')[0] : i.title }}
              </a-tooltip>
            </span>

            <div @click.stop="collect(i, item)" v-if="item.key != 'setting'">
              <span
                class="addBox editBox1 boxShadow"
                :style="i.favorite ? { display: 'inline-block', boxShadow: 'none' } : ''"
              >
                <star-outlined v-if="!i.favorite"/>
                <star-filled :style="{ color: 'gold' }" v-else/>
              </span>
            </div>
            <div @click.stop="emitOpt(i, 'hostOpt')" v-if="item.key != 'setting'">
              <span class="addBox editBox boxShadow">
                <edit-outlined/>
              </span>
            </div>
          </li>
        </ul>
      </a-card>
    </div>
    <div v-if="isMobile">
      <a-card class="groupBox" v-if="!showMobileHost">
        <template #cover>
          <p class="coverBox">
            <span
              class="svgBox boxShadow"
              :style="{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: '#ADCFFF',
              }"
            >
              <a-icon type="home-outlined"/>
            </span>
            <span style="display: inline-block; line-height: 38px; font-size: 20px; margin-left: 8px">常用TOP5</span>
          </p>
          <ul class="hostBox mobileHost">
            <li
              @click.stop="emitOpt(i, 'host')"
              v-for="i in allGroups[0].children"
              :key="i.title"
              class="host boxShadow mobileGroup"
              style="width: 100%"
            >
              <p class="etc" style="display: inline-block; width: calc(100% - 60px); font-size: 18px">
                <a-tooltip>
                  <template #title>
                    {{
                      i.title.includes('(') ? i.title.split('(')[1].slice(0, i.title.split('(')[1].length - 1) : i.title
                    }}
                  </template>
                  {{
                    i.title.includes('(') ? i.title.split('(')[1].slice(0, i.title.split('(')[1].length - 1) : i.title
                  }}
                </a-tooltip>
              </p>
              <span class="etc" style="font-size: 14px; color: #556370">
                <a-tooltip>
                  <template #title>{{ i.title.includes('(') ? i.title.split('(')[0] : i.title }}</template>
                  {{ i.title.includes('(') ? i.title.split('(')[0] : i.title }}
                </a-tooltip>
              </span>
            </li>
          </ul>
        </template>
      </a-card>
      <a-card class="groupBox" v-if="!showMobileHost">
        <template #cover>
          <p class="coverBox">
            <span
              class="svgBox boxShadow"
              :style="{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }"
            >
              <a-icon type="apartment-outlined"/>
            </span>
            <span style="display: inline-block; line-height: 38px; font-size: 18px; margin-left: 8px">组</span>
          </p>
          <ul class="hostBox mobileHost">
            <li @click="hostInGroup(i)" v-for="(i, index) in allData.data" :key="i.title" class="host-mobile boxShadow">
              <p style="height: 100%; display: flex; align-items: center">
                <span
                  :style="{
                    background: customColor[index % customColor.length],
                  }"
                  class="boxShadow mobileGroupItem"
                >
                  <a-icon :type="i.icon"></a-icon>
                </span>
              </p>

              <p
                class="etc"
                style="
                  width: calc(100% - 60px);
                  font-size: 18px;
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                "
              >
                <span style="display: inline-block; width: 100%">
                  {{ i.title }}
                </span>
                <span class="etc" style="display: inline-block; font-size: 14px; color: #556370">
                  {{ i.children.length }}
                </span>
              </p>
            </li>
          </ul>
        </template>
      </a-card>
      <a-card class="groupBox" v-if="showMobileHost">
        <template #cover>
          <p class="coverBox">
            <span
              class="svgBox boxShadow"
              :style="{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: '#FFC0A0',
              }"
            >
              <a-icon :type="targetGroup.icon"/>
            </span>
            <span style="display: inline-block; line-height: 38px; font-size: 20px; margin-left: 8px">
              {{ targetGroup.title }}
            </span>
          </p>
        </template>
        <ul class="hostBox mobileHost">
          <li
            @click.stop="emitOpt(i, 'host')"
            v-for="i in targetGroup.children"
            :key="i.title"
            class="host boxShadow mobileGroup"
            style="width: 100%"
          >
            <p class="etc" style="display: inline-block; width: calc(100% - 60px); font-size: 18px">
              <a-tooltip>
                <template #title>
                  {{
                    i.title.includes('(') ? i.title.split('(')[1].slice(0, i.title.split('(')[1].length - 1) : i.title
                  }}
                </template>
                {{ i.title.includes('(') ? i.title.split('(')[1].slice(0, i.title.split('(')[1].length - 1) : i.title }}
              </a-tooltip>
            </p>
            <span class="etc" style="font-size: 14px; color: #556370">
              <a-tooltip>
                <template #title>{{ i.title.includes('(') ? i.title.split('(')[0] : i.title }}</template>
                {{ i.title.includes('(') ? i.title.split('(')[0] : i.title }}
              </a-tooltip>
            </span>
          </li>
        </ul>
      </a-card>
    </div>
  </div>
</template>
<script setup>
import {ref, defineProps, defineEmits, onMounted, watch} from 'vue'
import {useRouter} from 'vue-router'
import {collectItem} from '@/api/desktop/desktop'
import {notification} from 'ant-design-vue'
import store from "@/store";
import {createOrder} from "@/api/workflow/order";

const router = useRouter()
const customColor = [
  '#ADCFFF',
  ' #FAF39E',
  '#FFC0A0',
  '#9BE8CF',
  '#C3ABFF',
  ' #FBE49C',
  '#FFA0A0',
  ' #A0E9F5',
  '#A1ECB2',
  '#F9AFF2',
  '#FFD59C',
  '#D0F6AE',
]
const emit = defineEmits(['applyFolder', 'folderConfig', 'refreshMenu', 'menuControl'])
const props = defineProps({
  allGroups: {
    type: Array,
    default: [
      {
        children: [],
      },
    ],
  },
  allData: {
    type: Object,
    default: {
      data: [],
    },
  },
  treeData: {
    type: Array,
    default: [],
  },
  userConfig: {
    type: Object,
    default: () => ({}),
  },
})
let myTree = ref([])
watch(
  props,
  (n, o) => {
    if (n && n.treeData.length) {
      myTree.value = JSON.parse(JSON.stringify(n.treeData))
    } else {
      myTree.value = []
    }
  },
  {deep: true}
)
const showMobileHost = ref(false)
const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
const value = ref('')
const emitOpt = (item, opt) => {
  emit('folderConfig', {item, opt})
}
const collect = (i, item) => {
  const ip = i.title.includes('(') ? i.title.split('(')[0] : i.title
  collectItem({
    ip,
    status: i.favorite ? '2' : '1',
  })
    .then(res => {
      i.favorite = !i.favorite
      emit('folderConfig', {item: i, opt: 'collect'})
    })
    .catch(err => {
    })
}
//按钮事件
const addFn = type => {
  switch (type) {
    case 'apply': //申请服务器
      let routeUrl = router.resolve({path: '/workflow/server-permission-operation'})
      window.open(routeUrl.href, '_blank')
      break
    //somecode
    case 'folder': //新建文件夹
      emit('applyFolder')
      //somecode
      break
    case 'refresh':
      emit('refreshMenu')
      break
    case 'menu':
      emit('menuControl')
      break
  }
}

const searchTreeData = (data, searchText) => {
  // 如果搜索内容为空，返回原数据
  if (!searchText) return data
  // 深拷贝原数据，避免修改原数组
  const result = JSON.parse(JSON.stringify(data))
  // 递归过滤
  return result.filter(item => {
    // 如果当前项标题包含搜索内容，保留整个分组
    if (item.title.includes(searchText)) {
      return true
    }
    // 检查子项
    if (item.children && item.children.length > 0) {
      // 过滤子项
      item.children = item.children.filter(child => child.title.includes(searchText))

      // 如果过滤后子项不为空，保留该分组
      return item.children.length > 0
    }
    // 默认不保留
    return false
  })
}
const isValidIPv4 = str => {
  const regex = /^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)$/
  return regex.test(str)
}

const lFlag = ref(false)
const hostSearch = v => {
  // debugger
  if (lFlag.value) {
    myTree.value.pop()
    lFlag.value = false
  }
  // 模拟搜索匹配的逻辑，
  const matchedOptions = searchTreeData(props.treeData, v)
  if (matchedOptions.length == 0) {
    if (isValidIPv4(`${v}`)) {
      myTree.value = []
      myTree.value.push({
        key: '无匹配机器，请申请服务器权限',
        title: `${v}无匹配机器，点击申请服务器权限`,
      })
      lFlag.value = true
    } else {
      myTree.value = []
      myTree.value.push({
        key: '输入的ip地址不合法',
        title: `${v}(输入的ip地址不合法，请重新输入)`,
      })
    }
  } else {
    myTree.value = matchedOptions
  }
}

// 搜索选中
const hostSelect = (v, label) => {
  if (v.value == '无匹配机器，请申请服务器权限') {
    let ip = v.label.replace(/无匹配机器，点击申请服务器权限/g, '')
    let userRole = props.userConfig.role
    myTree.value.pop()
    if (userRole == 'admin') {
      const now = new Date()
      let time_now = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`
      let temp = {
        id: '',
        orderType: '服务器管理员授权',
        node: 1,
        status: 1,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: store.getters.email,
        handlerEmail: store.getters.email,
        content: {
          hostList: ip,
          role: 'root',
          isExpired: 'temporary',
          validTime: '28800',
          time: '8',
        },
        timeline: [store.getters.name + '(' + store.getters.email + ') 创建工单 ' + time_now],
        comment: '',
      }
      createOrder(temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '工单创建失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          notification.success({
            message: '服务器管理员授权工单创建成功',
            description: '请在合小云点击确认,确认后点击右侧刷新刷新按钮',
          })
        }
      })
    } else {
      let routeUrl = router.resolve({path: '/workflow/server-permission-operation'})
      window.open(routeUrl.href, '_blank')
    }
    value.value = []
  } else if (v.value == '输入的ip地址不合法') {
    myTree.value.pop()
    value.value = []
    notification.error({
      message: v.value,
      description: v.label,
    })
  } else {
    let flag = true
    for (let i = 0; i < props.treeData.length; i++) {
      if (v.label == props.treeData[i].title && v.value == props.treeData[i].key) {
        flag = false
        value.value = []
        break
      }
    }
    if (flag) {
      if (v) emit('folderConfig', {item: v, opt: 'hostSearch'})
    }
  }
}
const targetGroup = ref({})
// 移动端点击组，出现组内host
const hostInGroup = hosts => {
  showMobileHost.value = true
  targetGroup.value = hosts
}
const backTo = () => {
  showMobileHost.value = false
}
onMounted(() => {
  if (isMobile) {
    let main = document.getElementsByClassName('terminalBox')[0].parentElement
    main.style.margin = 0
    let header = document.getElementsByTagName('header')[0]
    header.style.display = 'none'
  }
})
</script>

<style scoped lang="less">
/deep/ .ant-layout-conten {
  margin: 0 !important;
}

p {
  margin: 0;
  padding: 0;
}

.etc {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.boxShadow {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  border: none !important;
}

.menuItem {
  padding: 0 !important;
}

.svgBox {
  width: 38px;
  height: 38px;
  display: flex;
  border-radius: 6px;
  font-size: 20px;
  // line-height: 38px;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #556370;
}

.addBox {
  display: inline-block;
  text-align: center;
  float: right;
  margin-left: 8px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 18px;
  background-color: #ffffff;
}

.editBox {
  margin: 0;
  position: absolute;
  right: 8px;
  display: none;
  width: 26px;
  height: 26px;
  line-height: 26px;
  top: 75%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #556370;
}

.editBox1 {
  margin: 0;
  position: absolute;
  right: 8px;
  display: none;
  width: 26px;
  height: 26px;
  line-height: 26px;
  top: 25%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #556370;
}

.addBox:hover {
  background: rgba(245, 247, 250, 1);
}

.svgBox:hover {
  background: rgba(245, 247, 250, 1);
}

.termBox {
  background-color: #f5f7fa;
  margin-bottom: 15px;
  padding: 14px;

  .search {
    margin: 0;
    padding: 0;
    width: 100%;
    margin-bottom: 16px;

    .termSearch {
      border: 0;
      width: calc(100% - 160px);
      display: inline-block;
    }
  }

  .groupBox {
    background-color: #f5f7fa;
    border: 0 !important;
    margin-bottom: 16px;

    /deep/ .ant-card-meta-title {
      font-size: 14px !important;
      line-height: 24px;
    }

    /deep/ .ant-card-meta-avatar {
      padding-right: 8px !important;
    }

    .coverBox {
      display: flex;
      // padding: 24px;
      width: 100%;
      position: relative;
      margin-bottom: 0;
      padding-bottom: 0;

      /deep/ .ant-dropdown-menu-item {
        padding: 0 !important;
      }
    }

    .checkOutMore {
      position: absolute;
      right: 5px;
    }
  }
}

.hostBox {
  padding-left: 0px !important;
  display: grid;
  grid-template-columns: repeat(5, 20%);
  grid-row-gap: 24px;
  justify-items: start;
  margin-top: 16px;

  .host {
    cursor: pointer;
    display: inline-grid;
    width: calc(100% - 20px);
    height: 80px;
    padding: 8px 12px;
    position: relative;
    background-color: #fff;
    // margin-right: 20px;
  }

  .host:hover {
    background-color: #e6eaf0;

    .editBox {
      display: inline-block;
    }

    .editBox1 {
      display: inline-block;
    }
  }
}

.mobileHost {
  grid-template-columns: repeat(1, 100%);
}

.groupBox {
  /deep/ .ant-card-body {
    padding: 0 !important;
  }
}

.mobileGroupItem {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  margin: 0;
  width: 36px;
  height: 36px;
  font-size: 20px;
  color: #556370;
  margin-right: 16px;
}

.host-mobile {
  cursor: pointer;
  display: flex;
  width: 100%;
  height: 80px;
  padding: 8px 20px;
  background-color: #fff;
  align-items: center;
  // margin-right: 20px;
}
</style>
