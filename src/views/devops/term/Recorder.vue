<template>
  <a-card v-show="show" :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="IP">
              <a-input v-model:value="queryParam.ip" placeholder="IP" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="用户名">
              <a-input v-model:value="queryParam.user" placeholder="用户名" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="URL" :span="3">{{ record.url }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <a @click="handlePlayer(record.url)">回放</a>
        </template>
        <template v-else-if="column.dataIndex == 'platform'">
          <a-tag v-if="text === 'web'" color="#2f54eb">Web端</a-tag>
          <a-tag v-else-if="text === 'qw'" color="#52c41a">企业微信</a-tag>
          <a-tag v-else-if="text === 'phone'" color="#eb2f96">移动端</a-tag>
          <a-tag v-else color="#fa8c16">{{ text }}</a-tag>
        </template>
      </template>
    </s-table>
  </a-card>
  <div :id="id"></div>
</template>

<script>
import '@/styles/asciinema-player.css'
import { STable, Ellipsis } from '@/components'
import { termRecList } from '@/api/devops/term'
import * as AsciinemaPlayer from 'asciinema-player'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '用户名',
    dataIndex: 'user',
  },
  {
    title: '日期',
    dataIndex: 'date',
  },
  {
    title: '来源IP',
    dataIndex: 'remoteAddr',
  },
  {
    title: '访问方式',
    dataIndex: 'platform',
    scopedSlots: { customRender: 'platform' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Recorder',
  components: {
    STable,
    Ellipsis,
  },
  props: {
    id: String,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      player: null,
      show: true,
      queryParam: {},
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return termRecList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  methods: {
    handlePlayer(url) {
      this.show = false
      this.player = AsciinemaPlayer.create(url, document.getElementById(this.id))
    },
  },
}
</script>
