<template>
  <a-row :gutter="24" class="cards">
    <a-col :span="8">
      <a-statistic title="用户总数" :value="totalUser" />
    </a-col>
    <a-col :span="8">
      <a-statistic title="今日活跃用户" :value="activeUser" />
    </a-col>
    <a-col :span="8">
      <a-statistic title="今日活跃资产" :value="activeHost" />
    </a-col>
  </a-row>
  <a-row style="margin-bottom: 15px; height: 50%; margin-right: 40px" type="flex" justify="space-between">
    <a-col :span="24">
      <a-card title="云终端用户使用情况">
        <div :id="chartId1"></div>
      </a-card>
    </a-col>
  </a-row>
  <a-row style="margin-bottom: 15px; height: 50%; margin-right: 40px" type="flex" justify="space-between">
    <a-col :span="24">
      <a-card title="月用户top15">
        <div :id="chartId2"></div>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { Area } from '@antv/g2plot'
import { Column } from '@antv/g2plot'
import { getTermDashboard } from '@/api/devops/term'

export default {
  name: 'TermDashboard',
  props: {
    chartId1: String,
    chartId2: String,
  },
  data() {
    return {
      chart1: null,
      chart2: null,
      totalUser: 0,
      activeUser: 0,
      activeHost: 0,
      userData: [],
      userTop: [],
    }
  },
  mounted() {
    this.getDashboard()
  },
  methods: {
    getDashboard() {
      getTermDashboard().then(res => {
        this.totalUser = res.Data.totalUser
        this.activeUser = res.Data.activeUser
        this.activeHost = res.Data.activeHost
        this.userData = res.Data.userData
        this.userTop = res.Data.userTop

        this.chart1 = new Area(this.chartId1, {
          data: this.userData,
          xField: 'date',
          yField: 'total',
          xAxis: {
            range: [0, 1],
          },
          meta: {
            date: {
              alias: '日期',
            },
            total: {
              alias: '连接次数',
            },
          },
          height: 200,
          smooth: true,
        })
        this.chart1.render()

        this.chart2 = new Column(this.chartId2, {
          data: this.userTop,
          xField: 'user',
          yField: 'total',
          xAxis: {
            label: {
              autoHide: true,
              autoRotate: false,
            },
          },
          meta: {
            user: {
              alias: '用户名',
            },
            total: {
              alias: '总计',
            },
          },
          height: 200,
          minColumnWidth: 20,
          maxColumnWidth: 20,
        })
        this.chart2.render()
      })
    },
  },
}
</script>

<style scoped lang="less">
.cards {
  margin-bottom: 15px;
  margin-top: 20px;
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  white-space: nowrap;
  display: flex;
  background-color: white;
}
.chartContainer {
  width: 100%;
  height: 100%;
}
</style>
