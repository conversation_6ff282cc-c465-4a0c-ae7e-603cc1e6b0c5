<template>
  <a-card :bordered="false">
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'scrollBack'">
          <span>{{ record.scrollBack }}行</span>
        </template>
        <template v-if="column.dataIndex === 'aliasStatus'">
          <a-switch
            :checked="record.aliasStatus === 1"
            checked-children="开"
            un-checked-children="关"
            disabled
            class="custom-switch"
          />
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a @click="handleUpdate(record)">更新</a>
        </template>
      </template>
    </s-table>

    <a-modal
      title="更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      width="60%"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="终端中回滚量" name="scrollBack">
          <a-input-number v-model:value="form.scrollBack" addon-after="行" />
        </a-form-model-item>
        <!--        <a-form-model-item label="格式化文本编辑器" name="scrollBack">-->
        <!--          <a-radio-group-->
        <!--            v-model:value="form.vimStatus"-->
        <!--            :default-value="false"-->
        <!--            button-style="solid"-->
        <!--          >-->
        <!--            <a-radio-button :value="true">开启</a-radio-button>-->
        <!--            <a-radio-button :value="false">关闭</a-radio-button>-->
        <!--          </a-radio-group>-->
        <!--          <div v-if="form.vimStatus">-->
        <!--            <a-form-model-->
        <!--              class="mt-4"-->
        <!--              labelAlign="left"-->
        <!--              layout="horizontal"-->
        <!--              :label-col="{ span: 4 }"-->
        <!--              :wrapper-col="{ span: 15 }"-->
        <!--              :model="form"-->
        <!--            >-->
        <!--        <a-form-model-item label="命令行打开">-->
        <!--          <a-switch-->
        <!--            :checked="form.quickVimStatus === 1"-->
        <!--            @change="handleSwitchChange"-->
        <!--            checked-children="开"-->
        <!--            un-checked-children="关"-->
        <!--          />-->
        <!--        </a-form-model-item>-->
        <!--              <a-form-model-item>-->
        <!--                <template v-slot:label>-->
        <!--                  文件管理器插件-->
        <!--                  <a-tooltip>-->
        <!--                    <template #title>安装后通过 "鼠标右键-文件管理器" 打开</template>-->
        <!--                    <a-icon type="question-circle" />-->
        <!--                  </a-tooltip>-->
        <!--                </template>-->

        <!--                <div class="status-display">-->
        <!--                  <a-badge :status="form.commonVimStatus === 1 ? 'success' : 'default'"-->
        <!--                           :text="form.commonVimStatus === 1 ? '已安装' : '未安装'" />-->
        <!--                  <a-button-->
        <!--                    type="primary"-->
        <!--                    size="small"-->
        <!--                    style="margin-left: 10px"-->
        <!--                    :danger="form.commonVimStatus === 1"-->
        <!--                    :loading="isProcessing"-->
        <!--                    @click="handleAction"-->
        <!--                  >-->
        <!--                    {{ isProcessing ? (form.commonVimStatus === 1 ? '卸载中...' : '安装中...') : (form.commonVimStatus === 1 ? '卸载' : '安装') }}-->
        <!--                  </a-button>-->
        <!--                </div>-->
        <!--              </a-form-model-item>-->
        <!--            </a-form-model>-->
        <!--          </div>-->
        <!--        </a-form-model-item>-->
        <!--        <a-form-model-item label="全局Alias" name="aliasStatus">-->
        <!--          <a-switch-->
        <!--            v-model:checked="form.aliasSwitch"-->
        <!--            checked-children="开"-->
        <!--            un-checked-children="关"-->
        <!--            @change="checked => changeMonitorSwitch(checked, form)"-->
        <!--          />-->
        <!--        </a-form-model-item>-->
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { getTermConfigInfo, updateTermConfig, termConfigList } from '@/api/devops/term'
import { aliasUpdate } from '@/api/devops/term-proxy'
import { getCloudTermKey } from '@/config'
import CryptoJS from 'crypto-js'

const columns = [
  {
    title: '用户名',
    dataIndex: 'user',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '终端中回滚量',
    dataIndex: 'scrollBack',
    scopedSlots: { customRender: 'scrollBack' },
  },
  {
    title: '全局Alias',
    dataIndex: 'aliasStatus',
    scopedSlots: { customRender: 'aliasStatus' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'TermConfig',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 20 },
      advanced: false,
      isProcessing: false,
      form: {
        user: undefined,
        email: undefined,
        scrollBack: 100,
        vimStatus: false,
        quickVimStatus: false,
        commonVimStatus: false,
        aliasSwitch: 2,
      },
      updateVisible: false,
      confirmLoading: false,
      switchLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter)
        return termConfigList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        user: [{ required: true, message: '请填写用户名', trigger: 'change' }],
        email: [{ required: true, message: '请填写邮箱', trigger: 'change' }],
        scrollBack: [{ required: true, message: '请填写终端中回滚量行数', trigger: 'change' }],
        aliasStatus: [{ required: true, message: '请选择是否开启全局Alias', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleAction() {
      if (this.isProcessing) return
      this.isProcessing = true
      setTimeout(() => {
        if (this.form.commonVimStatus === 1) {
          this.uninstall()
        } else {
          this.install()
        }
        this.isProcessing = false
      }, 1000)
    },

    install() {
      this.form.commonVimStatus = 1
      this.updateTermVimConfig()
    },
    uninstall() {
      this.form.commonVimStatus = 2
      this.updateTermVimConfig()
    },
    updateTermVimConfig() {
      updateTermConfig(this.form).then(res => {
        if (res === undefined) {
          this.$message.error('安装失败,后端接口错误，请联系运维开发排查~')
        }
      })
    },

    handleSwitchChange(value) {
      this.form.quickVimStatus = value ? 1 : 2
    },
    changeMonitorSwitch(checked, record) {
      if (checked) {
        this.form.aliasStatus = 1
      } else {
        this.form.aliasStatus = 2
      }
    },
    handleUpdate(record) {
      getTermConfigInfo(record.id).then(res => {
        this.form = res.Data
        this.form.aliasSwitch = this.form.aliasStatus === 1
        this.form.vimStatus = true
        if (this.form.quickVimStatus === -1 || (this.form.commonVimStatus === 2 && this.form.quickVimStatus === 2)) {
          this.form.vimStatus = false
        }
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      if (this.form.aliasSwitch) {
        this.form.aliasStatus = 1
      } else {
        this.form.aliasStatus = 2
      }
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          if (this.form.vimStatus === false) {
            this.form.quickVimStatus = 2
            this.form.commonVimStatus = 2
          }
          updateTermConfig(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
              this.$emit('userConfigUpdate')
            }
            this.update(this.form.aliasStatus === 1)
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    update(status) {
      let authData = {
        email: this.form.email,
        status: status,
      }
      const auth = decodeURIComponent(this.encrypt(authData))
      aliasUpdate({ data: auth }).then(response => {
        if (response) {
          console.log(response)
        }
      })
    },
    encrypt(authData) {
      let keyStr = getCloudTermKey()
      let ivStr = keyStr
      let key = CryptoJS.enc.Utf8.parse(keyStr)
      let iv = CryptoJS.enc.Utf8.parse(ivStr)
      let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(authData))
      let encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      })
      return encodeURIComponent(encrypted.toString())
    },
  },
}
</script>
