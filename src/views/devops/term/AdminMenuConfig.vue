<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="邮箱">
              <a-input
                v-model:value="queryParam.email"
                placeholder="用户邮箱(可以模糊查询)"
                @pressEnter="$refs.table.refresh()"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <a @click="handleUpdate(record)">更新</a>
        </template>
      </template>
    </s-table>

    <a-modal
      title="更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      width="60%"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="菜单" name="menu">
          <a-textarea v-model:value="form.menu" :auto-size="{ minRows: 3 }" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { termConfigAdminList, getTermConfigInfo, updateTermConfig } from '@/api/devops/term'

const columns = [
  {
    title: '用户名',
    dataIndex: 'user',
  },
  {
    title: '自定义菜单',
    dataIndex: 'menu',
    width: '550px',
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'AdminMenuConfig',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 20 },
      queryParam: {},
      advanced: false,
      form: {
        user: undefined,
        email: undefined,
        fontSize: 16,
      },
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return termConfigAdminList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        user: [{ required: true, message: '请填写用户名', trigger: 'change' }],
        email: [{ required: true, message: '请填写邮箱', trigger: 'change' }],
        menu: [{ required: true, message: '请填写字体大小', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleUpdate(record) {
      getTermConfigInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateTermConfig(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
  },
}
</script>
