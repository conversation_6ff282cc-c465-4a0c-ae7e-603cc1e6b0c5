<template>
  <a-card :bordered="false">
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="ID">{{ record.id }}</a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">{{ record.comment }}</a-descriptions-item>
          <a-descriptions-item v-if="record.authModel === 'publicKey'" label="密钥(私钥)" :span="3">
            {{ record.privateKey }}
          </a-descriptions-item>
          <a-descriptions-item v-if="record.authModel === 'publicKey'" label="密钥(User私钥)" :span="3">
            {{ record.userPrivateKey }}
          </a-descriptions-item>
          <a-descriptions-item v-if="record.authModel === 'password'" label="密码" :span="3">
            {{ record.pwd }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'authModel'">
          <a-badge :status="authModelTypeFilter(text)" :text="authModelFilter(text)" />
        </template>
        <template v-else-if="column.dataIndex == 'action'">
          <a @click="handleUpdate(record)">更新</a>
        </template>
      </template>
    </s-table>

    <a-modal
      title="更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      width="60%"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="认证方式" name="authModel">
          <a-radio-group v-model:value="form.authModel" button-style="solid">
            <a-radio-button value="password">密码认证</a-radio-button>
            <a-radio-button value="publicKey">密钥认证</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item v-if="form.authModel === 'password'" label="密码" name="pwd">
          <a-input-password v-model:value="form.pwd" />
        </a-form-model-item>
        <a-form-model-item v-if="form.authModel === 'publicKey'" label="密钥(私钥)" name="privateKey">
          <a-textarea v-model:value="form.privateKey" :auto-size="{ minRows: 6 }" />
        </a-form-model-item>
        <a-form-model-item v-if="form.authModel === 'publicKey'" label="密钥(公钥)" name="publicKey">
          <a-textarea v-model:value="form.publicKey" :auto-size="{ minRows: 3 }" />
        </a-form-model-item>
        <a-form-model-item v-if="form.authModel === 'publicKey'" label="密钥(User私钥)" name="privateKey">
          <a-textarea v-model:value="form.userPrivateKey" :auto-size="{ minRows: 6 }" />
        </a-form-model-item>
        <a-form-model-item v-if="form.authModel === 'publicKey'" label="密钥(User公钥)" name="publicKey">
          <a-textarea v-model:value="form.userPublicKey" :auto-size="{ minRows: 3 }" />
        </a-form-model-item>
        <a-form-model-item label="备注" name="comment">
          <a-input v-model:value="form.comment" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { termConfigList, getTermConfigInfo, updateTermConfig } from '@/api/devops/term'

const columns = [
  {
    title: '用户名',
    dataIndex: 'user',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '认证方式',
    dataIndex: 'authModel',
    scopedSlots: { customRender: 'authModel' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '80px',
    align: 'center',
  },
]

const authModel = {
  password: {
    status: 'processing',
    text: '密码认证',
  },
  publicKey: {
    status: 'success',
    text: '密钥认证',
  },
}

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'ConnConfig',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 20 },
      queryParam: {},
      form: {
        user: undefined,
        email: undefined,
        authModel: undefined,
        pwd: undefined,
        privateKey: undefined,
        publicKey: undefined,
        userPrivateKey: undefined,
        userPublicKey: undefined,
        comment: undefined,
      },
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return termConfigList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        user: [{ required: true, message: '请填写用户名', trigger: 'change' }],
        email: [{ required: true, message: '请填写邮箱', trigger: 'change' }],
        authModel: [{ required: true, message: '请选择认证方式', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleUpdate(record) {
      getTermConfigInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateTermConfig(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
              this.$emit('userConfigUpdate')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    authModelFilter(type) {
      return authModel[type]?.text || type
    },
    authModelTypeFilter(type) {
      return authModel[type]?.status || type
    },
  },
}
</script>
