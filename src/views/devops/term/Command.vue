<template>
  <a-card v-show="show" :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="IP">
              <a-input v-model:value="queryParam.ip" placeholder="IP" @pressEnter="$refs.table.refresh()" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="用户名">
              <a-input
                v-model:value="queryParam.user"
                placeholder="用户名"
                @pressEnter="$refs.table.refresh()"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="输入">
              <a-input
                v-model:value="queryParam.input"
                placeholder="输入命令(可以模糊查询)"
                @pressEnter="$refs.table.refresh()"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 4) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #expandedRowRender="{ record }">
        <a-descriptions>
          <a-descriptions-item label="SessionID" :span="2">{{ record.sessionID }}</a-descriptions-item>
          <a-descriptions-item label="邮箱">{{ record.email }}</a-descriptions-item>
          <a-descriptions-item label="输入" :span="3">{{ record.input }}</a-descriptions-item>
          <a-descriptions-item label="输出" :span="3">{{ record.output }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ record.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ record.updatedAt }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { termCommandList } from '@/api/devops/term'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '用户名',
    dataIndex: 'user',
  },
  {
    title: '输入',
    dataIndex: 'input',
  },
  {
    title: '时间',
    dataIndex: 'timestamp',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Command',
  components: {
    STable,
    Ellipsis,
  },
  props: {
    id: String,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      player: null,
      show: true,
      queryParam: {},
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return termCommandList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
}
</script>
