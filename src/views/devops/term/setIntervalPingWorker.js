/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-10-09 15:35:43
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-10-09 15:36:01
 * @FilePath: \cloud_web\src\views\devops\term\setIntervalPingWorker.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
let pingInterval = null

self.onmessage = function (event) {
  if (event.data.command === 'startPing') {
    if (!pingInterval) {
      pingInterval = setInterval(() => {
        self.postMessage('ping') // 每5秒通知主线程发送Ping
      }, 5000)
    }
  } else if (event.data.command === 'stopPing' && pingInterval) {
    clearInterval(pingInterval)
    pingInterval = null
  }
}
