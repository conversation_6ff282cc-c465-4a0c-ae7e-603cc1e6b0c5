<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="用户">
              <a-input
                v-model:value="queryParam.email"
                placeholder="模糊查询"
                @pressEnter="$refs.table.refresh()"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
      class="pluginsConfig"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'AutoCompletion'">
          <a-switch v-model:checked="record.AutoCompletion" checked-children="开" un-checked-children="关" @change="pluginsChange(record,'AutoCompletion')"/>
        </template>
        <template v-if="column.dataIndex === 'Highlight'">
          <a-switch v-model:checked="record.Highlight" checked-children="开" un-checked-children="关" @change="pluginsChange(record,'Highlight')"/>
        </template>
        <template v-if="column.dataIndex === 'EditFiles'">
          <a-switch v-model:checked="record.EditFiles" checked-children="开" un-checked-children="关" @change="pluginsChange(record,'EditFiles')"/>
        </template>
        <template v-if="column.dataIndex === 'GlobalAlias'">
          <a-switch v-model:checked="record.GlobalAlias" checked-children="开" un-checked-children="关" @change="pluginsChange(record,'GlobalAlias')"/>
        </template>
      </template>
    </s-table>

    <a-modal
      title="更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      width="60%"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="终端中回滚量" name="scrollBack">
          <a-input-number v-model:value="form.scrollBack" addon-after="行" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { getTermConfigInfo, updateTermConfig, termConfigAdminList,updateUserPlugin } from '@/api/devops/term'

const columns = [
  {
    title: '用户',
    dataIndex: 'email',
  },
  {
    title: '插件类型',
    children: [
    {
    title: '自动补全',
    dataIndex: 'AutoCompletion',
    align: 'center',
  },
  {
    title: '语法高亮',
    dataIndex: 'Highlight',
    align: 'center',
  },
  {
    title: '文件格式化',
    dataIndex: 'EditFiles',
    align: 'center',
  },
  {
    title: '全局Alias',
    dataIndex: 'GlobalAlias',
    align: 'center',
  },
    ],
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'AdminPluginsSetting',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 20 },
      queryParam: {},
      advanced: false,
      form: {
        user: undefined,
        email: undefined,
        scrollBack: 100,
      },
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return termConfigAdminList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
            } else {
              for (let i = 0; i < res.Data.data.length; i++) {
                const item = res.Data.data[i]
                const pluginFeatures = ['AutoCompletion', 'Highlight', 'EditFiles', 'GlobalAlias']

                pluginFeatures.forEach(feature => {
                  item[feature] = false
                })

                if (item.plugins) {
                  pluginFeatures.forEach(feature => {
                    if (item.plugins.includes(feature)) {
                      item[feature] = true
                    }
                  })
                }
              }
              return res.Data
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        user: [{ required: true, message: '请填写用户名', trigger: 'change' }],
        email: [{ required: true, message: '请填写邮箱', trigger: 'change' }],
        scrollBack: [{ required: true, message: '请填写终端中回滚量行数', trigger: 'change' }],
      },
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    handleUpdate(record) {
      getTermConfigInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateTermConfig(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
              this.$emit('userConfigUpdate')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    pluginsChange(record, value){
      let actionPlugin = ''
      if (value === 'AutoCompletion') {
        actionPlugin= record.AutoCompletion ? 'install' : 'uninstall'
      } else if (value === 'Highlight') {
        actionPlugin=record.Highlight ? 'install' : 'uninstall'
      } else if (value === 'EditFiles') {
        actionPlugin= record.EditFiles ? 'install' : 'uninstall'
      } else if (value === 'GlobalAlias') {
        actionPlugin= record.GlobalAlias ? 'install' : 'uninstall'
      }

      updateUserPlugin({email:record.email,plugin:value,action:actionPlugin}).then(res => {
        console.log(res)
        if (res.Data.message !== 'ok') {
          this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
        } else {
          this.$message.success('更新成功')
        }
      })
    }
  },
}
</script>

<style lang="less" scoped>
.pluginsConfig {
  /deep/ .ant-table-thead > tr > th {
    background: #fafafa;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 800;
    position: relative;
    text-align: center;
    transition: background 0.3s ease;
    border: 2px solid #f7f5f5;
    padding-top: 8px;
    padding-bottom: 8px;
    line-height: 1.2;
    height: 30px; /* You can adjust this value as needed */
  }
}
</style>
