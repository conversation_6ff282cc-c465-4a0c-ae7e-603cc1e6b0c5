<template>
  <a-space>
    <!--    <a-tooltip title="IP和备注标签信息" color="blue">IP: {{ content }}</a-tooltip>-->
    <div class="monitor-footer" v-if="!monitorStatus">
      <a-tooltip title="请联系AIM运维处理~" color="orange">
        <alert-outlined />
        <span>&nbsp该机器监控数据获取异常！</span>
      </a-tooltip>
    </div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <clock-circle-outlined />
        <a-tooltip title="系统运行时间" color="orange">
          <span>&nbsp{{ info.bootTime }}</span>
        </a-tooltip>
      </a>
    </div>
    <div class="monitor-footer" v-if="monitorStatus"></div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <svg
          id="ot-cpu-icon"
          class="ruyi-icon ruyi-icon-ot-cpu-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
        >
          <g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
            <path d="M6 7a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H7a1 1 0 01-1-1V7Zm1 0h2v2H7V7Z"></path>
            <path
              d="M9 1v2H7V1H6v2H4a1 1 0 00-1 1v2H1v1h2v2H1v1h2v2a1 1 0 001 1h2v2h1v-2h2v2h1v-2h2a1 1 0 001-1v-2h2V9h-2V7h2V6h-2V4a1 1 0 00-1-1h-2V1H9ZM4 4h8v8H4V4Z"
            ></path>
          </g>
        </svg>
        <a-tooltip title="CPU使用率" color="orange">
          <span class="">&nbsp{{ info.cpuUsed }}</span>
        </a-tooltip>
      </a>
    </div>
    <div class="monitor-footer" v-if="monitorStatus"></div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <svg
          id="ot-cpu-icon"
          class="ruyi-icon ruyi-icon-ot-cpu-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
        >
          <g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
            <path d="M6 7a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H7a1 1 0 01-1-1V7Zm1 0h2v2H7V7Z"></path>
            <path
              d="M9 1v2H7V1H6v2H4a1 1 0 00-1 1v2H1v1h2v2H1v1h2v2a1 1 0 001 1h2v2h1v-2h2v2h1v-2h2a1 1 0 001-1v-2h2V9h-2V7h2V6h-2V4a1 1 0 00-1-1h-2V1H9ZM4 4h8v8H4V4Z"
            ></path>
          </g>
        </svg>
        <a-tooltip title="1分钟系统平均负载(cpu load)" color="orange">
          <span class="">&nbsp{{ info.cpuLoad }}</span>
        </a-tooltip>
      </a>
    </div>
    <div class="monitor-footer" v-if="monitorStatus"></div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <svg
          id="ot-storage-icon"
          class="ruyi-icon ruyi-icon-ot-storage-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
        >
          <g fill="currentColor">
            <path d="M11.5 7h-7V6h7v1Z"></path>
            <path
              d="M2 3a1 1 0 00-1 1v8.5a1 1 0 001 1h12a1 1 0 001-1V4a1 1 0 00-1-1H2Zm12 1H2v8.5h2.5V10h1v2.5h2V10h1v2.5h2V10h1v2.5H14V4Z"
              fill-rule="evenodd"
              clip-rule="evenodd"
            ></path>
          </g>
        </svg>
        <a-tooltip title="内存使用" color="orange">
          <span>&nbsp{{ info.memoryUsed }}({{ info.memoryUsedPercent }})</span>
        </a-tooltip>
      </a>
    </div>
    <div class="monitor-footer" v-if="monitorStatus"></div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <stock-outlined />
        <a-tooltip title="TCP活跃连接数" color="orange">
          <span>&nbsp{{ info.tcpActive }}</span>
        </a-tooltip>
      </a>
    </div>
    <div class="monitor-footer" v-if="monitorStatus"></div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <svg
          id="ot-network-icon"
          class="ruyi-icon ruyi-icon-ot-network-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
        >
          <path
            fill="currentColor"
            d="M7.937 15a7 7 0 11.127-14 7 7 0 01-.127 14ZM6.56 2.174a6.004 6.004 0 00-4.54 5.328h3.531c.037-1.634.25-3.12.587-4.24.121-.404.261-.772.422-1.088Zm2.87-.002c.162.316.303.686.424 1.09.336 1.12.55 2.606.586 4.24h3.54a6.004 6.004 0 00-4.55-5.33Zm.01 5.33c-.036-1.558-.24-2.942-.544-3.953-.169-.562-.36-.98-.549-1.248-.198-.28-.324-.3-.351-.3-.027 0-.153.02-.351.3-.19.267-.38.686-.549 1.248-.303 1.01-.508 2.395-.544 3.953H9.44Zm-2.888 1H9.44c-.036 1.557-.24 2.941-.544 3.952-.169.563-.36.981-.549 1.248-.174.246-.292.291-.337.299h-.027c-.045-.008-.163-.054-.337-.3-.19-.266-.38-.684-.549-1.247-.303-1.01-.508-2.395-.544-3.952Zm-1 0H2.02a6.005 6.005 0 004.539 5.325c-.16-.316-.3-.683-.42-1.085-.337-1.122-.551-2.607-.588-4.24Zm3.88 5.326a6.004 6.004 0 004.547-5.326H10.44c-.036 1.633-.25 3.118-.586 4.24-.121.402-.261.77-.422 1.086Z"
            fill-rule="evenodd"
            clip-rule="evenodd"
          ></path>
        </svg>
        <a-tooltip title="所有网卡下载总流量" color="orange">
          <span>&nbsp{{ info.networkIn }}</span>
          <svg
            id="ot-status-arrow"
            class="ruyi-icon ot-status-arrow-up ruyi-icon-ot-status-arrow"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
          >
            <path
              fill="currentColor"
              d="M7.5 3.914 3.854 7.561l-.707-.707 4.641-4.642a.3.3 0 01.424 0l4.642 4.642-.707.707L8.5 3.914V14h-1V3.914Z"
            ></path>
          </svg>
        </a-tooltip>
        <a-tooltip title="所有网卡上传总流量" color="orange">
          <span>&nbsp{{ info.networkOut }}</span>
          <svg
            id="ot-status-arrow"
            class="ruyi-icon ot-status-arrow-down ruyi-icon-ot-status-arrow"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
          >
            <path
              fill="currentColor"
              d="M7.5 3.914 3.854 7.561l-.707-.707 4.641-4.642a.3.3 0 01.424 0l4.642 4.642-.707.707L8.5 3.914V14h-1V3.914Z"
            ></path>
          </svg>
        </a-tooltip>
      </a>
    </div>
    <div class="monitor-footer" v-if="monitorStatus"></div>
    <div class="monitor-footer" v-if="monitorStatus">
      <a
        target="_blank"
        :href="
          'https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=' +
          ip +
          ':9100'
        "
        style="color: black"
      >
        <save-outlined />
        <a-tooltip title="磁盘使用率(/根系统盘,/data数据盘等)" color="orange">
          <span>&nbsp{{ info.dataUsedPercent }}</span>
        </a-tooltip>
      </a>
    </div>
  </a-space>
</template>

<script>
import { getTermMonitorInfo } from '@/api/devops/term'

export default {
  name: 'TerminalMonitor',
  props: {
    ip: String,
    content: String,
    termStatus: String,
  },
  data() {
    return {
      monitorStatus: false,
      info: {
        cpuUsed: '--',
        memoryUsed: '--',
        memoryUsedPercent: '--',
        bootTime: '--',
        cpuLoad: '--',
        dataUsedPercent: '--',
        tcpActive: '--',
        networkIn: '--',
        networkOut: '--',
      },
    }
  },
  created() {
    this.monitorInfo()
  },
  mounted() {
    const monitorId = setInterval(() => {
      if (this.termStatus === '2') {
        clearInterval(monitorId)
      }
      this.monitorInfo()
    }, 60000)
  },
  methods: {
    monitorInfo() {
      getTermMonitorInfo(this.ip).then(res => {
        this.info = res.Data
        if (this.info.memoryUsed !== '--' && this.info.memoryUsed !== '0.00GB') {
          this.monitorStatus = true
        }
      })
    },
  },
}
</script>

<style scoped>
.monitor-footer {
  align-items: center;
  display: flex;
}
</style>
