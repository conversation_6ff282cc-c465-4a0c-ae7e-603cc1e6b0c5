<template>
  <div>
    <a-card>
      <div class="fs-header">
        <div class="fs-header-left">
          <a-input
            v-model:value="this.localCurrentDirectoryInput" @pressEnter="$refs.table.refresh()"/>
        </div>
        <div class="fs-header-right">
          <a-space>
            <div class="fs-header-right-item">
              <a-tooltip title="刷新">
                <tx-button
                  type="primary"
                  size="small"
                  icon="reload"
                  @click="this.refresh"
                  :ghost="true">
                </tx-button>
              </a-tooltip>
            </div>
          </a-space>
        </div>
      </div>
      <a-table
        ref="table"
        :rowKey="(record) => record.name"
        :columns="columns"
        :data-source="this.files"
        size="small"
        :pagination="false"
        :loading="this.loading"
      >
        <template #bodyCell="{column, record}">
          <template v-if="column.dataIndex === 'name'">
              <span v-if="record.isDir" style="cursor: pointer" @click="rowClick(record)">
                <a-icon type="folder-open" theme="twoTone" style="margin-right: -1px"/>
                {{ record.name }}
              </span>
              <span v-else-if="record.isLink" style="cursor: default" >
                <a-icon type="link" style="color: #ff8300; margin-right: -1px" />
                {{ record.name }}
              </span>
              <span v-else class="no-select" style="cursor: pointer" @dblclick="openFile(record)">
                   <a-tooltip placement="top">
                    <template #title>
                      <span>双击打开</span>
                    </template>
                  <a-icon type="file"  style="margin-right: -1px"/>
                    {{ record.name }}
                  </a-tooltip>
                </span>
          </template>
<!--          <template v-if="column.dataIndex === 'opt'">-->
<!--/*                        <a href="javascript:;" style="margin-left: 8px" @click="clickRename(record)">重命名</a>*/-->
<!--          </template>-->
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import {h} from 'vue'
import {STable, Ellipsis} from '@/components'
import {
  termFileRename,
  termFileLs,
} from "@/api/devops/term-proxy"
import desktopRequest from '@/utils/desktopRequest'
import request from '@/utils/request'
import axios from 'axios'
import {getAccessToken} from '@/utils/auth'
import {getCloudTermKey} from "@/config";
import CryptoJS from "crypto-js";

export default {
  name: 'FileSystem',
  props: {
    currentDirectory: {
      type: String,
      default: '/'
    },
    currentDirectoryInput: {
      type: String,
      default: '/'
    },
    uuid: {
      type: String,
      default: ''
    }
  },
  components: {
    STable,
    Ellipsis,
  },
  data() {
    return {
      localCurrentDirectoryInput: this.currentDirectoryInput,
      localCurrentDirectory: this.currentDirectory,
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          scopedSlots: {customRender: 'name'},
          sorter: (a, b) => {
            if (a['key'] === '..') {
              return 0
            }

            if (b['key'] === '..') {
              return 0
            }
            return a.name.localeCompare(b.name)
          },
          sortDirections: ['descend', 'ascend']
        },
        {
          title: '大小',
          dataIndex: 'size',
          key: 'size',
          customRender: ({text, record}) => {
            if (!record.isDir && !record.isLink) {
              // return <span class='dode'>{this.renderSize(record.size)}</span>
              return h('span', {className: 'dode'}, this.renderSize(record.size))
            }
            // return <span class='dode'/>
            return h('span', {className: 'dode'})
          },
          sorter: (a, b) => {
            if (a['key'] === '..') {
              return 0
            }

            if (b['key'] === '..') {
              return 0
            }
            return a.size - b.size
          }
        },
        {
          title: '修改日期',
          dataIndex: 'modTime',
          key: 'modTime',
          sorter: (a, b) => {
            if (a['key'] === '..') {
              return 0
            }

            if (b['key'] === '..') {
              return 0
            }
            return a.modTime.localeCompare(b.modTime)
          },
          sortDirections: ['descend', 'ascend'],
          customRender: ({text, record}) => {
            // return <span class='dode'>{record.modTime}</span>
            return h('span', {className: 'dode'}, record.modTime)
          }
        },
        {
          title: '属性',
          dataIndex: 'mode',
          key: 'mode',
          customRender: ({text, record}) => {
            // return <span class='dode'>{record.mode}</span>
            return h('span', {className: 'dode'}, record.mode)
          }
        },
        // {
        //   title: '操作',
        //   dataIndex: 'opt',
        //   scopedSlots: {customRender: 'opt'}
        // }
      ],
      mkdirVisible: false,
      renameVisible: false,
      assetType: '',
      createVisible: false,
      confirmLoading: false,
      storageType: undefined,
      renameForm: {},
      mkdirForm: {},
      storageId: undefined,
      files: [
        {
          'name': '12.txt',
          'path': '/12.txt',
          'isDir': false,
          'mode': '-rw-------',
          'isLink': false,
          'modTime': '2023-01-29 14:09:14',
          'size': 0
        },
        {
          'name': 'Download',
          'path': '/Download',
          'isDir': true,
          'mode': 'drwx------',
          'isLink': false,
          'modTime': '2023-01-30 16:20:23',
          'size': 6
        },
        {
          'name': 'cfa-2.5.12-premium-x86-release(1).apk',
          'path': '/cfa-2.5.12-premium-x86-release(1).apk',
          'isDir': false,
          'mode': '-rw-r--r--',
          'isLink': false,
          'modTime': '2023-01-29 14:08:43',
          'size': 11419394
        }
      ],
      loading: false,
      currentFileKey: undefined,
      selectedRowKeys: [],
      selectedRows: [],
      uploading: {},
      callback: undefined,
      minHeight: 280,
      upload: false,
      delBtnLoading: false,
      download: false,
      delete: false,
      rename: false,
      edit: false,
      hasSelected: true,
      editorVisible: false,
      fileName: '',
      uploadKey: 'uploadKey',
      fileContent: ''
    }
  },
  created() {
    this.assetType = this.desktopType
    this.loadFiles(this.uuid,this.localCurrentDirectory)
  },
  methods: {
    renderSize(value) {
      if (value == null || value === '' || value === 0) {
        return '0 B'
      }
      const unitArr = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const srcSize = parseFloat(value)
      const index = Math.floor(Math.log(srcSize) / Math.log(1024))
      let size = srcSize / Math.pow(1024, index)
      size = size.toFixed(2)
      return size + ' ' + unitArr[index]
    },
    deleteAll() {
      const rows = this.selectedRows
      const title = '您确定要删除选中的'
      // const content = <div>{title}<span class='delAll' strong > {rows.length} </span>条记录吗？</div>
      const content = h('div', [
        title,
        h('span', {className: 'delAll'}, rows.length),
        '条记录吗？',
      ])
      this.$confirm({
        content: content,
        okType: 'danger',
        onOk: async () => {
          for (let i = 0; i < rows.length; i++) {
            if (rows[i] === '..') {
              continue
            }
            await this.rmFile(rows[i])
          }
          this.refresh()
        },
        onCancel() {

        }
      })
    },
    rowClick(record) {
      if (record.isDir || record.isLink) {
        if (record.path === '..') {
          // 获取当前目录的上级目录
          const currentDirectory = this.localCurrentDirectory
          let parentDirectory = currentDirectory.substring(0, currentDirectory.lastIndexOf('/'))
          if (parentDirectory === '') {
            this.localCurrentDirectoryInput = '/'
            this.localCurrentDirectory = '/'
            parentDirectory = '/'
          }
          this.loadFiles(this.uuid,parentDirectory)
        } else {
          console.log(1)
          console.log(record.path)
          this.loadFiles(this.uuid, record.path)
        }
      } else {
      }
    },
    openFile(record) {
        this.$emit("openFile",  record.path)
    },
    clickRename(record) {
      this.renameVisible = true
      this.currentFileKey = record.name
      this.renameForm = record
    },
    handleRenameOk() {
      // let currentDirectory = this.currentDirectory
      // if (!currentDirectory.endsWith('/')) {
      //   currentDirectory += '/'
      // }
      // const data = {
      //   'ip': this.ip,
      //   'username': this.username,
      //   'oldName': currentDirectory + this.currentFileKey,
      //   'newName': currentDirectory + this.renameForm['name']
      // }
      // if (data['oldName'] === data['newName']) {
      //   this.$message.success('重命名成功')
      //   this.renameVisible = false
      // } else {
      //   this.confirmLoading = true
      //   TermFileRename(data).then(res => {
      //     this.confirmLoading = false
      //     if (res['message'] === 'error') {
      //       this.renameVisible = false
      //       this.$message.error('获取失败,后端接口错误，请联系运维开发排查~')
      //     } else {
      //       this.renameVisible = false
      //       this.$message.success('重命名成功')
      //       this.loadFiles(this.currentDirectoryInput)
      //     }
      //   })
      // }
    },
    sortByName(a, b) {
      const a1 = a['name'].toUpperCase()
      const a2 = b['name'].toUpperCase()
      if (a1 < a2) {
        return -1
      }
      if (a1 > a2) {
        return 1
      }
      return 0
    },
    encrypt(authData) {
      let keyStr = getCloudTermKey()
      let ivStr = keyStr
      let key = CryptoJS.enc.Utf8.parse(keyStr)
      let iv = CryptoJS.enc.Utf8.parse(ivStr)
      let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(authData))
      let encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      })
      return encodeURIComponent(encrypted.toString())
    },
    loadFiles(uuid, filePath) {
      this.loading = true
      let authData = {
        uuid: this.uuid,
        filePath: this.serverPwd,
      }
      console.log("ssadasd",filePath)
      if (!filePath) {
        authData.filePath = '/'
      } else {
        authData.filePath = filePath
      }
      const auth = decodeURIComponent(this.encrypt(authData))
      termFileLs({uuid: auth}).then(res => {
        if (res['message'] === 'error') {
          this.loading = false
          this.$message.error(res['error'])
        } else {
          this.loading = false
          // this.selectedRowKeys = []
          const data = res['data']
          // 优先文件夹
          const items = data.map(item => {
            return {'key': item['path'], ...item}
          })
          const dirs = items.filter(item => item['isDir'] === true)
          dirs.sort(this.sortByName)
          const files = items.filter(item => item['isDir'] === false)
          files.sort(this.sortByName)
          dirs.push(...files)
          if (filePath !== '/') {
            dirs.splice(0, 0, {filePath: '..', name: '..', path: '..', isDir: true, disabled: true})
          }
          this.files = dirs
          this.localCurrentDirectory = filePath
          this.localCurrentDirectoryInput = filePath
        }
      })
    },
    refresh() {
      this.loadFiles(this.uuid,this.localCurrentDirectoryInput)
    },
    // onSelectChange(selectedRowKeys, selectedRows) {
    //   this.selectedRowKeys = selectedRowKeys
    //   this.selectedRows = selectedRows
    // }
  },
  watch: {
    // 'selectedRowKeys'(val) {
    //   this.hasSelected = val.length <= 0
    // }
  },
  computed: {
    // rowSelection() {
    //   return {
    //     selectedRowKeys: this.selectedRowKeys,
    //     onChange: this.onSelectChange
    //   }
    // }
  }
}
</script>

<style scoped>
.no-select {
  user-select: none; /* For modern browsers */
  -moz-user-select: none; /* For older Firefox */
  -webkit-user-select: none; /* For Safari */
}
.dode {
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.popup {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
  animation-duration: 0.4s;
  background-clip: padding-box;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.75);
  left: 0;
  list-style-type: none;
  margin: 0;
  outline: none;
  padding: 0;
  position: fixed;
  text-align: left;
  top: 0;
  overflow: hidden;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.75);
}

.popup li {
  clear: both;
  /*color: rgba(0, 0, 0, 0.65);*/
  cursor: pointer;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  margin: 0;
  padding: 5px 12px;
  transition: all .3s;
  white-space: nowrap;
  -webkit-transition: all .3s;
}

.popup li:hover {
  background-color: #e6f7ff;
}

.popup li > i {
  margin-right: 8px;
}

.fs-header {
  align-items: center;
  position: relative;
  display: flex;
}

.fs-header-left {
  flex: 1 1 0;
}

.fs-header-right {
  text-align: right;
  margin-left: 10px;
}

.fs-header-right-item {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  height: 100%;
}

.delAll {
  color: #1890FF
}
</style>
