<template>
  <a-layout v-if="!isSplit" style="padding: 0; height: 100%">
    <a-input type="file" ref="fileUpload" id="file-upload" style="display: none" @change="handleUploadFile" multiple />
    <a-input
      type="file"
      id="dir-upload"
      style="display: none"
      ref="dirUpload"
      @change="handleUploadDir"
      webkitdirectory=""
      multiple
    />
    <a-layout-content style="position: relative">
      <span v-if="syncInput" class="syncFlag">
        <img src="/vendor-cdn/@img/term/sync.png" alt="" />
      </span>
      <v-contextmenu ref="contextmenu">
        <!-- :disabled="this.copy === undefined" -->
        <v-contextmenu-item
          :disabled="vueInstance ? vueInstance.copy == undefined : copy === undefined"
          @click="menuClick('copy')"
        >
          复制&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ctrl+c
        </v-contextmenu-item>
        <v-contextmenu-item @click="menuClick('paste')" v-if="this.from != 'wxApp'">
          粘贴&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ctrl+v
        </v-contextmenu-item>
        <v-contextmenu-item disabled v-else>
          粘贴&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请使用快捷键ctrl+shift+v操作
        </v-contextmenu-item>
        <v-contextmenu-item @click="menuClick('clear')">
          清屏&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ctrl+l
        </v-contextmenu-item>
        <v-contextmenu-divider></v-contextmenu-divider>
        <v-contextmenu-item @click="menuClick('vertical')">右分屏</v-contextmenu-item>
        <v-contextmenu-item @click="menuClick('horizontal')">下分屏</v-contextmenu-item>
        <v-contextmenu-item @click="menuClick('close')">
          {{ position === 0 ? '关闭子屏' : '关闭当前' }}
        </v-contextmenu-item>
        <v-contextmenu-divider></v-contextmenu-divider>
        <v-contextmenu-item v-if="this.commonVimStatus === 1" @click="showFileSystem">文件管理</v-contextmenu-item>
        <v-contextmenu-item @click="commandInput('export PS1=&quot;[\\u@\\W]\\$&quot;\n')">
          缩短主机名
        </v-contextmenu-item>
        <v-contextmenu-item @click="menuClick('globalCommand')">批量发送命令</v-contextmenu-item>
        <v-contextmenu-item @click="syncInputSwitch">同步输入{{ syncInput ? '(已开)' : '(已关)' }}</v-contextmenu-item>
        <v-contextmenu-submenu title="最近使用top5" v-if="userCommandData.length !== 0" :disabled="termStatus === '2'">
          <template v-for="commandInfo in userCommandData" :key="commandInfo.title">
            <v-contextmenu-item @click="commandInput(commandInfo.value)">{{ commandInfo.title }}</v-contextmenu-item>
          </template>
        </v-contextmenu-submenu>
        <v-contextmenu-submenu title="快捷命令" :disabled="termStatus === '2' || userCommonCommandData.length === 0">
          <template v-for="commandInfo in userCommonCommandData" :key="commandInfo.title">
            <v-contextmenu-item @click="commandInput(commandInfo.value)">{{ commandInfo.title }}</v-contextmenu-item>
          </template>
        </v-contextmenu-submenu>
        <v-contextmenu-item
          @click="addCommonCommand()"
          :disabled="vueInstance ? vueInstance.copy == undefined : copy === undefined"
        >
          添加为快捷命令
        </v-contextmenu-item>
        <v-contextmenu-submenu title="智能自动补全(内测)">
          <v-contextmenu-item
            @click="
              commandInput(
                'if grep -qi \'ubuntu\' /etc/os-release; then sudo apt install fish -y >/dev/null 2>&1 && sudo chsh -s /usr/bin/fish $USER && fish; elif grep -qi \'centos\' /etc/os-release; then sudo yum install fish -y >/dev/null 2>&1 && sudo chsh -s /usr/bin/fish $USER && fish; fi\n'
              )
            "
          >
            安装此增强功能并启用
          </v-contextmenu-item>
          <v-contextmenu-item @click="commandInput('sudo chsh -s /usr/bin/fish $USER && fish\n')">
            启用自动补全（当前用户）
          </v-contextmenu-item>
          <v-contextmenu-item @click="commandInput('sudo chsh -s /usr/bin/bash $USER\n')">
            关闭自动补全（当前用户）
          </v-contextmenu-item>
        </v-contextmenu-submenu>
        <v-contextmenu-divider></v-contextmenu-divider>
        <v-contextmenu-item v-if="!isMobile" :disabled="termStatus === '2'" @click="uploadFile">
          上传文件
        </v-contextmenu-item>
        <v-contextmenu-item v-if="!isMobile" :disabled="termStatus === '2'" @mousedown="uploadDir">
          上传文件夹
        </v-contextmenu-item>
        <v-contextmenu-item @click="menuClick('download')">下载文件</v-contextmenu-item>

        <v-contextmenu-divider></v-contextmenu-divider>
        <v-contextmenu-submenu title="字体大小">
          <v-contextmenu-item @click="menuClick('upFontsize')">放大</v-contextmenu-item>
          <v-contextmenu-item @click="menuClick('downFontsize')">缩小</v-contextmenu-item>
        </v-contextmenu-submenu>
        <v-contextmenu-item v-if="from != 'wxApp' && this.isFull" @click="toggleFullscreen(false)">
          显示菜单栏
        </v-contextmenu-item>

        <v-contextmenu-item v-if="from != 'wxApp' && !this.isFull" @click="toggleFullscreen(true)">
          隐藏菜单栏
        </v-contextmenu-item>
        <v-contextmenu-item v-if="termStatus === '1'" @click="disconnectCTS">断开连接</v-contextmenu-item>
        <v-contextmenu-item v-if="termStatus === '2'" @click="connectCTS">重新连接</v-contextmenu-item>
        <v-contextmenu-divider></v-contextmenu-divider>
        <v-contextmenu-item v-if="!isMobile" @click="menuClick('pwa')">安装web应用指南</v-contextmenu-item>
        <!--<v-contextmenu-item v-if="!isMobile" @click="menuClick('ai')">AI助手（内测）</v-contextmenu-item>-->
        <v-contextmenu-item v-if="!isMobile" @click="menuClick('grafana')">监控信息</v-contextmenu-item>
        <v-contextmenu-item v-if="!isMobile" @click="menuClick('help')">帮助文档</v-contextmenu-item>
      </v-contextmenu>
      <div v-if="isSearchModalVisible" class="search-modal-content">
        <div class="search-input-wrapper">
          <input
            ref="searchInput"
            v-model="searchText"
            @keyup.enter="performSearch"
            placeholder="请输入搜索内容"
            class="search-input"
          />
        </div>
        <div class="search-controls">
          <label class="search-option">{{ searchResults.length }}/{{ selectedIndex }}</label>
          <label class="search-option">
            <a-tooltip placement="bottomLeft">
              <template #title>上一个</template>
              <UpOutlined class="optImg" @click="findPrevious" :disabled="!searchText" />
            </a-tooltip>
          </label>
          <label class="search-option">
            <a-tooltip placement="bottomLeft">
              <template #title>下一个</template>
              <DownOutlined class="optImg" @click="findNext" :disabled="!searchText" />
            </a-tooltip>
          </label>
          <label class="search-option">
            <a-tooltip placement="bottomLeft">
              <template #title>关闭</template>
              <CloseOutlined class="optImg" @click="closeSearchModal" />
            </a-tooltip>
          </label>
          <div class="search-buttons">
            <!-- <button>关闭</button> -->
          </div>
        </div>
        <!-- <div v-if="searchResultInfo" class="search-result-info">
          {{ searchResultInfo }}
        </div> -->
      </div>
      <div v-if="suggestions.length" id="hint-box" class="suggestions">
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          @click="selectSuggestion(suggestion)"
          :class="{ active: index === activeSuggestion }"
        >
          {{ suggestion }}
        </div>
      </div>
      <div v-contextmenu:contextmenu ref="myTerm" :id="id" class="xterm" style="min-width: 600px" @drop="onDrop"></div>
    </a-layout-content>
    <p class="shorts" v-if="isMobile && quickShow">
      <span v-for="i in Shortcut" :key="i.name" class="shortCut" @click="pressShort(i)">{{ i.name }}</span>
    </p>
  </a-layout>
  <splitpanes style="height: 100%" v-else :horizontal="isHorizontal">
    <pane ref="panes">
      <xterm
        :tabPane="tabPane"
        ref="termx"
        :position="0"
        :vueInstance="instance"
        :localInstanceId="instanceId"
        :ws-instance="wsInstanceLocal"
        :xterm-instance="xtermInstanceLocal"
        @close="handleChildClose(0)"
        :authkey="authkey"
        :fullScreen="fullScreen"
        :ip="ip"
        :content="content"
        :email="email"
        :id="id"
        :fontSize="fontSize"
        :cursorStyle="cursorStyle"
        :scrollBack="scrollBack"
        @collapseMenu="toggleFullscreen"
        @commandSwitch="commandSwitch"
        @syncCommand="syncCommand"
      />
    </pane>
    <pane ref="panes">
      <xterm
        :tabPane="tabPane"
        ref="termx"
        :position="1"
        @close="handleChildClose(1)"
        :authkey="authkey"
        :fullScreen="fullScreen"
        :ip="ip"
        :content="content"
        :email="email"
        :id="termKey2"
        :fontSize="fontSize"
        :cursorStyle="cursorStyle"
        :scrollBack="scrollBack"
        @collapseMenu="toggleFullscreen"
        @commandSwitch="commandSwitch"
        @syncCommand="syncCommand"
      />
    </pane>
  </splitpanes>
  <!-- <script src="http://terminal-ai.intsig.net/embed.min.js" id="LJRkyejpgXFxynJ1" defer></script> -->
  <div v-for="editor in openEditors" :key="editor.filePath" v-show="editor.visible">
    <vue-drag-resize-rotate
      :x="editor.vimEditorX"
      :y="editor.vimEditorY"
      :w="editor.vimEditorWidth"
      :h="editor.vimEditorHeight"
      :rotatable="true"
      :resizable="true"
      :draggable="true"
      :grid="[1, 1]"
      class="file-vim-content"
      :drag-handle="'.drag-handle'"
      @dragstop="(...args) => onDragStop(args, editor)"
      @resizestop="(...args) => onResizeStop(args, editor)"
    >
      <div class="editor-container">
        <div class="editor-toolbar drag-handle">
          <div>
            <a-button
              class="toolbar-btn save-btn"
              style="float: left; margin-right: 5px"
              @click="handleSave(editor.filePath, false)"
              :loading="editor.loading"
            >
              <span class="btn-icon"><a-icon :style="{ fontSize: '18px' }" type="save" /></span>
              保存
            </a-button>
            <!--            <a-button class="toolbar-btn save-btn" @click="handleSave(editor.filePath)">-->
            <!--              <span class="btn-icon"><a-icon :style="{ fontSize: '18px' }" type="save"/></span>-->
            <!--              另存为-->
            <!--            </a-button>-->
          </div>
          <span style="cursor: default; color: #cecece; margin-top: -5px">
            {{ editorFilter(editor.action) }}{{ editor.filePath }}
          </span>
          <div>
            <a-button
              v-show="showVimFullScreenEditor"
              class="toolbar-btn close-btn"
              style="float: left"
              @click="fullScreenVimEditor(editor.filePath)"
            >
              <span class="btn-icon"><a-icon :style="{ fontSize: '18px' }" type="fullscreen" /></span>
            </a-button>
            <a-button
              v-show="showVimFullScreenExitEditor"
              class="toolbar-btn close-btn"
              style="float: left"
              @click="exitFullScreenVimEditor(editor.filePath)"
            >
              <span class="btn-icon"><a-icon :style="{ fontSize: '18px' }" type="fullscreen-exit" /></span>
            </a-button>
            <a-button class="toolbar-btn close-btn" @click="closeVimEditor(editor.filePath)">
              <span class="btn-icon"><a-icon :style="{ fontSize: '18px' }" type="close" /></span>
            </a-button>
          </div>
        </div>
        <TxEditorCode
          v-model="editor.vimText"
          @update:modelValue="newValue => handleTextChange(editor, newValue)"
          class="monaco-editor"
          :height="editor.vimEditorHeightString"
          :language="editor.contentType"
          theme="vs-dark"
        />
      </div>
    </vue-drag-resize-rotate>
  </div>

  <a-drawer
    title="文件管理"
    placement="right"
    width="40%"
    :mask="false"
    :closable="true"
    :visible="termFileSystemVisible"
    @close="closeFileSystem"
  >
    <TermFileSystem
      :currentDirectory="this.serverPwd"
      :currentDirectoryInput="this.serverPwd"
      :uuid="this.id"
      @openFile="submitData"
    />
  </a-drawer>
</template>
<script>
import 'xterm/css/xterm.css'
import { SearchAddon } from 'xterm-addon-search'
import { defineAsyncComponent, getCurrentInstance, h, inject } from 'vue'
import { eventBus } from './eventBus'
import { Terminal } from 'xterm'
import { FitAddon } from 'xterm-addon-fit'
import { debounce } from 'lodash'
import CryptoJS from 'crypto-js'
import { createTermCommonCommand, termUserCommand, termUserCommonCommand } from '@/api/devops/term'
import { termConfigList } from '@/api/devops/term'
import { getCloudTermKey } from '@/config'
import { getAccessToken } from '@/utils/auth'
import { judgeEnv } from '@/utils/util'
import store from '@/store'
import { setWaterMarkWhite } from '@/utils/watermark'
import { Contextmenu, ContextmenuItem, directive } from 'v-contextmenu'
import 'v-contextmenu/dist/themes/default.css'
import { Pane, Splitpanes } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import { v4 as uuidv4 } from 'uuid'
import {} from 'vue'
import TermFileSystem from './components/TermFileSystem.vue'
import VueDragResizeRotate from '@gausszhou/vue3-drag-resize-rotate'
import '@gausszhou/vue3-drag-resize-rotate/lib/bundle.esm.css'
import { termFileContent, termFileContentSave } from '@/api/devops/term-proxy'
import VueDraggableResizable from 'vue-draggable-resizable/src/components/vue-draggable-resizable.vue'
import SecondaryVerification from '@/components/Verification/verify.vue'

let uniqueId
const key = 'updatable'
export default {
  directives: {
    contextmenu: directive,
  },
  setup() {
    const Shortcut = [
      {
        name: 'Esc',
        code: '\x1b',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: 'Tab ',
        code: '\t',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '↑',
        code: '\x1b[A',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '↓',
        code: '\x1b[B',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '←',
        code: '',
        msgType: 'leftArrow',
      },
      {
        name: '→',
        code: '',
        msgType: 'rightArrow',
      },
      {
        name: '^C',
        code: '\x03',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^K',
        code: '\x0b',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^V',
        code: '\x16',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^Z',
        code: '\x1a',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^XX',
        code: '\x18\x18',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^R',
        code: '\x12',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^G',
        code: '\x07',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^B',
        code: '\x02',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^X',
        code: '\x18',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^F',
        code: '\x06',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^P',
        code: '\x10',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^N',
        code: '\x0e',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^H',
        code: '\x08',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^U',
        code: '\x15',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^A',
        code: '\x01',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^E',
        code: '\x05',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^L',
        code: '\x0c',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^W',
        code: '\x17',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^K',
        code: '\x0b',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^D',
        code: '\x04',
        msgType: 'TERMINAL_DATA',
      },
      {
        name: '^Y',
        code: '\x19',
        msgType: 'TERMINAL_DATA',
      },
    ]
    const width = inject('width')
    const toogleNormal = inject('toogleNormal', () => {})
    const toogleFull = inject('toogleFull', () => {})
    const onkey = e => {}

    return {
      width,
      toogleFull,
      toogleNormal,
      Shortcut,
    }
  },
  components: {
    SecondaryVerification,
    VueDraggableResizable,
    VueDragResizeRotate,
    TermFileSystem,
    xterm: defineAsyncComponent(() => import('./Term.vue')),
    Pane,
    Splitpanes,
    [Contextmenu.name]: Contextmenu,
    [ContextmenuItem.name]: ContextmenuItem,
  },
  props: {
    tabPane: {
      type: Object,
      default: null,
    },
    position: {
      type: Number,
      default: 0,
    },
    localInstanceId: {
      type: String,
      default: '',
    },

    vueInstance: {
      type: Object,
      default: null,
    },
    xtermInstance: {
      type: Object,
      default: null,
    },
    wsInstance: {
      type: Object,
      default: null,
    },
    ip: String,
    content: String,
    email: String,
    authkey: String,
    id: String,
    fontSize: Number,
    cursorStyle: String,
    scrollBack: Number,
    fullScreen: Boolean,
    plantform: String,
    directConnect: String,
    from: {
      type: String,
      default: () => {
        return 'PC'
      },
    },
  },

  data() {
    return {
      x: 0,
      y: 0,
      needSearch: true,
      visible: false,
      openEditors: [],
      searchAddon: null,
      quickVimStatus: '2',
      commonVimStatus: '2',
      vimText: '',
      showOtherFunction: true,
      currentDirectory: '',
      drapTransition: 'all 0.3s ease-out',
      buttonOpacity: 0.5,
      showVimEditor: false,
      showVimFullScreenEditor: true,
      showVimFullScreenExitEditor: false,
      // 搜索相关状态
      isSearchModalVisible: false,
      termFileSystemVisible: false,
      searchText: '',
      searchResultInfo: '',
      searchOptions: {
        caseSensitive: false,
        wholeWord: false,
      },

      // 搜索结果缓存
      searchResults: [],
      currentSearchIndex: 0,

      // 智能提示相关
      suggestions: [],
      activeSuggestion: 0,

      syncInput: false,
      instanceId: '',
      // id: this.id,
      instance: null,
      test: 111,
      xtermInstanceLocal: this.xtermInstance,
      wsInstanceLocal: this.wsInstance,
      terminalContainer: null,
      termKey1: '',
      termKey2: '',
      isSplit: false, // 是否已分屏
      splitDirection: '', // 分屏方向：'horizontal' 或 'vertical'
      copy: undefined,
      handlerClose: false, // 主动关闭
      wsState: 1,
      isConnected: false,
      hasShownDisconnectMessage: false,
      isReconnecting: false,
      reconnectInterval: null, // 将定时器ID提升到类成员变量
      isOnline: navigator.onLine,
      quickShow: false,
      targetEle: null,
      customKey: {},
      isFull: true,
      termStatus: '1',
      aaaa: '1',
      timeCount: 0,
      sessionPath: '',
      sftpReady: false,
      isGetPwd: false,
      serverPwd: '/home/' + store.getters.email,
      fitAddon: null,
      fileList: [],
      terminal: null,
      isAdmin: true,
      isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
      userCommandReq: {
        ip: undefined,
        user: undefined,
      },
      userCommonCommandReq: {
        email: undefined,
      },
      userCommandData: [],
      userCommonCommandData: [],
      commandData: {
        email: undefined,
        command: undefined,
        comment: undefined,
      },
    }
  },
  computed: {
    // 判断分屏方向是否为水平
    isHorizontal() {
      return this.splitDirection == 'horizontal'
    },
    selectedIndex() {
      if (this.searchResults.length == 0) {
        return 0
      } else {
        return this.currentSearchIndex + 1
      }
    },
  },
  watch: {
    from: {
      handler: function (n, o) {},
      deep: true,
      immediate: true,
    },
    'navigator.online': {
      handler: function (n, o) {
        if (n) {
        }
      },
      deep: true,
      immediate: true,
    },
    searchText: {
      handler: function (n, o) {
        if (n || n == '') {
          this.needSearch = true
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    onDragStop(args, editor) {
      editor.vimEditorX = args[0]
      editor.vimEditorY = args[1]
    },
    onResizeStop(args, editor) {
      editor.vimEditorX = args[0]
      editor.vimEditorY = args[1]
      editor.vimEditorWidth = args[2]
      editor.vimEditorHeight = args[3]
      editor.vimEditorHeightString = args[3] + 'px'
    },
    editorFilter(action) {
      if (action === 'editor') {
        return '编辑文件：'
      } else {
        return '新建文件：'
      }
    },
    closeFileSystem() {
      this.termFileSystemVisible = false
    },
    showFileSystem() {
      this.termFileSystemVisible = true
    },
    handleTextChange(editor, newValue) {
      if (editor.originVimText !== newValue) {
        editor.fileChange = true
        editor.saved = false
      } else {
        editor.fileChange = false
      }
    },
    closeVimEditor(filePath) {
      const editor = this.openEditors.find(editor => editor.filePath === filePath)
      const that = this
      if (editor.fileChange) {
        if (!editor.saved) {
          this.$confirm({
            content: `您想将更改保存到 ${editor.filePath} 吗？`,
            width: '25%',
            onOk() {
              that.handleSave(editor.filePath, true)
            },
            onCancel() {
              const index = that.openEditors.indexOf(editor)
              if (index !== -1) {
                that.openEditors.splice(index, 1)
              }
            },
          })
        }
      } else {
        const index = this.openEditors.indexOf(editor)
        if (index !== -1) {
          this.openEditors.splice(index, 1)
        }
      }
    },
    fullScreenVimEditor(filePath) {
      const editor = this.openEditors.find(editor => editor.filePath === filePath)
      // 记住上一次
      editor.lastVimEditorX = editor.vimEditorX
      editor.lastVimEditorY = editor.vimEditorY
      editor.lastVimEditorHeightString = editor.vimEditorHeightString
      editor.lastVimEditorHeight = editor.vimEditorHeight
      editor.lastVimEditorWidth = editor.vimEditorWidth
      editor.vimEditorHeightString = this.$refs.myTerm.clientHeight
      editor.vimEditorHeight = this.$refs.myTerm.clientHeight
      editor.vimEditorWidth = this.$refs.myTerm.clientWidth
      editor.vimEditorX = 0
      editor.vimEditorY = window.innerHeight - this.$refs.myTerm.offsetHeight
      this.showVimFullScreenEditor = false
      this.showVimFullScreenExitEditor = true
    },
    exitFullScreenVimEditor(filePath) {
      const editor = this.openEditors.find(editor => editor.filePath === filePath)
      editor.vimEditorHeightString = editor.lastVimEditorHeightString
      editor.vimEditorHeight = editor.lastVimEditorHeight
      editor.vimEditorWidth = editor.lastVimEditorWidth
      editor.vimEditorX = editor.lastVimEditorX
      editor.vimEditorY = editor.lastVimEditorY

      this.showVimFullScreenEditor = true
      this.showVimFullScreenExitEditor = false
    },
    handleSave(filePath, needClose) {
      const editor = this.openEditors.find(editor => editor.filePath === filePath)
      let authData = {
        uuid: this.id,
        filePath: filePath,
        content: editor.vimText,
      }
      const auth = decodeURIComponent(this.encrypt(authData))
      if (editor.fileChange) {
        editor.loading = true
        termFileContentSave({ data: auth })
          .then(response => {
            if (response.error !== '') {
              this.$error({
                title: `保存失败！ ${response.error}`,
                onOk: (editor.loading = false),
              })
            } else {
              this.$message.success('文件保存成功~')
              // 关闭
              if (needClose) {
                const index = this.openEditors.indexOf(editor)
                if (index !== -1) {
                  this.openEditors.splice(index, 1)
                }
              } else {
                editor.loading = false
                editor.saved = true
                editor.fileChange = false
              }
            }
          })
          .catch(e => {
            this.$error({
              title: `保存失败！ ${e}`,
              okText: '返回主页',
              onOk: (editor.loading = false),
            })
          })
      } else {
        this.$message.success('文件保存成功~')
      }
    },
    getVimConfig() {
      termConfigList({}).then(res => {
        this.quickVimStatus = res.Data.data[0].quickVimStatus
        this.commonVimStatus = res.Data.data[0].commonVimStatus
      })
    },
    submitData(filePath) {
      let authData = {
        uuid: this.id,
        filePath: filePath,
      }
      const auth = decodeURIComponent(this.encrypt(authData))
      termFileContent({ uuid: auth }).then(response => {
        if (response.error !== '') {
          this.$error({
            title: `打开文件失败！ ${response.error}`,
          })
        } else {
          const existingEditor = this.openEditors.find(editor => editor.filePath === filePath)
          if (!existingEditor) {
            this.termFileSystemVisible = false
            this.openEditors.push({
              filePath: filePath,
              visible: true,
              vimText: response.content,
              originVimText: response.content,
              action: response.action,
              vimEditorHeightString: this.$refs.myTerm.clientHeight - 100 + 'px',
              vimEditorHeight: this.$refs.myTerm.clientHeight - 150,
              vimEditorWidth: this.$refs.myTerm.clientWidth - 500,
              vimEditorX: 100,
              vimEditorY: 50,
              contentType: response.contentType,
              loading: false,
              saved: false,
            })
          } else {
            existingEditor.visible = true
            existingEditor.vimText = response.content
          }
          this.$nextTick(() => {
            this.$forceUpdate()
          })
        }
      })
    },
    syncInputSwitch() {
      this.syncInput = !this.syncInput
      this.vueInstance.syncInput = this.syncInput
    },
    commandSwitch() {
      this.$emit('commandSwitch', this.tabPane, true)
    },
    syncCommand(data, key, instanceId) {
      this.$emit('syncCommand', data, key, instanceId)
    },
    termmExecute(text) {},

    menuClick(opt) {
      let username = this.email.split('@')[0]
      switch (opt) {
        case 'copy':
          this.vueInstance ? (this.copy = this.vueInstance.copy) : ''
          navigator.clipboard.writeText(this.copy)
          this.vueInstance ? (this.vueInstance.copy = undefined) : ''
          this.copy = undefined
          break
        case 'paste':
          navigator.clipboard
            .readText()
            .then(text => {
              // 将剪贴板的内容写入到终端
              this.wsInstanceLocal.send(JSON.stringify({ terminalId: this.id, msgType: 'TERMINAL_DATA', data: text }))
              this.xtermInstanceLocal.focus()
            })
            .catch(err => {})
          break
        case 'clear':
          this.wsInstanceLocal.send(JSON.stringify({ terminalId: this.id, msgType: 'TERMINAL_DATA', data: '\x0C' }))
          break
        case 'horizontal':
          this.syncInput = false
          this.termKey1 = `${username}@${this.ip}:${uuidv4()}`
          this.termKey2 = `${username}@${this.ip}:${uuidv4()}`
          this.isSplit = true
          this.splitDirection = opt
          // this.xtermInstanceLocal.dispose()
          break
        case 'vertical':
          this.syncInput = false
          this.termKey1 = `${username}@${this.ip}:${uuidv4()}`
          this.termKey2 = `${username}@${this.ip}:${uuidv4()}`
          this.isSplit = true
          this.splitDirection = opt

          break
        case 'close':
          // 向父组件发送关闭事件
          this.$emit('close')
          break
        case 'globalCommand':
          this.commandSwitch()
          break
        case 'download':
          // window.open(`http://cloud.intsig.net/workflow/server-data-download?ip=${this.ip}`, '_blank')
          window.open(`http://cloud.intsig.net/workflow/server-data-download?ip=${this.ip}`, '_blank')
          break
        case 'grafana':
          window.open(
            `https://grafana-autom.intsig.net/d/b8a9563c-4944-4d69-8d57-75b8062d2cc1/terminal?orgId=1&var-node=${this.ip}:9100`,
            '_blank'
          )
          break
        case 'ai':
          window.open('https://terminal-ai.intsig.net/chat/LJRkyejpgXFxynJ1', '_blank')
          break
        case 'help':
          window.open('https://doc.intsig.net/pages/viewpage.action?pageId=650510987', '_blank')
          break
        case 'upFontsize':
          this.xtermInstanceLocal.options.fontSize += 2
          break
        case 'downFontsize':
          this.xtermInstanceLocal.options.fontSize -= 2
          break
        case 'pwa':
          window.open('https://doc.intsig.net/pages/viewpage.action?pageId=863666179', '_blank')
          break
      }
    },
    handleChildClose(index) {
      const panes = this.$refs.panes
      if (panes && panes.length > 1) {
        // 移除当前关闭的 Pane
        panes.splice(index, 1)
        // 如果只剩下一个 Pane，更新状态
        if (panes.length === 1) {
          this.isSplit = false
          this.splitDirection = ''
        }
      } else {
        // 如果没有 Pane 了，恢复到未分屏状态
        this.isSplit = false
        this.splitDirection = ''
        // 需要重新附加 xterm 实例
        this.$nextTick(() => {
          this.attachTerminal()
          this.$forceUpdate()
        })
      }
    },
    attachTerminal() {
      // 移除之前的终端容器内容
      if (this.terminalContainer && this.terminalContainer.childNodes.length > 0) {
        this.terminalContainer.innerHTML = ''
      }
      // 重新获取终端容器的引用
      this.terminalContainer = this.$refs.myTerm
      this.xtermInstanceLocal == null ? (this.xtermInstanceLocal = this.xtermInstanceLocal) : ''
      if (!this.xtermInstanceLocal) {
        // 否则，创建新的 xterm 实例
        //  分屏 统一不需要二次验证 ，直接链接即可
        this.wsInit('isAutoConnect')
        this.termInit()
        this.createListener()
      } else {
        if (this.terminalContainer && this.terminalContainer.childNodes.length > 0) {
          this.terminalContainer.innerHTML = ''
        }
        // 将 xterm.js 实例重新附加到终端容器
        // 如果父组件传递了 xterm 实例，使用该实例，并重新附加到新的容器
        this.xtermInstanceLocal.open(this.terminalContainer)
        this.xtermInstanceLocal.onData().dispose()
        this.termFn(this.wsInstanceLocal, false).bind(this)
      }
      if (this.wsInstanceLocal) {
        this.workerInit()
        console.log(typeof this.wsFn, 'type')
        if (this.wsFn) {
          let fn = this.wsFn.bind(this)
          fn()
        }
      }
    },
    workerInit() {
      const terminalId = this.id
      this.worker = new Worker(new URL('./setIntervalPingWorker.js', import.meta.url))
      // this.worker = new Worker('./setIntervalPingWorker.js')
      const that = this
      this.worker.onmessage = function (event) {
        if (event.data === 'ping' && that.wsInstanceLocal && that.wsInstanceLocal.readyState === WebSocket.OPEN) {
          const msgType = 'PING'
          const data = ''
          that.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
        }
      }
    },
    updateNetworkStatus() {
      this.isOnline = navigator.onLine
    },
    simulateLeftArrowKey() {
      // 创建一个键盘事件对象
      const event = new KeyboardEvent('keydown', {
        key: 'ArrowLeft',
        keyCode: 37,
        code: 'ArrowLeft',
        which: 37,
        shiftKey: false,
        ctrlKey: false,
        altKey: false,
        metaKey: false,
      })

      // 分发事件到文档
      document.dispatchEvent(event)
    },
    pressShort(key) {
      this.xtermInstanceLocal.write('\x1b[5 i')

      if (key.msgType == 'TERMINAL_DATA') {
        this.wsInstanceLocal.send(JSON.stringify({ terminalId: this.id, msgType: key.msgType, data: key.code }))
      } else {
        if (key.msgType == 'leftArrow') {
          this.xtermInstanceLocal.write('\x1b[D')
        } else if (key.msgType == 'rightArrow') {
          this.xtermInstanceLocal.write('\x1b[C')
        }
      }
    },
    encrypt(authData) {
      let keyStr = getCloudTermKey()
      let ivStr = keyStr
      let key = CryptoJS.enc.Utf8.parse(keyStr)
      let iv = CryptoJS.enc.Utf8.parse(ivStr)
      let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(authData))
      let encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      })
      return encodeURIComponent(encrypted.toString())
    },
    createListener() {
      const handleResizeDebounced = debounce(this.handleResize, 300)
      const observer = new ResizeObserver(entries => {
        for (const entry of entries) {
          handleResizeDebounced()
        }
      })
      observer.observe(this.$refs.myTerm)
    },
    handleResize() {
      this.fitAddon.fit()
    },
    wsInit(connect = '') {
      let that = this
      const user = this.email.split('@')[0]
      const terminalId = this.id
      // let terminalContainer = document.getElementById(this.id)
      const authData = {
        email: this.email,
        ip: this.ip,
        terminalId: terminalId,
        auth: this.authkey,
        token: getAccessToken(),
        platform: this.plantform,
        connect,
      }
      const auth = this.encrypt(authData)
      let wsUrl = 'wss://termws.intsig.net/ws?&uuid=' + auth
      const env = judgeEnv()
      if (env !== 'ONLINE') {
        wsUrl = 'wss://termws-test.intsig.net/ws?&uuid=' + auth
      }
      // let wsUrl = 'ws://127.0.0.1:8089/ws?&uuid=' + auth
      // 初始化文件上传目录
      this.serverPwd = '/home/' + user

      this.wsInstanceLocal = new WebSocket(wsUrl)
      this.wsFn()
      // if (connect) {
      //   that.termFn(that.wsInstanceLocal, true, 'reconnect')
      // }
      window.SendTerminalData = function (data) {
        if (
          that.wsInstanceLocal.readyState === WebSocket.CLOSING ||
          that.wsInstanceLocal.readyState === WebSocket.CLOSED
        ) {
          return
        }
        const msgType = 'TERMINAL_DATA'
        that.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
      }
    },
    wsFn() {
      let that = this
      const user = this.email.split('@')[0]
      const terminalId = this.id
      this.wsInstanceLocal.binaryType = 'arraybuffer'
      const enc = new TextDecoder('utf-8')
      this.wsInstanceLocal.onmessage = function (event) {
        // 忽略上传而触发的pwd命令
        if (typeof event.data !== 'object') {
          that.dispatch(that.xtermInstanceLocal, event.data, that.wsInstanceLocal)
          let wsDatas = JSON.parse(event.data)
          if (wsDatas.msgType == 'CLOSE') {
            that.handlerClose = true
          }
        } else {
          that.xtermInstanceLocal ? that.xtermInstanceLocal.write(enc.decode(event.data)) : ''
        }
      }
      this.wsInstanceLocal.onopen = function () {
        that.isConnected = true
        that.hasShownDisconnectMessage = false // 连接时重置提示状态
        if (that.isReconnecting) {
          // this.xtermInstanceLocal.write('  \x1b[1;32m' + this.email.split('@')[0] + ', 重连成功 \x1b[m\r\n')
        }
        that.isReconnecting = false // 连接成功后，重置重连状态
        if (that.reconnectInterval) {
          clearInterval(that.reconnectInterval) // 成功连接后清除重连定时器
          that.$message.destroy()
          that.$message.success('重连成功', 5)
          that.reconnectInterval = null
        }
        that.termStatus = '1'
        that.xtermInstanceLocal.focus()
        that.worker.postMessage({ command: 'startPing' })
      }
      this.wsInstanceLocal.onclose = function (event) {
        that.wsState = 2
        that.isConnected = false
        // 非主动关闭
        if (!that.handlerClose) {
          // 检查是否是正常关闭
          if (!event.wasClean && !that.hasShownDisconnectMessage) {
            that.isReconnecting = true
            that.hasShownDisconnectMessage = true
            that.termStatus = '2'
            console.error('WebSocket connection unexpectedly closed')
          }
          // 且目前未有重连定时器设置，启动定时器尝试重连
          if (!that.reconnectInterval) {
            that.reconnectInterval = setInterval(async function () {
              that.wsInstanceLocal = null
              that.wsInit('isAutoConnect')
              that.termFn(that.wsInstanceLocal, true, 'reconnect')
            }, 10000) // 每10秒尝试重连
          }
        } else {
          console.log('主动断开，不自动重连')
        }
      }
      this.wsInstanceLocal.onerror = function (event) {
        that.termStatus = '2'
        that.wsInstanceLocal.close()
      }
    },
    termInit() {
      // 移动端个性化调整
      let termFontSize = this.fontSize
      // const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      if (this.isMobile) {
        termFontSize = 8
        this.$emit('collapseMenu')
      }

      let fontFamily = 'Monospace'
      if (navigator.platform.indexOf('Win') > -1) {
        fontFamily = 'Consolas'
      } else if (/Mac/i.test(navigator.platform)) {
        // fontFamily = 'Courier New'
        fontFamily = 'Menlo'
      }
      this.xtermInstanceLocal = new Terminal({
        scrollback: this.scrollBack, // 终端中的回滚量
        tabStopWidth: 4,
        lineHeight: 1.2,
        fontSize: termFontSize, // 字体大小
        fontFamily: fontFamily,
        cursorBlink: true, // 光标闪烁
        cursorStyle: this.cursorStyle, // 光标样式 underline
        macOptionIsMeta: true,
        theme: {
          // 自定义选中文本的背景和前景色
          selection: 'rgba(69, 170, 252,0.5)', // 橙色高亮
          selectionBackground: 'rgba(69, 170, 252,0.8)', // 选中背景
          selectionForeground: '#000000', // 选中文本的前景色
        },
      })

      this.fitAddon = new FitAddon()
      this.xtermInstanceLocal.loadAddon(this.fitAddon)
      this.searchAddon = new SearchAddon()
      this.xtermInstanceLocal.loadAddon(this.searchAddon)
      this.terminalContainer = this.$refs.myTerm
      this.xtermInstanceLocal.open(this.terminalContainer)
      // this.clearFn()
      this.termFn(this.wsInstanceLocal, true)
    },
    termFn(ws, flag, context = '') {
      let that = this
      const terminalId = this.id
      // this.fitAddon = new FitAddon()
      // this.xtermInstanceLocal.loadAddon(this.fitAddon)
      // this.searchAddon = new SearchAddon()
      // that.xtermInstanceLocal.loadAddon(that.searchAddon)
      // this.fitAddon.activate(this.xtermInstanceLocal)
      // this.fitAddon.fit()
      const user = this.email.split('@')[0]
      this.getTermUserCommand(this.ip, user)
      this.getTermUserCommonCommand(this.email)
      if (flag) {
        this.xtermInstanceLocal.onData(function (data) {
          if (!ws) return
          if (that.syncInput) {
            that.syncCommand(data, that.tabPane.key, that.instanceId)
          }
          if (data == '\x03') {
            if (that.copy !== undefined) {
              navigator.clipboard.writeText(that.copy)
              that.copy = undefined
            } else {
              const msgType = 'TERMINAL_DATA'
              ws.send(JSON.stringify({ terminalId, msgType, data }))
            }
          } else if (data == '\x16') {
            navigator.clipboard.readText().then(clipText => {
              if (clipText !== 'undefined') {
                const msgType = 'TERMINAL_DATA'
                const data = clipText
                ws.send(JSON.stringify({ terminalId, msgType, data }))
              }
            })
          } else if (data == '\x0b') {
            // Ctrl+K 清屏
            const msgType = 'TERMINAL_DATA'
            const data = 'clear\r'
            ws.send(JSON.stringify({ terminalId, msgType, data }))
          } else {
            const msgType = 'TERMINAL_DATA'
            ws.send(JSON.stringify({ terminalId, msgType, data }))
          }
        })

        this.xtermInstanceLocal.onSelectionChange(function () {
          if (that.xtermInstanceLocal.hasSelection()) {
            that.copy = that.xtermInstanceLocal.getSelection()
          }
        })
        if (context == '') {
          this.xtermInstanceLocal.write(
            '  \x1b[1;32m' + this.email.split('@')[0] + ', 欢迎您使用云终端服务CTS(Cloud Terminal Service) \x1b[m\r\n'
          )
        }
      }
      const debouncedResize = debounce(function ({ cols, rows }) {
        const msgType = 'TERMINAL_RESIZE'
        if (cols < 120) {
          cols = 120
        }
        const data = JSON.stringify({ cols, rows })
        ws.send(JSON.stringify({ terminalId, msgType, data }))
      }, 500) //
      this.fitAddon.fit()

      that.xtermInstanceLocal.focus()
      that.xtermInstanceLocal.onResize(debouncedResize)
      // 内容全屏显示-窗口大小发生改变时
      // resizeScreen
      window.addEventListener(
        'resize',
        function () {
          that.fitAddon.fit()
        },
        false
      )
      this.setupKeyboardShortcuts()
    },
    // 设置键盘快捷键
    setupKeyboardShortcuts() {
      this.xtermInstanceLocal.attachCustomKeyEventHandler(e => {
        // 监听 Ctrl+F 快捷键
        if (e) {
          this.needSearch = true
        }
        if (e.ctrlKey && e.key === 'f') {
          e.preventDefault()
          this.openSearchModal()
          return false
        }
        return true
      })
    },
    // 处理搜索输入
    handleSearchInput() {
      // 防抖处理
      this.debouncedSearch()
    },
    // 防抖搜索（简单实现）
    debouncedSearch: (() => {
      let timer = null
      return function () {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          this.performSearch()
        }, 300)
      }
    })(),
    // 打开搜索弹窗
    openSearchModal() {
      this.isSearchModalVisible = true
      this.$nextTick(() => {
        this.$refs.searchInput.focus()
      })
    },
    // 关闭搜索弹窗
    closeSearchModal() {
      this.isSearchModalVisible = false
      this.searchText = ''
      this.searchResultInfo = ''
      this.searchResults = []
      this.currentSearchIndex = 0

      // 清除选中
      this.clearSelection()
    },
    // 清除选中
    clearSelection() {
      try {
        this.xtermInstanceLocal.clearSelection()
      } catch (error) {
        console.error('Error clearing selection:', error)
      }
    },
    // 执行搜索
    performSearch() {
      if (this.needSearch) {
        if (!this.searchText) {
          this.searchResultInfo = ''
          this.searchResults = []
          this.currentSearchIndex = 0
          return
        }

        try {
          // 重置搜索结果和索引
          this.searchResults = []
          this.currentSearchIndex = 0
          // 获取终端的所有文本内容
          const terminalContent = this.getTerminalContent()

          // 创建简单的包含匹配正则
          const searchRegex = new RegExp(
            this.escapeRegExp(this.searchText),
            'gi' // 不区分大小写
          )

          // 查找匹配项
          const lines = terminalContent.split('\n')

          lines.forEach((line, index) => {
            let match
            // 重置正则的 lastIndex
            searchRegex.lastIndex = 0

            while ((match = searchRegex.exec(line)) !== null) {
              this.searchResults.push({
                line: index,
                column: match.index,
                // 存储匹配的确切长度
                length: match[0].length,
              })
            }
          })

          // 更新搜索结果信息
          if (this.searchResults.length > 0) {
            this.searchResultInfo = `找到 ${this.searchResults.length} 个匹配项`

            // 高亮第一个匹配项
            this.highlightSearchResult(0)
          } else {
            this.searchResultInfo = '未找到匹配项'
          }
        } catch (error) {
          console.error('Search error:', error)
          this.searchResultInfo = '搜索失败，请重试'
        }
        this.needSearch = false
      } else {
        this.findNext()
      }
    },

    // 转义正则特殊字符
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    },
    // 获取终端内容
    getTerminalContent() {
      let content = ''
      for (let i = 0; i < this.xtermInstanceLocal.buffer.active.length; i++) {
        const line = this.xtermInstanceLocal.buffer.active.getLine(i)
        if (line) {
          content += line.translateToString() + '\n'
        }
      }
      return content
    },
    // 字符串中包含多少个中文
    chineseLen(str) {
      const reg = /[\u4e00-\u9fa5]/g // 匹配所有中文字符
      const match = str.match(reg) // 返回所有匹配的字符数组
      return match ? match.length : 0
    },
    // 高亮搜索结果
    highlightSearchResult(index) {
      if (index < 0 || index >= this.searchResults.length) return
      const result = this.searchResults[index]
      const len = this.chineseLen(this.searchText) // 中文长度，中文占两个位置
      try {
        const startRow = result.line
        const startCol = result.column
        // 使用精确的匹配长度
        const exactEndCol = startCol + (result.length || this.searchText.length)
        // 精确选择
        this.xtermInstanceLocal.select(
          startCol, // 起始列
          startRow, // 行号
          result.length + len // 结束列（精确匹配长度   中文占两个位置）
        )

        // 滚动到目标行
        this.scrollToLine(startRow)

        // 更新当前搜索索引
        this.currentSearchIndex = index
      } catch (error) {
        console.error('Highlight error:', error)
      }
    },
    scrollToLine(lineIndex) {
      try {
        // 获取终端的缓冲区
        const buffer = this.xtermInstanceLocal.buffer.active

        // 计算理想的滚动位置
        // 尝试将目标行定位到视窗的三分之一处
        const viewportRows = this.xtermInstanceLocal.rows
        let scrollPosition = Math.max(0, lineIndex - Math.floor(viewportRows / 3))

        // 使用更精确的滚动方法
        this.xtermInstanceLocal.scrollLines(scrollPosition - buffer.baseY)

        // 额外的视图刷新和重绘
        this.$nextTick(() => {
          this.xtermInstanceLocal.refresh(0, this.xtermInstanceLocal.rows - 1)
        })
      } catch (error) {
        console.error('Advanced scroll to line error:', error)
      }
    },
    // 查找下一个
    findNext() {
      if (this.searchResults.length === 0) {
        this.performSearch()
        return
      }

      // 循环查找
      this.currentSearchIndex = (this.currentSearchIndex + 1) % this.searchResults.length
      this.highlightSearchResult(this.currentSearchIndex)
    },
    // 查找上一个
    findPrevious() {
      if (this.searchResults.length === 0) {
        this.performSearch()
        return
      }

      // 循环查找
      this.currentSearchIndex = (this.currentSearchIndex - 1 + this.searchResults.length) % this.searchResults.length
      this.highlightSearchResult(this.currentSearchIndex)
    },
    closeWebSocket() {
      if (this.wsInstanceLocal) {
        this.handlerClose = true

        const msgType = 'TERMINAL_HANDLER_CLOSE'
        const data = '主动关闭\r'
        const terminalId = this.id
        this.isGetPwd = false
        // 在关闭前发送一条消息
        this.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
        // 等待消息发送完成后再关闭连接（可根据需要添加延时）
        setTimeout(() => {
          this.wsInstanceLocal.close()
        }, 100) // 延时 100 毫秒，确保消息发送
      }
      if (this.reconnectInterval) {
        clearInterval(this.reconnectInterval)
        this.reconnectInterval = null
      }
    },
    getMessage(id, type, data) {
      return JSON.stringify({
        id,
        type,
        data,
      })
    },
    fireEvent(e) {
      window.dispatchEvent(e)
    },
    setQuickVimStatus(data) {
      const msgType = 'TERMINAL_ACTION_VIM_STATUS'
      const terminalId = this.id
      this.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
    },
    // 接受
    dispatch(term, msgData, ws) {
      if (msgData === undefined) {
        return
      }
      let msg = JSON.parse(msgData)
      const terminalId = msg.terminalId
      let msgType = 'TERMINAL_INIT'
      switch (msg.msgType) {
        case 'CONNECT':
          let cols = term.cols
          let rows = term.rows
          const data = JSON.stringify({ cols, rows })
          ws.send(JSON.stringify({ terminalId, msgType, data }))
          // ws.send(this.getMessage(terminalId, 'TERMINAL_INIT', JSON.stringify({ cols, rows })))
          // resizeTerminal()
          break
        case 'CLOSE':
          this.termStatus = '2'
          this.wsInstanceLocal.close()
          this.fireEvent(new Event('CLOSE', {}))
          break
        case 'PING':
          break
        case 'TERMINAL_ACTION_PWD':
          const regex = /\/[\w-\/]+/g
          const match = msg.data.match(regex)
          if (match) {
            const extractedText = match[0]
            this.serverPwd = extractedText
            this.isGetPwd = true
          } else {
            console.log('Pwd No match found.')
          }
          break
        case 'TERMINAL_ACTION_VIM':
          const that = this
          const regexPath = /^\s*\/[\w\-./&\u4E00-\u9FFF]+/g
          const matchPath = msg.data.match(regexPath)
          let filePath = ''
          msgType = 'TERMINAL_DATA'
          if (matchPath) {
            filePath = matchPath[0]
            if (!this.openEditors.find(editor => editor.filePath === filePath)) {
              if (this.quickVimStatus === -1) {
                const content = h('div', [
                  h(
                    'span',
                    {
                      style: {
                        fontSize: '20px',
                        margin: '0',
                        paddingTop: '5px',
                      },
                    },
                    '是否使用格式化编辑器打开文件？'
                  ),
                  h(
                    'p',
                    {
                      style: {
                        fontSize: '12px',
                        color: '#666',
                        margin: '0',
                        paddingTop: '5px',
                      },
                    },
                    '提示：可在云终端个人配置页选择开启或关闭此功能'
                  ),
                ])
                this.$confirm({
                  title: '格式化VIM编辑器',
                  content: content,
                  okText: '是',
                  cancelText: '否',
                  width: '25%',
                  onOk() {
                    // 修改命令及配置TERMINAL_ACTION_VIM
                    that.quickVimStatus = 1
                    that.setQuickVimStatus('true')
                    const data = '\r'
                    that.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
                    that.submitData(filePath)
                  },
                  onCancel() {
                    that.setQuickVimStatus('false')
                    const data = msg.originData + '\r'
                    that.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
                  },
                })
              } else {
                const data = '\r'
                that.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
                that.submitData(filePath)
              }
            }
          } else {
            console.log('Pwd No match found.')
          }
          break
      }
    },
    getTermUserCommand(ip, user) {
      this.userCommandReq.ip = ip
      this.userCommandReq.user = user
      termUserCommand(this.userCommandReq).then(res => {
        if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
          this.userCommandData = res.Data.data
        }
      })
    },
    getTermUserCommonCommand(email) {
      this.userCommonCommandReq.email = email
      termUserCommonCommand(this.userCommonCommandReq).then(res => {
        if (res.Data.hasOwnProperty('data') && res.Data.data != null) {
          this.userCommonCommandData = res.Data.data
        }
      })
    },
    commandInput(data) {
      console.log(data, 'data')
      // const msgType = 'TERMINAL_SUGGEST_DATA'
      const msgType = 'TERMINAL_DATA'
      const terminalId = this.id
      this.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
    },
    addCommonCommand() {
      this.vueInstance ? (this.copy = this.vueInstance.copy) : ''
      const commandText = this.copy
      this.commandData.email = this.email
      this.commandData.command = commandText
      this.commandData.comment = commandText
      this.vueInstance ? (this.vueInstance.copy = undefined) : ''
      this.copy = undefined
      createTermCommonCommand(this.commandData).then(res => {
        if (res === undefined) {
          this.$message.error('快捷命令：' + commandText + '添加失败,后端接口错误，请联系运维开发排查~')
        } else {
          this.userCommonCommandData.push({ title: '命令' + commandText, value: commandText + '\r' })
          this.$message.success('快捷命令：' + commandText + '添加成功')
        }
      })
    },
    disconnectCTS() {
      this.termStatus = '2'
      this.handlerClose = true
      this.wsInstanceLocal.close()
    },
    connectCTS() {
      this.termStatus = '1'
      this.wsInstanceLocal = null
      this.wsInit('isAutoConnect')
      this.termFn(this.wsInstanceLocal, true, 'reconnect')
    },
    cloudIDE() {
      let routeUrl = this.$router.resolve({ path: '/devops/ide' })
      window.open(routeUrl.href, '_blank')
    },
    beforeFileUpload(file, filelist) {
      let that = this
      return new Promise((resolve, reject) => {
        const isLt2KB = file.size / 1024 / 1024 < 200
        if (!isLt2KB) {
          that.$message.error('上传文件大于200MB!')
          reject(false)
        } else {
          resolve(true)
        }
      }).finally(() => {})
    },
    toggleFullscreen(check) {
      this.isFull = check
      // this.isFull = !this.isFull
      let main = document.getElementsByClassName('terminalBox')[0].parentElement
      let terms = document.getElementsByClassName('xterm')
      if (this.isFull) {
        this.toogleFull()
        main.style.margin = 0
        for (let i = 0; i < terms.length; i++) {
          terms[i].style.height = '100%'
        }
      } else {
        if (this.from != 'wxApp') {
          this.toogleNormal()
        }
        for (let i = 0; i < terms.length; i++) {
          terms[i].style.height = '100%'
        }
        main.style.margin = 24 + 'px'
      }
      this.$emit('collapseMenu', this.isFull)
    },
    // 文件上传部分
    onDrop(e) {
      this.readyUpload()
      uniqueId = this.id
      // 获取文件对象
      e.preventDefault()
      let file = e.dataTransfer.files[0]
      this.uploadFileSync(file, 'upload-file', file.name)
    },
    onDragOver(e) {
      e.preventDefault()
    },
    async readyUpload() {
      await this.getPwd()
    },
    uploadFile() {
      this.readyUpload()
      uniqueId = this.id

      document.getElementById('file-upload').click()
    },
    uploadDir() {
      this.readyUpload()
      uniqueId = this.id
      window.document.getElementById('dir-upload').click()
    },
    handleUploadFile() {
      const file = window.document.getElementById('file-upload').files[0]
      this.uploadFileSync(file, 'upload-file', file.name)
    },
    handleUploadDir() {
      const files = window.document.getElementById('dir-upload').files
      const dir = files[0].webkitRelativePath.split('/')[0]
      this.fileList.push({ dirName: dir, fileList: [] })
      for (let i = 0; i < files.length; i++) {
        const relativePath = files[i].webkitRelativePath
        this.uploadFileSyncDir(files[i], 'upload-dir', relativePath)
      }
    },
    // 通用
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    // 获取dir列表
    getDirArr(dirName) {
      const filteredData = this.fileList.filter(item => item.dirName === dirName)
      return filteredData.map(item => item.fileList)[0]
    },
    // 将 ArrayBuffer 转换为 Base64 编码的字符串
    arrayBufferToBase64(buffer) {
      let binary = ''
      let bytes = new Uint8Array(buffer)
      let len = bytes.byteLength
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return window.btoa(binary)
    },
    // 文件启动上传
    startProgress(sftpWs) {
      let duration = null
      let tips = ''
      let errMsg = ''
      if (!sftpWs.isAdmin) {
        tips = '由于您没有该机器sudo权限，文件将保存到您的家目录中\n'
      }
      const fileSizeStr = this.getFileSizeTitle(sftpWs.fileSize)
      // 处理进度条和msg
      let message = ''
      // 初始
      switch (sftpWs.fileStat) {
        case 'prepar':
          message = '准备中...'
          break
        case 'init':
          message = '初始化中...'
          break
        case 'uploading':
          message = `正在上传文件: ${sftpWs.fileName} ！`
          break
        case 'waiting':
          message = '向目标机器传输中...'
          sftpWs.progress = 99.99
          break
        case 'success':
          message = `文件 ${sftpWs.fileName}上传成功！`
          sftpWs.progress = 100
          duration = 2
          break
        case 'stop':
          message = `文件 ${sftpWs.fileName}上传失败！`
          errMsg = '上传已中断!'
          tips = ''
          duration = 5
          break
        case 'error':
          message = `文件 ${sftpWs.fileName}上传失败！`
          errMsg = '上传错误,请联系管理员!'
          tips = ''
          duration = 5
          break
        case 'timeout':
          message = `文件 ${sftpWs.fileName}上传失败！`
          errMsg = '连接超时,上传失败!'
          tips = ''
          duration = 5
          break
        default:
          errMsg = '未知错误,请联系管理员!'
          tips = ''
      }
      this.$notification.open({
        message: message,
        duration: duration,
        key: sftpWs.fileName,
        placement: 'bottomRight',
        description: () => {
          return h('div', null, [
            h(
              'a-row',
              {
                type: 'flex',
                style: 'margin: 25px 0 10px;',
              },
              [
                h(
                  'a-col',
                  {
                    flex: 'auto',
                  },
                  [
                    h('progress', {
                      id: 'fileProgress',
                      value: sftpWs.progress,
                      max: 100,
                      style: 'width:82%',
                    }),
                    h(
                      'span',
                      {
                        style: 'olor: rgba(0,0,0,.45);font-size: 1em;margin-left: 12px;',
                      },
                      `${sftpWs.progress}%`
                    ),
                  ]
                ),
              ]
            ),
            h(
              'div',
              {
                style: 'margin-bottom:20px;font-size:13px',
              },
              `文件大小:${fileSizeStr}`
            ),
            h(
              'div',
              {
                style: 'margin-bottom:5px;font-size:10px;color:red',
              },
              tips
            ),
            h(
              'div',
              {
                style: 'margin-bottom:5px;font-size:15px;color:red',
              },
              errMsg
            ),
          ])
        },
        onClose: () => {
          // fileStat分析1成功无需处理，只有为0的时候,手动断开因改为3
          if (sftpWs.fileStat === 'prepar') {
            sftpWs.fileStat = 'break'
            this.$message.info(`您已取消上传!"${sftpWs.fileName}"`, 3)
            return
          }
          if (sftpWs.fileStat === 'uploading' || sftpWs.fileStat === 'init' || sftpWs.fileStat === 'waiting') {
            sftpWs.fileStat = 'stop'
          }
          sftpWs.close()
          // cancel()
          if (sftpWs.fileStat === 'stop') {
            this.$message.info(`您已取消上传!"${sftpWs.fileName}"`, 3)
          } else if (sftpWs.fileStat === 'success') {
            this.$message.success(`文件上传成功!"${sftpWs.fileName}"`, 1)
          } else {
            this.$message.error(`文件上传失败!"${sftpWs.fileName}"`, 3)
          }
        },
      })
    },
    // 文件夹上传
    getFileSizeTitle(fileSize) {
      let fileSizeStr = fileSize / 1024
      if (fileSizeStr >= 1024 * 1024) {
        fileSizeStr = (fileSizeStr / 1024 / 1024).toFixed(2) + 'GB'
      } else if (fileSizeStr >= 1024) {
        fileSizeStr = (fileSizeStr / 1024).toFixed(2) + 'MB'
      } else {
        fileSizeStr = fileSizeStr.toFixed(2) + 'KB'
      }
      return fileSizeStr
    },
    // 根据进度和状态字段a获取对应的状态信息
    getStatusForProgress(fileWs) {
      let res = { text: '初始化中', color: 'gray' }
      switch (fileWs.fileStat) {
        case 'prepar':
          res = { text: '准备中...', color: 'grey' }
          break
        case 'init':
          res = { text: '初始化中', color: 'grey' }
          break
        case 'uploading':
          res = { text: '上传中...', color: 'blue' }
          break
        case 'waiting':
          fileWs.progress = 99.99
          res = { text: '向目标机器传输中...', color: 'blue' }
          break
        case 'success':
          fileWs.progress = 100
          res = { text: '上传成功!', color: 'green' }
          break
        case 'stop':
          res = { text: '上传中断,上传失败!', color: 'red' }
          break
        case 'error':
          res = { text: '上传错误,上传失败!', color: 'red' }
          break
        case 'timeout':
          res = { text: '上传超时,上传失败!', color: 'red' }
          break
        default:
          res = { text: '未知状态', color: 'gray' }
      }
      return res
    },
    startProgressDir(sftpWs) {
      let duration = null
      let tips = ''
      if (!sftpWs.isAdmin) {
        tips = '由于您没有该机器sudo权限，文件将保存到您的家目录中\n'
      }
      // 处理最后一个文件中间态为uploading,waiting,init
      const dirName = sftpWs.fileName.split('/')[0]
      let fileLists = this.getDirArr(dirName)
      const isAllSuccess = fileLists.some(item => item.fileStat !== 'success')
      const hasErrorEqual = fileLists.some(
        item => item.fileStat === 'uploading' || item.fileStat === 'waiting' || item.fileStat === 'init'
      )
      if (hasErrorEqual) {
        for (let i = 0; i < fileLists.length; i++) {
          if (fileLists[i].fileStat === 'error' || fileLists[i].fileStat === 'timeout') {
            const removed = fileLists.splice(i, 1)
            fileLists.unshift(removed[0])
            break
          }
        }
      }
      if (!isAllSuccess) {
        duration = 3
      }
      // 处理进度条和msg
      // let uploadProgress = Math.min(((currentOffset / fileSize) * 100).toFixed(2), 100)
      this.$notification.open({
        message: `文件夹"${dirName}"上传中...`,
        duration: duration,
        key: dirName,
        placement: 'bottomRight',
        description: () => {
          return h('div', null, [
            h(
              'ul',
              { style: 'text-align: left; margin: 0; padding: 0; width: 100%; overflow: auto; max-height: 200px;' },
              fileLists.map((fileWs, index) => {
                const status = this.getStatusForProgress(fileWs)
                return h('li', null, [
                  h('span', { style: 'font-size: 12px' }, `${fileWs.fileName}`),
                  h('div', { style: 'display: flex; align-items: center' }, [
                    h('progress', {
                      value: fileWs.progress,
                      max: 100,
                    }),
                    h(
                      'span',
                      { style: 'font-size: 12px;margin-left: 12px;' },
                      `${fileWs.progress}%` // 根据进度值渲染文本
                    ),
                    h(
                      'span',
                      {
                        style: `color: ${status.color};font-size: 12px;margin-left: 12px;`, // 根据状态设置文本颜色
                      },
                      status.text
                    ),
                  ]),
                ])
              })
            ),
            h(
              'div',
              {
                style: 'margin-bottom:5px;font-size:10px;color:red',
              },
              tips
            ),
          ])
        },
        onClose: () => {
          for (let i = 0; i < fileLists.length; i++) {
            if (
              fileLists[i].fileStat === 'uploading' ||
              fileLists[i].fileStat === 'init' ||
              fileLists[i].fileStat === 'waiting'
            ) {
              fileLists[i].fileStat = 'stop'
            }
            fileLists[i].close()
            // 去除通知
            // if (this.fileList[i].fileStat === 'stop') {
            //   this.$message.info(`您已取消上传!"${this.fileList[i].fileName}"`, 3)
            // } else if (this.fileList[i].fileStat === 'success') {
            //   this.$message.success(`文件上传成功!"${this.fileList[i].fileName}"`, 1)
            // } else {
            //   this.$message.error(`文件上传失败!"${this.fileList[i].fileName}"`, 3)
            // }
          }
          // 最后手动清理
          fileLists = []
        },
      })
    },
    async uploadFileSync(file, wsType, fileName) {
      const tempWs = {
        fileStat: 'prepar',
        fileName: fileName,
        isAdmin: true,
        fileSize: file.size,
        progress: 0,
      }
      this.startProgress(tempWs)
      await this.sleep(2000)
      if (tempWs.fileStat === 'break') {
        return
      }
      const sftpWs = this.creteSftp(fileName, file.size, wsType)
      // 开始通信
      // 初始化
      this.startProgress(sftpWs)
      const waitForWebSocketConnection = async (socket, callback, file) => {
        if (socket.readyState === WebSocket.OPEN) {
          // WebSocket 已连接，执行回调函数
          await callback(socket, file)
        } else {
          // WebSocket 未连接， 等待 1 秒后再次尝试
          setTimeout(async () => {
            await waitForWebSocketConnection(socket, callback, file)
          }, 1000)
        }
      }
      await waitForWebSocketConnection(sftpWs, this.uploadFileReader, file)
    },
    async uploadFileReader(sftpWs, file) {
      sftpWs.fileStat = 'uploading'
      const reader = new FileReader()
      // 计算文件大小和块大小
      const fileSize = file.size
      // 后端写入太慢, 进度不统一,约定每上传2MB, sleep 1s,(与后端缓存区有关)
      const allSleepTime = Math.floor(fileSize / 1024 / 1024 / 2)
      let chunkSize = 1024 * 1024
      if (fileSize / 1024 / 1024 < 500 && fileSize / 1024 / 1024 > 10) {
        chunkSize = 1024 * 512
      } else if (fileSize / 1024 / 1024 > 500) {
        chunkSize = 1024 * 128
      }
      // 计算每次休息多久(ms)
      const needSleepTime = Math.floor((allSleepTime / (fileSize / chunkSize)) * 1000)
      let offset = 0
      const uploadChunk = chunk => {
        const msg = { fileData: chunk, msgType: 'chunk' }
        if (sftpWs.fileStat === 'uploading') {
          sftpWs.send(JSON.stringify(msg))
        }
      }
      // 监听 FileReader 的 load 事件，读取文件并上传块数据
      reader.onload = () => {
        const chunk = reader.result
        const baseChunk = this.arrayBufferToBase64(chunk)
        // 上传当前块数据
        uploadChunk(baseChunk)
        offset += chunkSize
        if (offset < fileSize) {
          // readNextChunk()
          setTimeout(readNextChunk, needSleepTime)
        } else {
          // 当所有块上传完成时，发送完成消息
          const msg = { msgType: 'finish' }
          if (sftpWs.fileStat === 'uploading') {
            sftpWs.send(JSON.stringify(msg))
            sftpWs.fileStat = 'waiting'
            this.startProgress(sftpWs)
          }
        }
        // 状态0代表上传
        if (sftpWs.fileStat === 'uploading') {
          sftpWs.progress = Math.min(((offset / sftpWs.fileSize) * 100).toFixed(2), 100)
          this.startProgress(sftpWs)
        }
      }
      const readNextChunk = () => {
        const blob = file.slice(offset, offset + chunkSize)
        reader.readAsArrayBuffer(blob)
      }
      // this.startProgress(file.name)
      readNextChunk()
    },
    // DIR
    async uploadFileSyncDir(file, wsType, fileName) {
      // if (file.size === 0) {
      //   this.$message.success(`文件"${fileName}"大小为0KB,已跳过`, 3)
      //   return
      // }
      const sftpWs = this.creteSftp(fileName, file.size, wsType)
      // 添加到文件列表
      const dirName = fileName.split('/')[0]
      this.fileList.forEach(item => {
        if (item.dirName === dirName) {
          item.fileList.push(sftpWs)
        }
      })
      // 初始化
      this.startProgressDir(sftpWs)
      // 等待websocket连接成功然后发送文件
      const waitForWebSocketConnection = async (socket, callback, file) => {
        if (socket.readyState === WebSocket.OPEN) {
          // WebSocket 已连接，执行回调函数
          await callback(socket, file)
        } else {
          // WebSocket 未连接， 等待 1 秒后再次尝试
          setTimeout(async () => {
            await waitForWebSocketConnection(socket, callback, file)
          }, 1000)
        }
      }
      await waitForWebSocketConnection(sftpWs, this.uploadDirReader, file)
    },
    async uploadDirReader(sftpWs, file) {
      sftpWs.fileStat = 'uploading'
      const reader = new FileReader()
      // 计算文件大小和块大小
      const fileSize = file.size
      // 后端写入太慢, 进度不统一,约定每上传2MB, sleep 1s,(与后端缓存区有关)
      const allSleepTime = Math.floor(fileSize / 1024 / 1024 / 2)
      let chunkSize = 1024 * 1024
      if (fileSize / 1024 / 1024 < 500 && fileSize / 1024 / 1024 > 10) {
        chunkSize = 1024 * 512
      } else if (fileSize / 1024 / 1024 > 500) {
        chunkSize = 1024 * 128
      }
      // 计算每次休息多久(ms)
      const needSleepTime = Math.floor((allSleepTime / (fileSize / chunkSize)) * 1000)
      let offset = 0
      const uploadChunk = chunk => {
        const msg = { fileData: chunk, msgType: 'chunk' }
        if (sftpWs.fileStat === 'uploading') {
          sftpWs.send(JSON.stringify(msg))
        }
      }
      // 监听 FileReader 的 load 事件，读取文件并上传块数据
      reader.onload = () => {
        const chunk = reader.result
        const baseChunk = this.arrayBufferToBase64(chunk)
        // 上传当前块数据
        uploadChunk(baseChunk)
        offset += chunkSize
        if (offset < fileSize) {
          setTimeout(readNextChunk, needSleepTime)
        } else {
          // 当所有块上传完成时，发送完成消息
          const msg = { msgType: 'finish' }
          if (sftpWs.fileStat === 'uploading') {
            sftpWs.send(JSON.stringify(msg))
            sftpWs.fileStat = 'waiting'
            this.startProgressDir(sftpWs)
          }
        }
      }
      const readNextChunk = () => {
        const blob = file.slice(offset, offset + chunkSize)
        reader.readAsArrayBuffer(blob)
      }
      readNextChunk()
    },
    // Sftp 部分
    getTimeout(fileSize) {
      let timeout = 0
      // 上传时间+写入时间（预估)
      if (fileSize / 1024 / 1024 < 10) {
        timeout = 60 * 1000
      } else {
        timeout = (fileSize / 1024 / 1000 + fileSize / 1024 / 200) * 1000
      }
      return timeout
    },
    creteSftp(fileName, fileSize, wsType) {
      let wsUrl = 'wss://termws.intsig.net/sftp/ws?terminalId=' + uniqueId
      const env = judgeEnv()
      if (env !== 'ONLINE') {
        wsUrl = 'wss://termws-test.intsig.net/sftp/ws?terminalId=' + uniqueId
      }
      const sftpWs = new WebSocket(wsUrl)
      // 设置必要参数
      sftpWs.fileName = fileName
      sftpWs.fileSize = fileSize
      sftpWs.wsType = wsType
      sftpWs.progress = 0
      //  设置状态init初始化,uploading正在上传,waiting等待服务器返回,success上传成功，stop中断上传, error上传错误，timeout超时
      sftpWs.fileStat = 'init'
      sftpWs.isAdmin = true
      sftpWs.onopen = function () {}
      sftpWs.onmessage = event => {
        if (this.checkJson(event.data)) {
          event = JSON.parse(event.data)
          switch (event.SftpType) {
            case 'connect':
              this.sftpReady = true
              const getRoleMsg = {}
              getRoleMsg.msgType = 'get-role'
              getRoleMsg.filePath = this.serverPwd
              getRoleMsg.fileName = fileName
              getRoleMsg.fileSize = fileSize.toString()
              sftpWs.send(JSON.stringify(getRoleMsg))
              const sftpMsg = {}
              sftpMsg.msgType = wsType
              sftpMsg.filePath = this.serverPwd
              sftpMsg.fileName = fileName
              sftpMsg.fileSize = fileSize.toString()
              sftpWs.send(JSON.stringify(sftpMsg))
              break
            case 'upload-file':
              // let uploadFileTimeout
              // uploadFileTimeout = setTimeout(() => {
              //   // 上传超时上设置状态
              //   sftpWs.fileStat = 'timeout'
              //   sftpWs.close()
              // }, this.getTimeout(sftpWs.fileSize))
              // sftpWs.uploadTimeout = uploadFileTimeout
              break
            case 'upload-dir':
              // let uploadDirTimeout
              // uploadDirTimeout = setTimeout(() => {
              //   // 上传超时上设置状态
              //   sftpWs.fileStat = 'timeout'
              //   sftpWs.close()
              // }, this.getTimeout(sftpWs.fileSize))
              // sftpWs.uploadTimeout = uploadDirTimeout
              break
            case 'upload-result':
              clearTimeout(sftpWs.uploadTimeout)
              sftpWs.fileStat = 'success'
              if (event.SftpCode !== 200) {
                sftpWs.fileStat = 'error'
                sftpWs.close()
              }
              if (sftpWs.wsType === 'upload-file') {
                this.startProgress(sftpWs)
              } else {
                this.startProgressDir(sftpWs)
              }
              break
            case 'get-role-result':
              const SftpRoleDataRes = JSON.parse(event.SftpData)
              sftpWs.isAdmin = SftpRoleDataRes.isAdmin
              if (sftpWs.wsType === 'upload-file') {
                this.startProgress(sftpWs)
              } else {
                this.startProgressDir(sftpWs)
              }
              let uploadDirTimeout
              uploadDirTimeout = setTimeout(() => {
                // 上传超时上设置状态
                sftpWs.fileStat = 'timeout'
                sftpWs.close()
              }, this.getTimeout(sftpWs.fileSize))
              sftpWs.uploadTimeout = uploadDirTimeout
          }
        }
      }
      sftpWs.onerror = e => {
        clearTimeout(sftpWs.uploadTimeout)
        let funcProgress = this.startProgress
        if (sftpWs.wsType === 'upload-dir') {
          funcProgress = this.startProgressDir
        }
        // 手动取消会主动修改状态为stop，如果为正常状态，触发onclose视为异常，状态修改为error
        if (sftpWs.fileStat === 'uploading' || sftpWs.fileStat === 'init' || sftpWs.fileStat === 'waiting') {
          sftpWs.fileStat = 'error'
        }
        if (sftpWs.fileStat !== 'success') {
          funcProgress(sftpWs)
        }
      }
      sftpWs.onclose = e => {
        clearTimeout(sftpWs.uploadTimeout)
        let funcProgress = this.startProgress
        if (sftpWs.wsType === 'upload-dir') {
          funcProgress = this.startProgressDir
        }
        // 手动取消会主动修改状态为stop，如果为正常状态，触发onclose视为异常，状态修改为error
        if (sftpWs.fileStat === 'uploading' || sftpWs.fileStat === 'init' || sftpWs.fileStat === 'waiting') {
          sftpWs.fileStat = 'error'
        }
        if (sftpWs.fileStat !== 'success' && sftpWs.fileStat !== 'stop') {
          funcProgress(sftpWs)
        }
      }
      return sftpWs
    },
    checkJson(str) {
      if (typeof str === 'string') {
        try {
          let obj = JSON.parse(str)
          return !!(typeof obj == 'object' && obj)
        } catch (e) {
          return false
        }
      }
    },
    getPwd() {
      const msgType = 'TERMINAL_ACTION_PWD'
      const data = 'pwd\r'
      const terminalId = this.id
      this.isGetPwd = false
      this.wsInstanceLocal.send(JSON.stringify({ terminalId, msgType, data }))
      return new Promise((resolve, reject) => {
        let checkReply = setInterval(() => {
          if (this.isGetPwd) {
            clearInterval(checkReply)
            resolve()
          }
        }, 100)
      })
    },
    setWaterMark() {
      setWaterMarkWhite(this.email, this.ip)
    },
  },
  mounted() {
    this.getVimConfig()
    if (this.localInstanceId) {
      this.instanceId = this.localInstanceId
    } else {
      this.instanceId = this.encrypt(Math.random() * 10000 * Math.random())
    }
    const instance = getCurrentInstance()
    eventBus.registerComponent(instance, this.tabPane.key)
    this.instance = this
    let hide = null
    window.addEventListener('online', () => {
      console.log('网络已连接')
      // 执行网络恢复后需要执行的操作
      if (this.wsState == 1 && this.wsInstanceLocal.readyState == 1) {
        this.$message.destroy()
        this.$message.success('重连成功', 5)
      }
      this.wsState = 1
    })
    window.addEventListener('offline', () => {
      this.updateNetworkStatus()
      console.log('网络已断开')
      this.handlerClose = false

      // this.$message.loading('网络异常,正在重连...', 0)
      this.$message.loading({ content: '网络异常,正在重连...', key: 'updatable', duration: 0 })
    })
    const email = store.getters.email
    if (email) {
      setWaterMarkWhite(email, this.ip)
    }
    setTimeout(() => {
      if (this.isMobile) {
        let header = document.getElementsByTagName('header')[0]
        header.style.display = 'none'
        let main = document.getElementsByClassName('terminalBox')[0].parentElement
        main.style.margin = 0
      }

      if (noc.$('.ant-drawer-mask')[0] && this.isMobile) {
        noc.$('.ant-drawer-mask')[0].parentElement.style.display = 'none'
        noc.$('.ant-drawer-mask')[0].parentElement.style.display = 'none'
        noc.store.setItem('SIDEBAR_TYPE', String(true))
      }
      // setTimeout(() => {
      //   let layout = document.getElementById('myFooters').parentNode
      // })
    })
    this.isFull = this.fullScreen
    let terms = document.getElementsByClassName('xterm')
    if (this.isFull) {
      this.toogleFull()
      for (let i = 0; i < terms.length; i++) {
        terms[i].style.height = '100%'
      }
    }
    if (!this.isSplit) {
      // 获取终端容器的 DOM 元素
      this.attachTerminal()
    }
    this.isFull = true
    if (this.isMobile || this.from == 'wxApp') {
      for (let i = 0; i < terms.length; i++) {
        terms[i].style.height = '100%'
      }
    }
  },
  beforeUnmount() {
    if (this.xtermInstanceLocal) {
      this.xtermInstanceLocal.dispose()
    }
    if (this.xtermInstanceLocal && !this.isSplit) {
      // 如果不是由于分屏而销毁，则销毁 xterm 实例
      this.xtermInstanceLocal.dispose()
    }
    this.handlerClose = true
    if (this.wsInstanceLocal && !this.isSplit) {
      // 在关闭整个组件时，关闭 websocket 连接
      this.wsInstanceLocal.close()
      this.wsInstanceLocal = null
    }
    // socket.value.onopen = null
    // this.wsInstanceLocal.onmessage = null
    // this.wsInstanceLocal.onerror = null
    // this.wsInstanceLocal.onclose = null
    // this.wsInstanceLocal = null
  },
  unmounted() {
    // 组件卸载时移除监听器
    window.removeEventListener('online', this.updateNetworkStatus)
    window.removeEventListener('offline', this.updateNetworkStatus)
  },
}
</script>

<style lang="less" scoped>
/deep/ .xterm-viewport::-webkit-scrollbar {
  display: none !important;
}

.xterm {
  height: 100%;
  // overflow: hidden;
  position: relative;

  /deep/ .terminal {
    height: 100%;
    overflow: hidden;
  }
}

.xterm::-webkit-scrollbar {
  display: none;
}

.header {
  align-items: center;
  padding: 0 16px;
  background: #fff;
  display: flex;
  height: 32px;
}

.footer {
  padding: 3px 12px;
  background: #fff;
  display: flex;
}

.terminal-container {
  min-width: 2000px;
  /* 设置最大高度，超出部分将显示垂直滚动条 */
  overflow-y: auto;
  /* 当内容过高时显示垂直滚动条 */
}

.shorts {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin: 0;

  .shortCut {
    display: inline-block;
    width: 20%;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
  }
}

.quickKey {
  background-color: #347ef6;
  pointer-events: auto;
  position: absolute;
  text-align: center;
  line-height: 1.2em;
  top: 300px;
  width: 20px;
  height: auto;
  padding: 10px 0;
  right: 0;
  border-radius: 4px;
  color: #fff;
  display: flex;
  align-items: center;
  z-index: 99999;
  cursor: pointer;
}
</style>
<style scoped>
/* 与之前相同的样式 */
.terminal-container {
  position: relative;
  width: 100%;
  height: 100%;
  border: 1px solid #ccc;
}

.toolbar {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
}

.toolbar button {
  margin-left: 5px;
}

.terminal-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #1e1e1e;
  color: #fff;
  overflow: auto;
}

.splitpanes {
  width: 100%;
  height: 100%;
}

/* 自定义分隔条样式 */
.splitpanes-default-theme .splitpanes__splitter {
  background-color: #555;
}

.splitpanes-default-theme .splitpanes__splitter:hover {
  background-color: #777;
}

.termBoxs::-webkit-scrollbar {
  display: none;
}

.v-contextmenu-divider {
  width: 1px !important;
}

.syncFlag {
  display: inline-block;
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 9999;
  width: 30px;
  height: 30px;

  img {
    width: 30px;
    height: 30px;
  }

  /* background: pink; */
}

.search-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.search-modal-content {
  background: #2c2c2c;
  padding: 4px;
  border-radius: 8px;
  width: 320px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}

.search-input-wrapper {
}

.search-input {
  height: 25px;
  width: 170px;
  padding: 10px;
  background: #2c2c2c;
  color: #d4d4d4;
  border-radius: 4px;
  border: none;
  border: none;
  border-width: 0;

  /* 去除轮廓 */
  outline: none;

  /* 去除浏览器默认样式 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  /* 去除聚焦和活动状态的样式 */

  &:focus,
  &:active {
    border: none;
    outline: none;
    box-shadow: none;
  }
}

.search-controls {
  display: flex;
  align-items: center;
  /* flex-direction: column; */
}

.search-option {
  /* margin-bottom: 10px; */
  margin-right: 10px;
  color: #d4d4d4;
}

.search-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.search-buttons button {
  padding: 8px 15px;
  background: #444;
  color: #d4d4d4;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.optImg {
  width: 14px;
  height: 14px;
  margin-left: 8px;
  margin-bottom: 6px;
}

.search-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-result-info {
  margin-top: 15px;
  color: #d4d4d4;
  text-align: center;
}

/* 自定义终端选中文本的高亮颜色 */
.xterm .xterm-selection {
  background-color: rgba(255, 165, 0, 0.5) !important;
  /* 半透明橙色 */
}

/* 如果想要更精细的控制 */
.xterm-selection-layer {
  background-color: rgba(255, 0, 0, 0.3) !important;
  /* 半透明红色 */
}

.file-vim-content {
  background: #2c2c2c;
  padding: 4px;
  border-radius: 8px;
  width: 1000px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 12px;
  z-index: 1000;
}
</style>
<style>
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
  position: 123456 j;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}

.suggestions {
  position: absolute;
  /* 绝对定位 */
  background: #333;
  color: white;
  padding: 4px;
  min-width: 200px;
  border-radius: 4px;
  font-family: monospace;
  z-index: 1000;
  /* 确保提示框在终端内容上方 */
}

.suggestions div {
  font-size: 12px;

  cursor: pointer;
}

.suggestions div.active {
  background: #555;
}
</style>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: #1e1e1e;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  min-height: 40px;
  padding: 0 10px;
  background-color: #2d2d2d;
  border-bottom: 1px solid #3d3d3d;
}

.monaco-editor-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.save-btn {
  background-color: #0e639c;
  color: white;
}

.save-btn:hover {
  background-color: #1177bb;
}

.close-btn {
  background-color: transparent;
  color: #cccccc;
}

.close-btn:hover {
  background-color: #3d3d3d;
}

.btn-icon {
  margin-right: 6px;
  font-size: 14px;
}
</style>
