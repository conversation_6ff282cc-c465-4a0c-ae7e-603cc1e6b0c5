<template>
  <a-layout>
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible style="background: #fff; padding: 0">
      <div>
        <menu-unfold-outlined v-if="collapsed" class="trigger" @click="() => (collapsed = !collapsed)" />
        <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
      </div>
      <a-menu v-model:selectedKeys="selectedKeys" theme="light" mode="inline">
        <a-menu-item key="dashboard" @click="add({ title: '仪表盘', key: 'dashboard' })">
          <template #icon>
            <DashboardOutlined />
          </template>
          仪表盘
        </a-menu-item>
        <a-sub-menu key="sub1">
          <template #title>
            <span>
              <user-outlined />
              <span>用户设置</span>
            </span>
          </template>
          <a-menu-item key="connSetting" @click="add({ title: '连接配置', key: 'connSetting' })">连接配置</a-menu-item>
          <a-menu-item key="fontSetting" @click="add({ title: '字体设置', key: 'fontSetting' })">字体设置</a-menu-item>
          <a-menu-item key="termSetting" @click="add({ title: '终端设置', key: 'termSetting' })">终端设置</a-menu-item>
          <a-menu-item key="hotKey" @click="add({ title: '快捷键', key: 'hotKey' })">快捷键</a-menu-item>
          <a-menu-item key="menuSetting" @click="add({ title: '自定义菜单', key: 'menuSetting' })">自定义菜单 </a-menu-item>
          <a-menu-item key="commonCommand" @click="add({ title: '快速命令', key: 'commonCommand' })"> 快速命令 </a-menu-item>
          <a-menu-item key="termGateway" @click="add({ title: '代理设置', key: 'termGateway' })">代理设置</a-menu-item>
        </a-sub-menu>
        <a-sub-menu key="sub2">
          <template #title>
            <span>
              <check-square-outlined />
              <span>审计</span>
            </span>
          </template>
          <a-menu-item key="5" @click="add({ title: '录屏回放', key: 'recorder' })">录屏回放</a-menu-item>
          <a-menu-item key="6" @click="add({ title: '操作记录', key: 'command' })">操作记录</a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-layout-sider>
    <a-layout-content :style="{ padding: '0 12px', background: '#fff', minHeight: '280px' }">
      <a-tabs v-model:activeKey="activeKey" hide-add type="editable-card" @edit="onEdit" size="small">
        <a-tab-pane v-for="pane in panes" :key="pane.key" :tab="pane.title" :closable="pane.closable">
          <div v-if="pane.content === '仪表盘'">
            <dashboard :chartId1="'userData' + pane.key" :chartId2="'userTop' + pane.key" />
          </div>
          <div v-else-if="pane.content === '连接配置'"><admin-conn-config /></div>
          <div v-else-if="pane.content === '字体设置'"><admin-font-size-config /></div>
          <div v-else-if="pane.content === '终端设置'"><admin-term-config /></div>
          <div v-else-if="pane.content === '快捷键'"><hot-key /></div>
          <div v-else-if="pane.content === '自定义菜单'"><admin-menu-config /></div>
          <div v-else-if="pane.content === '快速命令'"><admin-common-command /></div>
          <div v-else-if="pane.content === '代理设置'"><admin-term-gateway /></div>
          <div v-else-if="pane.content === '录屏回放'"><recorder :id="pane.key" /></div>
          <div v-else-if="pane.content === '操作记录'"><command :id="pane.key" /></div>
          <div v-else>{{ pane.content }}</div>
        </a-tab-pane>
      </a-tabs>
    </a-layout-content>
  </a-layout>
</template>
<script>
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons-vue'
import { defineComponent, ref } from 'vue'
import Dashboard from '@/views/devops/term/DashBoardPage.vue'
import AdminConnConfig from '@/views/devops/term/AdminConnConfig.vue'
import AdminFontSizeConfig from '@/views/devops/term/AdminFontSizeConfig.vue'
import AdminTermConfig from '@/views/devops/term/AdminTermConfig.vue'
import AdminMenuConfig from '@/views/devops/term/AdminMenuConfig.vue'
import AdminTermGateway from '@/views/devops/term/AdminTermGateway.vue'
import AdminCommonCommand from '@/views/devops/term/AdminCommonCommand.vue'
import AdminPluginsSetting from '@/views/devops/term/AdminPluginsSetting.vue'
import Recorder from '@/views/devops/term/Recorder.vue'
import Command from '@/views/devops/term/Command.vue'
import HotKey from '@/views/devops/term/HotKey.vue'
import store from '@/store'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

export default defineComponent({
  name: 'TerminalManager',
  components: {
    Dashboard,
    AdminConnConfig,
    AdminFontSizeConfig,
    AdminTermConfig,
    AdminMenuConfig,
    AdminCommonCommand,
    Recorder,
    Command,
    HotKey,
    MenuUnfoldOutlined,
    MenuFoldOutlined,
    AdminTermGateway,
    AdminPluginsSetting,
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  setup() {
    const panes = ref([
      {
        title: '仪表盘',
        content: '仪表盘',
        key: 'dashboard',
      },
    ])
    const activeKey = ref(panes.value[0].key)
    const newTabIndex = ref(0)
    const add = ({ title, key }) => {
      activeKey.value = `newTab${newTabIndex.value++}`
      panes.value.push({
        title: title,
        content: title,
        key: activeKey.value,
      })
    }
    const remove = targetKey => {
      let lastIndex = 0
      panes.value.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1
        }
      })
      panes.value = panes.value.filter(pane => pane.key !== targetKey)
      if (panes.value.length && activeKey.value === targetKey) {
        if (lastIndex >= 0) {
          activeKey.value = panes.value[lastIndex].key
        } else {
          activeKey.value = panes.value[0].key
        }
      }
    }
    const onEdit = targetKey => {
      remove(targetKey)
    }
    return {
      panes,
      activeKey,
      onEdit,
      add,
      selectedKeys: ref(['dashboard']),
      collapsed: ref(false),
    }
  },
})
</script>
<style>
#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

.site-layout .site-layout-background {
  background: #fff;
}
</style>
