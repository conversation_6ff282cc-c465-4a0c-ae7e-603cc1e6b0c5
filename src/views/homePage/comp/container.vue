<template>
  <div>
    <div class="banner">
      <a-carousel autoplay dot-position="bottom">
        <div>
          <div class="bgImgs" :style="{ 'background-image': `url(${features.desktop.poster})` }">
            <div class="bannerText">
              <p class="bannerText_title">远程办公云桌面</p>
              <p class="bannerText_content">
                为您构建可靠的数字化工作空间，实现远程办公，提升业务访问的安全性和连续性。
              </p>
              <router-link class="banner_link" :to="features.desktop.path">
                <tx-button class="bannerText_btn">
                  进入产品
                  <a-icon type="double-right" />
                </tx-button>
              </router-link>
            </div>
          </div>
        </div>
        <div>
          <div
            class="bgImgs"
            :style="{
              'background-image': `url(${features.storage.posterLoaded ? features.storage.poster : defaultPoster})`,
            }"
          >
            <div class="bannerText">
              <p class="bannerText_title">云存储</p>
              <p class="bannerText_content">多平台挂载，支持弹性扩容，为您的数据安全保驾护航</p>
              <router-link class="banner_link" :to="features.storage.path">
                <tx-button class="bannerText_btn">
                  进入产品
                  <a-icon type="double-right" />
                </tx-button>
              </router-link>
            </div>
          </div>
        </div>
        <div>
          <div
            class="bgImgs"
            :style="{
              'background-image': `url(${features.finops.posterLoaded ? features.finops.poster : defaultPoster})`,
            }"
          >
            <div class="bannerText">
              <p class="bannerText_title">FinOps(费用中心)</p>
              <p class="bannerText_content">成本节省，人人有责。通过数据驱动成本决策，主打云上成本管理和优化。</p>
              <router-link class="banner_link" :to="features.finops.path">
                <tx-button class="bannerText_btn">
                  进入产品
                  <a-icon type="double-right" />
                </tx-button>
              </router-link>
            </div>
          </div>
        </div>
        <div>
          <div
            class="bgImgs"
            :style="{
              'background-image': `url(${features.terminal.posterLoaded ? features.terminal.poster : defaultPoster})`,
            }"
          >
            <div class="bannerText">
              <p class="bannerText_title">云终端</p>
              <p class="bannerText_content">随时随地通过浏览器远程登录服务器管理操作</p>
              <router-link class="banner_link" :to="features.terminal.path">
                <tx-button class="bannerText_btn">
                  进入产品
                  <a-icon type="double-right" />
                </tx-button>
              </router-link>
            </div>
          </div>
        </div>
      </a-carousel>
    </div>
    <div class="main_container">
      <menuContent />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive } from 'vue'
import menuContent from './menuContent.vue'

const defaultPoster = '/vendor-cdn/@img/bgImg/desktop.webp'

const features = reactive({
  desktop: {
    path: '/server/remote-work',
    poster: '/vendor-cdn/@img/bgImg/desktop.webp',
    posterLoaded: true,
  },
  finops: {
    path: '/cost/bill',
    poster: '/vendor-cdn/@img/bgImg/finops.webp',
    posterLoaded: false,
  },
  storage: {
    path: '/storage/object-storage-personal',
    poster: '/vendor-cdn/@img/bgImg/storage.webp',
    posterLoaded: false,
  },
  terminal: {
    path: '/devops/terminal',
    poster: '/vendor-cdn/@img/bgImg/terminal.webp',
    posterLoaded: false,
  },
})

function imgPrefetch() {
  let item = Object.values(features).find(value => !value.posterLoaded)
  if (item && item.poster) {
    let img = new Image()
    img.onload = () => {
      item.posterLoaded = true
      setTimeout(imgPrefetch)
    }
    img.onerror = img.onload
    img.src = item.poster
  }
}

onMounted(() => {
  setTimeout(imgPrefetch)
})
</script>

<style lang="less" scoped>
.banner {
  width: 100%;
  height: 560px;
  position: relative;
  overflow: hidden;
  background: #fbfbfb;

  /deep/.ant-carousel .slick-slide {
    text-align: center;
    height: 560px;
    overflow: hidden;
    div {
      height: 100%;
      .bgImgs {
        width: 100%;
        height: 100%;
        position: relative;
        background-repeat: no-repeat;
        background-size: cover;
        .bannerText {
          height: 240px;
          max-width: 1320px;
          width: 1320px;
          min-width: 1200px;
          margin: 0 auto;
          margin-top: 60px;
          display: inline-block;
          padding: 100px 0 0 45px;
          text-align: left;
          box-sizing: border-box;
          font-size: 16px;
          z-index: 999;
          color: #fff;

          .bannerText_title {
            font-size: 42px;
            text-align: left;
            margin-bottom: 14px;
          }
          .bannerText_content {
            font-size: 16px;
            text-align: left;
          }
          .banner_link {
            display: inline-block;
            height: auto;
          }
          .bannerText_btn {
            font-size: 14px;
            margin-top: 80px;
            color: #fff;
            background-color: #ff6a00;
            border: 0;
          }
        }
      }
    }
  }

  /deep/.ant-carousel .slick-slide h3 {
    color: #fff;
  }
  :deep(.ant-carousel .slick-dots-bottom) {
    left: -18px;
    bottom: 100px !important;
    justify-content: left;
    max-width: 1320px;
    min-width: 1200px;
    margin-left: 50%;
    transform: translateX(-50%);

    li {
      width: 36px;
      &.slick-active {
        width: 48px;
      }
    }
  }

  .bannerTabs {
    width: 100%;
    height: 120px;
    position: absolute;
    bottom: 0px;
    background-color: rgba(20, 123, 219, 0.5);
    z-index: 88;
    .bannerTabs_inner {
      max-width: 1320px;
      min-width: 1200px;
      height: 100%;
      margin: 0 auto;
      .bannerTabs_inner_list {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0;
        .list_item {
          height: 100%;
          width: 25%;
          text-align: center;
          display: flex;
          align-items: center;
          cursor: pointer;
          div {
            width: 100%;
            height: 60px;
            margin: 0;
            padding: 0;
            .itemTitle {
              display: inline-block;
              color: #fff;
              font-size: 16px;
            }
            .itemContent {
              color: rgba(255, 255, 255, 80);
            }
          }
        }
      }
    }
  }
}

.main_container {
  max-width: 1320px;
  min-width: 1200px;
  margin: 0 auto;
}

@media screen and (max-width: 1320px) {
  .bannerText {
    padding-left: 120px !important;
  }
  /deep/.ant-carousel .slick-dots-bottom {
    // left: 120px !important;
    padding-left: 120px !important;
  }
}
@media screen and (max-width: 1450px) {
  .bannerText {
    padding-left: 80px !important;
  }
  /deep/.ant-carousel .slick-dots-bottom {
    // left: 120px !important;
    padding-left: 80px !important;
  }
}
@media screen and (min-width: 1320px) {
  .bannerText {
    // padding-left:120px
  }
  /deep/.ant-carousel .slick-dots-bottom {
  }
}
</style>
