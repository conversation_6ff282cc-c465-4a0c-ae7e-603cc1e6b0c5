<template>
  <div>
    <div class="menu_list">
      <template v-for="(utem, index) in menuList" :key="utem.id">
        <a-menu
          id="menu_list_ul"
          class="menu_list_ul"
          @click="menuClick(arguments, index)"
          v-model:selectedKeys="current"
          mode="horizontal"
        >
          <a-menu-item class="list_item" v-for="item in utem.listItem" :key="item.id">
            <span class="iconBox"><a-icon class="list_item_icon" :type="item.iconType" /></span>
            <br />
            {{ item.title }}
          </a-menu-item>
        </a-menu>
        <div class="menu_content" v-if="currentLine == index && currentLine !== null">
          <template v-for="uls in products" :key="uls.belongsTo">
            <ul v-if="uls.belongsTo === current[0]" class="menu_content_ul">
              <li class="menu_content_item" v-for="lis in uls.list" :key="lis.id">
                <p class="itemTitle" :title="lis.title">
                  <!-- <b @click="jumpto(lis)" > {{ lis.title }}</b> -->
                  <a @click="jumpto(lis)" :href="lis.proDocuUrl">{{ lis.title }}</a>
                </p>
                <span class="itemType" @click="goReadMe(lis.readmeUrl)">文档</span>
                <span v-if="lis.edition" class="itemType itemEdition">{{ lis.edition }}</span>
                <p class="itemContent" :title="lis.content">{{ lis.content }}</p>
                <p class="interest" @click="userInterest(lis, uls.belongsTo)">
                  <a-icon type="smile" :class="lis.isInterest ? 'gold' : 'empty'" />
                  <span :class="lis.isInterest ? 'gold' : 'empty'">感兴趣</span>
                </p>
              </li>
            </ul>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import { menuList, products } from './mock'
import cloneDeep from 'lodash.clonedeep'
import { getIngerestList, interestAct } from '@/api/homepage'

export default {
  data() {
    return {
      current: ['hotProducts'], // 当前选中菜单
      menuList: cloneDeep(menuList), // 菜单列表
      products: cloneDeep(products), // 菜单中产品列表
      currentLine: 0, // 当前行数，根据v-for渲染时index来确定
      user: null,
    }
  },
  mounted() {
    const userInfo = noc.user.getUserInfo()
    if (userInfo) {
      getIngerestList({
        email: userInfo.email.split('@')[0],
        module: 'hotProducts',
      }).then(res => {
        let currentPros = []
        for (let i = 0; i < this.products.length; i++) {
          if (this.products[i].belongsTo === 'hotProducts') {
            currentPros = this.products[i].list
          }
        }
        for (let i = 0; i < currentPros.length; i++) {
          for (let u = 0; u < res.Data.UserInteresteList.length; u++) {
            if (currentPros[i].id === res.Data.UserInteresteList[u].id) {
              if (res.Data.UserInteresteList[u].interest) {
                currentPros[i].isInterest = true
              } else {
                currentPros[i].isInterest = false
              }
            }
          }
        }
      })
    }
  },

  methods: {
    goReadMe(url) {
      window.location.href = url
    },
    menuClick() {
      // this.current = arguments[0].key
      const moduleInfo = arguments[0]
      this.currentLine = arguments[1]
      const mycheck = cloneDeep(this.current)
      this.$nextTick(() => {
        if (this.current[0] === mycheck[0]) {
          this.current = []
          this.currentLine = null
        }
      })
      const userInfo = noc.user.getUserInfo()
      if (userInfo) {
        getIngerestList({
          email: userInfo.email.split('@')[0],
          module: moduleInfo[0].key,
        }).then(res => {
          let currentPros = []
          for (let i = 0; i < this.products.length; i++) {
            if (this.products[i].belongsTo === moduleInfo[0].key) {
              currentPros = this.products[i].list
            }
          }
          for (let i = 0; i < currentPros.length; i++) {
            for (let u = 0; u < res.Data.UserInteresteList.length; u++) {
              if (currentPros[i].id === res.Data.UserInteresteList[u].id) {
                if (res.Data.UserInteresteList[u].interest) {
                  currentPros[i].isInterest = true
                } else {
                  currentPros[i].isInterest = false
                }
              }
            }
          }
        })
      }
    },
    userInterest(list, module) {
      const userInfo = noc.user.getUserInfo()
      if (userInfo) {
        const params = {
          id: list.id,
          module: module,
          user: userInfo.name,
          email: userInfo.email.split('@')[0],
          product: list.title,
          intereste: list.isInterest ? 2 : 1,
        }
        interestAct(params).then(res => {
          this.$set(list, 'isInterest', !list.isInterest)
        })
      } else {
        this.$message.info('请登录')
      }
    },
    /**
     * 点击产品提示
     */
    jumpto(to) {
      if (to.proDocuUrl === 'javascript:void(0);') {
        this.$message.info('功能正在开发中...')
      }
    },
  },
}
</script>

<style lang="less" scope>
.menu_list {
  // height: 200px;
  position: relative;
  min-height: 600px;
  .ant-menu-item {
    &:after {
      display: none;
    }
  }
  .ant-menu-horizontal > .ant-menu-item-selected {
    color: #004259 !important;
    border-bottom: 6px solid #004259;
    margin-bottom: 0px !important;
    // border-radius: 5px;
  }

  .ant-menu-horizontal > .ant-menu-item-active {
    color: #004259 !important;
    border-bottom: 6px solid #004259 !important;
    // border-radius: 0 0 3px 0;
  }
  .menu_list_ul {
    // display: flex;
    // align-items: center;
    // flex-wrap: wrap;
    // justify-content: flex-start;
    //  display: grid;
    //   grid-template-columns: repeat(6, 16.6%);
    //   grid-template-columns:repeat(auto-fill, 165px);
    //    justify-content:stretch ;
    box-sizing: border-box;
    border-bottom: 0;
    height: 160px;

    .list_item {
      // width: 220px;
      font-size: 14px;
      text-align: center;
      padding: 24px 0;
      color: #1d7c99;
      font-size: 16px;
      box-sizing: border-box;
      height: 160px;
      // margin-left: 24px;

      .iconBox {
        display: inline-block;
        width: 56px;
        height: 56px;
        // border-radius: 64px;
        // border: 6px solid #1d7c99;
        text-align: center;
        line-height: 56px;
        .list_item_icon {
          text-align: center;
          margin: 0;
          font-size: 56px;
        }
      }
    }
  }
}

.menu_content {
  margin-top: 20px;
  background-color: #f0f0f0;
  // width: 100vw;\
  width: 100%;
  padding: 40px, 0;
  // min-height: 274px;
  position: relative;
  // height: 200px;
  .menu_content_ul {
    // position: absolute;
    margin-top: 40px;
    // max-width: 1320px;
    // min-width: 1200px;
    display: inline-grid;
    grid-template-columns: repeat(4, 25%);
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    box-sizing: border-box;
    .menu_content_item {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      width: 300px;
      height: 120px;
      // background-color: #ccc;
      .itemTitle {
        cursor: pointer;
        display: inline-block;
        height: 25px;
        line-height: 25px;
        text-align: left;
        font-size: 16px;
        font-weight: 700;
        color: rgba(0, 33, 64, 89);
        margin: 0;
        max-width: 140px;
        overflow: hidden; /*内容超出后隐藏*/
        text-overflow: ellipsis; /* 超出内容显示为省略号 */
        white-space: nowrap; /* 文本不进行换行 */
        margin-right: 16px;
        a {
          color: rgba(0, 33, 64, 89);
        }
      }
      .itemType {
        cursor: pointer;
        margin-right: 8px;
        display: inline-block;
        box-sizing: border-box;
        height: 18px;
        padding: 0px 8px;
        line-height: 18px;
        font-size: 14px;
        text-align: center;
        color: rgba(27, 82, 133, 71);
        border: 1px solid #1b5285;
        border-radius: 4px;
        max-width: 65px;
        overflow: hidden; /*内容超出后隐藏*/
        text-overflow: ellipsis; /* 超出内容显示为省略号 */
        white-space: nowrap; /* 文本不进行换行 */
      }
      .itemEdition {
        border: 1px solid #ff6a00;
        color: #ff6a00;
      }
      .itemContent {
        // text-indent: 1rem;/
        margin-top: 4px;
        margin-bottom: 0px;
        width: 100%;
        height: 50px;
        width: 80%;
        color: #736f6f;
        overflow: hidden; /*内容超出后隐藏*/
        text-overflow: ellipsis; /* 超出内容显示为省略号 */
        white-space: wrap; /* 文本不进行换行 */
      }
      .interest {
        // display: inline-block;
        margin-top: 8px;
        width: 100%;
        cursor: pointer;
        i {
          font-size: 16px;
        }
        .gold {
          color: #ff6a00;
          margin-right: 8px;
        }
        .empty {
          color: #736f6f;
          margin-right: 8px;
        }
      }
    }
    :nth-child(4n) {
      margin-right: 0;
    }
  }
}
@media screen and (max-width: 1350px) {
  .list_item {
    width: 200px;
  }
}
@media screen and (min-width: 1351px) {
  .list_item {
    width: 220px;
  }
}
</style>
