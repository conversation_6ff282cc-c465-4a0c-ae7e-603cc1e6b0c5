<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-07-20 14:15:33
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-08-11 16:16:31
 * @FilePath: \cloud_web\src\views\homePage\comp\header.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div>
    <div class="header" @mouseenter="headerEnter" @mouseleave="headerLeave" ref="myHeader">
      <div class="logo"></div>
      <div class="searchBox" v-if="!searchShow">
        <a-input-search class="searchInput" placeholder="搜索" @search="onSearch" />
        <span @click="searchToggle" class="closeInput"> <a-icon :type="searchShow ? 'search' : 'close'" /> </span>
      </div>

      <a-menu @click="menuClick" v-model:selectedKeys="current" mode="horizontal" class="nav nav_left" v-if="searchShow">
        <a-menu-item @click="menuClick" key="files"> 文档 </a-menu-item>
        <a-menu-item
          class="menuProducts"
          v-for="item in headerMenus"

          :key="item.id"
          @mouseenter="productEnter(item)"
          @mouseleave="productLeave(item)"
        >
          {{ item.name }}

          <drop ref="myDrop" v-show="currentKeys === item.id" :dropmenus="headerMenuDrops" />
        </a-menu-item>

        <!-- <a-menu-item key="connect"> 联系我们 </a-menu-item> -->
        <!-- <a-menu-item key="search"> <a-icon type="search" /> </a-menu-item> -->
      </a-menu>

      <div class="userInfo">
        <span @click="goControlPanel" v-if="userInfo.name" class="contorlPanel"> 控制台 </span>
        <span :class="userInfo.avatar ? 'userImg' : 'boxHidden'">
          <img :src="userInfo.avatar || undefined" alt="" />
        </span>
        <span v-if="userInfo.name" class="userName" :title="userInfo.name">
          {{ userInfo.name }}
        </span>
        <span @click="login" v-else class="userName">登录</span>
      </div>
    </div>
  </div>
</template>

<script>
import { navList, menuList, products, headerMenus, headerDrop2 } from './mock'
import drop from './dropDownList.vue'
export default {
  components: { drop },
  data () {
    return {
      currentKeys: '',
      current: [],
      navs: navList,
      menuList: menuList,
      products: products,
      searchShow: true,
      userInfo: {
        avatar: '',
        name: ''
      },
      headerScrollTop: 0,
      headerMenus: headerMenus
    }
  },
  mounted () {
    const baseInfo = noc.user.getUserInfo()
    if (Object.keys(baseInfo).length) {
      this.userInfo = baseInfo
    }
    document.onscroll = (e) => {
      this.headerScrollTop = document.documentElement.scrollTop
      if (this.headerScrollTop > 10) {
        this.$refs.myHeader.style.backgroundColor = 'rgba(0, 33, 64)'
      } else {
        this.$refs.myHeader.style.backgroundColor = 'rgba(0, 33, 64, 0.5)'
      }
    }
  },
  computed: {
    headerMenuDrops () {
      let n = []
       if (this.currentKeys === 'files') {
        n = []
      } else if (this.currentKeys === 'products') {
        n = headerDrop2
      }
      return n
    }
  },
  methods: {
    headerEnter () {
      this.$refs.myHeader.style.backgroundColor = 'rgba(0, 33, 64)'
    },
    headerLeave () {
      if (this.headerScrollTop < 10) {
        this.$refs.myHeader.style.backgroundColor = 'rgba(0, 33, 64, 0.5)'
      }
    },
    productEnter (item) {
      // console.log(item)
      this.currentKeys = item.id
    },
    productLeave () {
      this.currentKeys = ''
    },
    onSearch () {},
    menuClick () {
      if (arguments[0].key === 'search') {
        this.searchToggle()
      } else if (arguments[0].key === 'files') {
        window.location.href = 'https://doc.intsig.net/pages/viewpage.action?pageId=401736095'
      } else if (arguments[0].key === 'products') {
        // this.$router.push('/')
        // this.$message.info('功能正在开发中...')
      }
    },
    goControlPanel () {
      this.$router.push('/dashboard/workplace')
    },
    searchToggle () {
      this.searchShow = !this.searchShow
    },
    login () {
      this.$router.push('/dashboard/workplace')
    }
  }
}
</script>

<style lang="less" scoped>
.header {
  width: 100%;
  min-width: 1200px;
  background-color: rgba(0, 33, 64, 0.5);
  height: 64px;
  // display: flex;
  // align-items: center;
  margin: 0 auto;
  position: fixed;
  top: 0;
  z-index: 99;

  // justify-content: space-between;
}
.logo {
  display: inline-block;
  width: 214px;
  height: 100%;
  background: url(/vendor-cdn/@img/logo_x_white.png) center center no-repeat;
  background-size: 100%;
  margin-left: 100px;
  cursor: pointer;
}
.nav {
  padding: 0;
  display: inline-block;
  // display: flex;
  // justify-content: center;
  height: 100%;
  // align-items: center;
  // width: auto;
  margin-bottom: 0;
  // max-width: 875px;
  /deep/ .ant-menu-item {
    height: 64px;
    padding: 0 30px;
    display: inline-block;
    line-height: 64px;
    margin-right: 0px;
    color: #ebebeb;
    // opacity: 0.8;
    font-size: 16px;
    position: relative;
    box-sizing: border-box;
    cursor: pointer;
  }
  // .nav-item:hover {
  //   box-sizing: border-box;
  //   border-bottom: 2px solid #c0c0c0;
  //   color: rgb(255, 255, 255);
  //   // font-weight: 700;
  //   // background-color:  #013769;
  // }
}
.nav_left {
  margin-left: 50px;
  background-color: transparent !important;
  border-bottom: none;
  color: #ebebeb;
  position: absolute;
  display: inline-block;
  font-size: 16px;
}
/deep/.ant-menu-horizontal {
  border-bottom: 0 !important;
}
// /deep/.ant-menu-horizontal {
//   .ant-menu-submenu-selected {
//     color: rgb(255, 255, 255) !important;
//   }
// }
// /deep/.ant-menu-submenu-selected {
// }
.nav_right {
  float: right;
}
.searchBox {
  position: absolute;
  width: 400px;
  display: inline-block;
  margin-left: 50px;
  height: 100%;
  // display: flex;
  // align-items: center;
  .searchInput {
    display: inline-block;
    width: 350px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  .closeInput {
    display: inline-block;
    height: 100%;
    color: #ebebeb;

    // display: flex;
    // align-items: center;
    font-size: 16px;
    margin-left: 16px;
    box-sizing: border-box;
    position: absolute;
    right: 10px;
    line-height: 64px;
  }
  .closeInput:hover {
    box-sizing: border-box;
    border-bottom: 2px solid #c0c0c0;
    color: rgb(255, 255, 255);
    // font-weight: 700;
    // background-color:  #013769;
  }
}

.userInfo {
  cursor: pointer;
  display: flex;
  align-items: center;
  // width: 150px;
  height: 100%;
  // background-color: #1871c2;
  float: right;
  line-height: 64px;
  padding: 0 8px;
  font-weight: 700;
  font-size: 14px;
  color: rgba(251, 252, 252, 100);
  text-align: right;
  margin-right: 30px;

  // display: flex;
  // justify-content: center;
  // align-items: center;
  .contorlPanel {
    display: inline-block;
    margin-right: 20px;
    padding: 0 20px;
  }
  .userName {
    max-width: 65px;
    overflow: hidden; /*内容超出后隐藏*/
    text-overflow: ellipsis; /* 超出内容显示为省略号 */
    white-space: nowrap; /* 文本不进行换行 */
  }
  .userImg {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16px;
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .boxHidden {
    width: 40px;
    height: 40px;
    line-height: 40px;
    visibility: hidden;
  }
}
</style>
