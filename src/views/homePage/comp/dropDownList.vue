<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-08 11:50:48
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-08-11 11:38:50
 * @FilePath: \cloud_web\src\views\homePage\comp\dropDownList.vue
 * @Description:header 鼠标悬浮下拉
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div class="drop" ref="dropBox">
    <div class="fakeBg"></div>
    <div class="dropContainer">
      <div style="width: 256px" class="myBox">
        <a-menu
          v-model:selectedKeys="currentKey"
          mode="inline"
          :inline-collapsed="false"
          class="dropMenus"
        >
          <a-menu-item v-for="item in dropmenus" :key="item.id">
            <span>{{ item.name }}</span>
          </a-menu-item>
          <!-- <a-menu-item key="2">
            <a-icon type="desktop" />
            <span>Option 2</span>
          </a-menu-item>
          <a-menu-item key="3">
            <a-icon type="inbox" />
            <span>Option 3</span>
          </a-menu-item> -->
        </a-menu>
      </div>
      <div class="dropContent">
        <ul class="dropContent_ul">
          <template v-for="item in headerMenusList" :key="item.belongsTo">
            <li class="dropContent_content" v-if="item.belongsTo === currentKey[0]">
              <ul class="dropContent_content_ul" style="width: 100%; height: 100%">
                <li class="dropContent_content_list" v-for="utem in item.childList" :key="utem.hotProducts">
                  <span class="dropContent_content_list_title">{{ utem.title }}</span>
                  <ul class="dropContent_content_list_ul">
                    <li class="dropContent_content_list_utem" v-for="u in utem.list" :key="u.id">
                      <a :href="u.proDocuUrl">{{ u.title }}{{ u.edition }}</a>
                    </li>

                    <!-- <li>1111111</li>

                    <li>1111111</li>

                    <li>1111111</li> -->
                  </ul>
                </li>
              </ul>
            </li>
          </template>
        </ul>

        <!-- <p v-if="currentKey[0] === 'hotProducts'">11111</p>
        <p v-if="currentKey[0] === 'cloudBase'">2222</p>
        <p v-if="currentKey[0] === 'bigData'">33333</p> -->
      </div>
    </div>
  </div>
</template>

<script>
import { headerMenusList } from './mock'

export default {
  props: {
    dropmenus: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      currentKey: ['hotProducts'],
      headerMenusList: headerMenusList
    }
  },
  mounted () {
    console.log(this.dropmenus, '---')
  }
}
</script>

<style lang="less" scoped>
.drop {
  height: 600px;
  background-color: rgba(0, 33, 64);
  position: fixed;
  left: 0;
  width: 100vw;
  .fakeBg {
    position: absolute;
    height: 600px;
    width: 50%;
    background: rgb(0, 43, 83);
    z-index: 1;
  }
  .dropContainer {
    max-width: 1320px;
    min-width: 1200px;
    margin: 0 auto;
    margin: 0 auto;
    display: flex;
    .myBox {
      z-index: 9;

      /deep/ .ant-menu-item {
        display: block;
        font-size: 14px;
        height: 40px;
        line-height: 40px;
      }

      /deep/ .ant-menu-item-active,
      /deep/ .ant-menu-item-selected {
        background: transparent;
        color: var(--tx-primary);
      }
    }
    .dropMenus {
      background: rgb(0, 43, 83) !important;
      z-index: 8;
      // background: rgb(195, 215, 233) !important;

      color: #fff;
      max-height: 600px;
      // overflow: scroll;
      overflow-x: hidden;
      overflow-y: scroll;
      .ant-menu-item-selected {
        background: rgb(0, 43, 83) !important;
      }
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 4px;
      }

      &:hover::-webkit-scrollbar-thumb {
        background: hsla(0, 0%, 53%, 0.4);
      }

      &:hover::-webkit-scrollbar-track {
        background: hsla(0, 0%, 53%, 0.1);
      }
    }
    .dropContent {
      background-color: rgba(0, 33, 64);
      // background-color: rgb(0, 66, 128);

      width: calc(100% - 256px);
      padding: 32px;
      box-sizing: border-box;
      z-index: 99;
      height: 600px;
      overflow: hidden;
      .dropContent_ul {
        width: 100%;
        height: 100%;
      }
      .dropContent_content {
        width: 100%;
        height: 100%;
        .dropContent_content_ul {
          display: inline-grid;
          grid-template-columns: repeat(4, 25%);
          grid-column-gap: 8px;
          grid-row-gap: 8px;
        }
        .dropContent_content_list {
          display: inline-block;
          margin-right: 8px;
          width: 200px;
          height: 240px;
          // background-color: #fff;
          .dropContent_content_list_title {
            font-size: 14px;
            color: #FFF;
          }
          .dropContent_content_list_ul {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            .dropContent_content_list_utem {
              position: relative;
              // background-color: #ccc;
              height: 24px;
              font-size: 14px;
              line-height: 24px;
              margin-bottom: 4px;
              a {
                color: rgba(255, 255, 255, 0.65);
              }
            }
          }
        }
      }
    }
    // background-color:  rgba(0, 33, 64,0.99) !important;
  }

  // top: 0;
}
</style>
