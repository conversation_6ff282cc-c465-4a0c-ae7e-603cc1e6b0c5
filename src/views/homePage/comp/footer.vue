<template>
  <div class="footer">
    <div class="footerContainer">
      <div class="footerInfo">
        <ul class="footer_list">
          <li class="footer_list_item" v-for="item in footerMenus" :key="item.id">
            <p class="itemTitle">{{ item.title }}</p>
            <a
              class="list_items"
              :href="utem.url ? utem.url : 'javascript:void(0);'"
              v-for="utem in item.lists"
              :key="utem.id"
            >
              {{ utem.title }}
            </a>
          </li>
        </ul>
      </div>
      <div class="footerlogo">
        <div class="logo"></div>
        <div class="companyInfo">
          <p>上海合合信息科技股份有限公司</p>
          <p>地址: 上海市静安区万荣路1268号云立方A座11层</p>
          <p>电话：4006611390</p>
          <div class="footericons">
            <a-icon type="weibo-square" />
            <a-icon class="hhWechat" type="wechat" />
            <div class="wechatImg"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="copyRight">
      <p>
        copyright@2023 上海合合信息科技股份有限公司 保留所有权利
        <a href="https://beian.miit.gov.cn/" class="fontCopyright">沪ICP备12002324号-1 - 单位门户网站</a>
        法律声明 不良信息举报电话：4006611390
        <a href="http://www.beian.gov.cn/portal/registerSystemInfo" class="fontCopyright">
          沪公网安备 31010602003171号
        </a>
        上海市互联网违法和不良信息举报中心
      </p>
    </div>
  </div>
</template>
<script>
import { footerMenus } from './mock'
export default {
  data() {
    return {
      footerMenus: footerMenus,
    }
  },
}
</script>

<style lang="less" scoped>
.footer {
  width: 100%;
  height: 345px;
  background: #041c28;
  .footerContainer {
    max-width: 1320px;
    min-width: 1200px;
    margin: 0 auto;
    margin-top: 20px;
    display: flex;
    padding-top: 58px;
    .footerInfo {
      height: 180px;
      width: calc(100% - 320px);
      margin-bottom: 8px;
      // background-color: pink;

      .footer_list {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0;
        .footer_list_item {
          width: 20%;
          // background-color: rgb(138, 247, 88);
          height: 100%;
          .itemTitle {
            margin-bottom: 30px;
            height: 20px;
            width: 100%;
            display: inline-block;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }
          .list_items {
            display: block;
            color: rgba(255, 255, 255, 0.65);

            margin-bottom: 8px;
            font-size: 14px;
            border: none;
          }
        }
      }
      .footer_list :first-child {
        width: 25%;
        height: 100%;
      }
      .footer_list :last-child {
        border-right: 1px solid #fff;
      }
    }
    .footerlogo {
      margin-bottom: 20px;
      height: 180px;
      width: 320px;
      .logo {
        display: inline-block;
        width: 214px;
        height: 25px;
        background: url(/vendor-cdn/@img/logo_x_white.png) center center no-repeat;
        background-size: 100%;
        margin-left: 50px;
        margin-bottom: 20px;
        cursor: pointer;
      }
      .companyInfo {
        margin-left: 56px;
        color: rgba(255, 255, 255, 0.65);
        font-size: 12px;
      }
      .footericons {
        font-size: 25px;
        margin-top: 8px;
        position: relative;
        .anticon {
          margin-right: 8px;
        }
        .wechatImg {
          position: absolute;
          left: 30px;
          top: 30px;
          width: 150px !important;
          height: 150px !important;
          transition: all 0.5s;
          background-color: #fff;
          display: none;
        }
        .hhWechat :hover {
          .wechatImg {
            display: inline-block;
          }
        }
      }

      // background-color: red;
    }
  }
  .copyRight {
    max-width: 1320px;
    min-width: 1200px;
    margin: 0 auto;
    color: rgba(255, 255, 255, 0.65);
    p {
      width: calc(100% - 630px);
    }
    .fontCopyright {
      color: #fff;
    }
  }
}
</style>
