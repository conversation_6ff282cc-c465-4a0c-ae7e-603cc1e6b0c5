<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-07-20 11:09:15
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-11-04 10:17:42
 * @FilePath: \cloud_web\src\views\homePage\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div class="app">
    <headers />
    <container />
    <footers />
  </div>
</template>

<script>
import headers from './comp/header.vue'
import container from './comp/container.vue'
import footers from './comp/footer.vue'
export default {
  components: {
    headers,
    container,
    footers
  }
}
</script>

<style lang="less" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  // background-color: rgb(235, 235, 235,0.8);
  box-sizing: border-box;
}
</style>
