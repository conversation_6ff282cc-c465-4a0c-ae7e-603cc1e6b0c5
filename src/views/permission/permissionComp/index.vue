<template>
  <a-drawer
    title="权限策略配置"
    placement="right"
    :closable="false"
    width="50%"
    :visible="policyConfigVisible"
    @close="onClose"
  >
    <a-form-model ref="ruleForm" :model="policyForm" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item label="授权主体" name="name">
        <a-input v-model:value="policyForm.name" disabled />
      </a-form-model-item>
      <a-form-model-item label="选择权限">
        <a-transfer
          :data-source="policyData"
          :target-keys="targetPolicyKeys"
          :rowKey="policy => policy.policyName"
          show-search
          :filter-option="(inputValue, item) => item.title.indexOf(inputValue) !== -1"
          :show-select-all="false"
          @change="onChange"
        >
          <template
            #children="{
              direction,
              filteredItems,
              selectedKeys,
              disabled: listDisabled,
              onItemSelectAll: itemSelectAll,
              onItemSelect: itemSelect,
            }"
          >
            <a-table
              :row-selection="getRowSelection({ disabled: listDisabled, selectedKeys, itemSelectAll, itemSelect })"
              :columns="direction === 'left' ? leftColumns : rightColumns"
              :data-source="filteredItems"
              size="small"
              :style="{ pointerEvents: listDisabled ? 'none' : null }"
              :custom-row="
                ({ key, disabled: itemDisabled }) => ({
                  on: {
                    click: () => {
                      if (itemDisabled || listDisabled) return
                      itemSelect(key, !selectedKeys.includes(key))
                    },
                  },
                })
              "
            />
          </template>
        </a-transfer>
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
        <tx-button type="primary" @click="onSubmit">创建</tx-button>
        <tx-button style="margin-left: 10px" @click="resetForm">重置</tx-button>
      </a-form-model-item>
    </a-form-model>
  </a-drawer>
</template>

<script>
// import user from '@/store/modules/user'
import { createSubjectPolicy, getPolicyData, subjectPolicyList, deleteSubjectPolicy } from '@/api/permission/policy'
import { STable } from '@/components'
import { Transfer, notification } from 'ant-design-vue'
import { difference } from 'lodash'
// import { string } from 'yargs'
import cloneDeep from 'lodash.clonedeep'

const leftTableColumns = [
  {
    title: '权限策略名称',
    dataIndex: 'policyName',
  },
  {
    title: '备注',
    dataIndex: 'policyDesc',
  },
]
const rightTableColumns = [
  {
    title: '权限策略名称',
    dataIndex: 'policyName',
  },
]

export default {
  name: 'WorkflowComponents',
  components: {
    STable,
    aTransfer: Transfer,
  },
  props: {
    policyForm: {
      type: Object,
      default: () => {
        return {
          name: '',
          email: '',
        }
      },
    },
    policyConfigVisible: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  watch: {
    policyConfigVisible: {
      handler: function (val) {
        this.getPolicyList()
        this.getPolicyPersonalData()
      },
    },
  },
  data() {
    const validateTargetPolicyKey = (rule, value, callback) => {
      callback = antdFormValidateCallback
      if (this.targetPolicyKeys.length === 0) {
        return callback(new Error('资源未填写'))
      } else {
        return callback()
      }
    }
    return {
      labelCol: {},
      wrapperCol: {},
      confirmLoading: false,
      targetPolicyKeys: [],
      oldTargetPolicyKeys: [],
      rules: {
        name: [{ required: true, message: '授权主体不存在', trigger: 'change' }],
        targetPolicyKeys: [{ required: true, trigger: 'change', validator: validateTargetPolicyKey }],
      },
      parameter: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      leftColumns: leftTableColumns,
      rightColumns: rightTableColumns,
      policyData: [],
      queryParam: {},
    }
  },
  created() {
    this.getPolicyList()
  },
  mounted() {},
  methods: {
    getPolicyPersonalData() {
      this.queryParam.subject = this.policyForm.email
      this.queryParam.subjectType = this.policyForm.email.indexOf('@') > -1 ? 'user' : 'role'
      const requestParameters = Object.assign({}, this.parameter, this.queryParam)
      subjectPolicyList(requestParameters).then(res => {
        this.targetPolicyKeys = []
        if (noc.isArray(res.Data.permissions)) {
          res.Data.permissions.forEach(element => {
            if (this.targetPolicyKeys.indexOf(element.policyName) === -1) {
              this.targetPolicyKeys.push(element.policyName)
            }
          })
          this.oldTargetPolicyKeys = cloneDeep(this.targetPolicyKeys)
        }
      })
    },
    getPolicyList() {
      const requestParameters = Object.assign({}, this.parameter)
      getPolicyData(requestParameters).then(res => {
        if (res.Data.hasOwnProperty('policies')) {
          this.policyData = res.Data.policies
          this.parameter.pageNo = res.Data.pageNo
          this.parameter.pageSize = res.Data.totalCount
        } else {
          return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
        }
      })
    },
    onClose() {
      this.$emit('onClose')
    },
    resetForm() {
      this.targetPolicyKeys = []
    },
    onSubmit() {
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          var targetPolicyKeys = this.targetPolicyKeys
          var oldTargetPolicyKeys = this.oldTargetPolicyKeys
          var addPolicy = targetPolicyKeys.filter(function (val) {
            return oldTargetPolicyKeys.indexOf(val) === -1
          })
          var deletePolicy = oldTargetPolicyKeys.filter(function (val) {
            return targetPolicyKeys.indexOf(val) === -1
          })
          console.log(addPolicy, 'addPolicy')
          console.log(deletePolicy, 'deletePolicy')
          if (addPolicy.length !== 0) {
            const requestParameters = {
              subject: this.policyForm.email,
              policyNames: addPolicy,
              subjectType: this.policyForm.email.indexOf('@') > -1 ? 'user' : 'role',
            }
            createSubjectPolicy(requestParameters).then(res => {
              if (res === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (res.Code === 200) {
                notification.success({
                  message: '创建成功',
                  description: '绑定关系创建成功',
                })
                this.targetPolicyKeys = []
                this.onClose()
              } else {
                notification.error({
                  message: '创建失败',
                  description: '创建失败，请联系运维开发排查~',
                })
              }
            })
          }
          if (deletePolicy.length !== 0) {
            var deleteRes = []
            deletePolicy.forEach(item => {
              const requestParameters = {
                subject: this.policyForm.email,
                policyName: item,
                subjectType: this.policyForm.email.indexOf('@') > -1 ? 'user' : 'role',
              }
              console.log(requestParameters)
              deleteSubjectPolicy(requestParameters).then(res => {
                deleteRes.push(res)
              })
              for (let i = 0; i <= deleteRes.length; i++) {
                if (deleteRes[i] === undefined) {
                  notification.error({
                    message: '解绑失败',
                    description: '后端接口错误，请联系运维开发排查~',
                  })
                  break
                }
              }
              notification.success({
                message: '解绑成功',
                description: '角色解绑成功',
              })
            })
          }
          this.targetPolicyKeys = []
          this.onClose()
        } else {
          return false
        }
      })
    },
    onChange(nextTargetKeys) {
      console.log(this.oldTargetPolicyKeys)
      this.targetPolicyKeys = nextTargetKeys
    },
    getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
      return {
        getCheckboxProps: item => ({ props: { disabled: disabled || item.disabled } }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ policyName }) => policyName)
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys)
          itemSelectAll(diffKeys, selected)
        },
        onSelect({ key }, selected) {
          itemSelect(key, selected)
        },
        selectedRowKeys: selectedKeys,
      }
    },
  },
}
</script>
