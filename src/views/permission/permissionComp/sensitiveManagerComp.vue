<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="20">
          <a-col :md="8" :sm="10">
            <a-form-item label="服务名称">
              <a-select
                v-model:value="queryParam.serverName"
                allowClear
                show-search
                :filter-option="filterOption"
                placeholder="请选择服务名称"
                @pressEnter="$refs.table.refresh()"
              >
                <a-select-option v-for="server in serverNameList" :value="server" :key="server">
                  {{ server }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="10">
            <a-form-item label="路由">
              <a-input v-model:value="queryParam.path" placeholder="请输入路由" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="10">
            <a-form-item label="通知方式">
              <a-select
                v-model:value="queryParam.notice"
                @pressEnter="$refs.table.refresh()"
                allowClear
                placeholder="请选择通知方式"
              >
                <a-select-option value="群通知">群通知</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="10">
            <a-form-item label="创建用户">
              <a-input v-model:value="queryParam.creator" placeholder="请输入用户名/邮箱" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="10">
            <span class="table-page-search-submitButtons">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px; margin-right: 20px" @click="() => (this.queryParam = {})">
                重置
              </tx-button>
              <tx-button type="primary" icon="plus" @click="handleCreate">新建</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table ref="table" :rowKey="item => item.id" :pagination="pagination" :columns="columns" :data="loadData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'method'">
          <a-tag v-for="method in record.method" :key="method" color="processing">{{ method }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'action'">
          <tx-button type="link" size="small" @click="handleUpdate(record)">更新</tx-button>
          <tx-button type="link" size="small" @click="handleDelete(record)">删除</tx-button>
        </template>
      </template>
    </s-table>

    <!-- 新增和编辑敏感数据 -->
    <a-modal
      :title="dialogTitle"
      :visible="dialogVisible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      :width="800"
    >
      <a-form-model
        ref="sensitiveForm"
        v-if="dialogVisible"
        :model="sensitiveForm"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 12 }"
      >
        <a-form-model-item label="服务名称" name="serverName">
          <a-select
            v-model:value="sensitiveForm.serverName"
            allowClear
            show-search
            :filter-option="filterOption"
            placeholder="请选择服务名称"
          >
            <a-select-option v-for="server in serverNameList" :value="server" :key="server">
              {{ server }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="路由" name="path">
          <a-select
            v-model:value="sensitiveForm.path"
            allowClear
            show-search
            :filter-option="filterOption"
            placeholder="请输入路由"
          >
            <a-select-option v-for="backRoute in backedRouteList" :value="backRoute" :key="backRoute">
              {{ backRoute }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="通知方式" name="notice">
          <a-select v-model:value="sensitiveForm.notice" allowClear placeholder="请选择通知方式">
            <a-select-option value="群通知">群通知</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item v-if="dialogType === 'update'" label="路由" name="creator">
          <a-input v-model:value="sensitiveForm.creator" disabled placeholder="请输入路由" />
        </a-form-model-item>
        <a-form-model-item label="方法" name="method">
          <a-select v-model:value="sensitiveForm.method" mode="multiple" allowClear placeholder="请选择通知方式">
            <a-select-option value="GET">GET</a-select-option>
            <a-select-option value="PUT">PUT</a-select-option>
            <a-select-option value="POST">POST</a-select-option>
            <a-select-option value="DELETE">DELETE</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="备注" name="remark">
          <a-textarea auto-size placeholder="请输入备注" v-model:value="sensitiveForm.remark"></a-textarea>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import {
  createSensitive,
  deleteSensitive,
  getBackedRoute,
  getSensitiveList,
  updateSensitive,
} from '@/api/permission/sensitive'
import { STable } from '@/components'

const columns = [
  {
    title: '服务名称',
    dataIndex: 'serverName',
  },
  {
    title: '路由',
    dataIndex: 'path',
  },
  {
    title: '创建用户',
    dataIndex: 'creator',
  },
  {
    title: '通知方式',
    dataIndex: 'notice',
  },
  {
    title: '方法',
    dataIndex: 'method',
    scopedSlots: { customRender: 'method' },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    customRender: ({ text }) => {
      return text || '-'
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '操作',
    dataIndex: 'action',
    minWidth: '200px',
    scopedSlots: { customRender: 'action' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  inject: ['reload'],
  name: 'SensitiveManager',
  components: {
    STable,
  },
  setup() {
    const serverNameList = [
      'sms',
      'clb',
      'ai',
      'monitor',
      'dbmanagement',
      'kms',
      'cron',
      'desktop',
      'consul',
      'security',
      'workflow',
      'gateway',
      'db',
      'network',
      'coder',
      'domain',
      'storage',
      'cost',
      'user',
      'auth',
      'asset',
    ]
    const filterOption = (input, option) => {
      return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
    return {
      serverNameList,
      filterOption,
    }
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      dialogVisible: false,
      dialogType: 'create',
      dialogTitle: '新增敏感权限',
      confirmLoading: false,
      // 列表查询参数
      queryParam: {},
      //  编辑新建详情
      sensitiveForm: {
        serverName: undefined,
        path: undefined,
        method: undefined,
        creator: '',
        notice: undefined,
        remark: undefined,
      },
      // 编辑新建路由列表
      backedRouteList: [],
      // 规则
      rules: {
        serverName: [{ required: true, message: '请选择服务', trigger: 'blur' }],
        path: [{ required: true, message: '请选择路由', trigger: 'blur' }],
        method: [{ required: true, message: '请选择方法', trigger: 'blur' }],
        creator: [{ required: true, message: '请填写创建用户', trigger: 'blur' }],
        notice: [{ required: true, message: '请选择通知方式', trigger: 'blur' }],
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getSensitiveList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            const { data, pageNo, pageSize, totalCount, totalPage } = res.Data
            return {
              data: data
                ? data.map(item => {
                  return { ...item, method: item.method ? item.method.split('|') : undefined }
                })
                : [],
              pageNo,
              pageSize,
              totalCount,
              totalPage,
            }
          } else {
            return {
              data: [],
              pageNo: 1,
              pageSize: 10,
              totalCount: 0,
              totalPage: 0,
            }
          }
        })
      },
    }
  },
  created() {
    this.getBackedRouteList()
  },
  methods: {
    // 获取路由path
    getBackedRouteList() {
      getBackedRoute()
        .then(res => {
          this.backedRouteList = res?.Data?.resList || {}
        })
        .catch(err => {
          this.$message.error(`${err}`)
        })
    },
    // 新增敏感权限
    handleCreate() {
      this.dialogVisible = true
      this.dialogType = 'create'
      this.dialogTitle = '新增敏感权限'
      this.sensitiveForm = {
        serverName: undefined,
        path: undefined,
        method: undefined,
        creator: '',
        notice: undefined,
        remark: undefined,
      }
    },
    // 敏感权限更新
    handleUpdate(record) {
      this.dialogVisible = true
      this.dialogType = 'update'
      this.dialogTitle = '敏感权限更新'
      this.sensitiveForm = {
        ...record,
      }
    },
    // 提交弹框
    handleOk() {
      antdFormValidate(this.$refs.sensitiveForm, valid => {
        if (valid) {
          let params = {}
          this.confirmLoading = true
          const { method } = this.sensitiveForm
          params = { ...this.sensitiveForm, method: method.join('|') }
          if (this.dialogType === 'update') {
            updateSensitive(params)
              .then(res => {
                this.$message.success('编辑成功！')
                this.reload()
              })
              .catch(() => {})
              .finally(() => {
                this.confirmLoading = false
                this.handleCancel()
              })
          } else {
            createSensitive(params)
              .then(res => {
                this.$message.success('创建成功！')
                this.reload()
              })
              .catch(() => {})
              .finally(() => {
                this.confirmLoading = false
                this.handleCancel()
              })
          }
        }
      })
    },
    // 取消弹框
    handleCancel() {
      this.dialogVisible = false
    },
    // 删除数据
    handleDelete(record) {
      this.$confirm({
        title: '删除敏感权限?',
        content: `确认删除 ${record.path} 敏感权限吗？`,
        onOk: () => {
          deleteSensitive(record.id)
            .then(res => {
              this.$message.success('删除成功！')
              this.reload()
            })
            .catch(() => {
              this.$message.error('删除失败！')
            })
        },
        onCancel() {},
      })
    },
  },
}
</script>
