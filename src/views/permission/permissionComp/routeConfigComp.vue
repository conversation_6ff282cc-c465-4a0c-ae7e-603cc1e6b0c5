<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-18 14:26:40
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-03 11:24:05
 * @FilePath: \cloud_web\src\views\permission\routeConfig.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <a-card :bordered="false">
    <tx-button type="primary" @click="routeHandler('', 'addRoot')">新增模块</tx-button>
    <a-table :pagination="pagination" :columns="columns" rowKey="name" :data-source="routeData.routers">
      <template #bodyCell="{ column, record }">
        <template v-if="column.title == 'Action'">
          <tx-button v-if="record.level != 3" type="primary" @click="routeHandler(record, 'addChild')">
            新增子模块
          </tx-button>
          <p style="margin-right: 0" class="toRight">
            <span @click="adjustPos('down', record)" style="margin-right: 8px; cursor: pointer; font-size: 16px">
              <arrow-down-outlined />
            </span>
            <span @click="adjustPos('up', record)" style="cursor: pointer; font-size: 16px">
              <arrow-up-outlined />
            </span>
          </p>
          <tx-button :class="'toRight'" type="danger" @click="routeHandler(record, 'delete')">删除</tx-button>
          <tx-button class="toRight" @click="routeHandler(record, 'edit')">编辑</tx-button>
        </template>
      </template>
    </a-table>
  </a-card>
  <a-modal
    :title="dialogTitle"
    :visible="dialogVisible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="800"
  >
    <a-form-model
      ref="ruleForm"
      v-if="dialogVisible"
      :model="routeForm"
      :rules="rules"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 12 }"
    >
      <a-form-model-item label="模块层级">
        <a-radio-group v-model:value="routeForm.level">
          <a-radio-button v-if="routeForm.level == '1'" value="1">一级模块</a-radio-button>
          <a-radio-button v-if="routeForm.level == '2'" value="2">二级模块</a-radio-button>
          <a-radio-button v-if="routeForm.level == '3'" value="3">三级模块</a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="模块名" name="name">
        <a-input placeholder="菜单中要显示的名称 例:权限管理" v-model:value="routeForm.name"></a-input>
      </a-form-model-item>
      <a-form-model-item label="路径" name="path">
        <p v-if="dialogTitle != '编辑'">
          <a-input
            placeholder="访问该模块的路由地址 例:/test"
            v-if="routeForm.level == '1'"
            v-model:value="routeForm.path"
          ></a-input>
          <a-input
            placeholder="访问该模块的路由地址 例:/test/view"
            v-else
            :addonBefore="addonBefore"
            v-model:value="routeForm.path"
          />
        </p>
        <p v-else>
          <a-input
            :disabled="routeForm.children && routeForm.children.length"
            placeholder="访问该模块的路由地址 例:/test"
            v-if="routeForm.level == '1'"
            v-model:value="routeForm.newPath"
          ></a-input>
          <a-input
            :disabled="routeForm.children && routeForm.children.length"
            placeholder="访问该模块的路由地址 例:/test/view"
            v-else
            :addonBefore="addonBefore"
            v-model:value="routeForm.newPath"
          />
        </p>
      </a-form-model-item>
      <a-form-model-item label="组件" name="component">
        <a-input
          placeholder="一级模块为RouteView,二级模块为配置的模块例:Analysis"
          v-model:value="routeForm.component"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item v-if="Object.keys(this.currentModule).length == 0" label="重定向地址" name="redirect">
        <a-input placeholder="一般为一级模块的子模块路由地址例:/test/view" v-model:value="routeForm.redirect"></a-input>
      </a-form-model-item>
      <a-form-model-item v-if="Object.keys(this.currentModule).length == 0" label="icon" name="icon">
        <a-input placeholder="icon图标,可去antdesign中找 例:apple" v-model:value="routeForm.icon"></a-input>
      </a-form-model-item>
      <a-form-model-item label="备注" name="comment">
        <a-textarea auto-size placeholder="请输入备注" v-model:value="routeForm.comment"></a-textarea>
      </a-form-model-item>
      <a-form-model-item v-if="Object.keys(this.currentModule).length != 0" label="是否隐藏菜单" name="hidden">
        <a-select v-model:value="routeForm.hidden" style="width: 120px" @change="hiddenChange">
          <a-select-option :value="true">隐藏</a-select-option>
          <a-select-option :value="false">显示</a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import { getRouters, putRouter, deleteRouter, editRoute, sortRoute } from '@/api/routePermission'
const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '路径',
    dataIndex: 'path',
    key: 'path',
  },
  {
    title: 'Action',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: 400,
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}
export default {
  inject: ['reload'],
  name: 'PolicyManager',
  components: {},
  data() {
    return {
      routeData: [], // 当前路由数据
      columns,
      pagination,
      dialogTitle: '', // 弹出框名
      dialogVisible: false,
      confirmLoading: false,
      currentModule: {}, // 当前选中的路由
      addonBefore: '', // 字模块路由前缀
      routeForm: {
        level: '', // 几级路由
        name: '',
        path: '',
        component: '',
        redirect: '', // 重定向地址
        icon: '',
        hidden: false,
        comment: '', //备注
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
        secondModuleName: [{ required: true, message: '请输入二级模块名称', trigger: 'blur' }],
        secondRoutePath: [{ required: true, message: '请输入二级模块路径', trigger: 'blur' }],
      },
    }
  },
  mounted() {
    this.getAllRoutes()
  },

  methods: {
    hiddenChange(value) {},
    // 通过接口拿到所有路由
    getAllRoutes() {
      getRouters()
        .then(res => {
          this.routeData = res
          this.loopDelKey(this.routeData.routers)
          console.log(this.routeData, 'this.routeDatathis.routeData')
          // console.log(res, 'resssssssssss')
          // this.routeData = userAuthArr
          // console.log(this.routeData, 'this.routeData')
          // this.loopDelKey(this.routeData.routers)
          // console.log(this.routeData, 'this.routeDatathis.routeData')
        })
        .catch(() => {})
    },
    loopDelKey(routes) {
      return routes.map(item => {
        if (item.children.length > 0) {
          this.loopDelKey(item.children)
        } else {
          delete item.children
        }
        return item
      })
    },
    // 单个路由配置
    routeHandler(module, types) {
      if (module) {
        this.currentModule = module
      } else {
        this.currentModule = {}
      }
      console.log(this.currentModule, ' this.currentModule')
      switch (types) {
        case 'addChild':
          this.dialogTitle = '新增子模块'
          this.addonBefore = module.path + '/'
          this.routeForm = {
            level: '', // 几级路由
            name: '',
            path: '',
            component: '',
            redirect: '', // 重定向地址
            icon: '',
            hidden: false,
            comment: '',
          }
          this.routeForm.level = module.level + 1
          this.dialogVisible = true
          break
        case 'delete':
          this.dialogTitle = '删除模块'
          const that = this
          this.$confirm({
            title: '删除模块',
            content: `确认删除 ${module.name} 及其子模块吗？`,
            onOk() {
              deleteRouter({ path: module.path })
                .then(() => {
                  that.$message.info('删除成功')
                  that.reload()
                })
                .catch(() => {
                  that.$message.error('删除失败')
                })
            },
            onCancel() {},
          })

          break
        case 'addRoot':
          this.dialogTitle = '添加模块'
          this.routeForm.level = '1'
          this.dialogVisible = true
          break
        case 'edit':
          this.dialogTitle = '编辑'
          this.routeForm = {
            children: module.children,
            level: module.level, // 几级路由
            name: module.name,
            path: module.path,
            component: module.component,
            redirect: module.redirect, // 重定向地址
            icon: module.icon,
            hidden: module.hidden,
            newPath: module.path,
            comment: module.comment,
          }
          this.cutRoutePath(this.routeForm)
          this.dialogVisible = true
      }
    },
    cutRoutePath(route) {
      if (route.level != '1') {
        const signIndex = route.path.lastIndexOf('/')
        const handlerPath = route.path.slice(0, signIndex)
        this.addonBefore = handlerPath
        this.routeForm.newPath = route.path.substring(signIndex, this.routeForm.newPath.length)
      }
    },
    handleOk(e) {
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          let params = {}
          params = { ...this.routeForm }

          console.log(params, 'paramsparamsparams')
          if (this.dialogTitle == '编辑') {
            params.newPath = this.addonBefore + params.newPath
            params.parent = this.addonBefore
            editRoute(params)
              .then(res => {
                console.log(res, 'resss')
                this.reload()
              })
              .catch(err => {
                console.log(err, 'err')
              })
              .finally(() => {
                this.dialogVisible = false
                this.confirmLoading = false
                this.currentModule = {}
                this.addonBefore = ''
                this.routeForm = {
                  level: '', // 几级路由
                  name: '',
                  path: '',
                  component: '',
                  redirect: '', // 重定向地址
                  icon: '',
                  hidden: false,
                  comment: '',
                }
              })
          } else {
            if (this.currentModule.path) {
              params.parent = this.currentModule.path
            }
            params.path = this.addonBefore + params.path
            putRouter(params)
              .then(() => {
                // this.getAllRoutes()
                this.reload()
              })
              .catch(() => {})
              .finally(() => {
                this.dialogVisible = false
                this.confirmLoading = false
                this.currentModule = {}
                this.addonBefore = ''
                this.routeForm = {
                  level: '', // 几级路由
                  name: '',
                  path: '',
                  component: '',
                  redirect: '', // 重定向地址
                  icon: '',
                  hidden: false,
                  comment: '',
                }
              })
          }

          this.confirmLoading = true
        }
      })
    },
    handleCancel(e) {
      this.dialogVisible = false
      this.addonBefore = ''
    },
    adjustPos(type, e) {
      console.log(type, 'type')
      console.log(e, 'eee')
      sortRoute({
        path: e.path,
        sort: type,
      })
        .then(() => {
          this.reload()
        })
        .catch(err => {
          console.log(err, 'err')
        })
    },
  },
}
</script>
<style lang="less" scoped>
.toRight {
  float: right;
  margin-right: 20px;
}
.toRightLess {
  margin-left: 8px;
}
</style>
