<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="模糊查询">
              <a-input v-model:value="queryParam.searchText" placeholder="用户名/邮箱" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <!-- <tx-button type="primary" @click="handleFilter">查询</tx-button> -->
              <tx-button type="primary" @click="$refs.userTable.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table ref="userTable" :rowKey="user => user.id" :pagination="pagination" :columns="columns" :data="loadData">
      <template #bodyCell="{ column, record: user, text }">
        <template v-if="column.dataIndex == 'action'">
          <tx-button type="link" size="small" @click="handlePolicy(user)">权限策略</tx-button>
          <!-- <tx-button type="link" size="small" @click="handleRouter(user)">路由管理</tx-button> -->
        </template>
        <template v-else-if="column.dataIndex == 'roles'">
          <a-row>
            <a-col :span="18">
              <a-select
                show-search
                mode="multiple"
                :value="text"
                style="width: 90%"
                placeholder="请选择角色"
                :options="roleList"
                size="small"
                @change="bindingRole(user, $event)"
              ></a-select>
            </a-col>
            <a-col :span="6">
              <tx-button
                :disabled="user.rolesButtonDisable"
                @click="updateUserRoles(user)"
                type="primary"
                shape="round"
                size="small"
                icon="check"
              />
            </a-col>
          </a-row>
        </template>
      </template>
    </s-table>
  </a-card>
  <permission-comp
    :policyConfigVisible="policyConfigVisible"
    :policyForm="policyForm"
    @onClose="onPolicyClose"
  ></permission-comp>
  <a-drawer
    title="角色绑定"
    placement="right"
    width="30%"
    :visible="bindingRoleVisible"
    @close="onBindingRoleClose"
  ></a-drawer>
  <a-modal
    :width="800"
    title="用户路由管理"
    v-model:visible="visible"
    :confirm-loading="confirmLoading"
    @ok="handleCancel"
  >
    <a-table :columns="routeColumns" rowKey="name" :data-source="userRouters">
      <template #bodyCell="{ column, record: eachInfos }">
        <template v-if="column.dataIndex == 'action'">
          <tx-button style="margin-left: 8px" v-if="!eachInfos.auth" @click="topggleAuth(true, eachInfos)">
            启用
          </tx-button>
          <tx-button style="margin-left: 8px" v-else @click="topggleAuth(false, eachInfos)" type="danger">
            禁用
          </tx-button>
        </template>
      </template>
    </a-table>
    <template #footer>
      <tx-button key="back" @click="handleCancel">关闭</tx-button>
    </template>
  </a-modal>
</template>

<script>
import { getUserList } from '@/api/permission/user'
import { getRoleList, roleBinding, roleBindingDelete } from '@/api/permission/role'
import { GetAuth, Auth, unAuth } from '@/api/routePermission/index'
import permissionComp from './index.vue'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { difference } from 'lodash'
import cloneDeep from 'lodash.clonedeep'

const columns = [
  {
    title: 'UID',
    dataIndex: 'uid',
  },
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '所属部门',
    dataIndex: 'departmentName',
  },
  {
    title: 'TL',
    dataIndex: 'straightLineManagerName',
  },
  {
    title: '角色',
    dataIndex: 'roles',
    width: '300px',
    scopedSlots: { customRender: 'roles' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '200px',
    scopedSlots: { customRender: 'action' },
  },
]
const routeColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '路径',
    dataIndex: 'path',
    key: 'path',
  },
  {
    title: 'Action',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'UserManager',
  components: {
    STable,
    permissionComp,
  },
  data() {
    this.routeColumns = routeColumns
    this.columns = columns
    this.pagination = pagination
    return {
      currentUser: {},
      userRouters: [],
      visible: false,
      labelCol: {},
      wrapperCol: {},
      confirmLoading: false,
      policyConfigVisible: false,
      bindingRoleVisible: false,
      oldUserInfo: {},
      policyForm: {
        name: '',
        targetPolicyKeys: [],
        email: '',
      },
      rules: {
        name: [{ required: true, message: '授权主体不存在', trigger: 'change' }],
      },
      // 查询参数
      queryParam: {},
      pageParam: {
        pageSize: 10,
        pageNo: 0,
      },
      roleList: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getUserList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            // 默认加入test角色
            res.Data.data.forEach(e => {
              e.rolesButtonDisable = true
              e.roles = e.roles ? e.roles : []
            })
            this.oldUserInfo = cloneDeep(res.Data.data)
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  created() {
    this.getRoleData()
  },
  methods: {
    handleCancel(e) {
      this.visible = false
    },
    // 递归获取路由path
    loopGetPath(route) {
      let childPath = []
      childPath.push(route.path)
      if (route.children && route.children.length) {
        for (let i = 0; i < route.children.length; i++) {
          childPath = [...childPath, ...this.loopGetPath(route.children[i])]
        }
      }
      return childPath
    },

    // 递归改变路由状态
    loopToogleChildAuth(route, auth) {
      // route.auth = auth
      this.$set(route, 'auth', auth)
      if (route.children && route.children.length) {
        for (let i = 0; i < route.children.length; i++) {
          this.loopToogleChildAuth(route.children[i], auth)
        }
      }
    },
    // 子级别启用父级也要启用
    changeParentStatus(parentPath) {
      for (let i = 0; i < this.userRouters.length; i++) {
        if (this.userRouters[i].path === parentPath && this.userRouters[i].auth === false) {
          this.$set(this.userRouters[i], 'auth', true)
          Auth({
            subType: 'user',
            subject: this.currentUser.email,
            paths: [this.userRouters[i].path],
          }).then(() => {})
        }
      }
    },
    // 弹窗切换路由启用信息
    topggleAuth(flag, routeInfos) {
      const childPath = this.loopGetPath(routeInfos)

      switch (flag) {
        case true:
          Auth({
            subType: 'user',
            subject: this.currentUser.email,
            paths: childPath,
          })
            .then(res => {
              this.loopToogleChildAuth(routeInfos, flag)

              if (routeInfos.parent) {
                this.changeParentStatus(routeInfos.parent)
              }
            })
            .catch(() => {})
          break
        case false:
          if (!routeInfos.fromRole) {
            unAuth({
              subType: 'user',
              subject: this.currentUser.email,
              paths: childPath,
            })
              .then(res => {
                this.loopToogleChildAuth(routeInfos, flag)
              })
              .catch(() => {})
            this.loopToogleChildAuth(routeInfos, flag)
          } else {
            this.$message.warning('该路由权限继承自角色，无法禁用')
          }

          break
      }
    },
    // 路由管理按钮
    handleRouter(user) {
      this.currentUser = user
      const params = {
        subType: 'user',
        subject: this.currentUser.email,
      }
      // 获取当前user 路由信息
      GetAuth(params)
        .then(res => {
          this.visible = true
          this.userRouters = res.routers
          this.loopDelKey(this.userRouters)
        })
        .catch(() => {})
    },
    loopDelKey(routes) {
      return routes.map(item => {
        if (item.children.length > 0) {
          this.loopDelKey(item.children)
        } else {
          delete item.children
        }
        return item
      })
    },
    getRoleData() {
      getRoleList().then(res => {
        res.Data.roles.forEach(element => {
          this.roleList.push({ value: element.roleName, label: element.roleName })
        })
      })
    },
    handlePolicy(user) {
      this.policyConfigVisible = true
      this.policyForm.email = user.email
      this.policyForm.name = user.name
    },
    onPolicyClose() {
      this.policyConfigVisible = false
      this.$refs.userTable.refresh(true)
    },
    onBindingRoleClose() {
      this.bindingRoleVisible = false
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
    onChange(nextTargetKeys) {
      this.policyForm.targetPolicyKeys = nextTargetKeys
    },
    getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
      return {
        getCheckboxProps: item => ({ props: { disabled: disabled || item.disabled } }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ title }) => title)
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys)
          itemSelectAll(diffKeys, selected)
        },
        onSelect({ title }, selected) {
          itemSelect(title, selected)
        },
        selectedRowKeys: selectedKeys,
      }
    },
    handleBindingRole() {
      this.bindingRoleVisible = true
    },
    bindingRole(user, roles) {
      user.roles = roles
      user.rolesButtonDisable = false
    },
    updateUserRoles(newUserInfo) {
      this.oldUserInfo.forEach(e => {
        if (e.uid === newUserInfo.uid) {
          var newRoles = newUserInfo.roles
          var oldRoles = e.roles
          var addRoles = newRoles.filter(function (val) {
            return oldRoles.indexOf(val) === -1
          })
          var deleteRoles = oldRoles.filter(function (val) {
            return newRoles.indexOf(val) === -1
          })
          if (addRoles.length !== 0) {
            roleBinding({
              user: e.email,
              roleNames: addRoles,
            }).then(res => {
              if (res === undefined) {
                notification.error({
                  message: '绑定失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (res.Code === 200) {
                this.$set(newUserInfo, 'rolesButtonDisable', true)
                notification.success({
                  message: '绑定成功',
                  description: '角色绑定成功',
                })
              } else {
                notification.error({
                  message: '绑定失败',
                  description: '绑定失败，请联系运维开发排查~',
                })
              }
            })
          }
          if (deleteRoles.length !== 0) {
            roleBindingDelete({
              user: e.email,
              roleNames: deleteRoles,
            }).then(res => {
              if (res === undefined) {
                notification.error({
                  message: '绑定失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (res.Code === 200) {
                this.$set(newUserInfo, 'rolesButtonDisable', true)
                this.$refs.userTable.refresh(true)
                notification.success({
                  message: '绑定成功',
                  description: '角色绑定成功',
                })
              } else {
                notification.error({
                  message: '绑定失败',
                  description: '绑定失败，请联系运维开发排查~',
                })
              }
            })
          }
        }
      })
    },
  },
}
</script>
