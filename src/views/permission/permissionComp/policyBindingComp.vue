<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="2" :sm="24">
            <span class="table-page-search-submitButtons">
              <tx-button type="primary" @click="handleBindingPolicy()">策略绑定</tx-button>
            </span>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="模糊查询">
              <a-input v-model:value="queryParam.searchText" placeholder="授权主体/策略名称" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <tx-button type="primary" @click="$refs.policyTable.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="policyTable"
      :rowKey="policy => policy.subject + policy.policyName"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
      showPagination="auto"
    >
      <template #bodyCell="{ column, record: policy }">
        <template v-if="column.dataIndex == 'handle'">
          <tx-button type="link" size="small" @click="policy" disabled>详情</tx-button>
          <tx-button type="link" size="small" @click="handleUnbind(policy)">解绑</tx-button>
        </template>
      </template>
    </s-table>
  </a-card>

  <a-drawer
    title="绑定策略"
    placement="right"
    :closable="false"
    width="40%"
    :visible="bindingPolicyVisible"
    @close="onBindingPolicyClose"
  >
    <a-form-model
      ref="bindingPolicyForm"
      :model="bindingPolicyForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="授权主体" name="subject">
        <a-select v-model:value="bindingPolicyForm.subject" @popupScroll="handleSubjectPopupScroll">
          <a-select-opt-group>
            <template #label>
              <a-icon type="user" />
              角色
            </template>
            <a-select-option v-for="item in roleOption" :key="item.key">
              {{ item.value }}
            </a-select-option>
          </a-select-opt-group>
          <a-select-opt-group>
            <template #label>
              <a-icon type="user" />
              用户
            </template>
            <a-select-option v-for="item in userOption" :key="item.key">
              {{ item.value }}
            </a-select-option>
          </a-select-opt-group>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="权限策略名称" name="policyNames">
        <a-select
          mode="multiple"
          v-model:value="bindingPolicyForm.policyNames"
          @popupScroll="handlePolicyNamesPopupScroll"
        >
          <a-select-option v-for="item in policyNamesOption" :key="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="授权主体类型" name="subjectType">
        <a-select v-model:value="bindingPolicyForm.subjectType">
          <a-select-option value="role">role</a-select-option>
          <a-select-option value="user">user</a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
        <tx-button type="primary" @click="onBindingPolicySubmit">创建</tx-button>
        <tx-button style="margin-left: 10px" @click="resetBindingPolicyForm">重置</tx-button>
      </a-form-model-item>
    </a-form-model>
  </a-drawer>
</template>
<script>
import { createSubjectPolicy, subjectPolicyList, getPolicyData, deleteSubjectPolicy } from '@/api/permission/policy'
import { getUserList } from '@/api/permission/user'
import { getRoleList } from '@/api/permission/role'
import permissionComp from './index.vue'
// import permissionComp from './permissionComp/index.vue'
import { STable } from '@/components'
import { Transfer, notification } from 'ant-design-vue'
const columns = [
  {
    title: '授权主体',
    dataIndex: 'subject',
  },
  {
    title: '授权类型',
    dataIndex: 'subjectType',
  },
  {
    title: '策略名称',
    dataIndex: 'policyName',
  },
  {
    title: '授权时间',
    dataIndex: 'authorizeTime',
  },
  {
    title: '操作',
    dataIndex: 'handle',
    width: '200px',
    scopedSlots: { customRender: 'handle' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'PolicyBinding',
  components: {
    STable,
    aTransfer: Transfer,
    permissionComp,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: {},
      wrapperCol: {},
      confirmLoading: false,
      bindingPolicyVisible: false,
      roleOption: [],
      roleOptionPageNo: 1,
      userOption: [],
      userOptionPageNo: 1,
      policyNamesOption: [],
      policyNamesOptionPageNo: 1,
      bindingPolicyForm: {
        policyNames: [],
        subject: '',
        subjectType: '',
      },
      rules: {
        policyNames: [{ required: true, message: '权限策略未选择', trigger: 'change' }],
        subject: [{ required: true, message: '授权主体未选择', trigger: 'change' }],
        subjectType: [{ required: true, message: '授权主体类型未选择', trigger: 'change' }],
      },
      // 查询参数
      queryParam: {},
      subjectPageParam: {
        pageSize: 10,
        pageNo: 1,
      },
      policyNamesParam: {
        pageSize: 10,
        pageNo: 1,
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        console.log(parameter)
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return subjectPolicyList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('permissions')) {
            return {
              data: res.Data.permissions,
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage,
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  created() {
    this.getSubjectOption()
    this.getPolicyNamesOption()
  },
  methods: {
    onBindingPolicyClose() {
      this.bindingPolicyVisible = false
    },
    resetBindingPolicyForm() {
      this.$refs.bindingPolicyForm.resetFields()
    },
    onBindingPolicySubmit() {
      antdFormValidate(this.$refs.bindingPolicyForm, valid => {
        if (valid) {
          const requestParameters = Object.assign({}, this.bindingPolicyForm)
          // requestParameters.policyNames = requestParameters.policyNames.join('|')
          console.log(requestParameters)
          createSubjectPolicy(requestParameters).then(res => {
            if (res === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (res.Code === 200) {
              notification.success({
                message: '创建成功',
                description: '绑定关系创建成功',
              })
              this.$refs.policyTable.refresh(true)
              this.onBindingPolicyClose()
            } else {
              notification.error({
                message: '创建失败',
                description: '创建失败，请联系运维开发排查~',
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleBindingPolicy() {
      this.bindingPolicyVisible = true
    },
    getPolicyNamesOption() {
      if (this.policyNamesOptionPageNo >= this.policyNamesParam.pageNo) {
        this.policyNamesParam.pageNo = this.policyNamesOptionPageNo
        getPolicyData(this.policyNamesParam).then(res => {
          this.policyNamesOptionPageNo = res.Data.totalPage
          res.Data.policies.forEach(item => {
            this.policyNamesOption.push({
              value: item.policyName,
              key: item.policyName,
            })
          })
          console.log(res)
        })
      }
    },
    getSubjectOption() {
      if (this.userOptionPageNo >= this.subjectPageParam.pageNo) {
        this.subjectPageParam.pageNo = this.userOptionPageNo
        getUserList(this.subjectPageParam).then(res => {
          this.userOptionPageNo = res.Data.totalPage
          res.Data.data.forEach(item => {
            this.userOption.push({
              value: item.name,
              key: item.email,
            })
          })
          console.log(res)
        })
      }
      if (this.roleOptionPageNo >= this.subjectPageParam.pageNo) {
        this.subjectPageParam.pageNo = this.roleOptionPageNo
        getRoleList(this.subjectPageParam).then(res => {
          this.roleOptionPageNo = res.Data.totalPage
          res.Data.roles.forEach(item => {
            this.roleOption.push({
              value: item.roleDesc,
              key: item.roleName,
            })
          })
        })
      }
    },
    handleSubjectPopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - clientHeight === 0) {
        this.subjectPageParam.pageNo += 1
        console.log(this.subjectPageParam, 'handleSubjectPopupScroll')
        this.getSubjectOption()
      }
    },
    handlePolicyNamesPopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - clientHeight === 0) {
        this.policyNamesParam.pageNo += 1
        this.getPolicyNamesOption()
      }
    },
    handleUnbind(policy) {
      var that = this
      this.$confirm({
        title: '是否要解绑?',
        content: '解绑后，该用户将失去相应的权限',
        onOk() {
          return new Promise((resolve, reject) => {
            deleteSubjectPolicy(policy).then(res => {
              if (res === undefined) {
                notification.error({
                  message: '解绑失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (res.Code === 200) {
                notification.success({
                  message: '解绑成功',
                  description: '绑定关系解除成功',
                })
                that.$refs.policyTable.refresh(true)
                resolve()
              } else {
                notification.error({
                  message: '解绑失败',
                  description: '解绑失败，请联系运维开发排查~',
                })
              }
            })
          }).catch(() => console.log('Oops errors!'))
        },
        onCancel() {},
      })
    },
  },
}
</script>
