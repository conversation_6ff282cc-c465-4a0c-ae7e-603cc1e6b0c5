<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="模糊查询">
              <a-input v-model:value="queryParam.searchText" placeholder="服务/用户/URL" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="开始时间" name="beginTime">
              <a-date-picker
                v-model:value="queryParam.beginTime"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                show-time
                show-now
                placeholder="开始时间"
                style="width: 100%"
                :disabled-date="disabledStartDate" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="结束时间" name="endTime">
              <a-date-picker
                v-model:value="queryParam.endTime"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                show-time
                show-now
                placeholder="结束时间"
                style="width: 100%"
                :disabled-date="disabledEndDate" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="来源IP">
              <a-input v-model:value="queryParam.sourceIp" placeholder="模糊查询" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="状态码">
              <a-input-number v-model:value="queryParam.status" placeholder="请输入" :min="0" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="敏感度">
              <a-select placeholder="请选择" v-model:value="queryParam.level" style="width: 100%" allow-clear>
                <a-select-option value="高">高</a-select-option>
                <a-select-option value="中">中</a-select-option>
                <a-select-option value="低">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <span class="table-page-search-submitButtons">
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      :rowKey="record => record.id + record.serverName + record.method + record.path + record.operator + record.operatingTime"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{column, record: record}">
        <template v-if="column.dataIndex === 'level'">
          <a-tag :key="record.level"
                 :color="record.level === '高' ? 'red' : record.level === '中' ? 'orange' : 'green'">
            {{ record.level }}
          </a-tag>
        </template>
      </template>

      <template #expandedRowRender="{ record }">
        <a-descriptions>
<!--          <a-descriptions-item label="TraceID">{{ record.traceId }}</a-descriptions-item>-->
          <a-descriptions-item label="请求方法">{{ record.method }}</a-descriptions-item>
          <a-descriptions-item label="路由">{{ record.path }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ record.createdTime }}</a-descriptions-item>
<!--          <a-descriptions-item label="备注">{{ record.remark }}</a-descriptions-item>-->
          <a-descriptions-item label="请求参数">{{ record.parameter }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions>
          <a-descriptions-item label="客户端">{{ record.remark }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </s-table>
  </a-card>
</template>
<script>
import { listAuthCloudRequestLog } from '@/api/permission/cloudRequestLog'
import { getUserList } from '@/api/permission/user'
import { getRoleList } from '@/api/permission/role'
import permissionComp from './index.vue'
import { STable } from '@/components'
import { Transfer, notification } from 'ant-design-vue'
import moment from 'moment'

const columns = [
  {
    title: '服务名称',
    dataIndex: 'serverName'
  },
  {
    title: '操作方法',
    dataIndex: 'operation'
  },
  {
    title: '操作用户',
    dataIndex: 'operator'
  },
  {
    title: '来源IP',
    dataIndex: 'sourceIp'
  },
  {
    title: '敏感度',
    dataIndex: 'level',
    scopedSlots: { customRender: 'level' }
  },
  {
    title: '状态码',
    dataIndex: 'status'
  },
  {
    title: '操作时间',
    dataIndex: 'operatingTime'
  }
]
const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'CloudRequestLog',
  components: {
    STable,
    aTransfer: Transfer,
    permissionComp
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: {},
      wrapperCol: {},
      confirmLoading: false,
      bindingPolicyVisible: false,
      roleOption: [],
      roleOptionPageNo: 1,
      userOption: [],
      userOptionPageNo: 1,
      policyNamesOption: [],
      policyNamesOptionPageNo: 1,
      bindingPolicyForm: {
        policyNames: [],
        subject: ''
      },
      // 查询参数
      queryParam: {
        beginTime: moment().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss')
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return listAuthCloudRequestLog(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            return {
              data: res.Data.data,
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  methods: {
    disabledStartDate (startValue) {
      const endValue = this.queryParam.endTime
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= moment(endValue.valueOf())
    },
    disabledEndDate (endValue) {
      const startValue = this.queryParam.startTime
      if (!endValue || !startValue) {
        return false
      }
      return moment(startValue.valueOf()) >= endValue.valueOf()
    }
  }
}
</script>
