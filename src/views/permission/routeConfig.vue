<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-06 17:20:44
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-06 17:21:54
 * @FilePath: \cloud_web\src\views\permission\routeConfig.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-08-18 14:26:40
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-03 11:24:05
 * @FilePath: \cloud_web\src\views\permission\routeConfig.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <RouteConfigComp></RouteConfigComp>
    </SecondaryVerification>
  </page-header-wrapper>
</template>
<script>
import SecondaryVerification from '@/components/Verification/verify.vue'

import RouteConfigComp from './permissionComp/routeConfigComp.vue'
export default {
  components: {
    RouteConfigComp,
    SecondaryVerification,
  },
}
</script>
