<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <a-card :bordered="false">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="2" :sm="24">
                <span class="table-page-search-submitButtons">
                  <tx-button type="primary" @click="() => (this.policyConfigVisible = true)">创建策略</tx-button>
                </span>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="模糊查询">
                  <a-input v-model:value="queryParam.searchText" placeholder="权限策略名称/备注" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <span class="table-page-search-submitButtons">
                  <!-- <tx-button type="primary" @click="handleFilter">查询</tx-button> -->
                  <tx-button type="primary" @click="$refs.policyTable.refresh(true)">查询</tx-button>
                  <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <s-table
          ref="policyTable"
          :rowKey="policy => policy.policyName"
          :pagination="pagination"
          :columns="columns"
          :data="loadData"
        >
          <template #bodyCell="{ column, record: policy }">
            <template v-if="column.dataIndex == 'action'">
              {{ policy.action.join('|') }}
              <!-- <tx-button type="link" size="small" @click="policy" disabled>{{ record.action }}</tx-button> -->
            </template>
            <template v-if="column.dataIndex == 'handle'">
              <tx-button type="link" size="small" @click="policy" disabled>详情</tx-button>
            </template>
          </template>
        </s-table>
      </a-card>

      <a-drawer
        title="创建策略"
        placement="right"
        :closable="false"
        width="40%"
        :visible="policyConfigVisible"
        @close="onPolicyClose"
      >
        <a-form-model
          ref="policyForm"
          :model="policyForm"
          :rules="rules"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-form-model-item label="权限策略名称" name="policyName">
            <a-input v-model:value="policyForm.policyName" aria-disabled="true" />
          </a-form-model-item>
          <a-form-model-item label="服务" name="domain">
            <a-radio-group v-model:value="policyForm.domain" @change="changeResourceConfig">
              <a-radio value="ECS:cloud-server">云服务器</a-radio>
              <a-radio value="Cost:cost-date">日账单</a-radio>
              <a-radio value="Cost:cost-month">月账单</a-radio>
              <a-radio value="Cost:cost-label">账单标签</a-radio>
              <a-radio value="Domain:domain-list">域名</a-radio>
              <a-radio value="Domain:dns-list">DNS</a-radio>
              <a-radio value="Domain:icp-list">备案</a-radio>
              <a-radio value="Domain:ssl-list">SSL</a-radio>
              <a-radio value="Osp:cloud-server">云存储</a-radio>
              <a-radio value="Gateway:cluster-server">集群</a-radio>
              <a-radio value="Gateway:upstream-server">Upstream</a-radio>
              <a-radio value="Gateway:route-server">路由</a-radio>
              <a-radio value="Db:drms-server">DRMS</a-radio>
              <a-radio value="Osp:share-server">共享云存储</a-radio>
              <a-radio value="Desktop:desktop-list">云桌面</a-radio>
              <a-radio value="PublicAccount:publicAccount-list">公有云账户</a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="操作" name="action">
            <a-checkbox-group style="width: 220px" v-model:value="policyForm.action">
              <a-row style="width: 100%">
                <a-col :span="8">
                  <a-checkbox value="get">查看</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="delete">删除</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="post">更新</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-model-item>
          <a-form-model-item label="资源" name="resource">
            <a-alert>
              <template #message>
                <a-row>
                  <a-col :span="20">
                    <span>权限格式：/orgs/:org/deps/:dep/principals/dongxu/assets/:assets</span>
                    <br />
                    <span>{{ policyForm.resource }}</span>
                  </a-col>
                  <a-col :span="4">
                    <a-tooltip placement="top" v-if="!policyForm.domain">
                      <template #title>
                        <span>请先选择服务</span>
                      </template>
                      <tx-button type="link" disabled>添加资源</tx-button>
                    </a-tooltip>
                    <tx-button v-else type="link" :disabled="!policyForm.domain" @click="handelAddResource">
                      添加资源
                    </tx-button>
                  </a-col>
                </a-row>
              </template>
            </a-alert>
          </a-form-model-item>
          <a-form-model-item label="备注" name="desc">
            <a-input v-model:value="policyForm.policyDesc" aria-disabled="true" />
          </a-form-model-item>
          <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
            <tx-button type="primary" @click="onCreatePolicySubmit">创建</tx-button>
            <tx-button style="margin-left: 10px" @click="resetPolicyForm">重置</tx-button>
          </a-form-model-item>
        </a-form-model>
      </a-drawer>

      <a-modal
        title="添加资源"
        :visible="modalFormCreateResourceVisible"
        @ok="handleOk"
        width="50%"
        @cancel="modalFormCreateResourceVisible = false"
      >
        <a-alert type="info" show-icon style="margin-bottom: 20px">
          <template #message>
            <span>资源 ARN</span>
            <br />
            <span>资源 ARN 是描述资源范围的表达式，参考帮助文档。</span>
            <br />
            <span>当前输入的 ARN 是：{{ policyForm.resource }}。</span>
          </template>
        </a-alert>
        <a-form-model
          ref="preDataForm"
          :rules="rules"
          :model="resourceConfig"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 19 }"
        >
          <a-form-model-item
            v-for="item in resourceConfigTagOptions[policyForm.domain]"
            :key="item.value"
            :label="item.label"
          >
            <a-row>
              <a-col :span="18">
                <a-input v-model:value="resourceConfig[item.value]" placeholder="值" style="width: 90%" />
              </a-col>
              <a-col :span="6">
                <a-checkbox
                  :checked="resourceConfig[item.value].indexOf(':' + item.value) !== -1 ? true : false"
                  :value="item.value"
                  @change="onChangeMatch"
                >
                  匹配全部
                </a-checkbox>
              </a-col>
            </a-row>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </SecondaryVerification>
  </page-header-wrapper>
</template>

<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import { createPolicy, getPolicyData } from '@/api/permission/policy'
import permissionComp from './permissionComp/index.vue'
import { STable } from '@/components'
import { Transfer, notification } from 'ant-design-vue'
import { difference } from 'lodash'

const columns = [
  {
    title: '权限策略名称',
    dataIndex: 'policyName',
  },
  {
    title: '资源',
    dataIndex: 'resource',
  },
  {
    title: 'CURD操作',
    dataIndex: 'action',
  },
  {
    title: '备注',
    dataIndex: 'policyDesc',
  },
  {
    title: '操作',
    dataIndex: 'handle',
    width: '200px',
    scopedSlots: { customRender: 'handle' },
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'PolicyManager',
  components: {
    SecondaryVerification,
    STable,
    aTransfer: Transfer,
    permissionComp,
  },
  watch: {
    resourceConfig: {
      handler: function (val) {
        this.policyForm.resource = ''
        for (var item in val) {
          if (item === 'principals' && val[item] !== '' && val[item].indexOf(':') === -1) {
            this.policyForm.resource += `/${item}/?${val[item]}`
          } else if (val[item] !== '') {
            this.policyForm.resource += `/${item}/${val[item]}`
          }
        }
      },
      deep: true,
    },
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    const validateResource = (rule, value, callback) => {
      callback = antdFormValidateCallback
      if (value.length === 0) {
        return callback(new Error('资源未填写'))
      } else {
        for (var item in this.resourceConfig) {
          if (this.resourceConfig[item] === '') {
            this.resourceConfigTagOptions[this.policyForm.domain].forEach(res => {
              if (res.value === item) {
                return callback(new Error(`${res.label}未填写`))
                return true
              }
            })
          }
        }
        return callback()
      }
    }
    return {
      labelCol: {},
      wrapperCol: {},
      confirmLoading: false,
      modalFormCreateResourceVisible: false,
      policyConfigVisible: false,
      policyForm: {
        policyName: '',
        domain: '',
        resource: '',
      },
      rules: {
        policyName: [{ required: true, message: '权限策略未填写', trigger: 'change' }],
        domain: [{ required: true, message: '服务未填写', trigger: 'change' }],
        action: [{ required: true, message: '操作未填写', trigger: 'change' }],
        resource: [{ required: true, trigger: 'change', validator: validateResource }],
      },
      resourceOption: [{ name: '', value: '' }],
      resourceConfigTagOptions: {
        'ECS:cloud-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
          { value: 'assets', label: '服务器ID' },
        ],
        'Cost:cost-date': [
          { value: 'orgs', label: '事业部' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Cost:cost-month': [
          { value: 'orgs', label: '事业部' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Cost:cost-label': [
          { value: 'orgs', label: '事业部' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Domain:domain-list': [{ value: 'domains', label: '域名ID' }],
        'Domain:dns-list': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
          { value: 'dnses', label: '域名解析ID' },
        ],
        'Domain:icp-list': [{ value: 'icps', label: '域名备案ID' }],
        'Domain:ssl-list': [{ value: 'ssls', label: '域名证书ID' }],
        'Cost:account-list': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'supplier', label: '供应商' },
          { value: 'accounts', label: '详单ID' },
        ],
        'Osp:cloud-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Gateway:cluster-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Gateway:upstream-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Gateway:route-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Db:drms-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Osp:share-server': [
          { value: 'orgs', label: '组织' },
          { value: 'deps', label: '部门' },
          { value: 'principals', label: '负责人' },
        ],
        'Desktop:desktop-list': [{ value: 'emails', label: '邮箱' }],
        'PublicAccount:publicAccount-list': [{ value: 'emails', label: '邮箱' }],
      },
      resourceConfig: {
        orgs: '',
        deps: '',
        principals: '',
        assets: '',
        supplier: '',
        accounts: '',
        domains: '',
        dnses: '',
        icps: '',
        ssls: '',
        emails: '',
      },
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getPolicyData(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('policies')) {
            res.Data.policies.forEach(item => {
              item.action = item.action.split('|')
              for (let i = 0; i < item.action.length; i++) {
                if (item.action[i] == 'get') {
                  item.action[i] = '查看'
                } else if (item.action[i] == 'delete') {
                  item.action[i] = '删除'
                } else if (item.action[i] == 'post') {
                  item.action[i] = '更新'
                }
              }
            })
            console.log(res.Data.policies, 'res.Data.policies')
            return {
              data: res.Data.policies,
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage,
            }
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  methods: {
    onVerificationSuccess() {},
    onPolicyClose() {
      this.policyConfigVisible = false
    },
    resetPolicyForm() {
      this.$refs.policyForm.resetFields()
    },
    onCreatePolicySubmit() {
      antdFormValidate(this.$refs.policyForm, valid => {
        if (valid) {
          const requestParameters = Object.assign({}, this.policyForm)
          requestParameters.action = requestParameters.action.join('|')
          createPolicy(requestParameters).then(res => {
            if (res === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (res.Code === 200) {
              this.policyConfigVisible = false
              this.$refs.policyTable.refresh(true)
              notification.success({
                message: '创建成功',
                description: '权限策略创建成功',
              })
            } else {
              notification.error({
                message: '创建失败',
                description: res.Message,
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    onCheckboxChange(checkedValues) {
      this.policyForm.action = checkedValues
    },
    getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
      return {
        getCheckboxProps: item => ({ props: { disabled: disabled || item.disabled } }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ title }) => title)
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys)
          itemSelectAll(diffKeys, selected)
        },
        onSelect({ title }, selected) {
          itemSelect(title, selected)
        },
        selectedRowKeys: selectedKeys,
      }
    },
    handleOk() {
      this.modalFormCreateResourceVisible = false
    },
    onChangeMatch(e) {
      if (e.target.checked) {
        this.resourceConfig[e.target.value] = `:${e.target.value}`
      } else {
        this.resourceConfig[e.target.value] = ''
      }
    },
    handelAddResource() {
      this.modalFormCreateResourceVisible = true
      // this.policyForm.resource = ''
      //   for (var item in this.resourceConfig) {
      //     this.policyForm.resource += `/${item}/${this.resourceConfig[item]}`
      //   }
    },
    changeResourceConfig(e) {
      this.policyForm.resource = ''
      for (var item in this.resourceConfig) {
        this.resourceConfig[item] = ''
      }
      this.resourceConfigTagOptions[this.policyForm.domain].forEach(item => {
        console.log(item)
      })
    },
  },
}
</script>
