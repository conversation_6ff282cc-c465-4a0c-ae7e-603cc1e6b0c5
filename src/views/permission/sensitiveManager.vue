<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-16 17:20:44
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-06 17:21:54
 * @FilePath: \cloud_web\src\views\permission\routeConfig.vue
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <sensitiveManagerComp></sensitiveManagerComp>
    </SecondaryVerification>
  </page-header-wrapper>
</template>
<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import sensitiveManagerComp from './permissionComp/sensitiveManagerComp.vue'
export default {
  components: {
    SecondaryVerification,
    sensitiveManagerComp,
  },
}
</script>
