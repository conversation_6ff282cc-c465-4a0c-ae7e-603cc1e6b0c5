<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <a-card :bordered="false">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="2" :sm="24">
                <span class="table-page-search-submitButtons">
                  <tx-button type="primary" @click="() => (this.roleConfigVisible = true)">创建角色</tx-button>
                </span>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="模糊查询">
                  <a-input v-model:value="queryParam.searchText" placeholder="角色/描述" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <span class="table-page-search-submitButtons">
                  <tx-button type="primary" @click="$refs.roleTable.refresh(true)">查询</tx-button>
                  <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <s-table
          ref="roleTable"
          :rowKey="role => role.roleName"
          :pagination="pagination"
          :columns="columns"
          :data="loadData"
        >
          <template #bodyCell="{ column, record: role }">
            <template v-if="column.dataIndex == 'action'">
              <tx-button type="link" size="small" @click="handlePolicy(role)">权限策略</tx-button>
              <tx-button type="link" size="small" @click="handleRouter(role)">路由策略</tx-button>
              <tx-button type="link" size="small" @click="handleRoleInfo(role, 'member')">成员列表</tx-button>
              <tx-button type="link" size="small" @click="handleRoleInfo(role, 'router')">后端路由</tx-button>
              <tx-button type="link" size="small" @click="handleRoleInfo(role, 'edit')">编辑</tx-button>
              <tx-button type="link" size="small" @click="handleDelete(role)">删除</tx-button>
            </template>
          </template>
        </s-table>
      </a-card>
      <permission-comp
        :policyConfigVisible="policyConfigVisible"
        :policyForm="policyForm"
        @onClose="onPolicyClose"
      ></permission-comp>
      <!-- 路由配置 -->

      <a-modal :width="800" title="角色路由管理" v-model:visible="visible">
        <a-table :columns="routeColumns" rowKey="name" :data-source="userRouters">
          <!-- " -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.title == 'Action'">
              <tx-button style="margin-left: 8px" v-if="!record.auth" @click="topggleAuth(true, record)">
                启用
              </tx-button>
              <tx-button style="margin-left: 8px" v-else @click="topggleAuth(false, record)" type="danger">
                禁用
              </tx-button>
            </template>
          </template>
        </a-table>
        <template #footer>
          <tx-button key="back" @click="visible = false">关闭</tx-button>
        </template>
      </a-modal>
      <a-modal
        @cancel="cancelModal"
        :width="800"
        :title="handleRoleModalTitle"
        v-model:visible="handleRoleVis"
        @ok="handleOk"
      >
        <template v-if="handleRoleModalTitle == '查看用户列表'">
          <a-row>
            <a-col :span="3"><span style="display: inline-block; line-height: 36px">模糊查询：</span></a-col>
            <a-col :span="18">
              <a-row>
                <a-col :span="12">
                  <a-input placeholder="姓名/邮箱/二级组织/三级组织/团队" v-model:value="searchUser"></a-input>
                </a-col>
                <a-col :span="8">
                  <a-button style="margin-left: 14px" @click="queryUserList" type="primary">查询</a-button>
                  <a-button style="margin-left: 14px" @click="resetQueryUser">重置</a-button>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
          <a-table
            style="margin-top: 14px"
            :columns="userColumns"
            :pagination="rolePage"
            :data-source="userData"
            @change="handleRoleChange"
          ></a-table>
        </template>
        <template v-if="handleRoleModalTitle == '后端路由管理'">
          <a-button @click="addRoute" type="primary">
            <template #icon>
              <plus-outlined />
            </template>
            新建路由
          </a-button>
          <a-table
            style="margin-top: 14px"
            :columns="backendRouterColumns"
            :pagination="rolePage"
            :data-source="backendRouterData"
            @change="handRouteChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'method'">
                <a-select
                  :disabled="!record.edit"
                  v-model:value="record.method"
                  mode="multiple"
                  placeholder="请选择方法"
                  style="width: 220px"
                >
                  <a-select-option value="GET">get</a-select-option>
                  <a-select-option value="POST">post</a-select-option>
                  <a-select-option value="PUT">put</a-select-option>
                  <a-select-option value="DELETE">delete</a-select-option>
                </a-select>
              </template>
              <template v-if="column.key === 'path'">
                <!-- <a-select v-model:value="record.path" placeholder="Please select" style="width: 300px">
                  <a-select-option value="11111">11111</a-select-option>
                  <a-select-option value="222222">222222</a-select-option>
                </a-select> -->
                <a-select
                  :disabled="!record.edit"
                  v-model:value="record.path"
                  mode="tags"
                  style="width: 200px"
                  placeholder="请选择路由"
                  @change="routeSelectChange($event, record)"
                >
                  <a-select-option v-for="i in options" :value="i" :key="i">
                    <a-tooltip>
                      <template #title>{{ i }}</template>
                      {{ i }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </template>
              <template v-if="column.key === 'opt'">
                <a-button type="link" v-if="!record.edit" @click="cloumnOpt('edit', record)">编辑</a-button>
                <a-button type="link" v-if="!record.edit">
                  <a-popconfirm
                    title="确认删除该后端路由吗?"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="cloumnOpt('del', record)"
                  >
                    <a href="#">删除</a>
                  </a-popconfirm>
                </a-button>
                <a-button type="link" @click="cloumnOpt('save', record)" v-if="record.edit">保存</a-button>
                <a-button type="link" @click="cloumnOpt('cancel', record)" v-if="record.edit">取消</a-button>
              </template>
            </template>
          </a-table>
        </template>
        <template v-if="handleRoleModalTitle == '编辑角色'">
          <a-form
            :model="editRoleForm"
            name="basic"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
            autocomplete="off"
          >
            <a-form-item
              label="角色名称"
              :required="true"
              name="roleName"
              :rules="[{ required: true, message: '请输入角色名称' }]"
            >
              <a-input :disabled="true" v-model:value="editRoleForm.roleName" />
            </a-form-item>
            <a-form-item label="描述" :required="true" name="roleDesc">
              <a-input v-model:value="editRoleForm.roleDesc" />
            </a-form-item>
          </a-form>
        </template>
      </a-modal>
      <a-drawer
        title="创建角色"
        placement="right"
        :closable="false"
        width="30%"
        :visible="roleConfigVisible"
        @close="onRoleClose"
      >
        <a-form-model ref="roleForms" :model="roleForm" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item :required="true" label="角色名称" name="roleName">
            <a-input v-model:value="roleForm.roleName" aria-disabled="true" />
          </a-form-model-item>
          <a-form-model-item :required="true" label="备注" name="roleDesc">
            <a-input v-model:value="roleForm.roleDesc" aria-disabled="true" />
          </a-form-model-item>
          <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
            <tx-button type="primary" @click="onCreateRoleSubmit">创建</tx-button>
            <tx-button style="margin-left: 10px" @click="resetRoleForm">重置</tx-button>
          </a-form-model-item>
        </a-form-model>
      </a-drawer>
    </SecondaryVerification>
  </page-header-wrapper>
</template>

<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import {
  getRoleList,
  createRole,
  deleteRole,
  getRoleUsesrList,
  getBackendRoute,
  getRoleBindRoute,
  UpdateRoleBindRoute,
  DelRoleBindRoute,
  UpdateRoleInfo,
} from '@/api/permission/role'
import { GetAuth, Auth, unAuth } from '@/api/routePermission/index'

import permissionComp from './permissionComp/index.vue'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { difference, cloneDeep } from 'lodash'
const columns = [
  {
    title: '角色',
    dataIndex: 'roleName',
  },
  {
    title: '描述',
    dataIndex: 'roleDesc',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    minWidth: '200px',
    scopedSlots: { customRender: 'action' },
  },
]
const routeColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '路径',
    dataIndex: 'path',
    key: 'path',
  },
  {
    title: 'Action',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

const leftTableColumns = [
  {
    dataIndex: 'title',
    title: 'Name',
  },
  {
    dataIndex: 'description',
    title: '描述',
  },
]
const rightTableColumns = [
  {
    dataIndex: 'title',
    title: '名称',
  },
]
const pagination = {
  showTotal: total => `共 ${total} 条`,
}
const userColumns = [
  {
    title: 'Uid',
    dataIndex: 'uid',
    key: 'uid',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '邮箱',
    key: 'email',
    dataIndex: 'email',
  },
  {
    title: '二级组织',
    key: 'secondary_organization',
    dataIndex: 'secondary_organization',
  },
  {
    title: '三级组织',
    key: 'tertiary_organization',
    dataIndex: 'tertiary_organization',
  },
  {
    title: '团队',
    key: 'team',
    dataIndex: 'team',
  },
]

const backendRouterColumns = [
  {
    title: '路由',
    key: 'path',
    dataIndex: 'path',
  },
  {
    title: '方法',
    key: 'method',
    dataIndex: 'method',
  },
  {
    title: '操作',
    key: 'opt',
    dataIndex: 'opt',
  },
]
const backendRouterData = []
export default {
  name: 'UserManager',
  components: {
    STable,
    SecondaryVerification,
    permissionComp,
  },

  data() {
    this.routeColumns = routeColumns
    this.columns = columns
    this.pagination = pagination
    return {
      queryParams: {},
      searchUser: '',
      cloneRecord: null,
      editFlag: true,
      options: [],
      backendRouterColumns,
      backendRouterData,
      userColumns,
      userData: [],
      currentRole: null,
      rolePage: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      handleRoleVis: false,
      handleRoleModalTitle: '',
      visible: false,
      userRouters: [],
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      confirmLoading: false,
      policyConfigVisible: false,
      roleConfigVisible: false,
      policyForm: {
        name: '',
        targetPolicyKeys: [],
        email: '',
      },
      currentRule: null,
      roleForm: {
        roleName: '',
        roleDesc: '',
      },
      rules: {
        roleName: [{ required: true, message: '角色名称必填', trigger: 'change' }],
        roleDesc: [{ required: true, message: '备注必填', trigger: 'change' }],
      },
      leftColumns: leftTableColumns,
      rightColumns: rightTableColumns,
      policyData: [
        {
          key: '1',
          title: '云服务器查询权限',
          description: '可查看云服务器所有内容',
        },
      ],
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getRoleList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('roles')) {
            return {
              data: res.Data.roles,
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage,
            }
          } else {
            return {
              data: [
                {
                  roleName: 'CS查询管理员',
                  roleDesc: '可查询cs相关服务',
                  createTime: '',
                },
              ],
              pageNo: 1,
              pageSize: 10,
              totalCount: 0,
              totalPage: 0,
            }
          }
        })
      },
      editRoleForm: {
        roleName: '',
        roleDesc: '',
      },
    }
  },
  mounted() {
    // getBackendRoute()
    //   .then(res => {
    //     console.log(res, 'res')
    //   })
    //   .catch(err => {
    //     console.log(err, 'err')
    //   })
  },
  methods: {
    handleRoleInfo(role, type) {
      this.currentRole = role
      this.handleRoleVis = true
      switch (type) {
        case 'member':
          this.handleRoleModalTitle = '查看用户列表'
          console.log(role, 'role')
          this.handleRoleChange()
          break
        case 'router':
          //获取路由下拉列表
          getBackendRoute()
            .then(res => {
              console.log(res, 'res111')
              this.options = res.Data.resList
            })
            .catch(err => {
              console.log(err, 'err111')
            })
          this.handleRoleModalTitle = '后端路由管理'
          this.handRouteChange()
          break
        case 'edit':
          this.editRoleForm.roleName = role.roleName
          this.editRoleForm.roleDesc = role.roleDesc
          console.log(role, 'r')
          this.handleRoleModalTitle = '编辑角色'
          break
      }
    },
    routeSelectChange(value, record) {
      console.log(value)
      console.log(record)
      if (value.length == 2) {
        setTimeout(() => {
          record.path = [value[1]]
        })
      }
    },
    handleRoleChange(page = null) {
      if (page) {
        this.rolePage.total = page.total
        this.rolePage.current = page.current
        this.rolePage.pageSize = page.pageSize
      }
      this.queryParams.roleName = this.currentRole.roleName
      this.queryParams.pageNo = this.rolePage.current
      this.queryParams.pageSize = this.rolePage.pageSize
      getRoleUsesrList(this.queryParams)
        .then(res => {
          console.log(res, 'resss')
          this.userData = res.Data.users
          this.rolePage.total = res.Data.totalCount
          this.rolePage.current = res.Data.pageNo
          this.rolePage.pageSize = res.Data.pageSize
        })
        .catch(err => {
          console.log(err, 'err')
        })
    },
    handRouteChange(page = null) {
      if (page) {
        this.rolePage.total = page.total
        this.rolePage.current = page.current
        this.rolePage.pageSize = page.pageSize
      }
      getRoleBindRoute({
        roleName: this.currentRole.roleName,
        pageNo: this.rolePage.current,
        pageSize: this.rolePage.pageSize,
      })
        .then(res => {
          console.log(res, 'resss')
          if (res.Data.data) {
            this.backendRouterData = res.Data.data
            this.backendRouterData.forEach(item => {
              item.method = item.method.split('|')
              item.path = [item.path]
            })
            this.rolePage.total = res.Data.totalCount
            this.rolePage.current = res.Data.pageNo
            this.rolePage.pageSize = res.Data.pageSize
          } else {
            this.backendRouterData = []
            this.rolePage.total = 0
            this.rolePage.current = 1
            this.rolePage.pageSize = 10
          }
        })
        .catch(err => {
          console.log(err, 'err')
        })
    },
    addRoute() {
      console.log(this.backendRouterData, 'this.backendRouterData')
      if (!this.editFlag) {
        this.$message.warning('请先保存当前修改')
        return
      }
      if (this.backendRouterData.length) {
        this.backendRouterData = [
          {
            path: [],
            method: [],
            type: 'add',
            edit: true,
            id: 0,
          },
          ...this.backendRouterData,
        ]
      } else {
        this.backendRouterData.push({
          path: [],
          method: [],
          type: 'add',
          edit: true,
          id: 0,
        })
      }
    },
    cloumnOpt(type, record) {
      console.log(record, 'record')
      switch (type) {
        case 'edit':
          if (this.editFlag) {
            record.edit = true
            this.editFlag = false
            this.cloneRecord = cloneDeep(record)
            console.log(this.cloneRecord, 'this.cloneRecord')
          } else {
            this.$message.warning('每次只能编辑一条数据')
          }
          break
        case 'del':
          DelRoleBindRoute({ id: record.id })
            .then(res => {
              console.log(res, 'ress222')
              this.$message.success('删除成功')

              this.handRouteChange()
            })
            .catch(err => {})
          break
        case 'save':
          if (record.method.length == 0 || record.path.length == 0) {
            this.$message.warning('路由和方法为必填项')
            return
          }
          console.log(record, 'rrr')
          let params = {
            roleName: this.currentRole.roleName,
            path: record.path[0],
            method: record.method.join('|'),
            id: record.id,
          }
          UpdateRoleBindRoute(params)
            .then(res => {
              console.log(res, 'resss')
              if (res.Code == 200) {
                this.$message.success('已保存')
                record.edit = false
                this.editFlag = true
                this.handRouteChange()
              }
            })
            .catch(err => {
              console.log(err, 'err')
            })
          console.log(params, 'pppp')
          break
        case 'cancel':
          if (record.id == 0) {
            this.backendRouterData.splice(0, 1)
          } else {
            record.path = this.cloneRecord.path
            record.method = this.cloneRecord.method
          }
          record.edit = false
          this.editFlag = true
          this.cloneRecord = []
          this.console.log(this.cloneRecord, 'this.cloneRecord')
          break
      }
    },
    // handle
    handleOk(e) {
      if (this.handleRoleModalTitle == '编辑角色') {
        if (
          !this.editRoleForm.roleName ||
          !this.editRoleForm.roleDesc ||
          !this.editRoleForm.roleName.trim() ||
          !this.editRoleForm.roleDesc.trim()
        ) {
          this.$message.error('角色名称和描述必填')
        } else {
          console.log({
            roleName: this.editRoleForm.roleName,
            roleDesc: this.editRoleForm.roleDesc,
          })
          UpdateRoleInfo({
            roleName: this.editRoleForm.roleName,
            roleDesc: this.editRoleForm.roleDesc,
          }).then(res => {
            this.$message.success('修改成功')
            this.handleRoleVis = false
            this.editFlag = true
            this.$refs.roleTable.refresh(true)
          })
        }
      } else {
        this.handleRoleVis = false
        this.editFlag = true
      }
    },
    queryUserList() {
      this.rolePage.current = 1
      this.rolePage.pageSize = 10
      this.queryParams.searchText = this.searchUser
      this.handleRoleChange()
      // searchText
    },
    resetQueryUser() {
      this.rolePage.current = 1
      this.rolePage.pageSize = 10
      this.searchUser = ''
      this.queryParams.searchText = this.searchUser
    },
    cancelModal() {
      this.rolePage = {
        total: 0,
        current: 1,
        pageSize: 10,
      }
      this.editFlag = true
    },
    // 递归获取路由path
    loopGetPath(route) {
      let childPath = []
      childPath.push(route.path)
      if (route.children && route.children.length) {
        for (let i = 0; i < route.children.length; i++) {
          childPath = [...childPath, ...this.loopGetPath(route.children[i])]
        }
      }
      return childPath
    },

    // 递归改变路由状态
    loopToogleChildAuth(route, auth) {
      // route.auth = auth
      this.$set(route, 'auth', auth)
      if (route.children && route.children.length) {
        for (let i = 0; i < route.children.length; i++) {
          this.loopToogleChildAuth(route.children[i], auth)
        }
      }
    },
    // 子级别启用父级也要启用
    changeParentStatus(parentPath) {
      for (let i = 0; i < this.userRouters.length; i++) {
        if (this.userRouters[i].path === parentPath && this.userRouters[i].auth === false) {
          this.$set(this.userRouters[i], 'auth', true)
          Auth({
            subType: 'role',
            subject: this.currentRule.roleName,
            paths: [this.userRouters[i].path],
          }).then(() => {})
        }
      }
    },
    // 弹窗切换路由启用信息
    topggleAuth(flag, routeInfos) {
      const childPath = this.loopGetPath(routeInfos)
      if (flag) {
        Auth({
          subType: 'role',
          subject: this.currentRule.roleName,
          paths: childPath,
        })
          .then(res => {
            this.loopToogleChildAuth(routeInfos, flag)

            if (routeInfos.parent) {
              this.changeParentStatus(routeInfos.parent)
            }
          })
          .catch(() => {})
      } else {
        unAuth({
          subType: 'role',
          subject: this.currentRule.roleName,
          paths: childPath,
        })
          .then(res => {
            this.loopToogleChildAuth(routeInfos, flag)
          })
          .catch(() => {})
        //
        this.loopToogleChildAuth(routeInfos, flag)
      }
    },
    //  路由策略
    handleRouter(role) {
      this.currentRule = role
      const params = {
        subType: 'role',
        subject: this.currentRule.roleName,
      }

      // 获取当前user 路由信息
      GetAuth(params)
        .then(res => {
          this.visible = true
          this.userRouters = res.routers
          this.loopDelKey(this.userRouters)
          // console.log(this.loopDelKey(res.routers), 11111)
        })
        .catch(() => {})
    },
    loopDelKey(routes) {
      return routes.map(item => {
        if (item.children.length > 0) {
          this.loopDelKey(item.children)
        } else {
          delete item.children
        }
        return item
      })
    },
    handlePolicy(role) {
      this.policyConfigVisible = true
      this.policyForm.email = role.roleName
      this.policyForm.name = role.roleName
    },
    handleDelete(role) {
      var that = this
      this.$confirm({
        title: '确认删除？',
        content: `确认删除 ${role.roleName} 及其关联配置吗？`,
        onOk() {
          deleteRole({ roleName: role.roleName })
            .then(res => {
              if (res !== undefined && res.Code === 200) {
                notification.success({
                  message: '删除成功',
                  description: '角色删除成功',
                })
                that.$refs.roleTable.refresh(true)
              }
            })
            .catch(() => {})
        },
        onCancel() {},
      })
    },
    onPolicyClose() {
      this.policyConfigVisible = false
      this.$refs.roleTable.refresh(true)
    },
    onRoleClose() {
      this.roleConfigVisible = false
    },
    resetRoleForm() {
      this.$refs.roleForms.resetFields()
    },
    getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
      return {
        getCheckboxProps: item => ({ props: { disabled: disabled || item.disabled } }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ title }) => title)
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys)
          itemSelectAll(diffKeys, selected)
        },
        onSelect({ title }, selected) {
          itemSelect(title, selected)
        },
        selectedRowKeys: selectedKeys,
      }
    },
    onCreateRoleSubmit() {
      const requestData = Object.assign({}, this.roleForm)
      console.log(this.$refs.roleForms, ' this.$refs.roleForms')
      this.$refs.roleForms
        .validate()
        .then(res => {
          console.log(res, 'res')
          createRole(requestData).then(res => {
            this.roleConfigVisible = false
            if (res === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (res.Code === 200) {
              this.roleConfigVisible = false
              this.$refs.roleTable.refresh(true)
              notification.success({
                message: '创建成功',
                description: '角色创建成功',
              })
            } else {
              notification.error({
                message: '创建失败',
                description: res.data.msg,
              })
            }
          })
        })
        .catch(err => {
          console.log(err, 'err')
        })
    },
  },
}
</script>
<style lang="less" scoped>
/deep/.ant-select-multiple.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: #fff;
}
</style>
