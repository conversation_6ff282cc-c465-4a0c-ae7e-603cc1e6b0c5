<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-03 17:36:08
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-06 10:20:43
 * @FilePath: \cloud_web\src\views\permission\PolicyBinding.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <PolicyBindingComp />
    </SecondaryVerification>
  </page-header-wrapper>
</template>
<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import PolicyBindingComp from './permissionComp/policyBindingComp.vue'

export default {
  name: 'PolicyBinding',
  components: {
    PolicyBindingComp,
    SecondaryVerification,
  },
  data() {},
  created() {},
  methods: {},
}
</script>
