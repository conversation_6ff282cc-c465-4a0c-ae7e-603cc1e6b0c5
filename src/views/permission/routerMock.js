/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-06-16 10:58:45
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-09-25 11:34:16
 * @FilePath: \cloud_web\src\views\permission\routerMock.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const userAuthArr = {
  routers: [
    {
      path: '/dashboard',
      name: '仪表盘',
      redirect: '/dashboard/workplace',
      level: 1,
      icon: 'bxAnaalyse',
      component: 'RouteView',
      hidden: false,
      children: [
        {
          path: '/dashboard/workplace',
          name: '工作台',
          redirect: '',
          level: 2,
          icon: '',
          component: 'Workplace',
          hidden: false,
          children: [],
          children: [
            {
              path: '/dashboard/center2',
              name: '个人中心2',
              redirect: '',
              level: 3,
              icon: '',
              component: 'Center',
              hidden: true,
              children: [],
            },
          ],
        },
        {
          path: '/dashboard/center',
          name: '个人中心',
          redirect: '',
          level: 2,
          icon: '',
          component: 'Center',
          hidden: true,
          children: [],
        },
      ],
    },
  ],
}
// module.exports = [userAuthArr]
