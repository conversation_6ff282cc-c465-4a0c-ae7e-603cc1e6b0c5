<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2025-01-03 17:36:08
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-06 10:24:33
 * @FilePath: \cloud_web\src\views\permission\UserManager.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <userManagerComp />
    </SecondaryVerification>
  </page-header-wrapper>
</template>

<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import userManagerComp from './permissionComp/userManagerComp.vue'
export default {
  components: {
    SecondaryVerification,
    userManagerComp,
  },
  data() {},
  created() {},
  methods: {},
}
</script>
