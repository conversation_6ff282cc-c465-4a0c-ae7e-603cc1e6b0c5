<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="流程类型">
                <x-select
                  v-model:value="queryParam.orderType"
                  :options="orderTypeList"
                  placeholder="请选择流程"
                ></x-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" v-if="!emailDisabled">
              <a-form-item label="委托人邮箱">
                <a-select
                  placeholder="请选择被委托人邮箱"
                  v-model:value="queryParam.email"
                  :showSearch="true"
                  :allowClear="true"
                  @search="searchUserEmailMethod"
                >
                  <a-select-option v-for="email in userEmailList" :key="email" :value="email">
                    {{ email }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
                <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <tx-button type="primary" icon="plus" @click="handleAdd">新建委托</tx-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :rowKey="record => record.id"
        :pagination="pagination"
        :columns="columns"
        :data="loadData"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'action'">
            <a @click="handleUpdate(record)">更新</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
              <template #icon>
                <a-icon type="question-circle-o" style="color: red" />
              </template>
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </s-table>

      <a-modal
        title="新建委托"
        :visible="visible"
        :confirm-loading="confirmLoading"
        @ok="handleOk"
        @cancel="handleCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="委托人邮箱" name="email">
            <a-select
              placeholder="请选择委托人邮箱"
              v-model:value="form.email"
              :disabled="emailDisabled"
              :showSearch="true"
              :allowClear="true"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="email in userEmailList" :key="email" :value="email">
                {{ email }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="类型" name="orderType">
            <x-select v-model:value="form.orderType" :options="orderTypeList" placeholder="请选择流程"></x-select>
          </a-form-model-item>
          <a-form-model-item label="被委托人邮箱" name="agentEmail">
            <a-select
              placeholder="请选择被委托人邮箱"
              v-model:value="form.agentEmail"
              :showSearch="true"
              :allowClear="true"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="email in userEmailList" :key="email" :value="email">
                {{ email }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
        title="委托更新"
        :visible="updateVisible"
        :confirm-loading="confirmLoading"
        @ok="handleUpdateOk"
        @cancel="handleUpdateCancel"
      >
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="委托人邮箱" name="email">
            <a-input v-model:value="form.email" disabled />
          </a-form-model-item>
          <a-form-model-item label="类型" name="orderType">
            <x-select v-model:value="form.orderType" :options="orderTypeList" placeholder="请选择流程"></x-select>
          </a-form-model-item>
          <a-form-model-item label="被委托人邮箱" name="agentEmail">
            <a-select
              placeholder="请选择被委托人邮箱"
              v-model:value="form.agentEmail"
              :showSearch="true"
              :allowClear="true"
              @search="searchUserEmailMethod"
            >
              <a-select-option v-for="email in userEmailList" :key="email" :value="email">
                {{ email }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import {
  getAgentList,
  getProcessOrderTypeList,
  createAgent,
  getAgentInfo,
  updateAgent,
  deleteAgent,
} from '@/api/workflow/process'
import store from '@/store'
import { getUserList } from '@/api/permission/user'

const columns = [
  {
    title: '委托人邮箱',
    dataIndex: 'email',
    sorter: true,
  },
  {
    title: '类型',
    dataIndex: 'orderType',
    sorter: true,
  },
  {
    title: '被委托人',
    dataIndex: 'agentName',
    sorter: true,
  },
  {
    title: '被委托人邮箱',
    dataIndex: 'agentEmail',
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'WorkflowAgent',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      orderTypeList: [],
      userEmailList: [],
      queryParam: {},
      advanced: false,
      form: {
        email: store.getters.email,
        orderType: undefined,
        agentEmail: undefined,
      },
      emailDisabled: true,
      visible: false,
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getAgentList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        email: [{ required: true, message: '请选择委托人邮箱', trigger: 'change' }],
        orderType: [{ required: true, message: '请选择流程类型', trigger: 'change' }],
        agentEmail: [{ required: true, message: '请选择被委托人邮箱', trigger: 'change' }],
      },
    }
  },
  mounted() {
    this.getProcessOrderTypeListInfo()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    getProcessOrderTypeListInfo() {
      var allOrderType = {}
      allOrderType.value = '全部流程'
      allOrderType.label = '全部流程'
      this.orderTypeList.push(allOrderType)
      getProcessOrderTypeList().then(res => {
        for (var i = 0, len = res.Data.orderType.length; i < len; i++) {
          var orderType = {}
          orderType.value = res.Data.orderType[i]
          orderType.label = res.Data.orderType[i]
          this.orderTypeList.push(orderType)
        }
      })

      if (store.getters.email === '<EMAIL>') {
        this.emailDisabled = false
      }
    },
    handleAdd() {
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createAgent(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      getAgentInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateAgent(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteAgent(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    onJsonChange(value) {
      this.onJsonSave(value)
    },
    onJsonSave(value) {
      this.form.handlerContent = value
      this.hasJsonFlag = true
    },
    onError(value) {
      this.hasJsonFlag = false
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        var arry = response.Data.data
        for (var i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
  },
}
</script>
