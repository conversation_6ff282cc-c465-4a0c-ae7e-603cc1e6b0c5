/*
 * @Author: huid<PERSON>_yang <EMAIL>
 * @Date: 2022-10-13 10:56:42
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-09-05 12:55:34
 * @FilePath: \cloud_web\src\views\workflow\workFlowDatas.js
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
 */

// TODO 改为 import 或 url
function require(path) {
  return path.replace('@/assets', '/vendor-cdn/@img')
}

const serverList = [
  {
    type: '服务器',
    id: 'servers',
    lists: [
      {
        name: '服务器创建(混合云)',
        svg: require('@/assets/svgs/ecs.svg'),
        id: 'serverCreate',
        route: '/workflow/server-create',
        tooltipIcon: 'question-circle',
        tooltip: 'UCloud混合云机房专属,已支持全自动化处理，推荐使用',
      },
      {
        name: '服务器新增',
        svg: require('@/assets/svgs/serverAdd.svg'),
        id: 'serverAdd',
        route: '/workflow/server-add',
      },
      {
        name: '数据库创建(混合云)',
        svg: require('@/assets/svgs/db_ddl.svg'),
        id: 'serverAdd',
        route: '/workflow/db-server-create',
        tooltip: 'UCloud混合云机房专属,已支持全自动化处理，推荐使用',
      },
      {
        name: '数据库新增(阿里云)',
        svg: require('@/assets/svgs/db_dml.svg'),
        id: 'serverDbAdd',
        route: '/workflow/db-server-add',
      },
      {
        name: '运维资源申请',
        svg: require('@/assets/svgs/opsSource.svg'),
        id: 'opsSource',
        route: '/workflow/ops-source',
      },
      {
        name: '服务器授权',
        svg: require('@/assets/svgs/serverPermission.svg'),
        id: 'serverPermissionOperation',
        route: '/workflow/server-permission-operation',
      },
      {
        name: '服务器配置修改',
        svg: require('@/assets/svgs/serverConfigModify.svg'),
        id: 'ServerModifyConfig',
        route: '/workflow/server-modify-config',
      },
      {
        name: '服务器标签变更',
        svg: require('@/assets/svgs/serveTagModify.svg'),
        id: 'serverTagModify',
        route: '/server/asset-list',
      },
      {
        name: '服务器注销',
        svg: require('@/assets/svgs/serverDel.svg'),
        id: 'ServerDelete',
        route: '/workflow/server-del',
      },
      {
        name: '虚拟机配置变更',
        svg: require('@/assets/svgs/serveTagModify.svg'),
        id: 'VmAssetModifyConfig',
        route: '/workflow/vm-asset-modify-config',
        tooltip: 'UCloud混合云机房专属,已支持全自动化处理，推荐使用',
      },
      {
        name: '虚拟机注销(混合云)',
        svg: require('@/assets/svgs/serverDel.svg'),
        id: 'VmAssetDelete',
        route: '/workflow/vm-asset-del',
      },
    ],
  },
  {
    type: '云桌面',
    id: 'desktop',
    lists: [
      {
        name: '远程办公',
        svg: require('@/assets/svgs/remoteDesktop.svg'),
        id: 'remoteDesktop',
        route: '/workflow/remote-desktop',
        tooltipIcon: 'question-circle',
        tooltip: '远程办公云桌面',
      },
      // {
      //   name: 'URL白名单',
      //   svg: require('@/assets/svgs/remoteDesktop.svg'),
      //   id: 'remoteDesktop',
      //   route: '/workflow/remote-desktop-url',
      //   tooltipIcon: 'question-circle',
      //   tooltip: '远程办公云桌面URL白名单'
      // },
      // {
      //   name: '远程工作站',
      //   svg: require('@/assets/svgs/cloudDesktop.svg'),
      //   id: 'cloudDesktop',
      //   route: '/workflow/cloud-desktop'
      // },
      {
        name: 'DLP云桌面',
        svg: require('@/assets/svgs/dlpDesktop.svg'),
        id: 'dlpDesktop',
        route: '/workflow/dlp-desktop',
      },
      // {
      //   name: 'DLP云桌面场景',
      //   svg: require('@/assets/svgs/dlpDesktop.svg'),
      //   id: 'dlpDesktopPolicy',
      //   route: '/workflow/dlp-desktop-policy',
      //   tooltipIcon: 'question-circle',
      //   tooltip: '新增DLP云桌面场景策略'
      // },
      // {
      //   name: '服务器云桌面',
      //   svg: require('@/assets/svgs/serverDesktop.svg'),
      //   id: 'serverDesktop',
      //   route: '/workflow/server-desktop'
      // },
      {
        name: '云桌面注销',
        svg: require('@/assets/svgs/destroyDesktop.svg'),
        id: 'desktopDestroy',
        route: '/workflow/desktop-destroy',
      },
    ],
  },
  {
    type: '数据库',
    id: 'database',
    lists: [
      {
        name: '数据查询申请',
        svg: require('@/assets/svgs/db_approval.svg'),
        id: 'dbQuery-approval',
        route: '/workflow/db-approval',
      },
      {
        name: '数据导出申请',
        svg: require('@/assets/svgs/db_source.svg'),
        id: 'db-approval',
        route: '/workflow/db-source',
      },
      {
        name: 'DCL申请',
        svg: require('@/assets/svgs/db_dcl.svg'),
        id: 'dcl-approval',
        route: '/workflow/dcl',
        tooltipIcon: 'question-circle',
        tooltip: '设置数据库用户或角色权限',
      },
      {
        name: 'DDL申请',
        svg: require('@/assets/svgs/db_ddl.svg'),
        id: 'ddl-approval',
        route: '/workflow/ddl',
        tooltipIcon: 'question-circle',
        tooltip: '创建、删除、修改数据库及表、视图、索引等',
      },
      {
        name: 'DML申请',
        svg: require('@/assets/svgs/db_dml.svg'),
        id: 'dml-approval',
        route: '/workflow/dml',
        tooltipIcon: 'question-circle',
        tooltip: '对数据的增删改',
      },
      {
        name: 'CS同步库扫描',
        svg: require('@/assets/svgs/cs-scan.svg'),
        id: 'CSSyncScan',
        route: '/workflow/cs-scan',
      },
      {
        name: 'ClickHouse查询申请',
        svg: require('@/assets/svgs/ck_auth.svg'),
        id: 'ck-auth',
        route: '/workflow/ck-auth',
      },
      {
        name: 'ClickHouse资源导出',
        svg: require('@/assets/svgs/ck_source.svg'),
        id: 'ck-approval',
        route: '/workflow/ck-source',
      },
      {
        name: 'ClickHouse归档数据恢复',
        svg: require('@/assets/svgs/ck_restore.svg'),
        id: 'ck-approval',
        route: '/workflow/ck-restore',
      },
      {
        name: '数据库报警配置',
        svg: require('@/assets/svgs/db_alarm.svg'),
        id: 'ck-approval',
        route: '/workflow/dbalter',
      },
      {
        name: 'Consul密钥配置',
        svg: require('@/assets/svgs/consul.svg'),
        id: 'consul-approval',
        route: '/workflow/consul',
      },
      {
        name: 'Consul数据库配置',
        svg: require('@/assets/svgs/consul-db.svg'),
        id: 'consul-database',
        route: '/workflow/consul-database',
      },
      {
        name: '上传EXCEL/CSV数据到线上表',
        svg: require('@/assets/svgs/db_alarm.svg'),
        id: 'consul-database',
        route: '/workflow/upload-data-db',
      },
      {
        name: 'DDL申请(QA)',
        svg: require('@/assets/svgs/db_ddl.svg'),
        id: 'ddl-approval-QA',
        route: '/workflow/ddl-qa',
        tooltipIcon: 'question-circle',
        tooltip: '带QA审批节点的DDL工单',
      },
      {
        name: 'DML申请(QA)',
        svg: require('@/assets/svgs/db_dml.svg'),
        id: 'dml-approval-QA',
        route: '/workflow/dml-qa',
        tooltipIcon: 'question-circle',
        tooltip: '带QA审批节点的DML工单',
      },
    ],
  },
  {
    type: '域名',
    id: 'domain',
    lists: [
      {
        name: '域名解析新增',
        svg: require('@/assets/svgs/dnsAdd.svg'),
        id: 'dnsAdd',
        route: '/workflow/dns-add',
      },
      {
        name: '域名解析更新',
        svg: require('@/assets/svgs/dnsModify.svg'),
        id: 'dnsModify',
        route: '/domain/dns-list',
      },
      {
        name: '域名解析注销',
        svg: require('@/assets/svgs/dnsDel.svg'),
        id: 'dnsDel',
        route: '/domain/dns-list',
      },
      {
        name: '域名购买',
        svg: require('@/assets/svgs/domainPurchase.svg'),
        id: 'domainPurchase',
        route: '/workflow/domain-purchase',
      },
      {
        name: '网关路由配置',
        svg: require('@/assets/svgs/gateway.svg'),
        id: 'gatewayConifg',
        route: '/workflow/gateway-route-config',
      },
      {
        name: '网关StreamRoute配置',
        svg: require('@/assets/svgs/streamRoute.svg'),
        id: 'streamRoute',
        route: '/workflow/steam_route',
      },
      {
        name: '域名证书下载',
        svg: require('@/assets/svgs/ssl.svg'),
        id: 'domainSslDownload',
        route: '/workflow/domain-ssl-download',
      },
    ],
  },
  {
    type: '存储',
    id: 'storage',
    lists: [
      {
        name: '新增共享云存储',
        svg: require('@/assets/svgs/shareStorage.svg'),
        id: 'shareStorageCreate',
        route: '/workflow/share-storage-create',
      },
      {
        name: '加入已有共享云存储',
        svg: require('@/assets/svgs/shareStorageAdd.svg'),
        id: 'shareStorageAdd',
        route: '/workflow/share-storage-add',
      },
    ],
  },
  {
    type: '安全',
    id: 'safety',
    lists: [
      {
        name: '基线检查-资源类',
        svg: require('@/assets/svgs/baseline.svg'),
        id: 'baselineCheck',
        route: '/workflow/baseline-check-resources',
      },
      {
        name: 'SecretsManager',
        svg: require('@/assets/svgs/manager.svg'),
        id: 'secretsManager',
        route: '/workflow/secrets-manager',
      },
      {
        name: '阿里云Redis白名单',
        svg: require('@/assets/svgs/safe-group.svg'),
        id: 'redisSafeGroup',
        route: '/workflow/redis-safegroup',
      },
      {
        name: '混合云Redis白名单',
        svg: require('@/assets/svgs/safe-group.svg'),
        id: 'hybridRredisSafeGroup',
        route: '/workflow/hybird-redis-safegroup',
      },
      { name: '安全组解绑', svg: require('@/assets/svgs/sg-unbind.svg'), id: 'SgUnbind', route: '/workflow/sg-unbind' },
      {
        name: '安全组放开',
        svg: require('@/assets/svgs/securityGroupRelease.svg'),
        id: 'securityGroupRelease',
        route: '/workflow/security-group-release',
      },
      {
        name: '网关IP封禁',
        svg: require('@/assets/svgs/icon_IP_ban.svg'),
        id: 'GatewayIpBan',
        route: '/workflow/ip-ban',
      },
      // {
      //   name: '海外项目通信加速申请',
      //   svg: require('@/assets/svgs/overseasAcceleration.svg'),
      //   id: 'OverseasAcceleration',
      //   route: '/workflow/overseas-acceleration',
      // },
    ],
  },
  {
    type: '权限',
    id: 'auth',
    lists: [
      {
        name: '云平台权限申请',
        svg: require('@/assets/routeApply.svg'),
        id: 'ops',
        route: '/workflow/user-route-approve',
      },
      {
        name: '公有云个人账号权限(keycloak)',
        svg: require('@/assets/svgs/keycloak.svg'),
        id: 'RamPersonPermissionsKeycloak',
        route: '/workflow/cloud-access-keycloak',
      },
      {
        name: '公有云业务API权限(vault)',
        svg: require('@/assets/svgs/vault.svg'),
        id: 'RamBusinessAccessVault',
        route: '/workflow/cloud-access-vault',
      },
      {
        name: 'FTP账号申请',
        svg: require('@/assets/svgs/security_ftp.svg'),
        id: 'security-ftp-account',
        route: '/workflow/security-ftp-account',
      },
      {
        name: '启信宝OPS权限申请',
        svg: require('@/assets/svgs/qxb_ops.svg'),
        id: 'qxb-ops-auth',
        route: '/workflow/qxb-ops-auth',
      },
      {
        name: 'Grafana权限',
        svg: require('@/assets/svgs/grafana.svg'),
        id: 'grafanaAuth',
        route: '/workflow/grafana-auth',
      },
      {
        name: '公有云个人账号权限',
        svg: require('@/assets/svgs/personAccountPublic.svg'),
        id: 'RamPersonPermissions',
        route: '/safety/supplier-user',
      },
      {
        name: '公有云业务API权限',
        svg: require('@/assets/svgs/businessAccountPublic.svg'),
        id: 'RamBusinessPermissions',
        route: '/workflow/business-account-permission',
      },
      {
        name: '大模型密钥申请',
        svg: require('@/assets/svgs/ai.svg'),
        id: 'ApiKeyAuth',
        route: '/workflow/api-key',
      },
    ],
  },
  {
    type: '发布',
    id: 'apply',
    lists: [
      {
        name: 'Jenkins权限',
        svg: require('@/assets/svgs/jenkins.svg'),
        id: 'jenkinsAuth',
        route: '/workflow/jenkins-auth',
      },
      {
        name: 'Jenkins新建Job项目',
        svg: require('@/assets/svgs/jenkinsJob.svg'),
        id: 'jenkinsJob',
        route: '/workflow/jenkins-job',
      },
      {
        name: 'Jenkins删除Job项目',
        svg: require('@/assets/svgs/jenkinsJob.svg'),
        id: 'jenkinsJobDel',
        route: '/workflow/jenkins-job-del',
      },
      {
        name: 'GPU服务发布',
        svg: require('@/assets/svgs/gpu.svg'),
        id: 'gpuServiceApply',
        route: '/workflow/gpu-service-apply',
      },
    ],
  },
  {
    type: '算法',
    id: 'ai',
    lists: [
      {
        name: '数据集下载',
        svg: require('@/assets/svgs/dataSetDownload.svg'),
        id: 'dataSetDownload',
        route: '/ai/data-set',
      },
      {
        name: 'Slurm权限',
        svg: require('@/assets/svgs/slurmAuth.svg'),
        id: 'slurmAuth',
        route: '/workflow/slurm-auth',
      },
      {
        name: 'Slurm任务创建',
        svg: require('@/assets/svgs/slurmJobCreate.svg'),
        id: 'slurmJobCreate',
        route: '/workflow/slurm-job-create',
      },
      // {
      //   name: 'AI开发IDE申请',
      //   svg: require('@/assets/svgs/cloudIde.svg'),
      //   id: 'aiIdeApply',
      //   route: '/workflow/ai-ide-apply',
      // },
      {
        name: 'IDE申请GPU卡配额',
        svg: require('@/assets/svgs/cloudIde.svg'),
        id: 'AiIdeGpuQuota',
        route: '/workflow/ai-ide-gpu-quota',
      },
    ],
  },
  {
    type: '数据',
    id: 'db',
    lists: [
      {
        name: 'Superset权限',
        svg: require('@/assets/svgs/superset.svg'),
        id: 'superset',
        route: '/workflow/superset',
      },
      {
        name: '服务器数据下载',
        svg: require('@/assets/svgs/serverDataDownload.svg'),
        id: 'serverDataDownload',
        route: '/workflow/server-data-download',
      },
      {
        name: 'DF取数',
        svg: require('@/assets/svgs/serverDataDownloadDf.svg'),
        id: 'serverDataDownloadDf',
        route: '/workflow/server-data-download-df',
        tooltipIcon: 'question-circle',
        tooltip: '服务器数据下载（DF取数）',
      },
      {
        name: '数据操作申请',
        svg: require('@/assets/svgs/manipulation.svg'),
        id: 'dbManipulation',
        route: '/workflow/db-manipulation',
      },
    ],
  },
  {
    type: '开发与运维',
    id: 'devOps',
    lists: [
      {
        name: '代码成本检测申请',
        svg: require('@/assets/svgs/codeCost.svg'),
        id: 'codeCost',
        route: '/workflow/code-cost-detection',
      },
      {
        name: 'Coder账号申请',
        svg: require('@/assets/svgs/coderAccount.svg'),
        id: 'coderAccount',
        route: '/workflow/coder',
      },
      {
        name: '云IDE申请',
        svg: require('@/assets/svgs/cloudIde.svg'),
        id: 'cloudIde',
        route: '/workflow/cloud-ide',
      },
      {
        name: '运维人工协助',
        svg: require('@/assets/svgs/ops.svg'),
        id: 'ops',
        route: '/workflow/ops-help',
      },
      {
        name: '运维费用分摊比例',
        svg: require('@/assets/svgs/costSplitRatio.svg'),
        id: 'costSplitRatio',
        route: '/workflow/cost-split-ratio',
      },
    ],
  },
  {
    type: '大数据',
    id: 'big-database',
    lists: [
      {
        name: '大数据抽取申请',
        svg: require('@/assets/svgs/db_approval.svg'),
        id: 'bigdata-exctraction',
        route: '/workflow/bigdata-exctraction',
      },
      {
        name: 'IDS模块权限申请',
        svg: require('@/assets/svgs/ids-permission.svg'),
        id: 'ids-permission',
        route: '/workflow/ids-permission',
      },
      {
        name: '大数据共享主题库',
        svg: require('@/assets/svgs/shared-theme.svg'),
        id: 'shared-theme',
        route: '/workflow/shared-theme',
      },
      {
        name: '大数据网络互通',
        svg: require('@/assets/svgs/bigdata-net.svg'),
        id: 'bigdata-net',
        route: '/workflow/bigdata-net',
      },
      {
        name: '敏感数据解密Token',
        svg: require('@/assets/svgs/sd-decryption-token.svg'),
        id: 'sd-decryption-token',
        route: '/workflow/sd-decryption-token',
        tooltipIcon: 'question-circle',
        tooltip: '敏感数据解密Token权限申请',
      },
      {
        name: '敏感数据解密申请',
        svg: require('@/assets/svgs/sd-decryption.svg'),
        id: 'sd-decryption',
        route: '/workflow/sd-decryption',
        tooltipIcon: 'question-circle',
        tooltip: '敏感数据解密申请(大数据平台)',
      },
      {
        name: '敏感数据使用申请',
        svg: require('@/assets/svgs/sd-use.svg'),
        id: 'sd-use',
        route: '/workflow/sd-use',
        tooltipIcon: 'question-circle',
        tooltip: '敏感数据使用(运营平台)申请',
      },
      {
        name: '大数据建表修改表申请',
        svg: require('@/assets/svgs/sd-use.svg'),
        id: 'BigDataCreateTable',
        route: '/workflow/BigDataCreateTable',
        tooltipIcon: 'question-circle',
        tooltip: '大数据建表修改表申请',
      },
      {
        name: '大数据资产转交',
        svg: require('@/assets/svgs/bigdata-asset-change.svg'),
        id: 'bigdata-asset-change',
        route: '/workflow/bigdata-asset-change',
      },
      {
        name: '数据超限导出申请',
        svg: require('@/assets/svgs/bigdataDownload.svg'),
        id: 'bigdataDownload',
        route: '/workflow/bigdata-download',
      },
    ],
  },
  {
    type: '作业平台',
    id: 'cron-schedule',
    lists: [
      {
        name: '作业执行',
        svg: require('@/assets/svgs/cron_control.svg'),
        id: 'job-execute',
        route: '/workflow/job-execute',
      },
      {
        name: '服务器应用部署',
        svg: require('@/assets/svgs/server_app_deploy.svg'),
        id: 'server-app-deploy',
        route: '/workflow/server-app-deploy',
      },
    ],
  },
  // {
  //   type: '账单',
  //   id: 'cost',
  //   lists: [
  //     {
  //       name: '账单标签变更',
  //       svg: require('@/assets/svgs/costLabelChange.svg'),
  //       id: 'cost-label',
  //       route: '/workflow/cost-label',
  //     },
  //   ],
  // },
  // {
  //   type: '人工类',
  //   id: 'artificial',
  //   lists: [

  //   ]
  // }
]
const myMockData = {
  id: 1794,
  orderType: '服务器创建',
  node: 1,
  status: 1,
  content: {
    netAllow: true,
    needInit: true,
    hidsInit: true,
    filterMachines: [
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-120',
        cpuOversoldRate: '43.75%',
        memOversoldRate: '30.46%',
        freeMem: '445GB',
        diskRootUsage: '11.99%',
        perCpuAvgLoad: 0.0053,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
        allocated: true,
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-136',
        cpuOversoldRate: '78.13%',
        memOversoldRate: '36.16%',
        freeMem: '446GB',
        diskRootUsage: '26.60%',
        perCpuAvgLoad: 0.0876,
        kvmCount: 10,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-130',
        cpuOversoldRate: '82.81%',
        memOversoldRate: '38.47%',
        freeMem: '92GB',
        diskRootUsage: '24.22%',
        perCpuAvgLoad: 0.0534,
        kvmCount: 15,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-129',
        cpuOversoldRate: '87.50%',
        memOversoldRate: '43.08%',
        freeMem: '171GB',
        diskRootUsage: '21.45%',
        perCpuAvgLoad: 0.0305,
        kvmCount: 13,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-116',
        cpuOversoldRate: '59.38%',
        memOversoldRate: '44.14%',
        freeMem: '177GB',
        diskRootUsage: '6.52%',
        perCpuAvgLoad: 0.0317,
        kvmCount: 15,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-107',
        cpuOversoldRate: '50.00%',
        memOversoldRate: '57.85%',
        freeMem: '66GB',
        diskRootUsage: '30.98%',
        perCpuAvgLoad: 0.0116,
        kvmCount: 4,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-137',
        cpuOversoldRate: '96.88%',
        memOversoldRate: '39.60%',
        freeMem: '512GB',
        diskRootUsage: '21.35%',
        perCpuAvgLoad: 0.1909,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-119',
        cpuOversoldRate: '100.00%',
        memOversoldRate: '55.89%',
        freeMem: '119GB',
        diskRootUsage: '25.33%',
        perCpuAvgLoad: 0.0464,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-131',
        cpuOversoldRate: '76.56%',
        memOversoldRate: '51.03%',
        freeMem: '118GB',
        diskRootUsage: '35.77%',
        perCpuAvgLoad: 0.0712,
        kvmCount: 13,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-118',
        cpuOversoldRate: '87.50%',
        memOversoldRate: '57.35%',
        freeMem: '125GB',
        diskRootUsage: '25.25%',
        perCpuAvgLoad: 0.0385,
        kvmCount: 7,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-125',
        cpuOversoldRate: '118.75%',
        memOversoldRate: '56.92%',
        freeMem: '88GB',
        diskRootUsage: '46.49%',
        perCpuAvgLoad: 0.018,
        kvmCount: 17,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-117',
        cpuOversoldRate: '89.06%',
        memOversoldRate: '58.54%',
        freeMem: '135GB',
        diskRootUsage: '26.81%',
        perCpuAvgLoad: 0.0554,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-135',
        cpuOversoldRate: '87.50%',
        memOversoldRate: '57.23%',
        freeMem: '219GB',
        diskRootUsage: '27.15%',
        perCpuAvgLoad: 0.037,
        kvmCount: 16,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-133',
        cpuOversoldRate: '84.38%',
        memOversoldRate: '55.17%',
        freeMem: '160GB',
        diskRootUsage: '34.12%',
        perCpuAvgLoad: 0.0829,
        kvmCount: 12,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-101',
        cpuOversoldRate: '125.00%',
        memOversoldRate: '48.71%',
        freeMem: '122GB',
        diskRootUsage: '75.32%',
        perCpuAvgLoad: 0.2117,
        kvmCount: 21,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-128',
        cpuOversoldRate: '81.25%',
        memOversoldRate: '73.45%',
        freeMem: '36GB',
        diskRootUsage: '35.49%',
        perCpuAvgLoad: 0.0194,
        kvmCount: 12,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-110',
        cpuOversoldRate: '60.94%',
        memOversoldRate: '79.13%',
        freeMem: '40GB',
        diskRootUsage: '28.01%',
        perCpuAvgLoad: 0.0108,
        kvmCount: 4,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-111',
        cpuOversoldRate: '59.38%',
        memOversoldRate: '76.74%',
        freeMem: '84GB',
        diskRootUsage: '32.32%',
        perCpuAvgLoad: 0.057,
        kvmCount: 6,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-91',
        cpuOversoldRate: '75.00%',
        memOversoldRate: '71.71%',
        freeMem: '63GB',
        diskRootUsage: '36.04%',
        perCpuAvgLoad: 0.1207,
        kvmCount: 12,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-108',
        cpuOversoldRate: '68.75%',
        memOversoldRate: '83.10%',
        freeMem: '111GB',
        diskRootUsage: '27.32%',
        perCpuAvgLoad: 0.0439,
        kvmCount: 5,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-115',
        cpuOversoldRate: '114.84%',
        memOversoldRate: '69.78%',
        freeMem: '141GB',
        diskRootUsage: '6.43%',
        perCpuAvgLoad: 0.09,
        kvmCount: 23,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-105',
        cpuOversoldRate: '150.00%',
        memOversoldRate: '63.82%',
        freeMem: '113GB',
        diskRootUsage: '74.35%',
        perCpuAvgLoad: 0.2186,
        kvmCount: 14,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-109',
        cpuOversoldRate: '73.44%',
        memOversoldRate: '85.88%',
        freeMem: '96GB',
        diskRootUsage: '27.61%',
        perCpuAvgLoad: 0.0361,
        kvmCount: 7,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-100',
        cpuOversoldRate: '142.50%',
        memOversoldRate: '58.65%',
        freeMem: '179GB',
        diskRootUsage: '69.53%',
        perCpuAvgLoad: 0.2556,
        kvmCount: 18,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-106',
        cpuOversoldRate: '56.25%',
        memOversoldRate: '84.69%',
        freeMem: '98GB',
        diskRootUsage: '10.70%',
        perCpuAvgLoad: 0.0771,
        kvmCount: 3,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-92',
        cpuOversoldRate: '117.50%',
        memOversoldRate: '80.89%',
        freeMem: '59GB',
        diskRootUsage: '43.37%',
        perCpuAvgLoad: 0.1436,
        kvmCount: 14,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-99',
        cpuOversoldRate: '140.00%',
        memOversoldRate: '72.76%',
        freeMem: '122GB',
        diskRootUsage: '58.35%',
        perCpuAvgLoad: 0.2329,
        kvmCount: 15,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-113',
        cpuOversoldRate: '139.06%',
        memOversoldRate: '81.71%',
        freeMem: '132GB',
        diskRootUsage: '6.58%',
        perCpuAvgLoad: 0.1435,
        kvmCount: 23,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-104',
        cpuOversoldRate: '170.00%',
        memOversoldRate: '80.32%',
        freeMem: '96GB',
        diskRootUsage: '70.10%',
        perCpuAvgLoad: 0.2688,
        kvmCount: 17,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-96',
        cpuOversoldRate: '172.50%',
        memOversoldRate: '89.07%',
        freeMem: '112GB',
        diskRootUsage: '76.73%',
        perCpuAvgLoad: 0.1697,
        kvmCount: 22,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-103',
        cpuOversoldRate: '160.00%',
        memOversoldRate: '67.79%',
        freeMem: '100GB',
        diskRootUsage: '55.78%',
        perCpuAvgLoad: 0.5002,
        kvmCount: 18,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-95',
        cpuOversoldRate: '187.50%',
        memOversoldRate: '92.25%',
        freeMem: '58GB',
        diskRootUsage: '79.14%',
        perCpuAvgLoad: 0.3277,
        kvmCount: 20,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
    ],
    allMachines: [
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-120',
        cpuOversoldRate: '43.75%',
        memOversoldRate: '30.46%',
        freeMem: '445GB',
        diskRootUsage: '11.99%',
        perCpuAvgLoad: 0.0053,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
        allocated: true,
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-136',
        cpuOversoldRate: '78.13%',
        memOversoldRate: '36.16%',
        freeMem: '446GB',
        diskRootUsage: '26.60%',
        perCpuAvgLoad: 0.0876,
        kvmCount: 10,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-130',
        cpuOversoldRate: '82.81%',
        memOversoldRate: '38.47%',
        freeMem: '92GB',
        diskRootUsage: '24.22%',
        perCpuAvgLoad: 0.0534,
        kvmCount: 15,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-129',
        cpuOversoldRate: '87.50%',
        memOversoldRate: '43.08%',
        freeMem: '171GB',
        diskRootUsage: '21.45%',
        perCpuAvgLoad: 0.0305,
        kvmCount: 13,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-116',
        cpuOversoldRate: '59.38%',
        memOversoldRate: '44.14%',
        freeMem: '177GB',
        diskRootUsage: '6.52%',
        perCpuAvgLoad: 0.0317,
        kvmCount: 15,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-107',
        cpuOversoldRate: '50.00%',
        memOversoldRate: '57.85%',
        freeMem: '66GB',
        diskRootUsage: '30.98%',
        perCpuAvgLoad: 0.0116,
        kvmCount: 4,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-137',
        cpuOversoldRate: '96.88%',
        memOversoldRate: '39.60%',
        freeMem: '512GB',
        diskRootUsage: '21.35%',
        perCpuAvgLoad: 0.1909,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-119',
        cpuOversoldRate: '100.00%',
        memOversoldRate: '55.89%',
        freeMem: '119GB',
        diskRootUsage: '25.33%',
        perCpuAvgLoad: 0.0464,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-131',
        cpuOversoldRate: '76.56%',
        memOversoldRate: '51.03%',
        freeMem: '118GB',
        diskRootUsage: '35.77%',
        perCpuAvgLoad: 0.0712,
        kvmCount: 13,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-118',
        cpuOversoldRate: '87.50%',
        memOversoldRate: '57.35%',
        freeMem: '125GB',
        diskRootUsage: '25.25%',
        perCpuAvgLoad: 0.0385,
        kvmCount: 7,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-125',
        cpuOversoldRate: '118.75%',
        memOversoldRate: '56.92%',
        freeMem: '88GB',
        diskRootUsage: '46.49%',
        perCpuAvgLoad: 0.018,
        kvmCount: 17,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-117',
        cpuOversoldRate: '89.06%',
        memOversoldRate: '58.54%',
        freeMem: '135GB',
        diskRootUsage: '26.81%',
        perCpuAvgLoad: 0.0554,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-135',
        cpuOversoldRate: '87.50%',
        memOversoldRate: '57.23%',
        freeMem: '219GB',
        diskRootUsage: '27.15%',
        perCpuAvgLoad: 0.037,
        kvmCount: 16,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-133',
        cpuOversoldRate: '84.38%',
        memOversoldRate: '55.17%',
        freeMem: '160GB',
        diskRootUsage: '34.12%',
        perCpuAvgLoad: 0.0829,
        kvmCount: 12,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-101',
        cpuOversoldRate: '125.00%',
        memOversoldRate: '48.71%',
        freeMem: '122GB',
        diskRootUsage: '75.32%',
        perCpuAvgLoad: 0.2117,
        kvmCount: 21,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-128',
        cpuOversoldRate: '81.25%',
        memOversoldRate: '73.45%',
        freeMem: '36GB',
        diskRootUsage: '35.49%',
        perCpuAvgLoad: 0.0194,
        kvmCount: 12,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-110',
        cpuOversoldRate: '60.94%',
        memOversoldRate: '79.13%',
        freeMem: '40GB',
        diskRootUsage: '28.01%',
        perCpuAvgLoad: 0.0108,
        kvmCount: 4,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-111',
        cpuOversoldRate: '59.38%',
        memOversoldRate: '76.74%',
        freeMem: '84GB',
        diskRootUsage: '32.32%',
        perCpuAvgLoad: 0.057,
        kvmCount: 6,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-91',
        cpuOversoldRate: '75.00%',
        memOversoldRate: '71.71%',
        freeMem: '63GB',
        diskRootUsage: '36.04%',
        perCpuAvgLoad: 0.1207,
        kvmCount: 12,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-108',
        cpuOversoldRate: '68.75%',
        memOversoldRate: '83.10%',
        freeMem: '111GB',
        diskRootUsage: '27.32%',
        perCpuAvgLoad: 0.0439,
        kvmCount: 5,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-115',
        cpuOversoldRate: '114.84%',
        memOversoldRate: '69.78%',
        freeMem: '141GB',
        diskRootUsage: '6.43%',
        perCpuAvgLoad: 0.09,
        kvmCount: 23,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-105',
        cpuOversoldRate: '150.00%',
        memOversoldRate: '63.82%',
        freeMem: '113GB',
        diskRootUsage: '74.35%',
        perCpuAvgLoad: 0.2186,
        kvmCount: 14,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-109',
        cpuOversoldRate: '73.44%',
        memOversoldRate: '85.88%',
        freeMem: '96GB',
        diskRootUsage: '27.61%',
        perCpuAvgLoad: 0.0361,
        kvmCount: 7,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-100',
        cpuOversoldRate: '142.50%',
        memOversoldRate: '58.65%',
        freeMem: '179GB',
        diskRootUsage: '69.53%',
        perCpuAvgLoad: 0.2556,
        kvmCount: 18,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-106',
        cpuOversoldRate: '56.25%',
        memOversoldRate: '84.69%',
        freeMem: '98GB',
        diskRootUsage: '10.70%',
        perCpuAvgLoad: 0.0771,
        kvmCount: 3,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-92',
        cpuOversoldRate: '117.50%',
        memOversoldRate: '80.89%',
        freeMem: '59GB',
        diskRootUsage: '43.37%',
        perCpuAvgLoad: 0.1436,
        kvmCount: 14,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-99',
        cpuOversoldRate: '140.00%',
        memOversoldRate: '72.76%',
        freeMem: '122GB',
        diskRootUsage: '58.35%',
        perCpuAvgLoad: 0.2329,
        kvmCount: 15,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-113',
        cpuOversoldRate: '139.06%',
        memOversoldRate: '81.71%',
        freeMem: '132GB',
        diskRootUsage: '6.58%',
        perCpuAvgLoad: 0.1435,
        kvmCount: 23,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-104',
        cpuOversoldRate: '170.00%',
        memOversoldRate: '80.32%',
        freeMem: '96GB',
        diskRootUsage: '70.10%',
        perCpuAvgLoad: 0.2688,
        kvmCount: 17,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-96',
        cpuOversoldRate: '172.50%',
        memOversoldRate: '89.07%',
        freeMem: '112GB',
        diskRootUsage: '76.73%',
        perCpuAvgLoad: 0.1697,
        kvmCount: 22,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-103',
        cpuOversoldRate: '160.00%',
        memOversoldRate: '67.79%',
        freeMem: '100GB',
        diskRootUsage: '55.78%',
        perCpuAvgLoad: 0.5002,
        kvmCount: 18,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
      {
        ip: '*********',
        hostname: 'kvm-10-2-3-95',
        cpuOversoldRate: '187.50%',
        memOversoldRate: '92.25%',
        freeMem: '58GB',
        diskRootUsage: '79.14%',
        perCpuAvgLoad: 0.3277,
        kvmCount: 20,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
    ],
    priceInfo: {
      originalPrice: 190,
      discountPrice: 20.9,
      originalDiskPrice: 0,
      discountDiskPrice: 0,
      msg: '',
      allDiscount: '20.90',
      allOrigin: '190.00',
    },
    allocationList: [
      {
        ip: '**********',
        hostname: 'kvm-10-2-3-120',
        cpuOversoldRate: '43.75%',
        memOversoldRate: '30.46%',
        freeMem: '445GB',
        diskRootUsage: '11.99%',
        perCpuAvgLoad: 0.0053,
        kvmCount: 8,
        path: '',
        usageAfter: 0,
        start: '',
        end: '',
        remainingSize: 0,
        business: 'DG',
      },
    ],
    unHostAffine: [],
    hostAffineArr: [],
    idc: 'ucloud-shanghai2-hybrid',
    serverType: '[2,4]',
    num: 1,
    os: 'centos7',
    principal: [],
    project: 'AIM运维',
    projects: ['AIM运维'],
    resultIpList: [],
    env: 'online',
    application: ['ACG'],
    projectName: 'xxxxx',
    costUser: '<EMAIL>',
    organization: ['DG', 'DGC', '后端'],
    arrorg: 'DG/DGC/后端',
    costAttr: '运营成本',
    business: 'DG',
    productLineList: [
      {
        productLineNameList: ['CC', 'CC-C端-钻石1年VIP'],
        ratio: 1,
      },
    ],
    productLineListPercent: [
      {
        productLineNameList: ['CC', 'CC-C端-钻石1年VIP'],
        ratio: 100,
      },
    ],
    isDgDataAsset: false,
    isDgBusiness: true,
    dgDataAssetClassification: '',
    dgSpecificBusiness: '',
    needOtherDisks: false,
    preInstanceType: '',
    diskType: 'HDD',
    diskSize: 0,
    minDiskSize: 200,
    systemDiskSize: 40,
    systemDiskType: '',
    preImageIp: null,
    permissionType: '1',
    permissionDescription: '',
    needPreImageIp: false,
    isDb: false,
    isAutomation: false,
    hostAffine: false,
    reason: '测试',
    vlan: 33,
    needPublicIp: 0,
    approvalData: {
      projectName: 'xxxxx',
      keys: [],
      needInit: true,
      hidsInit: true,
      isDesktop: 0,
      vlan: 33,
      hostIpList: [],
      instanceType: '',
      defaultImageId: '',
      defaultSubnetId: [],
      defaultSecurityGroupList: [],
      defaultVpcSwitchList: [],
      defaultVpcId: '',
      defaultZoneId: '',
      zoneIdList: [],
      roleName: '',
    },
    productLine: 'CC-C端-钻石1年VIP:100',
    dgDataAsset: '否--',
  },
  founder: '杨慧东',
  founderEmail: '<EMAIL>',
  handler: '杨慧东',
  handlerEmail: '<EMAIL>',
  timeline: [
    '杨慧东(<EMAIL>) 创建工单 2025-03-19 19:28:26',
    '杨慧东(<EMAIL>) 待审核 2025-03-19 19:28:26',
  ],
  comment: '',
  tag: 1,
  affidavit: 1,
  createdAt: '2025-03-19 14:03:46',
  updatedAt: '2025-03-19 14:03:46',
}
export { serverList, myMockData }
