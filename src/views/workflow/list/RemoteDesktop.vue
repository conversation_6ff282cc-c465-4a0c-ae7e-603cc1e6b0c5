<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=576192633" style="text-decoration: none">
          说明文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 5" color="orange">自动化处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="name" label="云桌面名称">
              <a-input v-model:value="temp.content.name" show-count :maxlength="15" disabled></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="操作系统" name="os">
              <a-radio-group v-model:value="temp.content.os" button-style="solid">
                <a-radio-button value="windows">Windows</a-radio-button>
                <a-radio-button value="ubuntu">Ubuntu</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="配置规格" name="serverType">
              <a-cascader
                v-model:value="temp.content.serverType"
                :options="serverTypeOptions"
                placeholder="请选择CPU/内存"
                change-on-select
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="配置费用" name="serverType">
              <p v-if="temp.content.serverType[0] === '4'">
                <span style="font-size: 16px; font-weight: bolder; color: #ff4500">¥ {{ temp.content.cost }}</span>
                /每小时（
                <span style="text-decoration: line-through">官网价 ¥ {{ temp.content.price }}/每小时</span>
                ）。
                <span style="font-weight: bolder; color: #ff4500">关机不收费，磁盘费用约50¥/月</span>
                ，可在云桌面界面操作开关机。费用归属所属事业部承担。
              </p>
              <p v-else-if="temp.content.serverType[0] === '8'">
                <span style="font-size: 16px; font-weight: bolder; color: #ff4500">¥ 0.56</span>
                /每小时（
                <span style="text-decoration: line-through">官网价 ¥ 3.0/每小时</span>
                ）。
                <span style="font-weight: bolder; color: #ff4500">关机不收费，磁盘费用约50¥/月</span>
                ，可在云桌面界面操作开关机。费用归属所属事业部承担。
              </p>

              <p>
                <span style="font-weight: bolder; color: #ff4500">注意: 超过半年未开机系统将自动回收</span>
              </p>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="name" label="是否为外部用户" style="margin-left: -0.5%">
              <a-tooltip>
                <template #title>请先找AIM-何文龙开通外部用户的SSO账号</template>
                <a-icon type="question-circle" />
              </a-tooltip>
              <a-radio-group v-model:value="temp.content.isPocUser" button-style="solid">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12" v-if="temp.content.isPocUser === true">
            <a-form-model-item
              v-for="(i, index) in temp.content.pocUsers"
              :key="index"
              :name="'pocUsers.' + index"
              :rules="validatorUrl(i)"
              :label="index === 0 ? '外部用户SSO邮箱1' : '外部用户SSO邮箱' + (index + 1)"
            >
              <div style="margin-top: 10px; margin-left: 10px">
                <a-row :gutter="20">
                  <a-input
                    v-model:value="temp.content.pocUsers[index]"
                    placeholder="<EMAIL>"
                    style="width: 75%"
                  />
                  <tx-button
                    v-if="index === temp.content.pocUsers.length - 1"
                    style="margin-left: 20px"
                    type="primary"
                    @click="upStreamFormAdd"
                  >
                    新增
                  </tx-button>
                  <tx-button v-else style="margin-left: 20px" type="danger" @click="upStreamFormDel(i)">删除</tx-button>
                </a-row>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" placeholder="申请的用途或理由说明" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" :auto-size="{ minRows: 3 }" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/remote-work">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1 && temp.status !== 5"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="primary" @click="preApproveData" style="margin-left: 10px">预配置</tx-button>
          <tx-button type="primary" @click="preCreateApproveData" :loading="preStatus" style="margin-left: 10px">
            预创建
          </tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal
      title="云桌面关系绑定"
      :visible="preApproveVisible"
      @ok="autoApproveData"
      width="650px"
      @cancel="preApproveVisible = false"
    >
      <a-form-model ref="dataForm" :rules="rules" :model="temp.content" style="min-width: 200px; margin-left: 50px">
        <a-form-model-item label="云桌面IP">
          <a-input v-model:value="temp.content.ip" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="云桌面资源ID">
          <a-input v-model:value="temp.content.desktopId" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="代理信息" name="business">
          <a-select v-model:value="temp.content.idc">
            <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
            <a-select-option value="tencent">腾讯云</a-select-option>
            <a-select-option value="sh-office">云立方</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!--    服务预创建-->
    <a-modal
      title="服务器预创建"
      :visible="preCreateApproveVisible"
      @ok="autoCreateApproveData"
      width="800px"
      @cancel="preCreateApproveVisible = false"
    >
      <a-form-model
        ref="preDataForm"
        :rules="tencentRules"
        :model="temp.content"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="项目名称" name="projectName">
          <a-input v-model:value="temp.content.projectName" placeholder="默认为空！" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="可用区" name="approvalData.defaultZoneId">
          <a-select
            v-model:value="temp.content.approvalData.zoneIdList"
            filterable
            allow-create
            mode="multiple"
            placeholder="请选择"
            style="width: 500px"
            @change="refreshZoneData"
          >
            <a-select-option v-for="item in instanceTypeList" :key="item.zoneId" :value="item.zoneId">
              {{ item.zoneId }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="实例类型" name="approvalData.instanceType">
          <a-select
            v-model:value="temp.content.approvalData.instanceType"
            filterable
            allow-create
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option v-for="item in multipleInstanceList" :key="item.instanceType" :value="item.instanceType">
              {{ item.instanceType }} ( {{ item.label }} )
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="镜像名称" name="approvalData.defaultImageId">
          <a-select
            v-model:value="temp.content.approvalData.defaultImageId"
            filterable
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option v-for="item in imageList" :key="item.imageId" :value="item.imageId">
              {{ item.imageName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="VPC名称" name="approvalData.defaultVpcId">
          <a-select
            v-model:value="temp.content.approvalData.defaultVpcId"
            filterable
            placeholder="请选择"
            style="width: 500px"
            @change="refreshData"
          >
            <a-select-option v-for="item in tencentVpcInfo" :key="item.vpcId" :value="item.vpcId">
              {{ item.vpcName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="Subnet" name="approvalData.defaultVpcSubnetList">
          <a-select
            v-model:value="temp.content.approvalData.defaultSubnetId"
            filterable
            mode="multiple"
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option
              v-for="item in multipleVpcSubnetList"
              :key="item.subnetId + '|' + item.zone"
              :value="item.subnetId + '|' + item.zone"
            >
              {{ subnetOptionsWithDisplayValue[item.subnetId + '|' + item.zone] }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="安全组" name="approvalData.defaultSecurityGroupList">
          <a-select
            v-model:value="temp.content.approvalData.defaultSecurityGroupList"
            filterable
            mode="multiple"
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option
              v-for="item in securityGroupList"
              :key="item.securityGroupId"
              :value="item.securityGroupId"
            >
              {{ item.securityGroupName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="磁盘类型">
          <a-select v-model:value="temp.content.diskType" placeholder="为空则不添加额外磁盘" allowClear>
            <a-select-option v-for="item in tencentDiskTypeList" :key="item.key" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="磁盘大小">
          <a-input v-model:value="temp.content.diskSize" placeholder="" />
        </a-form-model-item>
        <a-form-model-item label="是否初始化">
          <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
            <a-radio-button :value="0">否</a-radio-button>
            <a-radio-button :value="1">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否安裝HIDS">
          <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
            <a-radio-button :value="0">否</a-radio-button>
            <a-radio-button :value="1">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getUserList } from '@/api/permission/user'
import AllUser from '@/views/comp/allOrgUser/index.vue'
import { tencentBaseInfo } from '@/api/workflow/asset_automation'
import cloneDeep from 'lodash.clonedeep'
import { GetUserOrgAllInfo } from '@/api/asset'
const serverTypeOptions = [
  {
    value: '4',
    label: '4vCPU',
    children: [
      {
        value: '8',
        label: '8GiB',
      },
    ],
  },
  {
    value: '8',
    label: '8vCPU',
    children: [
      {
        value: '16',
        label: '16GiB',
      },
    ],
  },
]
export default {
  name: 'RemoteDesktop',
  data() {
    return {
      localUser: store.getters.email,
      serverTypeOptions,
      userEmailList: [],
      time_now: '',
      preApproveVisible: false,
      preCreateApproveVisible: false,
      node_status: 1,
      tencentVpcIndex: 2,
      temp: {
        id: '',
        orderType: '远程办公云桌面申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: store.getters.email.split('@')[0],
          email: '',
          isPocUser: false,
          os: 'windows',
          serverType: ['4', '8'],
          needOtherDisks: '0',
          diskType: 'HDD',
          diskSize: 0,
          cost: 0.28,
          price: 1.5,
          ip: '',
          desktopId: '',
          accountType: 'user',
          accountName: '',
          authUsers: [],
          isAutoConfig: false,
          isAutoCreate: false,
          isAutomation: false,
          reason: '',
          // 自动化创建相关参数
          idc: 'tencent',
          org: 'AIM',
          dep: '运维',
          num: 1,
          env: 'online',
          application: ['云桌面'],
          project: '云桌面',
          isDb: false,
          preInstanceType: '',
          organization: [],
          business: '',
          pocUsers: [''],
          principal: [store.getters.email],
          costUser: store.getters.email,
          projectName: '',
          needPublicIp: 0,
          approvalData: {
            keys: [],
            needInit: 0,
            hidsInit: 0,
            isDesktop: 1,
            desktopType: 'remote',
            vlan: 15,
            hostIpList: [],
            instanceType: [],
            defaultImageId: '',
            defaultSubnetId: ['subnet-pu2fpjsj|ap-shanghai-4'],
            defaultSecurityGroupList: ['sg-a75s417q'],
            defaultVpcSwitchList: [],
            defaultVpcId: 'vpc-nf4jzh4y',
            defaultZoneId: 'ap-shanghai-4',
            zoneIdList: ['ap-shanghai-4'],
          },
        },
        timeline: [],
        comment: '',
      },
      tencentVpcInfo: [{ subnetInfo: [{}] }],
      tencentDiskTypeList: [
        { label: '普通云硬盘', key: 'CLOUD_BASIC', value: 'HDD' },
        { label: 'SSD云硬盘', key: 'CLOUD_SSD', value: 'SSD' },
      ],
      rules: antdFormRulesFormat({
        'content.name': [{ required: true, message: '请填写云桌面自定义名称', trigger: 'blur' }],
        'content.serverType': [{ required: true, message: '请选择配置规格', trigger: 'blur' }],
        'content.os': [{ required: true, message: '请选择操作系统', trigger: 'blur' }],
        'content.needOtherDisks': [{ required: true, message: '请选择是否需要数据盘', trigger: 'change' }],
        'content.accountType': [{ required: true, message: '请选择账号类型', trigger: 'change' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      tencentRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.defaultZoneId': [{ required: true, message: '请确认可用区', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultVpcId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultSubnetId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      multipleVpcSubnetList: [],
      securityGroupList: [],
      instanceTypeList: [{ zoneId: '', zoneInstanceTypeList: [{}] }],
      preStatus: false,
      imageList: [],
      multipleInstanceList: [],
      organizationOptions: [],
    }
  },
  components: {
    AllUser,
  },
  created() {
    this.getInfo()
  },
  mounted() {
    this.getUserTeamListMethod()
  },
  computed: {
    // 计算属性用于处理选项的显示值
    subnetOptionsWithDisplayValue() {
      const result = {}
      for (const item of this.multipleVpcSubnetList) {
        result[item.subnetId + '|' + item.zone] = `${item.subnetName} (${item.zone}) (${item.cidrBlock})`
      }
      return result
    },
  },
  methods: {
    getUserTeamListMethod() {
      GetUserOrgAllInfo({ email: store.getters.email }).then(response => {
        this.organizationOptions = response.Data.orgCas
        this.temp.content.organization = []
        if (this.organizationOptions[0].label !== undefined && this.organizationOptions[0].label !== null) {
          this.temp.content.organization.push(this.organizationOptions[0].label)
          if (this.organizationOptions[0].children !== null && this.organizationOptions[0].children[0].label !== null) {
            this.temp.content.organization.push(this.organizationOptions[0].children[0].label)
            if (
              this.organizationOptions[0].children[0].children !== null &&
              this.organizationOptions[0].children[0].children[0].label !== null
            ) {
              this.temp.content.organization.push(this.organizationOptions[0].children[0].children[0].label)
            }
          }
        }
        this.temp.content.business = response.Data.business[0]
      })
    },
    orgChange(orgs) {
      this.temp.content.authUsers = orgs
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
          // 临时兼容旧数据
        })
        this.nodeStatus = 1
      }
    },
    preApproveData() {
      this.preApproveVisible = true
    },
    preCreateApproveData() {
      this.preStatus = true
      this.getBaseListInfo()
    },
    // upStream 表单添加
    upStreamFormAdd() {
      this.temp.content.pocUsers.push('')
      console.log(this.temp.content.pocUsers)
    },
    // upStream 表单删除
    upStreamFormDel(infos) {
      this.temp.content.pocUsers.splice(this.temp.content.pocUsers.indexOf(infos), 1)
    },
    // 选择vpc后刷新子网
    refreshData(val) {
      this.$forceUpdate()
      this.tencentVpcIndex = (this.tencentVpcInfo || []).findIndex(item => item.vpcId === val)
      this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
      this.temp.content.approvalData.defaultSecurityGroupList = []
      this.temp.content.approvalData.defaultVpcSwitchList = []
      this.temp.content.approvalData.defaultSubnetId = []
    },
    refreshZoneData(val) {
      console.log(val)
      this.$forceUpdate()
      this.temp.content.approvalData.instanceType = []
      const tmpList = []
      if (val.length > 1) {
        for (var i = 0; i < val.length; i++) {
          const arr =
            this.instanceTypeList[(this.instanceTypeList || []).findIndex(item => item.zoneId === val[i])]
              .zoneInstanceTypeList
          tmpList.concat(arr)
        }
      } else {
        this.multipleInstanceList =
          this.instanceTypeList[
            (this.instanceTypeList || []).findIndex(item => item.zoneId === val[0])
          ].zoneInstanceTypeList
      }
      this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },
    getBaseListInfo() {
      const sendData = {
        cpu: this.temp.content.serverType[0],
        mem: this.temp.content.serverType[1],
      }
      tencentBaseInfo(sendData)
        .then(response => {
          // 复用服务器创建接口，云卓有些参数有默认值
          this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
          // this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
          this.temp.content.approvalData.defaultImageId =
            this.temp.content.os === 'ubuntu' ? 'img-l7xhxxe3' : 'img-bhh502ob'
          // this.temp.content.approvalData.defaultVpcId = response.Data.data.defaultVpcId
          this.temp.content.approvalData.defaultVpcId = 'vpc-nf4jzh4y'
          // this.temp.content.approvalData.defaultSubnetId = response.Data.data.defaultSubnetList
          this.temp.content.approvalData.defaultSubnetId = ['subnet-pu2fpjsj|ap-shanghai-4']
          // this.temp.content.approvalData.defaultZoneId = response.Data.data.zoneId
          // this.temp.content.approvalData.defaultZoneId = 'ap-shanghai-4'
          this.imageList = response.Data.data.imageList
          this.securityGroupList = response.Data.data.securityGroupList
          const names = this.temp.founderEmail.split('@')[0].split('_')
          let result = ''
          names.forEach(item => {
            result = result.concat(this.capitalizeFirstLetter(item))
          })
          this.temp.content.projectName = result
          this.$nextTick(() => {
            this.tencentVpcInfo = response.Data.data.vpcInfo
            this.instanceTypeList = response.Data.data.instanceTypeList
            // this.temp.content.approvalData.zoneIdList.push(this.temp.content.approvalData.defaultZoneId)
            this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
            this.multipleInstanceList =
              this.instanceTypeList[
                (this.instanceTypeList || []).findIndex(
                  item => item.zoneId === this.temp.content.approvalData.defaultZoneId
                )
              ].zoneInstanceTypeList
            this.temp.content.approvalData.instanceType = this.multipleInstanceList[0].instanceType
            // 初始化subnet交换id
          })
          this.preStatus = false
          this.preCreateApproveVisible = true
        })
        .catch(() => {
          this.preStatus = false
          this.preCreateApproveVisible = true
        })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.email = store.getters.email
          if (this.temp.content.isPocUser) {
            this.temp.content.pocUsers = Array.from(new Set(this.temp.content.pocUsers.filter(item => item)))
            this.temp.content.num = this.temp.content.pocUsers.length
          }
          this.temp.content.isAutomation = true
          this.temp.content.isAutoCreate = true
          const names = this.temp.founderEmail.split('@')[0].split('_')
          let result = ''
          names.forEach(item => {
            result = result.concat(this.capitalizeFirstLetter(item))
          })
          this.temp.content.approvalData.defaultImageId =
            this.temp.content.os === 'ubuntu' ? 'img-l7xhxxe3' : 'img-73bqwf59'
          if (this.temp.content.serverType[0] === '8') {
            this.temp.content.approvalData.instanceType = 'SA2.2XLARGE16'
          } else {
            this.temp.content.approvalData.instanceType = 'SA2.LARGE8'
          }
          this.temp.content.projectName = result
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/remote-desktop', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          if (this.temp.content.isPocUser) {
            this.temp.content.pocUsers = Array.from(new Set(this.temp.content.pocUsers.filter(item => item)))
            this.temp.content.num = this.temp.content.pocUsers.length
          }
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutomation = false
          this.temp.content.isAutoCreate = false
          this.temp.content.isAutoConfig = false
          if (this.temp.content.isPocUser) {
            this.temp.content.pocUsers = Array.from(new Set(this.temp.content.pocUsers.filter(item => item)))
            this.temp.content.num = this.temp.content.pocUsers.length
          }
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    autoApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutoConfig = true
          this.temp.content.isAutomation = false
          this.temp.content.isAutoCreate = false
          if (this.temp.content.isPocUser) {
            this.temp.content.pocUsers = Array.from(new Set(this.temp.content.pocUsers.filter(item => item)))
            this.temp.content.num = this.temp.content.pocUsers.length
          }
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    autoCreateApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutoCreate = true
          this.temp.content.isAutomation = true
          this.temp.content.approvalData.desktopType = 'remote'
          if (this.temp.content.isPocUser) {
            this.temp.content.pocUsers = Array.from(new Set(this.temp.content.pocUsers.filter(item => item)))
            this.temp.content.num = this.temp.content.pocUsers.length
          }
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          this.preCreateApproveVisible = false

          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    validatorUrl(row) {
      // 获取row信息
      // 返回的是rules
      return [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (row === '' || row === undefined) {
              return Promise.reject('请输入邮箱~')
            }
            return Promise.resolve()
          },
          trigger: 'blur',
        },
      ]
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          var arry = response.Data.data
          for (var i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
