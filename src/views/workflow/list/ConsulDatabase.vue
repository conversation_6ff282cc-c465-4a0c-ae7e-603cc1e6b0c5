<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="环境">
          <a-radio-group
            name="radioGroup"
            default-value="cloud_test"
            button-style="solid"
            v-model:value="temp.content.requestEnv"
            @change="changeRequestEnv"
          >
            <a-radio-button value="cloud_test">测试</a-radio-button>
            <a-radio-button value="consul_online">线上</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <!-- <a-form-model-item label="表类型">
          <a-radio-group name="radioGroup" default-value="multi" @change="radioGroupChang" button-style="solid" v-model:value="temp.content.requestEnv">
            <a-radio-button value="multi">multi表</a-radio-button>
          </a-radio-group>
        </a-form-model-item> -->
        <a-form-model-item label="机房" name="cluster">
          <a-select
            v-model:value="temp.content.cluster"
            mode="multiple"
            placeholder="请选择机房"
            style="width: 90%;"
            @change="clustersChange"
          >
            <a-select-option v-for="item in clustersList" :key="item.key" :value="item.key">
              {{ item.name }}
            </a-select-option>
          </a-select>
          <a-tooltip>
            <template #title>1.混合云的工单选择"混合云重保集群"，读取也从重保系统中读取<br>
              2.其他机房的工单选择"混合云线上"这个集群去管理，读取则按照之前给的各机房接入点，对应读取</template>
              <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="IP来源限制" name="ipOrigin">
          <div v-if="isIpError" class="ip-error-tips">IP格式错误，暂时无法添加，详情请联系运维人员</div>
          <a-select
            v-model:value="temp.content.ipOrigin"
            mode="tags"
            style="width: 100%"
            placeholder="请输入IP或网段"
            :options="ipsOptions"
            @change="ipItemChange"
            @search="ipItemSearch"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item label="Key值" name="">
          <div v-for="(i, index) in temp.content.keys" :key="index">
            <span style="color: gray">预览: database/{{ i.key }}</span>
            <a-input-group size="default">
              <a-row :gutter="20">
                <a-col :span="14">
                  <a-input
                    v-if="i.check === 'check'"
                    disabled
                    v-model:value="i.key"
                    placeholder="业务名/子模块名/数据库类型/文件名"
                  />
                  <a-input v-else v-model:value="i.key" placeholder="业务名/子模块名/数据库类型/文件名" />
                </a-col>
                <a-col :span="3">
                  <tx-button v-if="index == 0" type="primary" style="margin-left: 20px" @click="keyAdd">新增</tx-button>
                  <tx-button v-if="index > 0" style="margin-left: 20px" type="danger" @click="keyDel(i)">
                    删除
                  </tx-button>
                </a-col>
              </a-row>
            </a-input-group>
            <br />
          </div>
        </a-form-model-item>
        <a-form-model-item label="导入json">
          <NocEditorJson v-model="temp.content.json" style="height: 280px" :mode="'code'" lang="zh"></NocEditorJson>
        </a-form-model-item>
        <a-form-model-item label="key值校验">
          <!-- <tx-button v-if="temp.node>0" style="background-color: #43ad7f7f; " @click="consulDbCheck()">校验</tx-button> -->
          <a-table
            class="dataBaseTable"
            style="margin-top: 18px"
            :scroll="{ x: 800, y: 600 }"
            bordered
            :pagination="pagination"
            :columns="columnsInfo"
            :data-source="sqlRunInfotableList"
          ></a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/评论">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="approveDataCheck">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getConsulDbCheck } from '@/api/consul/database'
import { getClustersList } from '@/api/consul/console'
const statusMap = {
  2: {
    status: 'error',
    text: '未执行',
  },
  1: {
    status: 'success',
    text: '成功',
  },
}
const jsonSample = {
  数据库类型: {
    '分组名1': {
      user: 'xxxx',
      host: 'xxxx',
      port: 3306,
      database: 'xxxxx',
      password:"xxxx"
    },
    '分组名2': {
      user: 'xxxx',
      host: 'xxxx',
      port: 3306,
      database: 'xxxxx',
      password:"xxxx"
    },
  },
}
export default {
  name: 'Consuldatabase',
  components: {},

  data: function () {
    return {
      columnsInfo: [
        { title: 'key值', dataIndex: 'key', width: '400px', ellipsis: true },
        { title: '分组名', dataIndex: 'item', width: '200px', ellipsis: true },
        { title: '机房', dataIndex: 'zone', ellipsis: true },
        { title: '是否存在', dataIndex: 'exist', ellipsis: true },
      ],
      fetching: false,
      userEmailList: [],
      clustersList: [],
      ipsOptions: [],
      value: [],
      pagination: {
        defaultPageSize: 80,
        hideOnSinglePage: true,
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      size: 'small',
      loading: false,
      sqlRunInfotableList: [],
      node_status: 1,
      temp: {
        id: '',
        orderType: 'Consul数据库配置',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          requestEnv: 'consul_online',
          keys: [{ index: 0, key: '' }],
          sqlRunInfotableList: [],
          json: '',
          items: [],
          ids: [],
          reason: '',
          cluster: [],
          ipOrigin: [],
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        cluster: [{ required: true, message: '请选择机房' }],
        ipOrigin: [{ required: true, message: '请输入ip或网段' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },

    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id)
          .then(response => {
            response.Data.content = JSON.parse(response.Data.content.data)
            if (response.Data.orderType === this.temp.orderType) {
              this.temp = response.Data
              this.sqlRunInfotableList = this.temp.content.sqlRunInfotableList
              this.getClustersList().then(() => {
                if (this.temp.node === 2) {
                  const secAutomKey = 'secAutom'
                  const automKey = 'autom'
                  
                  if (!this.temp.content.cluster.includes(secAutomKey) && this.temp.content.cluster.includes(automKey)) {
                    this.temp.content.cluster.push(secAutomKey)
                  }
                }
              })
            } else {
              this.$router.push({ path: '/404' })
            }
          })
          .catch(() => {
            this.temp.content.json = jsonSample
          })
        this.nodeStatus = 1
      } else {
        this.temp.content.json = jsonSample
        this.getClustersList()
      }
    },
    changeRequestEnv() {
      this.temp.content.cluster = []
      this.getClustersList()
    },
    getClustersList() {
      const env = this.temp.content.requestEnv === 'consul_online' ? 'prod' : 'test'
      return getClustersList({ env }).then(response => {
        this.clustersList = response.Data.data || []
        return this.clustersList
      })
    },
    // key添加
    keyAdd() {
      this.temp.content.keys.push({ index: this.temp.content.keys.length, key: '' })
    },
    keyDel(infos) {
      this.temp.content.keys.splice(this.temp.content.keys.indexOf(infos), 1)
    },
    async consulDbCheck() {
      this.temp.content.sqlRunInfotableList = []
      let keys = []
      for (let i = 0; i < this.temp.content.keys.length; i++) {
        keys.push(this.temp.content.keys[i].key)
      }
      this.temp.content.items = []
      for (let k in this.temp.content.json) {
        for (let j in this.temp.content.json[k]) {
          this.temp.content.items.push(j)
        }
      }

      let query = {
        requestEnv: this.temp.content.requestEnv,
        type: 'multi',
        checkKey: keys,
        items: this.temp.content.items,
        zone: this.temp.content.cluster,
      }
      await getConsulDbCheck(query).then(response => {
        if (response.Data.exists !== null) {
          this.temp.content.sqlRunInfotableList = response.Data.exists
          this.sqlRunInfotableList = response.Data.exists
        }
      })
    },
    async approveDataCheck() {
      await this.consulDbCheck()
      // 增加内容是否齐全的判断
      let ContentCheck = false
      for (let k in this.temp.content.json) {
        for (let j in this.temp.content.json[k]) {
          if (
            (!this.temp.content.json[k][j].user && this.temp.content.json[k][j].user != '') ||
            (!this.temp.content.json[k][j].host && this.temp.content.json[k][j].host != '') ||
            !this.temp.content.json[k][j].port ||
            (!this.temp.content.json[k][j].database && this.temp.content.json[k][j].database != '')
          ) {
            ContentCheck = true
          }
        }
      }
      if (ContentCheck) {
        notification.error({
          message: '提交失败',
          description: '请填写完整的数据库信息！(host,user,port,database)',
        })
      } else if (this.temp.content.sqlRunInfotableList.every(item => item.exist !== 'key值格式错误,请重新输入')) {
        this.createData()
      } else {
        notification.error({
          message: '提交失败',
          description: 'key格式错误！',
        })
      }
    },
    createData() {
      // 工单不含机密信息
      // for (let i in this.temp.content.json) {
      //   for (let j in this.temp.content.json[i]) {
      //     this.temp.content.json[i][j].password = '******'
      //   }
      // }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData() {
      console.log(this.temp.content.json)
      if (this.temp.content.sqlRunInfotableList.length > 0) {
        this.temp.content.ids = []
        for (let i = 0; i < this.temp.content.sqlRunInfotableList.length; i++) {
          if (this.temp.content.sqlRunInfotableList[i].id !== 0) {
            this.temp.content.ids.push(this.temp.content.sqlRunInfotableList[i].id)
          }
        }
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    validateIPOrCIDR(input) {
      // 合并 IPv4、IPv6 和 CIDR 的正则表达式
      const ipOrCIDRRegex =
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(3[0-2]|[12]?[0-9]))?$|^(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}(?:\/(12[0-8]|1[01][0-9]|[1-9]?[0-9]))?$/i
      return ipOrCIDRRegex.test(input)
    },
    ipItemChange(newValue) {
      // 过滤掉不合法的值
      const validValues = newValue.filter(item => this.validateIPOrCIDR(item))
      // 如果有不合法的值，提示用户
      if (validValues.length < newValue.length) {
        // this.$message.error('输入包含不合法的 IP 地址或网段，已自动过滤')
        this.isIpError = true
      } else {
        this.isIpError = false
      }
      // 只更新合法的值
      this.temp.content.ipOrigin = validValues
      // 更新 options，只保留合法的值
      this.ipsOptions = validValues.map(item => ({ value: item, label: item }))
    },
    ipItemSearch(searchText) {
      if (searchText && !this.validateIPOrCIDR(searchText)) {
        this.isIpError = true
      } else if (searchText) {
        // 如果输入合法，将其添加到 options 中
        this.isIpError = false
        if (!this.ipsOptions || !this.ipsOptions.some(item => item.value === searchText)) {
          this.ipsOptions = [...this.ipsOptions, { value: searchText, label: searchText }]
        }
      } else {
        this.isIpError = false
      }
    },
  },
}
</script>

<style lang="less">
.dataBaseTable {
  /deep/.ant-table-header {
    font-size: 12px;
  }
  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  /deep/ .ant-table-tbody {
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.jsoneditor-vue {
  height: 100%;
}
.ip-error-tips {
  color: #ff4d4f;
  font-size: 14px;
}
</style>
