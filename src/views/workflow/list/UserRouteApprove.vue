<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-01-09 19:17:08
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-12-14 14:50:52
 * @FilePath: \cloud_web\src\views\workflow\list\UserRouteApprove.vue
 * @Description:
 *
 * Copyright (c) 2023 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <WorkflowStream
    @createDataSuccess="createDataSuccess"
    @cancel="cancelApply"
    :steps="steps"
    ref="WorkflowStream"
    :verifyNode="[
      {
        title: '路由表',
        name: 'selectedRoles',
      },
      {
        title: '变更权限',
        name: 'changeRoles',
      },
      {
        title: '申请理由',
        name: 'reason',
      },
    ]"
  >
    <template #contents>
      <a-form-model
        ref="temp"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item ref="selectedRoles" :required="true" name="selectedRoles" label="角色表">
          <a-transfer
            v-model:target-keys="targetKeys"
            :data-source="roleList"
            :disabled="disabled"
            :show-search="showSearch"
            :rowKey="item => item.roleName"
            :titles="['可供选择', '已选择']"
            :operations="['添加', '撤回']"
            :filter-option="filterOptions"
            :show-select-all="false"
            @change="onChange"
          >
            <template
              #children="{ filteredItems, selectedKeys: keys, disabled: listDisabled, onItemSelectAll, onItemSelect }"
            >
              <a-table
                :row-selection="
                  getRowSelection({
                    disabled: listDisabled,
                    selectedKeys: keys,
                    onItemSelectAll,
                    onItemSelect,
                  })
                "
                :columns="routeColumns"
                :data-source="filteredItems"
                size="small"
                :style="{ pointerEvents: listDisabled ? 'none' : null }"
                :custom-row="
                  ({ key, disabled: itemDisabled }) => ({
                    onClick: () => {
                      if (itemDisabled || listDisabled) return
                      onItemSelect(key, !keys.includes(key))
                    },
                  })
                "
              />
            </template>
          </a-transfer>
        </a-form-model-item>
        <a-form-model-item :required="true" label="变更权限" name="changeRoles">
          <a-table :childrenColumnName="'werqer121'" :columns="applyColumns" :data-source="changeAllKeys"></a-table>
        </a-form-model-item>
        <a-form-model-item :required="true" label="申请原因" name="reason">
          <a-textarea v-model:value="temp.content.reason" :disabled="disabled" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node != 0" label="回复/评论" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
      </a-form-model>
    </template>
  </WorkflowStream>
</template>
<script>
import WorkflowStream from '@/views/comp/workFlowStream/index.vue'
import { getOrderInfo } from '@/api/workflow/order'
import store from '@/store' // secondary package based on el-pagination
import { getRolesList, getUserRolesList } from '@/api/routePermission/index'
import { defineComponent } from 'vue'
const routeColumns = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'roleName',
    width: '120px',
  },
  {
    title: '描述',
    dataIndex: 'roleDesc',
    key: 'roleDesc',
    customRender: ({ text }) => {
      return text || '-'
    },
  },
]
const applyColumns = [
  {
    title: '变更类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '角色',
    dataIndex: 'roleName',
    key: 'roleName',
  },
]
export default defineComponent({
  components: { WorkflowStream },
  provide() {
    return {
      workFlowData: this,
    }
  },
  data() {
    this.routeColumns = routeColumns
    this.applyColumns = applyColumns
    return {
      userRouters: [],
      expandedKeys: [],
      selectedKeys: [],
      checkedKeys: [],
      changeAllKeys: [],
      targetKeys: [],
      originTargetKeys: [],
      disabled: false,
      showSearch: true,
      roleList: [],
      treeData: [],
      steps: [
        {
          title: '上级领导审批',
        },
        {
          title: '管理员审批',
        },
      ],
      rules: {},
      temp: {
        id: '',
        orderType: '云平台权限申请',
        founder: store.getters.name,
        founderEmail: store.getters.email,
        handlerEmail: '',
        node: 0,
        status: 0,
        time_now: '',
        handler: '',
        timeline: [],
        comment: '',
        content: {
          selectedRoles: '', // 数组字符串
          changeRoles: '', // 数组字符串
          reason: '',
        },
      },
    }
  },
  mounted() {
    this.getRoleTableData()
    if (this.$route.query.id) {
      this.initPage()
    } else {
      this.getUserRolesList()
    }
  },
  methods: {
    initPage(paramsId) {
      if (this.$route.query.id || paramsId) {
        getOrderInfo(this.$route.query.id ? this.$route.query.id : paramsId).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            let content = response.Data.content
            // 撤回后再组件 WorkflowStream 内包裹了数据
            if (response.Data.status === 30 && content.consulData) {
              content = JSON.parse(content.consulData || '{}')
            }
            this.temp = {
              ...response.Data,
              content,
            }
            const { changeRoles, selectedRoles } = content || {}
            this.changeAllKeys = JSON.parse(changeRoles || '[]') || []
            this.targetKeys = JSON.parse(selectedRoles || '[]') || []
            this.disabled = this.temp.node !== 0
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    createDataSuccess(params) {
      this.$router.push({ path: '/workflow/user-route-approve', query: { id: params.id } })
      this.initPage(params.id)
    },
    getRowSelection({ disabled, selectedKeys, onItemSelectAll, onItemSelect }) {
      return {
        getCheckboxProps: item => ({
          disabled: disabled || item.disabled,
        }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ key }) => key)
          onItemSelectAll(treeSelectedKeys, selected)
        },
        onSelect({ key }, selected) {
          onItemSelect(key, selected)
        },
        selectedRowKeys: selectedKeys,
      }
    },
    onChange(nextTargetKeys) {
      const addKeys = nextTargetKeys.filter(key => !this.originTargetKeys.includes(key)) || []
      const cancelKeys = this.originTargetKeys.filter(key => !nextTargetKeys.includes(key)) || []
      this.changeAllKeys = [
        ...addKeys.map(key => {
          const item = this.roleList.find(obj => obj.roleName === key)
          return {
            type: '新增',
            roleName: item.roleName,
          }
        }),
        ...cancelKeys.map(key => {
          const item = this.roleList.find(obj => obj.roleName === key)
          return {
            type: '删除',
            roleName: item.roleName,
          }
        }),
      ]
      this.temp.content.selectedRoles = JSON.stringify(nextTargetKeys || [])
      this.temp.content.changeRoles = JSON.stringify(this.changeAllKeys)
    },
    getUserRolesList(keyword) {
      getUserRolesList({
        keyword,
      })
        .then(res => {
          this.targetKeys = res.Data.roles
          this.originTargetKeys = res.Data.roles
        })
        .catch(() => {
          this.$message.error('获取角色失败')
        })
    },
    getRoleTableData(keyword) {
      getRolesList({
        keyword,
      })
        .then(res => {
          this.roleList = res.Data.roles
        })
        .catch(() => {
          this.$message.error('获取角色失败')
        })
    },
    cancelApply() {
      this.$router.back()
    },
    filterOptions(inputValue, item) {
      return (
        item.roleName.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1 ||
        item.roleDesc.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1
      )
    },
  },
})
</script>
