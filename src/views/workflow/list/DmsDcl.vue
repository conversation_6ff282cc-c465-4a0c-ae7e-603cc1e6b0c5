<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全管理员审批" />
        <a-step title="DBA审批DCL" />
        <a-step title="DBA审批Consul" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <div v-if="temp.node !== 4">
          <a-form-model-item label="数据库用户名" name="dbUserName">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-input v-model:value="temp.content.dbUserName" placeholder="" />
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="客户端IP" name="clientIps">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-input v-model:value="temp.content.clientIps" placeholder="例如:*********|*********" />
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="权限快捷操作:">
            <a-radio-group
              name="radioGroup"
              :default-value="1"
              @change="radioGroupChang"
              v-model:value="radioGroupValue"
            >
              <a-radio :value="1">只读</a-radio>
              <a-radio :value="2">读写</a-radio>
              <a-radio :value="3">自定义</a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="权限信息:" name="permissionValue">
            <a-row :gutter="24">
              <a-col :span="20">
                <a-checkbox-group
                  v-model:value="temp.content.permissionValue"
                  name="checkboxgroup"
                  :options="permissionOptions"
                  @change="chekboxChange"
                />
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="授权数据库" name="mysqls">
            <!-- <tx-button type="primary" @click="addRow">新增</tx-button> -->
            <a-row :gutter="24">
              <a-col :span="16">
                <a-select
                  placeholder="请搜索并选择数据库, 正则检索加前缀: Rex-"
                  mode="multiple"
                  v-model:value="temp.content.mysqls[0].dbNames"
                  :showSearch="true"
                  :filter-option="false"
                  :allowClear="true"
                  @search="searchmysqlIpMethod"
                >
                  <a-select-option v-for="item1 in mysqlIpDbNamesList" :key="item1" :value="item1">
                    {{ item1 }}
                  </a-select-option>
                  <template #dropdownRender="menu">
                    <v-nodes :vnodes="menu" />
                    <a-divider style="margin: 4px 0" />
                    <div style="padding: 4px 8px; cursor: pointer" @mousedown="e => e.preventDefault()">
                      <tx-button type="link" :size="size" @click="selectAll">全选</tx-button>
                      <tx-button type="link" :size="size" @click="clearAll">清空</tx-button>
                    </div>
                  </template>
                </a-select>
              </a-col>
              <a-col :span="4"></a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item v-if="temp.node === 3" label="密码获取方式:">
            <tx-button type="primary" @click="getDbPassword" :loading="loading">获取密码</tx-button>
            <tx-button type="danger" v-if="temp.node === 3" style="margin-left: 20px" @click="modifyGenSqlContent">
              修改生成SQL内容
            </tx-button>
          </a-form-model-item>
          <a-form-model-item v-if="temp.node >= 3" label="授权SQL" name="business">
            <NocEditorJson
              v-model="temp.content.authSQLContent"
              style="height: 280px"
              :mode="'code'"
              lang="zh"
            ></NocEditorJson>
          </a-form-model-item>
        </div>
        <a-form-model-item v-if="(temp.node === 4 || temp.node === 5) && userpRression" label="DCL执行情况:">
          <tx-button type="primary" @click="getSQLRunInfo">点击查看</tx-button>
        </a-form-model-item>
        <!-- consul配置选项 -->
        <a-form-model-item v-if="temp.node !== 3 && temp.node !== 4" label="Consul配置选型" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-radio-group v-model:value="temp.content.consulNeed" button-style="solid" @change="getClustersList()">
                <a-radio-button value="no">不需要</a-radio-button>
                <a-radio-button value="yes">需要</a-radio-button>
              </a-radio-group>
            </a-col>
          </a-row>
        </a-form-model-item>
        <div v-if="temp.content.consulNeed === 'yes' && temp.node !== 3">
          <a-form-model-item label="Key值" name="key">
            <a-row :gutter="24">
              <a-col :span="12">
                <span style="color: gray">预览: database/{{ temp.content.key }}</span>
                <a-input v-model:value="temp.content.key" placeholder="业务名/子模块名/数据库类型/文件名" />
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="机房" name="cluster">
          <a-select
            v-model:value="temp.content.cluster"
            mode="multiple"
            placeholder="请选择机房"
          >
            <a-select-option v-for="item in clustersList" :key="item.key" :value="item.key">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
          <a-form-model-item label="导入json">
            <a-radio-group v-model:value="temp.content.jsonExample" button-style="solid">
              <a-radio-button :value="false">填写内容</a-radio-button>
              <a-radio-button :value="true">查看示例</a-radio-button>
            </a-radio-group>
            <NocEditorJson
              v-if="temp.content.jsonExample"
              v-model="temp.content.exampleJson"
              style="height: 280px"
              :mode="'code'"
              lang="zh"
            ></NocEditorJson>
            <NocEditorJson
              v-else
              v-model="temp.content.json"
              style="height: 280px"
              :mode="'code'"
              lang="zh"
            ></NocEditorJson>
          </a-form-model-item>
        </div>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="反馈信息">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            (temp.node === 1 || temp.node === 2) &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safeapproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button v-if="temp.content.passwd === 'no'" type="primary" @click="dbaCheck">DBA校验</tx-button>
          <tx-button v-else type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="consulapproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal
      title="请DBA输入校验密码"
      :visible="dbaCheckVisible"
      :confirm-loading="confirmLoading"
      :closable="false"
      :maskClosable="false"
      @ok="dbaCheckSubmit"
      @cancel="dbaCheckCancel"
    >
      <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="密码">
          <a-input type="text" style="width: 300px; -webkit-text-security: disc" v-model:value="checkPaaswd" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="生成授权SQL"
      :visible="currentPasswordVisible"
      :closable="false"
      :maskClosable="false"
      @ok="generateAuthSqls"
      @cancel="cancelgenerate"
    >
      <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="密码">
          <a-input v-model:value="databasePaaswd" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal v-model:visible="sqlRunInfoVision" title="SQL执行详细情况" :width="1500" centered :footer="null">
      <a-table
        class="dataBaseTable"
        style="margin-top: 18px"
        :scroll="{ x: 1000, y: 600 }"
        bordered
        :pagination="pagination"
        :columns="columnsInfo"
        :data-source="sqlRunInfotableList"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'Status'">
            <a-form-model :ref="record.index" :model="record">
              <a-form-model-item class="ant-form-item—table">
                <a-badge
                  v-if="record.Status == 1"
                  :status="statusTypeFilter(record.Status)"
                  :text="statusFilter(record.Status)"
                />
                <tx-button v-else type="danger" @click="sqlFailedRetry(record)">重试</tx-button>
              </a-form-model-item>
            </a-form-model>
          </template>
        </template>
      </a-table>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import * as consul from '@/api/consul/console'
import { getUserList } from '@/api/permission/user'
import { getDbNamesList } from '@/api/db/dbApproval'

import {
  dbPasswdCheck,
  dbSqlSingle,
  dblDownloadload,
  dblUpload,
  dclGenerateAuthSqls,
  dclGetPasword,
} from '@/api/db/ddl'
const columns = [
  {
    title: '操作',
    dataIndex: 'action',
    width: '120px',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
  {
    title: '数据库名称',
    dataIndex: 'dbNames',
    scopedSlots: { customRender: 'dbNames' },
  },
]
const statusMap = {
  2: {
    status: 'error',
    text: '未执行',
  },
  1: {
    status: 'success',
    text: '成功',
  },
}
export default {
  name: 'DCL',
  components: {
    VNodes: {
      // functional: true,
      render: ctx => {
        return ctx.$attrs.vnodes.menuNode
      },
    },
  },

  data: function () {
    this.columns = columns
    let checkDbNames = async (_rule, value) => {
      console.log(value[0].dbNames, 'vvvvvvv')

      if (!value[0].dbNames.length) {
        return Promise.reject('请输入数据库名称')
      } else {
        return Promise.resolve()
      }
      // if (!Number.isInteger(value)) {
      //   return Promise.reject('Please input digits')
      // } else {
      //   if (value < 18) {
      //     return Promise.reject('Age must be greater than 18')
      //   } else {
      //     return Promise.resolve()
      //   }
      // }
    }
    return {
      columnsInfo: [
        {
          title: 'ID',
          dataIndex: 'Index',
          width: '80px',
          align: 'center',
          sorter: (a, b) => a.Index - b.Index,
          defaultSortOrder: 'ascend',
        },
        { title: '数据库|客户端', dataIndex: 'IpClient', width: '200px', ellipsis: true },
        { title: 'sql语句', dataIndex: 'SqlContent', ellipsis: true },
        {
          title: '执行情况',
          dataIndex: 'Status',
          width: '100px',
          scopedSlots: { customRender: 'Status' },
          filters: [
            {
              text: '成功',
              value: 1,
            },
            {
              text: '失败',
              value: 2,
            },
          ],
          onFilter: (value, record) => record.Status === value,
        },
        { title: '报错信息', dataIndex: 'ErrInfo', width: '300px' },
      ],
      sqlRunInfotableList: [],
      fetching: false,
      userpRression: false,
      radioGroupValue: 1,
      permissionOptions: [
        'SELECT',
        'UPDATE',
        'DELETE',
        'INSERT',
        'ALTER',
        'CREATE',
        'INDEX',
        'PROCESS',
        'RELOAD',
        'REPLICATION CLIENT',
        'REPLICATION SLAVE',
        'EXECUTE',
      ],
      userEmailList: [],
      mysqlIpList: [],
      mysqlIpDbNamesList: [],
      value: [],
      confirmLoading: false,
      dbaCheckVisible: false,
      currentPasswordVisible: false,
      sqlRunInfoVision: false,
      pagination: {
        defaultPageSize: 1000,
        hideOnSinglePage: true,
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      size: 'small',
      loading: false,
      node_status: 1,
      checkPaaswd: '',
      databasePaaswd: '',
      temp: {
        id: '',
        orderType: 'DCL申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          dbUserName: undefined,
          consulNeed: 'no',
          jsonExample: false,
          key: '',
          cluster:[],
          exampleJson: {
            mysql: {
              分组名1: {
                user: 'xxxx',
                host: 'xxxx',
                port: 3306,
                database: 'xxxxx',
              },
              分组名2: {
                user: 'xxxx',
                host: 'xxxx',
                port: 3306,
                database: 'xxxxx',
              },
            },
          },
          json: null,
          mysqls: [{ row: 0, dbNames: [] }],
          ipDbName: [],
          clientIps: undefined,
          permissionValue: ['SELECT'],
          sendUser: undefined,
          splitSqlfileName: '',
          reason: '',
          passwd: 'no',
          authSQLContent: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.mysqls': [{ required: true, validator: checkDbNames, trigger: 'blur' }],
        'content.dbUserName': [{ required: true, message: '请填写用户名', trigger: 'blur' }],
        'content.clientIps': [
          { required: true, message: '请填写客户端服务器的ip', trigger: 'blur' },
          { pattern: /^[^,，]*$/, message: '不能包含逗号,请用 | 分割ip' },
        ],
        'content.permissionValue': [{ required: true, message: '请选择用户分配权限', trigger: 'blur' }],
        'content.sendUser': [{ required: true, message: '请填写抄送人邮箱', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        'content.key': [{ required: true, message: '请填写Key值', trigger: 'blur' }],
        'content.cluster':[{required: true, message: '请选择机房', trigger: 'blur' }]
      }),
      dbList: [],
      clustersList:[]
    }
  },
  created() {
    this.getInfo()
    this.getUserRoles(store.getters.email)
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        if (response.Data.data[0].roles.includes('dba_user')) {
          this.userpRression = true
        }
      })
    },
    selectAll(record) {
      for (let i = 0; i < this.mysqlIpDbNamesList.length; i++) {
        this.temp.content.mysqls[0].dbNames.push(this.mysqlIpDbNamesList[i])
      }
    },
    clearAll(record) {
      this.temp.content.mysqls[0].dbNames = []
    },
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.node === 3 && this.temp.handlerEmail.includes(this.localUser)) {
              this.dbaCheckVisible = true
            }
            if (this.temp.node === 4 && this.temp.content.splitSqlfileName !== '') {
              let that1 = this
              dblDownloadload({ filesName: this.temp.content.splitSqlfileName }).then(response => {
                if (response.Data.filesStatus === 'exist') {
                  that1.temp.content.authSQLContent = JSON.parse(response.Data.filesContent)
                } else {
                  notification.error({
                    message: 'sql文件不存在',
                  })
                }
              })
            }

            // 当temp.node === 4时，自动填入secAutom机房
            if (this.temp.node === 4) {
              this.getClustersList().then(() => {
                // 确保clustersList中包含key为secAutom的机房选项
                const secAutomKey = 'secAutom'
                const automKey = 'autom'
            
                // 自动选中secAutom机房
                if (!this.temp.content.cluster.includes(secAutomKey) && this.temp.content.cluster.includes(automKey)) {
                  this.temp.content.cluster.push(secAutomKey)
                }
              })
            }

            // if (this.temp.node === 4 && this.temp.content.consulNeed == 'yes') {
            //   for (let i in this.temp.content.json) {
            //     for (let j in this.temp.content.json[i]) {
            //       this.temp.content.json[i][j].password = '******'
            //     }
            //   }
            // }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp.content = response.Data.content
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    modifyGenSqlContent() {
      if (this.temp.content.splitSqlfileName !== '') {
        let sendBody = JSON.stringify(this.temp.content.authSQLContent)
        dblUpload({
          filesName: this.temp.content.splitSqlfileName,
          filesContent: sendBody,
          modifyFlags: 'SQLmodify',
        }).then(response => {
          if (response.Data.message === 'ok') {
            notification.success({
              message: 'sql内容修改成功',
            })
          } else {
            notification.error({
              message: 'sql内容修改失败！',
            })
          }
        })
      } else {
        notification.error({
          message: 'SQL数据不存在',
        })
      }
    },
    getDbPassword() {
      if (this.temp.content.mysqls[0].dbNames.length > 0) {
        this.loading = true
        dclGetPasword({
          ipDbNames: this.temp.content.mysqls[0].dbNames,
          dbUserName: this.temp.content.dbUserName,
          dbaPassword: this.temp.content.passwd,
        })
          .then(response => {
            this.loading = false
            this.currentPasswordVisible = true
            if (response.Data.exist === 'exist') {
              this.databasePaaswd = response.Data.password
            } else {
              this.databasePaaswd = '未找到密码！'
            }
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        notification.error({
          message: '授权数据库为空！',
        })
      }
    },
    sqlFailedRetry(record) {
      dbSqlSingle({
        orderId: this.temp.id,
        taskId: record.ID,
        ip: record.Ip,
        dbName: 'sys',
        sqlContent: record.SqlContent,
        redisKey: this.temp.content.splitSqlfileName + 'info',
        password: this.temp.content.passwd,
      }).then(response => {
        if (response.Data.message === 'ok') {
          this.getSQLRunInfo()
        }
      })
    },
    generateAuthSqls() {
      dclGenerateAuthSqls({
        clientIps: this.temp.content.clientIps,
        dbUserName: this.temp.content.dbUserName,
        genPassword: this.databasePaaswd,
        permission: this.temp.content.permissionValue,
        ipDbNames: this.temp.content.mysqls[0].dbNames,
      }).then(response => {
        if (response.Data.message === 'ok') {
          this.temp.content.splitSqlfileName = response.Data.GenFileNames
          let that1 = this
          dblDownloadload({ filesName: response.Data.GenFileNames }).then(response => {
            if (response.Data.filesStatus === 'exist') {
              that1.temp.content.authSQLContent = JSON.parse(response.Data.filesContent)
            } else {
              notification.error({
                message: 'sql文件不存在',
              })
            }
          })
        }
      })
      this.cancelgenerate()
    },
    cancelgenerate() {
      this.currentPasswordVisible = false
      this.databasePaaswd = ''
    },
    getSQLRunInfo() {
      let sendBody = {}
      sendBody.filesName = this.temp.content.splitSqlfileName + 'info'
      sendBody.type = 'hash'
      dblDownloadload(sendBody).then(response => {
        if (response.Data.filesStatus === 'exist') {
          this.sqlRunInfoVision = true
          // var filesJson = JSON.parse(response.Data.filesContent)
          let filesJson = JSON.parse(response.Data.filesContent)
          let values = Object.values(filesJson)
          this.sqlRunInfotableList = []
          for (let i = 0, len = values.length; i < len; i++) {
            this.sqlRunInfotableList.push(JSON.parse(values[i]))
          }
        } else {
          notification.error({
            message: '执行详情不存在',
          })
        }
      })
    },
    radioGroupChang() {
      if (this.radioGroupValue === 1) {
        this.temp.content.permissionValue = ['SELECT']
      } else if (this.radioGroupValue === 2) {
        this.temp.content.permissionValue = ['SELECT', 'UPDATE', 'DELETE', 'INSERT']
      } else if (this.radioGroupValue === 3) {
        this.temp.content.permissionValue = []
      }
    },
    chekboxChange() {
      if (
        this.temp.content.permissionValue.length === 4 &&
        this.temp.content.permissionValue[0] === 'SELECT' &&
        this.temp.content.permissionValue[1] === 'UPDATE' &&
        this.temp.content.permissionValue[2] === 'DELETE' &&
        this.temp.content.permissionValue[3] === 'INSERT'
      ) {
        this.radioGroupValue = 2
      } else if (this.temp.content.permissionValue[0] === 'SELECT' && this.temp.content.permissionValue.length === 1) {
        this.radioGroupValue = 1
      } else {
        this.radioGroupValue = 3
      }
    },
    searchmysqlIpMethod(search) {
      this.mysqlIpDbNamesList = []
      if (search !== '' && search !== 'Rex-') {
        getDbNamesList({ searchText: search }).then(response => {
          this.mysqlIpDbNamesList = response.Data.ipdbName
        })
      }
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    addRow() {
      this.temp.content.mysqls.push({ row: this.temp.content.mysqls.length, dbNames: [] })
    },
    deleteRow(record) {
      if (this.temp.content.mysqls.length > 1) {
        this.temp.content.mysqls.splice(record.row, 1)
        for (let i = record.row; i < this.temp.content.mysqls.length; i++) {
          this.temp.content.mysqls[i].row -= 1
        }
      }
    },
    dbaCheck() {
      this.dbaCheckVisible = true
      this.checkPaaswd = ''
    },
    dbaCheckSubmit() {
      dbPasswdCheck({ password: this.checkPaaswd }).then(response => {
        if (response.Data.message !== 'error') {
          this.temp.content.passwd = response.Data.message
        } else {
          notification.error({
            message: '密码错误，请检查！',
          })
        }
      })
      this.dbaCheckVisible = false
    },
    dbaCheckCancel() {
      this.dbaCheckVisible = false
    },
    createData() {
      if (this.temp.content.consulNeed == 'yes') {
        console.log(this.temp.content.json, 'JSON')
        if (!this.temp.content.json || Object.keys(this.temp.content.json).length == 0) {
          console.log('未填写')
          notification.error({
            message: '未填写JSON，请检查',
          })
          return
        }
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          createOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/dcl', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    safeapproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 2
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    getClustersList() {
      const env =  'prod' 
      return consul.getClustersList({ 'env':env }).then(response => {
        this.clustersList = response.Data.data || []
        return this.clustersList
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              // if (this.temp.node === 4 && this.temp.content.consulNeed == 'yes') {
              //   for (let i in this.temp.content.json) {
              //     for (let j in this.temp.content.json[i]) {
              //       this.temp.content.json[i][j].password = '******'
              //     }
              //   }
              // }
            }
          })
        }
      })
    },
    consulapproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 5
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less">
.dataBaseTable {
  /deep/.ant-table-header {
    font-size: 12px;
  }
  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  /deep/ .ant-table-tbody {
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.jsoneditor-vue {
  height: 100%;
}
</style>
