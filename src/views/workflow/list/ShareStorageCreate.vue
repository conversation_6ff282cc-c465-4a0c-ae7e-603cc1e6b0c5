<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="IT管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card
      style="margin-top: 12px"
      :bordered="false"
      :title="`${temp.orderType}——流程完成后第二天生效（云存储自动同步用户数据）`"
    >
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="name" label="名称">
              <a-input
                :disabled="temp.node > 0"
                v-model:value="temp.content.name"
                @blur="checkSameName"
                placeholder="填写共享云存储的名称"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="userEmails" label="共享使用人邮箱列表">
              <OrgUser
                style="width: 100%"
                :checkValue="temp.content.userEmails"
                @selectChange="selectChange"
                placeholder="选填，可选多人"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="comment" label="申请理由">
              <a-textarea v-model:value="temp.content.comment" placeholder="填写申请的理由" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/storage/osp-share">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import OrgUser from '@/views/comp/allOrgUser/index.vue'
import { ospShareNamesExist } from '@/api/shareStorage'
import { deepClone } from '@/utils/util'

export default {
  name: 'ShareStorageCreate',
  data() {
    const reg = /^[a-z0-9.-]+$/
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('请填写云存储名称'))
      } else if (!reg.test(value)) {
        return callback(new Error('存储桶名称只能包含小写字符、数字、点和连字符'))
      } else {
        return new Promise((resolve, reject) => {
          ospShareNamesExist({
            name: this.temp.content.name,
          })
            .then(res => {
              if (res.exist) {
                reject('名称重复')
              } else {
                resolve()
              }
            })
            .catch(() => {
              reject('接口错误')
            })
        })
      }
    }

    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '新增共享云存储',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          founder_uid: '',
          name: '',
          userEmails: [],
          comment: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        name: [{ validator: validatePass, required: true, trigger: 'change' }],
        comment: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
    }
  },
  components: {
    OrgUser,
  },
  created() {
    this.getInfo()
  },
  methods: {
    checkSameName() {
      ospShareNamesExist({
        name: this.temp.content.name,
      }).then(res => {
        this.$refs.orderForm
      })
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.temp.content.userEmails = JSON.parse(this.temp.content.userEmails)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.founder_uid = String(noc.user.getUserInfo().uid) || ''
          const cloneTemp = deepClone(this.temp)
          cloneTemp.content.userEmails = JSON.stringify(cloneTemp.content.userEmails)
          createOrder(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })

              this.temp = response.Data
              this.temp.content.userEmails = JSON.parse(this.temp.content.userEmails)
              this.$router.push({ path: '/workflow/share-storage-create', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const cloneTemp = deepClone(this.temp)
          cloneTemp.content.userEmails = JSON.stringify(cloneTemp.content.userEmails)

          approveOrder(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.userEmails = JSON.parse(this.temp.content.userEmails)

              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // //this.$refs.orderForm.refresh()
    },
    selectChange(arg) {
      this.temp.content.userEmails = arg
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const cloneTemp = deepClone(this.temp)
          cloneTemp.content.userEmails = JSON.stringify(cloneTemp.content.userEmails)

          approveOrder(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.userEmails = JSON.parse(this.temp.content.userEmails)
              notification.success({
                message: '审批成功',
                description: 'IT管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      this.temp.node = 3
      this.temp.status = 20
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      const cloneTemp = deepClone(this.temp)
      cloneTemp.content.userEmails = JSON.stringify(cloneTemp.content.userEmails)

      approveOrder(cloneTemp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          this.temp = response.Data
          this.temp.content.userEmails = JSON.parse(this.temp.content.userEmails)

          notification.success({
            message: '审批完成',
            description: '该审批已被拒绝',
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      this.temp.node = 3
      this.temp.status = 30
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email

      const cloneTemp = deepClone(this.temp)
      cloneTemp.content.userEmails = JSON.stringify(cloneTemp.content.userEmails)

      approveOrder(cloneTemp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '撤回失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          this.temp = response.Data
          this.temp.content.userEmails = JSON.parse(this.temp.content.userEmails)

          notification.success({
            message: '撤回成功',
            description: '该工单被申请人撤回',
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
