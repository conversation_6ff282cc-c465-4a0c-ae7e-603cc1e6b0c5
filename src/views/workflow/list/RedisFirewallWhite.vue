<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="Redis IP" v-if="temp.status === 0" name="searchIp">
          <a-input v-model:value="temp.content.searchIp" placeholder="精确查询" style="width: 50%" />
          <tx-button type="primary" @click="searchRedisInfo" style="margin-left: 10px">查询</tx-button>
        </a-form-model-item>
        <a-form-model-item
          label="当前Redis信息"
          v-if="temp.content.vip != '' || temp.content.ip != '' || temp.content.ips != ''"
        >
          <a-span v-if="temp.content.vip != ''" style="margin-left: 10px">VIP :{{ temp.content.vip }}</a-span>
          <a-span v-if="temp.content.ip != ''" style="margin-left: 10px">主库IP:{{ temp.content.ip }}</a-span>
          <a-span v-if="temp.content.ips != ''" style="margin-left: 10px">从库IP:{{ temp.content.ips }}</a-span>
          <br />
          <a-table
            v-if="temp.content.existFireWall.length > 0"
            style="margin-top: 18px"
            bordered
            :pagination="false"
            :scroll="scrolls"
            :columns="tablecolumns"
            :data-source="temp.content.existFireWall"
          ></a-table>
        </a-form-model-item>
        <a-form-model-item label="新增白名单" v-if="temp.content.ip != ''">
          <tx-button @click="rowServerAdd" style="min-height: 24px; line-height: 24px; padding: 0 5px; float: right">
            添加白名单规则
          </tx-button>
          <a-table
            style="margin-top: 18px"
            bordered
            :pagination="false"
            :columns="newtablecolumns"
            :data-source="temp.content.newFireWall"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'ip'">
                <a-input v-model:value="record.ip" style="width: 80%" @blur="trimIP(record.index)" />
              </template>
              <template v-if="column.dataIndex == 'port'">
                <a-input-number v-model:value="record.port" :min="1" :max="65535" />
              </template>
              <template v-if="column.dataIndex == 'action'">
                <!-- <tx-button
                  v-if="record.index === 0"
                  type="primary"
                  icon="plus"
                  @click="rowServerAdd"
                  size="small"
                  style="min-height: 24px; line-height: 24px; padding: 0 5px"
                ></tx-button> -->
                <tx-button
                  type="danger"
                  icon="minus"
                  @click="rowServerDelete(record)"
                  size="small"
                  style="min-height: 24px; line-height: 24px; padding: 0 5px"
                ></tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>

        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" :auto-size="{ minRows: 3, maxRows: 4 }" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="反馈信息">
          <a-textarea v-model:value="temp.content.commit" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 5 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 4 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 5 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 5 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 5 }"
        >
          <tx-button type="primary" @click="safeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { CheckBlackIps, getGlobalRule } from '@/api/gateway/globalRule'
import { getRedisInfo } from '@/api/dbManagement/dbm'

const tablecolumns = [
  {
    title: 'IP/LAN',
    dataIndex: 'ip',
    width: '140x',
  },
  {
    title: '端口(1-65536)',
    dataIndex: 'port',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
]
const newtablecolumns = [
  {
    title: 'IP/LAN',
    dataIndex: 'ip',
  },
  {
    title: '端口(1-65536)',
    dataIndex: 'port',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    align: 'center',
    width: '100px',
  },
]

export default {
  name: 'RedisFirewallWhite',
  components: {},

  data: function () {
    this.lastFetchId = 0
    this.tablecolumns = tablecolumns
    this.newtablecolumns = newtablecolumns
    return {
      fetching: false,
      scrolls: {
        y: 200,
      },
      value: [],
      endOpen: false,
      ipsCheck: false,
      localUser: store.getters.email,
      DomainSslList: [],
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '混合云Redis白名单',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          vip: '',
          ip: '',
          ips: '',
          existFireWall: [],
          newFireWall: [{ index: 0, ip: '127.0.0.1', port: 6379 }],
          immediately: 'stop',
          deleteId: undefined,
          reason: '',
          ips: '',
          downloadURL: '',
          filesPassword: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        searchIp: [{ required: true, message: '请填写Redis IP', trigger: 'blur' }],
      },
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            response.Data.content = JSON.parse(response.Data.content.data)
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    rowServerAdd() {
      this.temp.content.newFireWall.push({ index: this.temp.content.newFireWall.length, ip: '127.0.0.1', port: '6379' })
    },
    rowServerDelete(record) {
      this.temp.content.newFireWall.splice(record.index, 1)
      for (let i = 0; i < this.temp.content.newFireWall.length; i++) {
        this.temp.content.newFireWall[i].index = i
      }
    },
    trimIP(index) {
      this.temp.content.newFireWall[index].ip = this.temp.content.newFireWall[index].ip.trim()
    },
    searchRedisInfo() {
      if (this.temp.content.searchIp === '' || this.temp.content.searchIp === undefined) {
        notification.error({
          message: '查询失败',
          description: '请填写查询IP',
        })
        this.temp.content.vip = ''
        this.temp.content.ip = ''
        this.temp.content.ips = ''
        this.temp.content.existFireWall = []
        return
      }
      getRedisInfo({ searchIp: this.temp.content.searchIp }).then(res => {
        this.temp.content.vip = res.Data.vip
        this.temp.content.ip = res.Data.ip
        this.temp.content.ips = res.Data.ips
        this.temp.content.existFireWall = res.Data.data
      })
    },

    createData() {
      if (this.temp.content.newFireWall.length === 0) {
        notification.error({
          message: '创建失败',
          description: '请填写新增防火墙白名单',
        })
        return
      } else {
        for (let i = 0; i < this.temp.content.newFireWall.length; i++) {
          for (let j = 0; j < this.temp.content.existFireWall.length; j++) {
            if (
              this.temp.content.newFireWall[i].ip === this.temp.content.existFireWall[j].ip &&
              this.temp.content.newFireWall[i].port === this.temp.content.existFireWall[j].port
            ) {
              notification.error({
                message: '创建失败',
                description:
                  '已存在白名单' + this.temp.content.newFireWall[i].ip + ':' + this.temp.content.newFireWall[i].port,
              })
              return
            }
          }
        }
        if (this.temp.content.newFireWall.length > 15) {
          notification.error({
            message: '创建失败',
            description: '工单 新增白名单不能超过15个',
          })
          return
        }
      }

      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          createOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/hybird-redis-safegroup', query: { id: response.Data.id } })
            }
            this.temp = response.Data
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
              })
            }
          })
        }
      })
    },
    safeApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 4
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less"></style>
