<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item label="目标机器" name="hosts">
              <a-textarea
                v-if="temp.node !== 0"
                readOnly
                v-model:value="temp.content.hosts"
                :auto-size="{ minRows: 2, maxRows: 5 }"
              />
              <div v-if="temp.node === 0">
                <tx-button type="primary" @click="chooseAsset">选择目标机器</tx-button>
                <span style="margin-left: 8px">
                  <template v-if="hasSelected">
                    {{ `已选数量： ${selectedData.length}` }}
                  </template>
                </span>
              </div>
            </a-form-model-item>
            <a-form-model-item label="是否Ubuntu" name="isUbuntu">
              <a-radio-group v-model:value="temp.content.isUbuntu" button-style="solid" @change="changeTaskNameList">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="任务名称" name="task_id">
              <a-select
                v-model:value="temp.content.task_id"
                placeholder="请选择任务名称"
                :options="taskNameList"
                allowClear
                mode="multiple"
              ></a-select>
            </a-form-model-item>
            <a-form-model-item name="email" label="通知人">
              <a-input placeholder="<EMAIL>" v-model:value="temp.content.email" />
            </a-form-model-item>
            <a-form-model-item name="comment" label="申请理由">
              <a-textarea v-model:value="temp.content.comment" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24" style="text-align: center">
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) && !temp.handlerEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
              <tx-button type="dashed" @click="approveDataManual" style="margin-left: 10px">已处理</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <a-modal :zIndex="1049" :visible="chooseAssetVisible" width="80%" title="选择服务器" :closable="false">
        <div>
          <a-form layout="inline">
            <a-row :gutter="12">
              <a-col :md="8" :sm="24">
                <a-form-item label="模糊查询">
                  <a-input
                    v-model:value="assetListQuery.searchKey"
                    placeholder="服务器名/IP/公网IP/物理机IP/UUID"
                    style="width: 250px"
                    @pressEnter="$refs.assetTable.refresh()"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="IDC">
                  <a-select
                    v-model:value="assetListQuery.idc"
                    style="width: 250px"
                    placeholder="请选择"
                    :options="assetIdcList"
                    allowClear
                    showSearch
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item>
                  <tx-button type="primary" @click="$refs.assetTable.refresh(true)">查询</tx-button>
                  <tx-button
                    type="primary"
                    :disabled="!hasSelected"
                    :loading="loading"
                    @click="start"
                    style="margin-left: 20px"
                  >
                    清空已选
                  </tx-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="已选择ip：">
              <a-tag v-for="(tag, index) in selectedData" :key="index" color="blue" size="small" class="tag-item">
                {{ `${tag.ip}:${tag.uuid}` }}
              </a-tag>
            </a-form-item>
          </a-form>
          <s-table
            ref="assetTable"
            :rowKey="record => record.uuid"
            :columns="assetColumns"
            :data="loadAssetData"
            :rowSelection="rowSelection"
            height="350"
          ></s-table>
        </div>
        <template #footer>
          <tx-button key="submit" type="primary" @click="submitChooseAsset">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { judgeEnv } from '@/utils/util'
import { getAssetListIdc, getAssetList } from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'
import { getUserList } from '@/api/permission/user'

const assetColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true
  },
  {
    title: 'UUID',
    dataIndex: 'uuid'
  },
  {
    title: '服务器名',
    dataIndex: 'hostname'
  },
  {
    title: '机房',
    dataIndex: 'idc'
  },
  {
    title: 'ip地址',
    dataIndex: 'ip'
  },
  {
    title: '业务标签',
    dataIndex: 'business'
  },
  {
    title: '应用标签',
    dataIndex: 'application'
  }
]

const nameMap = {
  install_java: 'java',
  install_openresty: 'openresty',
  install_webshell: 'webshell',
  install_webshell_ubuntu: 'webshell',
  install_rsync: 'rsync',
  install_rsync_ubuntu: 'rsync',
  install_tomcat: 'tomcat',
  install_php_aws: 'php'
}

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'ServerAppDeploy',
  components: {
    STable
  },
  provide () {
    return {
      reload: this.reload
    }
  },
  data: function() {
    this.assetColumns = assetColumns
    this.pagination = pagination
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '服务器应用部署',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: [],
          task_id: [],
          hosts: '',
          email: store.getters.email,
          isUbuntu: false,
          reason: ''
        },
        timeline: [],
        comment: ''
      },
      rules: antdFormRulesFormat({
        'content.task_id': [{ required: true, message: '请选择任务', trigger: 'change' }],
        'content.email': [{ required: true, message: '请填写通知人', trigger: 'blur' }],
        'content.isUbuntu': [{ required: true, message: '请选择是否为Ubuntu', trigger: 'blur' }],
        'content.comment': [{ required: true, message: '请填写申请理由', trigger: 'blur' }]
      }),
      loading: false,
      containerReload: true,
      assetIdcList: [],
      taskNameList: [],
      selectedRowKeys: [],
      selectedRows: [],
      selectedData: [],
      currentPageKeys: [],
      chooseAssetVisible: false,
      totalAsset: 0,
      assetListQuery: {},
      arrIdLabelMap: {},
      hasAdminRole: false,
      loadAssetData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.assetListQuery)
        // 仅查询运行中的机器
        requestParameters.status = 1
        return getAssetList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            const rsp = {
              data: res.Data.data || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage
            }
            this.currentPageKeys = []
            for (const record of rsp.data) {
              this.currentPageKeys.push(record.uuid)
            }
            const selectedRowKeys = []
            for (const record of this.selectedData) {
              if (this.currentPageKeys.includes(record.uuid)) {
                selectedRowKeys.push(record.uuid)
              }
            }
            this.selectedRowKeys = selectedRowKeys
            return rsp
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      }
    }
  },
  mounted () {
    this.getTaskNameList()
    this.getAssetListIdc()
    this.getInfo()
    this.checkAdminRole()
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    },
    hasSelected () {
      return this.selectedData.length > 0
    }
  },
  methods: {
    async reload () {
      this.containerReload = false
      await this.$nextTick()
      this.containerReload = true
    },
    start () {
      this.loading = true
      // ajax request after empty completing
      setTimeout(() => {
        this.loading = false
        this.selectedRowKeys = []
        this.selectedData = []
      }, 500)
    },
    checkAdminRole () {
      getUserList({ searchText: store.getters.email }).then(response => {
        const roles = response.Data.data[0].roles
        if (roles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    getInfo () {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.handleGetInfo(response.Data)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    handleGetInfo (data) {
      data.content = JSON.parse(data.content['data'])
      delete data.content['data']
      if (data.content.hosts) {
        data.content.hosts = data.content.hosts.join(',')
      }
      this.temp = data
      this.getTaskNameList()
    },
    handleSubmitInfo (requestParameters) {
      requestParameters.handler = store.getters.name
      requestParameters.handlerEmail = store.getters.email
      requestParameters.content.hosts = requestParameters.content.hosts
        ? requestParameters.content.hosts.split(',')
        : null
      requestParameters.content = { data: JSON.stringify(requestParameters.content) }
      return requestParameters
    },
    getTaskNameList () {
      // 作业平台-任务配置中心-对应任务的 id
      const env = judgeEnv()
      if (!this.temp.content.isUbuntu) {
        if (env === 'ONLINE') {
          this.taskNameList = [
            { value: 43, label: nameMap['install_rsync'] },
            { value: 44, label: nameMap['install_java'] },
            { value: 45, label: nameMap['install_openresty'] },
            { value: 46, label: nameMap['install_webshell'] },
            { value: 47, label: nameMap['install_tomcat'] },
            { value: 48, label: nameMap['install_php_aws'] }
          ]
          this.arrIdLabelMap[43] = 'install_rsync'
          this.arrIdLabelMap[44] = 'install_java'
          this.arrIdLabelMap[45] = 'install_openresty'
          this.arrIdLabelMap[46] = 'install_webshell'
          this.arrIdLabelMap[47] = 'install_tomcat'
          this.arrIdLabelMap[48] = 'install_php_aws'
        } else if (env === 'TEST') {
          this.taskNameList = [
            { value: 31, label: nameMap['install_rsync'] },
            { value: 34, label: nameMap['install_java'] },
            { value: 33, label: nameMap['install_openresty'] },
            { value: 32, label: nameMap['install_webshell'] },
            { value: 35, label: nameMap['install_tomcat'] },
            { value: 36, label: nameMap['install_php_aws'] }
          ]
          this.arrIdLabelMap[31] = 'install_rsync'
          this.arrIdLabelMap[34] = 'install_java'
          this.arrIdLabelMap[33] = 'install_openresty'
          this.arrIdLabelMap[32] = 'install_webshell'
          this.arrIdLabelMap[35] = 'install_tomcat'
          this.arrIdLabelMap[36] = 'install_php_aws'
        } else if (env === 'DEV') {
          this.taskNameList = [
            { value: 32, label: nameMap['install_rsync'] },
            { value: 29, label: nameMap['install_java'] },
            { value: 30, label: nameMap['install_openresty'] },
            { value: 31, label: nameMap['install_webshell'] },
            { value: 33, label: nameMap['install_tomcat'] },
            { value: 34, label: nameMap['install_php_aws'] }
          ]
          this.arrIdLabelMap[32] = 'install_rsync'
          this.arrIdLabelMap[29] = 'install_java'
          this.arrIdLabelMap[30] = 'install_openresty'
          this.arrIdLabelMap[31] = 'install_webshell'
          this.arrIdLabelMap[33] = 'install_tomcat'
          this.arrIdLabelMap[34] = 'install_php_aws'
        }
      } else {
        if (env === 'ONLINE') {
          this.taskNameList = [
            { value: 106, label: nameMap['install_rsync_ubuntu'] },
            { value: 110, label: nameMap['install_webshell_ubuntu'] }
          ]
          this.arrIdLabelMap[106] = 'install_rsync_ubuntu'
          this.arrIdLabelMap[110] = 'install_webshell_ubuntu'
        } else if (env === 'TEST') {
          this.taskNameList = [
            { value: 51, label: nameMap['install_rsync_ubuntu'] },
            { value: 52, label: nameMap['install_webshell_ubuntu'] }
          ]
          this.arrIdLabelMap[51] = 'install_rsync_ubuntu'
          this.arrIdLabelMap[52] = 'install_webshell_ubuntu'
        } else if (env === 'DEV') {
          this.taskNameList = [
            { value: 40, label: nameMap['install_rsync_ubuntu'] },
            { value: 41, label: nameMap['install_webshell_ubuntu'] }
          ]
          this.arrIdLabelMap[40] = 'install_rsync_ubuntu'
          this.arrIdLabelMap[41] = 'install_webshell_ubuntu'
        }
      }
    },
    changeTaskNameList () {
      this.getTaskNameList()
      this.temp.content.task_id = []
    },
    getAssetListIdc () {
      getAssetListIdc().then(res => {
        if (!res.Data.idc) {
          return
        }
        for (var i = 0, len = res.Data.idc.length; i < len; i++) {
          var idc = {}
          idc.value = res.Data.idc[i]
          idc.label = res.Data.idc[i]
          this.assetIdcList.push(idc)
        }
      })
    },
    submitChooseAsset () {
      this.chooseAssetVisible = false
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      if (this.selectedData.length > 0) {
        this.selectedData = this.MergeArray(this.selectedData, selectedRows)
      } else {
        this.selectedData = selectedRows
      }
    },
    MergeArray (arr1, arr2) {
      var _arr = []
      for (var i = 0; i < arr1.length; i++) {
        _arr.push(arr1[i])
      }
      for (var x = 0; x < arr2.length; x++) {
        var flag = true
        for (var j = 0; j < arr1.length; j++) {
          if (arr2[x].uuid === arr1[j].uuid) {
            flag = false
            break
          }
        }
        if (flag) {
          _arr.push(arr2[x])
        }
      }
      _arr = this.spliceArray(_arr, this.selectedRowKeys)
      return _arr
    },
    // 删除未选择数据
    spliceArray (selectedData, selectedRowKeys) {
      const notChooseKeys = []
      for (const key of this.currentPageKeys) {
        if (!selectedRowKeys.includes(key)) {
          notChooseKeys.push(key)
        }
      }
      const _arr = []
      for (let x = 0; x < selectedData.length; x++) {
        let needDeleted = false
        for (let j = 0; j < notChooseKeys.length; j++) {
          if (selectedData[x].uuid === notChooseKeys[j]) {
            needDeleted = true
            break
          }
        }
        if (!needDeleted) {
          _arr.push(selectedData[x])
        }
      }
      return _arr
    },
    chooseAsset () {
      this.chooseAssetVisible = true
    },
    createData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.selectedData.length === 0) {
            alert('请选择目标主机')
            return
          }
          this.Date()
          this.node_status = 0
          const requestParameters = cloneDeep(this.temp)
          requestParameters.node = 1
          requestParameters.status = 1
          requestParameters.timeline = [
            this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now
          ]
          var hosts = []
          this.selectedData.forEach(item => {
            hosts.push(item.ip + ':' + item.uuid)
          })
          requestParameters.content.hosts = hosts.length > 0 ? hosts : null
          requestParameters.content.task_id = this.temp.content.task_id
          this.temp.content.task_id.forEach(item => {
            requestParameters.content.name.push(this.arrIdLabelMap[item])
          })
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          createOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功'
              })
              this.handleGetInfo(response.Data)
              this.node_status = 1
              this.$router.push({ path: '/workflow/server-app-deploy', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 2
          requestParameters.status = 1
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功'
              })
            }
          })
        }
      })
    },
    approveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 10
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              this.node_status = 1
              this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
            }
          })
        }
      })
    },
    approveDataManual () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters.content.manual = true
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 10
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              this.node_status = 1
              this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
            }
          })
        }
      })
    },
    rejectData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 20
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.handleGetInfo(response.Data)
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝'
              })
            }
          })
        }
      })
    },
    revokeData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 30
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.handleGetInfo(response.Data)
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回'
              })
            }
          })
        }
      })
    },
    Date () {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds()
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    }
  }
}
</script>

<style scoped></style>
