<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        :model="temp.content"
        ref="orderForm"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="ID" name="id">
              <a-input v-model:value="temp.content.id" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="项目" name="project">
              <a-select
                v-model:value="temp.content.project"
                :options="projects"
                :showSearch="true"
                :allowClear="true"
                placeholder="请输入项目名称并选择"
              ></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="供应商" name="supplier">
              <a-input v-model:value="temp.content.supplier" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="负责人" name="user">
              <a-select
                placeholder="请输入邮箱选择"
                v-model:value="temp.content.user"
                :showSearch="true"
                :allowClear="true"
                @search="searchUserEmailMethod"
                @select="getUserTeamListMethod"
              >
                <a-select-option v-for="email in userEmailList" :key="email" :value="email">
                  {{ email }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="资源ID" name="resourceId">
              <a-input v-model:value="temp.content.resourceId" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="业务" name="business">
              <a-select v-model:value="temp.content.business" @change="changeCostAttr">
                <a-select-option v-for="business in businessList" :key="business" :value="business">
                  {{ business }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="模块" name="module">
              <a-select v-model:value="temp.content.module">
                <a-select-option value="服务器">服务器</a-select-option>
                <a-select-option value="服务器(硬件托管)">服务器(硬件托管)</a-select-option>
                <a-select-option value="存储">存储</a-select-option>
                <a-select-option value="流量">流量</a-select-option>
                <a-select-option value="专线">专线</a-select-option>
                <a-select-option value="CDN">CDN</a-select-option>
                <a-select-option value="验证码短信">验证码短信</a-select-option>
                <a-select-option value="营销短信">营销短信</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="组织" name="organization">
              <a-cascader v-model:value="temp.content.organization" :options="organizationOptions" change-on-select />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="环境" name="env">
              <a-radio-group v-model:value="temp.content.env" button-style="solid">
                <a-radio-button value="dev">开发</a-radio-button>
                <a-radio-button value="test">测试</a-radio-button>
                <a-radio-button value="pre">预发布</a-radio-button>
                <a-radio-button value="uat">UAT</a-radio-button>
                <a-radio-button value="online">生产</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="费用类型" name="costAttr">
              <a-radio-group v-model:value="temp.content.costAttr" button-style="solid" disabled>
                <a-radio-button value="研发成本">研发成本</a-radio-button>
                <a-radio-button value="运营成本">运营成本</a-radio-button>
                <a-radio-button value="开发支出">开发支出</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="资源情况" name="resourceStatus">
              <a-radio-group v-model:value="temp.content.resourceStatus" button-style="solid">
                <a-radio-button value="using">使用中</a-radio-button>
                <a-radio-button value="idle">闲置</a-radio-button>
                <a-radio-button value="unknown">未知</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="自定义标签" name="tag">
              <a-input v-model:value="temp.content.tag" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col
            :span="12"
            v-if="
              (temp.content.costAttr === '运营成本' &&
              temp.content.business !== '大数据' &&
              temp.content.business !== '搜索(DG)') || temp.content.costAttr === '开发支出'
            "
          >
            <a-form-model-item label="产品线" name="productLineList">
              <a-form name="productLineList_item" :model="temp.content.productLineList">
                <a-row
                  :gutter="24"
                  v-for="(productLine, index) in temp.content.productLineList"
                  v-bind="formItemLayoutWithOutLabel"
                >
                  <a-col :span="12">
                    <a-form-item name="form.productLineList[index].productLineList">
                      <a-cascader
                        v-model:value="productLine.productLineNameList"
                        :options="productLineOption"
                        change-on-select
                        placeholder="请选择产品线"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      name="form.productLineList[index].ratio"
                      :rules="[{ validator: (_, value, callback) => this.validateNumberSum(value, index, callback) }]"
                    >
                      <a-input-number
                        v-model:value="productLine.ratio"
                        :precision="4"
                        placeholder="比例"
                        :min="0"
                        :max="1"
                        :step="0.0001"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item>
                      <tx-button
                        v-if="index === 0"
                        type="primary"
                        icon="plus"
                        style="width: 30px"
                        size="small"
                        @click="addRow"
                      ></tx-button>
                      <tx-button
                        v-else
                        type="dashed"
                        size="small"
                        icon="minus"
                        style="width: 30px"
                        @click="removeRow(index)"
                      ></tx-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="resourceSplit">
              <template #label>
                资源拆分
                <a-tooltip placement="right">
                  <template #title>
                    <span>
                      例：[{"business":"ACG","secondaryOrganization":"ACG智能创新","tertiaryOrganization":"产品设计","team":"产品设计","user":"<EMAIL>","ratio":0.5},{"business":"AIM","secondaryOrganization":"AIM","tertiaryOrganization":"平台架构","team":"平台架构","user":"<EMAIL>","ratio":0.5}]
                    </span>
                  </template>
                  <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle" />
                </a-tooltip>
              </template>
              <a-input v-model:value="temp.content.resourceSplit" readOnly />
              <a-button type="primary" style="margin-top: 5px" @click="this.resourceSplitVisible = true">
                设置资源拆分
              </a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="是否DG数据资产" name="isDgDataAsset">
              <a-radio-group v-model:value="temp.content.isDgDataAsset" button-style="solid" @change="changeCostAttr">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item
              label="DG数据资产类别"
              name="dgDataAssetClassification"
              v-if="temp.content.isDgDataAsset"
            >
              <a-select
                v-model:value="temp.content.dgDataAssetClassification"
                placeholder="请选择DG数据资产类别"
                @change="handleDGSpecificBusiness"
              >
                <a-select-option value="基础数据">基础数据</a-select-option>
                <a-select-option value="知识数据">知识数据</a-select-option>
                <a-select-option value="组件数据">组件数据</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="DG数据资产业务方" name="dgSpecificBusiness" v-if="temp.content.isDgBusiness">
              <a-select
                v-model:value="temp.content.dgSpecificBusiness"
                placeholder="请选择DG数据资产具体业务方"
                allowClear
              >
                <a-select-option value="AUTOM">AUTOM</a-select-option>
                <a-select-option value="CC">CC</a-select-option>
                <a-select-option value="CS">CS</a-select-option>
                <a-select-option value="DGB">DGB</a-select-option>
                <a-select-option value="DGC">DGC</a-select-option>
                <a-select-option value="DGG">DGG</a-select-option>
                <a-select-option value="SSG">SSG</a-select-option>
                <a-select-option value="公用">公用</a-select-option>
                <a-select-option value="数据中心">数据中心</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <!--            <a-form-model-item label="重要信息">-->
            <!--              <div style="font-weight: bold">账单标签变更申请通过之后，将留档并于2024年生效！请知悉。</div>-->
            <!--            </a-form-model-item>-->
            <a-form-model-item label="申请理由" name="reason">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/asset-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <!--        <a-form-model-item v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 2 && node_status === 1" :wrapper-col="{ span: 16, offset: 4 }" >-->
        <!--          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>-->
        <!--        </a-form-model-item>-->
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal
      title="资源拆分"
      :visible="resourceSplitVisible"
      @ok="handleResourceSplitOk"
      @cancel="handleResourceSplitCancel"
      width="70%"
    >
      <dynamic-form
        ref="formResourceSplit"
        :wrapHeight="360"
        :dynamicValidateForm="temp.content.resourceSplitObj"
        :organizationOptions="organizationOptions"
      />
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import BusinessSelect from '@/suite/SelectBusiness/index.vue'
import { notification } from 'ant-design-vue'
import { getAssetListProject, getAssetListTeam } from '@/api/asset'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getUserList } from '@/api/permission/user'
import { GetUserOrgAllInfo } from '@/api/asset'
import { getCostLabelInfo, getProductLineList } from '@/api/cost/label'
import DynamicForm from '@/views/cost/DynamicForm.vue'

const organizationOptions = [
  {
    value: 'AIM',
    label: 'AIM',
    children: [
      {
        value: '运维',
        label: '运维',
      },
      {
        value: '安全与合规',
        label: '安全与合规',
      },
      {
        value: '数据处理',
        label: '数据处理',
      },
      {
        value: '大数据平台',
        label: '大数据平台',
      },
      {
        value: '企业信息化',
        label: '企业信息化',
      },
      {
        value: '图像算法',
        label: '图像算法',
      },
      {
        value: '机器视觉算法',
        label: '机器视觉算法',
      },
      {
        value: '自然语言算法',
        label: '自然语言算法',
      },
      {
        value: '平台架构',
        label: '平台架构',
      },
    ],
  },
  {
    value: 'CC',
    label: 'CC',
    children: [
      {
        value: '设计',
        label: '设计',
      },
      {
        value: '移动端',
        label: '移动端',
      },
      {
        value: 'Web',
        label: 'Web',
      },
      {
        value: '测试',
        label: '测试',
      },
      {
        value: '后端',
        label: '后端',
      },
      {
        value: '产品',
        label: '产品',
      },
      {
        value: '运营',
        label: '运营',
      },
    ],
  },
  {
    value: 'CS',
    label: 'CS',
    children: [
      {
        value: '产品',
        label: '产品',
      },
      {
        value: '商务',
        label: '商务',
      },
      {
        value: '流量商业化',
        label: '流量商业化',
      },
      {
        value: '数据分析',
        label: '数据分析',
      },
      {
        value: '技术',
        label: '技术',
      },
      {
        value: '运营增长',
        label: '运营增长',
      },
      {
        value: '作业项目',
        label: '作业项目',
      },
      {
        value: '体验研究',
        label: '体验研究',
      },
    ],
  },
  {
    value: 'DG',
    label: 'DG',
    children: [
      {
        value: '数据平台',
        label: '数据平台',
      },
      {
        value: '数据技术',
        label: '数据技术',
      },
      {
        value: 'C端业务',
        label: 'C端业务',
      },
      {
        value: 'DGB技术',
        label: 'DGB技术',
      },
      {
        value: 'DGB业务',
        label: 'DGB业务',
      },
      {
        value: 'DGG',
        label: 'DGG',
      },
      {
        value: '知识数据及服务',
        label: '知识数据及服务',
      },
    ],
  },
  {
    value: 'SSG',
    label: 'SSG',
    children: [
      {
        value: '解决方案',
        label: '解决方案',
      },
      {
        value: '项目研发',
        label: '项目研发',
      },
    ],
  },
  {
    value: '战略合作',
    label: '战略合作',
    children: [
      {
        value: '产研',
        label: '产研',
      },
      {
        value: '创新项目组',
        label: '创新项目组',
      },
    ],
  },
  {
    value: 'ACG智能创新',
    label: 'ACG智能创新',
    children: [
      {
        value: '产品设计',
        label: '产品设计',
      },
      {
        value: '算法研发',
        label: '算法研发',
      },
      {
        value: '工程与技术',
        label: '工程与技术',
      },
    ],
  },
]

const operation = [
  'CS-CC',
  'CS',
  'DG',
  'SSG',
  '搜索(DG)',
  '大数据',
  '市场',
  '招聘项目组',
  '营销云',
  '战略合作',
  '总裁办',
]

export default {
  name: 'CostLabelChange',
  components: {
    DynamicForm,
    BusinessSelect,
  },
  data() {
    this.operation = operation
    return {
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      organizationOptions: [],
      businessList: [],
      userEmailList: [],
      projects: [],
      termGatewayInfo: [],
      teams: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      productLineOption: [],
      temp: {
        id: '',
        orderType: '账单标签变更',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          id: undefined,
          supplier: undefined,
          resourceId: undefined,
          business: undefined,
          organization: [],
          env: undefined,
          module: undefined,
          user: undefined,
          project: undefined,
          team: undefined,
          resourceStatus: undefined,
          comment: undefined,
          reason: undefined,
          resourceSplit: undefined,
          resourceSplitObj: undefined,
          tag: undefined,
          costAttr: undefined,
          productLineList: [],
          isDgDataAsset: false,
          isDgBusiness: false,
          dgDataAssetClassification: '',
          dgSpecificBusiness: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.business': [{ required: true, message: '请选择费用承担业务方', trigger: 'blur' }],
        'content.organization': [{ required: true, message: '请选择事业部/部门', trigger: 'blur' }],
        'content.env': [{ required: true, message: '请选择环境', trigger: 'blur' }],
        'content.user': [{ required: true, message: '请填写费用负责人', trigger: 'blur' }],
        'content.project': [{ required: true, message: '请填写项目', trigger: 'blur' }],
        'content.team': [{ required: true, message: '请填写团队', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        'content.module': [{ required: true, message: '请选择模块', trigger: 'blur' }],
        'content.resourceStatus': [{ required: true, message: '请选择资源状态', trigger: 'change' }],
        'content.productLineList': [{ required: true, message: '请选择产品线', trigger: 'change' }],
        'content.isDgDataAsset': [{ required: true, message: '请选择是否为DG数据资产', trigger: 'blur' }],
        'content.dgDataAssetClassification': [{ required: true, message: '请选择DG数据资产类别', trigger: 'blur' }],
      }),
      resourceSplitVisible: false,
      scrollPage: 1,
      valueData: '',
      treePageSize: 50,
    }
  },
  created() {
    this.getInfo()
    this.getBaseInfoList()
    this.getProductLineList()
  },
  methods: {
    getProductLineList() {
      getProductLineList().then(response => {
        if (response.Data !== undefined && response.Data !== null) {
          for (const productLine in response.Data.productLineMap) {
            if (Object.hasOwnProperty.call(response.Data.productLineMap, productLine)) {
              const secondaryProductLineList = response.Data.productLineMap[productLine].secondaryProductLine
              let childrenList = []
              for (let i = 0; i < secondaryProductLineList.length; i++) {
                let tmp = {
                  label: secondaryProductLineList[i],
                  value: secondaryProductLineList[i],
                }
                childrenList.push(tmp)
              }
              this.productLineOption.push({
                label: productLine,
                value: productLine,
                children: childrenList,
              })
            }
          }
        }
      })
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.costLabelId) {
        getCostLabelInfo(this.$route.query.costLabelId).then(response => {
          this.temp.content.id = response.Data.id + ''
          this.temp.content.supplier = response.Data.supplier
          this.temp.content.resourceId = response.Data.resourceId
          this.temp.content.business = response.Data.business
          this.temp.content.organization = [
            response.Data.secondaryOrganization,
            response.Data.tertiaryOrganization,
            response.Data.team,
          ]
          this.temp.content.user = response.Data.user
          this.temp.content.module = response.Data.module
          this.temp.content.project = response.Data.project
          this.temp.content.team = response.Data.team
          this.temp.content.env = response.Data.env
          this.temp.content.costAttr = response.Data.costAttr
          this.temp.content.resourceStatus = response.Data.resourceStatus
          this.temp.content.resourceSplit = response.Data.resourceSplit
          this.temp.content.tag = response.Data.tag
          this.temp.content.originCostLabel = JSON.stringify(response.Data)
          this.temp.content.comment = response.Data.comment
          let productLineListDisplay = []
          if (response.Data.productLine !== null && response.Data.productLine.length > 0) {
            let productLineLists = response.Data.productLine.split(',')
            for (let i = 0; i < productLineLists.length; i++) {
              let lineThroughIndex = productLineLists[i].indexOf('-')
              let colonIndex = productLineLists[i].indexOf(':')
              let primaryProductLine = productLineLists[i].slice(0, lineThroughIndex)
              if (colonIndex === -1) {
                productLineListDisplay.push({
                  productLineNameList: [primaryProductLine, productLineLists[i].slice(0, productLineLists[i].length)],
                  ratio: 1,
                })
              } else {
                productLineListDisplay.push({
                  productLineNameList: [primaryProductLine, productLineLists[i].slice(0, colonIndex)],
                  ratio: parseFloat(productLineLists[i].slice(colonIndex + 1, productLineLists[i].length)),
                })
              }
            }
          } else {
            productLineListDisplay.push({
              productLineNameList: [],
              ratio: 0,
            })
          }
          this.temp.content.productLineList = productLineListDisplay
          if (response.Data.dgDataAsset.length > 0) {
            const arr = response.Data.dgDataAsset.split('-')
            if (arr.length > 0) {
              this.temp.content.isDgDataAsset = arr[0] === '是'
            }
            if (arr.length > 1) {
              this.temp.content.dgDataAssetClassification = arr[1]
            }
            if (arr.length > 2) {
              this.temp.content.dgSpecificBusiness = arr[2]
            }
          }
          if (this.temp.content.business === 'DG') {
            this.temp.content.isDgBusiness = true
          }
          if (this.temp.content.resourceSplit.length > 0) {
            let tmp = JSON.parse(this.temp.content.resourceSplit)
            tmp.forEach(item => {
              item.organization = [item.secondaryOrganization, item.tertiaryOrganization]
            })
            this.temp.content.resourceSplitObj = {
              users: tmp,
            }
          } else {
            this.temp.content.resourceSplitObj = {
              users: [
                {
                  business: '',
                  organization: [],
                  secondaryOrganization: '',
                  tertiaryOrganization: '',
                  user: '',
                  team: '',
                  ratio: 0,
                },
              ],
            }
          }
        })
        console.log('111', this.temp.content)
        this.nodeStatus = 1
      } else if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            let productLineListDisplay = []
            if (response.Data.productLine !== null && response.Data.productLine.length > 0) {
              let productLineLists = response.Data.productLine.split(',')
              for (let i = 0; i < productLineLists.length; i++) {
                let lineThroughIndex = productLineLists[i].indexOf('-')
                let colonIndex = productLineLists[i].indexOf(':')
                let primaryProductLine = productLineLists[i].slice(0, lineThroughIndex)
                if (colonIndex === -1) {
                  productLineListDisplay.push({
                    productLineNameList: [primaryProductLine, productLineLists[i].slice(0, productLineLists[i].length)],
                    ratio: 1,
                  })
                } else {
                  productLineListDisplay.push({
                    productLineNameList: [primaryProductLine, productLineLists[i].slice(0, colonIndex)],
                    ratio: parseFloat(productLineLists[i].slice(colonIndex + 1, productLineLists[i].length)),
                  })
                }
              }
            } else {
              productLineListDisplay.push({
                productLineNameList: [],
                ratio: 0,
              })
            }
            this.temp.content.productLineList = productLineListDisplay
            if (response.Data.dgDataAsset.length > 0) {
              const arr = response.Data.dgDataAsset.split('-')
              if (arr.length > 0) {
                this.temp.content.isDgDataAsset = arr[0] === '是'
              }
              if (arr.length > 1) {
                this.temp.content.dgDataAssetClassification = arr[1]
              }
              if (arr.length > 2) {
                this.temp.content.dgSpecificBusiness = arr[2]
              }
            }
            if (this.temp.content.resourceSplit.length > 0) {
              let tmp = JSON.parse(this.temp.content.resourceSplit)
              tmp.forEach(item => {
                item.organization = [item.secondaryOrganization, item.tertiaryOrganization]
              })
              this.temp.content.resourceSplitObj = {
                users: tmp,
              }
            } else {
              this.temp.content.resourceSplitObj = {
                users: [
                  {
                    business: '',
                    organization: [],
                    secondaryOrganization: '',
                    tertiaryOrganization: '',
                    user: '',
                    team: '',
                    ratio: 0,
                  },
                ],
              }
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    getBaseInfoList() {
      getAssetListProject().then(res => {
        for (let i = 0, len = res.Data.project.length; i < len; i++) {
          let pro = {}
          pro.value = res.Data.project[i]
          pro.label = res.Data.project[i]
          this.projects.push(pro)
        }
      })
      getAssetListTeam().then(res => {
        for (let i = 0, len = res.Data.team.length; i < len; i++) {
          let label = {}
          label.value = res.Data.team[i]
          label.label = res.Data.team[i]
          this.teams.push(label)
        }
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/cost-label', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      }).catch(() => {})
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        let arry = response.Data.data
        for (let i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    getUserTeamListMethod(userEmail) {
      if (userEmail === '') {
        return
      }
      GetUserOrgAllInfo({ email: userEmail }).then(response => {
        this.organizationOptions = response.Data.orgCas
        this.temp.content.organization = []
        if (this.organizationOptions[0].label !== undefined && this.organizationOptions[0].label !== null) {
          this.temp.content.organization.push(this.organizationOptions[0].label)
          if (this.organizationOptions[0].children !== null && this.organizationOptions[0].children[0].label !== null) {
            this.temp.content.organization.push(this.organizationOptions[0].children[0].label)
            if (
              this.organizationOptions[0].children[0].children !== null &&
              this.organizationOptions[0].children[0].children[0].label !== null
            ) {
              this.temp.content.organization.push(this.organizationOptions[0].children[0].children[0].label)
            }
          }
        }
        this.businessList = response.Data.business
        if (this.businessList !== null) {
          this.temp.content.business = this.businessList[0]
          if (this.temp.content.business === 'DG') {
            this.temp.content.isDgBusiness = true
          }
        }
        if (this.operation.includes(this.temp.content.business)) {
          this.temp.content.costAttr = '运营成本'
          if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
            this.temp.content.costAttr = '开发支出'
          }
          // this.temp.content.productLineList = [{
          //   productLineNameList: [],
          //   ratio: 0
          // }]
        } else {
          this.temp.content.costAttr = '研发成本'
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    checkProductLine() {
      if (
        this.temp.content.costAttr === '研发成本' ||
        this.temp.content.business === '大数据' ||
        this.temp.content.business === '搜索(DG)'
      ) {
        return
      }
      if (this.temp.content.productLineList.length > 5) {
        throw new Error('目前最多支持5个产品线，如有需求请联系运维管理员')
      }
      let sumRatios = 0
      let productLineArr = []
      let existArr = []
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (
          this.temp.content.productLineList[i].productLineNameList === undefined ||
          this.temp.content.productLineList[i].productLineNameList.length < 2
        ) {
          throw new Error('请填充完所有产品线空格')
        }
        if (existArr.includes(this.temp.content.productLineList[i].productLineNameList[1])) {
          throw new Error('产品线重复：' + this.temp.content.productLineList[i].productLineNameList[1])
        }
        productLineArr.push(
          this.temp.content.productLineList[i].productLineNameList[1] + ':' + this.temp.content.productLineList[i].ratio
        )
        sumRatios += this.temp.content.productLineList[i].ratio
        existArr.push(this.temp.content.productLineList[i].productLineNameList[1])
      }
      sumRatios = sumRatios.toFixed(4)
      if (sumRatios !== '1.0000') {
        const errStr = '比例之和： ' + sumRatios + '≠ 1'
        throw new Error(errStr)
      }
      this.temp.content.productLine = productLineArr.join(',')
      console.log(this.temp.content)
    },
    addRow() {
      this.temp.content.productLineList.push({
        productLineNameList: [],
        ratio: 0,
      })
    },
    // 移除某行
    removeRow(i) {
      if (this.temp.content.productLineList.length > 1) {
        this.temp.content.productLineList.splice(i, 1)
      }
    },
    handleResourceSplitOk() {
      let sumRatios = 0
      for (let i = 0; i < this.temp.content.resourceSplitObj.users.length; i++) {
        if (
          this.temp.content.resourceSplitObj.users[i].organization === undefined ||
          this.temp.content.resourceSplitObj.users[i].organization.length < 2
        ) {
          this.$message.error('请填充完所有空格')
          return
        }
        this.temp.content.resourceSplitObj.users[i].secondaryOrganization =
          this.temp.content.resourceSplitObj.users[i].organization[0]
        this.temp.content.resourceSplitObj.users[i].tertiaryOrganization =
          this.temp.content.resourceSplitObj.users[i].organization[1]
        this.temp.content.resourceSplitObj.users[i].team =
          this.temp.content.resourceSplitObj.users[i].organization[
            this.temp.content.resourceSplitObj.users[i].organization.length - 1
          ]
        if (
          this.temp.content.resourceSplitObj.users[i].business.length === 0 ||
          this.temp.content.resourceSplitObj.users[i].secondaryOrganization.length === 0 ||
          this.temp.content.resourceSplitObj.users[i].tertiaryOrganization.length === 0 ||
          this.temp.content.resourceSplitObj.users[i].team.length === 0 ||
          this.temp.content.resourceSplitObj.users[i].user.length === 0
        ) {
          this.$message.error('请填充完所有空格')
          return
        }
        sumRatios += this.temp.content.resourceSplitObj.users[i].ratio
      }
      sumRatios = sumRatios.toFixed(4)
      if (sumRatios !== '1.0000') {
        const errStr = '分摊比例之和： ' + sumRatios + '≠ 1'
        this.$message.error(errStr)
        return
      }
      try {
        this.temp.content.resourceSplit = JSON.stringify(this.temp.content.resourceSplitObj.users)
        this.resourceSplitVisible = false
      } catch (e) {
        this.$message.error('格式化错误')
      }
    },
    handleResourceSplitCancel() {
      this.resourceSplitVisible = false
    },
    changeCostAttr() {
      const operation = [
        'CS-CC',
        'CS',
        'DG',
        'SSG',
        '搜索(DG)',
        '大数据',
        '市场',
        '招聘项目组',
        '营销云',
        '战略合作',
        '总裁办',
      ]
      if (operation.includes(this.temp.content.business)) {
        this.temp.content.costAttr = '运营成本'
        if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
          this.temp.content.costAttr = '开发支出'
        }
      } else {
        this.temp.content.costAttr = '研发成本'
      }
    },
    handleDGSpecificBusiness() {
      if (this.temp.content.dgDataAssetClassification !== '') {
        this.temp.content.dgSpecificBusiness = '数据中心'
      }
    },
    validateNumberSum(_, currentIndex, callback) {
      callback = antdFormValidateCallback
      let total = 1
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (i === currentIndex) {
          continue
        }
        total -= this.temp.content.productLineList[i].ratio
      }
      if ((total - this.temp.content.productLineList[currentIndex].ratio).toFixed(4) < 0) {
        this.temp.content.productLineList[currentIndex].ratio = 0
        const totalFixed = total.toFixed(4)
        return callback(new Error('不得超过' + totalFixed))
      }
      return callback()
    }
  },
}
</script>

<style scoped></style>
