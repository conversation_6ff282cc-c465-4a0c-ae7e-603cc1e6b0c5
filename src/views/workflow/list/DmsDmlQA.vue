<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="QA审批" />
        <a-step title="安全管理员审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="DML信息" name="mysqls">
          <tx-button v-if="temp.status === 0" type="primary" @click="addRow">新增</tx-button>
          <a-table
            style="margin-top: 18px"
            bordered
            :pagination="pagination"
            :scroll="scrolls"
            :columns="tablecolumns"
            :data-source="temp.content.mysqls"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'ip'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-select
                      v-model:value="record.ip"
                      mode="multiple"
                      placeholder="请搜索并选择数据库, 正则检索加前缀: Rex-"
                      :showSearch="true"
                      :allowClear="true"
                      :filter-option="false"
                      @search="searchmysqlIpMethod"
                      @change="mysqlIpChange"
                    >
                      <a-select-option v-for="item1 in IpDbNamesList" :key="item1" :value="item1">
                        {{ item1 }}
                      </a-select-option>
                      <template #dropdownRender="menu">
                        <v-nodes :vnodes="menu" />
                        <a-divider style="margin: 4px 0" />
                        <div style="padding: 4px 8px; cursor: pointer" @mousedown="e => e.preventDefault()">
                          <tx-button type="link" @click="selectAll(record)">全选</tx-button>
                          <tx-button type="link" @click="clearAll(record)">清空</tx-button>
                        </div>
                      </template>
                    </a-select>
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-else-if="column.dataIndex == 'scope'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-select placeholder="" v-model:value="record.scope" style="width: 80px" :allowClear="true">
                      <a-select-option v-for="item1 in mysqlIpDbNamesList" :key="item1" :value="item1">
                        {{ item1 }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-else-if="column.dataIndex == 'clowneUploadMode'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-switch
                      v-if="temp.node === 0"
                      checked-children="文件"
                      un-checked-children="文本"
                      @change="checked => sqlUploadModeSwitch(checked, record)"
                      v-model:checked="record.clowneUploadMode"
                    />
                    <a-switch
                      v-else
                      disabled
                      checked-children="文件"
                      un-checked-children="文本"
                      v-model:checked="record.clowneUploadMode"
                    />
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-else-if="column.dataIndex == 'sqlfilename'">
                <a-form-model v-if="record.clowneUploadMode" :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-upload
                      v-if="record.sqlfilename === ''"
                      name="personfile"
                      listType="text"
                      accept=".sql"
                      :beforeUpload="beforeUpload"
                      :file-list="uploadFileList"
                      @change="uploadChange(record)"
                    >
                      <tx-button type="link">
                        <a-icon type="upload" />
                        文件上传
                      </tx-button>
                    </a-upload>
                    <tx-button v-if="record.sqlfilename !== ''" type="link" @click="editRowFiles(record)">
                      <a-icon type="paper-clip" />
                      {{ record.sqlfilename }}
                    </tx-button>
                    <a-popconfirm title="确定删除？" @confirm="deleteRowFiles(record)">
                      <tx-button
                        v-if="record.sqlfilename !== ''"
                        type="link"
                        icon="delete"
                        style="color: rgba(252, 85, 49, 1)"
                      ></tx-button>
                    </a-popconfirm>
                  </a-form-model-item>
                </a-form-model>
                <a-form-model v-else :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <tx-button v-if="record.sqlfilename === '查看SQL内容'" type="link" @click="editRowFiles(record)">
                      查看SQL内容
                    </tx-button>
                    <tx-button v-else type="link" icon="upload" @click="textUpload(record)">文本上传</tx-button>
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-else-if="column.dataIndex == 'status'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-badge :status="statusTypeFilter(record.status)" :text="statusFilter(record.status)" />
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-else-if="column.dataIndex == 'sqlfileRunInfo'">
                <a-form-model :ref="record.index" :model="record">
                  <tx-button type="link" @click="openSqlRunInfo(record, 'hash', '')">查看详情</tx-button>
                </a-form-model>
              </template>
              <template v-else-if="column.dataIndex == 'action'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <tx-button type="link" @click="deleteRow(record)">删除</tx-button>
                  </a-form-model-item>
                </a-form-model>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="业务标签" name="businessLabel" v-if="temp.node === 0">
          <a-select placeholder="" v-model:value="temp.content.businessLabel" style="width: 200px" :allowClear="true">
            <a-select-option v-for="item in businessLabelList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="需要安全组审批" v-if="temp.node !== 0">
          <a-radio-group v-model:value="temp.content.needSafeNode" :disabled="radioDisabled" button-style="solid">
            <a-radio-button value="true">需要</a-radio-button>
            <a-radio-button value="false">不需要</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item v-if="temp.content.needSafeNode === 'true'" label="字段等级详情">
          <a-table
            style="margin-top: 18px"
            bordered
            :pagination="pagination"
            :scroll="scrolls"
            :columns="levelcolumns"
            :data-source="temp.content.dbFiledLevels"
          ></a-table>
        </a-form-model-item>
        <a-form-model-item label="抄送人/表负责人:" name="sendUser">
          <a-select
            placeholder="<EMAIL>"
            v-model:value="temp.content.sendUser"
            style="width: 230px"
            mode="multiple"
            :showSearch="true"
            :allowClear="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="反馈信息">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" :loading="loading" @click="dbLevelAndCreate">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            (temp.node === 1 || temp.node === 2) &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <!-- 新增QA审批按钮 -->
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="qaApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safeapproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button v-if="temp.content.passwd === 'no'" type="primary" @click="dbaCheck">DBA校验</tx-button>
          <tx-button v-else type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal v-model:visible="modalTextUploadVision" title="SQL文本校验" :width="1000" centered>
      <template #footer>
        <tx-button key="back" @click="cancelSQLtextContent">关闭</tx-button>
        <tx-button key="submit" type="primary" @click="submitSQLtextContent">校验</tx-button>
      </template>
      <a-textarea
        v-model:value="textContent"
        placeholder="DML工单仅支持:  INSERT、UPDATE、DELETE 等SQL语句。"
        style="overflow: auto; overflow-x: hidden"
        :autosize="{ minRows: 20, maxRows: 20 }"
      />
    </a-modal>
    <a-modal v-model:visible="filesEditVision" title="SQL内容查看与编辑" :width="1000" centered>
      <template #footer>
        <tx-button key="back" @click="cancelEditfilesContent">关闭</tx-button>
        <tx-button key="submit" type="primary" @click="submitEditfilesContent">修改</tx-button>
      </template>
      <a-textarea
        v-model:value="filesContent"
        placeholder="Basic usage"
        style="overflow: auto; overflow-x: hidden"
        :autosize="{ minRows: 20, maxRows: 20 }"
      />
    </a-modal>
    <a-modal v-model:visible="sqlRunInfoVision" title="SQL执行详细情况" :width="1500" centered :footer="null">
      <a-form-model v-if="temp.node === 4" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="成功：">{{ successCount }} 条</a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="失败：">{{ errorCount }} 条</a-form-model-item>
          </a-col>
          <a-col :span="4" v-if="errorCount > 0 && userpRression">
            <tx-button type="primary" @click="allErrorRun">全部重试</tx-button>
          </a-col>
          <a-col :span="4" :push="8">
            <tx-button
              type="primary"
              @click="
                openSqlRunInfo(
                  { row: currentTableRow, sqlfileRunInfoFilename: currentTableSqlfileRunInfoFilename },
                  'hash',
                  'flush'
                )
              "
            >
              刷新执行结果
            </tx-button>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
        class="dataBaseTable"
        style="margin-top: 18px"
        :scroll="{ x: 1000, y: 600 }"
        bordered
        :pagination="paginationinfo"
        :columns="columnsInfo"
        :data-source="sqlRunInfotableList"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'Status'">
            <a-form-model :ref="record.index" :model="record">
              <a-form-model-item class="ant-form-item—table">
                <a-badge
                  v-if="record.Status == 1"
                  :status="statusTypeFilter(record.Status)"
                  :text="statusFilter(record.Status)"
                />
                <tx-button v-else-if="userpRression" type="danger" @click="sqlFailedRetry(record)">重试</tx-button>
                <a v-else-if="record.Status == 3">失败</a>
                <a v-else>执行中</a>
              </a-form-model-item>
            </a-form-model>
          </template>
        </template>
      </a-table>
    </a-modal>
    <a-modal
      title="请DBA输入校验密码"
      :visible="dbaCheckVisible"
      :confirm-loading="confirmLoading"
      :closable="false"
      @ok="dbaCheckSubmit"
      @cancel="dbaCheckCancel"
    >
      <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="密码">
          <a-input type="text" style="width: 300px; -webkit-text-security: disc" v-model:value="checkPaaswd" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getUserList } from '@/api/permission/user'
import { getDbNamesList } from '@/api/db/dbApproval'
import {
  allErrorRetry,
  dbPasswdCheck,
  dbSqlSingle,
  dblConfirm,
  dblDownloadload,
  dblUpload,
  dmlDataLevels,
} from '@/api/db/ddl'

const columns1 = [
  {
    title: '操作',
    dataIndex: 'action',
    width: '80px',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
  {
    title: '数据库',
    dataIndex: 'ip',
    width: '600px',
    scopedSlots: { customRender: 'ip' },
  },
  {
    title: '上传模式',
    dataIndex: 'clowneUploadMode',
    width: '100px',
    scopedSlots: { customRender: 'clowneUploadMode' },
  },
  {
    title: 'SQL上传',
    dataIndex: 'sqlfilename',
    width: '200px',
    scopedSlots: { customRender: 'sqlfilename' },
  },
  {
    title: '执行情况',
    dataIndex: 'sqlfileRunInfo',
    width: '120px',
    scopedSlots: { customRender: 'sqlfileRunInfo' },
  },
]
const columns3 = [
  {
    title: 'host',
    dataIndex: 'host',
  },
  {
    title: '数据库',
    dataIndex: 'dbName',
  },
  {
    title: '表名',
    dataIndex: 'tableName',
  },
  {
    title: '字段名',
    dataIndex: 'filed',
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
  },
  {
    title: '数据等级',
    dataIndex: 'dataLevel',
  },
  {
    title: '字段释义',
    dataIndex: 'filedComment',
  },
]
const statusMap = {
  3: {
    status: 'error',
    text: '执行错误',
  },
  2: {
    status: 'error',
    text: '未执行',
  },
  1: {
    status: 'success',
    text: '成功',
  },
}
export default {
  name: 'DMLQA',
  components: {
    VNodes: {
      // functional: true,
      render: ctx => {
        return ctx.$attrs.vnodes.menuNode
      },
    },
  },

  data: function () {
    this.tablecolumns1 = columns1
    this.tablecolumns3 = columns3
    return {
      columnsInfo: [
        {
          title: 'ID',
          dataIndex: 'Index',
          width: '80px',
          align: 'center',
          sorter: (a, b) => a.Index - b.Index,
          defaultSortOrder: 'ascend',
        },
        { title: '数据库', dataIndex: 'Ip', width: '200px', ellipsis: true },
        { title: '库名', dataIndex: 'DbName', width: '200px', ellipsis: true },
        { title: 'sql语句', dataIndex: 'SqlContent', ellipsis: true },
        {
          title: '执行情况',
          dataIndex: 'Status',
          width: '100px',
          scopedSlots: { customRender: 'Status' },
          filters: [
            {
              text: '成功',
              value: 1,
            },
            {
              text: '等待中',
              value: 2,
            },
          ],
          onFilter: (value, record) => record.Status === value,
        },
        { title: '报错信息', dataIndex: 'ErrInfo', width: '300px' },
      ],
      businessLabelList: [
        'CC-后端',
        'AIM-运维',
        'DG-数据技术',
        'ACG智能创新-工程与技术',
        'CS-技术',
        'AIM-平台架构',
        'AIM-智能前端',
        'AIM-企业信息化',
        'AIM-大数据平台',
        '战略合作-产研',
        'CS-作业项目',
        'SSG-项目研发',
        'DG-DGB技术',
        'CC-Web',
        'DG-知识数据及服务',
        'DG-数据平台',
        'DG-DGG',
      ],
      tablecolumns: [],
      sqlRunInfotableList: [],
      currentTableRow: undefined,
      currentTableSqlfileRunInfoFilename: undefined,
      levelcolumns: [],
      uploadFileList: [],
      filesEditVision: false,
      modalTextUploadVision: false,
      radioDisabled: true,
      successCount: undefined,
      errorCount: undefined,
      filesContent: undefined,
      textContent: undefined,
      editfileRow: undefined,
      sqlRunInfoVision: false,
      userpRression: false,
      scrolls: {},
      userEmailList: [],
      IpDbNamesList: [],
      mysqlIpDbNamesList: ['0-7', '0-15', '0-f', '0-99'],
      value: [],
      pagination: {
        defaultPageSize: 1000,
        hideOnSinglePage: true,
      },
      paginationinfo: {
        defaultPageSize: 20,
        hideOnSinglePage: true,
      },
      headers: {
        authorization: 'authorization-text',
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      cahngeRow: 0,
      checkPaaswd: '',
      loading: false,
      confirmLoading: false,
      dbaCheckVisible: false,
      temp: {
        id: '',
        orderType: 'DML申请(QA)',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          mysqls: [
            {
              row: 0,
              ip: [],
              scope: '',
              clowneUploadMode: false,
              sqlfilename: '上传',
              cachesqlfilename: '',
              sqlfileRunInfo: '详情查看',
              sqlfileRunInfoFilename: '',
            },
          ],
          sendUser: undefined,
          reason: '',
          passwd: 'no',
          businessLabel: 'CC',
          needSafeNode: 'false',
          dbFiledLevels: [],
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.mysqls': [{ required: true, message: '请选择授权数据库', trigger: 'blur' }],
        'content.dbUserName': [{ required: true, message: '请填写用户名', trigger: 'blur' }],
        'content.clientIps': [{ required: true, message: '请填写客户端服务器的ip', trigger: 'blur' }],
        'content.permissionValue': [{ required: true, message: '请选择用户分配权限', trigger: 'blur' }],
        'content.sendUser': [{ required: true, message: '请填写抄送人邮箱', trigger: 'blur' }],
        'content.businessLabel': [{ required: true, message: '请选择业务标签', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      dbList: [],
    }
  },
  created() {
    this.getInfo()
    this.tablecolumns = this.tablecolumns1
    this.levelcolumns = this.tablecolumns3
    this.mysqlIpChange()
    this.getUserRoles(store.getters.email)
    this.temp.content.businessLabel = noc.user.getUserInfo().org + '-' + noc.user.getUserInfo().dep
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        if (response.Data.data[0].roles.includes('dba_user')) {
          this.userpRression = true
        }
      })
    },
    allErrorRun() {
      let sendParmar = {
        filesName: this.temp.content.mysqls[this.currentTableRow].sqlfileRunInfoFilename,
        type: 'hash',
        orderId: this.temp.id,
        password: this.temp.content.passwd,
        errorCount: this.errorCount,
      }

      allErrorRetry(sendParmar).then(response => {
        if (response.Data.message === 'ok') {
          this.$message.success('重试中，请稍后查看')
        }
        this.sqlRunInfoVision = false
      })
    },
    sqlFailedRetry(record) {
      // this.temp.content.mysqls[this.currentTableRow].sqlfileRunInfoFilename
      dbSqlSingle({
        orderId: this.temp.id,
        taskId: record.ID,
        ip: record.Ip,
        dbName: record.DbName,
        sqlContent: record.SqlContent,
        redisKey: this.temp.content.mysqls[this.currentTableRow].sqlfileRunInfoFilename,
        password: this.temp.content.passwd,
      }).then(response => {
        if (response.Data.message === 'ok') {
          this.openSqlRunInfo(
            {
              sqlfileRunInfoFilename: this.temp.content.mysqls[this.currentTableRow].sqlfileRunInfoFilename,
              row: this.currentTableRow,
            },
            'hash',
            ''
          )
        }
      })
    },
    selectAll(record) {
      for (let i = 0; i < this.IpDbNamesList.length; i++) {
        this.temp.content.mysqls[record.row].ip.push(this.IpDbNamesList[i])
      }
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    clearAll(record) {
      this.temp.content.mysqls[record.row].ip = []
    },
    beforeUpload(file, record) {
      this.$message.warning('未提交工单, 上传sql文件仅保留1小时。', [5])
      console.log('上传前校验--文件类型', file)
      if (file.size > 1024 * 1024) {
        notification.error({
          message: 'SQL文件超过1M',
        })
        this.uploadFileList = []
        return false
      } else {
        let reader = new FileReader()
        reader.readAsText(file)
        reader.onload = e => {
          let fileName = 'dml--' + this.localUser + 'l--' + file.name + 'l--' + Date.parse(new Date())
          this.uploadFiles(fileName, e.target.result, file.name)
          this.uploadFileList = []
          return false
        }
        return false
      }
    },
    sqlUploadModeSwitch(value, record) {
      this.temp.content.mysqls[record.row].sqlfilename = ''
    },
    uploadChange(value) {
      this.cahngeRow = value.row
    },
    uploadFiles(fileName, value, sqlfilename) {
      dblUpload({ filesName: fileName, filesContent: value, runType: 'dml' }).then(response => {
        if (response.Data.message === 'ok') {
          this.temp.content.mysqls[this.cahngeRow].cachesqlfilename = fileName
          this.temp.content.mysqls[this.cahngeRow].sqlfileRunInfoFilename = fileName + 'runInfo'
          this.temp.content.mysqls[this.cahngeRow].sqlfilename = sqlfilename
          this.modalTextUploadVision = false
        } else if (response.Data.message === 'checkFailed') {
          notification.error({
            message: response.Data.errorInfo,
            duration: 5,
          })
        }
      })
    },
    async ddlfilesConfirm() {
      let data = []
      for (let i = 0; i < this.temp.content.mysqls.length; i++) {
        data.push(this.temp.content.mysqls[i].cachesqlfilename)
      }
      await dblConfirm({ fileNames: data }).then(response => {
        if (response.Data.message !== 'ok') {
          notification.error({
            message: 'sql文件有效期设置失败!',
          })
        } else {
          this.createData()
        }
      })
    },
    deleteRowFiles(record) {
      this.temp.content.mysqls[record.row].cachesqlfilename = ''
      this.temp.content.mysqls[record.row].sqlfileRunInfoFilename = ''
      this.temp.content.mysqls[record.row].sqlfilename = '上传'
    },
    editRowFiles(record) {
      this.editfileRow = record.row
      dblDownloadload({ filesName: record.cachesqlfilename }).then(response => {
        if (response.Data.filesStatus === 'exist') {
          this.filesEditVision = true
          this.filesContent = response.Data.filesContent
        } else {
          notification.error({
            message: 'sql文件不存在',
          })
        }
      })
    },
    openSqlRunInfo(record, type, flush) {
      let sendBody = {}
      sendBody.filesName = record.sqlfileRunInfoFilename
      if (type === 'hash') {
        sendBody.type = 'hash'
      }
      this.currentTableRow = record.row
      this.currentTableSqlfileRunInfoFilename = record.sqlfileRunInfoFilename
      dblDownloadload(sendBody).then(response => {
        if (response.Data.filesStatus === 'exist') {
          this.sqlRunInfoVision = true
          let filesJson = JSON.parse(response.Data.filesContent)
          let values = Object.values(filesJson)
          this.sqlRunInfotableList = []
          for (let i = 0, len = values.length; i < len; i++) {
            this.sqlRunInfotableList.push(JSON.parse(values[i]))
          }
          this.successCount = response.Data.successCount
          this.errorCount = response.Data.errorCount
        } else {
          notification.error({
            message: '执行结果不存在',
          })
        }
      })
      if (flush === 'flush') {
        notification.success({
          message: '刷新成功',
        })
      }
    },
    searchmysqlIpMethod(search) {
      this.IpDbNamesList = []
      if (search !== '') {
        getDbNamesList({ searchText: search }).then(response => {
          this.IpDbNamesList = response.Data.ipdbName
        })
      }
    },
    mysqlIpChange() {
      this.scrolls = { x: this.tablecolumns.length * 100 }
    },
    submitEditfilesContent() {
      if (this.temp.handlerEmail.includes(this.localUser) || this.temp.node === 0) {
        dblUpload({
          filesName: this.temp.content.mysqls[this.editfileRow].cachesqlfilename,
          filesContent: this.filesContent,
          modifyFlags: 'SQLmodify',
        }).then(response => {
          if (response.Data.message === 'ok') {
            notification.success({
              message: '文件修改成功',
            })
          }
        })
        this.filesEditVision = false
      } else {
        notification.error({
          message: '没有权限！',
        })
      }
    },
    cancelEditfilesContent() {
      this.filesEditVision = false
    },
    textUpload(record) {
      this.cahngeRow = record.row
      this.textContent = undefined
      this.modalTextUploadVision = true
    },
    submitSQLtextContent() {
      if (this.textContent !== '' && this.textContent !== undefined) {
        let fileName = 'dml--' + this.localUser + '--' + '查看SQL内容' + '--' + Date.parse(new Date())
        this.uploadFiles(fileName, this.textContent, '查看SQL内容')
      } else {
        notification.error({
          message: '内容为空!',
        })
      }
    },
    cancelSQLtextContent() {
      this.modalTextUploadVision = false
    },
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp.content = response.Data.content
            // 对sql内容做特殊化处理, 重新提交生成新key
            this.temp.content.passwd = 'no'
            for (let i = 0; i < this.temp.content.mysqls.length; i++) {
              // 获取历史sql内容
              this.repeateSubmitOrder(i)
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    async repeateSubmitOrder(i) {
      let tempSqlValue = ''
      await dblDownloadload({ filesName: this.temp.content.mysqls[i].cachesqlfilename }).then(response => {
        if (response.Data.filesStatus === 'exist') {
          tempSqlValue = response.Data.filesContent
        } else {
          notification.error({
            message: 'sql文件不存在',
          })
        }
      })
      this.temp.content.mysqls[i].cachesqlfilename =
        'dml--' + this.localUser + '--查看SQL内容--' + Date.parse(new Date()) + i
      this.temp.content.mysqls[i].sqlfileRunInfoFilename = this.temp.content.mysqls[i].cachesqlfilename + 'runInfo'
      // 上传sql内容
      await dblUpload({
        filesName: this.temp.content.mysqls[i].cachesqlfilename,
        filesContent: tempSqlValue,
        runType: 'dml',
      }).then(response => {
        if (response.Data.message === 'checkFailed') {
          notification.error({
            message: response.Data.errorInfo,
            duration: 5,
          })
        }
      })
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    addRow() {
      this.temp.content.mysqls.push({
        row: this.temp.content.mysqls.length,
        ip: [],
        scope: '',
        clowneUploadMode: false,
        sqlfilename: '上传',
        cachesqlfilename: '',
        status: 2,
      })
    },
    deleteRow(record) {
      if (this.temp.content.mysqls.length > 1) {
        this.temp.content.mysqls.splice(record.row, 1)
        for (let i = record.row; i < this.temp.content.mysqls.length; i++) {
          this.temp.content.mysqls[i].row -= 1
        }
      }
    },
    dbaCheck() {
      this.dbaCheckVisible = true
      this.checkPaaswd = ''
    },
    dbaCheckSubmit() {
      dbPasswdCheck({ password: this.checkPaaswd }).then(response => {
        if (response.Data.message !== 'error') {
          this.temp.content.passwd = response.Data.message
        } else {
          notification.error({
            message: '密码错误，请检查！',
          })
        }
      })
      this.dbaCheckVisible = false
    },
    dbaCheckCancel() {
      this.dbaCheckVisible = false
    },
    async dbLevelAndCreate() {
      // 校验存在非空任务的不允许提交
      for (let i = 0; i < this.temp.content.mysqls.length; i++) {
        if (this.temp.content.mysqls[i].ip.length === 0) {
          let row = i + 1
          notification.error({
            message: `第 ${row} 行数据库选项为空，请检查！`,
          })
          return false
        }
      }
      this.loading = true
      let sendBody = { businessLabel: this.temp.content.businessLabel, mysqlData: JSON.stringify(this.temp.content) }
      console.log(sendBody)
      await dmlDataLevels(sendBody).then(response => {
        console.log(response)
        if (response.Data.needSafeNode === 'true') {
          this.temp.content.needSafeNode = 'true'
          this.temp.content.dbFiledLevels = response.Data.dbFiledLevels
        }
        this.ddlfilesConfirm()
      })
      this.loading = false
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          createOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/dml-qa', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          // 原进入安全管理员审批(节点2)，现改为进入QA审批(节点2)
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
  
    // 新增QA审批方法
    qaApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          // QA审批后进入安全管理员审批(节点3)
          this.temp.node = this.temp.content.needSafeNode === 'false' ? 4 : 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: 'QA审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
  
    safeapproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          // 原进入DBA审批(节点3)，现改为进入DBA审批(节点4)
          this.temp.node = 4
          this.temp.status = 2
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
  
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          // 原进入完成状态(节点4)，现改为完成状态(节点5)
          this.temp.node = 5
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
  
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          // 原进入拒绝状态(节点5)，现改为拒绝状态(节点6)
          this.temp.node = 6
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
  
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          // 原进入撤回状态(节点5)，现改为撤回状态(节点6)
          this.temp.node = 6
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less" scoped>
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}
.dataBaseTable {
  /deep/.ant-table-header {
    font-size: 12px;
  }
  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  /deep/ .ant-table-tbody {
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}
</style>
