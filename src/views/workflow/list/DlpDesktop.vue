<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card
      style="margin-top: 12px"
      :bordered="false"
      :title="`${temp.orderType}——数据标注场景流程完成半小时后（IT半小时同步网络数据），访问操作请到12F小房间插网线连接,如果没有转换器/扩展槽请联系IT-桂国忠领取`"
    >
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="policyName" label="业务场景">
              <a-select v-model:value="temp.content.policyName" placeholder="请选择DLP云桌面使用的业务场景">
                <a-select-option v-for="item in policyNameList" :key="item.key" :value="item.value">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="expiration" label="过期时间">
              <a-date-picker show-time v-model:value="temp.content.expiration" valueFormat="YYYY-MM-DD HH:mm" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="name" label="云桌面名称">
              <a-input v-model:value="temp.content.name" placeholder="DLP云桌面自定义名称" allow-clear>
                <template #suffix>
                  <a-tooltip title="云桌面名称，可自定义">
                    <a-icon type="info-circle" style="color: rgba(0, 0, 0, 0.45)" />
                  </a-tooltip>
                </template>
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="操作系统" name="os">
              <a-radio-group v-model:value="temp.content.os" button-style="solid">
                <a-radio-button value="ubuntu">Ubuntu</a-radio-button>
                <a-radio-button value="windows">Windows</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.os === 'windows'">
                <p>
                  Windows云桌面需要购买License。费用是Ubuntu一倍，License成本由申请人所属事业部承担。建议优先选择Ubuntu。
                </p>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="配置规格" name="serverType">
              <a-cascader
                v-model:value="temp.content.serverType"
                :options="serverTypeOptions"
                placeholder="请选择CPU/内存"
                change-on-select
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="额外数据盘" name="needOtherDisks">
              <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
                <a-radio-button value="0">否</a-radio-button>
                <a-radio-button value="1">是</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.needOtherDisks === '1'">
                <a-input-group compact>
                  <a-select v-model:value="temp.content.diskType">
                    <a-select-option value="HDD">机械硬盘(GB)</a-select-option>
                    <a-select-option value="SSD">固态硬盘(GB)</a-select-option>
                  </a-select>
                  <a-input-number v-model:value="temp.content.diskSize" style="width: 30%" />
                </a-input-group>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24" v-if="temp.content.policyName === '数据标注'">
          <a-col :span="12">
            <a-form-model-item name="propertyCode" label="个人电脑编号">
              <a-select v-model:value="temp.content.propertyCode" placeholder="请选择需要访问的个人电脑编号">
                <a-select-option v-for="item in userIpList" :key="item.propertyCode" :value="item.propertyCode">
                  {{ item.propertyCode }}({{ item.propertyName }})
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" placeholder="申请的用途或理由说明" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" :auto-size="{ minRows: 3 }" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 5 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="primary" @click="preApproveData" style="margin-left: 10px">预操作</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal
      title="云桌面关系绑定"
      :visible="preApproveVisible"
      @ok="autoApproveData"
      width="650px"
      @cancel="preApproveVisible = false"
    >
      <a-form-model ref="dataForm" :rules="rules" :model="temp.content" style="min-width: 200px; margin-left: 50px">
        <a-form-model-item label="云桌面ip">
          <a-input v-model:value="temp.content.ip" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="云桌面资源ID">
          <a-input v-model:value="temp.content.desktopId" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="代理信息" name="business">
          <a-select v-model:value="temp.content.idc">
            <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
            <a-select-option value="tencent">腾讯云</a-select-option>
            <a-select-option value="sh-office">云立方</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder, getUserIpList } from '@/api/workflow/order'
import { getDesktopPermissionNameList } from '@/api/desktop/desktop_permission'

const serverTypeOptions = [
  {
    value: '1',
    label: '1vCPU',
    children: [
      {
        value: '1',
        label: '1GiB',
      },
      {
        value: '2',
        label: '2GiB',
      },
      {
        value: '4',
        label: '4GiB',
      },
    ],
  },
  {
    value: '2',
    label: '2vCPU',
    children: [
      {
        value: '2',
        label: '2GiB',
      },
      {
        value: '4',
        label: '4GiB',
      },
      {
        value: '8',
        label: '8GiB',
      },
    ],
  },
  {
    value: '4',
    label: '4vCPU',
    children: [
      {
        value: '4',
        label: '4GiB',
      },
      {
        value: '8',
        label: '8GiB',
      },
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '8',
    label: '8vCPU',
    children: [
      {
        value: '8',
        label: '8GiB',
      },
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '16',
    label: '16vCPU',
    children: [
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '32',
    label: '32vCPU',
    children: [
      {
        value: '32',
        label: '32GiB',
      },
      {
        value: '64',
        label: '64GiB',
      },
    ],
  },
]

export default {
  name: 'DlpDesktop',
  data() {
    return {
      localUser: store.getters.email,
      serverTypeOptions,
      time_now: '',
      preApproveVisible: false,
      userIpList: [],
      policyNameList: [],
      node_status: 1,
      userTemp: {
        email: store.getters.email,
      },
      temp: {
        id: '',
        orderType: 'DLP云桌面',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: store.getters.email,
          email: '',
          policyName: '数据标注',
          os: 'ubuntu',
          serverType: ['4', '8'],
          needOtherDisks: '0',
          diskType: 'HDD',
          ip: '',
          desktopId: '',
          idc: 'ucloud-shanghai2-hybrid',
          diskSize: 0,
          propertyCode: '',
          expiration: '',
          isAutoConfig: false,
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.policyName': [{ required: true, message: '请选择业务场景', trigger: 'blur' }],
        'content.name': [{ required: true, message: '请填写云桌面自定义名称', trigger: 'blur' }],
        'content.serverType': [{ required: true, message: '请选择配置规格', trigger: 'blur' }],
        'content.os': [{ required: true, message: '请选择操作系统', trigger: 'blur' }],
        'content.needOtherDisks': [{ required: true, message: '请选择是否需要数据盘', trigger: 'change' }],
        'content.propertyCode': [{ required: true, message: '请选择需要访问的个人电脑编号', trigger: 'change' }],
        'content.expiration': [{ required: true, message: '请选择DLP云桌面失效日期', trigger: 'change' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
    }
  },
  created() {
    this.getInfo()
    this.getUserIps()
    this.getPolicyNameListInfo()
  },
  methods: {
    preApproveData() {
      this.preApproveVisible = true
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    getUserIps() {
      getUserIpList(this.userTemp).then(response => {
        this.userIpList = response.Data.ipInfo
      })
    },
    getPolicyNameListInfo() {
      getDesktopPermissionNameList().then(res => {
        for (var i = 0, len = res.Data.permissionNameList.length; i < len; i++) {
          var name = {}
          name.value = res.Data.permissionNameList[i].name
          name.label = res.Data.permissionNameList[i].name
          this.policyNameList.push(name)
        }
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.email = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/dlp-desktop', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 2
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    autoApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutoConfig = true
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
