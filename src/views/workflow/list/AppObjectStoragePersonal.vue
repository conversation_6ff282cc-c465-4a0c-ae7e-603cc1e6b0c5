<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-01-09 19:17:08
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-01-31 15:27:37
 * @FilePath: \cloud_web\src\views\workflow\list\AppObjectStoragePersonal.vue
 * @Description:
 *
 * Copyright (c) 2023 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <WorkflowStream :steps="steps" ref="WorkflowStream">
    <template v-slot:contents>
      <a-form-model
        ref="temp"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item ref="bucketName" name="bucketName" label="存储库名称">
          <a-input
            v-model:value="temp.content.bucketName"
            @blur="
              () => {
                $refs.bucketName.onFieldBlur()
              }
            "
          />
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" />
        </a-form-model-item>
        <a-form-model-item label="回复/评论" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
      </a-form-model>
    </template>
  </WorkflowStream>
</template>
<script>
import WorkflowStream from '@/views/comp/workFlowStream/index.vue'
import store from '@/store' // secondary package based on el-pagination
export default {
  components: { WorkflowStream },
  provide() {
    return {
      workFlowData: this,
    }
  },
  data() {
    console.log(store.getters.email, 'store.getters.email')
    const defaultName = store.getters.email.split('@intsig.net')[0]
    return {
      steps: [
        {
          title: '上级领导审批',
        },
        {
          title: '管理员审批',
        },
      ],
      rules: {
        bucketName: [{ required: true, message: '请输入存储库名称', trigger: 'blur' }],
      },
      temp: {
        id: '',
        orderType: '对象存储个人版申请',
        founder: store.getters.name,
        founderEmail: store.getters.email,
        handlerEmail: '',
        node: 0,
        status: 0,
        time_now: '',
        handler: '',
        timeline: [],
        comment: '',
        content: {
          bucketName: defaultName,
          reason: '',
        },
      },
    }
  },
  mounted() {
    // this.$refs.WorkflowStream.initPage()
    // console.log(this.temp, 'ttttt')
  },
}
</script>

<style></style>
