<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="申请服务器IP" v-if="temp.content.resultIpList.length !== 0 && temp.status === 10">
              <a-tag v-for="(ip, index) in this.temp.content.resultIpList" :key="index" color="green">
                {{ ip }}
              </a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="机房" name="idc">
              <x-select v-model:value="temp.content.idc" placeholder="请选择机房" @change="getAssetListInfo">
                <a-select-option value="ucloud-shanghai2-public">UCloud云主机</a-select-option>
                <a-select-option value="ucloud-shanghai1-zhuanqiao">UCloud颛桥</a-select-option>
                <a-select-option value="aws-cn-north-1">AWS北京(cn-north-1)</a-select-option>
                <a-select-option value="aws-cn-northwest-1">AWS宁夏(cn-northwest-1)</a-select-option>
                <a-select-option value="aws-eu-west-1">AWS欧洲(爱尔兰)(eu-west-1)</a-select-option>
                <a-select-option value="aws-us-west-1">AWS美国西部(加利福尼亚北部)(us-west-1)</a-select-option>
                <a-select-option value="aws-us-west-2">AWS美国西部(俄勒冈)(us-west-2)</a-select-option>
                <a-select-option value="aws-ap-east-1">AWS亚太地区(香港)(ap-east-1)</a-select-option>
                <a-select-option value="aws-ap-south-1">AWS亚太地区(孟买)(ap-south-1)</a-select-option>
                <a-select-option value="aws-ap-southeast-1">AWS亚太地区(新加坡)(ap-southeast-1)</a-select-option>
                <a-select-option value="aws-ap-northeast-1">AWS亚太地区(东京)(ap-northeast-1)</a-select-option>
                <a-select-option value="aliyun-hehe-cn-hangzhou">阿里云(合合)</a-select-option>
                <a-select-option value="sh-office">上海云立方</a-select-option>
                <a-select-option value="aliyun-qxb-cn-hangzhou">阿里云(启信宝)</a-select-option>
                <a-select-option value="aliyun-cs-cn-shanghai">阿里云(CS)</a-select-option>
                <a-select-option value="tencent-shanghai">腾讯云上海区域</a-select-option>
                <a-select-option value="ucloud-shanghai2-hybrid">UCloud混合云(即将下线)</a-select-option>
                <a-select-option value="shanghai8-songjiang">松江机房(即将下线)</a-select-option>
                <a-select-option value="tencent-tokyo">腾讯云东京区域</a-select-option>
                <a-select-option value="azure-cn-chinaeast2">微软云上海2</a-select-option>
              </x-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="其他负责人">
              <a-select
                v-model:value="temp.content.principal"
                :options="principalListMini"
                :filter-option="filterOption"
                mode="multiple"
                style="width: 100%"
                placeholder="负责人可输入或选择"
                @popupScroll="handlePopupScrollUser"
                @search="handleSearch"
              ></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="配置规格" name="serverType">
              <a-cascader
                v-model:value="temp.content.serverType"
                :options="serverTypeOptions"
                placeholder="请选择CPU/内存"
                change-on-select
                @change="getAllAssetPrice"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="费用负责人" name="costUser">
              <a-select
                placeholder="请输入邮箱选择"
                v-model:value="temp.content.costUser"
                :options="principalListMini"
                :filter-option="filterOption"
                :showSearch="true"
                :allowClear="true"
                @popupScroll="handlePopupScrollUser"
                @search="handleSearch"
                @select="getUserTeamListMethod"
              ></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="数量" name="num">
              <a-input-number
                id="inputNumber"
                v-model:value="temp.content.num"
                :min="1"
                :max="100"
                @change="getAllAssetPrice"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="事业部/部门/团队" name="organization">
              <a-cascader v-model:value="temp.content.organization" :options="organizationOptions" change-on-select />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="操作系统" name="os">
              <a-cascader
                v-model:value="temp.content.os"
                :options="osOptions"
                placeholder="请选择系统"
                change-on-select
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="费用承担业务方" name="business">
              <a-select v-model:value="temp.content.business" :allowClear="true" @change="changeCostAttr">
                <a-select-option v-for="business in businessList" :key="business" :value="business">
                  {{ business }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="额外数据盘">
              <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.needOtherDisks">
                <a-input-group compact>
                  <a-select v-model:value="temp.content.diskType" @change="getAllAssetPrice">
                    <a-select-option value="HDD">机械硬盘(GB)</a-select-option>
                    <a-select-option value="SSD">固态硬盘(GB)</a-select-option>
                  </a-select>
                  <a-input-number v-model:value="temp.content.diskSize" style="width: 30%" @change="getAllAssetPrice" />
                </a-input-group>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="费用类型:" name="costAttr">
              <a-radio-group v-model:value="temp.content.costAttr" button-style="solid" disabled>
                <a-radio-button value="运营成本">运营</a-radio-button>
                <a-radio-button value="研发成本">研发</a-radio-button>
                <a-radio-button value="开发支出">开发支出</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="权限类型" name="permissionType">
              <a-radio-group v-model:value="temp.content.permissionType" button-style="solid">
                <a-radio-button value="0">普通权限</a-radio-button>
                <a-radio-button value="1">sudo权限</a-radio-button>
                <a-radio-button value="2">其他权限</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.permissionType === '2'">
                <a-input v-model:value="temp.content.permissionDescription" placeholder="请填写具体权限类型" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="项目" name="project">
              <x-select
                v-model:value="temp.content.project"
                :options="projectList"
                placeholder="项目可输入或选择"
                style="width: 100%"
              ></x-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="自定义服务器名" name="projectName">
              <a-input v-model:value="temp.content.projectName" placeholder="主机名" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="环境" name="env">
              <RadioEnv v-model="temp.content.env" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="是否使用镜像:" name="needPreImageIp">
              <a-radio-group v-model:value="temp.content.needPreImageIp" button-style="solid">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.needPreImageIp" class="ext-judge">
                <a-select
                  v-model:value="temp.content.preImageIp"
                  style="width: 100%"
                  @popupScroll="handlePopupScroll"
                  @search="handleSearchIp"
                  placeholder="选择镜像IP"
                  show-search
                  allowClear
                >
                  <a-select-option v-for="i in ipInfo" :key="i.ip">
                    {{ i.ip + '(' + i.hostname + ')' }}
                  </a-select-option>
                </a-select>
                <p style="color: red; font-size: 14px" v-show="showImageTips">请选择镜像</p>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="应用" name="application">
              <x-select
                v-model:value="temp.content.application"
                mode="tags"
                :options="applicationList"
                placeholder="应用可输入或选择"
                style="width: 100%"
              ></x-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col
            :span="12"
            v-if="
              (temp.content.costAttr === '运营成本' &&
              temp.content.business !== '大数据' &&
              temp.content.business !== '搜索(DG)') || temp.content.costAttr === '开发支出'
            "
          >
            <a-form-model-item label="产品线" name="productLineList">
              <a-form name="productLineList_item" :model="temp.content.productLineList">
                <a-row
                  :gutter="24"
                  v-for="(productLine, index) in temp.content.productLineList"
                  v-bind="formItemLayoutWithOutLabel"
                >
                  <a-col :span="12">
                    <a-form-item name="form.productLineList[index].productLineList">
                      <a-cascader
                        v-model:value="productLine.productLineNameList"
                        :options="productLineOption"
                        change-on-select
                        placeholder="请选择产品线"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      name="form.productLineList[index].ratio"
                      :rules="[{ validator: (_, value, callback) => this.validateNumberSum(value, index, callback) }]"
                    >
                      <a-input-number
                        v-model:value="productLine.ratio"
                        :precision="4"
                        placeholder="比例"
                        :min="0"
                        :max="1"
                        :step="0.0001"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item>
                      <tx-button
                        v-if="index === 0"
                        type="primary"
                        icon="plus"
                        style="width: 30px"
                        size="small"
                        @click="addRow"
                      ></tx-button>
                      <tx-button
                        v-else
                        type="dashed"
                        size="small"
                        icon="minus"
                        style="width: 30px"
                        @click="removeRow(index)"
                      ></tx-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="是否DG数据资产" name="isDgDataAsset">
              <a-radio-group v-model:value="temp.content.isDgDataAsset" button-style="solid" @change="changeCostAttr">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="temp.content.isDgDataAsset">
            <a-form-model-item label="DG数据资产类别" name="dgDataAssetClassification">
              <a-select
                v-model:value="temp.content.dgDataAssetClassification"
                placeholder="请选择DG数据资产类别"
                @change="handleDGSpecificBusiness"
              >
                <a-select-option value="基础数据">基础数据</a-select-option>
                <a-select-option value="知识数据">知识数据</a-select-option>
                <a-select-option value="组件数据">组件数据</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              v-if="
                this.temp.content.idc.startsWith('tencent') ||
                this.temp.content.idc.startsWith('aws') ||
                this.temp.content.idc.startsWith('ali')
              "
              label="安全组"
            >
              <a-select
                v-model:value="temp.content.approvalData.chooseSecurityGroupList"
                filterable
                mode="multiple"
                placeholder="请选择"
                style="width: 500px"
              >
                <a-select-option
                  v-for="item in tempSecurityGroupList"
                  :key="item.securityGroupId"
                  :value="item.securityGroupId"
                >
                  {{ item.securityGroupName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="DG数据资产业务方" name="dgSpecificBusiness" v-if="temp.content.isDgBusiness">
              <a-select
                v-model:value="temp.content.dgSpecificBusiness"
                placeholder="请选择DG数据资产具体业务方"
                allowClear
              >
                <a-select-option value="AUTOM">AUTOM</a-select-option>
                <a-select-option value="CC">CC</a-select-option>
                <a-select-option value="CS">CS</a-select-option>
                <a-select-option value="DGB">DGB</a-select-option>
                <a-select-option value="DGC">DGC</a-select-option>
                <a-select-option value="DGG">DGG</a-select-option>
                <a-select-option value="SSG">SSG</a-select-option>
                <a-select-option value="公用">公用</a-select-option>
                <a-select-option value="数据中心">数据中心</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请理由" name="content.reason">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="预估成本">
              <div v-if="this.priceLoading">
                <a-icon style="font-size: 25px; margin-left: 20px" type="loading" />
              </div>
              <div v-else>
                <p v-if="this.temp.content.priceMsg === '' || this.temp.content.priceMsg === undefined" class="myColl">
                  <a-collapse :expand-icon-position="'right'" :bordered="false">
                    <a-collapse-panel header="每月">
                      <p>
                        <span class="">CPU/内存费用：</span>
                        <span class="" style="float: right; margin-right: 20px">
                          ￥{{ temp.content.priceInfo.discountPrice }} （原价 ¥
                          {{ temp.content.priceInfo.originalPrice }}）
                        </span>
                      </p>
                      <p>
                        <span class="">磁盘费用：</span>
                        <span class="" style="float: right; margin-right: 20px">
                          ￥{{ temp.content.priceInfo.discountDiskPrice }} （原价 ¥
                          {{ temp.content.priceInfo.originalDiskPrice }}）
                        </span>
                      </p>
                      <template #extra>
                        <p style="line-height: 15px">
                          <span style="font-size: 14px; color: grey; float: right; line-height: 18px">
                            （原价 ¥
                            {{ temp.content.priceInfo.allOrigin }}）
                          </span>
                          <span
                            class="shadowPrice"
                            style="
                              float: right;
                              margin-right: 8px;
                              font-size: 20px;
                              font-weight: bolder;
                              color: #ff4500;
                            "
                          >
                            <span style="font-size: 14px">￥</span>
                            {{ temp.content.priceInfo.allDiscount }}
                          </span>
                        </p>
                      </template>
                    </a-collapse-panel>
                  </a-collapse>
                </p>
                <p v-else>
                  <span style="font-size: 16px; font-weight: bolder; color: #ff4500">
                    {{ temp.content.priceMsg }}
                  </span>
                </p>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/asset-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="createAsset">同意</tx-button>
          <tx-button type="primary" :loading="preStatus" @click="preCreateAsset" style="margin-left: 10px">
            预创建
          </tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <!--    服务预创建-->
    <a-modal
      title="服务器预创建"
      :visible="modalFormPreBindServerVisible"
      @ok="preApproveData"
      width="800px"
      @cancel="modalFormPreBindServerVisible = false"
    >
      <div v-if="this.temp.content.idc === 'ucloud-shanghai2-hybrid'">
        <a-form-model
          ref="preDataForm"
          :rules="assetRules"
          :model="temp.content.approvalData"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="预创建检查">
            <a-tag v-for="(i, index) in preCreateMsgs" :key="index" size="medium" :color="i.color">
              {{ index + 1 }}: {{ i.msg }}
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="项目名称" name="projectName">
            <a-input v-model:value="temp.content.projectName" placeholder="默认为空！" style="width: 500px" />
          </a-form-model-item>
          <a-form-model-item label="物理机IP" name="approvalData.hostIpList">
            <a-select
              ref="subnets"
              v-model:value="temp.content.approvalData.hostIpList"
              mode="tags"
              placeholder="请选择"
            >
              <a-select-option
                v-for="item in temp.content.approvalData.hostIpList"
                :key="item"
                :vpc="item"
                :value="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="vlan地址">
            <a-input v-model:value="temp.content.approvalData.vlan" placeholder="15" />
          </a-form-model-item>
          <a-form-model-item label="使用镜像ip">
            <a-input v-model:value="temp.content.preImageIp" placeholder="" />
          </a-form-model-item>
          <a-form-model-item label="额外数据盘">
            <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
              <a-radio-button :value="false">否</a-radio-button>
              <a-radio-button :value="true">是</a-radio-button>
            </a-radio-group>
            <div v-if="temp.content.needOtherDisks">
              <a-input-group compact>
                <a-select v-model:value="temp.content.diskType" placeholder="为空则不添加额外磁盘" allowClear>
                  <a-select-option v-for="item in diskTypeList" :key="item.key" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
                <a-input-number v-model:value="temp.content.diskSize" style="width: 30%" @change="getAllAssetPrice" />
              </a-input-group>
            </div>
          </a-form-model-item>
          <a-form-model-item label="是否初始化">
            <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="是否安装HIDS">
            <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div v-else-if="this.temp.content.idc.startsWith('aws')">
        <a-form-model
          ref="preDataForm"
          :rules="awsRules"
          :model="temp.content.approvalData"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="项目名称" name="projectName">
            <a-input v-model:value="temp.content.projectName" placeholder="默认为空！" style="width: 500px" />
          </a-form-model-item>
          <a-form-model-item label="实例类型" name="approvalData.instanceType">
            <a-select
              v-model:value="temp.content.approvalData.instanceType"
              placeholder="请选择"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option v-for="item in instanceTypeList" :key="item.type" :value="item.type">
                {{ item.type }} ( {{ item.data }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="使用镜像ip">
            <a-input v-model:value="temp.content.preImageIp" placeholder="默认为空！" style="width: 500px" />
          </a-form-model-item>
          <a-form-model-item label="镜像名称" name="approvalData.defaultImageId">
            <a-select
              v-model:value="temp.content.approvalData.defaultImageId"
              show-search
              :filter-option="filterOption"
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option v-for="item in imageList" :key="item.imageId" :value="item.imageId">
                {{ item.imageName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item v-if="temp.content.num === 1" label="子网" name="approvalData.defaultSubnetId">
            <a-select
              ref="subnet"
              v-model:value="temp.content.approvalData.defaultSubnetId"
              style="width: 500px"
              allowClear
              placeholder="请选择"
            >
              <a-select-option v-for="item in subnetList" :key="item.subnetId" :vpc="item.vpcId" :value="item.subnetId">
                {{ item.subnetName }} ( {{ item.network }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item v-else label="子网" name="approvalData.defaultSubnetId">
            <a-select
              ref="subnets"
              mode="multiple"
              v-model:value="temp.content.approvalData.defaultSubnetId"
              style="width: 500px"
              placeholder="请选择"
              @change="getSecurityLists"
            >
              <a-select-option v-for="item in subnetList" :key="item.subnetId" :vpc="item.vpcId" :value="item.subnetId">
                {{ item.subnetName }} ( {{ item.network }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="是否开启外网IP">
            <a-select v-model:value="temp.content.needPublicIp" filterable style="width: 500px" placeholder="请选择">
              <a-select-option v-for="item in needPublicIpList" :key="item.key" :value="item.key">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="安全组" name="approvalData.defaultSecurityGroupList">
            <a-select
              v-model:value="temp.content.approvalData.defaultSecurityGroupList"
              :disabled="vpcStatus"
              style="width: 500px"
              placeholder="请选择"
              mode="multiple"
              :filter-option="filterOption"
            >
              <a-select-option
                v-for="item in securityGroupList"
                :key="item.securityGroupId"
                :value="item.securityGroupId"
              >
                {{ item.securityGroupName }}
              </a-select-option>
            </a-select>
            <span v-if="vpcStatus" style="font-size: 10px; color: red; margin-left: 3px">加载中~</span>
          </a-form-model-item>
          <a-form-model-item label="是否初始化">
            <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="是否安裝HIDS">
            <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div v-else-if="this.temp.content.idc.startsWith('ali')">
        <a-form-model
          ref="preDataForm"
          :rules="aliyunRules"
          :model="temp.content.approvalData"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="项目名称" name="projectName">
            <a-input v-model:value="temp.content.projectName" placeholder="默认为空！" style="width: 500px" />
          </a-form-model-item>
          <a-form-model-item label="VPC名称" name="approvalData.defaultVpcId">
            <a-select
              v-model:value="temp.content.approvalData.defaultVpcId"
              filterable
              placeholder="请选择"
              style="width: 500px"
              @change="refreshData"
            >
              <a-select-option v-for="item in vpcInfo" :key="item.vpcId" :value="item.vpcId">
                {{ item.vpcName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="可用区" name="approvalData.defaultZoneId">
            <a-select
              v-model:value="temp.content.approvalData.zoneIdList"
              filterable
              allow-create
              mode="multiple"
              placeholder="请选择"
              style="width: 500px"
              @change="refreshZoneData"
            >
              <a-select-option v-for="item in instanceTypeList" :key="item.zoneId" :value="item.zoneId">
                {{ item.zoneId }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="虚拟交换机ID" name="approvalData.defaultVpcSwitchList">
            <a-select
              v-model:value="temp.content.approvalData.defaultVpcSwitchList"
              filterable
              mode="tags"
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option
                v-for="item in multipleVpcSwitchList"
                :key="item.vpcSwitchId + '|' + item.zoneId"
                :value="item.vpcSwitchId + '|' + item.zoneId"
              >
                {{ item.vpcSwitchId }} ( {{ item.zoneId }} ) ( {{ item.cidrBlock }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="实例类型" name="approvalData.instanceType">
            <a-select
              v-model:value="temp.content.approvalData.instanceType"
              filterable
              allow-create
              placeholder="请选择"
              style="width: 500px"
              @change="getDiskInfo"
            >
              <a-select-option v-for="item in multipleInstanceList" :key="item.instanceType" :value="item.instanceType">
                {{ item.instanceType }} ( {{ item.label }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="镜像名称" name="approvalData.defaultImageId">
            <a-select
              v-model:value="temp.content.approvalData.defaultImageId"
              filterable
              :filter-option="filterOption"
              show-search
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option v-for="item in imageList" :key="item.imageId" :value="item.imageId">
                {{ item.imageName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="安全组" name="approvalData.defaultSecurityGroupList">
            <a-select
              v-model:value="temp.content.approvalData.defaultSecurityGroupList"
              filterable
              mode="multiple"
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option
                v-for="item in vpcInfo[this.vpcIndex].vpcSecurityGroupList"
                :key="item.securityGroupId"
                :value="item.securityGroupId"
              >
                {{ item.securityGroupName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="系统盘类型">
            <a-select v-model:value="temp.content.systemDiskType">
              <a-select-option
                @change="getSystemDiskSize"
                v-for="item in aliyunSystemDiskTypeDetailList"
                :key="item.diskType + '|' + item.performanceLevel"
                :value="item.diskType + '|' + item.performanceLevel"
              >
                {{ item.diskName }} ({{ item.diskType }} {{ item.performanceLevel }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="系统盘大小(G)">
            <a-input-number :min="dataDiskMinSize" :max="dataDiskMaxSize" v-model:value="temp.content.systemDiskSize" />
          </a-form-model-item>
          <a-form-model-item label="额外数据盘">
            <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
              <a-radio-button :value="false">否</a-radio-button>
              <a-radio-button :value="true">是</a-radio-button>
            </a-radio-group>
            <div v-if="temp.content.needOtherDisks">
              <a-input-group compact>
                <a-select
                  style="width: 50%"
                  v-model:value="temp.content.diskType"
                  placeholder="为空则不添加额外磁盘"
                  allowClear
                >
                  <a-select-option
                    @change="getDataDiskSize"
                    v-for="item in aliyunDataDiskTypeDetailList"
                    :key="item.diskType + '|' + item.performanceLevel"
                    :value="item.diskType + '|' + item.performanceLevel"
                  >
                    {{ item.diskName }} ({{ item.diskType }} {{ item.performanceLevel }} )
                  </a-select-option>
                </a-select>
                <a-input-number
                  :min="dataDiskMinSize"
                  :max="dataDiskMaxSize"
                  v-model:value="temp.content.diskSize"
                  style="width: 30%"
                  @change="getAllAssetPrice"
                />
              </a-input-group>
            </div>
          </a-form-model-item>
          <a-form-model-item label="是否初始化">
            <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="是否安裝HIDS">
            <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div v-else-if="this.temp.content.idc.startsWith('tencent')">
        <a-form-model
          ref="preDataForm"
          :rules="tencentRules"
          :model="temp.content.approvalData"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="项目名称" name="projectName">
            <a-input v-model:value="temp.content.projectName" placeholder="默认为空！" style="width: 500px" />
          </a-form-model-item>
          <a-form-model-item label="可用区" name="approvalData.defaultZoneId">
            <a-select
              v-model:value="temp.content.approvalData.zoneIdList"
              filterable
              allow-create
              mode="multiple"
              placeholder="请选择"
              style="width: 500px"
              @change="refreshZoneData"
            >
              <a-select-option v-for="item in instanceTypeList" :key="item.zoneId" :value="item.zoneId">
                {{ item.zoneId }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="实例类型" name="approvalData.instanceType">
            <a-select
              v-model:value="temp.content.approvalData.instanceType"
              filterable
              allow-create
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option v-for="item in multipleInstanceList" :key="item.instanceType" :value="item.instanceType">
                {{ item.instanceType }} ( {{ item.label }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="镜像名称" name="approvalData.defaultImageId">
            <a-select
              v-model:value="temp.content.approvalData.defaultImageId"
              filterable
              show-search
              :filter-option="filterOption"
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option v-for="item in imageList" :key="item.imageId" :value="item.imageId">
                {{ item.imageName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="VPC名称" name="approvalData.defaultVpcId">
            <a-select
              v-model:value="temp.content.approvalData.defaultVpcId"
              filterable
              placeholder="请选择"
              style="width: 500px"
              @change="refreshData"
            >
              <a-select-option v-for="item in tencentVpcInfo" :key="item.vpcId" :value="item.vpcId">
                {{ item.vpcName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="Subnet" name="approvalData.defaultVpcSubnetList">
            <a-select
              v-model:value="temp.content.approvalData.defaultSubnetId"
              filterable
              mode="multiple"
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option
                v-for="item in multipleVpcSubnetList"
                :key="item.subnetId + '|' + item.zone"
                :value="item.subnetId + '|' + item.zone"
              >
                {{ item.subnetName }} ( {{ item.zone }} ) ( {{ item.cidrBlock }} )
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="安全组" name="approvalData.defaultSecurityGroupList">
            <a-select
              v-model:value="temp.content.approvalData.defaultSecurityGroupList"
              filterable
              mode="multiple"
              show-search
              :filter-option="filterOption"
              placeholder="请选择"
              style="width: 500px"
            >
              <a-select-option
                v-for="item in securityGroupList"
                :key="item.securityGroupId"
                :value="item.securityGroupId"
              >
                {{ item.securityGroupName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="磁盘类型">
            <a-select v-model:value="temp.content.diskType" placeholder="为空则不添加额外磁盘" allowClear>
              <a-select-option v-for="item in tencentDiskTypeList" :key="item.key" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="磁盘大小(G)">
            <a-input-number v-model:value="temp.content.diskSize" placeholder="" />
          </a-form-model-item>
          <a-form-model-item label="是否初始化">
            <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="是否安裝HIDS">
            <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
              <a-radio-button :value="0">否</a-radio-button>
              <a-radio-button :value="1">是</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-modal>
    <!--    服务器信息绑定-->
    <a-modal
      title="服务器关系绑定"
      :visible="modalFormBindServerVisible"
      @ok="approveData"
      width="650px"
      @cancel="modalFormBindServerVisible = false"
    >
      <a-form-model ref="dataForm" :rules="rules" :model="temp.content" style="min-width: 200px; margin-left: 50px">
        <a-form-model-item
          v-for="(keys, i) in temp.content.num"
          :key="i"
          :label="`Key${temp.content.num === 1 ? '' : i + 1}`"
          :name="'keys[' + i + ']'"
        >
          <a-input v-model:value="temp.content.approvalData.keys[i]" style="width: 500px" />
          <span v-if="tipsStatus" style="font-size: 10px; color: red; margin-left: 3px">请确认服务器唯一标识!</span>
        </a-form-model-item>
        <a-form-model-item label="是否初始化">
          <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
            <a-radio-button :value="0">否</a-radio-button>
            <a-radio-button :value="1">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否安裝HIDS">
          <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
            <a-radio-button :value="0">否</a-radio-button>
            <a-radio-button :value="1">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import user from '@/store/modules/user'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import {
  aliyunBaseInfo,
  aliyunDiskInfo,
  awsBaseInfo,
  tencentBaseInfo,
  vmBaseInfo,
  getAssetPrice,
  awsSecurityBaseInfo,
  getAllAssetsIp,
} from '@/api/workflow/asset_automation'
import { getUserEmailList, getUserList } from '@/api/permission/user'
import { getAssetListApplication, getAssetListProject, GetUserOrgAllInfo } from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'
import { getProductLineList } from '@/api/cost/label'
import BusinessSelect from '@/suite/SelectBusiness/index.vue'
import { listAssetSecurityGroupName } from '@/api/asset/securityGroup'

const serverTypeOptions = [
  {
    value: '1',
    label: '1vCPU',
    children: [
      {
        value: '1',
        label: '1GiB',
      },
      {
        value: '2',
        label: '2GiB',
      },
      {
        value: '4',
        label: '4GiB',
      },
    ],
  },
  {
    value: '2',
    label: '2vCPU',
    children: [
      {
        value: '2',
        label: '2GiB',
      },
      {
        value: '4',
        label: '4GiB',
      },
      {
        value: '8',
        label: '8GiB',
      },
    ],
  },
  {
    value: '4',
    label: '4vCPU',
    children: [
      {
        value: '4',
        label: '4GiB',
      },
      {
        value: '8',
        label: '8GiB',
      },
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '8',
    label: '8vCPU',
    children: [
      {
        value: '8',
        label: '8GiB',
      },
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '16',
    label: '16vCPU',
    children: [
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
      {
        value: '64',
        label: '64GiB',
      },
      {
        value: '128',
        label: '128GiB',
      },
    ],
  },
  {
    value: '32',
    label: '32vCPU',
    children: [
      {
        value: '32',
        label: '32GiB',
      },
      {
        value: '64',
        label: '64GiB',
      },
      {
        value: '128',
        label: '128GiB',
      },
    ],
  },
]


const osOptions = [
  {
    value: '云厂商系统',
    label: '云厂商系统',
    children: [
      {
        value: 'linux',
        label: 'Linux',
      },
      {
        value: 'windows',
        label: 'Windows',
      },
    ],
  }
]
const operation = [
  'CC',
  'CS-CC',
  'CS',
  'DG',
  'SSG',
  '搜索(DG)',
  '大数据',
  '市场',
  '招聘项目组',
  '营销云',
  '战略合作',
  '总裁办',
]

export default {
  name: 'ServerAdd',
  components: { BusinessSelect },
  data() {
    this.operation = operation
    return {
      organizationOptions: [],
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      localUser: user.state.email,
      serverTypeOptions,
      userEmailList: [],
      businessList: [],
      userTeamList: [],
      osOptions,
      priceLoading: false,
      principalList: [],
      principalNameToEmail: {},
      principalListMini: [],
      projectList: [],
      applicationList: [],
      serverOption: [],
      serverOptionMini: [],
      node_status: 1,
      productLineOption: [],
      temp: {
        id: '',
        orderType: '服务器新增',
        node: 0,
        status: 0,
        founderEmail: user.state.email,
        founder: user.state.name,
        time_now: '',
        handler: '',
        handlerEmail: '',
        content: {
          dep: undefined, // 部门
          ip: undefined,
          hostname: undefined,
          idc: 'ucloud-shanghai2-public',
          serverType: ['1', '2'],
          num: 1,
          os: ['云厂商系统', 'linux'],
          principal: [], // 其他负责人
          org: undefined,
          project: undefined,
          resultIpList: [],
          env: 'online',
          application: [],
          projectName: '',
          costUser: '',
          organization: [],
          costAttr: '运营成本',
          productLineList: [
            {
              productLineNameList: [],
              ratio: 0,
            },
          ],
          ownershipAsset: '合合',
          isDgDataAsset: false,
          isDgBusiness: false,
          dgDataAssetClassification: '',
          dgSpecificBusiness: '',
          needOtherDisks: false,
          preInstanceType: '',
          diskType: 'HDD',
          diskSize: 0,
          preImageIp: '',
          permissionType: '1',
          permissionDescription: '',
          needPreImageIp: false,
          isDb: false,
          isAutomation: false,
          reason: '',
          // aliyun
          systemDiskType: 'cloud',
          systemDiskSize: 20,
          // aws
          needPublicIp: 0,
          approvalData: {
            projectName: '', // 同步自 content.projectName，为了通过 rules 验证
            keys: [],
            needInit: 0,
            isDesktop: 0,
            hidsInit: 0,
            vlan: 15,
            hostIpList: [],
            instanceType: [],
            defaultImageId: '',
            defaultSubnetId: [],
            defaultSecurityGroupList: [],
            chooseSecurityGroupList: [],
            defaultVpcSwitchList: [],
            defaultVpcId: '',
            defaultZoneId: '',
            zoneIdList: [],
            roleName: '',
          },
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.idc': [{ required: true, message: '请选择机房', trigger: 'blur' }],
        'content.principal': [{ required: true, message: '请选择其他负责人', trigger: 'blur' }],
        'content.serverType': [{ required: true, message: '请选择配置规格', trigger: 'blur' }],
        'content.org': [{ required: true, message: '请选择所属事业部', trigger: 'blur' }],
        'content.num': [{ required: true, message: '请选择数量', trigger: 'blur' }],
        'content.dep': [{ required: true, message: '请选择部门', trigger: 'blur' }],
        'content.os': [{ required: true, message: '请选择操作系统', trigger: 'blur' }],
        'content.costUser': [{ required: true, message: '请选择费用负责人', trigger: 'blur' }],
        'content.project': [{ required: true, message: '请选择项目', trigger: 'blur' }],
        'content.env': [{ required: true, message: '请选择环境', trigger: 'blur' }],
        'content.permissionType': [{ required: true, message: '请选择权限类型', trigger: 'blur' }],
        'content.application': [{ required: true, message: '请选择应用', trigger: 'blur' }],
        'content.projectName': [{ required: true, message: '请输入自定义服务器名', trigger: 'blur' }],
        'content.productLineList': [{ required: true, message: '请选择产品线', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请输入原因', trigger: 'blur' }],
        'content.isDgDataAsset': [{ required: true, message: '请选择是否为DG数据资产', trigger: 'blur' }],
        'content.ownershipAsset': [{ required: true, message: '请选择资产归属', trigger: 'blur' }],
        'content.dgDataAssetClassification': [{ required: true, message: '请选择DG数据资产类别', trigger: 'blur' }],
      }),
      awsRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultSubnetId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      aliyunRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.defaultZoneId': [{ required: true, message: '请确认可用区', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultVpcId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultVpcSwitchList': [{ required: true, message: '请确认交换机ID', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      tencentRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.defaultZoneId': [{ required: true, message: '请确认可用区', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultVpcId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultSubnetId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      assetRules: {
        projectName: [{ required: true, message: '请确认项目名', trigger: 'change' }],
        hostIpList: [{ required: true, message: '请确认物理机', trigger: 'change' }],
        vlan: [{ required: true, message: '请确认vlan', trigger: 'change' }],
      },
      vpcInfo: [
        { vpcSwitchList: [{}] },
        { vpcSecurityGroupList: [], vpcSwitchList: [{}] },
        { vpcSecurityGroupList: [], vpcSwitchList: [{}] },
        { vpcSecurityGroupList: [], vpcSwitchList: [{}] },
      ],
      tencentVpcInfo: [{ subnetInfo: [{}] }],
      needPublicIpList: [
        { key: 0, label: '否' },
        { key: 1, label: '是' },
      ],
      diskTypeList: [
        { label: '固态硬盘', key: '2', value: 'SSD' },
        { label: '机械硬盘', key: '1', value: 'HDD' },
      ],
      aliyunDiskTypeList: [
        { diskName: '高效云盘', diskType: 'cloud_efficiency', key: 'HDD' },
        { diskName: 'SSD云盘', diskType: 'cloud_ssd', key: 'SSD' },
      ],
      tencentDiskTypeList: [
        { label: '普通云硬盘', value: 'CLOUD_BASIC', key: 'HDD' },
        { label: 'SSD云硬盘', value: 'CLOUD_SSD', key: 'SSD' },
      ],
      instanceTypeList: [{ zoneId: '', zoneInstanceTypeList: [{}] }],
      imageList: [],
      subnetList: [],
      securityGroupList: [],
      preProjectNameLIst: [],
      preStatus: false,
      vpcStatus: false,
      tipsStatus: false,
      modalFormBindServerVisible: false,
      vpcIndex: 4,
      tempSecurityGroupList: [], // 提供工单安全组
      tencentVpcIndex: 3,
      instance_index: 0,
      scrollPage: 1,
      serverScrollPage: 1,
      valueData: '',
      serverValueData: '',
      treePageSize: 50,
      modalFormPreBindServerVisible: false,
      // 前端将后端的vpc嵌套信息进行过滤
      multipleInstanceList: [],
      aliyunSystemDiskTypeDetailList: [],
      aliyunDataDiskTypeDetailList: [],
      // 阿里云vpc下switch信息
      multipleVpcSwitchList: [],
      // 腾讯云vpc下subnet信息
      multipleVpcSubnetList: [],
      preCreateMsgs: [{}],
      // 阿里云磁盘信息
      systemDiskMaxSize: 2048,
      systemDiskMinSize: 20,
      dataDiskMaxSize: 2048,
      dataDiskMinSize: 20,
      //镜像处理
      ipInfo: [],
      showImageTips: false,
      request: {
        pageNo: 1,
        isAdmin: true,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
      },
    }
  },
  created() {
    this.getList()
    this.getBaseInfoList()
    this.startGetAssetListInfo()
    this.getUserBaseInfo()
    this.getProductLineList()
  },
  watch: {
    'temp.content.projectName': {
      handler(value) {
        this.temp.content.approvalData.projectName = value
      },
      immediate: true,
    },
  },
  methods: {
    getProductLineList() {
      getProductLineList().then(response => {
        if (response.Data !== undefined && response.Data !== null) {
          for (const productLine in response.Data.productLineMap) {
            if (Object.hasOwnProperty.call(response.Data.productLineMap, productLine)) {
              const secondaryProductLineList = response.Data.productLineMap[productLine].secondaryProductLine
              let childrenList = []
              for (let i = 0; i < secondaryProductLineList.length; i++) {
                let tmp = {
                  label: secondaryProductLineList[i],
                  value: secondaryProductLineList[i],
                }
                childrenList.push(tmp)
              }
              this.productLineOption.push({
                label: productLine,
                value: productLine,
                children: childrenList,
              })
            }
          }
        }
      })
    },
    startGetAssetListInfo() {
      const sendData = { idc: this.temp.content.idc }
      getAllAssetsIp(sendData).then(response => {
        if (response && response.data && response.data.data) {
          this.serverOption = response.data.data
          this.serverOptionMini = this.serverOption.slice(0, 50)
        }
      })
    },
    splitString(input) {
      const parts = input.split('-')
      const firstPart = parts[0]
      const secondPart = parts.slice(1).join('-')
      return [firstPart, secondPart]
    },
    getAssetListInfo() {
      this.getAllAssetPrice()
      const sendData = { idc: this.temp.content.idc }
      getAllAssetsIp(sendData).then(response => {
        this.serverOption = response.data.data
        this.serverOptionMini = this.serverOption.slice(0, 50)
      })
      if (
        this.temp.content.idc.startsWith('tencent') ||
        this.temp.content.idc.startsWith('aws') ||
        this.temp.content.idc.startsWith('ali')
      ) {
        const [supplier, region] = this.splitString(this.temp.content.idc)
        listAssetSecurityGroupName({ supplier: supplier, region: region }).then(response => {
          this.tempSecurityGroupList = response.Data.data
        })
      }
      if (this.temp.content.idc === 'shanghai8-songjiang' || this.temp.content.idc === 'ucloud-shanghai2-hybrid' || this.temp.content.idc === 'ucloud-shanghai1-zhuanqiao' || this.temp.content.idc === 'sh-office') {
        this.osOptions = [
          {
            value: 'linux',
            label: 'Linux',
            children: [
              {
                value: 'centos7',
                label: 'CentOS 7',
              },
              {
                value: 'ubuntu_server',
                label: 'Ubuntu Server 18.04',
              },
              {
                value: 'ubuntu_server_22.04',
                label: 'Ubuntu Server 22.04',
              },
            ],
          },
          {
            value: 'windows',
            label: 'Windows',
            children: [
              {
                value: 'windows_server_2016',
                label: 'Windows Server 2016',
              },
              {
                value: 'windows_server_2018',
                label: 'Windows Server 2018',
              },
              {
                value: 'other',
                label: '其他',
              },
            ],
          },
        ]
        this.temp.content.os = ['linux', 'centos7']
      }else {
        this.osOptions = [
          {
            value: '云厂商系统',
            label: '云厂商系统',
            children: [
              {
                value: 'linux',
                label: 'Linux',
              },
              {
                value: 'windows',
                label: 'Windows',
              },
            ],
          }
        ]
        this.temp.content.os = ['云厂商系统', 'linux']
      }
    },
    fixedPrice(fixedStr) {
      if (String(fixedStr).includes('.')) {
        if (String(fixedStr).split('.')[1].length > 2) {
          fixedStr = fixedStr.toFixed(2)
        } else if (String(fixedStr).split('.')[1].length == 2) {
          fixedStr = fixedStr
        } else {
          fixedStr = String(fixedStr) + '0'
        }
      } else {
        fixedStr = String(fixedStr) + '.00'
      }
      return fixedStr
    },
    getAllAssetPrice() {
      // 获取价格
      let diskType = this.temp.content.diskType
      if (this.temp.content.idc.startsWith('aliyun')) {
        diskType = this.aliyunDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].diskType
      } else if (this.temp.content.idc.startsWith('tencent')) {
        diskType = this.tencentDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
      }
      const sendCostData = {
        cpu: this.temp.content.serverType[0],
        idc: this.temp.content.idc,
        mem: this.temp.content.serverType[1],
        diskSize: this.temp.content.diskSize,
        diskType: diskType,
        num: this.temp.content.num,
      }
      this.priceLoading = true
      getAssetPrice(sendCostData).then(response => {
        this.temp.content.priceInfo = response.Data
        let fixedStr = this.temp.content.priceInfo.discountPrice + this.temp.content.priceInfo.discountDiskPrice
        this.temp.content.priceInfo.allDiscount = this.fixedPrice(fixedStr)
        let originPrice = this.temp.content.priceInfo.originalPrice + this.temp.content.priceInfo.originalDiskPrice
        this.temp.content.priceInfo.allOrigin = this.fixedPrice(originPrice)
        // this.temp.content.discountPrice = response.Data.discountPrice
        // this.temp.content.originalPrice = response.Data.originalPrice
        this.temp.content.priceMsg = response.Data.msg
        if (this.priceLoading) {
          this.priceLoading = false
        }
      })
    },
    // 默认选择框获取
    getUserBaseInfo() {
      getUserEmailList().then(res => {
        for (var i = 0, len = res.Data.items.length; i < len; i++) {
          const user = {}
          user.value = res.Data.items[i].key
          user.label = res.Data.items[i].key + '(' + res.Data.items[i].value + ')'
          this.principalList.push(user)
          if (i <= 50) {
            this.principalListMini.push(user)
          }
        }
      })
      console.log(this.principalListMini)
    },
    getBaseInfoList() {
      getAssetListProject().then(res => {
        for (var i = 0, len = res.Data.project.length; i < len; i++) {
          var project = {}
          project.value = res.Data.project[i]
          project.label = res.Data.project[i]
          this.projectList.push(project)
        }
      })
      getAssetListApplication().then(res => {
        for (var i = 0, len = res.Data.application.length; i < len; i++) {
          var application = {}
          application.value = res.Data.application[i]
          application.label = res.Data.application[i]
          this.applicationList.push(application)
        }
      })
      /*
      getAssetListOrganization().then(res => {
        for (var i = 0, len = res.Data.organization.length; i < len; i++) {
          var organization = {}
          organization.value = res.Data.organization[i]
          organization.label = res.Data.organization[i]
          this.organizationList.push(organization)
        }
      })
      getAssetListDepartment().then(res => {
        for (var i = 0, len = res.Data.department.length; i < len; i++) {
          var department = {}
          department.value = res.Data.department[i]
          department.label = res.Data.department[i]
          this.departmentList.push(department)
        }
      })
      */
    },
    handleSearch(val) {
      this.valueData = val
      if (!val) {
        this.getUserBaseInfo()
      } else {
        this.principalListMini = []
        this.scrollPage = 1
        this.principalList.forEach(item => {
          if (item.label.indexOf(val) >= 0) {
            if (!this.principalListMini.includes(item.label)) {
              this.principalListMini.push(item)
            }
          }
        })
        this.principalListMini = this.uniqueDicts(this.principalListMini.slice(0, 50))
      }
    },
    handlePopupScrollUser(e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.principalList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.principalList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.principalListMini = newData
        }
      }
    },
    handleServerSearch(val) {
      this.serverOptionMini = []
      this.serverScrollPage = 1
      this.serverOption.forEach(item => {
        if (item.ip.indexOf(val) >= 0 || item.hostname.indexOf(val) >= 0) {
          this.serverOptionMini.push(item)
        }
      })
      this.serverOptionMini = this.serverOptionMini.slice(0, 50)
    },
    handleServerPopupScroll(e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.serverScrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.serverScrollPage = this.serverScrollPage + 1
          const scrollPage = this.serverScrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.serverOption.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.serverOption.length
          }
          // 判断是否有搜索
          if (this.serverValueData) {
            this.serverOption.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.serverOption.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.serverOptionMini = newData
        }
      }
    },
    getList() {
      if (this.$route.query.id) {
        this.id = this.$route.query.id
        getOrderInfo(this.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.content.organization !== undefined && this.temp.content.organization.length >0){
              const bu = this.temp.content.organization[0]
              if (bu === 'DG' && this.temp.content.isDgDataAsset) {
                this.temp.content.costAttr = '开发支出'
              } else if (this.operation.includes(bu)) {
                this.temp.content.costAttr = '运营成本'
              } else {
                this.temp.content.costAttr = '研发成本'
              }
            }
            if (this.temp.content.resultIpList === null) {
              this.temp.content.resultIpList = []
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      } else {
        this.getAllAssetPrice()
      }

      this.GetAssetIps()
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.temp.content.needPreImageIp && this.temp.content.preImageIp === '') {
            this.showImageTips = true
          } else {
            this.showImageTips = false
            try {
              this.checkProductLine()
            } catch (e) {
              this.$message.error(e.toString())
              return
            }
            this.temp.content.dgDataAsset = '否-'
            if (this.temp.content.isDgDataAsset) {
              this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
            }
            if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
              this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
            } else {
              this.temp.content.dgDataAsset += '-'
            }
            this.Date()
            this.node_status = 0
            this.temp.node = 1
            this.temp.status = 1
            this.temp.content.approvalData.instanceType = ''
            this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
            const obj = cloneDeep(this.temp)
            obj.content = { data: JSON.stringify(obj.content) }
            createOrder(obj).then(response => {
              if (response === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else {
                notification.success({
                  message: '创建成功',
                  description: '工单创建成功',
                })
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                this.node_status = 1
                this.$router.push({ path: '/workflow/server-add', query: { id: response.Data.id } })
              }
            })
          }
        }
      })
    },
    calculateIntersection(...arrays) {
      if (arrays.length === 0) {
        return []
      }
      // 获取第一个数组的属性集合
      const propertiesSet = new Set(arrays[0].map(item => JSON.stringify(item)))
      // 从第二个数组开始，逐个计算交集
      for (let i = 1; i < arrays.length; i++) {
        const currentProperties = new Set(arrays[i].map(item => JSON.stringify(item)))
        // 保留与前面数组的交集
        const intersection = arrays[i].filter(item => propertiesSet.has(JSON.stringify(item)))
        // 更新属性集合
        propertiesSet.clear()
        intersection.forEach(item => propertiesSet.add(JSON.stringify(item)))
      }
      // 将结果转换为原始对象的数组形式
      const result = Array.from(propertiesSet).map(item => JSON.parse(item))
      return result
    },
    refreshZoneData(val) {
      this.$forceUpdate()
      this.temp.content.approvalData.instanceType = []
      let instanceTmpList = []
      if (val.length > 1) {
        for (var i = 0; i < val.length; i++) {
          const arr =
            this.instanceTypeList[(this.instanceTypeList || []).findIndex(item => item.zoneId === val[i])]
              .zoneInstanceTypeList
          instanceTmpList.push(arr)
        }
        this.multipleInstanceList = this.calculateIntersection(...instanceTmpList)
      } else {
        this.multipleInstanceList =
          this.instanceTypeList[
            (this.instanceTypeList || []).findIndex(item => item.zoneId === val[0])
          ].zoneInstanceTypeList
      }
      // 处理vpc
      if (this.temp.content.idc.startsWith('ali')) {
        this.multipleVpcSwitchList = this.vpcInfo[this.vpcIndex].vpcSwitchList
          .map(value => value)
          .filter(value => val.includes(value.zoneId))
        if (this.multipleVpcSwitchList.length > 0) {
          this.temp.content.approvalData.defaultVpcSwitchList = this.multipleVpcSwitchList.map(
            switchItem => switchItem.vpcSwitchId + '|' + switchItem.zoneId
          )
        }
      }
      if (this.temp.content.idc.startsWith('tencent')) {
        this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
      }
    },
    getDiskInfo(val) {
      this.temp.content.systemDiskType = ''
      this.aliyunSystemDiskTypeDetailList = []
      this.aliyunDataDiskTypeDetailList = []
      const sendSystemDiskData = {
        idc: this.temp.content.idc,
        instanceType: val,
        resourceName: 'SystemDisk',
        zoneList: this.temp.content.approvalData.zoneIdList,
      }
      const sendDataDiskData = {
        idc: this.temp.content.idc,
        instanceType: val,
        resourceName: 'DataDisk',
        zoneList: this.temp.content.approvalData.zoneIdList,
      }
      aliyunDiskInfo(sendSystemDiskData).then(response => {
        this.aliyunSystemDiskTypeDetailList = response.Data.data
        if (this.aliyunSystemDiskTypeDetailList.length > 0) {
          this.temp.content.systemDiskType =
            this.aliyunSystemDiskTypeDetailList[0].diskType +
            '|' +
            this.aliyunSystemDiskTypeDetailList[0].performanceLevel
        }
      })
      aliyunDiskInfo(sendDataDiskData).then(response => {
        this.aliyunDataDiskTypeDetailList = response.Data.data
      })
    },
    getSystemDiskSize(val) {
      const resData = this.aliyunSystemDiskTypeDetailList.find(
        item => item.diskType + '|' + item.performanceLevel === 'zz'
      )
      this.systemDiskMinSize = resData.diskMinSize
      this.systemDiskMaxSize = resData.diskMaxSize
    },
    getDataDiskSize(val) {
      const resData = this.aliyunDataDiskTypeDetailList.find(
        item => item.diskType + '|' + item.performanceLevel === 'zz'
      )
      this.dataDiskMinSize = resData.diskMinSize
      this.dataDiskMaxSize = resData.diskMaxSize
    },
    // 选择vpc后刷新子网
    refreshData(val) {
      this.$forceUpdate()
      this.vpcIndex = (this.vpcInfo || []).findIndex(item => item.vpcId === val)
      this.tencentVpcIndex = (this.tencentVpcInfo || []).findIndex(item => item.vpcId === val)
      if (this.temp.content.idc.startsWith('tencent')) {
        this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
      }
      if (this.temp.content.idc.startsWith('ali')) {
        this.multipleVpcSwitchList = this.vpcInfo[this.vpcIndex].vpcSwitchList
          .map(value => value)
          .filter(value => val.includes(value.zoneId))
      }
      // this.temp.content.approvalData.defaultSecurityGroupList = []
      this.temp.content.approvalData.defaultSecurityGroupList = this.temp.content.approvalData.chooseSecurityGroupList
      this.temp.content.approvalData.defaultVpcSwitchList = []
      this.temp.content.approvalData.defaultSubnetId = []
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }
          this.Date()
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 2
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    createAsset() {
      this.modalFormBindServerVisible = true
    },
    approveData() {
      if (this.modalFormBindServerVisible === true && this.temp.content.approvalData.keys.length === 0) {
        this.tipsStatus = true
      } else {
        antdFormValidate(this.$refs.orderForm, valid => {
          if (valid) {
            try {
              this.checkProductLine()
            } catch (e) {
              this.$message.error(e.toString())
              return
            }
            this.temp.content.dgDataAsset = '否-'
            if (this.temp.content.isDgDataAsset) {
              this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
            }
            if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
              this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
            } else {
              this.temp.content.dgDataAsset += '-'
            }
            this.Date()
            this.temp.node = 3
            this.temp.status = 10
            if (!this.temp.content.needOtherDisks) {
              this.temp.content.diskType = 'HDD'
              this.temp.content.diskSize = 0
            }
            for (let i = 0; i < this.temp.content.approvalData.keys.length; i++) {
              this.temp.content.approvalData.keys[i] = this.temp.content.approvalData.keys[i].trim()
            }
            this.modalFormBindServerVisible = false
            this.modalFormPreBindServerVisible = false
            // if (this.temp.content.approvalData.instanceType.length !== 0 && this.temp.content.idc.startsWith('aws')) {
            //   this.temp.content.approvalData.instanceType = this.temp.content.approvalData.instanceType[0]
            // }
            this.temp.handler = store.getters.name
            this.temp.handlerEmail = store.getters.email
            const obj = cloneDeep(this.temp)
            obj.content = { data: JSON.stringify(obj.content) }
            approveOrder(obj).then(response => {
              if (response === undefined) {
                notification.error({
                  message: '审批失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (response.data.code === 400) {
                notification.error({
                  message: '审批执行报错',
                  description: response.data.msg,
                })
              } else {
                notification.success({
                  message: '审批成功',
                  description: '管理员审批成功',
                })
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                this.node_status = 1
              }
            })
          }
        })
      }
      // this.$refs.orderForm.refresh()
    },
    preApproveData() {
      antdFormValidate(this.$refs.preDataForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 3
          this.temp.status = 10
          if (!this.temp.content.needOtherDisks) {
            this.temp.content.diskType = 'HDD'
            this.temp.content.diskSize = 0
          }
          if (this.temp.content.idc.startsWith('aws')) {
            let singleSubnetList = []
            if (this.temp.content.num === 1) {
              singleSubnetList.push(this.temp.content.approvalData.defaultSubnetId)
            } else {
              singleSubnetList = this.temp.content.approvalData.defaultSubnetId
            }
            this.temp.content.approvalData.defaultSubnetId = singleSubnetList
          }
          this.modalFormBindServerVisible = false
          this.modalFormPreBindServerVisible = false
          // if (this.temp.content.approvalData.instanceType.length !== 0 && this.temp.content.idc.startsWith('aws')) {
          //   this.temp.content.approvalData.instanceType = this.temp.content.approvalData.instanceType[0]
          // }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutomation = true
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.data.code === 400) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
      // this.$refs.preDataForm.refresh()
    },
    // 预创建获取信息
    preCreateAsset() {
      this.preStatus = true
      this.getBaseListInfo()
    },
    getBaseListInfo() {
      if (this.temp.content.idc.startsWith('aws')) {
        const sendData = {
          cpu: this.temp.content.serverType[0],
          idc: this.temp.content.idc,
          mem: this.temp.content.serverType[1],
        }
        awsBaseInfo(sendData)
          .then(response => {
            this.temp.content.approvalData.instanceType = response.Data.data.instanceType
            this.imageList = response.Data.data.imageList
            this.subnetList = response.Data.data.subnetList
            this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
            this.temp.content.approvalData.defaultSubnetId = response.Data.data.defaultSubnetId
            // this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
            this.temp.content.approvalData.defaultSecurityGroupList =
              this.temp.content.approvalData.chooseSecurityGroupList.concat(response.Data.data.defaultSecurityGroupList)
            this.securityGroupList = response.Data.data.securityGroupList
            this.instanceTypeList = response.Data.data.instanceTypeList
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else if (this.temp.content.idc === 'ucloud-shanghai2-hybrid') {
        const sendData = {
          cpu: this.temp.content.serverType[0],
          business: this.temp.content.business,
          diskType: this.temp.content.diskType,
          diskSize: parseInt(this.temp.content.diskSize),
          application: this.temp.content.application[0],
          os: this.temp.content.os[0],
          server_num: this.temp.content.num,
          env: this.temp.content.env,
          mem: parseInt(this.temp.content.serverType[1]),
        }
        vmBaseInfo(sendData)
          .then(response => {
            // this.temp.content.projectName = response.Data.data.projectName
            this.temp.content.approvalData.hostIpList = response.Data.data.hostIpList
            this.temp.content.approvalData.vlan = response.Data.data.vlan
            this.temp.content.approvalData.msgs = response.Data.data.msgs
            if (this.temp.content.approvalData.msgs.length !== 0) {
              if (this.temp.content.approvalData.msgs[0].type === undefined) {
                this.preCreateMsgs = [{ color: 'green', msg: '查询无异常' }]
              } else {
                const msgs = response.data.data.msgs
                msgs.forEach(function (item, index) {
                  if (item.type === 'error') {
                    item.color = 'red'
                  } else if (item.type === 'warning') {
                    item.color = 'orange'
                  }
                })
                this.preCreateMsgs = msgs
              }
            } else {
              this.preCreateMsgs = [{ color: 'green', msg: '查询无异常' }]
            }
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else if (this.temp.content.idc.startsWith('ali')) {
        if (this.temp.content.idc === 'aliyun-cs-cn-shanghai') {
          this.vpcIndex = 0
        }
        const sendData = {
          cpu: this.temp.content.serverType[0],
          diskType: this.temp.content.diskType,
          diskSize: parseInt(this.temp.content.diskSize),
          idc: this.temp.content.idc,
          mem: this.temp.content.serverType[1],
        }
        aliyunBaseInfo(sendData)
          .then(response => {
            this.modalFormPreBindServerVisible = true
            if (response.Data.data.instanceTypeList !== null) {
              // this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
              this.temp.content.approvalData.defaultSecurityGroupList =
                this.temp.content.approvalData.chooseSecurityGroupList.concat(
                  response.Data.data.defaultSecurityGroupList
                )
              this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
              this.temp.content.approvalData.defaultVpcId = response.Data.data.defaultVpcId
              this.temp.content.approvalData.defaultVpcSwitchList = response.Data.data.defaultVpcSwitchList
              this.imageList = response.Data.data.imageList
            }
            this.temp.content.approvalData.defaultZoneId = response.Data.data.zoneId
            this.temp.content.approvalData.instanceType = response.Data.data.instanceType
            // 获取磁盘相关
            const sendSystemDiskData = {
              idc: this.temp.content.idc,
              instanceType: response.Data.data.instanceType,
              resourceName: 'SystemDisk',
              zoneList: [response.Data.data.zoneId],
            }
            const sendDataDiskData = {
              idc: this.temp.content.idc,
              instanceType: response.Data.data.instanceType,
              resourceName: 'DataDisk',
              zoneList: [response.Data.data.zoneId],
            }
            aliyunDiskInfo(sendSystemDiskData).then(response => {
              this.aliyunSystemDiskTypeDetailList = response.Data.data
              if (this.aliyunSystemDiskTypeDetailList.length > 0) {
                this.temp.content.systemDiskType =
                  this.aliyunDataDiskTypeDetailList[0].diskType +
                  '|' +
                  this.aliyunDataDiskTypeDetailList[0].performanceLevel
              }
            })
            aliyunDiskInfo(sendDataDiskData).then(response => {
              this.aliyunDataDiskTypeDetailList = response.Data.data
            })
            this.$nextTick(() => {
              this.temp.content.approvalData.zoneIdList.push(response.Data.data.zoneId)
              this.vpcInfo = response.Data.data.vpcInfo
              this.instanceTypeList = response.Data.data.instanceTypeList
              this.aliyunDiskTypeList = response.Data.data.diskTypeList
              // 初始化虚拟机交换id
              this.multipleVpcSwitchList = this.vpcInfo[this.vpcIndex].vpcSwitchList
                .map(value => value)
                .filter(value => this.temp.content.approvalData.zoneIdList.includes(value.zoneId))
              if (this.multipleVpcSwitchList.length > 0) {
                this.temp.content.approvalData.defaultVpcSwitchList = [
                  this.multipleVpcSwitchList[0].vpcSwitchId + '|' + this.multipleVpcSwitchList[0].zoneId,
                ]
              }
              // instance
              this.multipleInstanceList =
                this.instanceTypeList[
                  (this.instanceTypeList || []).findIndex(
                    item => item.zoneId === this.temp.content.approvalData.zoneIdList[0]
                  )
                ].zoneInstanceTypeList
            })
            this.preStatus = false
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else if (this.temp.content.idc.startsWith('tencent')) {
        const sendData = {
          cpu: this.temp.content.serverType[0],
          mem: this.temp.content.serverType[1],
        }
        tencentBaseInfo(sendData)
          .then(response => {
            this.temp.content.approvalData.instanceType = response.Data.data.instanceType
            this.temp.content.approvalData.defaultSecurityGroupList =
              this.temp.content.approvalData.chooseSecurityGroupList.concat(response.Data.data.defaultSecurityGroupList)
            // this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
            this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
            this.temp.content.approvalData.defaultVpcId = response.Data.data.defaultVpcId
            this.temp.content.approvalData.defaultSubnetId = response.Data.data.defaultSubnetList
            this.temp.content.approvalData.defaultZoneId = response.Data.data.zoneId
            this.imageList = response.Data.data.imageList
            this.securityGroupList = response.Data.data.securityGroupList
            this.$nextTick(() => {
              this.tencentVpcInfo = response.Data.data.vpcInfo
              this.instanceTypeList = response.Data.data.instanceTypeList
              this.temp.content.approvalData.zoneIdList.push(response.Data.data.zoneId)
              // 初始化subnet交换id
              this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
            })
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else {
        this.preStatus = false
        this.modalFormPreBindServerVisible = true
      }
    },
    getVpcList(value, e) {
      const vpcList = []
      this.vpcStatus = true
      for (var i in e) {
        vpcList.push(e[i].vpc)
      }
      return vpcList
    },
    async getSecurityLists(value, e) {
      this.vpcStatus = true
      const vpcList = this.getVpcList(value, e)
      const sendData = {
        idc: this.temp.content.idc,
        defaultSubnetList: vpcList,
      }
      awsSecurityBaseInfo(sendData).then(response => {
        this.securityGroupList = response.Data.data.securityGroupList
        this.securityGroupList = this.securityGroupList.filter((item, index) => {
          const str = JSON.stringify(item)
          return (
            index ===
            this.securityGroupList.findIndex(obj => {
              return JSON.stringify(obj) === str
            })
          )
        })
        this.temp.content.approvalData.defaultSecurityGroupList =
          this.temp.content.approvalData.chooseSecurityGroupList.concat(response.Data.data.defaultSecurityGroupList)
        // this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
        this.vpcStatus = false
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
            response.Data.content = JSON.parse(response.Data.content.data)
            this.temp = response.Data
            this.node_status = 1
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    uniqueDicts(arr) {
      const uniqueDictsSet = new Set()
      return arr.filter(item => {
        const dictString = JSON.stringify(item)
        return uniqueDictsSet.has(dictString) ? false : uniqueDictsSet.add(dictString)
      })
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    filterOption: filterLabelValue,
    checkProductLine() {
      if (
        this.temp.content.costAttr === '研发成本' ||
        this.temp.content.business === '大数据' ||
        this.temp.content.business === '搜索(DG)'
      ) {
        return
      }
      if (this.temp.content.productLineList.length > 5) {
        throw new Error('目前最多支持5个产品线，如有需求请联系运维管理员')
      }
      let sumRatios = 0
      let productLineArr = []
      let existArr = []
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (
          this.temp.content.productLineList[i].productLineNameList === undefined ||
          this.temp.content.productLineList[i].productLineNameList.length < 2
        ) {
          throw new Error('请填充完所有产品线空格')
        }
        if (existArr.includes(this.temp.content.productLineList[i].productLineNameList[1])) {
          throw new Error('产品线重复：' + this.temp.content.productLineList[i].productLineNameList[1])
        }
        productLineArr.push(
          this.temp.content.productLineList[i].productLineNameList[1] + ':' + this.temp.content.productLineList[i].ratio
        )
        sumRatios += this.temp.content.productLineList[i].ratio
        existArr.push(this.temp.content.productLineList[i].productLineNameList[1])
      }
      sumRatios = sumRatios.toFixed(4)
      if (sumRatios !== '1.0000') {
        const errStr = '比例之和： ' + sumRatios + '≠ 1'
        throw new Error(errStr)
      }
      this.temp.content.productLine = productLineArr.join(',')
      console.log(this.temp.content)
    },
    addRow() {
      this.temp.content.productLineList.push({
        productLineNameList: [],
        ratio: 0,
      })
    },
    // 移除某行
    removeRow(i) {
      if (this.temp.content.productLineList.length > 1) {
        this.temp.content.productLineList.splice(i, 1)
      }
    },
    // 费用负责人联动
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        var arry = response.Data.data
        for (var i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    getUserTeamListMethod(userEmail) {
      this.userTeamList = []
      if (userEmail === '') {
        return
      }
      GetUserOrgAllInfo({ email: userEmail }).then(response => {
        this.businessList = response.Data.business
        this.temp.content.business = this.businessList[0]
        if (this.temp.content.business === 'DG') {
          this.temp.content.isDgBusiness = true
        }
        this.organizationOptions = response.Data.orgCas
        this.temp.content.organization = []
        if (this.organizationOptions[0].label !== undefined && this.organizationOptions[0].label !== null) {
          this.temp.content.organization.push(this.organizationOptions[0].label)
          if (this.organizationOptions[0].children !== null && this.organizationOptions[0].children[0].label !== null) {
            this.temp.content.organization.push(this.organizationOptions[0].children[0].label)
            if (
              this.organizationOptions[0].children[0].children !== null &&
              this.organizationOptions[0].children[0].children[0].label !== null
            ) {
              this.temp.content.organization.push(this.organizationOptions[0].children[0].children[0].label)
            }
          }
        }
        if (this.operation.includes(this.temp.content.business)) {
          this.temp.content.costAttr = '运营成本'
          if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
            this.temp.content.costAttr = '开发支出'
          }
          this.temp.content.productLineList = [
            {
              productLineNameList: [],
              ratio: 0,
            },
          ]
        } else {
          this.temp.content.costAttr = '研发成本'
        }
      })
    },
    handleDGSpecificBusiness() {
      if (this.temp.content.dgDataAssetClassification !== '') {
        this.temp.content.dgSpecificBusiness = '数据中心'
      }
    },
    validateNumberSum(_, currentIndex, callback) {
      callback = antdFormValidateCallback
      let total = 1
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (i === currentIndex) {
          continue
        }
        total -= this.temp.content.productLineList[i].ratio
      }
      if ((total - this.temp.content.productLineList[currentIndex].ratio).toFixed(4) < 0) {
        this.temp.content.productLineList[currentIndex].ratio = 0
        const totalFixed = total.toFixed(4)
        return callback(new Error('不得超过' + totalFixed))
      }
      return callback()
    },
    changeCostAttr() {
      if (this.operation.includes(this.temp.content.business)) {
        this.temp.content.costAttr = '运营成本'
        if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
          this.temp.content.costAttr = '开发支出'
        }
        // this.temp.content.productLineList = [
        //   {
        //     productLineNameList: [],
        //     ratio: 0,
        //   },
        // ]
      } else {
        this.temp.content.costAttr = '研发成本'
      }
    }
  },
}
</script>

<style scoped></style>
