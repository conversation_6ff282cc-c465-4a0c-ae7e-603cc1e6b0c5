<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item v-if="this.source === ''" label="IP" name="ip">
              <a-select
                v-model:value="temp.content.ip"
                style="width: 100%"
                @popupScroll="handlePopupScroll"
                @search="handleSearchIp"
                @change="handleChangeIp"
                placeholder="选择IP"
                show-search
                allowClear
              >
                <a-select-option v-for="i in ipInfo" :key="i.ip">
                  {{ i.ip + '(' + i.hostname + ')' }}
                </a-select-option>
              </a-select>
              <span v-if="temp.content.ip === undefined" style="font-size: 12px; color: red; margin-left: 3px">
                注意：仅显示自己负责的服务器！未找到服务器请找运维确认服务器负责人
              </span>
            </a-form-model-item>
            <a-form-model-item v-else label="IP" name="ip">{{ temp.content.ip }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="服务器名" name="hostname">{{ temp.content.hostname }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="原配置">
              <span style="font-size: 20px; color: grey">
                {{ this.temp.content.oldCpu }}核/{{ this.temp.content.oldMem }}G
              </span>
            </a-form-model-item>
            <a-form-model-item label="CPU(核)" name="cpu">
              <a-select v-model:value="temp.content.cpu" placeholder="请选择CPU大小">
                <a-select-option value="1">1</a-select-option>
                <a-select-option value="2">2</a-select-option>
                <a-select-option value="4">4</a-select-option>
                <a-select-option value="8">8</a-select-option>
                <a-select-option value="16">16</a-select-option>
                <a-select-option value="32">32</a-select-option>
                <a-select-option value="64">64</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="额外数据盘" name="needOtherDisks">
              <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
                <a-radio-button value="0">否</a-radio-button>
                <a-radio-button value="1">是</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.needOtherDisks === '1'">
                <a-input-group compact>
                  <a-select v-model:value="temp.content.diskType">
                    <a-select-option value="HDD">机械硬盘(GB)</a-select-option>
                    <a-select-option value="SSD">固态硬盘(GB)</a-select-option>
                  </a-select>
                  <a-input-number v-model:value="temp.content.diskSize" style="width: 30%" />
                </a-input-group>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="内存(GB)" name="memory">
              <a-select v-model:value="temp.content.memory" placeholder="请选择内存大小">
                <a-select-option value="1">1</a-select-option>
                <a-select-option value="2">2</a-select-option>
                <a-select-option value="4">4</a-select-option>
                <a-select-option value="8">8</a-select-option>
                <a-select-option value="16">16</a-select-option>
                <a-select-option value="32">32</a-select-option>
                <a-select-option value="64">64</a-select-option>
                <a-select-option value="128">128</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请理由" name="reason">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/asset-list">取消</router-link>
          </tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="primary" @click="preServerModify" style="margin-left: 10px">预修改</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <div>
      <a-modal v-model:visible="dialogFormPreServerModifyVisible" title="服务器预修改" @ok="approvePreData">
        <a-form-model-item label="预修改检查结果">
          <a-tag v-for="(i, index) in preShutdownMsgs" :key="index" :color="i.color">
            {{ index + 1 }}: {{ i.msg }}
          </a-tag>
        </a-form-model-item>
      </a-modal>
    </div>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { getAssetInfo } from '@/api/asset'
import { preServerModifyInfo } from '@/api/cmdb/workflow/order'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getAllAssetsIp } from '@/api/workflow/asset_automation'
// import ipInfo from "lodash";

export default {
  name: 'ServerModifyConfig',
  data() {
    return {
      dialogFormPreServerModifyVisible: false,
      preShutdownMsgs: [{}],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      source: 'server',
      tempIpData: [],
      ipInfo: [],
      request: {
        pageNo: 1,
        isAdmin: false,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
      },
      temp: {
        id: '',
        orderType: '服务器配置变更',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          id: undefined,
          ip: undefined,
          hostname: undefined,
          cpu: undefined,
          memory: undefined,
          needOtherDisks: '0',
          diskType: 'HDD',
          diskSize: 0,
          reason: undefined,
          autoModify: false,
          assetId: 0,
          oldCpu: 0,
          oldMem: 0,
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        ip: [{ required: true, message: '请填写IP', trigger: 'blur' }],
        'content.hostname': [{ required: true, message: '请填写主机名', trigger: 'blur' }],
        cpu: [{ required: true, message: '请选择CPU', trigger: 'blur' }],
        memory: [{ required: true, message: '请选择内存', trigger: 'blur' }],
        'content.needOtherDisks': [{ required: true, message: '请选择是否需要数据盘', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.assetId) {
        getAssetInfo(this.$route.query.assetId).then(response => {
          this.temp.content.id = response.Data.id + ''
          this.temp.content.ip = response.Data.ip
          this.temp.content.hostname = response.Data.hostname
          this.temp.content.cpu = response.Data.cpu
          this.temp.content.memory = response.Data.memory
          this.temp.content.assetId = this.$route.query.assetId
          this.temp.content.oldCpu = response.Data.cpu
          this.temp.content.oldMem = response.Data.memory
        })
        this.nodeStatus = 1
      } else if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else {
        this.source = ''
        this.GetAssetIps()
      }
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    preServerModify() {
      this.dialogFormPreServerModifyVisible = true
      this.getPreServerModifyInfo()
    },
    getPreServerModifyInfo() {
      const sendData = {
        disk_size: this.temp.content.diskSize,
        ip: this.temp.content.ip,
        disk_type: this.temp.content.diskType,
        cpu: this.temp.content.cpu,
        mem: this.temp.content.mem,
      }
      preServerModifyInfo(sendData).then(response => {
        const msgs = response.data.data.msgs
        if (msgs.length !== 0) {
          if (msgs[0].type === undefined) {
            this.preShutdownMsgs = [{ type: 'green', msg: '查询无异常' }]
          } else {
            const msgs = response.data.data.msgs
            msgs.forEach(function (item, index) {
              if (item.type === 'error') {
                item.color = 'red'
              } else if (item.type === 'warning') {
                item.color = 'orange'
              }
            })
            this.preShutdownMsgs = msgs
          }
        } else {
          this.preShutdownMsgs = [{ type: 'green', msg: '查询无异常' }]
        }
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.TempToString()
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              this.temp = response.Data
              this.$router.push({ path: '/workflow/server-modify-config', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.TempToString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.TempToString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approvePreData() {
      this.dialogFormPreServerModifyVisible = false
      this.temp.node = 3
      this.temp.status = 10
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content.autoModify = true
      this.TempToString()
      if (this.temp.content.needOtherDisks !== '1') {
        this.temp.content.diskType = 'HDD'
        this.temp.content.diskSize = 0
      }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          this.temp = response.Data
          notification.success({
            message: '审批成功',
            description: '管理员审批成功',
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.TempToString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    TempToString() {
      this.temp.content.cpu = this.temp.content.cpu.toString()
      this.temp.content.memory = this.temp.content.memory.toString()
      this.temp.content.diskSize = this.temp.content.diskSize.toString()
      this.temp.content.autoModify = this.temp.content.autoModify.toString()
      // 兼容旧工单
      if (this.temp.content.oldMem !== undefined) {
        this.temp.content.oldMem = this.temp.content.oldMem.toString()
        this.temp.content.oldCpu = this.temp.content.oldCpu.toString()
        this.temp.content.assetId = this.temp.content.assetId.toString()
      }
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    handleChangeIp(value) {
      const foundObject = this.ipInfo.find(obj => obj.ip === value)
      this.temp.content.hostname = foundObject.hostname
      this.temp.content.oldCpu = foundObject.cpu
      this.temp.content.oldMem = foundObject.memory
      this.temp.content.assetId = foundObject.uuid
    },
  },
}
</script>

<style scoped></style>
