<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全管理员审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="区域">
          <a-radio-group v-model:value="temp.content.origin" button-style="solid">
            <a-radio-button value="Ningxia">宁夏</a-radio-button>
            <a-radio-button value="US">美国</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="表名">
          <a-radio-group v-model:value="temp.content.tableName" button-style="solid" @change="ChangeTableName">
            <a-radio-button value="ts_cs_ocr">ts_cs_ocr_0-99</a-radio-button>
            <a-radio-button value="ts_cs_file">ts_cs_file_0-99</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="并发数">
          <a-select v-model:value="temp.content.fork">
            <a-select-option value="1">1</a-select-option>
            <a-select-option value="2">2</a-select-option>
            <a-select-option value="3">3</a-select-option>
            <a-select-option value="4">4</a-select-option>
            <a-select-option value="5">5</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="查询字段" name="column">
          <a-input v-model:value="temp.content.column" @change="ChangeColumn" />
        </a-form-model-item>
        <a-form-model-item label="查询条件" name="condition">
          <a-textarea v-model:value="temp.content.condition" @change="ChangeCondition" />
        </a-form-model-item>
        <a-form-model-item label="SQL语句" name="sql">
          <a-span>{{ temp.content.sql }}</a-span>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" style="width: 40%" :auto-size="{ minRows: 2, maxRows: 2 }" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="反馈信息">
          <a-textarea v-model:value="temp.content.commit" style="width: 40%" :auto-size="{ minRows: 2, maxRows: 2 }" />
        </a-form-model-item>
        <div v-if="temp.content.status == '4' && temp.node > 3">
          <a-form-model-item label="执行情况">
            <a-tag color="orange">执行中</a-tag>
          </a-form-model-item>
          <a-form-model-item v-if="temp.node > 3 && DBAUser.includes(localUser)" label="重试">
            <tx-button type="danger" icon="rollback" @click="failedRetry">重试</tx-button>
          </a-form-model-item>
        </div>
        <div v-else-if="temp.content.status == '2' && temp.node > 3">
          <a-form-model-item label="执行情况">
            <a-tag color="green">成功</a-tag>
          </a-form-model-item>
          <a-form-model-item label="下载地址">
            <a-textarea
              v-model:value="temp.content.downloadURL"
              style="width: 40%"
              :auto-size="{ minRows: 4, maxRows: 6 }"
            />
          </a-form-model-item>
          <a-form-model-item v-if="temp.node > 3 && DBAUser.includes(localUser)" label="文件密码">
            {{ temp.content.Password }}
          </a-form-model-item>
          <a-form-model-item label="错误信息">
            <tx-button v-if="temp.content.errList != 'null'" type="danger" icon="rollback" @click="failedRetry">
              重试
            </tx-button>
            <br />
            <a-textarea
              v-model:value="temp.content.errList"
              style="width: 40%"
              :auto-size="{ minRows: 4, maxRows: 6 }"
            />
          </a-form-model-item>
        </div>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 4 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { RetryCsScan } from '@/api/db/cs_scan'
export default {
  name: 'CSSyncScan',
  components: {},

  data: function () {
    this.lastFetchId = 0
    return {
      fetching: false,
      value: [],
      endOpen: false,
      localUser: store.getters.email,
      IpDbNamesList: [],
      DBAUser: '<EMAIL>|<EMAIL>|<EMAIL>|<EMAIL>',
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: 'CS同步库扫描',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          origin: 'Ningxia',
          tableName: 'ts_cs_ocr',
          fork: '1',
          column: '',
          sql: 'select {查询字段} from  {ts_cs_ocr_0-99} where {查询条件};',
          reason: '',
          downloadURL: '',
          status: '5',
          Password: '',
          errList: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        checkedValue: [{ required: true, message: '请选择数据库', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        column: [{ required: true, message: '请填写查询字段', trigger: 'blur' }],
        condition: [{ required: true, message: '请填写查询条件语句', trigger: 'blur' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    ChangeTableName() {
      if (this.temp.content.tableName === 'ts_cs_ocr') {
        const regex = /ts_cs_file_0-99/g
        this.temp.content.sql = this.temp.content.sql.replace(regex, 'ts_cs_ocr_0-99')
      } else {
        const regex = /ts_cs_ocr_0-99/g
        this.temp.content.sql = this.temp.content.sql.replace(regex, 'ts_cs_file_0-99')
      }
    },
    ChangeColumn() {
      const regex = /select.*?from/g
      if (this.temp.content.column !== '') {
        this.temp.content.sql = this.temp.content.sql.replace(regex, 'select ' + this.temp.content.column + ' from')
      } else {
        this.temp.content.sql = this.temp.content.sql.replace(regex, 'select {查询字段} from')
      }
    },
    ChangeCondition() {
      let singleLineString = this.temp.content.condition.replace(/\s*\n\s*/g, ' ')

      const regex = / where(.*(\n))*.*;/g
      if (this.temp.content.condition !== '') {
        this.temp.content.sql = this.temp.content.sql.replace(regex, ' where ' + singleLineString + ';')
      } else {
        this.temp.content.sql = this.temp.content.sql.replace(regex, ' where {查询条件} ;')
      }
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    dbNameChange(value) {
      this.temp.content.ip = value.split('---')[0]
      this.temp.content.dbName = value.split('---')[1]
    },
    failedRetry() {
      let sendBody = {}
      sendBody.orderId = this.temp.id
      RetryCsScan(sendBody).then(response => {
        if (response.Data.message == 'ok') {
          notification.success({
            message: '重试成功',
            description: '重试成功',
          })
        } else {
          notification.error({
            message: '重试失败',
            description: response.Data.Message,
          })
        }
        // this.$router.push({ path: '/workflow/cs-scan', query: { id: response.Data.id } })
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/cs-scan', query: { id: response.Data.id } })
            }
            this.temp = response.Data
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    safeApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.status = "4"
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 4
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
