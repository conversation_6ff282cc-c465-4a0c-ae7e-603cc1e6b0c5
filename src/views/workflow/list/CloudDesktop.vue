<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="name" label="云桌面名称">
              <a-input v-model:value="temp.content.name" placeholder="远程工作站自定义名称" allow-clear>
                <template #suffix>
                  <a-tooltip title="云桌面名称，可自定义">
                    <a-icon type="info-circle" style="color: rgba(0, 0, 0, 0.45)" />
                  </a-tooltip>
                </template>
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="操作系统" name="os">
              <a-radio-group v-model:value="temp.content.os" button-style="solid">
                <a-radio-button value="ubuntu">Ubuntu</a-radio-button>
                <a-radio-button value="windows">Windows</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.os === 'windows'">
                <p>
                  Windows云桌面需要购买License。费用是Ubuntu一倍，License成本由申请人所属事业部承担。建议优先选择Ubuntu。
                </p>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="配置规格" name="serverType">
              <a-cascader
                v-model:value="temp.content.serverType"
                :options="serverTypeOptions"
                placeholder="请选择CPU/内存"
                change-on-select
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="额外数据盘" name="needOtherDisks">
              <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
                <a-radio-button value="0">否</a-radio-button>
                <a-radio-button value="1">是</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.needOtherDisks === '1'">
                <a-input-group compact>
                  <a-select v-model:value="temp.content.diskType">
                    <a-select-option value="HDD">机械硬盘(GB)</a-select-option>
                    <a-select-option value="SSD">固态硬盘(GB)</a-select-option>
                  </a-select>
                  <a-input-number v-model:value="temp.content.diskSize" style="width: 30%" />
                </a-input-group>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="账号类型" name="accountType">
              <a-radio-group v-model:value="temp.content.accountType" button-style="solid">
                <a-radio-button value="user">本人账号</a-radio-button>
                <a-radio-button value="common">公共账号</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.accountType === 'common'">
                <a-form-model-item label="名称">
                  <a-input
                    v-model:value="temp.content.accountName"
                    placeholder="以_rdp后缀结尾。例：common_rdp"
                    style="width: 75%"
                  />
                </a-form-model-item>
                <a-form-model-item label="授权人员">
                  <AllUser
                    :placeholder="'可按邮箱搜索'"
                    :checkValue="temp.content.authUsers"
                    @selectChange="orgChange"
                  />
                  <!-- <a-select
                    mode="multiple"
                    placeholder="输入邮箱搜索并选择(多选)"
                    v-model:value="temp.content.authUsers"
                    style="width: 75%"
                    :showSearch="true"
                    @search="searchUserEmailMethod"
                  >
                    <a-select-option v-for="item in userEmailList" :key="item" :value="item">
                      {{ item }}
                    </a-select-option>
                  </a-select> -->
                </a-form-model-item>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" placeholder="申请的用途或理由说明" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" :auto-size="{ minRows: 3 }" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            localUser !== '<EMAIL>' &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="primary" @click="preApproveData" style="margin-left: 10px">预配置</tx-button>
          <tx-button type="primary" @click="preCreateApproveData" :loading="preStatus" style="margin-left: 10px">
            预创建
          </tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal
      title="云桌面关系绑定"
      :visible="preApproveVisible"
      @ok="autoApproveData"
      width="650px"
      @cancel="preApproveVisible = false"
    >
      <a-form-model ref="dataForm" :rules="rules" :model="temp.content" style="min-width: 200px; margin-left: 50px">
        <a-form-model-item label="云桌面IP">
          <a-input v-model:value="temp.content.ip" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="云桌面资源ID">
          <a-input v-model:value="temp.content.desktopId" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="代理信息" name="business">
          <a-select v-model:value="temp.content.idc">
            <a-select-option value="ucloud-shanghai2-hybrid">混合云</a-select-option>
            <a-select-option value="tencent">腾讯云</a-select-option>
            <a-select-option value="sh-office">云立方</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!--    服务预创建-->
    <a-modal
      title="服务器预创建"
      :visible="preCreateApproveVisible"
      @ok="autoCreateApproveData"
      width="800px"
      @cancel="preCreateApproveVisible = false"
    >
      <a-form-model
        ref="preDataForm"
        :rules="tencentRules"
        :model="temp.content.approvalData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="项目名称" name="projectName">
          <a-input v-model:value="temp.content.projectName" placeholder="默认为空！" style="width: 500px" />
        </a-form-model-item>
        <a-form-model-item label="可用区" name="defaultZoneId">
          <a-select
            v-model:value="temp.content.approvalData.zoneIdList"
            filterable
            allow-create
            mode="multiple"
            placeholder="请选择"
            style="width: 500px"
            @change="refreshZoneData"
          >
            <a-select-option v-for="item in instanceTypeList" :key="item.zoneId" :value="item.zoneId">
              {{ item.zoneId }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="实例类型" name="instanceType">
          <a-select
            v-model:value="temp.content.approvalData.instanceType"
            filterable
            allow-create
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option v-for="item in multipleInstanceList" :key="item.instanceType" :value="item.instanceType">
              {{ item.instanceType }} ( {{ item.label }} )
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="镜像名称" name="defaultImageId">
          <a-select
            v-model:value="temp.content.approvalData.defaultImageId"
            filterable
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option v-for="item in imageList" :key="item.imageId" :value="item.imageId">
              {{ item.imageName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="VPC名称" name="defaultVpcId">
          <a-select
            v-model:value="temp.content.approvalData.defaultVpcId"
            filterable
            placeholder="请选择"
            style="width: 500px"
            @change="refreshData"
          >
            <a-select-option v-for="item in tencentVpcInfo" :key="item.vpcId" :value="item.vpcId">
              {{ item.vpcName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="Subnet" name="defaultVpcSubnetList">
          <a-select
            v-model:value="temp.content.approvalData.defaultSubnetId"
            filterable
            mode="multiple"
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option
              v-for="item in multipleVpcSubnetList"
              :key="item.subnetId + '|' + item.zone"
              :value="item.subnetId + '|' + item.zone"
            >
              {{ subnetOptionsWithDisplayValue[item.subnetId + '|' + item.zone] }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="安全组" name="defaultSecurityGroupList">
          <a-select
            v-model:value="temp.content.approvalData.defaultSecurityGroupList"
            filterable
            mode="multiple"
            placeholder="请选择"
            style="width: 500px"
          >
            <a-select-option
              v-for="item in securityGroupList"
              :key="item.securityGroupId"
              :value="item.securityGroupId"
            >
              {{ item.securityGroupName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="磁盘类型">
          <a-select v-model:value="temp.content.diskType" placeholder="为空则不添加额外磁盘" allowClear>
            <a-select-option v-for="item in tencentDiskTypeList" :key="item.key" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="磁盘大小">
          <a-input v-model:value="temp.content.diskSize" placeholder="" />
        </a-form-model-item>
        <a-form-model-item label="是否初始化">
          <a-radio-group v-model:value="temp.content.approvalData.needInit" button-style="solid">
            <a-radio-button :value="0">否</a-radio-button>
            <a-radio-button :value="1">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否安裝HIDS">
          <a-radio-group v-model:value="temp.content.approvalData.hidsInit" button-style="solid">
            <a-radio-button :value="0">否</a-radio-button>
            <a-radio-button :value="1">是</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getUserList } from '@/api/permission/user'
import AllUser from '@/views/comp/allOrgUser/index.vue'
import { tencentBaseInfo } from '@/api/workflow/asset_automation'
import cloneDeep from 'lodash.clonedeep'
import router from '@/router'
const serverTypeOptions = [
  {
    value: '1',
    label: '1vCPU',
    children: [
      {
        value: '1',
        label: '1GiB',
      },
      {
        value: '2',
        label: '2GiB',
      },
      {
        value: '4',
        label: '4GiB',
      },
    ],
  },
  {
    value: '2',
    label: '2vCPU',
    children: [
      {
        value: '2',
        label: '2GiB',
      },
      {
        value: '4',
        label: '4GiB',
      },
      {
        value: '8',
        label: '8GiB',
      },
    ],
  },
  {
    value: '4',
    label: '4vCPU',
    children: [
      {
        value: '4',
        label: '4GiB',
      },
      {
        value: '8',
        label: '8GiB',
      },
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '8',
    label: '8vCPU',
    children: [
      {
        value: '8',
        label: '8GiB',
      },
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '16',
    label: '16vCPU',
    children: [
      {
        value: '16',
        label: '16GiB',
      },
      {
        value: '32',
        label: '32GiB',
      },
    ],
  },
  {
    value: '32',
    label: '32vCPU',
    children: [
      {
        value: '32',
        label: '32GiB',
      },
      {
        value: '64',
        label: '64GiB',
      },
    ],
  },
]
export default {
  name: 'CloudDesktop',
  data() {
    return {
      localUser: store.getters.email,
      serverTypeOptions,
      userEmailList: [],
      time_now: '',
      preApproveVisible: false,
      preCreateApproveVisible: false,
      node_status: 1,
      tencentVpcIndex: 2,
      temp: {
        id: '',
        orderType: '远程工作站',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: store.getters.email,
          email: '',
          os: 'ubuntu',
          serverType: ['4', '8'],
          needOtherDisks: '0',
          diskType: 'HDD',
          diskSize: 0,
          ip: '',
          desktopId: '',
          accountType: 'user',
          accountName: '',
          authUsers: [],
          isAutoConfig: false,
          isAutoCreate: false,
          isAutomation: false,
          reason: '',
          // 自动化创建相关参数
          idc: 'tencent',
          org: 'AIM',
          dep: '运维',
          num: 1,
          env: 'online',
          application: ['云桌面'],
          project: '云桌面',
          isDb: false,
          preInstanceType: '',
          principal: [store.getters.email],
          projectName: '',
          needPublicIp: 0,
          approvalData: {
            keys: [],
            needInit: 0,
            hidsInit: 0,
            isDesktop: 1,
            desktopType: 'personal',
            vlan: 15,
            hostIpList: [],
            instanceType: [],
            defaultImageId: '',
            defaultSubnetId: [],
            defaultSecurityGroupList: [],
            defaultVpcSwitchList: [],
            defaultVpcId: '',
            defaultZoneId: '',
            zoneIdList: [],
          },
        },
        timeline: [],
        comment: '',
      },
      tencentVpcInfo: [{ subnetInfo: [{}] }],
      tencentDiskTypeList: [
        { label: '普通云硬盘', key: 'CLOUD_BASIC', value: 'HDD' },
        { label: 'SSD云硬盘', key: 'CLOUD_SSD', value: 'SSD' },
      ],
      rules: antdFormRulesFormat({
        'content.name': [{ required: true, message: '请填写云桌面自定义名称', trigger: 'blur' }],
        'content.serverType': [{ required: true, message: '请选择配置规格', trigger: 'blur' }],
        'content.os': [{ required: true, message: '请选择操作系统', trigger: 'blur' }],
        'content.needOtherDisks': [{ required: true, message: '请选择是否需要数据盘', trigger: 'change' }],
        'content.accountType': [{ required: true, message: '请选择账号类型', trigger: 'change' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      tencentRules: antdFormRulesFormat({
        // 'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.defaultZoneId': [{ required: true, message: '请确认可用区', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultVpcId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultSubnetId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      multipleVpcSubnetList: [],
      securityGroupList: [],
      instanceTypeList: [{ zoneId: '', zoneInstanceTypeList: [{}] }],
      preStatus: false,
      imageList: [],
      multipleInstanceList: [],
    }
  },
  components: {
    AllUser,
  },
  created() {
    this.getInfo()
  },
  computed: {
    // 计算属性用于处理选项的显示值
    subnetOptionsWithDisplayValue() {
      const result = {}
      for (const item of this.multipleVpcSubnetList) {
        result[item.subnetId + '|' + item.zone] = `${item.subnetName} (${item.zone}) (${item.cidrBlock})`
      }
      return result
    },
  },
  methods: {
    orgChange(orgs) {
      this.temp.content.authUsers = orgs
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
          // 临时兼容旧数据
          // this.temp.content.approvalData = {
          //   keys: [],
          //   needInit: 0,
          //   hidsInit: 0,
          //   isDesktop: 1,
          //   vlan: 15,
          //   hostIpList: [],
          //   instanceType: [],
          //   defaultImageId: '',
          //   defaultSubnetId: [],
          //   defaultSecurityGroupList: [],
          //   defaultVpcSwitchList: [],
          //   defaultVpcId: '',
          //   defaultZoneId: '',
          //   zoneIdList: []
          // }
        })
        this.nodeStatus = 1
      }
    },
    preApproveData() {
      this.preApproveVisible = true
    },
    preCreateApproveData() {
      this.preStatus = true
      this.getBaseListInfo()
    },
    // 选择vpc后刷新子网
    refreshData(val) {
      this.$forceUpdate()
      this.tencentVpcIndex = (this.tencentVpcInfo || []).findIndex(item => item.vpcId === val)
      this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
      this.temp.content.approvalData.defaultSecurityGroupList = []
      this.temp.content.approvalData.defaultVpcSwitchList = []
      this.temp.content.approvalData.defaultSubnetId = []
    },
    refreshZoneData(val) {
      console.log(val)
      this.$forceUpdate()
      this.temp.content.approvalData.instanceType = []
      const tmpList = []
      if (val.length > 1) {
        for (var i = 0; i < val.length; i++) {
          const arr =
            this.instanceTypeList[(this.instanceTypeList || []).findIndex(item => item.zoneId === val[i])]
              .zoneInstanceTypeList
          tmpList.concat(arr)
        }
      } else {
        this.multipleInstanceList =
          this.instanceTypeList[
            (this.instanceTypeList || []).findIndex(item => item.zoneId === val[0])
          ].zoneInstanceTypeList
      }
      this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },
    getBaseListInfo() {
      const sendData = {
        cpu: this.temp.content.serverType[0],
        mem: this.temp.content.serverType[1],
      }
      tencentBaseInfo(sendData)
        .then(response => {
          // 复用服务器创建接口，云卓有些参数有默认值
          this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
          // this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
          if (this.temp.content.os === 'ubuntu') {
            this.temp.content.approvalData.defaultImageId = 'img-l7xhxxe3'
          } else {
            this.temp.content.approvalData.defaultImageId = 'img-73bqwf59'
          }
          // this.temp.content.approvalData.defaultVpcId = response.Data.data.defaultVpcId
          this.temp.content.approvalData.defaultVpcId = 'vpc-nf4jzh4y'
          // this.temp.content.approvalData.defaultSubnetId = response.Data.data.defaultSubnetList
          this.temp.content.approvalData.defaultSubnetId = ['subnet-pu2fpjsj|ap-shanghai-4']
          // this.temp.content.approvalData.defaultZoneId = response.Data.data.zoneId
          this.temp.content.approvalData.defaultZoneId = 'ap-shanghai-4'
          this.imageList = response.Data.data.imageList
          this.securityGroupList = response.Data.data.securityGroupList
          const names = this.temp.founderEmail.split('@')[0].split('_')
          let result = ''
          names.forEach(item => {
            result = result.concat(this.capitalizeFirstLetter(item))
          })
          this.temp.content.projectName = 'RWin' + result
          this.$nextTick(() => {
            this.tencentVpcInfo = response.Data.data.vpcInfo
            this.instanceTypeList = response.Data.data.instanceTypeList
            this.temp.content.approvalData.zoneIdList.push(this.temp.content.approvalData.defaultZoneId)
            this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
            this.multipleInstanceList =
              this.instanceTypeList[
                (this.instanceTypeList || []).findIndex(
                  item => item.zoneId === this.temp.content.approvalData.defaultZoneId
                )
              ].zoneInstanceTypeList
            this.temp.content.approvalData.instanceType = this.multipleInstanceList[0].instanceType
            // 初始化subnet交换id
          })
          this.preStatus = false
          this.preCreateApproveVisible = true
        })
        .catch(() => {
          this.preStatus = false
          this.preCreateApproveVisible = true
        })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.email = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/cloud-desktop', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 2
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              console.log(999)
              console.log(this.temp)
              console.log(999)
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    autoApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutoConfig = true
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      }).catch(err => {
        console.log(err) //代码错误、请求失败捕获
      })
      // this.$refs.orderForm.refresh()
    },
    autoCreateApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutoCreate = true
          this.temp.content.isAutomation = true
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          this.preCreateApproveVisible = false
          approveOrder(obj)
            .then(response => {
              if (response === undefined) {
                notification.error({
                  message: '审批失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else if (response.Code !== 200) {
                notification.error({
                  message: '审批执行报错',
                  description: response.Code,
                })
              } else {
                this.node_status = 1
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                notification.success({
                  message: '审批成功',
                  description: '管理员审批成功',
                })
              }
            })
            .catch((this.preCreateApproveVisible = false))
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          var arry = response.Data.data
          for (var i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
