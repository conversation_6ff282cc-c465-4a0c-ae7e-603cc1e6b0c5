<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=544768044" style="text-decoration: none">
          流程文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全管理员审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}——最多支持查询200条记录, 超出200条数据需要在导出工单中申请`">
      <a-form-model ref="orderForm" :model="temp.content" :rules="rules" :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="数据库类型">
          <a-radio-group v-model:value="temp.content.dbType" button-style="solid">
            <a-radio-button value="mysql">MySQL</a-radio-button>
            <!-- <a-radio-button value="clickhouse"> ClickHouse </a-radio-button> -->
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item>
          <template #label>
            检索方式
            <a-tooltip placement="right" class="ant-tooltip">
              <template #title>
                <span>正则搜索需加前缀Rex-</span>
              </template>
              <tx-button type="link" class="question-ant-btn"><a-icon type="question-circle" /></tx-button>
            </a-tooltip>
          </template>
          <a-radio-group @change="searchTypeChange" v-model:value="temp.content.ipMode" button-style="solid">
            <a-radio-button value="ip">数据库IP</a-radio-button>
            <a-radio-button value="ipDb">数据库名称</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label=" " name="checkedValue">
          <a-select mode="multiple" label-in-value :value="temp.content.checkedValue" placeholder="请搜索并选择，可多选"
            style="width: 100%" :filter-option="false" :not-found-content="fetching ? undefined : null"
            @search="fetchUser" @change="handleChange">
            <template v-if="fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
            <a-select-option v-for="d in dbList" :value="JSON.stringify(d)"
              :key="temp.content.ipMode === 'ip' ? d.ip : d.ip + d.dbName">
              <span v-if="temp.content.ipMode === 'ip'">{{ d.ip }}</span>
              <span v-else>{{ d.ip }}--{{ d.dbName }}</span>
            </a-select-option>
            <template #dropdownRender="menu">
              <v-nodes :vnodes="menu" />
              <a-divider style="margin: 4px 0" />
              <div style="padding: 4px 8px; cursor: pointer" @mousedown="e => e.preventDefault()">
                <tx-button type="link" :size="size" @click="selectAll">全选</tx-button>
                <tx-button type="link" :size="size" @click="clearAll">清空</tx-button>
              </div>
            </template>
          </a-select>
        </a-form-model-item>
        <a-form-model-item v-if="commentAuthDis" label="Comment变更">
          <a-radio-group v-model:value="temp.content.commentAuth" button-style="solid">
            <a-radio-button value="false">不需要</a-radio-button>
            <a-radio-button value="true">需要</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否为紧急授权" name="isUrgent">
          <a-radio-group v-model:value="temp.content.isUrgent" button-style="solid">
            <a-popconfirm title="紧急授权为TL审批通过后即授权1小时，并通知运维管理员。" ok-text="我已经知晓" @confirm="confirm">
              <a-radio-button value="true">是</a-radio-button>
            </a-popconfirm>
            <a-radio-button value="false">否</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="有效期限" name="ttl" v-if="temp.content.isUrgent === 'false'">
          <a-select v-model:value="temp.content.ttl" style="width: 100%" placeholder="请选择有效期限">
            <a-select-option value="1h">1小时</a-select-option>
            <a-select-option value="8h">8小时</a-select-option>
            <a-select-option value="7d">7天</a-select-option>
            <a-select-option value="1m">1个月</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/db-approval">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item v-else-if="
          temp.founderEmail.includes(localUser) &&
          !temp.handlerEmail.includes(localUser) &&
          temp.node !== 0 &&
          temp.node !== 3 &&
          temp.node !== 4 &&
          node_status === 1
        " :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="leaderApproveData('safe')">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>

        <a-form-model-item v-else-if="temp.founderEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="reApply">重新申请</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getDbList } from '@/api/db/dbApproval'
import { debounce } from 'lodash'
import cloneDeep from 'lodash.clonedeep'

export default {
  name: 'DbApproval',
  components: {
    VNodes: {
      // functional: true,
      render: ctx => {
        return ctx.$attrs.vnodes.menuNode
      },
    },
  },

  data: function () {
    this.lastFetchId = 0
    this.fetchUser = debounce(this.fetchUser, 800)
    return {
      size: 'small',
      fetching: false,
      value: [],
      endOpen: false,
      commentAuthDis: false,
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '数据查询申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          dbType: 'mysql',
          ipMode: 'ip',
          ipDbName: [],
          checkedValue: [],
          commentAuth: 'false',
          reason: '',
          currentValue: '',
          startDate: '',
          endDate: '',
          isUrgent: 'false',
          ttl: '1h',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        checkedValue: [{ required: true, message: '请填写服务器', trigger: 'blur' }],
        isUrgent: [{ required: true, message: '请选择是否为紧急授权', trigger: 'blur' }],
        ttl: [{ required: true, message: '请选择有效期限', trigger: 'blur' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    selectAll() {
      switch (this.temp.content.ipMode) {
        case 'ip':
          this.dbList.forEach(item => {
            this.temp.content.checkedValue.push({
              key: item.ip,
              label: item.ip,
              value: JSON.stringify(item),
            })
          })
          this.temp.content.ipDbName = this.temp.content.checkedValue.map(item => {
        // delete item.label
        const utem = item.key
        return utem
      })
      this.temp.content.ipDbName = JSON.stringify(this.temp.content.ipDbName)
        case 'ipDb':
          this.dbList.forEach(item => {
            this.temp.content.checkedValue.push({
              key: item.ip + '-' + item.dbName,
              label: item.ip + item.dbName,
              value: JSON.stringify(item),
            })
          })
this.temp.content.ipDbName = this.temp.content.checkedValue.map(item => {
        // delete item.label
        const utem = JSON.parse(item.value)
        return utem
      })
      this.temp.content.ipDbName = JSON.stringify(this.temp.content.ipDbName)
      }

      
    },
    clearAll() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },
    fetchUser(value) {
      this.lastFetchId += 1
      const fetchId = this.lastFetchId
      this.fetching = true
      getDbList({
        ipMode: this.temp.content.ipMode,
        searchText: value,
      })
        .then(response => {
          this.dbList = response.Data.ipDbName
        })
        .then(body => {
          if (fetchId !== this.lastFetchId) {
            return
          }
          this.fetching = false
        })
    },
    handleChange(value) {
      this.fetching = false
      this.temp.content.checkedValue = value.map(item => {
        return {
          key: item.key,
          label: item.key,
          value: item.value,
        }
      })
      this.temp.content.ipDbName = value.map(item => {
        return JSON.parse(item.value)
      })
      this.temp.content.ipDbName = JSON.stringify(this.temp.content.ipDbName)
    },
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.commentAuth) {
        this.commentAuthDis = true
        this.temp.content.commentAuth = 'true'
      }
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            if (response.Data.content.checkedValue) {
              response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
            }
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            if (response.Data.content.checkedValue) {
              response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
            }
            this.temp.content = response.Data.content
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    reApply() {
      // window.location.href
      this.$router.push({
        path: '/workflow/db-approval',
      })
      this.temp = {
        ...this.temp,
        id: '',
        orderType: '数据查询申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        timeline: [],
        comment: '',
      }
      // window.location.href = window.location.host + '/workflow/db-approval'
    },
    createData() {
      console.log(this.temp.content.checkedValue, 'temp.content.checkedValue')
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          const obj = cloneDeep(this.temp)
          obj.content.checkedValue = JSON.stringify(obj.content.checkedValue)

          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              if (response.Data.content.checkedValue) {
                response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
              }
              this.temp = response.Data
              this.$router.push({ path: '/workflow/db-approval', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData(vaule) {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          if (vaule === 'safe') {
            this.temp.node = 3
          } else {
            this.temp.node = 2
          }
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content.checkedValue = JSON.stringify(obj.content.checkedValue)
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              if (response.Data.content.checkedValue) {
                response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
              }
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content.checkedValue = JSON.stringify(obj.content.checkedValue)
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              if (response.Data.content.checkedValue) {
                response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
              }
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 4
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content.checkedValue = JSON.stringify(obj.content.checkedValue)
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              if (response.Data.content.checkedValue) {
                response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
              }
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content.checkedValue = JSON.stringify(obj.content.checkedValue)
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              if (response.Data.content.checkedValue) {
                response.Data.content.checkedValue = JSON.parse(response.Data.content.checkedValue)
              }
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    confirm() {
      this.temp.content.isUrgent = 'true'
      this.temp.content.ttl = ''
    },
  },
}
</script>

<style scoped>
.ant-tooltip {
  -webkit-box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(240, 17, 17, 0.65);
  font-size: 14px;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: absolute;
  z-index: 1060;
  display: block;
  max-width: 2000px;
  visibility: visible;
}

.question-ant-btn {
  line-height: 1.499;
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  -webkit-box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
  box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
  cursor: pointer;
  -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  height: 32px;
  padding: 0 0px;
  font-size: 14px;
  border-radius: 2px;
}
</style>
