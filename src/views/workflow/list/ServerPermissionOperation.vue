<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="HR审批(仅涉及HR服务器)" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model
            ref="orderForm"
            :model="temp.content"
            :rules="rules"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item label="处理人" v-if="temp.handler !== ''">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <!--            <a-form-model-item name="permissionType" label="工单类型">-->
            <!--              <a-radio-group v-model:value="temp.content.permissionType" button-style="solid">-->
            <!--                <a-radio-button value="apply">申请权限</a-radio-button>-->
            <!--                <a-radio-button value="cancel">权限取消</a-radio-button>-->
            <!--              </a-radio-group>-->
            <!--            </a-form-model-item>-->
            <div v-if="temp.content.permissionType === 'apply'">
              <a-form-model-item label="机器IP列表" name="host_list">
                <a-select
                  v-model:value="temp.content.host_list"
                  mode="tags"
                  style="width: 100%"
                  @popupScroll="handlePopupScroll"
                  @search="handleSearch"
                  @select="checkHost"
                  @deselect="unCheckHost"
                  @change="checkLength"
                  placeholder="选择服务器IP"
                  :disabled="this.disableTable"
                >
                  <a-select-option v-for="i in info" :key="i.ip">
                    {{ i.ip + '(' + i.hostname + ')' }}
                  </a-select-option>
                </a-select>
                <div v-if="!isAuth">
                  <p style="color: orangered">
                    {{ checkIpAuth.toString() }} 为数据库服务器无法申请授权，详情请联系DBA!
                  </p>
                </div>
                <div v-if="isSensitive">
                  <p style="color: orangered">
                    {{ isSensitiveIpList.toString() }} 为敏感无法申请授权，详情请联系运维开发!
                  </p>
                </div>
                <div v-if="isOverServerLength">
                  <p style="color: orangered">单次最多申请10台机器权限！(超过10台请分次申请）</p>
                </div>
                <div v-if="!isValidHostList">
                  <p style="color: orangered">请输入合法的Ip地址</p>
                </div>
                <div v-if="this.temp.content.isHrApprove === 'true'">
                  <p style="color: orangered">所选ip存在HR服务器无法长期授权，请选择临时授权，详情请联系运维处理!</p>
                </div>
              </a-form-model-item>
              <a-form-model-item name="role" label="权限类型">
                <a-radio-group v-model:value="temp.content.role" button-style="solid" :disabled="this.disableTable">
                  <a-radio-button value="common">普通权限</a-radio-button>
                  <a-radio-button value="root">sudo权限</a-radio-button>
                  <a-radio-button value="other">其他权限(申请理由说明)</a-radio-button>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item name="isExpired" label="有效期限">
                <a-radio-group
                  v-model:value="temp.content.isExpired"
                  button-style="solid"
                  :disabled="temp.node > 0"
                  @change="checkHostExpired"
                >
                  <a-radio-button value="long-term">长期授权</a-radio-button>
                  <a-radio-button value="temporary">临时授权</a-radio-button>
                </a-radio-group>
                <div v-if="isOnlyTempAuth && this.temp.content.isExpired !== 'temporary'">
                  <p style="color: orangered">{{ checkIpTempAuth.toString() }} 无法长期授权,请选择临时授权！</p>
                </div>
              </a-form-model-item>
              <a-form-model-item label="添加Docker权限">
                <a-radio-group
                  v-model:value="temp.content.needDocker"
                  button-style="solid"
                  :disabled="this.disableTable"
                >
                  <a-radio-button value="true">是</a-radio-button>
                  <a-radio-button value="false">否</a-radio-button>
                </a-radio-group>
              </a-form-model-item>
              <div v-if="temp.content.isExpired === 'temporary'">
                <a-form-model-item label="授权时长">
                  <a-select
                    v-model:value="temp.content.time"
                    placeholder="请选择"
                    style="width: 120px"
                    @change="approveTimeChange"
                    :disabled="this.temp.content.isHrApprove === 'true' || this.disableTable"
                  >
                    <a-select-option value="1">1小时</a-select-option>
                    <a-select-option value="2">2小时</a-select-option>
                    <a-select-option value="4">4小时</a-select-option>
                    <a-select-option value="8">8小时</a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item v-if="hasGpu" label="是否添加docker权限">
                  <a-radio-group
                    v-model:value="temp.content.needDocker"
                    button-style="solid"
                    :disabled="this.disableTable"
                  >
                    <a-radio-button value="true">是</a-radio-button>
                    <a-radio-button value="false">否</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="是否为紧急授权">
                  <a-radio-group
                    v-model:value="temp.content.isUrgent"
                    button-style="solid"
                    :disabled="temp.content.isHrApprove === 'true' || this.disableTable"
                  >
                    <a-popconfirm
                      title="紧急授权为TL审批通过后即添加权限，并通知运维管理员。"
                      ok-text="我已经知晓"
                      @confirm="confirm"
                    >
                      <a-radio-button value="true" :disabled="temp.content.isHrApprove === 'true'">是</a-radio-button>
                    </a-popconfirm>
                    <a-radio-button value="false" :disabled="temp.content.isHrApprove === 'true'">否</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </div>
            </div>
            <div v-if="temp.content.permissionType === 'cancel'">
              <a-form-model-item label="用户">
                <a-input placeholder="Basic usage" />
              </a-form-model-item>
              <a-form-model-item label="权限">
                <a-input placeholder="Basic usage" />
              </a-form-model-item>
            </div>
            <a-form-model-item v-if="temp.status === 10" name="reason" label="授权结果">
              <tx-button type="link" @click="getJmsAuthResult">查看详情</tx-button>
            </a-form-model-item>
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea
                v-model:value="temp.content.reason"
                placeholder="申请的用途或理由说明"
                :auto-size="{ minRows: 2 }"
                :disabled="this.disableTable"
              />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <a-button type="primary" @click="handleSubmit" :disabled="this.submitStatus">提交</a-button>
              <a-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </a-button>
            </a-form-model-item>
            <a-form-model-item label="审批节点" v-if="temp.node > 0">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
            <a-form-model-item
              v-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <a-button type="primary" @click="leaderApproveData">同意</a-button>
              <a-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</a-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.handlerEmail.includes(localUser) &&
                temp.node === 2 &&
                temp.content.isHrApprove === 'true' &&
                node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <a-button type="primary" @click="hrApproveData">同意</a-button>
              <a-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</a-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <a-button type="primary" @click="approveData">同意</a-button>
              <a-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</a-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 4 && node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <a-button type="danger" @click="revokeData">撤回</a-button>
            </a-form-model-item>
          </a-form-model>
        </a-col>
      </a-row>
    </a-card>

    <a-modal v-model:visible="jmsAuthResult" title="授权执行详细情况" :width="1200" centered :footer="null">
      <a-table
        class="dataBaseTable"
        style="margin-top: 18px"
        :scroll="{ x: 1000, y: 600 }"
        bordered
        :pagination="paginationinfo"
        :columns="columnsInfo"
        :data-source="jmsAuthTableList"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
          </template>
          <template v-if="column.dataIndex === 'taskLog' && record.status === 'Failed'">
            <a @click="handleJmsAuthDetail(record)">详情</a>
          </template>
          <template v-if="column.dataIndex === 'action' && record.status === 'Failed' && this.userpRression">
            <a @click="handleRetry(record)">重试</a>
          </template>
        </template>
      </a-table>
    </a-modal>
    <a-modal
      v-model:visible="logVisible"
      width="55%"
      title="授权日志"
      centered
      :footer="null"
      :afterClose="handleClose"
    >
      <div ref="logTerm" class="terminal-container"></div>
    </a-modal>
    <a-modal v-model:visible="captchaVisible" width="30%" title="二次验证" centered :footer="null">
      <div style="justify-content: center; display: flex; align-items: center">
        <p style="float: left; color: red">TL申请</p>
        <p style="float: left; color: red; font-weight: bold">紧急授权</p>
        <p style="color: red">会优先授权，此操作需要二次验证</p>
      </div>
      <SecondaryVerification v-if="captchaVisible" @verification-success="createData()" />
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import { Terminal } from 'xterm'
import 'xterm/css/xterm.css'
import { FitAddon } from 'xterm-addon-fit'
import { checkHost, getAllAssetsIp, listJmsAuthResult, retryJmsAuth } from '@/api/workflow/asset_automation'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, checkUserNeedsCaptchaVerification, createOrder, getOrderInfo } from '@/api/workflow/order'
import SecondaryVerification from '@/components/Verification/verify.vue'
import { globalOtp } from '@/api/commonVerify'
import { myColumns } from '@/views/ai/aiData'
import { waterMarkVs } from '@/utils/watermark'
import { getUserList } from '@/api/permission/user'

const statusMap = {
  Running: {
    status: 'processing',
    text: '授权中',
  },
  Success: {
    status: 'success',
    text: '成功',
  },
  Failed: {
    status: 'error',
    text: '失败',
  },
}

export default {
  name: 'ServerPermissionOperation',
  data() {
    return {
      jmsAuthTableList: [],
      columnsInfo: [
        { title: '授权IP', dataIndex: 'ips', width: '200px', ellipsis: true },
        { title: '授权用户', dataIndex: 'name', width: '200px', ellipsis: true },
        { title: '权限类型', dataIndex: 'role', width: '200px', ellipsis: true },
        {
          title: '执行情况',
          dataIndex: 'status',
          width: '100px',
        },
        { title: '报错信息', dataIndex: 'taskLog', width: '150px' },
        { title: '操作', dataIndex: 'action', width: '150px' },
      ],
      localUser: store.getters.email,
      node_status: 1,
      isOverServerLength: false,
      isAuth: true,
      terminal: null,
      isOnlyTempAuth: false,
      // 二次验证
      needsCaptchaVerification: false,
      captchaVisible: false,
      isValidHostList: true,
      authOtpKey: {},
      checkIpAuth: [],
      isSensitiveIpList: [],
      isSensitive: false,
      checkIpTempAuth: [],
      hrNodeIp: [],
      hasGpu: false,
      submitStatus: true,
      needDockers: [],
      disableTable: false,
      jmsAuthResult: false,
      userpRression: false,
      logVisible: false,
      logContent: '',
      paginationinfo: {
        defaultPageSize: 20,
        hideOnSinglePage: true,
      },
      temp: {
        id: 0,
        orderType: '服务器授权',
        node: 0,
        status: 0,
        comment: '',
        needCheck: false,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        time_now: '',
        handler: '',
        handlerEmail: '',
        host_list: [],
        content: {
          permissionType: 'apply',
          host_list: [],
          role: 'root',
          isExpired: 'long-term',
          needDocker: 'false',
          validTime: '1',
          time: '1',
          isUrgent: 'false',
          isHrApprove: 'false',
          reason: '',
        },
        timeline: [],
      },
      rules: antdFormRulesFormat({
        'content.permissionType': [{ required: true, message: '请选择类型', trigger: 'blur' }],
        'content.host_list': [{ required: true, message: '请选择服务器', trigger: 'change' }],
        'content.role': [{ required: true, message: '请选择权限类型', trigger: 'blur' }],
        'content.isExpired': [{ required: true, message: '请选择有效期限', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      queryParam: {},
      request: {
        pageNo: 1,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
        isAdmin: true,
      },
      info: [],
      test: '',
    }
  },
  components: {
    SecondaryVerification,
  },
  created() {
    this.getInfo()
    this.CheckUserNeedsCaptchaVerification()
    this.getInitUsersStatus()
    this.getUserRoles(store.getters.email)
  },
  methods: {
    // 用户角色权限隔离
    getUserRoles(userEmail) {
      getUserList({ searchText: userEmail }).then(response => {
        if (response.Data.data[0].roles.includes('admin') || response.Data.data[0].roles.includes('jms_admin')) {
          this.userpRression = true
        }
      })
    },
    // 认证相关
    getInitUsersStatus() {
      this.authOtpKey.value = ''
      myColumns.forEach(item => {
        this.authOtpKey.value += item.scopeKey
      })
      this.authOtpKey.value = waterMarkVs + this.authOtpKey.value
    },
    checkStatus() {
      globalOtp().then(res => {
        this.authToStr(res.Data.network, this.authOtpKey.value).then(r => {
          this.authToStr(res.Data.otpToken, this.authOtpKey.value).then(res1 => {
            if (res1) {
              localStorage.setItem('globalToken', res.Data.otpToken)
              this.captchaVisible = false
              // 无需验证触发提交工单
              this.createData()
            } else {
              this.captchaVisible = true
            }
          })
        })
      })
    },
    async authToStr(text, a) {
      const importedKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(a),
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      )
      const textData = atob(text)
      const dataArray = new Uint8Array(textData.split('').map(char => char.charCodeAt(0)))

      const nonceSize = 12
      const nonce = dataArray.slice(0, nonceSize)
      const ciphertext = dataArray.slice(nonceSize)
      try {
        const myData = await crypto.subtle.decrypt({ name: 'AES-GCM', iv: nonce }, importedKey, ciphertext)
        return new TextDecoder().decode(myData)
      } catch (error) {
        return null
      }
    },
    CheckUserNeedsCaptchaVerification() {
      checkUserNeedsCaptchaVerification().then(response => {
        this.needsCaptchaVerification = response.Data.needsCaptchaVerification
      })
    },
    orgChange(orgs) {
      this.temp.content.authUsers = orgs
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.temp.content.host_list = this.temp.content.host_list.split(',')
            this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
            // node = 0 或者3  可以修改
            if (this.temp.node === 0 || this.temp.node === 3) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
      this.GetAssetIps()
    },
    preApproveData() {
      this.preApproveVisible = true
    },
    handleSubmit() {
      if (
        this.needsCaptchaVerification === false ||
        (this.needsCaptchaVerification === true && this.temp.content.isUrgent !== 'true')
      ) {
        this.createData()
      } else {
        this.temp.needCheck = true
        this.checkStatus()
      }
    },
    createData() {
      this.captchaVisible = false
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.host_list = this.temp.content.host_list.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.temp.content.host_list = this.temp.content.host_list.split(',')
              this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              this.$router.push({ path: '/workflow/server-permission-operation', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.host_list = this.temp.content.host_list.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.host_list = this.temp.content.host_list.split(',')
              this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    hrApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.host_list = this.temp.content.host_list.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.host_list = this.temp.content.host_list.split(',')
              this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
              notification.success({
                message: '审批成功',
                description: 'HR审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 10
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.host_list = this.temp.content.host_list.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          approveOrder(this.temp).then(response => {
            this.preApproveVisible = false
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.host_list = this.temp.content.host_list.split(',')
              this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.host_list = this.temp.content.host_list.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.host_list = this.temp.content.host_list.split(',')
              this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.host_list = this.temp.content.host_list.join(',')
          this.temp.content.validTime = (3600 * Number(this.temp.content.time)).toString()
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.host_list = this.temp.content.host_list.split(',')
              this.temp.content.time = (Number(this.temp.content.validTime) / 3600).toString()
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.info.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            if (res && res.Data && res.Data.info) {
              this.info.push(...res.Data.info)
              this.request.pageNo++
              this.request.isRequest = false
            }
          })
        }
      }
    },
    handleSearch(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.info = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    checkHost(ip) {
      let req = { ip: ip }
      this.submitStatus = true
      checkHost(req).then(res => {
        if (!res.Data.isAuth) {
          this.checkIpAuth.push(ip)
          this.isAuth = false
        } else if (res.Data.isSensitive) {
          this.isSensitiveIpList.push(ip)
          this.isSensitive = true
        } else if (res.Data.isOnlyTempAuth) {
          if (!res.Data.isSensitive) {
            this.checkIpTempAuth.push(ip)
            this.isOnlyTempAuth = true
          }
        } else if (res.Data.hasGpu !== 'false') {
          this.hasGpu = true
          this.needDockers.push(ip)
        }
        if (res.Data.isHrNode) {
          this.temp.content.isHrApprove = 'true'
          this.isOnlyTempAuth = true
          this.hrNodeIp.push(ip)
        }

        this.submitStatus =
          this.isSensitive ||
          !this.isValidHostList ||
          this.isOverServerLength ||
          !this.isAuth ||
          (this.isOnlyTempAuth && this.temp.content.isExpired !== 'temporary') ||
          this.temp.content.host_list.length === 0
        this.checkLength(this.temp.content.host_list)
      })
    },
    checkHostExpired() {
      this.submitStatus =
        this.isSensitive ||
        !this.isValidHostList ||
        this.isOverServerLength ||
        !this.isAuth ||
        (this.isOnlyTempAuth && this.temp.content.isExpired !== 'temporary') ||
        this.temp.content.host_list.length === 0
    },
    unCheckHost(ip) {
      this.checkLength(this.temp.content.host_list)
      if (this.checkIpTempAuth.indexOf(ip) !== -1) {
        this.checkIpTempAuth.splice(ip, 1)
        if (this.checkIpTempAuth.length === 0) {
          this.isOnlyTempAuth = false
        }
      } else if (this.checkIpAuth.indexOf(ip) !== -1) {
        this.checkIpAuth.splice(ip, 1)
        if (this.checkIpAuth.length === 0) {
          this.isAuth = true
        }
      } else if (this.needDockers.indexOf(ip) !== -1) {
        this.needDockers.splice(ip, 1)
        if (this.needDockers.length === 0) {
          this.hasGpu = false
        }
      }
      if (this.isSensitiveIpList.indexOf(ip) !== -1) {
        this.isSensitiveIpList.splice(ip, 1)
        if (this.isSensitiveIpList.length === 0) {
          this.isSensitive = false
        }
      }
      if (this.hrNodeIp.indexOf(ip) !== -1) {
        this.hrNodeIp.splice(ip, 1)
        if (this.hrNodeIp.length === 0) {
          this.temp.content.isHrApprove = 'false'
          this.isOnlyTempAuth = false
        }
      }
      this.submitStatus =
        this.isSensitive ||
        !this.isValidHostList ||
        this.isOverServerLength ||
        !this.isAuth ||
        (this.isOnlyTempAuth && this.temp.content.isExpired !== 'temporary') ||
        this.temp.content.host_list.length === 0
    },
    isValidIP(ip) {
      const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
      if (!ipRegex.test(ip)) {
        return false
      }
      const parts = ip.split('.')
      return parts.every(part => {
        const num = parseInt(part, 10)
        return num >= 0 && num <= 255 && part === num.toString()
      })
    },

    checkLength(arr) {
      if (arr.length > 10) {
        this.temp.content.host_list = arr.splice(0, 10)
        this.$message.warning('服务器授权单次申请不能超过十台')
        // this.isOverServerLength = true
      } else {
        if (arr.length > 0) {
          console.log('212123', arr)
          this.isValidHostList = arr.every(part => this.isValidIP(part.trim()))
          this.isOverServerLength = false
        }
      }
      this.submitStatus =
        this.isSensitive ||
        !this.isValidHostList ||
        (this.isValidHostList && this.isOverServerLength) ||
        !this.isAuth ||
        (this.isOnlyTempAuth && this.temp.content.isExpired !== 'temporary') ||
        this.temp.content.host_list.length === 0
    },
    getJmsAuthResult() {
      listJmsAuthResult({ orderId: this.temp.id }).then(response => {
        this.jmsAuthResult = true
        this.jmsAuthTableList = response.Data.data
      })
    },
    handleJmsAuthDetail(record) {
      this.logVisible = true
      this.$nextTick(() => {
        this.initTerminal()
        this.terminal.clear()
        this.terminal.reset()
        this.terminal.write(record.taskLog)
        this.fitAddon.fit()
      })
    },
    initTerminal() {
      if (!this.terminal) {
        // 初始化终端
        this.terminal = new Terminal({
          tabStopWidth: 4,
          lineHeight: 1.2,
          fontSize: 14,
          fontFamily: 'Consolas',
        })
        this.fitAddon = new FitAddon()
        this.terminal.loadAddon(this.fitAddon)

        // 打开终端
        this.terminal.open(this.$refs.logTerm)

        // 添加窗口 resize 事件监听
        const that = this
        window.addEventListener(
          'resize',
          function () {
            if (that.logVisible && that.fitAddon) {
              that.fitAddon.fit()
            }
          },
          false
        )
      }
    },
    handleClose() {
      this.logContent = ''
      this.logVisible = false
    },
    handleRetry(record) {
      this.jmsAuthResult = false
      retryJmsAuth({ id: record.id }).then(response => {})
    },
    confirm() {
      if (this.temp.content.isHrApprove === 'false') {
        this.temp.content.isUrgent = 'true'
      }
    },
    approveTimeChange() {
      if (this.temp.content.isHrApprove === 'true') {
        this.temp.content.time = '1'
      }
    },
  },
}
</script>
<style scoped></style>
