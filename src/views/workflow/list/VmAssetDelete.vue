<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item v-if="this.source === ''" label="IP" name="ip">
              <a-select
                v-model:value="temp.content.ip"
                style="width: 100%"
                @popupScroll="handlePopupScroll"
                @search="handleSearchIp"
                @change="handleChangeIp"
                placeholder="选择IP"
                show-search
                allowClear
                mode="multiple"
              >
                <a-select-option v-for="i in ipInfo" :key="i.ip">
                  {{ i.ip + '(' + i.hostname + ')' }}
                </a-select-option>
              </a-select>
              <span v-if="temp.content.ip.length === 0" style="font-size: 12px; color: red; margin-left: 3px">
                注意：仅显示自己负责的服务器！未找到服务器请找运维确认服务器负责人
              </span>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="typeof temp.content.ip === 'object' && this.source !== ''"
              label="IP"
              name="ip"
            >
              <div v-for="item in temp.content.ip" :value="item" :key="item">
                <a-tag color="blue" style="margin: 2px">
                  {{ item }}
                </a-tag>
                <br />
              </div>
            </a-form-model-item>
            <a-form-model-item v-else label="IP" name="ip">
              <a-tag color="blue">
                {{ temp.content.ip }}
              </a-tag>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              v-if="typeof temp.content.hostname === 'object' && this.source !== ''"
              label="服务器名"
              name="hostname"
            >
              <div v-for="item in temp.content.hostname" :value="item" :key="item">
                <a-tag color="blue" style="margin: 2px">
                  {{ item }}
                </a-tag>
                <br />
              </div>
            </a-form-model-item>
            <a-form-model-item v-else-if="this.source !== '' && this.source !== ''" label="服务器名" name="hostname">
              <a-tag color="blue">
                {{ temp.content.hostname }}
              </a-tag>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否保留镜像" name="hostname">
              <a-radio-group v-model:value="temp.content.preserveImage" button-style="solid">
                <a-radio-button value="false">否</a-radio-button>
                <a-radio-button value="true">是</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/备注">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/asset-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="primary" @click="checkServerDelete" style="margin-left: 10px">注销检查</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal
      title="服务器注销检查"
      :visible="modalFormPreServerDeleteVisible"
      width="800px"
      @cancel="modalFormPreServerDeleteVisible = false"
    >
      <div>
        <a-table
          :columns="CheckColumns"
          :data-source="checkData"
          bordered
          :loading="checkLoading"
          size="middle"
          :scroll="{ x: 'calc(700px + 50%)', y: 240 }"
        />
      </div>
      <div>
        5天内登录的用户有:
        <a-tag v-for="(user, index) in this.syslog" :key="index" color="pink">
          {{ user }}
        </a-tag>
      </div>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { getAssetInfo } from '@/api/asset'
import {
  getAllAssetsIp,
  offlineAssetCpuCheck,
  offlineAssetLoadCheck,
  offlineAssetMemCheck,
  offlineAssetNetInCheck,
  offlineAssetNetOutCheck,
  offlineAssetSyslogCheck,
  offlineAssetTcpCheck,
} from '@/api/workflow/asset_automation'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import cloneDeep from 'lodash.clonedeep'
import ipInfo from 'lodash'
// import pick from 'lodash.pick'
const CheckColumns = [
  {
    title: '日期',
    dataIndex: 'time',
    key: 'time',
    width: 40,
  },
  {
    title: 'CPU使用率(%)',
    children: [
      {
        title: '平均值',
        dataIndex: 'CpuMetricAvg',
        key: 'CpuMetricAvg',
        width: 25,
      },
      {
        title: '最大值',
        dataIndex: 'CpuMetricMax',
        key: 'CpuMetricMax',
        width: 25,
      },
    ],
  },
  {
    title: '内存使用率(%)',
    children: [
      {
        title: '平均值',
        dataIndex: 'MemMetricAvg',
        key: 'MemMetricAvg',
        width: 25,
      },
      {
        title: '最大值',
        dataIndex: 'MemMetricMax',
        key: 'MemMetricMax',
        width: 25,
      },
    ],
  },
  {
    title: 'TCP连接数',
    children: [
      {
        title: '平均值',
        dataIndex: 'TcpMetricAvg',
        key: 'TcpMetricAvg',
        width: 25,
      },
      {
        title: '最大值',
        dataIndex: 'TcpMetricMax',
        key: 'TcpMetricMax',
        width: 25,
      },
    ],
  },
  {
    title: '负载(load1)',
    children: [
      {
        title: '平均值',
        dataIndex: 'LoadMetricAvg',
        key: 'LoadMetricAvg',
        width: 25,
      },
      {
        title: '最大值',
        dataIndex: 'LoadMetricMax',
        key: 'LoadMetricMax',
        width: 25,
      },
    ],
  },
  {
    title: '出口流量(Bytes)',
    children: [
      {
        title: '平均值',
        dataIndex: 'NetOutMetricAvg',
        key: 'NetOutMetricAvg',
        width: 25,
      },
      {
        title: '最大值',
        dataIndex: 'NetOutMetricMax',
        key: 'NetOutMetricMax',
        width: 25,
      },
    ],
  },
  {
    title: '入口流量(Bytes)',
    children: [
      {
        title: '平均值',
        dataIndex: 'NetInMetricAvg',
        key: 'NetInMetricAvg',
        width: 25,
      },
      {
        title: '最大值',
        dataIndex: 'NetInMetricMax',
        key: 'NetInMetricMax',
        width: 25,
      },
    ],
  },
]
export default {
  name: 'ServerDelete',
  data() {
    return {
      localUser: store.getters.email,
      CheckColumns: CheckColumns,
      time_now: '',
      node_status: 1,
      checkLoading: false,
      // 工单 来源
      source: 'server',
      tempIpData: [],
      temp: {
        id: '',
        orderType: '虚拟机注销',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          id: [],
          ip: [],
          idc: [],
          hostname: [],
          hostIp: [],
          reason: undefined,
          preserveImage: 'false',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        ip: [{ required: true, message: '请填写IP', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      modalFormPreServerDeleteVisible: false,
      preShutdownMsgs: [{}],
      cpuRes: [{}],
      memRes: [{}],
      tcpRes: [{}],
      loadRes: [{}],
      netInRes: [{}],
      netOutRes: [{}],
      syslog: [],
      checkData: [],
      ipInfo: [],
      request: {
        pageNo: 1,
        isAdmin: false,
        pageSize: 10,
        search: '',
        isRequest: false,
        idc: 'ucloud-shanghai2-hybrid',
        totalPage: -1,
      },
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.assetId) {
        if (typeof JSON.parse(this.$route.query.assetId) === 'number') {
          getAssetInfo(this.$route.query.assetId).then(response => {
            this.temp.content.id = response.Data.uuid
            this.temp.content.ip = response.Data.ip
            this.temp.content.idc = response.Data.idc
            this.temp.content.hostname = response.Data.hostname
            this.temp.content.hostIp = response.Data.hostIp
          })
          this.nodeStatus = 1
        } else {
          const assetIdList = JSON.parse(this.$route.query.assetId)
          for (let assetIndex in assetIdList) {
            getAssetInfo(assetIdList[assetIndex]).then(response => {
              this.temp.content.id.push(response.Data.uuid)
              this.temp.content.ip.push(response.Data.ip)
              this.temp.content.idc.push(response.Data.idc)
              this.temp.content.hostname.push(response.Data.hostname)
              this.temp.content.hostIp.push(response.Data.hostIp)
            })
          }
          this.nodeStatus = 1
        }
      } else if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            if (response.Data.content.id === undefined) {
              response.Data.content = JSON.parse(response.Data.content.data)
            }
            this.temp = response.Data
            this.temp.content.id = this.temp.content.id.split('|')
            this.temp.content.ip = this.temp.content.ip.split('|')
            this.temp.content.hostname = this.temp.content.hostname.split('|')
            this.temp.content.idc = this.temp.content.idc.split('|')
            this.temp.content.hostIp = this.temp.content.hostIp.split('|')
            this.node_status = 1
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      } else {
        this.source = ''
        this.GetAssetIps()
      }
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.source === '') {
            for (let i = 0; i < this.temp.content.ip.length; i++) {
              const foundObject = this.tempIpData.find(obj => obj.ip === this.temp.content.ip[i])
              this.temp.content.hostname.push(foundObject.hostname)
              this.temp.content.id.push(foundObject.id)
              this.temp.content.idc.push(foundObject.idc)
              this.temp.content.hostIp.push(foundObject.hostIp)
            }
          }
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          const obj = cloneDeep(this.temp)
          // 转成'xxx|xxx'格式
          if (typeof this.temp.content.hostname === 'object') {
            obj.content.ip = obj.content.ip.join('|')
            obj.content.hostname = obj.content.hostname.join('|')
            // obj.content.idc = obj.content.idc.join('|')
            obj.content.id = obj.content.id.join('|')
            obj.content.idc = obj.content.idc.join('|')
            obj.content.hostIp = obj.content.hostIp.join('|')
          }
          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.ip = this.temp.content.ip.split('|')
              this.temp.content.hostname = this.temp.content.hostname.split('|')
              this.temp.content.idc = this.temp.content.idc.split('|')
              this.temp.content.id = this.temp.content.id.split('|')
              this.temp.content.hostIp = this.temp.content.hostIp.split('|')
              this.$router.push({ path: '/workflow/vm-asset-del', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          // 转成'xxx|xxx'格式
          if (typeof this.temp.content.hostname === 'object') {
            obj.content.ip = obj.content.ip.join('|')
            obj.content.hostname = obj.content.hostname.join('|')
            obj.content.idc = obj.content.idc.join('|')
            obj.content.id = obj.content.id.join('|')
            obj.content.hostIp = obj.content.hostIp.join('|')
          }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          // 转成'xxx|xxx'格式
          if (typeof this.temp.content.hostname === 'object') {
            obj.content.ip = obj.content.ip.join('|')
            obj.content.hostname = obj.content.hostname.join('|')
            obj.content.idc = obj.content.idc.join('|')
            obj.content.id = obj.content.id.join('|')
            obj.content.hostIp = obj.content.hostIp.join('|')
          }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          // 转成'xxx|xxx'格式
          if (typeof this.temp.content.hostname === 'object') {
            obj.content.ip = obj.content.ip.join('|')
            obj.content.hostname = obj.content.hostname.join('|')
            obj.content.idc = obj.content.idc.join('|')
            obj.content.id = obj.content.id.join('|')
            obj.content.hostIp = obj.content.hostIp.join('|')
          }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    checkServerDelete() {
      this.modalFormPreServerDeleteVisible = true
      this.checkLoading = true
      // for (var i = 0; i < ip.length; i++) {
      const sendData = { ip: this.temp.content.ip[0], idc: this.temp.content.idc[0] }
      this.getCheckInfo(sendData)
    },

    getCheckInfo(sendData) {
      const cpu = new Promise((resolve, reject) => {
        offlineAssetCpuCheck(sendData)
          .then(response => {
            this.cpuRes = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('Cpu check failed:', error)
            reject(error)
          })
      })
      const mem = new Promise((resolve, reject) => {
        offlineAssetMemCheck(sendData)
          .then(response => {
            this.memRes = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('mem check failed:', error)
            reject(error)
          })
      })
      const tcp = new Promise((resolve, reject) => {
        offlineAssetTcpCheck(sendData)
          .then(response => {
            this.tcpRes = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('tcp check failed:', error)
            reject(error)
          })
      })
      const load = new Promise((resolve, reject) => {
        offlineAssetLoadCheck(sendData)
          .then(response => {
            this.loadRes = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('load check failed:', error)
            reject(error)
          })
      })
      const netIn = new Promise((resolve, reject) => {
        offlineAssetNetInCheck(sendData)
          .then(response => {
            this.netInRes = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('netIn check failed:', error)
            reject(error)
          })
      })
      const netOut = new Promise((resolve, reject) => {
        offlineAssetNetOutCheck(sendData)
          .then(response => {
            this.netOutRes = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('netOut check failed:', error)
            reject(error)
          })
      })

      const syslog = new Promise((resolve, reject) => {
        offlineAssetSyslogCheck(sendData)
          .then(response => {
            this.syslog = response.Data.data
            resolve('success')
          })
          .catch(error => {
            console.error('syslog check failed:', error)
            reject(error)
          })
      })

      Promise.all([cpu, mem, tcp, load, netIn, netOut, syslog])
        .then(result => {
          this.PushData()
        })
        .catch(error => {
          this.PushData()
          console.log(error)
        })
    },
    PushData() {
      if (this.checkData.length === 0) {
        for (let i = 1; i <= 5; i++) {
          const date = this.NowDate(i)
          this.checkData.push({
            key: date,
            time: date,
            CpuMetricAvg:
              this.cpuRes.find(item => item.time === date) !== undefined
                ? this.cpuRes.find(item => item.time === date).avg
                : '-',
            CpuMetricMax:
              this.cpuRes.find(item => item.time === date) !== undefined
                ? this.cpuRes.find(item => item.time === date).max
                : '-',
            MemMetricAvg:
              this.memRes.find(item => item.time === date) !== undefined
                ? this.memRes.find(item => item.time === date).avg
                : '-',
            MemMetricMax:
              this.memRes.find(item => item.time === date) !== undefined
                ? this.memRes.find(item => item.time === date).max
                : '-',
            TcpMetricAvg:
              this.tcpRes.find(item => item.time === date) !== undefined
                ? this.tcpRes.find(item => item.time === date).avg
                : '-',
            TcpMetricMax:
              this.tcpRes.find(item => item.time === date) !== undefined
                ? this.tcpRes.find(item => item.time === date).max
                : '-',
            LoadMetricAvg:
              this.tcpRes.find(item => item.time === date) !== undefined
                ? this.loadRes.find(item => item.time === date).avg
                : '-',
            LoadMetricMax:
              this.tcpRes.find(item => item.time === date) !== undefined
                ? this.loadRes.find(item => item.time === date).max
                : '-',
            NetOutMetricAvg:
              this.netInRes.find(item => item.time === date) !== undefined
                ? this.netInRes.find(item => item.time === date).avg
                : '-',
            NetOutMetricMax:
              this.netInRes.find(item => item.time === date) !== undefined
                ? this.netInRes.find(item => item.time === date).max
                : '-',
            NetInMetricAvg:
              this.netOutRes.find(item => item.time === date) !== undefined
                ? this.netOutRes.find(item => item.time === date).avg
                : '-',
            NetInMetricMax:
              this.netOutRes.find(item => item.time === date) !== undefined
                ? this.netOutRes.find(item => item.time === date).max
                : '-',
          })
        }
      }
      this.checkLoading = false
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    NowDate(shifting) {
      const timeStamp = new Date().getTime() - 24 * 60 * 60 * 1000 * shifting
      const date = new Date(timeStamp)
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      if (month < 10) {
        month = '0' + month
      }
      if (day < 10) {
        day = '0' + day
      }
      return year + '-' + month + '-' + day
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    handleChangeIp(value) {
      for (let i = 0; i < value.length; i++) {
        const foundObject = this.ipInfo.find(obj => obj.ip === value[i])
        if (foundObject !== undefined) {
          this.tempIpData.push({
            ip: foundObject.ip,
            hostname: foundObject.hostname,
            id: foundObject.uuid,
            idc: foundObject.idc,
            hostIp: foundObject.hostIp,
          })
        }
      }
    },
  },
}
</script>

<style scoped></style>
