<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="对应共享存储负责人审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card
      style="margin-top: 12px"
      :bordered="false"
      :title="`${temp.orderType}——流程完成后第二天生效（云存储自动同步用户数据）`"
    >
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="checkedName" label="名称">
              <a-select
                v-model:value="temp.content.checkedName"
                :labelInValue="true"
                show-search
                :filter-option="filterOption"
                style="width: 100%"
                placeholder="填写共享云存储的名称"
                @change="handleChange"
              >
                <a-select-option
                  v-for="(i, index) in storageNames"
                  :key="i.name"
                  :value="i.principalEmail + '&&' + i.name"
                >
                  {{ i.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="comment" label="申请理由">
              <a-textarea v-model:value="temp.content.comment" placeholder="填写申请的理由" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/storage/osp-share">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getOspShareNames } from '@/api/shareStorage'
import { deepClone } from '@/utils/util'

export default {
  name: 'ShareStorageAdd',

  data() {
    return {
      filterOption: (input, option) => {
        return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      storageNames: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '加入已有共享云存储',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: '',
          comment: '',
          checkedName: '',
          ospSharePrinciple: '',
          founder_uid: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        checkedName: [{ required: true, message: '填写共享云存储的名称', trigger: 'blur' }],
        comment: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
    }
  },
  created() {
    this.getInfo()
  },
  mounted() {
    getOspShareNames().then(res => {
      this.storageNames = res.buckets
    })
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.temp.content.checkedName = JSON.parse(this.temp.content.checkedName)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    handleChange(value) {
      delete this.temp.content.checkedName.label
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.name = this.temp.content.checkedName.key.trim()
          this.temp.content.ospSharePrinciple = this.temp.content.checkedName.value.split('&&')[0]
          this.temp.content.founder_uid = String(noc.user.getUserInfo().uid) || ''
          this.temp.handlerEmail = 'ospSharePrinciple'

          const cloneTemp = deepClone(this.temp)
          cloneTemp.content.checkedName = JSON.stringify(cloneTemp.content.checkedName)
          createOrder(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = response.Data
              this.temp.content.checkedName = JSON.parse(this.temp.content.checkedName)
              this.$router.push({ path: '/workflow/share-storage-add', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const cloneTemp = deepClone(this.temp)
          cloneTemp.content.checkedName = JSON.stringify(cloneTemp.content.checkedName)

          approveOrder(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.checkedName = JSON.parse(this.temp.content.checkedName)
              notification.success({
                message: '审批成功',
                description: 'IT管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const cloneTemp = deepClone(this.temp)
          cloneTemp.content.checkedName = JSON.stringify(cloneTemp.content.checkedName)
          approveOrder(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.checkedName = JSON.parse(this.temp.content.checkedName)
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
