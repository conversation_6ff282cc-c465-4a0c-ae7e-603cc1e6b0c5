<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-04-11 14:18:55
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-12-30 17:00:26
 * @FilePath: \cloud_web\src\views\workflow\list\ServerDbCreate.vue
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="DBA审批" />
        <a-step title="管理员审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <!--  -->
    <div style="display: flex; justify-content: space-between; padding-bottom: 40px">
      <div style="width: 60%">
        <!-- 第一步创建 -->
        <div v-if="temp.node === 0 || temp.node == 3">
          <a-form-model
            :labelAlign="'left'"
            ref="orderForm"
            :model="temp.content"
            :rules="rules"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <!-- 基础配置 -->
            <a-card style="margin-top: 14px" :bordered="false" title="基础配置">
              <a-form-model-item label="机房" name="idc">
                <x-select
                  :allowClear="false"
                  style="width: 180px"
                  v-model:value="temp.content.idc"
                  placeholder="请选择机房"
                  @change="getAssetListInfo"
                >
                  <a-select-option value="ucloud-shanghai2-hybrid">UCloud混合云</a-select-option>
                  <!-- <a-select-option value="aws-cn-north-1">AWS北京(cn-north-1)</a-select-option>
                  <a-select-option value="aws-cn-northwest-1">AWS宁夏(cn-northwest-1)</a-select-option>
                  <a-select-option value="aliyun-qxb-cn-hangzhou">阿里云(启信宝)</a-select-option> -->
                  <a-select-option value="shanghai8-songjiang">松江机房</a-select-option>
                </x-select>
              </a-form-model-item>
              <a-form-model-item label="CPU/内存" name="serverType">
                <a-select
                  :allowClear="false"
                  show-search
                  style="width: 180px"
                  v-model:value="temp.content.serverType"
                  placeholder="请选择CPU/内存"
                  change-on-select
                  @change="serverTypeChange"
                >
                  <a-select-option v-for="i in serverTypeOptions" :value="i.value">
                    {{ i.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-item label="镜像:" name="needPreImageIp">
                <a-radio-group
                  @change="needPreImageIpChange"
                  v-model:value="temp.content.needPreImageIp"
                  button-style="solid"
                >
                  <a-radio-button :value="false">操作系统</a-radio-button>
                  <!-- <a-radio-button  :value="true">克隆已有服务器</a-radio-button> -->
                </a-radio-group>
              </a-form-item>
              <a-form-item v-if="temp.content.needPreImageIp" label="镜像规格:" name="preImageIp">
                <a-select
                  v-model:value="temp.content.preImageIp"
                  @change="preImageIpChange"
                  style="width: 280px"
                  @popupScroll="handlePopupScroll"
                  @search="handleSearchIp"
                  placeholder="选择镜像IP"
                  show-search
                  allowClear
                >
                  <a-select-option v-for="i in ipInfo" :key="i.ip" :value="JSON.stringify(i)">
                    {{ i.ip + '(' + i.hostname + ')' }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item v-if="!temp.content.needPreImageIp" label="镜像规格:" name="os">
                <a-select style="width: 280px" v-model:value="temp.content.os" placeholder="请选择系统">
                  <a-select-option v-for="i in osOptions" :key="i.value" :value="i.value">
                    {{ i.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-model-item label="数量" name="num">
                <a-input-number
                  id="inputNumber"
                  v-model:value="temp.content.num"
                  :min="1"
                  :max="temp.content.isFromControl ? 1 : 100"
                  @change="getAllAssetPrice"
                />
              </a-form-model-item>
              <a-form-model-item label="申请理由" name="reason">
                <a-textarea style="width: 400px" v-model:value="temp.content.reason" />
              </a-form-model-item>
            </a-card>
            <!-- 磁盘 -->
            <a-card style="margin-top: 14px" :bordered="false" title="磁盘">
              <a-form-model-item label="系统盘" name="content.systemDiskSize">
                <span>{{ temp.content.systemDiskSize }}GB</span>
              </a-form-model-item>
              <a-form-model-item v-if="temp.content.application != 'redis'" label="额外数据盘">
                <a-radio-group v-model:value="temp.content.needOtherDisks" button-style="solid">
                  <a-radio-button :value="false">否</a-radio-button>
                  <a-radio-button :value="true">是</a-radio-button>
                </a-radio-group>
                <div v-if="temp.content.needOtherDisks">
                  <a-input-group compact>
                    <a-select
                      @change="diskTypeChange"
                      v-model:value="temp.content.diskType"
                      placeholder="为空则不添加额外磁盘"
                      allowClear
                    >
                      <a-select-option v-for="item in diskTypeList" :key="item.key" :value="item.value">
                        {{ item.label }}
                      </a-select-option>
                    </a-select>
                    <a-input-number
                      v-model:value="temp.content.diskSize"
                      :min="minDiskSize"
                      :step="100"
                      addon-after="G"
                      style="width: 30%"
                      :formatter="value => `${value}`"
                      @change="diskSizeChange"
                    />
                  </a-input-group>
                </div>
              </a-form-model-item>
            </a-card>
            <!-- 资产标签 -->
            <div class="customCard">
              <p class="customTitle">
                <a-icon
                  type="caret-down"
                  v-if="assetTagVis"
                  @click="assetTagVis = !assetTagVis"
                  style="cursor: pointer; font-size: 12px"
                />
                <a-icon
                  type="caret-up"
                  v-else
                  @click="assetTagVis = !assetTagVis"
                  style="cursor: pointer; font-size: 12px"
                />
                <span style="margin-left: 14px">资产标签</span>
                <span style="color: #9e9e9e; margin-left: 20px; font-size: 14px">其他负责人 | 环境 | 项目 | 应用</span>
              </p>
              <p class="customContent" v-if="assetTagVis">
                <a-form-model-item label="其他负责人">
                  <a-select
                    v-model:value="temp.content.principal"
                    :options="principalListMini"
                    :filter-option="filterOption"
                    mode="multiple"
                    style="width: 362px"
                    placeholder="负责人可输入或选择"
                    @popupScroll="handlePopupScrollUser"
                    @search="handleSearch"
                  ></a-select>
                </a-form-model-item>
                <a-form-model-item label="环境" name="env">
                  <!-- <RadioEnv v-model="temp.content.env" /> -->
                  <a-radio-group v-model:value="temp.content.env" button-style="solid">
                    <a-radio-button value="online">正式环境</a-radio-button>
                    <a-radio-button value="pre">预发布环境</a-radio-button>
                    <a-radio-button value="test">测试环境</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="项目" name="projects">
                  <x-select
                    @change="projectChange"
                    mode="tags"
                    v-model:value="temp.content.projects"
                    :options="projectList"
                    placeholder="项目可输入或选择"
                    style="width: 362px"
                  ></x-select>
                </a-form-model-item>
                <a-form-model-item label="应用" name="application">
                  <a-radio-group
                    style="margin-bottom: 20px"
                    v-model:value="temp.content.application"
                    button-style="solid"
                    @change="applicationChange"
                  >
                    <a-radio-button :disabled="$route.query.dbType == 'addMysql'" value="mongoDB">
                      MongoDB
                    </a-radio-button>
                    <a-radio-button value="mysql">Mysql</a-radio-button>
                    <a-radio-button :disabled="$route.query.dbType == 'addMysql'" value="redis">Redis</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item v-if="temp.content.application.includes('mysql') && !temp.content.isFromControl">
                  <li>
                    <p class="itemTitle">架构类型</p>
                    <p class="itemContent">
                      <a-radio-group
                        style="margin-bottom: 20px"
                        v-model:value="temp.content.architectureType"
                        button-style="solid"
                      >
                        <a-radio-button value="single" :disabled="temp.content.highAvailability">单节点</a-radio-button>
                        <a-radio-button :disabled="temp.content.num != 2" value="masterSlave">主从</a-radio-button>
                        <!-- <a-radio-button :disabled="temp.content.num < 3" value="cluster">集群</a-radio-button> -->
                      </a-radio-group>
                    </p>
                  </li>
                </a-form-model-item>
                <a-form-model-item v-if="temp.content.application == 'redis'" label="Redis配置">
                  <div class="redisBox">
                    <ul style="padding: 0">
                      <li v-if="temp.content.thread">
                        <p class="itemTitle">线程数</p>
                        <p class="itemContent">
                          {{ temp.content.thread }}
                        </p>
                      </li>
                      <li v-if="temp.content.maxMemory">
                        <p class="itemTitle">最大内存限制</p>
                        <p class="itemContent">
                          {{ temp.content.maxMemory }}
                        </p>
                      </li>
                      <li>
                        <p class="itemTitle">内存淘汰策略</p>
                        <p class="itemContent">
                          <x-select
                            v-model:value="temp.content.memoryObsolescence"
                            :options="memoryObsolescenceList"
                            placeholder="选择内存淘汰策略"
                            style="width: 362px"
                            :allowClear="false"
                          ></x-select>
                        </p>
                        <a-tooltip :overlayStyle="{ width: '300px' }">
                          <template #title>
                            <span
                              v-html="
                                '内存不足时，淘汰数据以释放空间的策略选项。<br/>volatile-lru:使用近似LRU算法，仅淘汰设置了过期时间的键;<br/>volatile-lru:使用近似LRU算法，仅淘汰设置了过期时间的键;<br/>allkeys-lru:使用进入LRU算法，淘汰任意键<br/>volatile-lfu:使用近似LFU算法，仅淘汰设置了过期时间的键;<br/>allkeys-lfu:使用近似LFU算法，淘汰任意键;<br/>volatile-random:随机淘汰一个设置了过期时间的键;<br/>allkeys-random:随机淘汰任意键;<br/>volatile-ttl:淘汰最近过期时间的键，<br/>noeviction:不淘汰任何键，写操作返回错误。'
                              "
                            />
                          </template>
                          <a-icon
                            style="margin-left: 8px; font-size: 20px; color: blue"
                            theme="filled"
                            type="question-circle"
                          />
                        </a-tooltip>
                      </li>
                      <li>
                        <p class="itemTitle">持久化策略</p>
                        <p class="itemContent">
                          <x-select
                            :allowClear="false"
                            v-model:value="temp.content.persistence"
                            :options="persistenceList"
                            placeholder="选择持久化策略"
                            style="width: 362px"
                          ></x-select>
                        </p>
                        <a-tooltip :overlayStyle="{ width: '300px' }">
                          <template #title>
                            <span
                              v-html="
                                '如果你需要能够快速恢复且不介意丢失最近的数据RDB可能更适合你。如果你需要确保数据的完整性并且可以接受较慢的恢复速度，AOF可能是更好的选择'
                              "
                            />
                          </template>
                          <a-icon
                            style="margin-left: 8px; font-size: 20px; color: blue"
                            theme="filled"
                            type="question-circle"
                          />
                        </a-tooltip>
                      </li>
                      <li v-if="temp.content.persistence == 'rdb'">
                        <ul style="padding: 0; margin-bottom: 16px">
                          <li style="color: #ccc; font-size: 12px">
                            键变更数量达到设定指标后，在设定的间隔时长后执行持久化
                          </li>
                          <li style="display: flex; justify-content: space-between">
                            <span>键变更数量</span>
                            <span>持久化执行间隔（秒）</span>
                            <span></span>
                          </li>
                          <li
                            v-for="(i, index) in temp.content.RDBconfig"
                            :key="i"
                            style="display: flex; justify-content: space-between; margin-top: 10px"
                          >
                            <span>
                              <a-input-number v-model:value="i.keyNum" :min="0" :step="1" />
                            </span>
                            <span><a-input-number v-model:value="i.intervalTime" :min="0" :step="100" /></span>
                            <span style="font-size: 18px; color: #1677ff; width: 60px">
                              <span style="cursor: pointer" v-if="index == 0" @click="configOpt('add', i)">
                                <PlusSquareOutlined />
                              </span>
                              <span
                                @click="configOpt('reduce', i)"
                                v-if="temp.content.RDBconfig.length > 1"
                                style="margin-left: 10px; cursor: pointer"
                              >
                                <MinusSquareOutlined />
                              </span>
                            </span>
                          </li>
                        </ul>
                      </li>
                      <li v-if="!temp.content.isFromControl">
                        <p class="itemTitle">架构类型</p>
                        <p class="itemContent">
                          <a-radio-group
                            style="margin-bottom: 20px"
                            v-model:value="temp.content.architectureType"
                            button-style="solid"
                            @change="architectureTypeChange"
                          >
                            <a-radio-button :disabled="temp.content.highAvailability" value="single">
                              单节点
                            </a-radio-button>
                            <a-radio-button :disabled="temp.content.num != 2" value="masterSlave">主从</a-radio-button>
                            <a-radio-button
                              :disabled="temp.content.num < 3 || temp.content.highAvailability"
                              value="cluster"
                            >
                              集群
                            </a-radio-button>
                          </a-radio-group>
                        </p>
                      </li>
                      <li v-if="temp.content.architectureType == 'cluster'">
                        <p class="itemTitle">副本数量</p>
                        <p class="itemContent">
                          <a-radio-group
                            style="margin-bottom: 20px"
                            v-model:value="temp.content.duplicateNum"
                            button-style="solid"
                          >
                            <a-radio-button
                              :disabled="temp.content.duplicateNum == '1' && duplicateNumDisabled"
                              value="0"
                            >
                              0
                            </a-radio-button>
                            <a-radio-button
                              :disabled="temp.content.duplicateNum == '0' && duplicateNumDisabled"
                              value="1"
                            >
                              1
                            </a-radio-button>
                          </a-radio-group>

                          <!-- {{ temp.content.duplicateNum }} -->
                          <!-- <a-input-number
                            id="inputNumber"
                            :disabled="duplicateNumDisabled"
                            v-model="temp.content.duplicateNum"
                            :min="0"
                            :max="10"
                          /> -->
                          <!-- <a-radio-group
                            style="margin-bottom: 20px"
                            v-model:value="temp.content.duplicateNum"
                            button-style="solid"
                          >
                            <a-radio value="0">0</a-radio>
                            <a-radio value="1">1</a-radio>
                          </a-radio-group> -->
                        </p>
                      </li>
                    </ul>
                  </div>
                </a-form-model-item>
              </p>
            </div>
            <!-- 费用标签 -->
            <div class="customCard">
              <p class="customTitle">
                <a-icon
                  type="caret-down"
                  v-if="costTagVis"
                  @click="costTagVis = !costTagVis"
                  style="cursor: pointer; font-size: 12px"
                />
                <a-icon
                  type="caret-up"
                  v-else
                  @click="costTagVis = !costTagVis"
                  style="cursor: pointer; font-size: 12px"
                />
                <span style="margin-left: 14px">费用标签</span>
                <span style="color: #9e9e9e; margin-left: 20px; font-size: 14px">
                  费用负责人 | 事业部/部门/团队 | 费用承担业务方 | 产品线/承担比例 | 是否属于DG资产
                </span>
              </p>
              <p class="customContent" v-if="costTagVis">
                <a-form-model-item label="费用负责人" name="costUser">
                  <a-select
                    :allowClear="false"
                    style="width: 362px"
                    placeholder="请输入邮箱选择"
                    v-model:value="temp.content.costUser"
                    :options="principalListMini"
                    :filter-option="filterOption"
                    :showSearch="true"
                    @popupScroll="handlePopupScrollUser"
                    @search="handleSearch"
                    @select="getUserTeamListMethod"
                  ></a-select>
                </a-form-model-item>
                <a-form-model-item label="事业部/部门/团队" name="arrorg">
                  <a-form-item-rest>
                    <a-select style="width: 362px" v-model:value="temp.content.arrorg" change-on-select>
                      <a-select-option v-for="i in organizationOptions" :value="i">
                        {{ i }}
                      </a-select-option>
                    </a-select>
                  </a-form-item-rest>
                </a-form-model-item>
                <a-form-model-item label="费用承担业务方" name="business">
                  <a-select style="width: 362px" v-model:value="temp.content.business" :allowClear="true" @change="changeCostAttr">
                    <a-select-option v-for="business in businessList" :key="business" :value="business">
                      {{ business }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="费用类型" name="business">
                  <a-radio-group v-model:value="temp.content.costAttr" button-style="solid" disabled>
                    <a-radio-button value="运营成本">运营</a-radio-button>
                    <a-radio-button value="研发成本">研发</a-radio-button>
                    <a-radio-button value="开发支出">开发支出</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item
                  v-if="
                    (temp.content.costAttr === '运营成本' &&
                    temp.content.business !== '大数据' &&
                    temp.content.business !== '搜索(DG)') || temp.content.costAttr === '开发支出'
                  "
                  label="根据IP同步产品线"
                  name="productLineByIp"
                >
                  <a-input-search
                    style="width: 200px"
                    placeholder="输入IP"
                    enter-button
                    @search="onSearchProductLineByIp"
                  />
                </a-form-model-item>
                <a-form-model-item
                  v-if="
                    (temp.content.costAttr === '运营成本' &&
                    temp.content.business !== '大数据' &&
                    temp.content.business !== '搜索(DG)') || temp.content.costAttr === '开发支出'
                  "
                  label="产品线/承担比例"
                  name="productLineList"
                >
                  <a-form name="productLineList_item" :model="temp.content.productLineList">
                    <a-row
                      :gutter="24"
                      v-for="(productLine, index) in temp.content.productLineList"
                      v-bind="formItemLayoutWithOutLabel"
                    >
                      <a-col :span="8">
                        <a-form-item name="form.productLineList[index].productLineList">
                          <a-form-item-rest>
                            <a-cascader
                              v-model:value="productLine.productLineNameList"
                              :options="productLineOption"
                              change-on-select
                              placeholder="请选择产品线"
                            />
                          </a-form-item-rest>
                        </a-form-item>
                      </a-col>
                      <a-col :span="6">
                        <a-form-item name="form.productLineList[index].ratio">
                          <!-- <a-col :span="12">
                              <a-slider v-model:value="productLine.ratio" :min="0" :step="0.1" :max="1" />
                            </a-col> -->
                          <a-input-number
                            style="display: inline-block"
                            v-model:value="productLine.ratio"
                            :min="0"
                            :step="1"
                            :max="100"
                            :precision="2"
                            placeholder="比例"
                          />
                          <span style="margin-left: 4px">%</span>
                        </a-form-item>
                      </a-col>
                      <a-col :span="4">
                        <a-form-item>
                          <tx-button
                            v-if="index === 0"
                            type="primary"
                            icon="plus"
                            style="width: 30px"
                            size="small"
                            @click="addRow"
                          ></tx-button>
                          <tx-button
                            v-else
                            type="dashed"
                            size="small"
                            icon="minus"
                            style="width: 30px"
                            @click="removeRow(index)"
                          ></tx-button>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </a-form-model-item>
                <a-form-model-item label="是否属于DG资产" v-if="temp.content.business == 'DG'" name="isDgDataAsset">
                  <a-radio-group v-model:value="temp.content.isDgDataAsset" button-style="solid" @change="changeCostAttr">
                    <a-radio :value="false">不属于</a-radio>
                    <a-radio :value="true">属于</a-radio>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item
                  label="DG数据资产类别"
                  name="dgDataAssetClassification"
                  v-if="temp.content.isDgDataAsset && temp.content.business == 'DG'"
                >
                  <a-select
                    :allowClear="false"
                    style="width: 362px"
                    v-model:value="temp.content.dgDataAssetClassification"
                    placeholder="请选择DG数据资产类别"
                    @change="handleDGSpecificBusiness"
                  >
                    <a-select-option value="基础数据">基础数据</a-select-option>
                    <a-select-option value="知识数据">知识数据</a-select-option>
                    <a-select-option value="组件数据">组件数据</a-select-option>
                  </a-select>
                </a-form-model-item>
              </p>
            </div>
            <!-- 高级配置 -->
            <div class="customCard">
              <p class="customTitle">
                <a-icon
                  type="caret-down"
                  v-if="higherConfigVis"
                  @click="higherConfigVis = !higherConfigVis"
                  style="cursor: pointer; font-size: 12px"
                />
                <a-icon
                  type="caret-up"
                  v-else
                  @click="higherConfigVis = !higherConfigVis"
                  style="cursor: pointer; font-size: 12px"
                />
                <span style="margin-left: 14px">高级配置</span>
                <span style="color: #9e9e9e; margin-left: 20px; font-size: 14px">
                  自定义服务器名 | 高可用 | vlan网段
                </span>
              </p>
              <p class="customContent" v-if="higherConfigVis">
                <a-form-model-item
                  v-if="
                    ((temp.content.preImageIp || temp.content.os) &&
                      temp.content.business &&
                      temp.content.application) ||
                    temp.content.arrorg.includes('DG')
                  "
                  label="主机名"
                  name="projectName"
                >
                  <a-input
                    style="display: inline-block; width: 265px"
                    v-model:value="temp.content.projectName"
                    placeholder="主机名"
                  />
                  <a-tooltip :overlayStyle="{ width: '300px' }">
                    <template #title>
                      <span
                        v-html="
                          '主机名命名规则一般为:<br/><b>操作系统名称-业务名-应用名-ip后5位</b>。<span>分配ip后，ip后5位由系统自动在主机名中补充。<br />服务如您需要自定义主机名，请不要在主机名中使用中文、空格或者/<br />DG业务和DG(搜索)的主机名需要用户自定义填写</span>'
                        "
                      />
                    </template>
                    <a-icon
                      style="margin-left: 8px; font-size: 20px; color: blue"
                      theme="filled"
                      type="question-circle"
                    />
                  </a-tooltip>
                </a-form-model-item>
                <a-form-model-item label="Vlan网段" name="vlan">
                  <x-select
                    v-model:value="temp.content.vlan"
                    :options="vlanList"
                    placeholder="选择vlan网段"
                    style="width: 362px"
                  ></x-select>
                </a-form-model-item>
                <a-form-model-item
                  v-if="
                    temp.content.application != 'mongoDB' &&
                    temp.content.architectureType == 'masterSlave' &&
                    !temp.content.isFromControl
                  "
                  label="高可用"
                  name="highAvailability"
                >
                  <a-switch @change="vipChange" v-model:checked="temp.content.highAvailability" />
                  <a-tooltip :overlayStyle="{ width: '300px' }">
                    <template #title>
                      为本次工单申请的服务器配置虚拟IP，当提供服务的主服务器宕机时，服务会切换到备用服务器上继续对外提供服务，以保证服务的连续性
                    </template>
                    <a-icon
                      style="margin-left: 8px; font-size: 20px; color: blue"
                      theme="filled"
                      type="question-circle"
                    />
                  </a-tooltip>
                </a-form-model-item>
              </p>
            </div>
            <div></div>
          </a-form-model>
        </div>
        <!-- 上级领导审批页面 -->
        <div v-if="temp.node != 0 && temp.node != 3">
          <a-card style="margin-top: 12px" :bordered="false" title="数据库服务器创建信息">
            <dbAddList :temp="temp" :showTips="false" />
          </a-card>
        </div>
      </div>
      <div class="fixedBox" style="width: calc(40% - 14px)">
        <a-card style="height: auto; margin-top: 12px; padding-bottom: 20px" :bordered="false" :title="rightHandTitle">
          <!-- 第一步创建 -->
          <div v-if="temp.node === 0" style="padding: 0; font-size: 16px; height: auto; overflow-y: auto">
            <dbAddList :temp="temp" :showTips="false" />
          </div>
          <!-- 管理员审批-->
          <div v-if="temp.node === 3 && temp.handlerEmail.includes(localUser)">
            <dbAddLeaderPage
              ref="adminPage"
              @getMachines="getMachines"
              @radioChange="radioChange"
              @handlerMachine="handlerMachine"
              @searchMachine="searchMachine"
              :temp="temp"
            />
          </div>
          <!-- DBA审批 -->
          <div v-if="temp.node == 4">
            <MysqlConfig ref="mysqlConfig" :temp="temp" @paramsChange="paramsChange" />
          </div>
          <!-- 完成页面 -->
          <div v-if="temp.node == 5">
            <dbAddFinish @getMachines="getMachines" @searchMachine="searchMachine" :temp="temp" />
          </div>
          <div style="margin-top: 20px">
            <a-row :gutter="24">
              <a-col v-if="temp.node > 0" :span="24">
                <a-form-model-item label="审批节点">
                  <a-timeline>
                    <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                      {{ activity }}
                    </a-timeline-item>
                  </a-timeline>
                </a-form-model-item>
                <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/备注">
                  <a-textarea v-model:value="temp.comment" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <div v-if="temp.node === 0">
              <div class="itemContent" style="width: 100%">
                <div v-if="priceLoading">
                  <a-icon style="font-size: 25px; margin-left: 20px" type="loading" />
                </div>
                <div v-else>
                  <p class="myColl">
                    <a-collapse :expand-icon-position="'right'" :bordered="false">
                      <a-collapse-panel header="预估月成本" style="width: 100%">
                        <p>
                          <span class="">CPU/内存费用：</span>
                          <span class="" style="float: right; margin-right: 20px">
                            ￥{{ temp.content.priceInfo.discountPrice }} （原价 ¥
                            {{ temp.content.priceInfo.originalPrice }}）
                          </span>
                        </p>
                        <p>
                          <span class="">磁盘费用：</span>
                          <span class="" style="float: right; margin-right: 20px">
                            ￥{{ temp.content.priceInfo.discountDiskPrice }} （原价 ¥
                            {{ temp.content.priceInfo.originalDiskPrice }}）
                          </span>
                        </p>
                        <template #extra>
                          <p style="line-height: 15px">
                            <span style="font-size: 14px; color: grey; float: right; line-height: 18px">
                              （原价 ¥
                              {{ temp.content.priceInfo.allOrigin }}）
                            </span>
                            <span
                              class="shadowPrice"
                              style="
                                float: right;
                                margin-right: 8px;
                                font-size: 20px;
                                font-weight: bolder;
                                color: #ff4500;
                              "
                            >
                              <span style="font-size: 14px">￥</span>
                              {{ temp.content.priceInfo.allDiscount }}
                            </span>
                          </p>
                        </template>
                      </a-collapse-panel>
                    </a-collapse>
                  </p>
                </div>
              </div>
              <div>
                <tx-button type="primary" @click="createData">提交</tx-button>
                <tx-button style="margin-left: 10px">
                  <router-link to="/server/asset-list">取消</router-link>
                </tx-button>
              </div>
            </div>

            <p v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1">
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </p>
            <p v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1">
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </p>
            <p v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1">
              <tx-button type="primary" @click="manageApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </p>
            <p v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1">
              <tx-button type="primary" @click="createAsset">同意</tx-button>
              <!-- <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button> -->
            </p>
            <p
              v-else-if="
                temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 5 && node_status === 1
              "
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </p>
          </div>
        </a-card>
      </div>
      <a-modal
        width="60%"
        title="您本次创建的服务器配置信息如下"
        :visible="serverAddVis"
        @ok="handleOk"
        @cancel="serverAddVis = false"
      >
        <dbAddList :temp="temp" />
      </a-modal>
    </div>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import user from '@/store/modules/user'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder, getTemplateSelect } from '@/api/workflow/order'
import {
  aliyunBaseInfo,
  aliyunDiskInfo,
  awsBaseInfo,
  tencentBaseInfo,
  vmBaseInfo,
  getAssetPrice,
  awsSecurityBaseInfo,
  getAllAssetsIp,
  cpuList,
  allocationHost,
  addMysqlSlave,
  addRedisSlave,
} from '@/api/workflow/asset_automation'
import { vlanOptionsList } from '@/api/network/vlan'
import { getUserEmailList, getUserList } from '@/api/permission/user'
import {getAssetListApplication, getAssetListProject, getProductLineByIp, GetUserOrgAllInfo} from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'
import { getProductLineList } from '@/api/cost/label'
import BusinessSelect from '@/suite/SelectBusiness/index.vue'
import dbAddList from '@/views/workflow/list/comp/serverdb/dbAddList.vue'
import dbAddLeaderPage from '@/views/workflow/list/comp/serverdb/dbAddLeaderPage.vue'
import dbAddFinish from '@/views/workflow/list/comp/serverdb/dbAddFinish.vue'
import MysqlConfig from '@/views/workflow/list/comp/serverdb/mysqlConfig.vue'
const dict = {
  OCR: 'Ocr',
  ACG: 'Acg',
  AI开放平台: 'AiPlatform',
  总裁办: 'Zcb',
  'CS-CC': 'Cc',
  CS: 'Cs',
  SSG: 'Ssg',
  DG: 'Qxb',
  '搜索(DG)': 'Search',
  AIM图像算法: 'Ima',
  AIM机器视觉算法: 'Cv',
  AIM自然语言算法: 'Nlp',
  AIM平台架构: 'Arch',
  AIM运维: 'Autom',
  大数据: 'Bigdata',
  AIM企业信息化: 'Inner',
  AIM安全与合规: 'Sec',
  橘子兼职: 'Dps',
}

const osOptions = [
  {
    value: 'centos7',
    label: 'CentOS 7',
  },
  // {
  //   value: 'ubuntu_server 18.04',
  //   label: 'Ubuntu Server 18.04',
  // },
  {
    value: 'ubuntu_server_22.04',
    label: 'Ubuntu Server 22.04',
  },
  // {
  //   value: 'windows_server_2016',
  //   label: 'Windows Server 2016',
  // },
  // {
  //   value: 'windows_server_2018',
  //   label: 'Windows Server 2018',
  // },
  // {
  //   value: 'other',
  //   label: '其他',
  // },
]
const operation = ['CS-CC','CC', 'CS', 'DG', 'SSG', '搜索(DG)', '大数据', '市场', '招聘项目组', '营销云', '战略合作', '总裁办']
const vlanList = [
  {
    label: '自动分配',
    value: 1,
  },
]
const memoryObsolescenceList = [
  {
    label: 'volatile-lru ',
    value: 'volatile-lru',
  },
  {
    label: 'allkeys-lru  ',
    value: 'allkeys-lru',
  },
  {
    label: 'volatile-lfu ',
    value: 'volatile-lfu',
  },
  {
    label: 'allkeys-lfu ',
    value: 'allkeys-lfu',
  },
  {
    label: 'volatile-random ',
    value: 'volatile-random',
  },
  {
    label: 'allkeys-random',
    value: 'allkeys-random',
  },
  {
    label: 'volatile-ttl',
    value: 'volatile-ttl',
  },
  {
    label: 'noeviction ',
    value: 'noeviction',
  },
]
const persistenceList = [
  {
    label: 'None',
    value: 'none',
  },
  {
    label: 'AOF',
    value: 'aof',
  },
  {
    label: 'RDB',
    value: 'rdb',
  },
]
const RDBcolumns = [
  {
    title: '键变更数量',
    dataIndex: 'keyNum',
  },
  {
    title: '持久化执行间隔（秒）',
    dataIndex: 'intervalTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
]
let validateProjectName = async (_rule, value) => {
  if (value === '') {
    return Promise.reject('请输入自定义服务器名')
  } else {
    const reg = /^[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*$/
    console.log(reg.test(value), 'reg.test(value)')
    if (!reg.test(value)) {
      return Promise.reject('主机名仅允许输入英文、数字和-,且-不能相连，不能出现在两头')
    } else {
      return Promise.resolve()
    }
  }
}
export default {
  name: 'ServerCreate',
  components: { BusinessSelect, dbAddList, dbAddLeaderPage, dbAddFinish, MysqlConfig },
  data() {
    this.operation = operation
    return {
      minDiskSize: 200,
      RDBcolumns,
      persistenceList,
      memoryObsolescenceList,
      rightHandTitle: '配置清单',
      cpuLists: [],
      serverAddVis: false,
      vlanList,
      assetTagVis: true,
      costTagVis: true,
      higherConfigVis: true,
      organizationOptions: [],
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      localUser: user.state.email,
      serverTypeOptions: [],
      userEmailList: [],
      businessList: [],
      userTeamList: [],
      osOptions,
      priceLoading: false,
      principalList: [],
      principalNameToEmail: {},
      principalListMini: [],
      projectList: [],
      applicationList: [],
      serverOption: [],
      serverOptionMini: [],
      node_status: 1,
      productLineOption: [],
      duplicateNumDisabled: true,
      temp: {
        id: '',
        orderType: '数据库服务器创建',
        node: 0,
        status: 0,
        founderEmail: user.state.email,
        founder: user.state.name,
        time_now: '',
        handler: '',
        handlerEmail: '',
        content: {
          dbHandler: '',
          serverHandler: '',
          masterIp: '',
          hostAffine: false,
          masterId: 0,
          isFromControl: false,
          duplicateNum: '0',
          architectureType: 'single',
          RDBconfig: [
            {
              keyNum: 3600,
              intervalTime: 3600,
            },
          ],
          persistence: 'None',
          maxMemory: '',
          memoryObsolescence: 'allkeys-lru',
          thread: 0, //线程数
          highAvailability: false,
          vip: '',
          needInit: true,
          hidsInit: true,
          filterMachines: [],
          allMachines: [],

          priceInfo: {},
          allocationList: [],

          dep: undefined, // 部门
          ip: undefined,
          hostname: undefined,
          idc: 'ucloud-shanghai2-hybrid',
          serverType: '[2,4]',
          num: 1,
          os: 'centos7',
          principal: [], // 其他负责人
          org: undefined,
          project: undefined,
          projects: [],
          resultIpList: [],
          env: 'online',
          application: 'mysql',
          projectName: '',
          costUser: '',
          organization: [],
          arrorg: '',
          costAttr: '运营成本',
          business: '',
          productLineList: [
            {
              productLineNameList: [],
              ratio: 0,
            },
          ],
          isDgDataAsset: false,
          isDgBusiness: false,
          dgDataAssetClassification: '',
          dgSpecificBusiness: '',
          needOtherDisks: false,
          preInstanceType: '',
          diskType: 'HDD',
          diskSize: 200, //额外盘

          systemDiskSize: 40, //系统盘
          systemDiskType: '',
          preImageIp: null,
          permissionDescription: '',
          needPreImageIp: false,
          isDb: true,
          isAutomation: false,
          reason: '',
          vlan: 1,
          // aliyun
          // aws
          needPublicIp: 0,
          template: {},
          version: 'mysql8',
          pwdStr: '',
          approvalData: {
            projectName: '', // 同步自 content.projectName，为了通过 rules 验证
            keys: [],
            needInit: true,
            isDesktop: 0,
            hidsInit: 0,
            vlan: 1,
            hostIpList: [],
            instanceType: [],
            defaultImageId: '',
            defaultSubnetId: [],
            defaultSecurityGroupList: [],
            defaultVpcSwitchList: [],
            defaultVpcId: '',
            defaultZoneId: '',
            zoneIdList: [],
            roleName: '',
          },
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.idc': [{ required: true, message: '请选择机房', trigger: 'blur' }],
        'content.principal': [{ required: true, message: '请选择其他负责人', trigger: 'blur' }],
        'content.serverType': [{ required: true, message: '请选择配置规格', trigger: 'blur' }],
        'content.org': [{ required: true, message: '请选择所属事业部', trigger: 'blur' }],
        'content.num': [{ required: true, message: '请选择数量', trigger: 'blur' }],
        'content.dep': [{ required: true, message: '请选择部门', trigger: 'blur' }],
        'content.os': [{ required: true, message: '请选择操作系统', trigger: 'blur' }],
        'content.costUser': [{ required: true, message: '请选择费用负责人', trigger: 'blur' }],
        'content.projects': [{ required: true, message: '请选择项目', trigger: 'blur' }],
        'content.env': [{ required: true, message: '请选择环境', trigger: 'blur' }],
        'content.application': [{ required: true, message: '请选择应用', trigger: 'change' }],
        // 'content.projectName': [{ required: true, message: '请输入自定义服务器名', trigger: 'blur' }],
        'content.projectName': [{ validator: validateProjectName, trigger: 'change' }],
        'content.vlan': [{ required: true, message: '请选择vlan网段', trigger: 'blur' }],
        'content.productLineList': [{ required: true, message: '请选择产品线', trigger: 'blur' }],
        'content.isDgDataAsset': [{ required: true, message: '请选择是否为DG数据资产', trigger: 'blur' }],
        'content.dgDataAssetClassification': [{ required: true, message: '请选择DG数据资产类别', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写理由', trigger: 'blur' }],
        'content.preImageIp': [{ required: true, message: '请选择系统', trigger: 'blur' }],
      }),
      awsRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultSubnetId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      aliyunRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.defaultZoneId': [{ required: true, message: '请确认可用区', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultVpcId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultVpcSwitchList': [{ required: true, message: '请确认交换机ID', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      tencentRules: antdFormRulesFormat({
        'content.projectName': [{ required: true, message: '请确认项目名', trigger: 'change' }],
        'content.approvalData.defaultZoneId': [{ required: true, message: '请确认可用区', trigger: 'change' }],
        'content.approvalData.instanceType': [{ required: true, message: '请确认类型', trigger: 'change' }],
        'content.approvalData.defaultVpcId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultSubnetId': [{ required: true, message: '请确认网络', trigger: 'change' }],
        'content.approvalData.defaultImageId': [{ required: true, message: '请确认镜像', trigger: 'change' }],
        'content.approvalData.defaultSecurityGroupList': [
          { required: true, message: '请确认安全组', trigger: 'change' },
        ],
      }),
      assetRules: {
        projectName: [{ required: true, message: '请确认项目名', trigger: 'change' }],
        hostIpList: [{ required: true, message: '请确认物理机', trigger: 'change' }],
        vlan: [{ required: true, message: '请确认vlan', trigger: 'change' }],
      },
      vpcInfo: [
        { vpcSwitchList: [{}] },
        { vpcSecurityGroupList: [], vpcSwitchList: [{}] },
        { vpcSecurityGroupList: [], vpcSwitchList: [{}] },
        { vpcSecurityGroupList: [], vpcSwitchList: [{}] },
      ],
      tencentVpcInfo: [{ subnetInfo: [{}] }],
      needPublicIpList: [
        { key: 0, label: '否' },
        { key: 1, label: '是' },
      ],
      diskTypeList: [
        { label: '固态硬盘', key: '2', value: 'SSD' },
        { label: '机械硬盘', key: '1', value: 'HDD' },
      ],
      aliyunDiskTypeList: [
        { diskName: '高效云盘', diskType: 'cloud_efficiency', key: 'HDD' },
        { diskName: 'SSD云盘', diskType: 'cloud_ssd', key: 'SSD' },
      ],
      tencentDiskTypeList: [
        { label: '普通云硬盘', value: 'CLOUD_BASIC', key: 'HDD' },
        { label: 'SSD云硬盘', value: 'CLOUD_SSD', key: 'SSD' },
      ],
      instanceTypeList: [{ zoneId: '', zoneInstanceTypeList: [{}] }],
      imageList: [],
      subnetList: [],
      securityGroupList: [],
      preProjectNameLIst: [],
      preStatus: false,
      vpcStatus: false,
      tipsStatus: false,
      modalFormBindServerVisible: false,
      vpcIndex: 4,
      tencentVpcIndex: 3,
      instance_index: 0,
      scrollPage: 1,
      serverScrollPage: 1,
      valueData: '',
      serverValueData: '',
      treePageSize: 50,
      modalFormPreBindServerVisible: false,
      // 前端将后端的vpc嵌套信息进行过滤
      multipleInstanceList: [],
      aliyunSystemDiskTypeDetailList: [],
      aliyunDataDiskTypeDetailList: [],
      // 阿里云vpc下switch信息
      multipleVpcSwitchList: [],
      // 腾讯云vpc下subnet信息
      multipleVpcSubnetList: [],
      preCreateMsgs: [{}],
      // 阿里云磁盘信息
      systemDiskMaxSize: 2048,
      systemDiskMinSize: 20,
      dataDiskMaxSize: 2048,
      dataDiskMinSize: 20,
      //镜像处理
      ipInfo: [],
      showImageTips: false,
      request: {
        pageNo: 1,
        isAdmin: true,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
      },
    }
  },
  async created() {
    await this.getUserBaseInfo()
    this.getBaseInfoList()
    this.startGetAssetListInfo()
    this.getProductLineList()
    this.getList()

    setTimeout(() => {})
  },
  mounted() {
    console.log(this.$route, 'route')
  },
  watch: {
    // 项目名称
    'temp.content.projectName': {
      handler(value) {
        if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
        this.temp.content.approvalData.projectName = value
      },
      immediate: true,
    },
    // 机房
    'temp.content.idc': {
      handler(value) {
        if (value) {
          if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
          this.vlanFn()
        }
      },
      immediate: true,
    },
    // 环境
    'temp.content.env': {
      handler(value) {
        console.log(this.temp.node, 'nodenodenode')
        if (value) {
          if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
          this.vlanFn()
        }
      },
      immediate: true,
    },
    // 费用承担业务方
    'temp.content.business': {
      handler(value) {
        if (value) {
          this.vlanFn()
          this.defineName()
          if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
          if (value != 'DG') {
            this.temp.content.isDgDataAsset = false
            this.temp.content.dgDataAssetClassification = ''
          }
        }
      },
      immediate: true,
    },
    // 产品线承担比例
    'temp.content.productLineList': {
      handler(value) {
        if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
        let percent = 0
        value.forEach(function (item) {
          percent += item.ratio
        })
        if (percent > 100) {
          notification.error({
            message: '承担比例错误',
            description: '承担比例总和不应大于100%',
          })
        }
      },
      deep: true,
      immediate: true,
    },
    // vlan网段
    'temp.content.vlan': {
      handler(value) {
        if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
        this.temp.content.approvalData.vlan = value
      },
      immediate: true,
    },
    // 镜像IP
    'temp.content.preImageIp': {
      handler(value) {
        if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
        if (
          (this.temp.content.preImageIp || this.temp.content.os) &&
          this.temp.content.business &&
          this.temp.content.application
        ) {
          const sys = this.temp.content.preImageIp ? JSON.parse(this.temp.content.preImageIp).ip : this.temp.content.os
          const bus = this.temp.content.business[0] + this.temp.content.business[1]
          const app = this.temp.content.application
          this.temp.content.projectName = sys + '-' + bus + '-' + app
          this.defineName()
        }
      },
      immediate: true,
    },
    // 系统
    'temp.content.os': {
      handler(value) {
        if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
        this.defineName()
      },
      immediate: true,
    },
    // 应用
    'temp.content.application': {
      handler(value) {
        if (value == 'redis') {
          this.temp.content.needOtherDisks = false
          this.temp.content.diskSize = 0
        }
        if (this.$router.query && this.$router.query.id && this.temp.node != 3 && this.temp.node != 0) return
        this.defineName()
      },
      immediate: true,
    },
    'temp.content.arrorg': {
      handler(value) {
        if (this.$route.query.id) {
          return
        }
        if (value.includes('DG')) {
          this.temp.content.projectName = ''
        }
      },
      immediate: true,
    },
    'temp.content.num': {
      handler(value) {
        if (this.$route.query.id) {
          return
        }

        if (value == 1) {
          this.temp.content.architectureType = 'single'
          this.temp.content.highAvailability = false
        }
        if (this.temp.content.application == 'redis') {
          if (value > 5 && !(value % 2)) {
            this.temp.content.duplicateNum = '1'
            this.duplicateNumDisabled = false
          } else if (value > 5 && value % 2) {
            this.temp.content.duplicateNum = '0'
            this.duplicateNumDisabled = true
          }
          if (value > 2 && value < 6) {
            this.temp.content.duplicateNum = '0'
            this.duplicateNumDisabled = true
          }
          if (value == 2 && !this.temp.content.architectureType) {
            this.temp.content.architectureType = 'masterSlave'
          } else if (value == 2 && this.temp.content.architectureType == 'cluster') {
            this.temp.content.architectureType = 'masterSlave'
          } else if (value > 2 && !this.temp.content.architectureType) {
            this.temp.content.architectureType = 'masterSlave'
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    diskSizeChange(val) {
      if (val % 480 == 0) {
        this.temp.content.diskSize = val
        this.getAllAssetPrice()
        return
      } else if (val % 100 != 0) {
        this.temp.content.diskSize = this.temp.content.diskSize - (val % 100)
      }
      this.getAllAssetPrice()
    },
    architectureTypeChange() {
      // console.log(this.temp.content.architectureType, 'temp.content.architectureType')
      if (this.temp.content.application == 'redis' && this.temp.content.architectureType == 'cluster') {
        if (this.temp.content.num > 5 && !(this.temp.content.num % 2)) {
          this.temp.content.duplicateNum = '1'
          this.duplicateNumDisabled = false
        } else if (this.temp.content.num > 5 && this.temp.content.num % 2) {
          this.temp.content.duplicateNum = '0'
          this.duplicateNumDisabled = true
        }
        if (this.temp.content.num > 2 && this.temp.content.num < 6) {
          this.temp.content.duplicateNum = '0'
          this.duplicateNumDisabled = true
        }
      }
    },
    getMachines() {
      let diskType = this.temp.content.diskType
      if (this.temp.content.idc.startsWith('aliyun')) {
        diskType = this.aliyunDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
      } else if (this.temp.content.idc.startsWith('tencent')) {
        diskType = this.tencentDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
      }
      allocationHost({
        cpu: JSON.parse(this.temp.content.serverType)[0],
        mem: JSON.parse(this.temp.content.serverType)[1],
        num: this.temp.content.num,
        idc: this.temp.content.idc,
        diskType,
        diskSize: this.temp.content.diskSize,
        hostAffine: this.temp.content.hostAffine,
        needOtherDisks: this.temp.content.needOtherDisks,
        fetchAll: true,
        business: this.temp.content.business,
        env: this.temp.content.env,
      }).then(res => {
        if (res.Data.data) {
          this.temp.content.filterMachines = res.Data.data
          for (let i = 0; i < this.temp.content.allocationList.length; i++) {
            for (let v = 0; v < this.temp.content.filterMachines.length; v++) {
              if (this.temp.content.filterMachines[v].ip == this.temp.content.allocationList[i].ip) {
                this.temp.content.filterMachines[v].allocated = true
              }
            }
          }
          this.temp.content.allMachines = this.temp.content.filterMachines
        }
      })
    },
    vipChange(v) {
      if (v) {
        this.temp.content.architectureType = 'masterSlave'
      }
    },
    vlanFn(opt, vlan) {
      if (this.$route.query && this.$route.query.id && this.temp.node != 3 && this.temp.node != 0) {
        return
      } else {
        if (this.temp.content.idc && this.temp.content.env && this.temp.content.business) {
          vlanOptionsList({
            env: this.temp.content.env,
            idc: this.temp.content.idc,
            bu: this.temp.content.business,
          }).then(res => {
            console.log(***********)
            if (opt == 'addSlave') {
              console.log(vlan, 'vvvvvv')
              console.log('222222222222')
              this.temp.content.vlan = vlan
              return
            }
            this.temp.content.vlan = ''
            if (res.Data && res.Data.data) {
              this.vlanList = res.Data.data

              this.temp.content.vlan = this.vlanList[0].value
            } else {
              this.temp.content.vlan = ''
              this.vlanList = []
              notification.error({
                message: '无可用vlan网段',
                description: '请联系管理员',
              })
            }
          })
        } else {
          this.temp.content.vlan = ''
          this.vlanList = []
        }
      }
    },
    paramsChange(params) {
      if (params.type == 'version') {
        this.temp.content.version = params.val
      } else if (params.type == 'template') {
        this.temp.content.template = JSON.parse(params.val)
      }
    },
    configOpt(opt, item) {
      switch (opt) {
        case 'add':
          this.temp.content.RDBconfig.push({
            keyNum: 3600,
            intervalTime: 3600,
          })
          break
        case 'reduce':
          let index = this.temp.content.RDBconfig.indexOf(item)
          this.temp.content.RDBconfig.splice(index, 1)
          break
      }
    },
    // cpu 内存改变
    serverTypeChange() {
      this.getAllAssetPrice()
      this.applicationChange()
    },
    applicationChange() {
      if (this.temp.content.application == 'redis') {
        this.temp.content.version = 'redis6.2.14'
        this.temp.content.maxMemory = ''
        this.temp.content.memoryObsolescence = 'allkeys-lru'
        this.temp.content.persistence = 'none'
        this.temp.content.RDBconfig = [
          {
            keyNum: 3600,
            intervalTime: 3600,
          },
        ]
        this.temp.content.architectureType = null
        let arrayServerType = eval(this.temp.content.serverType)
        if (arrayServerType[0] > 3) {
          this.temp.content.thread = eval(this.temp.content.serverType)[0] - 1
        } else {
          this.temp.content.thread = 0
        }
        this.temp.content.maxMemory = Math.floor(Math.max(arrayServerType[1] - 3, arrayServerType[1] * 0.8))
        if (this.temp.content.num == 1) {
          this.temp.content.architectureType = 'single'
        } else if (this.temp.content.num == 2) {
          this.temp.content.architectureType = 'masterSlave'
        } else if (this.temp.content.num > 2 && !this.temp.content.architectureType) {
          this.temp.content.architectureType = 'masterSlave'
        }
      } else {
        if (!this.temp.content.version) {
          this.temp.content.version = 'mysql8'
        }
        if (this.temp.content.version && !this.temp.content.version.includes('mysql')) {
          this.temp.content.version = 'mysql8'
        }
        this.temp.content.thread = 0
        this.temp.content.maxMemory = ''
        this.temp.content.memoryObsolescence = null
        this.temp.content.persistence = null
        this.temp.content.RDBconfig = []
        // this.temp.content.architectureType = 'single'
        this.temp.content.duplicateNum = ''
      }
    },
    preImageIpChange(v) {},
    fixedPrice(fixedStr) {
      if (String(fixedStr).includes('.')) {
        if (String(fixedStr).split('.')[1].length > 2) {
          fixedStr = fixedStr.toFixed(2)
        } else if (String(fixedStr).split('.')[1].length == 2) {
          fixedStr = fixedStr
        } else {
          fixedStr = String(fixedStr) + '0'
        }
      } else {
        fixedStr = String(fixedStr) + '.00'
      }
      return fixedStr
    },
    handlerMachine(type, machine) {
      switch (type) {
        case 'delete':
          this.temp.content.allMachines.forEach(i => {
            if (i.ip == machine.ip) {
              i.allocated = false
            }
          })
          this.temp.content.filterMachines.forEach(i => {
            if (i.ip == machine.ip) {
              i.allocated = false
            }
          })
          this.temp.content.allocationList.forEach((i, index) => {
            if (i.ip == machine.ip) {
              this.temp.content.allocationList.splice(index, 1)
            }
          })
          break
        case 'add':
          this.temp.content.allocationList.push(machine)
          this.temp.content.allMachines.forEach(i => {
            if (i.ip == machine.ip) {
              i.allocated = true
            }
          })
          this.temp.content.filterMachines.forEach(i => {
            if (i.ip == machine.ip) {
              i.allocated = true
            }
          })
          break
      }
    },
    searchMachine(val) {
      if (val.length) {
        this.temp.content.filterMachines = []
        this.temp.content.allMachines.forEach(item => {
          if (item.hostname.includes(val) || item.ip.includes(val)) {
            this.temp.content.filterMachines.push(item)
          }
        })
      } else {
        this.temp.content.filterMachines = this.temp.content.allMachines
      }
    },
    radioChange(type, val) {
      this.temp.content[type] = val
    },
    appChange(val) {
      const reg = /[\u4e00-\u9fff]/
      if (reg.test(val[0]) || val[0].includes('|')) {
        this.temp.content.application = ''
        notification.warning({
          message: '不符合服务器命名规范',
          description: '第一个应用将用作务器命名，此应用不符合命名规范',
        })
      }
    },
    defineName() {
      if (this.$route.query.id) {
        return
      }
      if (
        // 镜像IP 费用承担业务方 应用
        (this.temp.content.preImageIp || this.temp.content.os) &&
        this.temp.content.business &&
        this.temp.content.application &&
        !this.temp.content.arrorg.includes('DG')
      ) {
        let sys = ''
        if (this.temp.content.preImageIp) {
          sys = JSON.parse(this.temp.content.preImageIp).os
        } else if (this.temp.content.os) {
          if (this.temp.content.os.includes('centos')) {
            sys = 'centos'
          } else if (this.temp.content.os.includes('ubuntu')) {
            sys = 'ubuntu'
          } else if (this.temp.content.os.includes('windows')) {
            sys = 'windows'
          } else {
            sys = 'other'
          }
        }
        let bus = ''
        if (dict[this.temp.content.business]) {
          bus = dict[this.temp.content.business]
        }
        let app = ''
        if (this.temp.content.application.includes('mysql')) {
          app = 'mysql'
        } else if (this.temp.content.application.includes('redis')) {
          app = 'redis'
        } else {
          app = 'mongodb'
        }
        // let app = this.temp.content.application
        // app = app.charAt(0).toUpperCase() + app.slice(1)
        this.temp.content.projectName = sys + '-' + bus + '-' + app
      }
    },
    needPreImageIpChange(val) {
      if (this.temp.content.needPreImageIp) {
        this.temp.content.os = ''
        this.temp.content.preImageIp = null
      } else {
        this.temp.content.preImageIp = null
        this.temp.content.os = 'centos7'
      }
    },
    projectChange(val) {
      if (val.length > 1) {
        this.temp.content.projects = [val[val.length - 1]]
      }
    },
    diskTypeChange(val) {
      switch (val) {
        case 'SSD':
          this.temp.content.diskSize = 100
          this.minDiskSize = 100
          break
        case 'HDD':
          this.temp.content.diskSize = 200
          this.minDiskSize = 200
          break
      }
    },
    getProductLineList() {
      getProductLineList().then(response => {
        if (response.Data !== undefined && response.Data !== null) {
          for (const productLine in response.Data.productLineMap) {
            if (Object.hasOwnProperty.call(response.Data.productLineMap, productLine)) {
              const secondaryProductLineList = response.Data.productLineMap[productLine].secondaryProductLine
              let childrenList = []
              for (let i = 0; i < secondaryProductLineList.length; i++) {
                let tmp = {
                  label: secondaryProductLineList[i],
                  value: secondaryProductLineList[i],
                }
                childrenList.push(tmp)
              }
              this.productLineOption.push({
                label: productLine,
                value: productLine,
                children: childrenList,
              })
            }
          }
        }
      })
    },
    startGetAssetListInfo() {
      const sendData = { idc: this.temp.content.idc }
      getAllAssetsIp(sendData).then(response => {
        if (response.Data && response.Data.info) {
          this.serverOption = response.Data.info
          this.serverOptionMini = this.serverOption.slice(0, 50)
        }
      })
    },
    getAssetListInfo(val) {
      // 阿里云，ucloud公有云20G
      if (val == 'ucloud-shanghai2-public' || val.includes('aliyun')) {
        this.temp.content.systemDiskSize = 20
      } else if (val.includes('hybrid') || val == 'shanghai8-songjiang') {
        // 混合云40G
        this.temp.content.systemDiskSize = 40
      } else {
        // 其他10GB
        this.temp.content.systemDiskSize = 10
      }
      this.getAllAssetPrice()
      const sendData = { idc: this.temp.content.idc }
      getAllAssetsIp(sendData).then(response => {
        this.serverOption = response.data.data
        this.serverOptionMini = this.serverOption.slice(0, 50)
      })
    },
    getAllAssetPrice() {
      // 获取价格
      let diskType = this.temp.content.diskType
      if (this.temp.content.idc.startsWith('aliyun')) {
        diskType = this.aliyunDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
      } else if (this.temp.content.idc.startsWith('tencent')) {
        diskType = this.tencentDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
      }
      const sendCostData = {
        cpu: JSON.parse(this.temp.content.serverType)[0],
        idc: this.temp.content.idc,
        mem: JSON.parse(this.temp.content.serverType)[1],
        diskSize: this.temp.content.diskSize,
        diskType: diskType,
        num: this.temp.content.num,
      }
      this.priceLoading = true
      getAssetPrice(sendCostData).then(response => {
        this.temp.content.priceInfo = response.Data
        let fixedStr = this.temp.content.priceInfo.discountPrice + this.temp.content.priceInfo.discountDiskPrice
        this.temp.content.priceInfo.allDiscount = this.fixedPrice(fixedStr)
        let originPrice = this.temp.content.priceInfo.originalPrice + this.temp.content.priceInfo.originalDiskPrice
        this.temp.content.priceInfo.allOrigin = this.fixedPrice(originPrice)
        // this.temp.content.discountPrice = response.Data.discountPrice
        // this.temp.content.originalPrice = response.Data.originalPrice
        // this.temp.content.priceMsg = response.Data.msg
        if (this.priceLoading) {
          this.priceLoading = false
        }
      })
    },
    // 默认选择框获取
    getUserBaseInfo() {
      getUserEmailList().then(res => {
        for (var i = 0, len = res.Data.items.length; i < len; i++) {
          const user = {}
          user.value = res.Data.items[i].key
          user.label = res.Data.items[i].key + '(' + res.Data.items[i].value + ')'
          this.principalList.push(user)
          if (i <= 50) {
            this.principalListMini.push(user)
          }
        }
        // 创建工单时，默认把费用负责人看做工单创建人
        if (this.temp.node == 0 && !this.$route.query.dbType) {
          this.temp.content.costUser = store.getters.email
          this.getUserTeamListMethod(store.getters.email)
        }
      })
    },
    getBaseInfoList() {
      getAssetListProject().then(res => {
        for (var i = 0, len = res.Data.project.length; i < len; i++) {
          var project = {}
          project.value = res.Data.project[i]
          project.label = res.Data.project[i]
          this.projectList.push(project)
        }
      })
      // getAssetListApplication().then(res => {
      //     for (var i = 0, len = res.Data.application.length; i < len; i++) {
      //         var application = {}
      //         application.value = res.Data.application[i]
      //         application.label = res.Data.application[i]
      //         this.applicationList.push(application)
      //     }
      // })
    },
    handleSearch(val) {
      this.valueData = val
      if (!val) {
        this.getUserBaseInfo()
      } else {
        this.principalListMini = []
        this.scrollPage = 1
        this.principalList.forEach(item => {
          if (item.label.indexOf(val) >= 0) {
            if (!this.principalListMini.includes(item.label)) {
              this.principalListMini.push(item)
            }
          }
        })
        this.principalListMini = this.uniqueDicts(this.principalListMini.slice(0, 50))
      }
    },
    handlePopupScrollUser(e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.principalList.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.principalList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.principalList.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.principalListMini = newData
        }
      }
    },
    handleServerSearch(val) {
      this.serverOptionMini = []
      this.serverScrollPage = 1
      this.serverOption.forEach(item => {
        if (item.ip.indexOf(val) >= 0 || item.hostname.indexOf(val) >= 0) {
          this.serverOptionMini.push(item)
        }
      })
      this.serverOptionMini = this.serverOptionMini.slice(0, 50)
    },
    handleServerPopupScroll(e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.serverScrollPage = 1
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.serverScrollPage = this.serverScrollPage + 1
          const scrollPage = this.serverScrollPage
          const treePageSize = this.treePageSize * (scrollPage || 1)
          const newData = []
          let max = ''
          if (this.serverOption.length > treePageSize) {
            max = treePageSize
          } else {
            max = this.serverOption.length
          }
          // 判断是否有搜索
          if (this.serverValueData) {
            this.serverOption.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          } else {
            this.serverOption.forEach((item, index) => {
              if (index < max) {
                newData.push(item)
              }
            })
          }
          this.serverOptionMini = newData
        }
      }
    },
    getList() {
      if (this.$route.query.id) {
        this.id = this.$route.query.id
        getOrderInfo(this.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.content.organization !== undefined && this.temp.content.organization.length >0){
              const bu = this.temp.content.organization[0]
              if (bu === 'DG' && this.temp.content.isDgDataAsset) {
                this.temp.content.costAttr = '开发支出'
              } else if (this.operation.includes(bu)) {
                this.temp.content.costAttr = '运营成本'
              } else {
                this.temp.content.costAttr = '研发成本'
              }
            }
            this.temp.content.project = this.temp.content.projects[0]
            if (this.temp.node == 2 || this.temp.node == 3) {
              this.rightHandTitle = '预处理'
            } else if (this.temp.node == 5) {
              this.rightHandTitle = '工单处理结果 '
            } else if (this.temp.node == 1) {
              this.rightHandTitle = ''
            } else if (this.temp.node == 4) {
              this.rightHandTitle = '数据库初始化'
            }
            if (this.temp.content.resultIpList === null) {
              this.temp.content.resultIpList = []
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      } else if (this.$route.query.dbType) {
        switch (this.$route.query.dbType) {
          case 'addMysql':
            this.temp.content.application = 'mysql'
            if (this.$route.query.serverId) {
              this.temp.content.masterId = this.$route.query.serverId
              this.temp.content.isFromControl = true
              addMysqlSlave({
                id: this.$route.query.serverId,
              }).then(res => {
                console.log(res, 'salveIfno')
                if (res.Data) {
                  delete res.Data.organization
                  this.temp.content = {
                    ...this.temp.content,
                    ...res.Data,
                  }
                  this.temp.content.principal == null ? (this.temp.content.principal = []) : ''
                  this.temp.content.projects == ''
                    ? (this.temp.content.projects = [])
                    : (this.temp.content.projects = [this.temp.content.projects])
                  this.temp.content.application = 'mysql'
                  this.temp.content.highAvailability = false
                  if (!this.temp.content.needOtherDisks) {
                    this.temp.content.needOtherDisks = true
                    this.temp.content.diskType = 'SSD'
                    this.temp.content.diskSize = 200
                  }
                  this.getAllAssetPrice()
                  this.getUserTeamListMethod(this.temp.content.costUser)
                }

                this.temp.content.systemDiskSize = 40 //创建从服务器 40g
                if (res.Data.workOrderId) {
                  getOrderInfo(res.Data.workOrderId).then(r => {
                    const contentData = JSON.parse(r.Data.content.data)
                    contentData.dbHandler ? (this.temp.content.dbHandler = contentData.dbHandler) : ''
                    contentData.serverHandler ? (this.temp.content.serverHandler = contentData.serverHandler) : ''
                    let that = this
                    console.log(this.temp.content, 'temp.contenttemp.content')
                    this.$nextTick(() => {
                      that.vlanFn('addSlave', res.Data.vlan)
                    })
                  })
                } else {
                  this.temp.content.dbHandler = ''
                  this.temp.content.serverHandler = ''
                }
              })
            } else {
              this.vlanFn()
            }
            break
          case 'addRedis':
            this.temp.content.application = 'redis'
            if (this.$route.query.serverId) {
              this.temp.content.masterId = this.$route.query.serverId
              this.temp.content.isFromControl = true
              this.temp.content.masterIp = this.$route.query.masterIp
              addRedisSlave({
                id: this.$route.query.serverId,
              }).then(res => {
                console.log(res, 'salveIfno')
                if (res.Data) {
                  delete res.Data.organization
                  this.temp.content = {
                    ...this.temp.content,
                    ...res.Data,
                  }
                  this.temp.content.application = 'redis'
                  this.temp.content.principal == null ? (this.temp.content.principal = []) : ''
                  this.temp.content.projects == ''
                    ? (this.temp.content.projects = [])
                    : (this.temp.content.projects = [this.temp.content.projects])
                  if (!this.temp.content.needOtherDisks) {
                    this.temp.content.needOtherDisks = false
                    this.temp.content.diskType = ''
                    this.temp.content.diskSize = 0
                  }

                  this.getAllAssetPrice()
                }
                this.temp.content.highAvailability = false
                console.log(this.temp.content, 'ccccc')
                this.temp.content.systemDiskSize = 40 //从服务器直接写死 40g’
                this.temp.content.version = 'redis6.2.14'
                this.getUserTeamListMethod(this.temp.content.costUser)
                this.$nextTick(() => {
                  this.vlanFn('addSlave', res.Data.vlan)
                })
              })
            } else {
              this.vlanFn()
            }
            break
        }
      } else {
        this.getAllAssetPrice()
      }
      cpuList().then(res => {
        this.serverTypeOptions = res.Data.data
      })
      this.GetAssetIps()
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.temp.content.application.includes('mysql')) {
            if (!this.temp.content.needOtherDisks) {
              this.$message.warning('Mysql数据库服务器需配置额外数据盘，请检查')
              return
            } else {
              if (this.temp.content.diskSize == 0) {
                this.$message.warning('额外数据盘容量不能为0')
                return
              }
            }
          }

          let diskType = this.temp.content.diskType
          if (this.temp.content.idc.startsWith('aliyun')) {
            diskType = this.aliyunDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
          } else if (this.temp.content.idc.startsWith('tencent')) {
            diskType = this.tencentDiskTypeList.filter(item => item.key === this.temp.content.diskType)[0].value
          }
          this.temp.content.allocationList = []
          // 获取分配物理机列表
          allocationHost({
            cpu: JSON.parse(this.temp.content.serverType)[0],
            mem: JSON.parse(this.temp.content.serverType)[1],
            num: this.temp.content.num,
            diskType,
            idc: this.temp.content.idc,
            diskSize: this.temp.content.diskSize,
            hostAffine: this.temp.content.hostAffine,
            needOtherDisks: this.temp.content.needOtherDisks,
            business: this.temp.content.business,
            env: this.temp.content.env,
          }).then(res => {
            if (res.Data.data) {
              this.temp.content.allocationList = res.Data.data
              // 获取全量物理机列表
              allocationHost({
                idc: this.temp.content.idc,
                cpu: JSON.parse(this.temp.content.serverType)[0],
                mem: JSON.parse(this.temp.content.serverType)[1],
                num: this.temp.content.num,
                diskType,
                diskSize: this.temp.content.diskSize,
                hostAffine: this.temp.content.hostAffine,
                needOtherDisks: this.temp.content.needOtherDisks,
                fetchAll: true,
                business: this.temp.content.business,
                env: this.temp.content.env,
              }).then(res => {
                if (res.Data.data) {
                  this.temp.content.filterMachines = res.Data.data
                  for (let i = 0; i < this.temp.content.allocationList.length; i++) {
                    for (let v = 0; v < this.temp.content.filterMachines.length; v++) {
                      if (this.temp.content.filterMachines[v].ip == this.temp.content.allocationList[i].ip) {
                        this.temp.content.filterMachines[v].allocated = true
                      }
                    }
                  }
                  this.temp.content.allMachines = this.temp.content.filterMachines
                }
              })
            }
          })

          this.serverAddVis = true
        }
      })
    },
    // 创建弹窗的确定，真正的创建，走到下一步
    handleOk() {
      if (this.temp.content.allocationList.length) {
        if (this.temp.content.needPreImageIp && this.temp.content.preImageIp === null) {
          this.showImageTips = true
        } else {
          this.showImageTips = false
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }

          this.Date()
          this.node_status = 0
          // 从数据库控制台页面跳过来的
          if (this.temp.content.isFromControl) {
            this.temp.node = 3
          } else {
            this.temp.node = 1
          }
          this.temp.status = 1
          this.temp.content.approvalData.instanceType = ''
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content.project = this.temp.content.projects[0]
          this.temp.content.organization = this.temp.content.arrorg.split('/')
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          createOrder(obj)
            .then(response => {
              if (response === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else {
                notification.success({
                  message: '创建成功',
                  description: '工单创建成功',
                })
                this.rightHandTitle = '预处理'
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                this.node_status = 1
                this.$router.push({ path: '/workflow/db-server-create', query: { id: response.Data.id } })
              }
            })
            .finally(() => {
              this.serverAddVis = false
            })
        }
      } else {
        notification.error({
          message: '创建工单失败',
          description: '宿主机预分配失败，请联系运维管理员处理',
        })
      }
    },
    calculateIntersection(...arrays) {
      if (arrays.length === 0) {
        return []
      }
      // 获取第一个数组的属性集合
      const propertiesSet = new Set(arrays[0].map(item => JSON.stringify(item)))
      // 从第二个数组开始，逐个计算交集
      for (let i = 1; i < arrays.length; i++) {
        const currentProperties = new Set(arrays[i].map(item => JSON.stringify(item)))
        // 保留与前面数组的交集
        const intersection = arrays[i].filter(item => propertiesSet.has(JSON.stringify(item)))
        // 更新属性集合
        propertiesSet.clear()
        intersection.forEach(item => propertiesSet.add(JSON.stringify(item)))
      }
      // 将结果转换为原始对象的数组形式
      const result = Array.from(propertiesSet).map(item => JSON.parse(item))
      return result
    },
    refreshZoneData(val) {
      this.$forceUpdate()
      this.temp.content.approvalData.instanceType = []
      let instanceTmpList = []
      if (val.length > 1) {
        for (var i = 0; i < val.length; i++) {
          const arr =
            this.instanceTypeList[(this.instanceTypeList || []).findIndex(item => item.zoneId === val[i])]
              .zoneInstanceTypeList
          instanceTmpList.push(arr)
        }
        this.multipleInstanceList = this.calculateIntersection(...instanceTmpList)
      } else {
        this.multipleInstanceList =
          this.instanceTypeList[
            (this.instanceTypeList || []).findIndex(item => item.zoneId === val[0])
          ].zoneInstanceTypeList
      }
      // 处理vpc
      if (this.temp.content.idc.startsWith('ali')) {
        this.multipleVpcSwitchList = this.vpcInfo[this.vpcIndex].vpcSwitchList
          .map(value => value)
          .filter(value => val.includes(value.zoneId))
        if (this.multipleVpcSwitchList.length > 0) {
          this.temp.content.approvalData.defaultVpcSwitchList = [
            this.multipleVpcSwitchList[0].vpcSwitchId + '|' + this.multipleVpcSwitchList[0].zoneId,
          ]
        }
      }
      if (this.temp.content.idc.startsWith('tencent')) {
        this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
      }
    },
    getDiskInfo(val) {
      this.temp.content.systemDiskType = ''
      this.aliyunSystemDiskTypeDetailList = []
      this.aliyunDataDiskTypeDetailList = []
      const sendSystemDiskData = {
        instanceType: val,
        resourceName: 'SystemDisk',
        zoneList: this.temp.content.approvalData.zoneIdList,
      }
      const sendDataDiskData = {
        instanceType: val,
        resourceName: 'DataDisk',
        zoneList: this.temp.content.approvalData.zoneIdList,
      }
      aliyunDiskInfo(sendSystemDiskData).then(response => {
        this.aliyunSystemDiskTypeDetailList = response.Data.data
        if (this.aliyunSystemDiskTypeDetailList.length > 0) {
          this.temp.content.systemDiskType =
            this.aliyunSystemDiskTypeDetailList[0].diskType +
            '|' +
            this.aliyunSystemDiskTypeDetailList[0].performanceLevel
        }
      })
      aliyunDiskInfo(sendDataDiskData).then(response => {
        this.aliyunDataDiskTypeDetailList = response.Data.data
      })
    },
    getSystemDiskSize(val) {
      const resData = this.aliyunSystemDiskTypeDetailList.find(
        item => item.diskType + '|' + item.performanceLevel === 'zz'
      )
      this.systemDiskMinSize = resData.diskMinSize
      this.systemDiskMaxSize = resData.diskMaxSize
    },
    getDataDiskSize(val) {
      const resData = this.aliyunDataDiskTypeDetailList.find(
        item => item.diskType + '|' + item.performanceLevel === 'zz'
      )
      this.dataDiskMinSize = resData.diskMinSize
      this.dataDiskMaxSize = resData.diskMaxSize
    },
    // 选择vpc后刷新子网
    refreshData(val) {
      this.$forceUpdate()
      this.vpcIndex = (this.vpcInfo || []).findIndex(item => item.vpcId === val)
      this.tencentVpcIndex = (this.tencentVpcInfo || []).findIndex(item => item.vpcId === val)
      if (this.temp.content.idc.startsWith('tencent')) {
        this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
      }
      if (this.temp.content.idc.startsWith('ali')) {
        this.multipleVpcSwitchList = this.vpcInfo[this.vpcIndex].vpcSwitchList
          .map(value => value)
          .filter(value => val.includes(value.zoneId))
      }
      this.temp.content.approvalData.defaultSecurityGroupList = []
      this.temp.content.approvalData.defaultVpcSwitchList = []
      this.temp.content.approvalData.defaultSubnetId = []
    },
    leaderApproveData() {
      try {
        this.checkProductLine()
      } catch (e) {
        this.$message.error(e.toString())
        return
      }
      this.temp.content.dgDataAsset = '否-'
      if (this.temp.content.isDgDataAsset) {
        this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
      }
      if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
        this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
      } else {
        this.temp.content.dgDataAsset += '-'
      }
      this.Date()
      this.node_status = 0
      this.temp.node = 2
      this.temp.status = 1
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      const obj = cloneDeep(this.temp)
      obj.content = { data: JSON.stringify(obj.content) }
      approveOrder(obj).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          notification.success({
            message: '审批成功',
            description: '上级领导审批成功',
          })
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.node_status = 1
          location.reload()
        }
      })
      // this.$refs.orderForm.refresh()
    },
    async createAsset() {
      this.$refs.mysqlConfig
        .validateForm()
        .then(res => {
          if (!res.val || !res.pwdStr) {
            this.$message.error('请校验密码或必填项')
            return
          }
          this.temp.content.pwdStr = res.pwdStr
          if (this.temp.content.num != this.temp.content.allocationList.length) {
            this.$message.error('分配物理机数量和申请服务器数量不一致')
            return
          }
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }
          this.Date()
          this.temp.node = 5
          this.temp.status = 10
          if (!this.temp.content.needOtherDisks) {
            this.temp.content.diskType = 'HDD'
            this.temp.content.diskSize = 0
          }
          for (let i = 0; i < this.temp.content.approvalData.keys.length; i++) {
            this.temp.content.approvalData.keys[i] = this.temp.content.approvalData.keys[i].trim()
          }
          this.modalFormBindServerVisible = false
          this.modalFormPreBindServerVisible = false
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.dbHandler = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.data.code === 400) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        })
        .catch(() => {})
    },
    approveData() {
      this.Date()
      this.node_status = 0
      this.temp.node = this.temp.node + 1
      this.temp.status = 2
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content.dbHandler = store.getters.email
      const obj = cloneDeep(this.temp)
      obj.content = { data: JSON.stringify(obj.content) }

      approveOrder(obj).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          notification.success({
            message: '审批成功',
            description: '上级领导审批成功',
          })
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.node_status = 1
          location.reload()
        }
      })
    },
    manageApproveData() {
      this.Date()
      this.node_status = 0
      console.log(this.$refs.adminPage, 'adminPage')
      if (this.$refs.adminPage && this.$refs.adminPage.customHostName) {
        this.temp.content.projectName = this.$refs.adminPage.customHostName
      } else {
        this.$message.error('请确认主机名')
      }
      // 从数据库控制台页面跳过来的
      if (this.temp.content.application.includes('mysql')) {
        this.temp.node = 4
        this.temp.status = 2
      } else {
        this.temp.node = 5
        this.temp.status = 10
      }
      // if (this.temp.content.isFromControl) {
      //   if (this.temp.content.application.includes('mysql')) {
      //     this.temp.node = 4
      //     this.temp.status = 1
      //   } else {
      //     this.temp.node = 5
      //     this.temp.status = 10
      //   }
      // } else {
      //   if (this.temp.content.application.includes('mysql')) {
      //     this.temp.node = 4
      //     this.temp.status = 1
      //   } else {
      //     this.temp.node = 5
      //     this.temp.status = 10
      //   }
      // }

      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content.serverHandler = store.getters.email
      const obj = cloneDeep(this.temp)
      obj.content = { data: JSON.stringify(obj.content) }
      approveOrder(obj).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          notification.success({
            message: '审批成功',
            description: '上级领导审批成功',
          })
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.node_status = 1
          location.reload()
        }
      })
    },
    preApproveData() {
      antdFormValidate(this.$refs.preDataForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 3
          this.temp.status = 10
          if (!this.temp.content.needOtherDisks) {
            this.temp.content.diskType = 'HDD'
            this.temp.content.diskSize = 0
          }
          if (this.temp.content.idc.startsWith('aws')) {
            let singleSubnetList = []
            if (this.temp.content.num === 1) {
              singleSubnetList.push(this.temp.content.approvalData.defaultSubnetId)
            } else {
              singleSubnetList = this.temp.content.approvalData.defaultSubnetId
            }
            this.temp.content.approvalData.defaultSubnetId = singleSubnetList
          }
          this.modalFormBindServerVisible = false
          this.modalFormPreBindServerVisible = false
          // if (this.temp.content.approvalData.instanceType.length !== 0 && this.temp.content.idc.startsWith('aws')) {
          //   this.temp.content.approvalData.instanceType = this.temp.content.approvalData.instanceType[0]
          // }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content.isAutomation = true
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.data.code === 400) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
      // this.$refs.preDataForm.refresh()
    },
    // 预创建获取信息
    preCreateAsset() {
      this.preStatus = true
      this.getBaseListInfo()
    },
    getBaseListInfo() {
      if (this.temp.content.idc.startsWith('aws')) {
        const sendData = {
          cpu: this.temp.content.serverType[0],
          idc: this.temp.content.idc,
          mem: this.temp.content.serverType[1],
        }
        awsBaseInfo(sendData)
          .then(response => {
            this.temp.content.approvalData.instanceType = response.Data.data.instanceType
            this.imageList = response.Data.data.imageList
            this.subnetList = response.Data.data.subnetList
            this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
            this.temp.content.approvalData.defaultSubnetId = response.Data.data.defaultSubnetId
            this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
            this.securityGroupList = response.Data.data.securityGroupList
            this.instanceTypeList = response.Data.data.instanceTypeList
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else if (this.temp.content.idc === 'ucloud-shanghai2-hybrid') {
        const sendData = {
          cpu: JSON.parse(this.temp.content.serverType)[0],
          business: this.temp.content.business,
          diskType: this.temp.content.diskType,
          diskSize: parseInt(this.temp.content.diskSize),
          application: this.temp.content.application,
          os: this.temp.content.os,
          server_num: this.temp.content.num,
          env: this.temp.content.env,
          mem: JSON.parse(this.temp.content.serverType)[1],
        }
        vmBaseInfo(sendData)
          .then(response => {
            // this.temp.content.projectName = response.Data.data.projectName
            this.temp.content.approvalData.hostIpList = response.Data.data.hostIpList
            this.temp.content.approvalData.vlan = response.Data.data.vlan
            this.temp.content.approvalData.msgs = response.Data.data.msgs
            if (this.temp.content.approvalData.msgs.length !== 0) {
              if (this.temp.content.approvalData.msgs[0].type === undefined) {
                this.preCreateMsgs = [{ color: 'green', msg: '查询无异常' }]
              } else {
                const msgs = response.data.data.msgs
                msgs.forEach(function (item, index) {
                  if (item.type === 'error') {
                    item.color = 'red'
                  } else if (item.type === 'warning') {
                    item.color = 'orange'
                  }
                })
                this.preCreateMsgs = msgs
              }
            } else {
              this.preCreateMsgs = [{ color: 'green', msg: '查询无异常' }]
            }
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else if (this.temp.content.idc.startsWith('ali')) {
        const sendData = {
          cpu: JSON.parse(this.temp.content.serverType)[0],
          diskType: this.temp.content.diskType,
          diskSize: parseInt(this.temp.content.diskSize),
          idc: 'ali-hangzhou-qxb',
          mem: JSON.parse(this.temp.content.serverType)[1],
        }
        aliyunBaseInfo(sendData)
          .then(response => {
            this.modalFormPreBindServerVisible = true
            if (response.Data.data.instanceTypeList !== null) {
              this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
              this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
              this.temp.content.approvalData.defaultVpcId = response.Data.data.defaultVpcId
              this.temp.content.approvalData.defaultVpcSwitchList = response.Data.data.defaultVpcSwitchList
              this.imageList = response.Data.data.imageList
            }
            this.temp.content.approvalData.defaultZoneId = response.Data.data.zoneId
            this.temp.content.approvalData.instanceType = response.Data.data.instanceType
            // 获取磁盘相关
            const sendSystemDiskData = {
              instanceType: response.Data.data.instanceType,
              resourceName: 'SystemDisk',
              zoneList: [response.Data.data.zoneId],
            }
            const sendDataDiskData = {
              instanceType: response.Data.data.instanceType,
              resourceName: 'DataDisk',
              zoneList: [response.Data.data.zoneId],
            }
            aliyunDiskInfo(sendSystemDiskData).then(response => {
              this.aliyunSystemDiskTypeDetailList = response.Data.data
              if (this.aliyunSystemDiskTypeDetailList.length > 0) {
                this.temp.content.systemDiskType =
                  this.aliyunDataDiskTypeDetailList[0].diskType +
                  '|' +
                  this.aliyunDataDiskTypeDetailList[0].performanceLevel
              }
            })
            aliyunDiskInfo(sendDataDiskData).then(response => {
              this.aliyunDataDiskTypeDetailList = response.Data.data
            })
            this.$nextTick(() => {
              this.temp.content.approvalData.zoneIdList.push(response.Data.data.zoneId)
              this.vpcInfo = response.Data.data.vpcInfo
              this.instanceTypeList = response.Data.data.instanceTypeList
              this.aliyunDiskTypeList = response.Data.data.diskTypeList
              // 初始化虚拟机交换id
              this.multipleVpcSwitchList = this.vpcInfo[this.vpcIndex].vpcSwitchList
                .map(value => value)
                .filter(value => this.temp.content.approvalData.zoneIdList.includes(value.zoneId))
              if (this.multipleVpcSwitchList.length > 0) {
                this.temp.content.approvalData.defaultVpcSwitchList = [
                  this.multipleVpcSwitchList[0].vpcSwitchId + '|' + this.multipleVpcSwitchList[0].zoneId,
                ]
              }
              // instance
              this.multipleInstanceList =
                this.instanceTypeList[
                  (this.instanceTypeList || []).findIndex(
                    item => item.zoneId === this.temp.content.approvalData.zoneIdList[0]
                  )
                ].zoneInstanceTypeList
            })
            this.preStatus = false
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else if (this.temp.content.idc.startsWith('tencent')) {
        const sendData = {
          cpu: JSON.parse(this.temp.content.serverType)[0],
          mem: JSON.parse(this.temp.content.serverType)[1],
        }
        tencentBaseInfo(sendData)
          .then(response => {
            this.temp.content.approvalData.instanceType = response.Data.data.instanceType
            this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
            this.temp.content.approvalData.defaultImageId = response.Data.data.defaultImageId
            this.temp.content.approvalData.defaultVpcId = response.Data.data.defaultVpcId
            this.temp.content.approvalData.defaultSubnetId = response.Data.data.defaultSubnetList
            this.temp.content.approvalData.defaultZoneId = response.Data.data.zoneId
            this.imageList = response.Data.data.imageList
            this.securityGroupList = response.Data.data.securityGroupList
            this.$nextTick(() => {
              this.tencentVpcInfo = response.Data.data.vpcInfo
              this.instanceTypeList = response.Data.data.instanceTypeList
              this.temp.content.approvalData.zoneIdList.push(response.Data.data.zoneId)
              // 初始化subnet交换id
              this.multipleVpcSubnetList = this.tencentVpcInfo[this.tencentVpcIndex].subnetInfo
            })
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
          .catch(() => {
            this.preStatus = false
            this.modalFormPreBindServerVisible = true
          })
      } else {
        this.preStatus = false
        this.modalFormPreBindServerVisible = true
      }
    },
    getVpcList(value, e) {
      const vpcList = []
      this.vpcStatus = true
      for (var i in e) {
        vpcList.push(e[i].vpc)
      }
      return vpcList
    },
    async getSecurityLists(value, e) {
      this.vpcStatus = true
      const vpcList = this.getVpcList(value, e)
      const sendData = {
        idc: this.temp.content.idc,
        defaultSubnetList: vpcList,
      }
      awsSecurityBaseInfo(sendData).then(response => {
        this.securityGroupList = response.Data.data.securityGroupList
        this.securityGroupList = this.securityGroupList.filter((item, index) => {
          const str = JSON.stringify(item)
          return (
            index ===
            this.securityGroupList.findIndex(obj => {
              return JSON.stringify(obj) === str
            })
          )
        })
        this.temp.content.approvalData.defaultSecurityGroupList = response.Data.data.defaultSecurityGroupList
        this.vpcStatus = false
      })
    },
    rejectData() {
      this.temp.node = 5
      this.temp.status = 20
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          notification.success({
            message: '审批完成',
            description: '该审批已被拒绝',
          })
        }
        response.Data.content = JSON.parse(response.Data.content.data)
        this.temp = response.Data
        this.node_status = 1
      })
    },
    revokeData() {
      this.node_status = 0
      this.temp.node = 5
      this.temp.status = 30
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '撤回失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '撤回成功',
            description: '该工单被申请人撤回',
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    uniqueDicts(arr) {
      const uniqueDictsSet = new Set()
      return arr.filter(item => {
        const dictString = JSON.stringify(item)
        return uniqueDictsSet.has(dictString) ? false : uniqueDictsSet.add(dictString)
      })
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    filterOption: filterLabelValue,
    checkProductLine() {
      if (
        this.temp.content.costAttr === '研发成本' ||
        this.temp.content.business === '大数据' ||
        this.temp.content.business === '搜索(DG)'
      ) {
        return
      }
      if (this.temp.content.productLineList.length > 5) {
        throw new Error('目前最多支持5个产品线，如有需求请联系运维管理员')
      }
      let sumRatios = 0
      let productLineArr = []
      let existArr = []
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (
          this.temp.content.productLineList[i].productLineNameList === undefined ||
          this.temp.content.productLineList[i].productLineNameList.length < 2
        ) {
          throw new Error('请填充完所有产品线空格')
        }
        if (existArr.includes(this.temp.content.productLineList[i].productLineNameList[1])) {
          throw new Error('产品线重复：' + this.temp.content.productLineList[i].productLineNameList[1])
        }
        productLineArr.push(
          this.temp.content.productLineList[i].productLineNameList[1] + ':' + this.temp.content.productLineList[i].ratio
        )
        sumRatios += this.temp.content.productLineList[i].ratio
        existArr.push(this.temp.content.productLineList[i].productLineNameList[1])
      }
      sumRatios = sumRatios.toFixed(2)
      if (sumRatios !== '100.00') {
        const errStr = '比例之和： ' + sumRatios + '≠ 100.00'
        throw new Error(errStr)
      }
      this.temp.content.productLine = productLineArr.join(',')
    },
    addRow() {
      this.temp.content.productLineList.push({
        productLineNameList: [],
        ratio: 0,
      })
    },
    // 移除某行
    removeRow(i) {
      if (this.temp.content.productLineList.length > 1) {
        this.temp.content.productLineList.splice(i, 1)
      }
    },
    // 费用负责人联动
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        var arry = response.Data.data
        for (var i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    getUserTeamListMethod(userEmail) {
      this.userTeamList = []
      if (userEmail === '') {
        return
      }
      if (this.$route.query.id) return
      GetUserOrgAllInfo({ email: userEmail }).then(response => {
        this.businessList = response.Data.business
        this.temp.content.business = this.businessList[0]
        if (this.temp.content.business === 'DG') {
          this.temp.content.isDgBusiness = true
        }
        // this.organizationOptions = response.Data.orgCas
        this.organizationOptions = response.Data.orgCasList
        if (this.organizationOptions && this.organizationOptions.length) {
          this.temp.content.arrorg = this.organizationOptions[0]
        }
        if (this.operation.includes(this.temp.content.business)) {
          this.temp.content.costAttr = '运营成本'
          this.temp.content.isOperatingCost = true
          this.temp.content.productLineList = [
            {
              productLineNameList: [],
              ratio: 0,
            },
          ]
        } else {
          this.temp.content.costAttr = '研发成本'
          this.temp.content.isOperatingCost = false
        }
      })
    },
    handleDGSpecificBusiness() {
      if (this.temp.content.dgDataAssetClassification !== '') {
        this.temp.content.dgSpecificBusiness = '数据中心'
      }
    },
    onSearchProductLineByIp(value) {
      getProductLineByIp({ ip: value }).then(response => {
        this.temp.content.productLineList = response.Data.productLineList
      })
    },
    changeCostAttr() {
      if (this.operation.includes(this.temp.content.business)) {
        this.temp.content.costAttr = '运营成本'
        if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
          this.temp.content.costAttr = '开发支出'
        }
        // this.temp.content.productLineList = [
        //   {
        //     productLineNameList: [],
        //     ratio: 0,
        //   },
        // ]
      } else {
        this.temp.content.costAttr = '研发成本'
      }
    }
  },
}
</script>

<style lang="less" scoped>
.redisBox {
  border: 8px solid #d8d8d8;
  padding: 20px;
  box-sizing: border-box;
}

.customCard {
  width: 100%;
  margin-top: 14px;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  background-color: #fff;

  .customTitle {
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    background: transparent;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
    margin-bottom: -1px;
    min-height: 48px;
    display: inline-block;
    flex: 1;
    overflow: hidden;
    padding: 16px 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .customContent {
    padding: 24px;
  }
}

.itemTitle {
  display: inline-block;
  width: 100px;
  height: 100%;
  color: #9e9e9e;
}

.itemContent {
}

.shadowPrice {
  font-size: 14px;
  color: #9e9e9e;
}

.myColl {
  width: 100%;

  /deep/.ant-collapse-header {
    padding-left: 0 !important;
    background-color: #fff !important;
  }

  /deep/.ant-collapse-content-box {
  }
}

.fixedBox {
  position: sticky;
  top: 0px;

  /deep/ .ant-card-body {
    padding: 0 24px !important;
  }
}

.itemTitle {
  display: inline-block;
  text-align: left;
  font-weight: bolder;
  width: 150px;
  height: 100%;
  margin-bottom: 6px;
}

.itemContent {
  display: inline-block;
  margin-bottom: 6px;
}
</style>
