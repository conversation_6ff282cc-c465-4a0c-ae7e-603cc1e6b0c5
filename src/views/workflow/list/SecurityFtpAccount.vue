<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item name="isOcr" label="是否为算法FTP">
              <a-radio-group v-model:value="temp.content.isOcr" button-style="solid">
                <a-radio value="false">No</a-radio>
                <a-radio value="true">Yes</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item name="email" label="通知人">
              <a-input placeholder="<EMAIL>" v-model:value="temp.content.email" />
            </a-form-model-item>
            <a-form-model-item name="comment" label="申请理由">
              <a-textarea v-model:value="temp.content.comment" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24" style="text-align: center">
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getCronScheduleTaskNameList } from '@/api/cron/cron_schedule_task'
import { getAssetListIdc, getAssetList } from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'
import { getUserList } from '@/api/permission/user'

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'SecurityFtpAccount',
  components: {
    STable,
  },
  data: function () {
    this.pagination = pagination
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: 'FTP账号申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: store.getters.name,
          isOcr: 'false',
          email: store.getters.email,
          comment: '',
        },
        timeline: [],
        comment: '',
      },
      hasAdminRole: false,
      rules: antdFormRulesFormat({
        'content.isOcr': [{ required: true, message: '请选择是否为算法', trigger: 'change' }],
        'content.email': [{ required: true, message: '请填写通知人', trigger: 'blur' }],
        'content.comment': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
    }
  },
  mounted() {
    this.getInfo()
    this.checkAdminRole()
  },
  methods: {
    checkAdminRole() {
      getUserList({ searchText: store.getters.email }).then(response => {
        const roles = response.Data.data[0].roles
        if (roles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = this.handleGetInfo(response.Data)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          const requestParameters = cloneDeep(this.temp)
          requestParameters.node = 1
          requestParameters.status = 1
          requestParameters.timeline = [
            this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now,
          ]
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          createOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = this.handleGetInfo(response.Data)
              this.node_status = 1
              this.$router.push({ path: '/workflow/security-ftp-account', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    handleGetInfo(data) {
      data.content = JSON.parse(data.content['data'])
      delete data.content['data']
      return data
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          requestParameters.node = 2
          requestParameters.status = 1
          requestParameters.handler = store.getters.name
          requestParameters.handlerEmail = store.getters.email
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          requestParameters.node = 3
          requestParameters.status = 10
          requestParameters.handler = store.getters.name
          requestParameters.handlerEmail = store.getters.email
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    approveDataManual() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters.content.manual = true
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 10
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          requestParameters.node = 3
          requestParameters.status = 20
          requestParameters.handler = store.getters.name
          requestParameters.handlerEmail = store.getters.email
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          requestParameters.node = 3
          requestParameters.status = 30
          requestParameters.handler = store.getters.name
          requestParameters.handlerEmail = store.getters.email
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
