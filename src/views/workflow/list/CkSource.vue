<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全管理员审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}一仅支持单表导出`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item name="checkedKeys" label="数据库选择">
              <a-tree
                v-model:checkedKeys="temp.content.checkedKeys"
                checkable
                v-model:selectedKeys="selectedKeys"
                :defaultExpandParent="true"
                v-model:expandedKeys="temp.content.defaultExpandedKeys"
                :tree-data="treeData"
                @check="treeSelect"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item name="sql">
              <template #label>
                SQL语句
                <a-tooltip placement="right" class="ant-tooltip">
                  <template #title>
                    <span>sql语句提交前确保语句校验通过</span>
                  </template>
                  <tx-button type="link" class="question-ant-btn"><a-icon type="question-circle" /></tx-button>
                </a-tooltip>
              </template>
              <a-textarea v-model:value="temp.content.sql" :auto-size="{ minRows: 3, maxRows: 10 }" />
            </a-form-model-item>
            <a-form-model-item label="申请理由" name="reason">
              <a-textarea v-model:value="temp.content.reason" :auto-size="{ minRows: 2 }" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="反馈信息">
              <a-textarea v-model:value="temp.content.commit" :auto-size="{ minRows: 2 }" />
            </a-form-model-item>
            <div v-if="runResult.Status == 4 && temp.node > 3">
              <a-form-model-item label="执行情况">
                <a-tag color="orange">执行中</a-tag>
                <tx-button
                  v-if="temp.node > 3 && DBAUser.includes(localUser)"
                  type="danger"
                  icon="rollback"
                  @click="failedRetry"
                >
                  重试
                </tx-button>
              </a-form-model-item>
            </div>
            <div v-else-if="runResult.Status == 2 && temp.node > 3">
              <a-form-model-item label="执行情况">
                <a-tag color="green">成功</a-tag>
              </a-form-model-item>
              <a-form-model-item label="下载地址">
                <a-textarea v-model:value="runResult.ucloudUrl" :auto-size="{ minRows: 4, maxRows: 6 }" />
              </a-form-model-item>
              <a-form-model-item v-if="temp.node > 3 && DBAUser.includes(localUser)" label="文件密码">
                {{ runResult.password }}
              </a-form-model-item>
            </div>
            <div v-else-if="runResult.Status == 1 && temp.node > 3">
              <a-form-model-item label="错误信息">
                <tx-button type="danger" icon="rollback" @click="failedRetry">重试</tx-button>
                <br />
                <a-textarea v-model:value="runResult.ErrInfo" :auto-size="{ minRows: 4, maxRows: 6 }" />
              </a-form-model-item>
            </div>
            <div v-else-if="temp.node > 3">
              <a-form-model-item label="执行情况">
                <a-tag color="blue">停止</a-tag>
              </a-form-model-item>
            </div>
            <a-form-model-item v-if="temp.node > 0" label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 5 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) &&
                !temp.handlerEmail.includes(localUser) &&
                temp.node !== 0 &&
                temp.node !== 4 &&
                node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 5 }"
            >
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 5 }"
            >
              <tx-button type="primary" @click="leaderApproveData('safe')">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 5 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getClickHouseTree } from '@/api/db/dms'
import { dblDownloadload } from '@/api/db/ddl'
import { downloadRetry } from '@/api/db/dbApproval'
export default {
  name: 'BaselineCheckResources',
  components: {},
  data: function () {
    return {
      fetching: false,
      value: [],
      endOpen: false,
      localUser: store.getters.email,
      DBAUser: '<EMAIL>|<EMAIL>|<EMAIL>|<EMAIL>',
      time_now: '',
      expandedKeys: [],
      selectedKeys: [],
      treeData: [],
      node_status: 1,
      runResult: {
        Status: 0,
        filesPassword: '',
        downloadURL: '',
      },
      temp: {
        id: '',
        orderType: 'ClickHouse资源导出',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          checkedKeys: [],
          defaultExpandedKeys: [],
          reason: '',
          sql: '',
          downloadURL: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        checkedKeys: [{ required: true, message: '请选择需要查询的数据库', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        sql: [{ required: true, message: '请填写查询sql语句', trigger: 'blur' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
    this.getCkTreeInfo()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          var downloadURL = response.Data.content.downloadURL
          var ip = response.Data.content.ip
          var dbName = response.Data.content.dbName
          var sql = response.Data.content.sql
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.node > 3) {
              this.temp.content.ip = ip
              this.temp.content.dbName = dbName
              this.temp.content.sql = sql
              this.temp.content.downloadURL = downloadURL
              this.getRunResult(this.temp.content.downloadURL)
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    getCkTreeInfo() {
      getClickHouseTree().then(response => {
        this.treeData = noc.promiseArray(response.Data.tree)
        for (let i = 0; i < this.treeData.length; i++) {
          this.treeData[i].disabled = true
        }
        if (this.temp.node === 0) {
          this.temp.content.defaultExpandedKeys.push(this.treeData[0].key)
        }
      })
    },
    treeSelect(value) {
      if (value.length === 2) {
        this.temp.content.checkedKeys.splice(0, 1)
      }
      if (value.length > 0) {
        this.temp.content.defaultExpandedKeys = []
        this.temp.content.defaultExpandedKeys.push(this.temp.content.checkedKeys[0].split('|')[0])
      }
    },
    getRunResult(url) {
      dblDownloadload({ filesName: url }).then(response => {
        this.runResult = JSON.parse(response.Data.filesContent)
      })
    },
    failedRetry() {
      var sendBody = {}
      sendBody.ip = this.temp.content.ip
      sendBody.dbName = this.temp.content.dbName
      sendBody.sql = this.temp.content.sql
      sendBody.redisKey = this.temp.content.downloadURL
      sendBody.dbType = 'clickhouse'
      sendBody.orderId = this.temp.id
      downloadRetry(sendBody).then(response => {
        if (response.Data.downloadUrl !== '') {
          this.temp.content.downloadURL = response.Data.downloadUrl
          this.getRunResult(this.temp.content.downloadURL)
        }
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/ck-source', query: { id: response.Data.id } })
            }
            response.Data.content = JSON.parse(response.Data.content.data)
            this.temp = response.Data
          })
        }
      })
    },
    leaderApproveData(value) {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          if (value === 'safe') {
            this.temp.node = 3
          } else {
            this.temp.node = 2
          }
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.runResult.Status = 4
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          var ip = this.temp.content.checkedKeys[0].split('|')[1]
          var dbName = this.temp.content.checkedKeys[0].split('|')[2]
          var sql = this.temp.content.sql
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.content.ip = ip
          this.temp.content.dbName = dbName
          this.temp.content.sql = sql
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped>
.ant-tooltip {
  -webkit-box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(240, 17, 17, 0.65);
  font-size: 14px;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: absolute;
  z-index: 1060;
  display: block;
  max-width: 500px;
  visibility: visible;
}
.question-ant-btn {
  line-height: 1.499;
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  -webkit-box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
  box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
  cursor: pointer;
  -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  height: 32px;
  padding: 0 0px;
  font-size: 14px;
  border-radius: 2px;
}
</style>
