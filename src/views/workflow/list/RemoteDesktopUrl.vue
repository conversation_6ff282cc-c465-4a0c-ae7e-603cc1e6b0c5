<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <p>
        1. 公司域名(
        <b>
          intsig.net, qixin.com, camcard.com, camscanner.com, qixin007.com, jingjidanao.com, cc.co, intsig.com,
          textin.com, cscan.co
        </b>
        )默认全部开通，无需额外申请
        <br />
        2. 不得开通公司明令禁止的第三方存储服务和在线编辑系统，如百度网盘，腾讯微云，石墨文档，语雀等
        <br />
        3. 与公司业务无关的网站（iqiyi，weibo等），无特殊原因不开通，如需访问请使用连接云桌面的终端设备
        <br />
        <b style="color: orangered">注： 申请开通的外部站点需详细说明理由</b>
        <br />
      </p>
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="14">
            <a-form-model-item
              v-for="(i, index) in temp.content.urls"
              :key="index"
              :name="'urls.' + index + '.url'"
              :rules="validatorUrl(i.url)"
              :label="index === 0 ? '申请URL地址1' : '申请URL地址' + (index + 1)"
            >
              <div style="margin-left: 10px">
                <a-row :gutter="20">
                  <a-textarea auto-size v-model:value="i.url" placeholder="xxxx.intsig.net" style="width: 70%" />
                  <div style="margin-top: 1%" v-if="temp.node === 3">
                    <span style="font-size: 10px">代理：</span>
                    <a-switch
                      v-model:checked="temp.content.urls[index].needProxy"
                      checked-children="开"
                      size="small"
                      style="margin-top: 1%"
                      un-checked-children="关"
                    />
                  </div>
                  <tx-button
                    v-if="index === temp.content.urls.length - 1"
                    style="margin-left: 20px"
                    type="primary"
                    @click="upStreamFormAdd(index)"
                  >
                    新增
                  </tx-button>
                  <tx-button v-else style="margin-left: 20px" type="danger" @click="upStreamFormDel(i)">删除</tx-button>
                </a-row>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea
                v-model:value="temp.content.reason"
                placeholder="申请开通的外部站点需详细说明理由"
                :auto-size="{ minRows: 2 }"
              />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/remote-work">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="securityApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { uniqBy } from 'lodash'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import cloneDeep from 'lodash.clonedeep'

export default {
  name: 'RemoteDesktopUrl',
  data() {
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '远程办公URL白名单',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          urls: [{ url_0: '', needProxy: 'false' }],
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', validator: this.checkUrl, trigger: 'blur' }],
      },
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.content.url === undefined) {
            response.Data.content = JSON.parse(response.Data.content.data)
            console.log(111111111111)
            console.log(response.Data.content)
            response.Data.content.urls = response.Data.content.urls.map(item => {
              item.needProxy = JSON.parse(item.needProxy)
              return item
            })
          }
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          // 去重和去空
          this.temp.content.urls = uniqBy(
            this.temp.content.urls.filter(item => item.url),
            'url'
          )
          this.temp.content.urls = this.temp.content.urls.map(item => {
            item.needProxy = item.needProxy.toString()
            return item
          })
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = response.Data
              this.$router.push({ path: '/workflow/remote-desktop-url', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // 去重和去空
          this.temp.content.urls = uniqBy(
            this.temp.content.urls.filter(item => item.url),
            'url'
          )
          this.temp.content.urls = this.temp.content.urls.map(item => {
            item.needProxy = item.needProxy.toString()
            return item
          })
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    securityApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // 去重和去空
          this.temp.content.urls = uniqBy(
            this.temp.content.urls.filter(item => item.url),
            'url'
          )
          this.temp.content.urls = this.temp.content.urls.map(item => {
            item.needProxy = item.needProxy.toString()
            return item
          })
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '安全审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // 去重和去空
          this.temp.content.urls = uniqBy(
            this.temp.content.urls.filter(item => item.url),
            'url'
          )
          this.temp.content.urls = this.temp.content.urls.map(item => {
            item.needProxy = item.needProxy.toString()
            return item
          })
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    validatorUrl(row) {
      // 获取row信息
      // 返回的是rules
      return [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (row === '' || row === undefined) {
              return Promise.reject('请输入URL或者IP地址~')
            }
            if (row.includes('\n')) {
              return Promise.reject('单个输入框只支持一个URL地址或者IP，如需多个，请点击右侧新增按钮~')
            }
            if (row.includes('http') || row.includes('https')) {
              return Promise.reject('请输入完整域名，形如："cloud.intsig.net"')
            }
            if (row.includes('/')) {
              return Promise.reject('只需要输入完整域名即可，形如："cloud.intsig.net"')
            }
            // 如果包含":"代表ip一类，进行判断
            if (row.includes(':')) {
              const isIP = this.isIPAddress(row.split(':')[0])
              if (isIP) {
                return Promise.reject('不支持IP:端口形式,请直接输入IP~')
              }
            } else if (row.includes('：')) {
              const isIP = this.isIPAddress(row.split('：')[0])
              if (isIP) {
                return Promise.reject('不支持IP:端口形式,请直接输入IP~')
              }
            }
            return Promise.resolve()
          },
          trigger: 'blur',
        },
      ]
    },
    // 判断是否是ip
    isIPAddress(str) {
      const ipRegEx = /^(\d{1,3}\.){3}\d{1,3}$/
      if (!ipRegEx.test(str)) {
        return false
      }
      const ipArray = str.split('.')
      for (let i = 0; i < ipArray.length; i++) {
        const num = parseInt(ipArray[i])
        if (num < 0 || num > 255) {
          return false
        }
      }
      return true
    },
    checkUrl(rule, value, callback) {
      console.log(111111)
      console.log(this.temp.content.urls)
      console.log(rule)
      console.log(value)
      console.log(callback)
      if (value === '' || value === undefined) {
        callback('请输入url或者ip')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    // upStream 表单添加
    upStreamFormAdd(index) {
      // let a = 'url_' +index
      // let b = {"needProxy": "false"}
      // b[a]=''
      // this.temp.content.urls.push(b)
      this.temp.content.urls.push({ url: '', needProxy: 'false' })
      console.log(this.temp.content)
    },
    // upStream 表单删除
    upStreamFormDel(infos) {
      this.temp.content.urls.splice(this.temp.content.urls.indexOf(infos), 1)
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
