<template>
  <page-header-wrapper>
    <template #content>
      <a-space>
        <tx-button icon="solution" size="small">
          <a href="https://doc.intsig.net/pages/viewpage.action?pageId=499941751" style="text-decoration: none">
            说明文档
          </a>
        </tx-button>
        <tx-button icon="solution" size="small">
          <a href="https://doc.intsig.net/pages/viewpage.action?pageId=498237815" style="text-decoration: none">
            操作文档
          </a>
        </tx-button>
      </a-space>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="安全审批" />
        <a-step title="事业部负责人审批" />
        <a-step title="数据所属事业部负责人审批" />
        <a-step title="CTO审批" />
        <a-step title="数据抽取审批" />
        <a-step title="数据交付审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card
      style="margin-top: 12px"
      :bordered="false"
      :title="`${temp.orderType}——DLP云桌面交付流程审批完成半小时后（IT半小时同步网络数据），访问操作请到12F小房间插网线连接,如果没有转换器/扩展槽请联系IT-桂国忠领取`"
    >
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else-if="temp.status === 40" color="green">数据已回收</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="source" label="数据来源">
              <a-select
                v-model:value="temp.content.source"
                placeholder="请选择"
                style="width: 120px"
                @change="sourceChange"
              >
                <a-select-option value="CS">CS</a-select-option>
                <a-select-option value="CC">CC</a-select-option>
                <a-select-option value="DG">DG</a-select-option>
                <a-select-option value="AIM">AIM</a-select-option>
                <a-select-option value="ACG">ACG</a-select-option>
                <a-select-option value="SSG">SSG</a-select-option>
                <a-select-option value="财务">财务</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="数据事业部负责人">{{ temp.content.dataBuLeader }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="dataProcessing" label="数据脱敏">
              <a-radio-group v-model:value="temp.content.dataProcessing" button-style="solid">
                <a-radio-button value="脱敏">脱敏</a-radio-button>
                <a-radio-button value="不脱敏">不脱敏</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="dataDelivery" label="数据交付方式">
              <a-radio-group v-model:value="temp.content.dataDelivery" button-style="solid">
                <a-radio-button value="DLP云桌面">DLP云桌面</a-radio-button>
                <a-radio-button value="生产服务器">生产服务器</a-radio-button>
                <a-radio-button value="办公电脑">办公电脑</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="amount" label="数量">
              <a-input v-model:value="temp.content.amount" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.content.dataDelivery === 'DLP云桌面'" :span="12">
            <a-form-model-item name="propertyCode" label="个人电脑编号">
              <a-select v-model:value="temp.content.propertyCode" placeholder="请选择需要访问的个人电脑编号">
                <a-select-option v-for="item in userIpList" :key="item.propertyCode" :value="item.propertyCode">
                  {{ item.propertyCode }}({{ item.propertyName }})
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-else-if="temp.content.dataDelivery === '办公电脑'" :span="12">
            <a-form-model-item name="propertyCode" label="交付办公电脑理由">
              <a-textarea
                v-model:value="temp.content.pcReason"
                placeholder="原则不允许数据交付到办公电脑，如选择必须填写充分的理由并流程中会涉及CTO审批"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="expiration" label="使用截止时间">
              <a-date-picker show-time v-model:value="temp.content.expiration" valueFormat="YYYY-MM-DD HH:mm" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.content.dataDelivery === '办公电脑'" :span="12">
            <a-form-model-item name="dlp" label="办公电脑DLP">
              <a-radio-group v-model:value="temp.content.dlp" button-style="solid">
                <a-radio-button value="已安装">已安装</a-radio-button>
                <a-radio-button value="未安装">未安装</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由/用途">
              <a-textarea
                v-model:value="temp.content.reason"
                placeholder="默认不允许数据交付到办公电脑，如有此类请求必须填写充分的理由"
              />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论注">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 6 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safetyApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="buLeaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="dataBuLeaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="ctoApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 5 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 6 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="opsApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.handlerEmail.includes(localUser) && temp.node === 7 && temp.status === 10 && node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" @click="recycleData">数据回收</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import { createOrder, getOrderInfo, approveOrder, getUserIpList } from '@/api/workflow/order'

export default {
  name: 'DbManipulation',
  data() {
    return {
      localUser: store.getters.email,
      time_now: '',
      userIpList: [],
      node_status: 1,
      userTemp: {
        email: store.getters.email,
      },
      temp: {
        id: '',
        orderType: '数据操作申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          source: undefined,
          dataBuLeader: '',
          dataProcessing: '脱敏',
          dataDelivery: 'DLP云桌面',
          propertyCode: '',
          pcReason: '',
          dlp: '已安装',
          expiration: '',
          amount: '',
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.source': [{ required: true, message: '请选择数据来源', trigger: 'change' }],
        'content.amount': [{ required: true, message: '请填写数量', trigger: 'blur' }],
        'content.dataProcessing': [{ required: true, message: '请选择数据是否脱敏', trigger: 'change' }],
        'content.dataDelivery': [{ required: true, message: '请选择数据交付方式', trigger: 'change' }],
        'content.pcReason': [{ required: true, message: '请填写数据交付到办公电脑的理由', trigger: 'blur' }],
        'content.propertyCode': [{ required: true, message: '请选择需要访问的个人电脑编号', trigger: 'change' }],
        'content.dlp': [{ required: true, message: '请选择DLP是否已安装', trigger: 'change' }],
        'content.expiration': [{ required: true, message: '请选择数据使用截止时间', trigger: 'change' }],
        'content.reason': [{ required: true, message: '请填写申请理由/用途', trigger: 'blur' }],
      }),
    }
  },
  created() {
    this.getInfo()
    this.getUserIps()
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    getUserIps() {
      getUserIpList(this.userTemp).then(response => {
        this.userIpList = response.Data.ipInfo
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = response.Data
              this.$router.push({ path: '/workflow/db-manipulation', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    safetyApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '安全管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    buLeaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '事业部负责人审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    dataBuLeaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '数据所属事业部负责人审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    ctoApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 5
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: 'CTO审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 6
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '数据抽取管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    opsApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 7
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '数据交付管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 7
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 7
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    recycleData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 7
          this.temp.status = 40
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '回收失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '回收成功',
                description: '数据成功回收销毁',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    sourceChange(value) {
      if (value === 'CS') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      } else if (value === 'CC') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      } else if (value === 'DG') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      } else if (value === 'AIM') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      } else if (value === 'ACG') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      } else if (value === 'SSG') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      } else if (value === '财务') {
        this.temp.content.dataBuLeader = '<EMAIL>'
      }
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
