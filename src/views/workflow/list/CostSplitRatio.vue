<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item name="business" label="业务">
              <a-select
                v-model:value="temp.content.business"
                placeholder="请选择业务"
                :options="businessList"
                showSearch
                @change="listCostSplitRatio"
              ></a-select>
            </a-form-model-item>
            <a-form-model-item label="比例参考">
              <a-table
                ref="table"
                size="default"
                :rowKey="record => record.id"
                :pagination="false"
                :columns="columnsRatioBusiness"
                :data-source="tableData"
              ></a-table>
            </a-form-model-item>
            <a-form-model-item name="ratioConfirm" label="比例确认">
              <a-table
                ref="table"
                size="default"
                :rowKey="record => record.id"
                :pagination="false"
                :columns="columnsRatioBusiness"
                :data-source="tableDataNew"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'year'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <!--                        <a-select-->
                        <!--                          v-model:value="temp.content.year"-->
                        <!--                          placeholder="请选择年份"-->
                        <!--                          :options="yearList"-->
                        <!--                          showSearch-->
                        <!--                        ></a-select>-->
                        {{ temp.content.year }}
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'cc'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.cc"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'cs'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.cs"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'qxb'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.qxb"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'ssg'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.ssg"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'zcb'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.zcb"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'aim'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.aim"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <template v-if="column.dataIndex === 'acg'">
                    <a-form-model>
                      <a-form-model-item class="ant-form-item—table">
                        <a-input-number
                          v-model:value="temp.content.acg"
                          :precision="2"
                          :step="0.01"
                          :min="0"
                          :max="2"
                          string-mode
                        />
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                </template>
              </a-table>
            </a-form-model-item>
            <a-form-model-item name="comment" label="申请理由">
              <a-textarea v-model:value="temp.content.comment" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24" style="text-align: center">
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getCostSplitRatioList, getSplitRatioBusinessList } from '@/api/cost/split_ratio'
import cloneDeep from 'lodash.clonedeep'
import moment from 'moment'

const pagination = {
  showTotal: total => `共 ${total} 条`
}

const columnsRatioBusiness = [
  {
    title: '年份',
    dataIndex: 'year'
  },
  // {
  //   title: '业务',
  //   dataIndex: 'business'
  // },
  {
    title: '分摊比例',
    children: [
      {
        title: 'CS-CC',
        dataIndex: 'cc'
      },
      {
        title: 'CS',
        dataIndex: 'cs'
      },
      {
        title: '启信宝',
        dataIndex: 'qxb'
      },
      {
        title: '总裁办',
        dataIndex: 'zcb'
      },
      {
        title: 'SSG',
        dataIndex: 'ssg'
      },
      {
        title: 'AIM',
        dataIndex: 'aim'
      },
      {
        title: 'ACG智能创新',
        dataIndex: 'acg'
      }
    ]
  }
]

export default {
  name: 'CostSplitRatio',
  components: {
    STable
  },
  data: function() {
    this.pagination = pagination
    this.columnsRatioBusiness = columnsRatioBusiness
    return {
      localUser: store.getters.email,
      businessList: [],
      yearList: [],
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '运维费用分摊比例',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          year: moment().format('YYYY'),
          business: '',
          ratioConfirm: '1',
          cc: '',
          cs: '',
          qxb: '',
          ssg: '',
          zcb: '',
          aim: '',
          acg: ''
        },
        timeline: [],
        comment: ''
      },
      hasAdminRole: false,
      rules: antdFormRulesFormat({
        'content.business': [{ required: true, message: '请选择业务', trigger: 'change' }],
        'content.ratioConfirm': [{ required: true, message: '请填写完比例', trigger: 'blur' }],
        'content.comment': [{ required: true, message: '请填写申请理由', trigger: 'blur' }]
      }),
      tableData: [
        {
          year: ''
        }
      ],
      tableDataNew: [
        {
          year: ''
        }
      ]
    }
  },
  mounted () {
    this.getInfo()
    this.getBusinessList()
  },
  methods: {
    getYearList () {
      const myDate = new Date()
      const startYear = myDate.getFullYear() //起始年份
      const endYear = myDate.getFullYear() + 2 //结束年份
      for (let i = startYear; i <= endYear; i++) {
        this.yearList.push({
          label: i,
          value: i
        })
      }
      this.temp.content.year = startYear.toString()
      this.temp.content.cc = '0'
      this.temp.content.cs = '0'
      this.temp.content.qxb = '0'
      this.temp.content.acg = '0'
      this.temp.content.ssg = '0'
      this.temp.content.aim = '0'
      this.temp.content.zcb = '0'
    },
    getBusinessList () {
      getSplitRatioBusinessList({}).then(res => {
        if (res.Data && res.Data.business) {
          for (var i = 0, len = res.Data.business.length; i < len; i++) {
            var business = {}
            business.value = res.Data.business[i]
            business.label = res.Data.business[i]
            this.businessList.push(business)
          }
        }
      })
    },
    getInfo () {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.listCostSplitRatio()
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else {
        this.getYearList()
      }
    },
    createData () {
      // let sum =
      //   Number(this.temp.content.cc) +
      //   Number(this.temp.content.cs) +
      //   Number(this.temp.content.qxb) +
      //   Number(this.temp.content.acg) +
      //   Number(this.temp.content.ssg) +
      //   Number(this.temp.content.aim) +
      //   Number(this.temp.content.zcb)
      // if (sum !== 1) {
      //   notification.error({
      //     message: '比例之和不为1',
      //     description: '比例之和 = ' + sum,
      //   })
      //   return
      // }
      this.temp.content.ratioConfirm = '1'
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          createOrder(this.temp).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功'
              })
              this.temp = response.Data
              this.node_status = 1
              this.$router.push({ path: '/workflow/cost-split-ratio', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    listCostSplitRatio () {
      let requestParameters = {
        month: moment().subtract(12, 'months').format('YYYY-MM'),
        business: this.temp.content.business
      }
      getCostSplitRatioList(requestParameters).then(res => {
        if (res.Data.hasOwnProperty('data')) {
          this.tableData = []
          res.Data.data.forEach(item => {
            item.year = moment().subtract(12, 'months').format('YYYY')
            this.tableData.push(item)
          })
        }
      })
    },
    leaderApproveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功'
              })
            }
          })
        }
      })
    },
    approveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
            }
          })
        }
      })
    },
    approveDataManual () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
            }
          })
        }
      })
    },
    rejectData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝'
              })
            }
          })
        }
      })
    },
    revokeData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回'
              })
            }
          })
        }
      })
    },
    Date () {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds()
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    }
  }
}
</script>

<style scoped></style>
