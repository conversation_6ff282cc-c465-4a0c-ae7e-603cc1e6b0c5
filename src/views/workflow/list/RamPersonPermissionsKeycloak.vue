<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a
          href="https://doc.intsig.net/pages/viewpage.action?pageId=852623435"
          target="_blank"
          style="text-decoration: none"
        >
          流程文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="用户提交申请理由" />
        <a-step title="上级领导填写内容" />
        <a-step title="隔级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        layout="horizontal"
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="云厂商" name="supplier">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-select
                v-model:value="temp.content.supplier"
                showSearch
                @change="supplerChange"
                :disabled="temp.content.editMode"
              >
                <a-select-option
                  v-for="item in supplierList"
                  :key="item.value"
                  :value="item.value"
                  :disabled="item.disabled"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-model-item>
        <div>
          <a-form-model-item name="permissions">
            <template #label>
              权限
              <a-tooltip placement="right">
                <template #title>
                  <span>
                    已选择列表会默认添加上用户已有的控制台账号权限
                    <br />
                  </span>
                </template>
                <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle" />
              </a-tooltip>
            </template>
            <a-row :gutter="24">
              <a-col :span="18">
                <a-transfer
                  v-model:target-keys="temp.content.permissions"
                  :data-source="supplerAllPermission"
                  :list-style="{
                    width: '400px',
                    height: '300px',
                  }"
                  :titles="['可供选择', '已选择( 阿里云最多20个权限,AWS最多10个权限 )']"
                  :operations="['添加', '撤回']"
                  :render="item => item.description"
                  show-search
                  :filter-option="filterOption"
                  @change="handleChange"
                />
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="变更权限" v-if="temp.content.changePermissions.length > 0">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-table
                  :columns="columnsChangePermissions"
                  :data-source="temp.content.changePermissions"
                  :pagination="pagination"
                  class="mytable"
                >
                  <!-- <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex == 'Status'">
                      <a-tag :color="StatusColorFilter(record.Status)">{{ StatusFilter(record.Status) }}</a-tag>
                    </template>
                  </template> -->
                </a-table>
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="申请理由" name="reason">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-textarea
                  v-model:value="temp.content.reason"
                  placeholder="作用用途，信息补充"
                  :autoSize="{ minRows: 2, maxRows: 10 }"
                />
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item v-if="temp.node > 0" label="反馈信息">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-textarea v-model:value="temp.comment" />
              </a-col>
            </a-row>
          </a-form-model-item>
        </div>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            temp.node !== 4 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(2)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(3)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="primary" @click="orSign" style="margin-left: 10px">加签</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal v-model:visible="visible" title="加签" @ok="orSignHandleOk">
      <a-form-model ref="orderForm" :model="temp" :rules="rules" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item name="orSignEmail" label="审批人">
          <a-select
            placeholder="输入**************进行匹配"
            v-model:value="temp.orSignEmail"
            style="width: 300px"
            :showSearch="true"
            :allowClear="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item name="comment" label="说明">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo, orSignOrder } from '@/api/workflow/order'
import { PublicAccountListPolicy } from '@/api/security/account'
import { getKmsKeycloakPolicies } from '@/api/kms/keycloak'
import { getUserList } from '@/api/permission/user'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const statusMap = {
  2: {
    status: 'error',
    text: '未执行',
  },
  1: {
    status: 'success',
    text: '成功',
  },
}
const columnsChangePermissions = [
  {
    title: '变更类型',
    dataIndex: 'changType',
    width: '100px',
  },
  {
    title: '权限',
    dataIndex: 'description',
  },
]

export default {
  name: 'RamPersonPermissionsKeycloak',
  components: {},
  data: function () {
    const filterOption = (inputValue, option) => {
      return option.description.indexOf(inputValue) > -1
    }
    this.columnsChangePermissions = columnsChangePermissions
    return {
      filterOption,
      fetching: false,
      userEmailList: [],
      value: [],
      pagination: {
        defaultPageSize: 1000,
        hideOnSinglePage: true,
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      size: 'small',
      loading: false,
      ipWhiteGroupNameList: [],
      supplerAllPermission: [],
      changePermissions: [],
      supplierList: [
        { label: '阿里云-启信宝', value: '阿里云(启信宝)' },
        { label: '阿里云-合合', value: '阿里云(合合)' },
        { label: '阿里云-临冠', value: '阿里云(临冠)' },
        { label: 'AWS-海外', value: 'AWS(海外)', disabled: true },
        { label: 'AWS-合合', value: 'AWS(合合)', disabled: false },
        { label: 'AWS-临冠', value: 'AWS(临冠)', disabled: true },
        { label: '腾讯云-合合', value: '腾讯云(intsig)', disabled: false },
        { label: '腾讯云-启信宝', value: '腾讯云(qixinbao)', disabled: false },
        { label: '腾讯云-CS', value: '腾讯云(camscanner)', disabled: false },
        { label: '腾讯云-火种项目', value: '腾讯云(火种项目)', disabled: false },
        { label: '火山云-生腾', value: '火山云(生腾)', disabled: false },
        { label: '火山云-合合', value: '火山云(合合)', disabled: false },
      ],
      node_status: 1,
      visible: false,
      temp: {
        id: '',
        orderType: '公有云个人账号权限(keycloak)',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        orSignEmail: '',
        content: {
          editMode: false,
          accountName: undefined,
          supplier: '阿里云(合合)',
          permissions: [],
          permissions_type: [],
          oldPermissions: [],
          changePermissions: [],
          addedPermissions: [],
          deletedPermissions: [],
          accountType: 'person',
          worktype: '',
          reason: '',
        },
        timeline: [],
        comment: '',
        tag: 1,
        affidavit: 1,
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        supplier: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
        accountName: [{ required: true, message: '请输入用户名称', trigger: 'blur' }],
        permissions: [{ required: true, message: '请选择权限', trigger: 'blur' }],
        webLogin: [{ required: true, message: '请选择是否允许web登录', trigger: 'blur' }],
        accessKey: [{ required: true, message: '请选择是否允许访问密钥', trigger: 'blur' }],
        orSignEmail: [{ required: true, message: '请填写审批人邮箱', trigger: 'blur' }],
      },
      dbList: [],
      downloadUrl: '',
      noticeMessage: '',
    }
  },
  created() {
    this.getInfo()
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    orSign() {
      this.visible = true
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.node === 4) {
            this.downloadUrl = response.Data.content.downloadUrl
            this.noticeMessage = response.Data.content.noticeMessage
          }
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.getAccountPolicyList(false)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.supplier) {
        this.temp.content.supplier = this.$route.query.supplier
        this.getAccountPolicyList()
      } else {
        this.getAccountPolicyList()
      }
    },
    async getAccountPolicyList(initPermission = true) {
      let params = { supplier: this.temp.content.supplier }
      getKmsKeycloakPolicies(params).then(response => {
        this.oldPermissions = response.Data.policy || []
        if (initPermission) {
          this.temp.content.permissions = this.oldPermissions || []
          this.temp.content.permissions_type = response.Data.permissions_type
          this.temp.content.oldPermissions = this.oldPermissions || []
        }
      })
      let Supplier = ''
      switch (this.temp.content.supplier) {
        case '阿里云(启信宝)':
          Supplier = 'Aliyun(启信宝)'
          break
        case '阿里云(合合)':
          Supplier = 'Aliyun(合合)'
          break
        case '阿里云(临冠)':
          Supplier = 'Aliyun(临冠)'
          break
        case 'AWS(海外)':
          Supplier = 'AWS(海外)'
          break
        case 'AWS(合合)':
          Supplier = 'AWS(合合)'
          break
        case 'AWS(临冠)':
          Supplier = 'AWS(CS)'
          break
        case '腾讯云(intsig)':
          Supplier = '腾讯云(合合)'
          break
        case '腾讯云(qixinbao)':
          Supplier = '腾讯云(启信宝)'
          break
        case '腾讯云(camscanner)':
          Supplier = '腾讯云(CS)'
          break
        case '腾讯云(火种项目)':
          Supplier = '腾讯云(火种项目)'
          break
        case '火山云(合合)':
          Supplier = '火山云(合合)'
          break
        case '火山云(生腾)':
          Supplier = '火山云(生腾)'
          break
      }
      await PublicAccountListPolicy({ Suppier: Supplier }).then(response => {
        this.supplerAllPermission = response.Data.data
      })
    },
    supplerChange() {
      this.getAccountPolicyList()
      this.temp.content.permissions = []
      this.temp.content.permissions_type = []
      this.temp.content.oldPermissions = []
    },

    handleChange(keys, direction, moveKeys) {
      // 对于aliyun的权限，限制最多20个
      if (
        keys.length > 20 &&
        direction === 'right' &&
        (this.temp.content.supplier === '阿里云(启信宝)' || this.temp.content.supplier === '阿里云(合合)' || this.temp.content.supplier === '阿里云(临冠)')
      ) {
        notification.error({
          message: '添加失败',
          description: '阿里云权限数量最多为20个,请移除当前不需要的权限',
          duration: 7,
        })
      }
      // aws默认为10个权限
      if (
        keys.length > 10 &&
        direction === 'right' &&
        (this.temp.content.supplier === 'AWS(海外)' ||
          this.temp.content.supplier === 'AWS(合合)' ||
          this.temp.content.supplier === 'AWS(临冠)')
      ) {
        notification.error({
          message: '添加失败',
          description: 'aws默认权限数量最多为10个,请移除当前不需要的权限',
          duration: 7,
        })
      }

      // 当前变更权限判断
      this.temp.content.changePermissions = []
      this.temp.content.deletedPermissions = []
      this.temp.content.addedPermissions = []
      const oldPermissions = new Set(this.temp.content.oldPermissions)
      const newPermissions = new Set(this.temp.content.permissions)
      const addedPermissions = Array.from(newPermissions).filter(permission => !oldPermissions.has(permission))
      const deletedPermissions = Array.from(oldPermissions).filter(permission => !newPermissions.has(permission))
      for (let i = 0; i < addedPermissions.length; i++) {
        let description = ''
        if (this.supplerAllPermission !== null) {
          this.supplerAllPermission.forEach(item => {
            if (item.key === addedPermissions[i]) {
              description = item.description
            }
          })
        }
        this.temp.content.changePermissions.push({
          changType: '新增',
          description: description,
        })
        this.temp.content.addedPermissions.push(description)
      }
      for (let i = 0; i < deletedPermissions.length; i++) {
        let description = ''
        if (this.supplerAllPermission !== null) {
          this.supplerAllPermission.forEach(item => {
            if (item.key === deletedPermissions[i]) {
              description = item.description
            }
          })
        }
        this.temp.content.changePermissions.push({
          changType: '删除',
          description: description,
        })
        this.temp.content.deletedPermissions.push(description)
      }
    },

    createData() {
      if (this.temp.content.changePermissions.length === 0) {
        notification.error({
          message: '提交失败',
          description: '无权限变更',
        })
        return
      }
      this.temp.content.permissions_type = []
      this.temp.content.permissions = [...new Set(this.temp.content.permissions)]
      if (this.supplerAllPermission !== null) {
        this.supplerAllPermission.forEach(item => {
          if (this.temp.content.permissions.includes(item.key)) {
            this.temp.content.permissions_type.push(item)
          }
        })
      }

      if (
        this.temp.content.permissions.length > 20 &&
        (this.temp.content.supplier === '阿里云(启信宝)' || this.temp.content.supplier === '阿里云(合合)' || this.temp.content.supplier === '阿里云(临冠)')
      ) {
        notification.error({
          message: '创建失败',
          description: '阿里云权限数量最多为20个,请移除当前不需要的权限',
        })
        return
      }

      if (
        this.temp.content.permissions.length > 10 &&
        (this.temp.content.supplier === 'AWS(海外)' ||
          this.temp.content.supplier === 'AWS(合合)' ||
          this.temp.content.supplier === 'AWS(临冠)')
      ) {
        notification.error({
          message: '创建失败',
          description: 'AWS权限数量最多为10个,请移除当前不需要的权限',
        })
        return
      }

      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          createOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              this.$router.push({ path: '/workflow/cloud-access-keycloak', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData(node) {
      this.temp.content.permissions_type = []
      if (this.supplerAllPermission !== null) {
        this.supplerAllPermission.forEach(item => {
          if (this.temp.content.permissions.includes(item.key)) {
            this.temp.content.permissions_type.push(item)
          }
        })
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = node
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData() {
      this.temp.content.permissions_type = []
      this.temp.content.permissions = [...new Set(this.temp.content.permissions)]
      if (this.supplerAllPermission !== null) {
        this.supplerAllPermission.forEach(item => {
          if (this.temp.content.permissions.includes(item.key)) {
            this.temp.content.permissions_type.push(item)
          }
        })
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    orSignHandleOk() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.affidavit = 100
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          orSignOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
          this.visible = false
        }
      })
    },
    userApproveData() {
      this.node_status = 0
      this.temp.node = 5
      this.temp.status = 10
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      // this.temp.content = { data: JSON.stringify(this.temp.content), noticeMessage: this.noticeMessage }
      let data = JSON.parse(JSON.stringify(this.temp))
      data.content = { data: JSON.stringify(data.content), noticeMessage: this.noticeMessage }
      approveOrder(data).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code !== 200) {
          notification.error({
            message: '审批执行报错',
            description: response.Code,
          })
        } else {
          notification.success({
            message: '审批成功',
            description: '用户审批成功',
          })
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
        }
      })
    },
    rejectData() {
      this.Date()
      this.temp.node = 5
      this.temp.status = 20
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      // this.temp.content = { data: JSON.stringify(this.temp.content) }
      let data = JSON.parse(JSON.stringify(this.temp))
      data.content = { data: JSON.stringify(data.content) }
      approveOrder(data).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '审批完成',
            description: '该审批已被拒绝',
          })
        }
      })
    },
    revokeData() {
      this.temp.node = 5
      this.temp.status = 30
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      let data = JSON.parse(JSON.stringify(this.temp))
      data.content = { data: JSON.stringify(data.content) }
      approveOrder(data).then(response => {
        if (response === undefined) {
          notification.error({
            message: '撤回失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '撤回成功',
            description: '该工单被申请人撤回',
          })
        }
      })
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less" scoped>
.dataBaseTable {
  /deep/ .ant-table-header {
    font-size: 12px;
  }

  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  /deep/ .ant-table-tbody {
    font-size: 14px;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}

.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.jsoneditor-vue {
  height: 100%;
}

.mytable {
  /deep/ .ant-table-tbody > tr > td {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }

  /deep/ .ant-table-thead > tr > th {
    overflow-wrap: break-word;
    padding: 8px;
    position: relative;
  }
}
</style>
