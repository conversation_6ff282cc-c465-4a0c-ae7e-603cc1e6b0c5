<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-10-30 14:10:51
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-05-08 14:25:25
 * @FilePath: \cloud_web\src\views\workflow\WorkflowList.vue
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <ConsulSecretComp />
    </SecondaryVerification>
  </page-header-wrapper>
</template>

<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import ConsulSecretComp from './comp/ConsulSecretComp.vue'
export default {
  name: 'WorkflowList',
  components: {
    ConsulSecretComp,
    SecondaryVerification,
  },
  data() {
    return {
      params: {
        status: undefined,
      },
    }
  },
}
</script>
