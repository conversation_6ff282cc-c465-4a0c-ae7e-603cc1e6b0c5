<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a
          href="https://doc.intsig.net/pages/viewpage.action?pageId=777126182"
          target="_blank"
          style="text-decoration: none"
        >
          流程文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="用户提交申请理由" />
        <a-step title="上级领导填写内容" />
        <a-step title="隔级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        layout="horizontal"
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">待Web端处理</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="云厂商" name="supplier">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-select
                v-model:value="temp.content.supplier"
                showSearch
                @change="supplerChange"
                :disabled="temp.content.editMode"
              >
                <a-select-option v-for="item in supplierList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item name="resourceIdList">
          <template #label>
            资源ID
            <a-tooltip placement="right">
              <template #title>
                <span>
                  1.申请权限的对象
                  <br />
                </span>
                <span>
                  2.当申请对象为腾讯云的存储桶时，资源ID={#bucket-appid}，如 cc-test-1303028177
                  <br />
                </span>
                <span>
                  3.非固定资源的，可填*
                  <br />
                </span>
              </template>
              <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle" />
            </a-tooltip>
          </template>
          <a-form name="resourceIdList_item" :model="temp.content.resourceIdList">
            <a-row
              :gutter="24"
              v-for="(resourceId, index) in temp.content.resourceIdList"
              v-bind="formItemLayoutWithOutLabel"
            >
              <a-col :span="12">
                <a-form-item name="form.resourceIdList[index].value">
                  <a-input v-model:value="resourceId.value" placeholder="请输入资源ID，如 cc-test-1303028177 或 *" />
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item>
                  <tx-button
                    v-if="index === 0"
                    type="primary"
                    icon="plus"
                    style="width: 30px"
                    size="small"
                    @click="addResourceId"
                  ></tx-button>
                  <tx-button
                    v-else
                    type="dashed"
                    size="small"
                    icon="minus"
                    style="width: 30px"
                    @click="removeResourceId(index)"
                  ></tx-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-form-model-item>
        <a-form-model-item name="inputUserName">
          <template #label>
            业务名
            <a-tooltip placement="right">
              <template #title>
                <span>
                  1.业务名必须为字母或者中划线
                  <br />
                </span>
                <span>
                  2.作为vault访问路径，并默认增加后缀：-vault
                  <br />
                </span>
              </template>
              <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle" />
            </a-tooltip>
          </template>
          <a-row :gutter="24">
            <a-col :span="6">
              <a-input
                addon-after="-vault"
                v-model:value="temp.content.inputUserName"
                placeholder=""
                :disabled="temp.content.editMode"
                @change="UserNameChange"
              />
            </a-col>
            <a-icon style="margin-top: 10px" type="arrow-right" />
            <a-col :span="6">
              <a-input v-model:value="temp.content.userName" disabled />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="Consul环境" name="env">
          <a-radio-group
            name="radioGroup"
            default-value="consul_secret"
            button-style="solid"
            v-model:value="temp.content.env"
            @change="envChange"
          >
            <a-radio-button value="consul_vault_secret_test">测试</a-radio-button>
            <a-radio-button value="consul_vault_secret">线上</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="权限" name="permissions">
          <a-row :gutter="24">
            <a-col :span="18">
              <a-transfer
                v-model:target-keys="temp.content.permissions"
                :data-source="supplerAllPermission"
                :list-style="{
                  width: '400px',
                  height: '300px',
                }"
                :titles="['可供选择', '已选择']"
                :operations="['添加', '撤回']"
                :render="item => item.description"
                show-search
                :filter-option="filterOption"
                @change="handleChange"
              />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="变更权限" v-if="temp.content.changePermissions.length > 0">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-table
                :columns="columnsChangePermissions"
                :data-source="temp.content.changePermissions"
                :pagination="pagination"
                class="mytable"
              ></a-table>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea
                v-model:value="temp.content.reason"
                placeholder="作用用途，信息补充"
                :autoSize="{ minRows: 2, maxRows: 20 }"
              />
            </a-col>
          </a-row>
        </a-form-model-item>
        <!--        <a-form-model-item label="Vault Endpoint" name="vaultEndpoint" v-if="temp.node===3 || temp.node===4">-->
        <!--          <a-row :gutter="24">-->
        <!--            <a-col :span="12">-->
        <!--              <a-select-->
        <!--                v-model:value="temp.content.vaultEndpoint"-->
        <!--                showSearch-->
        <!--              >-->
        <!--                <a-select-option v-for="item in vaultEndpointList" :key="item.value" :value="item.value">-->
        <!--                  {{ item.label }}-->
        <!--                </a-select-option>-->
        <!--              </a-select>-->
        <!--            </a-col>-->
        <!--          </a-row>-->
        <!--        </a-form-model-item>-->
        <a-form-model-item label="Consul Endpoint" name="consulEndpoint" v-if="temp.node === 3 || temp.node === 4">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-select v-model:value="temp.content.consulEndpoint" showSearch>
                <a-select-option v-for="item in consulEndpointList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="反馈信息">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(2)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(3)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { listCloudPolicyKey } from '@/api/kms/cloud_policy'
import { checkKmsVaultRoleExist, getConsulSecretExistCheck } from '@/api/kms/vault_path'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const statusMap = {
  2: {
    status: 'error',
    text: '未执行'
  },
  1: {
    status: 'success',
    text: '成功'
  }
}
const columnsChangePermissions = [
  {
    title: '变更类型',
    dataIndex: 'changType',
    width: '100px'
  },
  {
    title: '权限',
    dataIndex: 'description'
  }
]

export default {
  name: 'RamBusinessAccessVault',
  components: {},
  data: function() {
    const filterOption = (inputValue, option) => {
      return option.description.indexOf(inputValue) > -1
    }
    this.columnsChangePermissions = columnsChangePermissions
    return {
      filterOption,
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 }
        }
      },
      fetching: false,
      userEmailList: [],
      value: [],
      pagination: {
        defaultPageSize: 1000,
        hideOnSinglePage: true
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      size: 'small',
      loading: false,
      ipWhiteGroupNameList: [],
      supplerAllPermission: [],
      supplierList: [
        { label: '阿里云-启信宝', value: '阿里云(启信宝)' },
        { label: '阿里云-合合', value: '阿里云(合合)' },
        { label: '阿里云-临冠', value: '阿里云(临冠)' },
        { label: 'AWS-海外', value: 'AWS(海外)' },
        { label: 'AWS-海外AI', value: 'AWS(海外AI)' },
        { label: 'AWS-海外AI2', value: 'AWS(海外AI2)' },
        { label: 'AWS-合合', value: 'AWS(合合)' },
        { label: 'AWS-临冠', value: 'AWS(临冠)' },
        { label: '腾讯云-合合', value: '腾讯云(intsig)' },
        { label: '腾讯云-CS', value: '腾讯云(camscanner)' }
      ],
      vaultEndpointList: [{ label: 'https://vault-test.intsig.net:8200', value: 'https://vault-test.intsig.net:8200' }],
      consulEndpointList: [{ label: 'sh2-test-internal.intsig.net:8500', value: 'sh2-test-internal.intsig.net:8500' }],
      node_status: 1,
      temp: {
        id: '',
        orderType: '公有云业务API权限(vault)',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          editMode: false,
          userName: '-vault',
          supplier: '阿里云(启信宝)',
          cluster: 'ucloud',
          changePermissions: [],
          addedPermissions: [],
          deletedPermissions: [],
          permissions: [],
          permissions_type: [],
          reason: '',
          method: 'Vault',
          operation: 1, // 1-create 2-update
          inputUserName: '',
          consulKey: 'vault-token/',
          env: 'consul_vault_secret_test',
          inputConsulKey: '',
          vaultEndpoint: '',
          consulEndpoint: '',
          resourceIdList: []
        },
        timeline: [],
        comment: ''
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        supplier: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
        cluster: [{ required: true, message: '请选择集群', trigger: 'blur' }],
        resourceIdList: [{ required: true, message: '请填写资源ID', trigger: 'blur' }],
        inputUserName: [
          { required: true, message: '请输入用户名称', trigger: 'blur' },
          {
            validator: this.validateUserName,
            trigger: 'blur'
          }
        ],
        permissions: [{ required: true, message: '请选择权限', trigger: 'blur' }],
        inputConsulKey: [{ required: true, message: '请输入Consul路径', trigger: 'blur' }],
        env: [{ required: true, message: '请选择环境', trigger: 'blur' }],
        vaultEndpoint: [{ required: true, message: '请选择Vault Endpoint', trigger: 'blur' }],
        consulEndpoint: [{ required: true, message: '请选择Consul Endpoint', trigger: 'blur' }]
      },
      dbList: []
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed () {
    removeWatermark()
  },
  created () {
    this.getInfo()
    // this.temp.content.userName = store.getters.email.split('@')[0] + '@bertadata.onaliyun.com'
  },
  methods: {
    validateUserName (rule, value) {
      return new Promise((resolve, reject) => {
        const reg = /^[a-zA-Z-]+$/ // 正则表达式，匹配字母
        if (value && !reg.test(value)) {
          reject(new Error('业务名称必须为字母或者中划线'))
        } else {
          resolve()
        }
      })
    },
    getInfo () {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.getAccountPolicyList()
            this.envChange()
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.record) {
        let record = JSON.parse(this.$route.query.record)
        console.log('[getInfo]', record)
        this.temp.content.supplier = record.supplier
        this.supplerAllPermission = []
        this.getAccountPolicyList()
        this.temp.content.userName = record.userName
        this.temp.content.id = record.id
        this.temp.content.operation = record.operation
        this.temp.content.permissions = record.permissions
        this.temp.content.permissions_type = record.permissions_type
        this.temp.content.method = record.method
        this.temp.content.changePermissions = []
        this.temp.content.oldPermissions = this.temp.content.permissions
        this.temp.content.editMode = true
        this.envChange()
      } else {
        this.temp.content.resourceIdList.push({
          value: ''
        })
        this.getAccountPolicyList()
        this.envChange()
      }
    },
    getAccountPolicyList () {
      let params = { supplier: this.temp.content.supplier }
      listCloudPolicyKey(params).then(response => {
        this.supplerAllPermission = response.Data.kmsCloudPolicies || []
      })
    },
    supplerChange () {
      this.getAccountPolicyList()
      this.temp.content.permissions = []
      this.temp.content.permissions_type = []
      this.temp.content.oldPermissions = []
    },

    handleChange (keys, direction, moveKeys) {
      if (direction === 'left') {
        console.log('handleChange1', this.temp.content.permissions_type)
        if (this.temp.content.permissions_type !== null && this.temp.content.permissions_type !== undefined) {
          this.temp.content.permissions_type.forEach((item, index) => {
            if (moveKeys.includes(item.key)) {
              this.temp.content.permissions_type.splice(index, 1)
            }
          })
        }
      } else {
        console.log('handleChange2', this.supplerAllPermission)
        if (this.supplerAllPermission !== null && this.supplerAllPermission !== undefined) {
          this.supplerAllPermission.forEach(item => {
            if (moveKeys.includes(item.key)) {
              this.temp.content.permissions_type.push(item)
            }
          })
        }
      }
      // 当前变更权限判断
      this.temp.content.changePermissions = []
      this.temp.content.deletedPermissions = []
      this.temp.content.addedPermissions = []
      const oldPermissions = new Set(this.temp.content.oldPermissions)
      const newPermissions = new Set(this.temp.content.permissions)
      const addedPermissions = Array.from(newPermissions).filter(permission => !oldPermissions.has(permission))
      const deletedPermissions = Array.from(oldPermissions).filter(permission => !newPermissions.has(permission))
      if (addedPermissions !== undefined) {
        for (let i = 0; i < addedPermissions.length; i++) {
          let description = ''
          console.log('handleChange3', this.supplerAllPermission)
          if (this.supplerAllPermission !== null && this.supplerAllPermission !== undefined) {
            this.supplerAllPermission.forEach(item => {
              if (item.key === addedPermissions[i]) {
                description = item.description
              }
            })
          }
          this.temp.content.changePermissions.push({
            changType: '新增',
            description: description
          })
          this.temp.content.addedPermissions.push(description)
        }
      }
      if (deletedPermissions !== undefined) {
        for (let i = 0; i < deletedPermissions.length; i++) {
          let description = ''
          console.log('handleChange4', this.supplerAllPermission)
          if (this.supplerAllPermission !== null && this.supplerAllPermission !== undefined) {
            this.supplerAllPermission.forEach(item => {
              if (item.key === deletedPermissions[i]) {
                description = item.description
              }
            })
          }
          this.temp.content.changePermissions.push({
            changType: '删除',
            description: description
          })
          this.temp.content.deletedPermissions.push(description)
        }
      }
    },

    envChange () {
      if (this.temp.content.env === 'consul_vault_secret_test') {
        this.consulEndpointList = [
          { label: 'consul-uc-sandbox-a.intsig.net:8500', value: 'consul-uc-sandbox-a.intsig.net:8500' },
          { label: 'consul-aliqxb-sandbox-a.intsig.net:8500', value: 'consul-aliqxb-sandbox-a.intsig.net:8500' },
          { label: 'consul-us-sandbox-a.intsig.net:8500', value: 'consul-us-sandbox-a.intsig.net:8500' }
        ]
        this.vaultEndpointList = [
          { label: 'https://vault-test.intsig.net:8200', value: 'https://vault-test.intsig.net:8200' }
          // { label: '[本地环境]http://*************:8300', value: 'http://*************:8300' }
        ]
      } else {
        this.consulEndpointList = [
          { label: 'consul-uc-a.intsig.net:8500', value: 'consul-uc-a.intsig.net:8500' },
          { label: 'consul-us-a.intsig.net:8500', value: 'consul-us-a.intsig.net:8500' },
          { label: 'consul-awsnx-a.intsig.net:8500', value: 'consul-awsnx-a.intsig.net:8500' },
          { label: 'consul-aliqxb-a.intsig.net:8500', value: 'consul-aliqxb-a.intsig.net:8500' }
        ]
        this.vaultEndpointList = [
          { label: 'https://a-uc-vault.intsig.net:8200', value: 'https://a-uc-vault.intsig.net:8200' },
          { label: 'https://b-uc-vault.intsig.net:8200', value: 'https://b-uc-vault.intsig.net:8200' },
          { label: 'https://a-us-vault.intsig.net:8200', value: 'https://a-us-vault.intsig.net:8200' },
          { label: 'https://b-us-vault.intsig.net:8200', value: 'https://b-us-vault.intsig.net:8200' },
          { label: 'https://c-us-vault.intsig.net:8200', value: 'https://c-us-vault.intsig.net:8200' },
          { label: 'https://a-ali-vault.intsig.net:8200', value: 'https://a-ali-vault.intsig.net:8200' },
          { label: 'https://b-ali-vault.intsig.net:8200', value: 'https://b-ali-vault.intsig.net:8200' }
        ]
      }
    },

    consulKeyChange () {
      this.temp.content.consulKey = 'vault-token/' + this.temp.content.inputConsulKey
      // // 去后台校验是否已经存在
      // getConsulSeInfo({ requestEnv: this.temp.content.env, consulkey: this.temp.content.consulKey }).then(
      //   response => {
      //     if (response.Code === 200 && response.Data.consulValue !== 'noExist') {
      //       notification.error({
      //         message: 'Key值已存在',
      //         description: '请重新设置Key值'
      //       })
      //     }
      //   })
    },

    UserNameChange () {
      this.temp.content.userName = this.temp.content.inputUserName + '-vault'
      this.temp.content.consulKey = 'vault-token/' + this.temp.content.inputUserName
    },

    createData () {
      // if (this.temp.content.permissions.length > 20) {
      //   notification.error({
      //     message: '创建失败',
      //     description: '权限数量最多为20个,请移除当前不需要的权限'
      //   })
      //   return
      // }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.temp.content.method === 'Cloud' && this.temp.content.operation === 1) {
            checkKmsRamUserExist({
              supplier: this.temp.content.supplier,
              userName: this.temp.content.userName
            }).then(response => {
              if (response === undefined) {
                notification.error({
                  message: '校验失败',
                  description: '后端接口错误，请联系运维开发排查~'
                })
              } else if (response.Data.exist) {
                notification.error({
                  message: '业务名称已存在',
                  description: '请重新设置业务名称'
                })
              } else if (!response.Data.exist) {
                this.submitCreateOrder()
              }
            })
          } else if (this.temp.content.method === 'Vault' && this.temp.content.operation === 1) {
            let validFlag = true
            // 去后台校验是否已经存在
            getConsulSecretExistCheck({ requestEnv: this.temp.content.env, consulkey: this.temp.content.consulKey }).then(
              response => {
                if (response.Code === 200 && response.Data.consulValue !== 'noExist') {
                  validFlag = false
                  notification.error({
                    message: '业务名称已存在-Consul',
                    description: '请重新设置业务名称'
                  })
                }
                checkKmsVaultRoleExist({
                  supplier: this.temp.content.supplier,
                  cluster: this.temp.content.cluster,
                  userName: this.temp.content.userName
                }).then(response => {
                  if (response === undefined) {
                    validFlag = false
                    notification.error({
                      message: '校验失败',
                      description: '后端接口错误，请联系运维开发排查~'
                    })
                  } else if (response.Data.exist) {
                    validFlag = false
                    notification.error({
                      message: '业务名称已存在',
                      description: '请重新设置业务名称'
                    })
                  }
                  if (validFlag) {
                    this.submitCreateOrder()
                  }
                })
              }
            )
          } else {
            this.submitCreateOrder()
          }
        }
      })
    },
    submitCreateOrder () {
      let resourceIdList = []
      if (this.temp.content.resourceIdList !== null && this.temp.content.resourceIdList !== undefined) {
        this.temp.content.resourceIdList.forEach(item => {
          if (item.value !== '') {
            resourceIdList.push(item.value)
          }
        })
      }
      if (resourceIdList.length === 0) {
        notification.error({
          message: '缺少资源ID',
          description: '请填写资源ID'
        })
        return
      }
      this.temp.content.resourceId = resourceIdList.join('|')
      this.Date()
      this.node_status = 0
      this.temp.node = 1
      this.temp.status = 1
      this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
      // this.temp.content = { data: JSON.stringify(this.temp.content) }
      let data = JSON.parse(JSON.stringify(this.temp))
      data.content = { data: JSON.stringify(data.content) }
      createOrder(data).then(response => {
        if (response === undefined) {
          notification.error({
            message: '创建失败',
            description: '后端接口错误，请联系运维开发排查~'
          })
        } else {
          notification.success({
            message: '创建成功',
            description: '工单创建成功'
          })
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.node_status = 1
          this.$router.push({ path: '/workflow/cloud-access-vault', query: { id: response.Data.id } })
        }
      })
    },
    leaderApproveData (node) {
      let resourceIdList = []
      if (this.temp.content.resourceIdList !== null && this.temp.content.resourceIdList !== undefined) {
        this.temp.content.resourceIdList.forEach(item => {
          if (item.value !== '') {
            resourceIdList.push(item.value)
          }
        })
      }
      if (resourceIdList.length === 0) {
        notification.error({
          message: '缺少资源ID',
          description: '请填写资源ID'
        })
        return
      }
      this.temp.content.resourceId = resourceIdList.join('|')
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = node
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '领导审批成功'
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData () {
      let resourceIdList = []
      if (this.temp.content.resourceIdList !== null && this.temp.content.resourceIdList !== undefined) {
        this.temp.content.resourceIdList.forEach(item => {
          if (item.value !== '') {
            resourceIdList.push(item.value)
          }
        })
      }
      if (resourceIdList.length === 0) {
        notification.error({
          message: '缺少资源ID',
          description: '请填写资源ID'
        })
        return
      }
      this.temp.content.resourceId = resourceIdList.join('|')
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData () {
      this.Date()
      this.temp.node = 5
      this.temp.status = 20
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      // this.temp.content = { data: JSON.stringify(this.temp.content) }
      let data = JSON.parse(JSON.stringify(this.temp))
      data.content = { data: JSON.stringify(data.content) }
      approveOrder(data).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~'
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '审批完成',
            description: '该审批已被拒绝'
          })
        }
      })
    },
    revokeData () {
      this.temp.node = 5
      this.temp.status = 30
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      // this.temp.content = { data: JSON.stringify(this.temp.content) }
      let data = JSON.parse(JSON.stringify(this.temp))
      data.content = { data: JSON.stringify(data.content) }
      approveOrder(data).then(response => {
        if (response === undefined) {
          notification.error({
            message: '撤回失败',
            description: '后端接口错误，请联系运维开发排查~'
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '撤回成功',
            description: '该工单被申请人撤回'
          })
        }
      })
    },
    Date () {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds()
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    addResourceId () {
      this.temp.content.resourceIdList.push({
        value: ''
      })
      console.log('resourceIdList', this.temp.content.resourceIdList)
    },
    removeResourceId (i) {
      if (this.temp.content.resourceIdList.length > 1) {
        this.temp.content.resourceIdList.splice(i, 1)
      }
    }
  }
}
</script>

<style lang="less">
.dataBaseTable {
  /deep/ .ant-table-header {
    font-size: 12px;
  }

  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  /deep/ .ant-table-tbody {
    font-size: 14px;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}

.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.jsoneditor-vue {
  height: 100%;
}
</style>
