<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-10-20 15:05:27
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2022-10-21 16:48:02
 * @FilePath: \cloud_web\src\views\comp\editor.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <TxEditorCode v-model="opts.value" class="monaco-editor" height="380" :options="defaultOpts" @change="handleChange" @widgetCreate="editorCreate"/>
</template>

<script>
export default {
  props: {
    opts: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      monacoEditor: {},
      defaultOpts: {
        value: '', // 编辑器的值
        theme: 'vs-dark', // 编辑器主题：vs, hc-black, or vs-dark，更多选择详见官网
        minimap: { // 关闭小地图
          enabled: false,
        },
        foldingStrategy: 'indentation', // 代码可分小段折叠
        scrollbar: { // 滚动条设置
          verticalScrollbarSize: 8, // 竖滚动条
          horizontalScrollbarSize: 8, // 横滚动条
        },
        autoClosingBrackets: 'always', // 是否自动添加结束括号(包括中括号) "always" | "languageDefined" | "beforeWhitespace" | "never"
        autoClosingDelete: 'always', // 是否自动删除结束括号(包括中括号) "always" | "never" | "auto"
        autoClosingQuotes: 'always', // 是否自动添加结束的单引号 双引号 "always" | "languageDefined" | "beforeWhitespace" | "never"
        overviewRulerBorder: false,
        autoIndent: true, // 自动缩进
        // 存在性能问题，组件已支持自动计算编辑器区域大小
        // automaticLayout: true, // 自动布局
        readOnly: true, // 只读
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 生成编辑器配置
      Object.assign(this.defaultOpts, this.opts)
    },
    editorCreate(editor) {
      this.monacoEditor = editor
    },
    handleChange() {
      // 使用传递进来的值进行检查和替换
      if (this.opts.value.includes('--array=')) {
        let selection = this.monacoEditor.getSelection()
        let value = this.opts.value.replaceAll("--array=", "")
        this.monacoEditor.setValue(value)
        this.monacoEditor.setPosition({
          lineNumber: selection.startLineNumber,
          column: selection.startColumn,
        })
      }
    }
  },
}
</script>

<style>
.monaco-editor {
  width: 100%;
  height: 100%;
  margin-top: 16px;
  border: 1px solid grey;
}
</style>
