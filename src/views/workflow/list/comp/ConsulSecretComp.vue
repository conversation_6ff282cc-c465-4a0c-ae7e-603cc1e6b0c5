<template>
  <a-card :bordered="false">
    <a-steps :current="temp.node">
      <a-step title="提交工单" />
      <a-step title="上级领导审批" />
      <a-step title="应用管理员审批" />
      <a-step title="完成" />
    </a-steps>
  </a-card>
  <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
    <a-form-model
      ref="orderForm"
      :model="temp.content"
      :rules="rules"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
      <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
      <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
        <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
        <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
        <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
        <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
        <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
        <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
        <a-tag v-else>未知</a-tag>
      </a-form-model-item>
      <a-form-model-item label="类型">
        <a-radio-group
          name="radioGroup"
          default-value="addConsul"
          @change="radioGroupChang"
          button-style="solid"
          v-model:value="temp.content.consulOrderType"
        >
          <a-radio-button value="addConsul">新增</a-radio-button>
          <a-radio-button value="modifyConsul">修改</a-radio-button>
          <a-radio-button value="deleteConsul">删除</a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="环境">
        <a-radio-group
          name="radioGroup"
          default-value="consul_secret"
          @change="radioGroupChang"
          button-style="solid"
          v-model:value="temp.content.requestEnv"
        >
          <a-radio-button value="consul_secret_test">测试</a-radio-button>
          <a-radio-button value="consul_secret">线上</a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <div v-if="temp.content.consulOrderType === 'addConsul'">
        <a-form-model-item label="Key预览" name="projectName">
          <a-input v-model:value="temp.content.consulKey" disabled />
        </a-form-model-item>
        <a-form-model-item label="Key" name="inputConsulKey">
          <a-input v-model:value="temp.content.inputConsulKey" placeholder="请输入密钥" @change="consulKeyChange" />
          <div v-if="exceedAuth" class="ip-error-tips">Key值超出授权范围，请联系运维增加授权！</div>
        </a-form-model-item>
        <a-form-model-item label="秘钥内容" name="consulValue">
          <a-textarea v-model:value="temp.content.consulValue" :autoSize="{ minRows: 5, maxRows: 20 }" />
        </a-form-model-item>
        <a-form-model-item label="机房" name="cluster">
          <a-select
            v-model:value="temp.content.cluster"
            placeholder="请选择机房"
            style="width: 90%"
            @change="clustersChange"
          >
            <a-select-option v-for="item in clustersList" :key="item.key" :value="item.key">
              {{ item.name }}
            </a-select-option>
          </a-select>
          <a-tooltip>
            <template #title>1.混合云的工单选择“混合云重保集群”，读取也从重保系统中读取<br>
              2.其他机房的工单选择“混合云线上”这个集群去管理，读取则按照之前给的各机房接入点，对应读取</template>
              <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="IP来源限制" name="ipOrigin">
          <div v-if="isIpError" class="ip-error-tips">IP格式错误，暂时无法添加，详情请联系运维人员</div>
          <a-select
            v-model:value="temp.content.ipOrigin"
            mode="tags"
            style="width: 100%"
            placeholder="请输入IP或网段"
            :options="ipsOptions"
            @change="ipItemChange"
            @search="ipItemSearch"
          ></a-select>
        </a-form-model-item>
      </div>
      <div
        v-else-if="temp.content.consulOrderType === 'modifyConsul' || temp.content.consulOrderType === 'deleteConsul'"
      >
        <a-form-model-item label="机房" name="cluster">
          <a-select v-model:value="temp.content.cluster"  placeholder="请选择机房" @change="clustersChange">
            <a-select-option v-for="item in clustersList" :key="item.key" :value="item.key">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="Key" name="consulKey">
          <a-select
            v-model:value="temp.content.consulKey"
            placeholder="请选择密钥"
            allowClear
            :showSearch="true"
            @change="userKeyChange"
          >
            <a-select-option v-for="item in consulKeyList" :key="item.key" :value="item.key">
              {{ `${item.clusterName}：${item.key}` }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item v-if="temp.content.consulOrderType === 'modifyConsul'" label="IP来源限制" name="ipOrigin">
          <div v-if="isIpError" class="ip-error-tips">IP格式错误，暂时无法添加，详情请联系运维人员</div>
          <a-select
            v-model:value="temp.content.ipOrigin"
            mode="tags"
            style="width: 100%"
            placeholder="请输入IP或网段"
            :options="ipsOptions"
            @change="ipItemChange"
            @search="ipItemSearch"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item label="秘钥内容">
          <a-textarea
            v-model:value="temp.content.oldConsulValue"
            placeholder="密钥内容"
            disabled
            :autoSize="{ minRows: 5, maxRows: 20 }"
          />
          <tx-button icon="copy" size="small" @click="copyContent(temp.content.oldConsulValue)"/>
        </a-form-model-item>
        <a-form-model-item v-if="temp.content.consulOrderType === 'modifyConsul'" label="秘钥内容比对">
          <NocCodeDiff :old-string="temp.content.oldConsulValue" :new-string="temp.content.consulValue" :context="20" />
        </a-form-model-item>
        <a-form-model-item label="新秘钥内容" name="consulValue" v-if="temp.content.consulOrderType === 'modifyConsul'">
          <a-textarea
            v-model:value="temp.content.consulValue"
            placeholder="密钥内容"
            :autoSize="{ minRows: 5, maxRows: 20 }"
          />
        <tx-button icon="copy" size="small" @click="copyContent(temp.content.consulValue)"/>
        </a-form-model-item>
      </div>
      <a-form-model-item label="申请理由" name="reason">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
          </a-col>
        </a-row>
      </a-form-model-item>
      <a-form-model-item v-if="temp.node > 0" label="回复/评论">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-textarea v-model:value="temp.comment" />
          </a-col>
        </a-row>
      </a-form-model-item>
      <a-form-model-item v-if="temp.node > 0" label="审批节点">
        <a-timeline>
          <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
            {{ activity }}
          </a-timeline-item>
        </a-timeline>
      </a-form-model-item>
      <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
        <tx-button type="primary" @click="createData">提交</tx-button>
        <tx-button style="margin-left: 10px">
          <router-link to="/workflow/createWorkflow">取消</router-link>
        </tx-button>
      </a-form-model-item>
      <a-form-model-item
        v-else-if="
          temp.founderEmail.includes(localUser) &&
          !temp.handlerEmail.includes(localUser) &&
          temp.node !== 0 &&
          temp.node !== 3 &&
          node_status === 1
        "
        :wrapper-col="{ span: 16, offset: 4 }"
      >
        <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
      </a-form-model-item>

      <a-form-model-item
        v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
        :wrapper-col="{ span: 16, offset: 4 }"
      >
        <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
        <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
      </a-form-model-item>
      <a-form-model-item
        v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
        :wrapper-col="{ span: 16, offset: 4 }"
      >
        <tx-button v-if="temp.content.passwd === 'no'" type="primary" @click="dbaCheck">DBA校验</tx-button>
        <tx-button v-else type="primary" @click="approveData">同意</tx-button>
        <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
      </a-form-model-item>
    </a-form-model>
  </a-card>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import NocCodeDiff from '@/suite/CodeDiff/index.vue'
import { Copy } from '@ant-design/icons-vue';
import { approveOrder, createOrder, getImportOrderInfo } from '@/api/workflow/order'
import { getConsulSeInfo, getUserSeList, getCheckKey } from '@/api/consul/secret'
import { getClustersList, getConsulKey } from '@/api/consul/console'

export default {
  name: 'ConsulSecret',
  components: { NocCodeDiff },
  data: function () {
    return {
      fetching: false,
      creatKey: true,
      exceedAuth: false,
      userEmailList: [],
      clustersList: [],
      consulKeyList: [],
      ipsOptions: [],
      isIpError: false,
      value: [],
      confirmLoading: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      userKeyList: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      checkPaaswd: '',
      temp: {
        id: '',
        orderType: 'Consul密钥配置申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          consulOrderType: 'addConsul',
          requestEnv: 'consul_secret',
          consulKey: 'thirdparty/',
          inputConsulKey: '',
          oldConsulValue: '',
          consulValue: '',
          id: undefined,
          reason: '',
          cluster: [],
          ipOrigin: [],
        },
        timeline: [],
        comment: '',
      },
      rules: {
        // 'content.inputConsulKey': [{ required: true, message: '请输入秘钥key值' }],
        // 'content.consulKey': [{ required: true, message: '请选择秘钥' }],
        // 'content.consulValue': [{ required: true, message: '请输入秘钥value值' }],
        reason: [{ required: true, message: '请填写申请理由' }],
        cluster: [{ required: true, message: '请选择机房' }],
        ipOrigin: [{ required: true, message: '请输入ip或网段' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id && this.$route.query.orderType) {
        getImportOrderInfo({ id: this.$route.query.id, orderType: this.$route.query.orderType }).then(response => {
          response.Data.content = JSON.parse(response.Data.content.consulData)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.getClustersList()
            this.getConsulKey()
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
      this.getClustersList()
    },
    radioGroupChang() {
      this.getClustersList()
      const { consulOrderType, inputConsulKey } = this.temp.content
      if (consulOrderType === 'addConsul') {
        this.temp.content.consulKey = 'thirdparty/' + inputConsulKey
      } else {
        this.temp.content.cluster = []
        this.temp.content.consulKey = ''
        this.temp.content.consulValue = ''
        this.temp.content.oldConsulValue = ''
        // getUserSeList({ requestEnv: this.temp.content.requestEnv, userName: this.localUser }).then(response => {
        //   this.userKeyList = response.Data.keys
        // })
      }
    },
    clustersChange() {
      const { consulOrderType } = this.temp.content
      if (consulOrderType !== 'addConsul') {
        this.temp.content.consulKey = ''
        this.temp.content.consulValue = ''
        this.temp.content.oldConsulValue = ''
        this.getConsulKey()
      }
    },
    getClustersList() {
      const env = this.temp.content.requestEnv === 'consul_secret' ? 'prod' : 'test'
      getClustersList({ env }).then(response => {
        this.clustersList = response.Data.data || []
      })
    },
    getConsulKey() {
      getConsulKey({
        cluster: this.temp.content.cluster,
      }).then(response => {
        this.consulKeyList = response.Data.data || []
      })
    },
    consulKeyChange() {
      this.temp.content.consulKey = 'thirdparty/' + this.temp.content.inputConsulKey
      this.getConsulSeInfo()
      this.getCheckKey()
    },
    getConsulSeInfo() {
      // 去后台校验是否已经存在
      getConsulSeInfo({
        requestEnv: this.temp.content.requestEnv,
        consulkey: this.temp.content.consulKey,
        cluster: this.temp.content.cluster,
      }).then(response => {
        if (response.Code === 200 && response.Data.consulValue !== 'noExist') {
          // this.temp.content.oldConsulValue = response.Data.consulValue
          notification.error({
            message: 'key值已存在,不允许创建！',
          })
          this.creatKey = false
        } else {
          this.creatKey = true
        }
      })
    },
    getCheckKey() {
      // 去后台校验是否合格
      getCheckKey({ key: this.temp.content.consulKey }).then(response => {
        if (response.Code === 200 && response.Data.message !== 'pass') {
          this.exceedAuth = true
        } else {
          this.exceedAuth = false
        }
      })
    },
    userKeyChange() {
      if (this.temp.content.consulKey !== '' || this.temp.content.consulKey !== 'thirdparty/') {
        for (let i = 0; i < this.userKeyList.length; i++) {
          if (this.userKeyList[i].key === this.temp.content.consulKey) {
            this.temp.content.id = this.userKeyList[i].id
          }
        }
        this.temp.content.oldConsulValue = ''
        getConsulSeInfo({
          requestEnv: this.temp.content.requestEnv,
          consulkey: this.temp.content.consulKey,
          cluster: this.temp.content.cluster,
        }).then(response => {
          this.temp.content.oldConsulValue = response.Data.consulValue
          this.temp.content.ipOrigin = response.Data.access_ips || []
        })
      }
    },
    createData() {
      if (this.temp.content.consulOrderType === 'addConsul' && (this.creatKey === false || this.exceedAuth === true)) {
        notification.error({
          message: this.creatKey === false ? 'key值已存在,不允许创建！' : 'Key值超出授权范围，请联系运维增加授权！',
        })
        return
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { consulData: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.consulData)
              this.temp = response.Data
              this.$router.push({
                path: '/workflow/consul',
                query: { id: response.Data.id, orderType: response.Data.orderType },
              })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          if (this.temp.content.requestEnv == 'consul_secret_test') {
            this.temp.status = 10
            this.temp.node = 3
          }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { consulData: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.consulData)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { consulData: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.consulData)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { consulData: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.consulData)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { consulData: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.consulData)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    copyContent (textToCopy){
    navigator.clipboard.writeText(textToCopy).then(() => {
      this.$message.success("复制成功")
      console.log('Text copied to clipboard');
    }).catch((err) => {
      this.$message.warning("复制失败")
      console.error('Unable to copy text to clipboard', err);
    });
  },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    validateIPOrCIDR(input) {
      // 合并 IPv4、IPv6 和 CIDR 的正则表达式
      const ipOrCIDRRegex =
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(3[0-2]|[12]?[0-9]))?$|^(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}(?:\/(12[0-8]|1[01][0-9]|[1-9]?[0-9]))?$/i
      return ipOrCIDRRegex.test(input)
    },
    ipItemChange(newValue) {
      // 过滤掉不合法的值
      const validValues = newValue.filter(item => this.validateIPOrCIDR(item))
      // 如果有不合法的值，提示用户
      if (validValues.length < newValue.length) {
        // this.$message.error('输入包含不合法的 IP 地址或网段，已自动过滤')
        this.isIpError = true
      } else {
        this.isIpError = false
      }
      // 只更新合法的值
      this.temp.content.ipOrigin = validValues
      // 更新 options，只保留合法的值
      this.ipsOptions = validValues.map(item => ({ value: item, label: item }))
    },
    ipItemSearch(searchText) {
      if (searchText && !this.validateIPOrCIDR(searchText)) {
        this.isIpError = true
      } else if (searchText) {
        // 如果输入合法，将其添加到 options 中
        this.isIpError = false
        if (!this.ipsOptions.some(item => item.value === searchText)) {
          this.ipsOptions = [...this.ipsOptions, { value: searchText, label: searchText }]
        }
      } else {
        this.isIpError = false
      }
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.hljs {
  display: inline-block;
  padding: 0;
  background: transparent;
  vertical-align: middle;
  height: auto;
}
/deep/.d2h-diff-table {
  width: 100%;
  /* border-collapse: collapse; */
  font-family: Menlo, Consolas, monospace;
  font-size: 9px;
  line-height: normal;
}
.ip-error-tips {
  color: #ff4d4f;
  font-size: 14px;
}
</style>
