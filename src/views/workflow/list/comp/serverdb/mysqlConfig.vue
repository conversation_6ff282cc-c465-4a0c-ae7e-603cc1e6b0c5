<template>
  <!-- 配置列表 -->
  <div style="margin-top: 24px">
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item ref="version" label="version" name="version">
        <a-select @change="versionChange" v-model:value="formState.version" placeholder="请选择版本">
          <a-select-option value="mysql5.7">mysql5.7</a-select-option>
          <a-select-option value="mysql8">mysql8</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="参数模板" name="template">
        <a-select @change="templateChange" v-model:value="formState.template" placeholder="请选择参数模板">
          <a-select-option v-for="i in templateOpts" :value="JSON.stringify(i)">{{ i.name }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="管理员密码" name="pwd">
        <a-input-search
          v-model:value="formState.pwd"
          type="password"
          placeholder="请输入管理员密码并校验"
          enter-button="校验"
          @search="checkPwd"
        />
      </a-form-item>
      <!-- <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button type="primary" @click="onSubmit">Create</a-button>
        <a-button style="margin-left: 10px" @click="resetForm">Reset</a-button>
      </a-form-item> -->
    </a-form>
  </div>
</template>

<script>
import { serverResult, reTry } from '@/api/workflow/asset_automation'
import { ref, props, watch } from 'vue'
import { reactive } from 'vue'
import { getTemplateSelect } from '@/api/workflow/order'
import { dbPasswdCheck } from '@/api/db/ddl'
import { message } from 'ant-design-vue'
import { notification } from 'ant-design-vue'
import user from '@/store/modules/user'
export default {
  props: ['temp'],
  setup(props, context) {
    const templateOpts = ref([])
    const passwordStr = ref('')
    // const hidsInit = ref(props.temp.content.hidsInit)
    // const needInit = ref(props.temp.content.needInit)
    const formRef = ref()
    const labelCol = {
      span: 5,
    }
    const wrapperCol = {
      span: 13,
    }
    const formState = reactive({
      version: 'mysql8',
      template: '',
      pwd: '',
    })

    const rules = {
      template: [
        {
          required: true,
          message: '请选择模板',
          trigger: 'change',
        },
      ],
      version: [
        {
          required: true,
          message: '请选择版本',
          trigger: 'change',
        },
      ],
    }
    const validateForm = async () => {
      let obj = {}
      await formRef.value
        .validate()
        .then(res => {
          obj = { val: true, pwdStr: passwordStr.value }
        })
        .catch(err => {
          obj = { val: false, pwdStr: passwordStr.value }
        })
      return obj
    }

    const checkPwd = v => {
      dbPasswdCheck({ password: v })
        .then(res => {
          if (res.Data.message && res.Data.message != 'error') {
            passwordStr.value = res.Data.message
            message.success('检验成功')
          } else {
            passwordStr.value = ''
            message.error('校验失败，请重新输入密码')
          }
        })
        .catch(err => {
          passwordStr.value = ''
          message.error('校验失败')
        })
    }
    const versionChange = val => {
      context.emit('paramsChange', { val: val, type: 'version' })
      getTemplateSelect({
        version: val,
      })
        .then(res => {
          if (res.Data.data) {
            templateOpts.value = res.Data.data
            console.log(templateOpts.value, '  templateOpts.value')
            if (templateOpts.value && templateOpts.value.length) {
              for (let i = 0; i < templateOpts.value.length; i++) {
                if (templateOpts.value[i].default == '是') {
                  formState.template = JSON.stringify(templateOpts.value[i])
                  console.log(formState.template, 'formState.template')
                  context.emit('paramsChange', { val: JSON.stringify(templateOpts.value[i]), type: 'template' })
                }
              }
            }
          } else {
            templateOpts.value = []
          }
        })
        .catch(err => {
          console.log(err, 'errr')
        })
    }

    const templateChange = val => {
      context.emit('paramsChange', { val: val, type: 'template' })
    }
    getTemplateSelect({
      version: formState.version,
    })
      .then(res => {
        if (res.Data.data) {
          templateOpts.value = res.Data.data
          if (templateOpts.value && templateOpts.value.length) {
            for (let i = 0; i < templateOpts.value.length; i++) {
              if (templateOpts.value[i].default == '是') {
                formState.template = JSON.stringify(templateOpts.value[i])
                console.log(formState.template, 'formState.template')
                context.emit('paramsChange', { val: JSON.stringify(templateOpts.value[i]), type: 'template' })
              }
            }
          }
        } else {
          templateOpts.value = []
        }
      })
      .catch(err => {
        console.log(err, 'errr')
      })
    watch(props.temp, (newValue, oldValue) => {
      filterMachines.value = newValue.content.filterMachines
    })
    return {
      validateForm,
      templateChange,
      versionChange,
      checkPwd,
      passwordStr,
      formRef,
      formState,
      labelCol,
      wrapperCol,
      templateOpts,
      rules,
    }
  },
}
</script>
<style lang="less" scoped></style>
