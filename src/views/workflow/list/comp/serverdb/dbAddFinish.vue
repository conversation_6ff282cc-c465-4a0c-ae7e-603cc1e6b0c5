<template>
  <!-- 配置列表 -->
  <div>
    <a-result v-if="temp.status == 10" status="success" sub-title="已完成">
      <template #extra>
        <a-button @click="showResult">查看执行结果</a-button>
      </template>
    </a-result>
    <a-result v-if="temp.status == 30" status="error" sub-title="申请人已撤回"></a-result>
    <a-result v-if="temp.status == 20" status="error" :sub-title="temp.handler + '拒绝了您的工单'"></a-result>
    <a-modal :closable="false" :keyboard="false" :width="900" v-model:visible="visible" title="执行结果">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="数据库服务器创建">
          <a-table :loading="resLoading" bordered :pagination="false" :data-source="resultTableData" :columns="columns">
            <template #bodyCell="{ column, text, record }">
              <template
                v-if="
                  column.dataIndex === 'opt' &&
                  (temp.content.serverHandler == localUser || localUser == '<EMAIL>')
                "
              >
                <a-button
                  size="small"
                  v-if="record.processStatus.includes('失败')"
                  @click="serverRetry('server', record)"
                  type="primary"
                >
                  重试
                </a-button>
                <a-button
                  size="small"
                  style="margin-left: 8px"
                  v-if="record.processStatus.includes('失败')"
                  @click="changeConfig(record)"
                  type="primary"
                >
                  变更
                </a-button>
                <a-button
                  size="small"
                  style="margin-left: 8px"
                  v-if="record.processStatus.includes('失败')"
                  @click="opt(record)"
                  type="primary"
                >
                  操作日志
                </a-button>
              </template>
              <template v-if="column.dataIndex === 'processStatus'">
                <span style="color: #52c41a" v-if="record.processStatus.includes('成功')">
                  {{ record.processStatus }}
                </span>
                <span style="color: red" v-else-if="record.processStatus.includes('失败')">
                  {{ record.processStatus }}
                </span>
                <span style="color: blue" v-else>{{ record.processStatus }}</span>
              </template>
            </template>
          </a-table>
          <a-card v-if="changeVis" title="重新分配物理机">
            <p>
              <span style="margin-left: 16px">请分配一台物理机：</span>
              <a-tag v-for="i in allocationData">{{ i.ip }}</a-tag>
            </p>
            <p>
              <span style="margin-left: 16px; line-height: 32px; display: inline-block; height: 100%">名称/IP：</span>
              <a-input-search
                style="width: 200px"
                v-model:value="searchValue"
                placeholder="输入名称或IP搜索"
                enter-button
                @search="$emit('searchMachine', searchValue)"
              />
            </p>
            <a-table
              bordered
              :scroll="{ y: 200 }"
              :pagination="false"
              :data-source="filterMachines"
              :columns="serverListColum"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'opt'">
                  <a
                    v-if="record.ip == allocationData[0].ip"
                    @click="removeList(record)"
                    style="color: red; font-size: 12px"
                  >
                    从物理机列表删除
                  </a>
                  <a v-else @click="addToList(record)" style="color: green; font-size: 12px">添加至物理机列表</a>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-tab-pane>

        <a-tab-pane key="2" tab="数据库初始化" force-render>
          <div style="display: flex; justify-content: flex-start; flex-wrap: wrap; color: #9e9e9e">
            <p style="width: 30%">
              <span>数据库版本：</span>
              {{ temp.content.version }}
            </p>
            <p v-if="temp.content.application.includes('mysql')" style="width: 30%">
              <span>Mysql参数模板：</span>
              {{ temp.content.template.name }}
            </p>
            <p v-if="temp.content.application.includes('redis')" style="width: 30%">
              <span>线程数：</span>
              {{ temp.content.thread }}
            </p>
            <p v-if="temp.content.application.includes('redis')" style="width: 30%">
              <span>最大内存限制:</span>
              {{ temp.content.maxMemory }}
            </p>
            <p v-if="temp.content.application.includes('redis')" style="width: 30%">
              <span>内存淘汰策略:</span>
              {{ temp.content.memoryObsolescence }}
            </p>
            <p v-if="temp.content.application.includes('redis')" style="width: 30%">
              <span>持久化策略:</span>
              {{ temp.content.persistence }}
            </p>
            <p v-if="temp.content.application.includes('redis')" style="width: 30%">
              <span>架构类型:</span>
              {{ temp.content.architectureType }}
              <span v-if="temp.content.architectureType == 'cluster'">副本数量:{{ temp.content.duplicateNum }}</span>
            </p>
            <p v-if="temp.content.highAvailability" style="width: 30%">
              <span>VIP:</span>
              {{ temp.content.vip }}
            </p>
          </div>
          <p>执行结果</p>

          <a-table :loading="resLoading" bordered :pagination="false" :data-source="dbResultData" :columns="columns">
            <template #bodyCell="{ column, text, record }">
              <template
                v-if="column.dataIndex === 'opt' && (temp.content.dbHandler == localUser || !temp.content.dbHandler)"
              >
                <a-button
                  size="small"
                  v-if="record.processStatus.includes('失败')"
                  @click="serverRetry('db', record)"
                  type="primary"
                >
                  重试
                </a-button>
              </template>
              <template v-if="column.dataIndex === 'processStatus'">
                <span style="color: #52c41a" v-if="record.processStatus.includes('成功')">
                  {{ record.processStatus }}
                </span>
                <span style="color: red" v-else-if="record.processStatus.includes('失败')">
                  {{ record.processStatus }}
                </span>
                <span style="color: blue" v-else>{{ record.processStatus }}</span>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>

      <template #footer>
        <a-button :loading="loading" @click="visible = false">关闭</a-button>
        <a-button v-if="changeVis" key="submit" type="primary" :loading="loading" @click="handleOk">确定变更</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { serverResult, dbResult, reTry, dbServerreTry, dbInitreTry } from '@/api/workflow/asset_automation'
import { ref, props, watch } from 'vue'
import { reactive } from 'vue'
import { notification } from 'ant-design-vue'
import user from '@/store/modules/user'
const columns = [
  {
    title: '服务器名',
    dataIndex: 'hostname',
    key: 'hostname',
  },
  {
    title: '物理机IP',
    dataIndex: 'hostIp',
    key: 'hostIp',
  },
  {
    title: '云主机IP',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '状态',
    dataIndex: 'processStatus',
    key: 'processStatus',
  },
  {
    title: '操作',
    dataIndex: 'opt',
    key: 'opt',
  },
]

const serverListColum = [
  {
    title: '服务器名',
    dataIndex: 'hostname',
    key: 'hostname',
    width: 120,
  },
  {
    title: 'ip',
    dataIndex: 'ip',
    key: 'ip',
    width: 120,
  },
  {
    title: '业务',
    dataIndex: 'business',
    key: 'business',
  },

  {
    title: 'CPU分配率/可用内存',
    dataIndex: 'cpuOversoldRate',
    key: 'cpuOversoldRate',
  },
  {
    title: '内存分配率/可用内存',
    dataIndex: 'memOversoldRate',
    key: 'memOversoldRate',
  },
  {
    title: '系统盘分配率/可用内存',
    dataIndex: 'diskRootUsage',
    key: 'diskRootUsage',
  },
  {
    title: '操作',
    dataIndex: 'opt',
    key: 'opt',
    width: 130,
  },
]
const dataSource = [
  {
    name: '',
    ip: '',
    business: '',
    priority: '',
    cpu_rate_mem: '',
    mem_mem_rate: '',
    disk_mem_rate: '',
  },
]
export default {
  props: ['temp'],
  setup(props, ctx) {
    console.log(props.temp.content, 'props.temp.content')

    const activeKey = ref('1')
    const hidsInit = ref(props.temp.content.hidsInit)
    const needInit = ref(props.temp.content.needInit)
    let visible = ref(false)
    let allocationData = reactive([])
    let filterMachines = ref([])
    let resLoading = ref(true)
    let resultTableData = ref([])
    let dbResultData = ref([])
    let targetRecord = ref()
    const changeVis = ref(false)
    let localUser = ref(user.state.email)
    const reAllocation = () => {
      visible.value = true
    }
    // 查看执行结果
    const getResult = async () => {
      let res = null
      await serverResult({
        orderId: props.temp.id,
      }).then(r => {
        res = r
      })
      return res
    }
    // 查看执行结果
    const getDbResult = async () => {
      let res = null
      await dbResult({
        orderId: props.temp.id,
      }).then(r => {
        res = r
      })
      return res
    }
    const showResult = () => {
      changeVis.value = false
      visible.value = true
      getResult()
        .then(r => {
          resultTableData.value = r.Data.data
          if (resultTableData.value == null) {
            notification.warning({
              message: '提示',
              description: '结果请求中，请三十秒后刷新',
            })
          }
          resLoading.value = false
        })
        .catch(() => {
          notification.error({
            message: '错误',
            description: '请求失败，请联系管理员',
          })
        })
      getDbResult().then(t => {
        dbResultData.value = t.Data.data
        console.log(dbResultData.value, 'vvvvvvvvv')
      })
    }
    // 重试
    const serverRetry = (type, record) => {
      resLoading.value = true
      if (type == 'server') {
        changeVis.value = false
        let targetMachine = {}
        props.temp.content.allocationList.forEach(item => {
          if (item.ip == record.hostIp) {
            targetMachine = item
          }
        })
        dbServerreTry({
          idc: props.temp.content.idc,
          orderId: props.temp.id, // 工单Id
          processId: record.processId, //任务id
          hostname: record.hostname, // 服务器名
          cpuNum: record.cpuNum, // cpu
          memNum: record.memNum, //内存
          vlan: record.vlan,
          os: record.os,
          hostIp: record.hostIp, // 物理机ip
          path: targetMachine.path, // 磁盘路径
          start: targetMachine.start, // 磁盘开始
          end: targetMachine.end, // 磁盘结束
        })
          .then(res => {
            console.log(res, 'retryRes')
          })
          .finally(() => {
            console.log(11111111111111111)
            resLoading.value = false
            // visible.value = false
            record.processStatus = '处理中'
          })
      } else if (type == 'db') {
        changeVis.value = false
        let targetMachine = {}
        props.temp.content.allocationList.forEach(item => {
          if (item.ip == record.hostIp) {
            targetMachine = item
          }
        })
        dbInitreTry({
          orderId: props.temp.id, // 工单Id
          processId: record.processId, //任务id
        })
          .then(res => {
            console.log(res, 'retryRes')
          })
          .finally(() => {
            resLoading.value = false
            // visible.value = false
            record.processStatus = '处理中'
          })
      }
    }
    // 手动分配物理机ok
    const handleOk = () => {
      // console.log(111111)
      if (allocationData.length != 1) {
        notification.warning({
          message: '不符合规则',
          description: '分配物理机数量和申请服务器数量不一致',
        })
      } else {
        dbServerreTry({
          idc: props.temp.content.idc,
          orderId: props.temp.id, // 工单Id
          processId: targetRecord.value.processId, //任务id
          hostname: targetRecord.value.hostname, // 服务器名
          cpuNum: targetRecord.value.cpuNum, // cpu
          memNum: targetRecord.value.memNum, //内存
          vlan: targetRecord.value.vlan,
          os: targetRecord.value.os,
          hostIp: allocationData[0].ip, // 物理机ip
          path: allocationData[0].path, // 磁盘路径
          start: allocationData[0].start, // 磁盘开始
          end: allocationData[0].end, // 磁盘结束
        }).then(res => {
          console.log(res, 'retryRes')
          visible.value = false
          changeVis.value = false
        })
      }
    }
    // 关闭物理机Tag
    const log = e => {
      console.log(e)
    }
    const searchValue = ref('')
    const onSearch = searchValue => {
      console.log('use value', searchValue)
      console.log('or use this.value', value.value)
    }
    // 变更
    const changeConfig = async record => {
      targetRecord.value = record
      allocationData.pop()
      allocationData.push({ ...record, ip: record.hostIp })
      await ctx.emit('getMachines')
      filterMachines.value = props.temp.content.filterMachines
      console.log(filterMachines.value, 'filterMachines')
      setTimeout(() => {
        changeVis.value = !changeVis.value
      })
    }
    //  移除物理机
    const removeList = r => {
      allocationData.pop()
      allocationData.push({})
    }
    //添加物理机
    const addToList = r => {
      console.log(r, 'rrr')
      allocationData.pop()
      allocationData.push(r)
      console.log(allocationData, ' allocationData')
    }

    // 操作日志
    const opt = r => {
      console.log(r)
      notification.warning({
        message: '开发种',
        description: '功能开发中',
      })
    }
    watch(props.temp, (newValue, oldValue) => {
      console.log(newValue, 'newValue')
      filterMachines.value = newValue.content.filterMachines
    })
    return {
      activeKey,
      opt,
      resLoading,
      addToList,
      removeList,
      changeConfig,
      serverRetry,
      changeVis,
      localUser,
      showResult,
      resultTableData,
      dbResultData,
      filterMachines,
      allocationData,
      visible,
      handleOk,
      columns,
      hidsInit,
      needInit,
      log,
      reAllocation,
      serverListColum,
      dataSource,
      searchValue,
      onSearch,
    }
  },
}
</script>

<style lang="less" scoped>
.customCard {
  width: 100%;
  margin-top: 14px;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  background-color: #fff;

  .customTitle {
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    background: transparent;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
    margin-bottom: -1px;
    min-height: 48px;
    display: inline-block;
    flex: 1;
    overflow: hidden;
    padding: 16px 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .customContent {
    padding: 24px;
  }
}

.itemTitle {
  display: inline-block;
  width: 130px;
  height: 100%;
}

.itemColor {
  color: #9e9e9e;
}

.itemContent {
  display: inline-block;
  margin-left: 40px;
}
</style>
