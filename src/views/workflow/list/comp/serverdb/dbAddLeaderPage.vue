<template>
    <!-- 配置列表 -->
    <div>
        <ul style="padding:0">
            <li>
                <p class="itemTitle itemColor">主机名:</p>
                <a-input style="width: 250px;display: inline-block" v-model:value="customHostName"></a-input>
                <!-- <p class="itemContent">{{ temp.founder }}</p> -->
            </li>
            <li>
                <p class="itemTitle itemColor">是否初始化:</p>
                <a-radio-group @change="$emit('radioChange', 'needInit', needInit)" v-model:value="needInit">
                    <a-radio :value="false" style="margin-right: 16px;">否</a-radio>
                    <a-radio :value="true">是</a-radio>
                </a-radio-group>
                <!-- <p class="itemContent">{{ temp.founder }}</p> -->
            </li>
            <li>
                <p class="itemTitle itemColor">是否安装HIDS:</p>
                <a-radio-group :disabled="true" @change="$emit('radioChange', 'hidsInit', hidsInit)"
                    v-model:value="hidsInit">
                    <a-radio style="margin-right: 16px;" :value="false">否</a-radio>
                    <a-radio :value="true">是</a-radio>
                </a-radio-group>
            </li>
            <li>
                <p class="itemTitle itemColor">物理机分配信息:</p>
                <span @click="reAllocation" style="cursor: pointer; color: cornflowerblue;">手动重新分配</span>
                <a-table :columns="columns" :pagination="false" :data-source="allocationData">
                    <template #expandedRowRender="{ record }">
                        <p style="margin: 0">
                        <p><span>物理机名：</span>{{ record.hostname }}</p>
                        <p><span>物理机ip：</span>{{ record.ip }}</p>
                        <p><span>CPU分配率：</span>{{ record.cpuOversoldRate }}</p>
                        <p><span>内存分配率：</span>{{ record.memOversoldRate }}</p>
                        <p><span>系统盘分配率：</span>{{ record.diskRootUsage }}</p>
                        </p>
                    </template>
                </a-table>
            </li>
        </ul>
        <a-modal :maskClosable="false" :closable="false" :keyboard="false" :width="1200" v-model:visible="visible"
            title="手动分配物理机">
            <p>
                <span style="margin-left: 16px;">请分配&nbsp;{{ temp.content.num }}&nbsp;台物理机： </span>
                <a-tag closable v-for="i in allocationData" @close.prevent="tagClose(i)">{{ i.ip
                    }}</a-tag>
            </p>
            <p>
                <span style="margin-left: 16px;line-height: 32px;display: inline-block;height: 100%;">名称/IP：
                </span>
                <a-input-search style="width: 200px" v-model:value="searchValue" placeholder="输入名称或IP搜索" enter-button
                    @search="$emit('searchMachine', searchValue)" />
            </p>
            <a-table :scroll="{ y: 300 }" bordered :pagination="false" :data-source="filterMachines"
                :columns="serverListColum">
                <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex === 'opt'">
                        <a-popconfirm v-if="dataSource.length" title="确定删除吗？"
                            @confirm="$emit('handlerMachine', 'delete', record)">
                            <a v-if="record.allocated" style="color: red;font-size: 12px;">从物理机列表删除</a>
                        </a-popconfirm>
                        <a v-if="!record.allocated" @click="$emit('handlerMachine', 'add', record)"
                            style="color: green;font-size: 12px;">添加至物理机列表</a>
                    </template>
                </template>
            </a-table>
            <template #footer>
                <a-button type="primary" @click="handleOk">确认</a-button>
            </template>
        </a-modal>
    </div>
</template>

<script>
import { allocationHost } from '@/api/workflow/asset_automation'
import { defineComponent, ref, reactive, props, watch } from 'vue';
import { notification } from 'ant-design-vue'
import setting from '@/locales/lang/en-US/setting';

const columns = [{
    title: '服务器名',
    dataIndex: 'hostname',
    key: 'hostname',
}, {
    title: '物理机IP',
    dataIndex: 'ip',
    key: 'ip',
},];
const serverListColum = [{
    title: '服务器名',
    dataIndex: 'hostname',
    key: 'hostname',
    width: 120,
},
{
    title: 'ip',
    dataIndex: 'ip',
    key: 'ip',
    width: 100,

}, {
    title: '业务',
    dataIndex: 'business',
    key: 'business',
},
{
    title: 'CPU分配率/可用内存',
    dataIndex: 'cpuOversoldRate',
    key: 'cpuOversoldRate',
},
{
    title: '内存分配率/可用内存',
    dataIndex: 'memOversoldRate',
    key: 'memOversoldRate',
}, {
    title: '系统盘分配率/可用内存',
    dataIndex: 'diskRootUsage',
    key: 'diskRootUsage',
},
{
    title: '操作',
    dataIndex: 'opt',
    key: 'opt',
    width: 130
},
];

const dataSource = [
    {
        name: '',
        ip: '',
        business: '',
        priority: '',
        cpu_rate_mem: '',
        mem_mem_rate: '',
        disk_mem_rate: ''
    }
]
export default defineComponent({
    props: ['temp'],
    setup(props, ctx) {
        const customHostName = ref(props.temp.content.projectName)
        console.log(props.temp, 'ttttttttttt');
        console.log(props.temp.content.needPreImageIp, 'needPreImageIp');
        const hidsInit = ref(props.temp.content.hidsInit)
        const needInit = ref(props.temp.content.needInit)
        let visible = ref(false)
        let allocationData = ref(props.temp.content.allocationList)
        let filterMachines = ref(props.temp.content.filterMachines)
        const reAllocation = async () => {
            visible.value = true
            ctx.emit('getMachines')
        }
        // 手动分配物理机ok
        const handleOk = () => {
            console.log(allocationData.value,'vvv')
            console.log( props.temp.content.num,'numnumnum')
            if (props.temp.content.hostAffine) {
                visible.value = false
            } else {
                if (allocationData.value.length != props.temp.content.num) {
                    notification.warning({
                        message: '不符合规则',
                        description: '分配物理机数量和申请服务器数量不一致',
                    })
                } else {
                    visible.value = false
                }
            }
        }
        // 关闭物理机Tag
        const log = e => {
        };
        const searchValue = ref('')
        const onSearch = searchValue => {
        };

        watch(props.temp, (newValue, oldValue) => {
            console.log('watch 已触发', newValue)
            filterMachines.value = newValue.content.filterMachines
            allocationData.value = newValue.content.allocationList
            console.log(allocationData.value, 'allocationData');
        })
        const tagClose = (i)=>{
            console.log(arguments,'arg');
            // console.log($event,'arg');
            setTimeout(() => {
                ctx.emit('handlerMachine', 'delete', i)
            });
        }
        return {

            tagClose,
            customHostName,
            filterMachines,
            allocationData,
            visible,
            handleOk,
            columns,
            hidsInit,
            needInit,
            log,
            reAllocation,
            serverListColum,
            dataSource,
            searchValue,
            onSearch
        };
    },
});
</script>

<style lang="less" scoped>
.customCard {
    width: 100%;
    margin-top: 14px;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    background-color: #fff;

    .customTitle {
        width: 100%;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        background: transparent;
        border-bottom: 1px solid #f0f0f0;
        border-radius: 2px 2px 0 0;
        margin-bottom: -1px;
        min-height: 48px;
        display: inline-block;
        flex: 1;
        overflow: hidden;
        padding: 16px 24px;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .customContent {
        padding: 24px;
    }
}

.itemTitle {
    display: inline-block;
    width: 130px;
    height: 100%
}

.itemColor {
    color: #9e9e9e;
}

.itemContent {
    display: inline-block;
    margin-left: 40px;
}
</style>