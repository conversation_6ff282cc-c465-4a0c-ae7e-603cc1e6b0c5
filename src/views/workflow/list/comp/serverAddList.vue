<template>
    <!-- 配置清单 -->
    <div class="setingInfo" :style="{ height: height }">
        <ul style="padding: 0;margin: 0;">
            <li style="margin-bottom: 8px;" v-if="showTips">
                <a-alert
                    v-if="temp.content.allocationList.length && ((temp.content.allocationList.length == temp.content.num&&!temp.content.hostAffine)||temp.content.hostAffine)"
                    message="宿主机预分配成功." type="success" show-icon />
                <a-alert v-else message="宿主机预分配失败，请联系运维管理员处理." type="error" show-icon />
            </li>
            <li>
                <p class="itemTitle">申请人</p>
                <p class="itemContent">{{ temp.founder }}</p>
            </li>
            <li>
                <p class="itemTitle">机房</p>
                <p class="itemContent">{{ temp.content.idc }}</p>
            </li>
            <li>
                <p class="itemTitle">CPU/内存</p>
                <p v-if="JSON.parse(temp.content.serverType).length" class="itemContent">
                    {{ JSON.parse(temp.content.serverType)[0] }}核/{{ JSON.parse(temp.content.serverType)[1] }}G
                </p>
            </li>
            <li>
                <p class="itemTitle">镜像</p>
                <p class="itemContent" v-if="!temp.content.preImageIp && !temp.content.os"><span
                        class="tips">必填项,不能为空</span></p>
                <p class="itemContent">{{ temp.content.needPreImageIp && temp.content.preImageIp ?
        JSON.parse(temp.content.preImageIp).ip :
        temp.content.os }}</p>
            </li>
            <li>
                <p class="itemTitle">数量</p>
                <p class="itemContent">{{ temp.content.num }}</p>
            </li>
            <li>
                <p class="itemTitle">申请理由</p>
                <p class="itemContent" v-if="temp.content.reason.length === 0"><span class="tips">必填项,不能为空</span></p>
                <p class="itemContent" v-else>{{ temp.content.reason }}</p>
            </li>
            <li>
                <p class="itemTitle">系统盘</p>
                <p class="itemContent">{{ temp.content.systemDiskSize }}GB</p>
            </li>
            <li v-if="temp.content.needOtherDisks">
                <p class="itemTitle">数据盘</p>
                <p class="itemContent">{{ temp.content.diskType }}/{{ temp.content.diskSize }}GB</p>
            </li>
            <li v-if="temp.content.principal.length > 0" style="display:flex">
                <p class="itemTitle">其他负责人</p>
                <p class="itemContent" v-if="temp.content.principal.length === 1">{{ temp.content.principal[0] }}</p>
                <p class="itemContent" v-else>
                  <p v-for="i in temp.content.principal">
                      {{ i }}
                  </p>
                </p>
            </li>
            <li>
                <p class="itemTitle">环境</p>
                <p class="itemContent">{{ temp.content.env }}</p>
            </li>
            <li>
                <p class="itemTitle">项目</p>
                <p class="itemContent" v-if="temp.content.projects.length === 0"><span class="tips">必填项,不能为空</span></p>
                <p class="itemContent" v-else>{{ temp.content.projects[0] }}</p>
            </li>
            <li style="display:flex">
                <p class="itemTitle">应用</p>
                <p class="itemContent" v-if="temp.content.application.length === 1">
                  {{ temp.content.application[0] }}
                </p>
                <p class="itemContent" v-else-if="temp.content.application.length > 1">
                  <p v-for="i in temp.content.application">
                    {{ i }}
                  </p>
                </p>
                <p class="itemContent" v-else><span class="tips">必填项,不能为空</span></p>
            </li>
            <li>
                <p class="itemTitle">费用负责人</p>
                <p class="itemContent">{{ temp.content.costUser }}</p>
            </li>
            <li>
                <p class="itemTitle">事业部/部门/团队</p>
                <p class="itemContent" v-if="temp.content.arrorg.length">
                    {{ temp.content.arrorg }}
                    <!-- <span v-for="(i, index) in temp.content.arrorg "> {{ i }} <span
                  v-if="index < temp.content.arrorg.length - 1">/</span> </span> -->
                </p>
            </li>
            <li>
                <p class="itemTitle">费用承担业务方</p>
                <p class="itemContent">{{ temp.content.business }}</p>
            </li>
            <li>
                <p class="itemTitle">费用类型</p>
                <p class="itemContent" >{{ temp.content.costAttr }}</p>
            </li>
          <li
            v-if="(temp.content.costAttr === '运营成本' && temp.content.business !== '大数据' && temp.content.business !== '搜索(DG)') || temp.content.costAttr === '开发支出'"
            style="display:flex">
                <p class="itemTitle">产品线/承担比例</p>
                <p v-if="temp.content.productLineListPercent&&temp.content.productLineListPercent.length && temp.content.productLineListPercent[0].productLineNameList[0] && temp.content.productLineListPercent[0].productLineNameList[1]"
                    class="itemContent">
                <p v-for="i in temp.content.productLineListPercent">
                    {{ i.productLineNameList[0] }}/{{ i.productLineNameList[1] }}:{{ i.ratio }}%
                </p>
                </p>
                <p class="itemContent" v-else><span class="tips">必填项,不能为空</span></p>
            </li>
            <li v-if="temp.content.business === 'DG'">
                <p class="itemTitle">是否属于DG资产</p>
                <p class="itemContent">{{ temp.content.isDgDataAsset ? '属于' : '不属于' }}</p>
            </li>
            <li v-if="temp.content.isDgDataAsset">
                <p class="itemTitle">DG数据资产类别</p>
                <p class="itemContent" v-if="temp.content.dgDataAssetClassification">{{
        temp.content.dgDataAssetClassification }}</p>
                <p class="itemContent" v-else><span class="tips">必填项,不能为空</span></p>
            </li>
            <li>
                <p class="itemTitle">访问权限</p>
                <p class="itemContent" v-if="temp.content.permissionType != 2">{{
        ['普通权限', 'sudo权限', '其他权限'][temp.content.permissionType]
    }}</p>
                <p class="itemContent" v-else>{{ temp.content.permissionDescription }}</p>
            </li>
            <li>
                <p class="itemTitle">主机名</p>
                <p class="itemContent" v-if="temp.content.projectName.length === 0"><span class="tips">必填项,不能为空</span>
                </p>
                <p class="itemContent">{{ temp.content.projectName }}</p>
            </li>
            <li>
                <p class="itemTitle">宿主亲和性</p>
                <p class="itemContent" v-if="temp.content.hostAffine">亲和(
                  <span v-for="i in temp.content.hostAffineArr">
                    {{ i }},
                  </span>
                  )
                </p>
              <p class="itemContent" v-else>互斥(
                <span v-for="i in temp.content.unHostAffine">
                  {{ i }},
                </span>
                )
              </p>
            </li>
            <li>
                <p class="itemTitle">Vlan网段ID</p>
                <p class="itemContent" v-if="temp.content.vlan.length === 0"><span
                        class="tips">必填项,不能为空</span></p>
                <p class="itemContent">{{ temp.content.vlan }}</p>
            </li>
        </ul>
    </div>
</template>

<script>

export default {
    props: {
        temp: {
            type: Object,
            default: () => {
                return {
                    id: '',
                    orderType: '服务器创建',
                    node: 0,
                    status: 0,
                    founderEmail: user.state.email,
                    founder: user.state.name,
                    time_now: '',
                    handler: '',
                    handlerEmail: '',
                    content: {
                        dep: undefined, // 部门
                        ip: undefined,
                        hostname: undefined,
                        idc: 'ucloud-shanghai2-hybrid',
                        serverType: ['1', '2'],
                        num: 1,
                        os: ['linux', 'centos7'],
                        principal: [], // 其他负责人
                        org: undefined,
                        project: undefined,
                        projects: [],
                        resultIpList: [],
                        env: 'online',
                        application: [],
                        projectName: '',
                        costUser: '',
                        organization: [],
                        costAttr: '运营成本',
                        productLineListPercent: [
                            {
                                productLineNameList: [],
                                ratio: 0,
                            },
                        ],
                        isDgDataAsset: false,
                        isDgBusiness: false,
                        dgDataAssetClassification: '',
                        dgSpecificBusiness: '',
                        needOtherDisks: false,
                        preInstanceType: '',
                        diskType: 'HDD',
                        diskSize: 0, //额外盘
                        systemDiskSize: 40, //系统盘
                        systemDiskType: '',
                        preImageIp: '',
                        permissionType: '1',
                        permissionDescription: '',
                        needPreImageIp: false,
                        isDb: false,
                        isAutomation: false,
                        hostAffine: false,//宿主亲和性 默认互斥
                        reason: '',
                        // aliyun
                        // aws
                        needPublicIp: 0,
                        approvalData: {
                            projectName: '', // 同步自 content.projectName，为了通过 rules 验证
                            keys: [],
                            needInit: 0,
                            isDesktop: 0,
                            hidsInit: 0,
                            vlan: 1,
                            hostIpList: [],
                            instanceType: [],
                            defaultImageId: '',
                            defaultSubnetId: [],
                            defaultSecurityGroupList: [],
                            defaultVpcSwitchList: [],
                            defaultVpcId: '',
                            defaultZoneId: '',
                            zoneIdList: [],
                            roleName: '',
                        },
                    },
                    timeline: [],
                    comment: '',
                }
            }
        },
        showTips: {
            type: Boolean,
            default: () => {
                return true
            }
        },
        height: {
            type: String,
            default: () => {
                return 'auto'
            }
        }
    },
    data() {
        return {}
    },
    created() {
    },
    mounted(){
        console.log(this.temp,'tttttttttttttt')
    },
    watch: {},
    methods: {},
}
</script>

<style lang="less" scoped>
p {
    font-size: 14px;
}

.itemTitle {
    display: inline-block;
    text-align: left;
    font-weight: bolder;
    width: 150px;
    height: 100%;
    margin-bottom: 6px;

}

.tips {
    color: red;
}

.itemContent {
    display: inline-block;
    margin-bottom: 6px;
}

.setingInfo {}
</style>
