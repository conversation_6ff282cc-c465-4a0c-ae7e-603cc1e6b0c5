<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="temp.content.dbType == 'clickhouse' ? rules1 : rules2"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="类型">
          <a-radio-group
            name="radioGroup"
            @change="radioGroupChang"
            button-style="solid"
            v-model:value="temp.content.orderType"
          >
            <a-radio-button value="add">新增</a-radio-button>
            <a-radio-button value="modify">修改</a-radio-button>
            <a-radio-button value="delete">删除</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="数据库类型">
          <a-radio-group
            name="dbradioGroup"
            default-value="clickhouse"
            button-style="solid"
            v-model:value="temp.content.dbType"
            @change="dbRadioGroupChang"
          >
            <a-radio-button value="clickhouse">ClickHouse</a-radio-button>
            <a-radio-button value="mysql">Mysql</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="报警名称" v-if="temp.content.orderType === 'add'" name="alertName">
          <a-input
            v-model:value="temp.content.alertName"
            placeholder="后续不可修改"
            style="width: 400px"
            @change="alterCheck"
          />
        </a-form-model-item>
        <a-form-model-item label="报警名称" v-else>
          <a-select
            v-model:value="temp.content.alertId"
            placeholder="选择报警名称"
            allowClear
            :showSearch="true"
            :filter-option="filterOption"
            style="width: 400px"
            @change="alertNameSelectChange"
          >
            <a-select-option v-for="item in alertNameList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="业务名">
          <a-select v-model:value="temp.content.bussiness" placeholder="默认用户业务" allowClear style="width: 400px">
            <a-select-option v-for="item in bussinessList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <div v-if="temp.content.dbType === 'clickhouse'">
          <a-form-model-item label="区域" name="idc">
            <a-select v-model:value="temp.content.idc" allowClear style="width: 400px">
              <a-select-option v-for="item in dbIdcList" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="数据库名" name="dbName">
            <a-select
              placeholder="输入选择"
              v-model:value="temp.content.dbName"
              :showSearch="true"
              :allowClear="true"
              style="width: 400px"
              @search="alertQuerySearchDbName"
            >
              <a-select-option v-for="item in dbNameList" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
        <div v-else>
          <a-form-model-item label="数据库" name="host">
            <a-select
              v-model:value="temp.content.host"
              placeholder="请搜索并选择数据库"
              :showSearch="true"
              :allowClear="true"
              :filter-option="false"
              style="width: 400px"
              @search="searchmysqlIpMethod"
            >
              <a-select-option v-for="item1 in IpDbNamesList" :key="item1" :value="item1">{{ item1 }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
        <a-form-model-item label="SQL语句" name="sqlContent">
          <a-textarea v-model:value="temp.content.sqlContent" :autoSize="{ minRows: 5, maxRows: 20 }" />
        </a-form-model-item>
        <a-form-model-item label="告警人" name="contact">
          <a-select
            placeholder="<EMAIL>"
            v-model:value="temp.content.contact"
            mode="multiple"
            style="width: 400px"
            :showSearch="true"
            :allowClear="true"
            @search="searchUserEmailMethod"
          >
            <a-select-option v-for="item in userEmailList" :key="item" :value="item">{{ item }}</a-select-option>
          </a-select>
          <a-tooltip>
            <template #title>默认为告警通知人，也是修改或删除时的负责人。</template>
            <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="报警消息" name="message">
          <a-textarea v-model:value="temp.content.message" placeholder="自定义内容" />
        </a-form-model-item>
        <a-form-model-item label="告警类型" name="alterType">
          <a-select v-model:value="temp.content.alterType" style="width: 400px">
            <a-select-option v-for="item in alterTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="告警机器人key" v-if="temp.content.alterType === 'webhook_key'">
          <a-input v-model:value="temp.content.webhookKey" style="width: 400px" placeholder="自定义内容" />
          <a-tooltip>
            <template #title>创建完成后的机器人所对应的Webhook地址中的key</template>
            <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="webhook地址" v-if="temp.content.alterType === 'dbwebhook'">
          <a-input v-model:value="temp.content.webhookKey" style="width: 400px" placeholder="webhook url" />
          <a-tooltip>
            <template #title>自定义webhook 请求 post方法 body 传参</template>
            <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="间隔时间" name="taskTime">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-input-number v-model:value="temp.content.taskTime" placeholder="10" />
            </a-col>
            <a-col :span="6">
              <a-select v-model:value="temp.content.taskTimeType">
                <a-select-option v-for="item in taskTimeTypeList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/评论">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { alterNameCheck, getAlterInfo, getAlterPlate, getBussiness, getDbInfo } from '@/api/db/alter_plate'
import { getUserList } from '@/api/permission/user'
import { getDbNamesList } from '@/api/db/dbApproval'
import { filterLabelValue } from '@aim/helper'

export default {
  name: 'DbAlter',
  data: function () {
    return {
      fetching: false,
      userEmailList: [],
      value: [],
      confirmLoading: false,
      alertNameCheck: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      dbIdcList: ['ali-hangzhou-qxb', 'aws-cn-northwest-1', 'aws-us-west-1', 'ucloud-shanghai2-hybrid'],
      taskTimeTypeList: [
        { label: '分', value: 'minutes' },
        { label: '时', value: 'hours' },
        { label: '天', value: 'days' },
      ],
      alterTypeList: [
        { label: 'AlertPlus+', value: 'alertPlus' },
        { label: '企业微信群', value: 'webhook_key' },
        { label: '电话', value: 'tellPhone' },
        { label: '自定义Webhook', value: 'dbwebhook' },
      ],
      userKeyList: [],
      bussinessList: [],
      dbNameList: [],
      IpDbNamesList: [],
      alertNameList: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      checkPaaswd: '',
      temp: {
        id: '',
        orderType: '数据库报警配置',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          orderType: 'add',
          dbType: 'clickhouse',
          alterType: 'alertPlus',
          id: undefined,
          taskTime: undefined,
          dbName: undefined,
          taskTimeType: 'minutes',
          alertName: undefined,
          host: undefined,
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules1: antdFormRulesFormat({
        'content.alertName': [{ required: true, message: '请输入报警名称' }],
        'content.idc': [{ required: true, message: '请输入ck区域' }],
        'content.dbName': [{ required: true, message: '请输入数据库名选择' }],
        'content.sqlContent': [{ required: true, message: '请输入sql语句, 以分号结尾' }],
        'content.contact': [{ required: true, message: '请输入用户邮箱选择告警人, 支持多人' }],
        'content.message': [{ required: true, message: '请输入报警消息' }],
        'content.taskTime': [{ required: true, message: '请输入时间' }],
        'content.reason': [{ required: true, message: '请填写申请理由' }],
      }),
      rules2: antdFormRulesFormat({
        'content.alertName': [{ required: true, message: '请输入报警名称' }],
        'content.host': [{ required: true, message: '请输入数据库名，选择对应数据库' }],
        'content.sqlContent': [{ required: true, message: '请输入sql语句, 以分号结尾' }],
        'content.contact': [{ required: true, message: '请输入用户邮箱选择告警人, 支持多人' }],
        'content.message': [{ required: true, message: '请输入报警消息' }],
        'content.taskTime': [{ required: true, message: '请输入时间' }],
        'content.reason': [{ required: true, message: '请填写申请理由' }],
      }),
      dbList: [],
    }
  },
  created() {
    this.GetBussiness()
    this.getUserAlterList()
    this.getInfo()
  },
  methods: {
    filterOption: filterLabelValue,

    GetBussiness() {
      this.bussinessList = []
      getBussiness().then(response => {
        this.bussinessList = response.Data.bussiness
      })
    },
    getUserAlterList() {
      let requestParameters = {
        pageNo: 1,
        pageSize: 10000,
      }
      getAlterPlate(requestParameters).then(res => {
        if (res.Data.hasOwnProperty('data')) {
          if (res.Data.data === null || res.Data.data.length <= 0) {
            this.alertNameList = []
          } else {
            for (let i = 0; i < res.Data.data.length; i++) {
              let obj = {
                label: res.Data.data[i].alertName,
                value: res.Data.data[i].id,
              }
              this.alertNameList.push(obj)
            }
            return res.Data
          }
        }
      })
    },
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },
    alertQuerySearchDbName(searchText) {
      this.dbNameList = []
      if (searchText !== '') {
        getDbInfo({ searchText: searchText, dataType: 'ckDbName' }).then(response => {
          this.dbNameList = response.Data.data
        })
      }
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      } else {
        this.userEmailList = []
        getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
          let arry = response.Data.data
          for (let i = 0; i < arry.length; i++) {
            if (this.userEmailList.indexOf(arry[i].email) === -1) {
              this.userEmailList.push(arry[i].email)
            }
          }
        })
      }
    },
    searchmysqlIpMethod(search) {
      this.IpDbNamesList = []
      if (search !== '' && search !== 'Rex-') {
        getDbNamesList({ searchText: search }).then(response => {
          this.IpDbNamesList = response.Data.ipdbName
        })
      }
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.content.orderType === 'delete') {
              let obj = {
                label: this.temp.content.alertName,
                value: this.temp.content.id,
              }
              this.alertNameList.push(obj)
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    dbRadioGroupChang() {
      if (this.temp.content.dbType === 'clickhouse') {
        this.temp.content.idc = ''
        this.temp.content.dbName = ''
      } else {
        this.temp.content.host = ''
      }
    },
    radioGroupChang(value) {
      this.temp.content = {
        orderType: this.temp.content.orderType,
        dbType: 'clickhouse',
        alterType: 'alertPlus',
        id: undefined,
        taskTimeType: 'days',
        alertName: undefined,
        host: undefined,
        reason: '',
      }
    },
    alertNameSelectChange(value) {
      getAlterInfo({ id: value }).then(response => {
        let orderType = this.temp.content.orderType
        this.temp.content = response.Data.dbAlter
        this.temp.content.orderType = orderType
        this.temp.content.alertId = response.Data.dbAlter.id
        if (this.temp.content.dbType === 'mysql') {
          this.temp.content.host = response.Data.dbAlter.host + '---' + response.Data.dbAlter.dbName
        }
      })
    },
    alterCheck() {
      if (this.temp.content.orderType === 'add') {
        alterNameCheck({ alertName: this.temp.content.alertName }).then(response => {
          if (response.Data.message === 'ok') {
            notification.error({
              message: '报警名称已存在',
              description: '请重新输入报警名称',
            })
            this.alertNameCheck = true
          } else {
            this.alertNameCheck = false
          }
        })
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.temp.content.orderType === 'add' && this.alertNameCheck) {
            notification.error({
              message: '报警名称已存在',
              description: '请重新输入报警名称',
            })
          } else {
            this.Date()
            this.node_status = 0
            this.temp.node = 1
            this.temp.status = 1
            this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
            this.temp.content = { data: JSON.stringify(this.temp.content) }
            createOrder(this.temp).then(response => {
              if (response === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else {
                notification.success({
                  message: '创建成功',
                  description: '工单创建成功',
                })
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                this.$router.push({ path: '/workflow/dbalter', query: { id: response.Data.id } })
              }
            })
          }
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.hljs {
  display: inline-block;
  padding: 0;
  background: transparent;
  vertical-align: middle;
  height: auto;
}
/deep/.d2h-diff-table {
  width: 100%;
  /* border-collapse: collapse; */
  font-family: Menlo, Consolas, monospace;
  font-size: 9px;
  line-height: normal;
}
</style>
