<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="DF产品审批" />
        <a-step title="安全审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.status < 10" :span="12">
            <a-form-model-item name="reason" label="下载内容">
              <div v-for="(assetFile, assetFileIndex) in temp.content.assetFiles" :key="assetFileIndex">
                <a-card style="width: 250%">
                  <div class="group">
                    <a-row :gutter="16">
                      <a-col :span="8">
                        <a-form-item label="IP">
                          <a-select
                            v-model:value="assetFile.ip"
                            style="width: 100%"
                            @popupScroll="handlePopupScroll"
                            @search="handleSearchIp"
                            @change="handleChangeIp"
                            placeholder="选择IP"
                            show-search
                          >
                            <a-select-option v-for="i in ipInfo" :key="i.ip">
                              {{ i.ip + '(' + i.hostname + ')' }}
                            </a-select-option>
                          </a-select>
                          <span v-if="assetFile.ip.length === 0" style="font-size: 12px; color: red; margin-left: 3px">
                            注意：仅显示自己负责的服务器！未找到服务器请找运维确认服务器负责人
                          </span>
                        </a-form-item>
                      </a-col>
                      <a-col :span="15">
                        <a-form-item label="文件路径">
                          <div v-for="(file, fileIndex) in assetFile.files" :key="fileIndex" style="display: flex">
                            <div class="file-path" style="width: 90%">
                              <a-input v-model:value="assetFile.files[fileIndex]" placeholder="文件路径" />
                            </div>
                            <div>
                              <a-button
                                v-if="fileIndex !== 0 && temp.node === 0"
                                @click="removeFilePath(assetFileIndex, fileIndex)"
                                type="danger"
                                style="margin-left: 5px"
                              >
                                删除
                              </a-button>
                            </div>
                          </div>
                          <a-button v-if="temp.node === 0" @click="addFilePath(assetFileIndex)" type="primary">
                            继续添加路径
                          </a-button>
                        </a-form-item>
                      </a-col>
                      <a-col :span="24" style="text-align: right">
                        <a-button
                          v-if="temp.node === 0 && assetFileIndex !== 0"
                          @click="removeAssetFile(assetFileIndex)"
                          type="danger"
                          style="float: right; margin-top: 16px"
                        >
                          删除
                        </a-button>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </div>
              <a-button v-if="temp.node === 0" @click="addAssetFile" type="primary" style="margin-top: 16px">
                继续添加
              </a-button>
            </a-form-model-item>
          </a-col>
          <a-col v-else :span="12">
            <a-form-model-item name="reason" label="下载内容">
              <tx-button type="link" @click="getDetail">查看详情</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，备注相关" :rows="5" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" :rows="5" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="productApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safetyApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 5 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>

    <a-modal
      :zIndex="1049"
      :visible="assetFileDownloadDetail"
      width="80%"
      title="文件下载详情"
      :closable="true"
      @cancel="assetFileDownloadDetail = false"
    >
      <a-table
        :rowKey="record => record.id"
        :columns="assetFileColumns"
        :data-source="assetFileData"
        :loading="loading"
        height="350"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'status'">
            <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a v-if="record.status === 1" @click="handleDownload(record)">下载</a>
            <a v-if="record.status === 2" @click="handleRetry(record)">重试</a>
          </template>
        </template>
      </a-table>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getAllAssetsIp } from '@/api/workflow/asset_automation'
import cloneDeep from 'lodash.clonedeep'
import { downloadAssetFileTaskRetry, getAssetFile } from '@/api/asset'
const assetFileColumns = [
  {
    title: 'Ip',
    dataIndex: 'ip',
  },
  {
    title: '文件源地址',
    dataIndex: 'filePath',
  },
  {
    title: '文件名',
    dataIndex: 'fileName',
  },
  {
    title: '过期时间',
    dataIndex: 'expirationTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '250px',
    scopedSlots: { customRender: 'action' },
  },
]

const statusMap = {
  0: {
    status: 'processing',
    text: '处理中',
  },
  1: {
    status: 'success',
    text: '成功',
  },
  2: {
    status: 'error',
    text: '失败',
  },
  3: {
    status: 'error',
    text: '已过期',
  },
}

export default {
  name: 'ServerDataDownload',
  data() {
    this.assetFileColumns = assetFileColumns
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      assetFileDownloadDetail: false,
      loading: false,
      source: 'server',
      ipInfo: [],
      assetFileData: [],
      request: {
        pageNo: 1,
        isAdmin: false,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
      },
      temp: {
        id: '',
        orderType: 'DF取数',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          assetFiles: [
            {
              ip: '',
              files: [''],
            },
          ],
        },
        timeline: [],
        comment: '',
      },
      rules: {
        ip: [{ required: true, message: '请填写IP', trigger: 'blur' }],
        path: [{ required: true, message: '请填写文件路径', trigger: 'blur' }],
        size: [{ required: true, message: '请填写文件大小', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
    }
  },
  created() {
    this.getInfo()
  },
  mounted() {
    if (this.$route.query && this.$route.query.ip) {
      this.temp.content.assetFiles[0].ip = this.$route.query.ip
      this.handleSearchIp(this.$route.query.ip, 'route')
    }
  },
  methods: {
    loadAssetFileData() {
      getAssetFile({ orderId: this.temp.id }).then(res => {
        this.assetFileData = res.Data.data
        this.assetFileData.forEach(pane => {
          if (this.checkExpired(pane.expirationTime)) {
            pane.status = 3
          }
        })
      })
    },
    getDetail() {
      this.loadAssetFileData()
      this.assetFileDownloadDetail = true
    },
    checkExpired(data) {
      const inputDate = new Date(data)
      const currentDate = new Date()
      currentDate.setHours(0, 0, 0, 0)
      const sevenDaysAgo = new Date(currentDate)
      sevenDaysAgo.setDate(currentDate.getDate() - 7)
      return inputDate < sevenDaysAgo
    },
    handleDownload(record) {
      let routeUrl =
        'https://cloud-api.intsig.net/v1/asset/file/download?fileUrl=' +
        encodeURIComponent(record.downloadUrl) +
        '&downloadType=new'
      window.open(routeUrl, '_blank')
    },
    handleRetry(record) {
      downloadAssetFileTaskRetry({ taskId: record.id }).then(response => {
        if (response === undefined) {
          notification.error({
            message: '失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          notification.success({
            message: '重试成功',
          })
          this.assetFileDownloadDetail = false
        }
      })
    },
    statusFilter(type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter(type) {
      return statusMap[type]?.status || type
    },
    addAssetFile() {
      this.temp.content.assetFiles.push({
        ip: '',
        files: [''],
      })
    },
    removeAssetFile(index) {
      this.temp.content.assetFiles.splice(index, 1)
    },
    addFilePath(assetFileIndex) {
      this.temp.content.assetFiles[assetFileIndex].files.push('')
    },
    removeFilePath(assetFileIndex, fileIndex) {
      this.temp.content.assetFiles[assetFileIndex].files.splice(fileIndex, 1)
    },
    getInfo() {
      this.nodeStatus = 0
      this.source = ''
      if (this.$route.query.id) {
        this.id = this.$route.query.id
        getOrderInfo(this.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.content.resultIpList === null) {
              this.temp.content.resultIpList = []
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
      this.GetAssetIps()
    },
    GetAssetIps() {
      getAllAssetsIp(this.request).then(res => {
        if (res && res.Data && res.Data.info) {
          this.ipInfo.push(...res.Data.info)
          this.request.pageNo++
          this.request.isRequest = false
        }
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/server-data-download-df', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    productApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: 'DF产品审批成功',
                description: '安全审批成功',
              })
            }
          })
        }
      })
    },
    safetyApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '安全审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 5
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(obj.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    handlePopupScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target
      if (scrollHeight - scrollTop - 1 <= clientHeight && clientHeight !== 0) {
        if (!this.request.isRequest && (this.request.totalPage < 0 || this.request.pageNo <= this.request.totalPage)) {
          this.request.isRequest = true
          getAllAssetsIp(this.request).then(res => {
            this.info.push(...res.Data.info)
            this.request.pageNo++
            this.request.isRequest = false
          })
        }
      }
    },
    handleSearchIp(value, from = '') {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.ipInfo = res.Data.info
        if (from) {
          this.handleChangeIp(value)
        }
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    handleChangeIp(value) {
      this.temp.content.uninstallDisks = false
      this.temp.content.uninstallDisks = false
      const foundObject = this.ipInfo.find(obj => obj.ip === value)
      this.temp.content.hostname = foundObject.hostname
      this.temp.content.oldCpu = foundObject.cpu
      this.temp.content.oldMem = foundObject.memory
      this.temp.content.assetId = foundObject.uuid
      this.temp.content.disks = foundObject.disks
      if (foundObject.disks === null) {
        this.temp.content.disks = []
      }
      this.temp.content.hostIp = foundObject.hostIp
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped>
.group {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.file-path {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
</style>
