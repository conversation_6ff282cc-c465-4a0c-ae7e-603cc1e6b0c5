<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item name="name" label="任务名称">
              <a-input v-model:value="temp.content.name" />
            </a-form-model-item>
            <a-form-model-item name="hosts" label="目标机器">
              <a-input v-model:value="temp.content.hosts" placeholder="127.0.0.1或127.0.0.1|*********" />
            </a-form-model-item>
            <a-form-model-item name="taskType" label="任务类型">
              <a-radio-group v-model:value="temp.content.taskType" button-style="solid">
                <a-radio-button value="ansible">Ansible</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item name="module" label="任务模块">
              <a-radio-group v-model:value="temp.content.module" button-style="solid">
                <a-radio-button value="script">Script</a-radio-button>
                <a-radio-button value="shell" disabled>Shell</a-radio-button>
              </a-radio-group>
            </a-form-model-item>

            <a-form-model-item name="trigger" label="类型">
              <a-radio-group v-model:value="temp.content.trigger" button-style="solid">
                <a-radio-button value="一次性任务" disabled>一次性任务</a-radio-button>
                <a-radio-button value="定时任务">定时任务</a-radio-button>
                <a-radio-button value="周期任务" disabled>周期任务</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item name="crontab" label="Crontab">
              <a-input placeholder="30 8 * * *" v-model:value="temp.content.crontab">
                <template #suffix>
                  <a-tooltip title="* * * * *: 分 时 日 月 周">
                    <a-icon type="info-circle" style="color: rgba(0, 0, 0, 0.45)" />
                  </a-tooltip>
                </template>
              </a-input>
            </a-form-model-item>
            <a-form-model-item name="startDate" label="检查开始时间">
              <a-date-picker
                v-model:value="temp.content.startDate"
                :disabled-date="disabledStartDate"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="开始时间"
                @openChange="handleStartOpenChange"
                @change="onStartChange"
              />
            </a-form-model-item>
            <a-form-model-item name="endDate" label="检查结束时间">
              <a-date-picker
                v-model:value="temp.content.endDate"
                :disabled-date="disabledEndDate"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="结束时间"
                :open="endOpen"
                @openChange="handleEndOpenChange"
                @change="onEndChange"
              />
            </a-form-model-item>
            <a-form-model-item name="email" label="通知人">
              <a-input placeholder="<EMAIL>" v-model:value="temp.content.email" />
            </a-form-model-item>
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <!-- <a-form-model-item name="content" label="脚本内容：">
              <a-radio-group default-value="python" @change="languageChange" button-style="solid">
                <a-radio-button value="python"> Python </a-radio-button>
                <a-radio-button value="shell"> Shell </a-radio-button>
              </a-radio-group>
              <TxEditorCode v-model="editorOpt.value" height="400px" :language="editorOpt.language" theme="vs-dark" />
            </a-form-model-item> -->

            <a-form-model-item name="ansibleType" label="脚本语言">
              <a-radio-group v-model:value="temp.content.ansibleType" @change="languageChange" button-style="solid">
                <a-radio-button value="python">Python</a-radio-button>
                <a-radio-button value="shell">Shell</a-radio-button>
              </a-radio-group>
              <TxEditorCode
                v-if="containerReload"
                v-model="editorOpt.value"
                height="400px"
                :language="editorOpt.language"
                theme="vs-dark"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24" style="text-align: center">
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/safety/baseline-check">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'

export default {
  name: 'BaselineCheckResources',
  components: {},
  provide() {
    return {
      reload: this.reload,
    }
  },
  data: function () {
    return {
      endOpen: false,
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '基线检查-资源类',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: 'cloud_test',
          hosts: '',
          taskType: 'ansible',
          module: 'script',
          ansibleType: 'python',
          content: '',
          trigger: '定时任务',
          crontab: '',
          startDate: '',
          endDate: '',
          email: store.getters.email,
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.name': [{ required: true, message: '请输入检查名称', trigger: 'blur' }],
        'content.hosts': [{ required: true, message: '请填写目标机器', trigger: 'blur' }],
        'content.task_type': [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
        'content.module': [{ required: true, message: '请选择任务模块', trigger: 'blur' }],
        'content.ansible_type': [{ required: true, message: '请选择脚本语言', trigger: 'blur' }],
        'content.content': [{ required: true, message: '请填写脚本内容', trigger: 'blur' }],
        'content.trigger': [{ required: true, message: '请选择类型', trigger: 'blur' }],
        'content.crontab': [{ required: true, message: '请填写Crontab', trigger: 'blur' }],
        'content.startDate': [{ required: true, message: '请选择检查开始时间', trigger: 'change' }],
        'content.endDate': [{ required: true, message: '请选择检查结束时间', trigger: 'change' }],
        'content.email': [{ required: true, message: '请填写通知人', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      editorOpt: {
        language: 'python',
        value: ' #!/bin/python3 \r # 默认使用python3',
      },
      containerReload: true,
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    async reload() {
      this.containerReload = false
      await this.$nextTick()
      this.containerReload = true
    },
    languageChange(e) {
      const language = e.target.value
      let value = this.editorOpt.value
      // 保留前导空格
      const defaultShell = ' #!/bin/bash \r # 默认使用bash'
      const defaultPython = ' #!/bin/python3 \r # 默认使用python3'
      switch (language) {
        case 'shell':
          if (value.replace(/[\r\n]/g, '') == defaultPython.replace(/[\r\n]/g, '')) {
            value = defaultShell
          }
          break
        case 'python':
          if (value.replace(/[\r\n]/g, '') == defaultShell.replace(/[\r\n]/g, '')) {
            value = defaultPython
          }
          break
        default: {
          return
        }
      }
      this.editorOpt.language = language
      this.editorOpt.value = value
    },
    /*
    scriptChange (e) {
      let value = ''
      switch (e.target.value) {
        case 'shell':
          value = ` #!/bin/bash \r # 默认使用bash`
          break
        case 'python':
          value = ' #!/bin/python3 \r # 默认使用python3'
          break
      }
      this.editorOpt = {
        language: e.target.value,
        value: value
      }
    },
    */
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              noc.notice.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              noc.notice.ok({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = response.Data
              this.$router.push({ path: '/workflow/baseline-check-resources', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              noc.notice.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              noc.notice.ok({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              noc.notice.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              noc.notice.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              noc.notice.ok({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              noc.notice.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              noc.notice.ok({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              noc.notice.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              noc.notice.ok({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    onStartChange(value, dateString) {
      this.temp.content.startDate = dateString
    },
    onEndChange(value, dateString) {
      this.temp.content.endDate = dateString
    },
    disabledStartDate(startDate) {
      const endDate = this.temp.content.endDate
      if (!startDate || !endDate) {
        return false
      }
      return startDate.valueOf() > endDate.valueOf()
    },
    disabledEndDate(endDate) {
      const startDate = this.temp.content.startDate
      if (!endDate || !startDate) {
        return false
      }
      return startDate.valueOf() >= endDate.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
  },
}
</script>

<style scoped></style>
