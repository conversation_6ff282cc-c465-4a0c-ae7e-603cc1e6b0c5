<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="downloadSource" label="下载来源">
              <a-radio-group v-model:value="temp.content.downloadSource" button-style="solid">
                <a-radio-button value="国内">国内</a-radio-button>
                <a-radio-button value="国外">国外</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.downloadSource === '国外'">
                <p>
                  国外数据集下载额外费用：
                  <span style="font-size: 16px; font-weight: bolder; color: #ff4500">0.041$/GB</span>
                  (主要涉及流量和实例费用)
                </p>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="storageReq" label="存储需求">
              <a-radio-group v-model:value="temp.content.storageReq" button-style="solid">
                <a-radio-button value="国外下载完拉回至国内（提供给用户后可删除）">国外下载完拉回至国内</a-radio-button>
                <a-radio-button value="仅存储到S3（需要用的时候再从S3下载到国内）">仅存储到S3对象存储中</a-radio-button>
              </a-radio-group>
              <div v-if="temp.content.storageReq === '国外下载完拉回至国内（提供给用户后可删除）'">
                <p><span style="font-weight: bolder">提供给用户后可删除</span></p>
              </div>
              <div v-else-if="temp.content.storageReq === '仅存储到S3（需要用的时候再从S3下载到国内）'">
                <p><span style="font-weight: bolder">需要用的时候再从S3下载到国内</span></p>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="name" label="数据集名称">
              <a-input
                v-model:value="temp.content.name"
                placeholder="一律小写，空格用_代替，只包含小写英文和_和数字"
                v-on:input="limitInput"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="size" label="数据大小">
              <a-input-number v-model:value="temp.content.size" placeholder="填写数据大小">
                <template #addonAfter>
                  <a-select v-model:value="temp.content.sizeUnit">
                    <a-select-option value="MB">MB</a-select-option>
                    <a-select-option value="GB">GB</a-select-option>
                    <a-select-option value="TB">TB</a-select-option>
                  </a-select>
                </template>
              </a-input-number>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="language" label="数据集语言">
              <a-checkbox-group v-model:value="temp.content.language" :options="languageOptions" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="modal" label="数据集模态">
              <a-checkbox-group v-model:value="temp.content.modal" :options="modalOptions" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="check" label="数据集自查">
              <a-radio-group v-model:value="temp.content.check" :options="checkOptions" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="downloadType" label="下载方法">
              <a-textarea
                v-model:value="temp.content.downloadType"
                placeholder="下载方法包括不限于git，wget，百度网盘等，请根据填写的内容明确无误的下载相应的数据"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="path" label="数据存放位置">
              <a-textarea
                v-model:value="temp.content.path"
                placeholder="数据下载或者从海外s3拉回到服务器的具体位置，建议起始目录开头为/juicefs-algorithm/"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="summary" label="数据简介">
              <a-textarea v-model:value="temp.content.summary" placeholder="请填写下载数据的简短但充分的介绍" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" placeholder="申请的用途或理由说明" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node >= 2" :span="12">
            <a-form-model-item name="automation" label="是否自动化">
              <a-radio-group v-model:value="temp.content.automation" button-style="solid">
                <a-radio-button value="yes">是</a-radio-button>
                <a-radio-button value="no">否</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node >= 2 && temp.content.automation === 'yes'" :span="12">
            <a-form-model-item name="automation" label="自动化脚本">
              <a-radio-group v-model:value="temp.content.scriptId" button-style="solid">
                <a-radio-button :value="89">huggingface</a-radio-button>
                <a-radio-button :value="165">wget</a-radio-button>
                <a-radio-button :value="166">hf-mirror</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <a-popconfirm title="请先确认数据集名称，数据存放位置，下载方法是否填写准确！！" @confirm="approveData">
            <tx-button type="primary">同意</tx-button>
          </a-popconfirm>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { removeWatermark, setWaterMark } from '@/utils/watermark'

const languageOptions = ['中文', '英文', '中英混合', '其他']
const modalOptions = ['文本', '图像', '语音', '模型', '其他']
const checkOptions = ['不存在数据集列表中', '已存在数据集列表中']

export default {
  name: 'DataSetDownload',
  data() {
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      languageOptions,
      modalOptions,
      checkOptions,
      temp: {
        id: '',
        orderType: '数据集下载',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          downloadSource: '国外',
          storageReq: '',
          name: '',
          downloadType: '',
          language: [],
          check: '',
          modal: [],
          size: 0,
          sizeUnit: 'GB',
          path: '',
          summary: '',
          reason: '',
          automation: 'yes',
          scriptId: 89,
        },
        timeline: [],
        comment: '',
      },
      rules: {
        downloadSource: [{ required: true, message: '请选择下载来源', trigger: 'blur' }],
        storageReq: [{ required: true, message: '请选择存储需求', trigger: 'blur' }],
        name: [{ required: true, message: '请填写数据集名称', trigger: 'blur' }],
        downloadType: [{ required: true, message: '请填写下载方式', trigger: 'blur' }],
        language: [{ required: true, message: '请选择数据集语言', trigger: 'blur' }],
        check: [{ required: true, message: '请选择数据集自查结果', trigger: 'blur' }],
        modal: [{ required: true, message: '请选择数据集模态', trigger: 'blur' }],
        size: [{ required: true, message: '请填写数据大小', trigger: 'blur' }],
        path: [{ required: true, message: '请填写数据存放位置', trigger: 'blur' }],
        summary: [{ required: true, message: '请填写数据简介', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
    }
  },
  created() {
    this.getInfo()
  },
  mounted() {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed() {
    removeWatermark()
  },
  methods: {
    limitInput: function (event) {
      this.temp.content.name = event.target.value.replace(/[^0-9a-z_]/g, '')
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.content.data) {
            response.Data.content = JSON.parse(response.Data.content.data)
          }
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
          if (response.Data.content.data) {
            response.Data.content = JSON.parse(response.Data.content.data)
          }
          if (response.Data.orderType === this.temp.orderType) {
            this.temp.content = response.Data.content
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/data-set-download', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
