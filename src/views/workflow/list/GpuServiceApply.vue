<template>
  <page-header-wrapper>
    <template v-slot:content>
      <tx-button icon="solution" size="small">
        <a
          href="https://gitlab.intsig.net/dongfei_gao/publish-doc#gpu%E6%9C%8D%E5%8A%A1%E5%8F%91%E5%B8%83%E5%B7%A5%E5%8D%95%E8%AF%B4%E6%98%8E"
          style="text-decoration: none"
        >
          参考文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-row>
      <a-col :span="temp.handlerEmail.includes(localUser) && temp.status !== 30 ? 16 : 24">
        <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
          <a-form-model
            @change="getYamlInfo()"
            ref="orderForm"
            :model="temp.content"
            :rules="rules"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
              </a-col>
            </a-row>
            <a-row v-if="temp.node > 0" :gutter="24">
              <a-col :span="24">
                <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
              </a-col>
            </a-row>
            <a-row v-if="temp.node > 0" :gutter="24">
              <a-col :span="24">
                <a-form-model-item label="状态">
                  <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
                  <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
                  <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
                  <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
                  <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
                  <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
                  <a-tag v-else>未知</a-tag>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item v-if="temp.node === 0" name="recordType" label="类型">
                  <a-radio-group v-model:value="temp.content.recordType" button-style="solid">
                    <a-radio-button value="add">新增</a-radio-button>
                    <a-radio-button value="update">更新</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-form-model-item label="服务列表" v-if="temp.node === 0 && temp.content.recordType === 'update'" >
              <a-select
                v-model:value="temp.id"
                placeholder="选择之前提交的服务"
                allowClear
                :showSearch="true"
                :filter-option="filterOption"                
                style="width: 400px"
                @change="serviceNameSelectChange"
              >
                <a-select-option v-for="item in serviceNameList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="服务名称" v-else name="serviceName">
              <a-input
              v-model:value="temp.content.serviceName"
              placeholder="后续不可修改"
              style="width: 400px"
              @change="serviceCheck"
            />
            </a-form-model-item> 
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="idc" label="集群">
                  <a-radio-group v-model:value="temp.content.idc" button-style="solid">
                    <a-radio-button value="松江">松江</a-radio-button>
                    <a-radio-button value="松江切片">松江切片</a-radio-button>
                    <a-radio-button value="颛桥">颛桥</a-radio-button>
                    <a-radio-button value="俄勒冈">俄勒冈</a-radio-button>
                    <a-radio-button value="宁夏">宁夏</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="org" label="事业部">
                  <a-checkbox-group v-model:value="temp.content.org" :options="orgOptions" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="project" label="项目">
                  <a-input
                    v-model:value="temp.content.project"
                    placeholder="选填，一律小写，空格用-代替，只包含小写英文数字和-"
                    v-on:input="limitProjectInput"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="servicePort" label="服务端口">
                  <a-input-number v-model:value="temp.content.servicePort" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="serviceType" label="服务类型">
                  <a-radio-group v-model:value="temp.content.serviceType" button-style="solid">
                    <a-radio-button value="gpu">GPU</a-radio-button>
                    <a-radio-button value="cpu">CPU</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item v-if="temp.content.serviceType == 'gpu'" name="gpuType" label="GPU类型">
                  <a-radio-group v-model:value="temp.content.gpuType" button-style="solid">
                    <a-radio-button v-if="temp.content.idc === '俄勒冈' || temp.content.idc === '宁夏'" value="t4">T4</a-radio-button>
                    <a-radio-button v-if="temp.content.idc != '宁夏'" value="l4-a10">L4/A10</a-radio-button>
                    <a-radio-button v-if="temp.content.idc == '松江'" value="a100">A100</a-radio-button>
                    <a-radio-button v-if="temp.content.idc == '松江' || temp.content.idc == '松江切片'" value="4090D">4090D</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="image" label="镜像名">
                  <a-input v-model:value="temp.content.image" placeholder="镜像名称" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="command" label="Command">
                  <a-textarea v-model:value="temp.content.command" :precision="4" placeholder="服务启动命令，选填" :auto-size="{ minRows: 1, maxRows:8 }"/>
                </a-form-model-item>  
                    <!-- <a-col :span="4">
                      <!-- <a-form-item>
                          <tx-button
                            v-if="index === 0"
                            type="primary"
                            icon="plus"
                            style="width: 30px"
                            size="small"
                            @click="addCommandRow"
                          ></tx-button>
                          <tx-button
                            v-else
                            type="dashed"
                            size="small"
                            icon="minus"
                            style="width: 30px"
                            @click="removeCommandRow(index)"
                          ></tx-button>
                        </a-form-item> -->
                    <!-- </a-col>
                  </a-row>
                </a-form-model-item> --> 
              </a-col>
            </a-row>
            <!-- <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="args" label="Args">
                  <a-form name="args_item" :model="temp.content.args">
                    <a-row :gutter="24" v-for="(item, index) in temp.content.args" v-bind="formItemLayoutWithOutLabel">
                      <a-col :span="20">
                        <a-form-item name="form.item[index].value">
                          <a-input v-model:value="item.value" :precision="4" placeholder="服务启动参数，选填" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="4">
                        <a-form-item>
                          <tx-button
                            v-if="index === 0"
                            type="primary"
                            icon="plus"
                            style="width: 30px"
                            size="small"
                            @click="addArgsRow"
                          ></tx-button>
                          <tx-button
                            v-else
                            type="dashed"
                            size="small"
                            icon="minus"
                            style="width: 30px"
                            @click="removeArgsRow(index)"
                          ></tx-button>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </a-form-model-item>
              </a-col>
            </a-row> -->
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="env" label="环境变量">
                  <a-form name="productLineList_item" :model="temp.content.env">
                    <a-row :gutter="24" v-for="(item, index) in temp.content.env" v-bind="formItemLayoutWithOutLabel">
                      <a-col :span="10">
                        <a-form-item name="form.item[index].name">
                          <a-input v-model:value="item.name" :precision="4" placeholder="选填" addon-before="key" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="10">
                        <a-form-item name="form.item[index].value">
                          <a-input v-model:value="item.value" :precision="4" placeholder="选填" addon-before="value" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="4">
                        <a-form-item>
                          <tx-button
                            v-if="index === 0"
                            type="primary"
                            icon="plus"
                            style="width: 30px"
                            size="small"
                            @click="addEnvRow"
                          ></tx-button>
                          <tx-button
                            v-else
                            type="dashed"
                            size="small"
                            icon="minus"
                            style="width: 30px"
                            @click="removeEnvRow(index)"
                          ></tx-button>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="startupCheck" label="启动检查">
                  <a-radio-group v-model:value="temp.content.startupCheck" button-style="solid">
                    <a-radio-button :value="true">支持</a-radio-button>
                    <a-radio-button :value="false">不支持</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="2"></a-col>
              <a-col :span="22" v-if="temp.content.startupCheck">
                <a-form-model-item name="startupPath" label="启动检查路径">
                  <a-input v-model:value="temp.content.startupPath" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="healthyCheck" label="健康检查">
                  <a-radio-group v-model:value="temp.content.healthyCheck" button-style="solid">
                    <a-radio-button :value="true">支持</a-radio-button>
                    <a-radio-button :value="false">不支持</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="2"></a-col>
              <a-col :span="22" v-if="temp.content.healthyCheck">
                <a-form-model-item name="healthyPath" label="健康检查路径">
                  <a-input v-model:value="temp.content.healthyPath" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="warmUpCheck" label="就绪检查">
                  <a-radio-group v-model:value="temp.content.warmUpCheck" button-style="solid">
                    <a-radio-button :value="true">支持</a-radio-button>
                    <a-radio-button :value="false">不支持</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="temp.content.warmUpCheck">
              <a-col :span="4"></a-col>
              <a-col :span="10">
                <a-form-model-item name="warmUpTime" label="时间间隔">
                  <a-input-number v-model:value="temp.content.warmUpTime" addon-after="秒" />
                </a-form-model-item>
              </a-col>
              <a-col :span="10">
                <a-form-model-item name="warmUpPath" label="检查路径">
                  <a-input v-model:value="temp.content.warmUpPath" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="autoCheck" label="自动扩容">
                  <a-radio-group v-model:value="temp.content.autoCheck" button-style="solid">
                    <a-radio-button :value="true">支持</a-radio-button>
                    <a-radio-button :value="false">不支持</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="temp.content.autoCheck">
              <a-col :span="4"></a-col>
              <a-col :span="10">
                <a-form-model-item name="minReplicas" label="最低副本数">
                  <a-input-number v-model:value="temp.content.minReplicas" />
                </a-form-model-item>
              </a-col>
              <a-col :span="10">
                <a-form-model-item name="maxReplicas" label="最高副本数">
                  <a-input-number v-model:value="temp.content.maxReplicas" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item v-if="temp.content.serviceType == 'gpu'" name="gpu" label="显存">
                  <a-input-number v-model:value="temp.content.gpu" addon-after="G" />
                </a-form-model-item>
                <a-form-model-item name="cpu" label="CPU">
                  <a-input-number v-model:value="temp.content.cpu" addon-after="核" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item name="memory" label="内存">
                  <a-input-number v-model:value="temp.content.memory" addon-after="G" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="dirMount" label="目录挂载">
                  <a-form name="dirMountList_item" :model="temp.content.dirMount">
                    <a-row
                      :gutter="24"
                      v-for="(item, index) in temp.content.dirMount"
                      v-bind="formItemLayoutWithOutLabel"
                    >
                      <a-col :span="20">
                        <a-form-item name="form.item[index].name">
                          <!-- <a-input
                            v-model:value="item.name"
                            :precision="4"
                            placeholder="选填,必须为/seaweedfs_mount_hdd、/beegfs_mount 这两个共享目录开头,数据为20G以内"
                            addon-before="宿主机目录"
                          /> -->
                          <a-input-group style="display: flex" compact>
                            <p class="hostMenus" style="height: 32px; line-height: 32px">宿主机目录</p>
                            <a-select
                              @change="rootChange(index)"
                              style="width: 200px"
                              v-model:value="item.hostMenuRoot"
                              default-value=""
                              allowClear
                            >
                              <a-select-option value="/seaweedfs_mount_hdd/">/seaweedfs_mount_hdd/</a-select-option>
                              <a-select-option value="/beegfs_mount/">/beegfs_mount/</a-select-option>
                              <a-select-option value="/DeepLearning/">/DeepLearning/</a-select-option>
                              <a-select-option value="/juicefs-algorithm/">/juicefs-algorithm/</a-select-option>
                            </a-select>
                            <a-input
                              @change="rootChange(index)"
                              v-model:value="item.defineName"
                              style="display: inline-block"
                            />
                          </a-input-group>
                        </a-form-item>
                      </a-col>
                      <a-col :span="20">
                        <a-form-item name="form.item[index].value">
                          <a-input
                            v-model:value="item.value"
                            :precision="4"
                            placeholder="选填"
                            addon-before="容器目录"
                          />
                        </a-form-item>
                      </a-col>

                      <!-- <a-col :span="20">
                        <a-form-item name="form.item[index].value">
                          <a-input-group style="display: flex" compact>
                            <p class="hostMenus" style="height: 32px; line-height: 32px">使用新负载均衡</p>
                            <a-select
                              @change="rootChange(index)"
                              style="width: 100%"
                              v-model:value="item.loadBalance"
                              default-value=""
                            >
                              <a-select-option :value="true">使用</a-select-option>
                              <a-select-option :value="false">不使用</a-select-option>
                            </a-select>
                          </a-input-group>
                        </a-form-item>
                      </a-col> -->
                      <a-col :span="4">
                        <a-form-item>
                          <tx-button
                            v-if="index === 0"
                            type="primary"
                            icon="plus"
                            style="width: 30px"
                            size="small"
                            @click="addDirMountRow"
                          ></tx-button>
                          <tx-button
                            v-else
                            type="dashed"
                            size="small"
                            icon="minus"
                            style="width: 30px"
                            @click="removeDirMountRow(index)"
                          ></tx-button>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col v-if="temp.node > 0" :span="24">
                <a-form-model-item name="dirCopy" label="复制路径">
                  <a-input
                    v-model:value="temp.content.dirCopy"
                    @input="handleDirCopyChange"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>            
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="loadBalance" label="使用新负载均衡">
                  <a-radio-group v-model:value="temp.content.loadBalance" button-style="solid">
                    <a-radio-button :value="false">不使用</a-radio-button>
                    <a-radio-button :value="true">使用</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item name="reason" label="申请理由">
                  <a-textarea v-model:value="temp.content.reason" auto-size />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col v-if="temp.node > 0" :span="24">
                <a-form-model-item name="comment" label="回复/评论">
                  <a-textarea v-model:value="temp.comment" auto-size />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col v-if="temp.node > 0" :span="24">
                <a-form-model-item label="审批节点">
                  <a-timeline>
                    <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                      {{ activity }}
                    </a-timeline-item>
                  </a-timeline>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button
                v-if="temp.content.dirMount.length >= 1 && temp.content.dirMount[0].value !== ''"
                type="primary"
                @click="copyDirData"
                style="margin-left: 10px"
              >
                目录复制
              </tx-button>
              <tx-button
                v-if="temp.content.idc != '' && temp.content.image !== ''"
                type="primary"
                @click="imageCache()"
                style="margin-left: 10px"
              >
                镜像缓存
              </tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
              temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
          </a-form-model>
        </a-card>
      </a-col>
      <a-col :span="8" v-if="temp.handlerEmail.includes(localUser) && temp.status !== 30">
        <!--      <a-col :span="8">-->
        <a-card style="margin-top: 12px; margin-left: 4px" :bordered="false" :title="`YAML格式`">
          <a-button type="primary" @click="copyText">复制YAML内容</a-button>
          <a-button type="primary" @click="compareYaml" style="margin-left: 10px">查看YAML差异</a-button>
          <a-textarea class="myTextarea" v-model:value="temp.content.yaml" :auto-size="{ minRows: 40 }" disabled />
        </a-card>
      </a-col>
    </a-row>
  </page-header-wrapper>

<a-modal
  v-model:visible="compareYamlModal"
  title="YAML配置差异对比"
  width="80%"
  :footer="null"
>
  <div v-html="yamlDiffContent" style="max-height: 70vh; overflow-y: auto"></div>
</a-modal>
</template>

<script>
import store from '@/store'
import { notification, message } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder, gpuApplyCron, imageCacheCron, getHistoryOrderList, addGpuServiceRecord, checkGpuServiceRecord, updateGpuServiceRecord} from '@/api/workflow/order'
import { filterLabelValue } from '@aim/helper'
import { diffLines } from 'diff'



const orgOptions = ['CS', 'SSG', 'DG', 'CC', 'AIM', 'no_statistic']

export default {
  name: 'GpuServiceApply',
  data() {
    let validateImage = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入镜像'))
      } else {
        const reg = /^registry\.intsig\.net/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('镜像必须以registry\.intsig\.net开头'))
        }
      }
    }
    return {
      orgOptions,
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      serviceNameCheck : false,
      serviceNameList: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      originalYaml : null,
      yamlDiffHtml: '',
      compareYamlModal: false,
      yamlDiffContent: '',
    temp: {
        id: '',
        orderType: 'GPU服务发布',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          recordType: 'update',
          gpuType: 'l4-a10',
          idc: '松江',
          org: [],
          project: '',
          serviceType: 'gpu',
          serviceName: '',
          servicePort: 30006,
          image: '',
          env: [
            {
              name: '',
              value: '',
            },
          ],
          command: '',
          // args: [
          //   {
          //     value: '',
          //   },
          // ],
          startupCheck: true,
          startupPath: '/healthy',
          healthyCheck: true,
          healthyPath: '/healthy',
          warmUpCheck: true,
          warmUpTime: 10,
          warmUpPath: '/ready',
          autoCheck: true,
          minReplicas: 2,
          maxReplicas: 4,
          cpu: undefined,
          gpu: undefined,
          memory: undefined,
          dirMount: [
            {
              name: '',
              value: '',
              hostMenuRoot: '',
              defineName: '',
            },
          ],
          dirCopy: '',
          dirCopyAutoSet: true,
          yaml: '',
          loadBalance: false,
          reason: '',
        },
        timeline: [],
        comment: '',
      },

      rules: {
        idc: [{ required: true, message: '请选择集群', trigger: 'blur' }],
        org: [{ required: true, message: '请填写事业部', trigger: 'blur' }],
        serviceName: [{ required: true, message: '请填写服务名', trigger: 'blur' }],
        servicePort: [{ required: true, message: '请填写服务端口', trigger: 'blur' }],
        serviceType: [{ required: true, message: '请选择服务类型', trigger: 'blur' }],
        gpuType: [{ required: true, message: '请选择gpu类型', trigger: ['change','blur']}],
        image: [{ required: true, validator: validateImage, trigger: 'blur' }],
        startupCheck: [{ required: true, message: '是否支持启动检查', trigger: 'blur' }],
        startupPath: [{ required: true, message: '请填写启动检查路径', trigger: 'blur' }],
        healthyCheck: [{ required: true, message: '是否支持健康检查', trigger: 'blur' }],
        healthyPath: [{ required: true, message: '请填写健康检查路径', trigger: 'blur' }],
        warmUpCheck: [{ required: true, message: '是否支持就绪检查', trigger: 'blur' }],
        warmUpTime: [{ required: true, message: '请填写就绪检查路径', trigger: 'blur' }],
        warmUpPath: [{ required: true, message: '请填写就绪检查路径', trigger: 'blur' }],
        autoCheck: [{ required: true, message: '是否支持自动扩容', trigger: 'blur' }],
        minReplicas: [{ required: true, message: '请填写最低副本数', trigger: 'blur' }],
        maxReplicas: [{ required: true, message: '请填写最高副本数', trigger: 'blur' }],
        cpu: [{ required: true, message: '请填写服务所需cpu最大核数', trigger: 'blur' }],
        gpu: [{ required: true, message: '请填写服务所需显存', trigger: 'blur' }],
        memory: [{ required: true, message: '请填写服务所需最大内存', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
    }
  },
  created() {
    this.listHistoryOrder()
    this.getInfo()
  },

  methods: {
    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    },
    filterOption: filterLabelValue,
    rootChange(i) {
      this.temp.content.dirMount[i].name =
        this.temp.content.dirMount[i].hostMenuRoot + this.temp.content.dirMount[i].defineName
    },
    serviceCheck() {
      if (this.temp.content.recordType === 'add'){
          checkGpuServiceRecord({serviceName: this.temp.content.serviceName}).then(response =>{
            if (response.Data.status === 'ok'){
              this.serviceNameCheck = true
            }else {
              this.serviceNameCheck = false
            }
          })
      }
    },
    addRecord() {
      if (this.temp.content.recordType === 'add'){
        let requestData = {
          serviceName: this.temp.content.serviceName,
          serviceId: Number(this.temp.id),
        }
        console.log('id类型:', typeof Number(this.temp.id), '值:', this.temp.id)
        console.log('here is add Record requestData :', requestData)
        addGpuServiceRecord(requestData).then(response =>{
          if (response.Data.status !== 'ok'){
            console.log("add record failed, please check!")
          }
        })
      }
    },
    updateRecord() {
      if (this.temp.content.recordType === 'update' ){
        let requestParameters = {
          serviceName: this.temp.content.serviceName,
          id: this.temp.id,
        }
        updateGpuServiceRecord(requestParameters).then(response =>{
          if (response.Data.status !== 'ok'){
            console.log("update record failed, please check!")
          }
        })
        console.log("update record success!")
      }
    },
    // 确保diff库已加载
    async ensureDiffLibLoaded() {
      // 如果diffLines已经可用，直接返回
      if (typeof diffLines === 'function') {
        return true;
      }
      try {
        // 尝试通过vendorLoader加载diff库
        const { load } = await import('@aim/helper').then(m => m.loader);
        await load('diff');
        // 重新获取diffLines函数
        const diffFn = window.diffLines || (window.JsDiff && window.JsDiff.diffLines);
        if (typeof diffFn !== 'function') {
          throw new Error('无法加载diff库');
        }
        return true;
      } catch (error) {
        console.error('加载diff库失败:', error);
        return false;
      }
    },
    async compareYaml() {
      if (!this.originalYaml || !this.temp.content.yaml) {
        this.yamlDiffContent = '<div style="padding: 20px; text-align: center; color: #999">无可用对比数据</div>';
        this.compareYamlModal = true;
        return;
      }
      this.compareYamlModal = true;
      try {
        // 确保diff库已加载
        const diffLoaded = await this.ensureDiffLibLoaded();
        
        if (!diffLoaded) {
          throw new Error('diffLines函数未定义，请确保diff库已正确加载');
        }
        
        // 重新获取最新的diffLines函数
        const diffFn = window.diffLines || (window.JsDiff && window.JsDiff.diffLines);
        // 使用专业的diff库进行差异比较
        const changes = diffFn(this.originalYaml, this.temp.content.yaml);
        let result = '<div class="diff-container">';
        changes.forEach((change, index) => {
          const lines = change.value.split('\n').filter(line => line.trim() !== '');
          lines.forEach(line => {
            if (change.added) {
              result += `<div class="diff-line diff-added">+ ${this.escapeHtml(line)}</div>`;
            } else if (change.removed) {
              result += `<div class="diff-line diff-removed">- ${this.escapeHtml(line)}</div>`;
            } else {
              result += `<div class="diff-line diff-unchanged">  ${this.escapeHtml(line)}</div>`;
            }
          });
        });
        result += '</div>';
        this.yamlDiffContent = result;
      } catch (error) {
        console.error('YAML比较错误:', error);
        this.yamlDiffContent = `<div style="padding: 20px; color: #ff4d4f">比较时发生错误: ${error.message}</div>`;
      }
    },
    limitInput: function (event) {
      this.temp.content.serviceName = event.target.value.replace(/[^a-z0-9-]/g, '')
      if (this.temp.content.dirCopyAutoSet){
        this.temp.content.dirCopy = this.temp.content.serviceName
      }   
    },
    handleDirCopyChange(){
      this.temp.content.dirCopyAutoSet = false;
    },
    limitProjectInput: function (event) {
      this.temp.content.project = event.target.value.replace(/[^a-z0-9-]/g, '')
    },
    copyText() {
      let text = this.temp.content.yaml
      navigator.clipboard
        .writeText(text)
        .then(function () {
          message.success('复制成功')
        })
        .catch(function (error) {
          message.error('复制失败: ', error)
        })
    },
    listHistoryOrder(){
      let requestParameters = {
        pageNo: 1,
        pageSize: 10000,
      }
      getHistoryOrderList(requestParameters).then(res => {
        if (res.Data.historyOrder_infos.data === null || res.Data.historyOrder_infos.length <= 0) {
          this.serviceNameList = []
          console.log('res.data is null ')
        } else {
          for (let i = 0; i < res.Data.historyOrder_infos.length; i++) {
            let obj = {
              label: res.Data.historyOrder_infos[i].serviceName,
              value: res.Data.historyOrder_infos[i].serviceId,
            }
            console.log('obj :', obj);
            this.serviceNameList.push(obj)
          }
          console.log('serviceNameList  :', this.serviceNameList);
        }
      })
    },
    serviceNameSelectChange(value) {
      getOrderInfo(value).then(response => {
        response.Data.content = JSON.parse(response.Data.content.data)
        // 添加dirCopyAutoSet初始化
        response.Data.content.dirCopyAutoSet = response.Data.content.dirCopyAutoSet !== false
        this.temp.content.dirCopy = this.temp.content.serviceName
        // 保存之前的yaml文件用于对比
        this.originalYaml = response.Data.content.yaml
        if (response.Data.orderType === this.temp.orderType) {
          this.temp.content = response.Data.content
          this.temp.content.recordType = 'update'
        } else {
          this.$router.push({ path: '/404' })
        }
      })       
    },
    
    getInfo() {
      const lastOrderId = this.loadLastOrderId(); 
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          // 添加dirCopyAutoSet初始化
          response.Data.content.dirCopyAutoSet = response.Data.content.dirCopyAutoSet !== false
          this.temp.content.dirCopy = this.temp.content.serviceName
          // 保存之前的yaml文件用于对比
          this.originalYaml = response.Data.content.yaml
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
            response.Data.content = JSON.parse(response.Data.content.data)
            // 添加dirCopyAutoSet初始化
            response.Data.content.dirCopyAutoSet = response.Data.content.dirCopyAutoSet !== false
            this.temp.content.dirCopy = this.temp.content.serviceName
            // 保存之前的yaml文件用于对比
            this.originalYaml = response.Data.content.yaml
            if (response.Data.orderType === this.temp.orderType) {
              this.temp.content = response.Data.content
            } else {
              this.$router.push({ path: '/404' })
            }
        })
      }else if (lastOrderId){
          getOrderInfo(lastOrderId).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          // 添加dirCopyAutoSet初始化
          response.Data.content.dirCopyAutoSet = response.Data.content.dirCopyAutoSet !== false
          this.temp.content.dirCopy = this.temp.content.serviceName
          // 保存之前的yaml文件用于对比
          this.originalYaml = response.Data.content.yaml          
          if (response.Data.orderType === this.temp.orderType) {
            this.temp.content = response.Data.content
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    saveLastOrderId(orderId){
      try {
        // 将订单ID存储到localStorage
        localStorage.setItem('lastGpuServiceOrderId', orderId);
        console.log('上次提交订单ID已保存:', orderId);
      } catch (e) {
        console.error('保存订单ID失败:', e);
        this.$message.error('保存订单信息失败，请检查浏览器存储权限');
      }
    },
    loadLastOrderId(){
      const orderidStr = localStorage.getItem('lastGpuServiceOrderId');
      console.log('成功加载lastOrderId: ', orderidStr);
      return orderidStr; // 添加返回值
    },
    getYamlInfo() {
      let org = this.temp.content.org[0]
      if (this.temp.content.org.length > 1) {
        for (let i = 1; i < this.temp.content.org.length; i++) {
          org = org + '_' + this.temp.content.org[i]
        }
      }

      if (this.temp.content.project === '') {
        this.temp.content.project = org.toLowerCase()
      }
      let labelInfo = ''
      if (this.temp.content.serviceType == 'gpu' && (this.temp.content.gpuType == 'a100' || this.temp.content.gpuType == '4090D' ) ) {
        labelInfo = this.temp.content.gpuType
      } else {
        labelInfo = ''
      }
      let gpuInfo = ''
      this.temp.content.gpuType == 'a100' || this.temp.content.gpuType == '4090D' ? (gpuInfo = `\n    gpu: ${labelInfo}`) : gpuInfo == ''
      let yamlInfo =
        'apiVersion: apps/v1\n' +
        'kind: Deployment\n' +
        'metadata:\n' +
        '  name: ' +
        this.temp.content.serviceName +
        '\n' +
        '  labels:' +
        gpuInfo +
        '\n' +
        '    department: ' +
        org +
        '\n' +
        '    group: ' +
        this.temp.content.project +
        '\n' +
        '    type: ' +
        this.temp.content.serviceType +
        '\n' +
        '    app: ' +
        this.temp.content.serviceName +
        '\n' +
        'spec:\n' +
        '  selector:\n' +
        '    matchLabels:\n' +
        '      app: ' +
        this.temp.content.serviceName +
        '\n' +
        '  template:\n' +
        '    metadata:\n' +
        '      annotations:\n' +
        "        prometheus.io/port: '" +
        this.temp.content.servicePort.toString() +
        "'\n" +
        '      labels:\n' +
        '        app: ' +
        this.temp.content.serviceName +
        '\n' 
        if (this.temp.content.idc == "俄勒冈" ){
          yamlInfo +=
            '    spec:\n' +
            '      containers:\n' +
            '      - name: ' +
            this.temp.content.serviceName +
            '\n' +
            '        image: ' +
            this.temp.content.image.replace('registry.intsig.net', '150184970070.dkr.ecr.us-west-2.amazonaws.com/aws_ecr') +
            '\n' 
        }else if (this.temp.content.idc == "宁夏"){
          yamlInfo +=
            '    spec:\n' +
            '      containers:\n' +
            '      - name: ' +
            this.temp.content.serviceName +
            '\n' +
            '        image: ' +
            this.temp.content.image.replace('registry.intsig.net', '090646384768.dkr.ecr.cn-northwest-1.amazonaws.com.cn/aws_ecr') +
            '\n' 
        }else{
          yamlInfo +=
            '    spec:\n' +
            '      containers:\n' +
            '      - name: ' +
            this.temp.content.serviceName +
            '\n' +
            '        image: ' +
            this.temp.content.image +
            '\n' 
        }
        yamlInfo +=
          '        env:\n' +
          '        # INTSIG_LICENSE_SERVER为固定，每个服务都有\n' +
          '        - name: INTSIG_LICENSE_SERVER\n' +
          '          value: license-server:8885\n'
      if (this.temp.content.env.length >= 1 && this.temp.content.env[0].value !== '') {
        for (let i = 0; i < this.temp.content.env.length; i++) {
          let envName = this.temp.content.env[i].name
          let envValue = this.temp.content.env[i].value
          if (Number.isFinite(+envName)) {
            envName = '"' + envName + '"'
          }
          if (Number.isFinite(+envValue)) {
            envValue = '"' + envValue + '"'
          }
          yamlInfo += '        - name: ' + envName + '\n' + '          value: ' + envValue + '\n'
        }
      }
      let a = ''
      let b = ''
      if (this.temp.content.command.length >= 1) {
        // test修改
        let index = this.temp.content.command.indexOf('--')
        a = this.temp.content.command.slice(0, index).trim()
        b = this.temp.content.command.slice(index).trim()
        // 如果a 为空，说明command不存在，则不需要command选项
        if ( a == "" && index != -1 ){
          yamlInfo += '        args:\n'
          const argArr = b.split(/\s+/)
          for (let i = 0; i < argArr.length; i++) {
            let argValue = argArr[i]
            if (Number.isFinite(+argValue)) {
              argValue = '"' + argValue + '"'
            }
            yamlInfo += '        - ' + argValue + '\n'
          }
        }else{
          const commandArr = a.split(/\s+/)
          // commad
          yamlInfo += '        command:\n'
          for (let i = 0; i < commandArr.length; i++) {
            yamlInfo += '        - ' + commandArr[i] + '\n'
          }
          // args
          if (index != -1) {
            yamlInfo += '        args:\n'
            const argArr = b.split(/\s+/)
            for (let i = 0; i < argArr.length; i++) {
              let argValue = argArr[i]
              if (Number.isFinite(+argValue)) {
                argValue = '"' + argValue + '"'
              }
              yamlInfo += '        - ' + argValue + '\n'
            }
          }
        }
      }
      // if (this.temp.content.args.length >= 1 && this.temp.content.args[0].value !== '') {
      // }

      // test修改
      if (this.temp.content.startupCheck) {
        yamlInfo +=
          '        startupProbe:\n' +
          '          httpGet:\n' +
          '            path: ' +
          this.temp.content.startupPath +
          '\n' +
          '            port: ' +
          this.temp.content.servicePort.toString() +
          '\n' +
          '          initialDelaySeconds: 15\n' +
          '          timeoutSeconds: 5\n' +
          '          periodSeconds: 10\n' +
          '          failureThreshold: 60\n'           
      }

      if (this.temp.content.healthyCheck) {
        yamlInfo +=
          '        livenessProbe:\n' +
          '          httpGet:\n' +
          '            path: ' +
          this.temp.content.healthyPath +
          '\n' +
          '            port: ' +
          this.temp.content.servicePort.toString() +
          '\n' +
          '          initialDelaySeconds: 10\n' +
          '          timeoutSeconds: 10\n'
      }

      yamlInfo += '        ports:\n' + '        - containerPort: ' + this.temp.content.servicePort.toString() + '\n'

      if (this.temp.content.warmUpCheck) {
        yamlInfo +=
          '        readinessProbe:\n' +
          '          httpGet:\n' +
          '            path: ' +
          this.temp.content.warmUpPath +
          '\n' +
          '            port: ' +
          this.temp.content.servicePort.toString() +
          '\n' +
          '          initialDelaySeconds: ' +
          this.temp.content.warmUpTime.toString() +
          '\n' +
          '          timeoutSeconds: 10\n'
      }
      yamlInfo +=
        '        lifecycle:\n' +
        '          preStop:\n' +
        '            exec:\n' +
        '              command: [ "/bin/bash", "-c", "sleep 10" ]\n' +
        '        resources:\n' +
        '            limits:\n'
      if (this.temp.content.serviceType === 'gpu') {
        yamlInfo += '              nvidia.com/gpu: 1\n'
      }
      if (this.temp.content.gpu && this.temp.content.idc === '松江切片') {
        yamlInfo +=
          '              tke.cloud.tencent.com/qgpu-core: 10' +
          '\n' +
          '              tke.cloud.tencent.com/qgpu-memory: ' +
          this.temp.content.gpu.toString() +
          '\n'
      }else if(this.temp.content.gpu && this.temp.content.idc !== '松江切片'){
        yamlInfo +=
          '#             tke.cloud.tencent.com/qgpu-core: 10' +
          '\n' +
          '#             tke.cloud.tencent.com/qgpu-memory: ' +
          this.temp.content.gpu.toString() +
          '\n'        
      }else {
        yamlInfo +=
          '#             tke.cloud.tencent.com/qgpu-core: 10\n' + '#             tke.cloud.tencent.com/qgpu-memory: 0\n'
      }

      if (this.temp.content.cpu) {
        yamlInfo += '              cpu: ' + this.temp.content.cpu.toString() + '\n'
      } else {
        yamlInfo += '              cpu: 0\n'
      }

      if (this.temp.content.memory) {
        yamlInfo += '              memory: ' + this.temp.content.memory.toString() + 'Gi\n'
      } else {
        yamlInfo += '              memory: 0Gi\n'
      }

      if (this.temp.content.dirMount.length >= 1 && this.temp.content.dirMount[0].value !== '') {
        yamlInfo += '        volumeMounts:\n'
        for (let i = 0; i < this.temp.content.dirMount.length; i++) {
          let dstPath = this.temp.content.dirMount[i].value
          yamlInfo += '        - name: path' + i.toString() + '\n' + '          mountPath: ' + dstPath + '\n'
        }
        yamlInfo += '      volumes:\n'
        for (let i = 0; i < this.temp.content.dirMount.length; i++) {
          // let srcPathList = this.temp.content.dirMount[i].name.split('/')
          // let srcPath = srcPathList[srcPathList.length - 1]
          yamlInfo +=
            '      - name: path' +
            i.toString() +
            '\n' +
            '        hostPath:\n' +
            '          path: /juicefs-llm-release/' +  this.temp.content.dirCopy +
            '\n' +
            '          type: Directory\n'
        }
      }

      let astr = ''
      let numStr = ''
      let metricsStr = ''
      if (this.temp.content.loadBalance) {
        astr = 'lcp-' + this.temp.content.serviceName + '\n'
        numStr = String(30006) + '\n'
        metricsStr =
          '    - type: External\n' +
          '      external:\n' +
          '       metric:\n' +
          '          name: pending_requests_count\n' +
          '          selector:\n' +
          '            matchLabels:\n' +
          '             app: ' +
          'lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '       target:\n' +
          '         type: AverageValue\n' +
          '         averageValue: 2\n'
      } else {
        astr = this.temp.content.serviceName + '\n'
        numStr = this.temp.content.servicePort.toString() + '\n'
        metricsStr =
          '  - pods:\n' +
          '      metric:\n' +
          '        name: pending_requests_count\n' +
          '      target:\n' +
          '        averageValue: 2\n' +
          '        type: AverageValue\n' +
          '    type: Pods\n'
      }
      if (this.temp.content.serviceType == 'gpu' && this.temp.content.gpuType == 'a100') {
        yamlInfo +=
          '      imagePullSecrets:\n' +
          '        - name: harbor-hawkeye\n' +
          '      tolerations:\n' +
          '      - key: gpu\n' +
          '        value: "a100"\n' +
          '        operator: "Equal"\n' +
          '        effect: "NoSchedule"\n' +
          '      affinity:\n' +
          '        nodeAffinity:\n' +
          '          requiredDuringSchedulingIgnoredDuringExecution:\n' +
          '            nodeSelectorTerms:\n' +
          '              - matchExpressions:\n' +
          '                  - key: gpu\n' +
          '                    operator: In\n' +
          '                    values:\n' +
          '                      - "a100"\n'
      } else if (this.temp.content.serviceType == 'gpu' && this.temp.content.gpuType == '4090D') {
        yamlInfo +=
          '      imagePullSecrets:\n' +
          '        - name: harbor-hawkeye\n' +
          '      tolerations:\n' +
          '      - key: gpu\n' +
          '        value: "4090D"\n' +
          '        operator: "Equal"\n' +
          '        effect: "NoSchedule"\n' +
          '      affinity:\n' +
          '        nodeAffinity:\n' +
          '          requiredDuringSchedulingIgnoredDuringExecution:\n' +
          '            nodeSelectorTerms:\n' +
          '              - matchExpressions:\n' +
          '                  - key: gpu\n' +
          '                    operator: In\n' +
          '                    values:\n' +
          '                      - "4090D"\n'
      } else if (this.temp.content.idc == "俄勒冈" && this.temp.content.gpuType == "l4-a10" && this.temp.content.serviceType == 'gpu'){
        yamlInfo +=
          '      affinity:\n' +
          '        nodeAffinity:\n' +
          '          requiredDuringSchedulingIgnoredDuringExecution:\n' +
          '            nodeSelectorTerms:\n' +
          '              - matchExpressions:\n' +
          '                  - key: gpu\n' +
          '                    operator: In\n' +
          '                    values:\n' +
          '                      - "a10"\n' +
          '                      - "l4"\n' +
          '      imagePullSecrets:\n' +
          '        - name: harbor-hawkeye\n' 
      }else if ((this.temp.content.idc == "俄勒冈" || this.temp.content.idc == '宁夏') && this.temp.content.gpuType == "t4" && this.temp.content.serviceType == 'gpu'){
        yamlInfo +=
          '      affinity:\n' +
          '        nodeAffinity:\n' +
          '          requiredDuringSchedulingIgnoredDuringExecution:\n' +
          '            nodeSelectorTerms:\n' +
          '              - matchExpressions:\n' +
          '                  - key: gpu\n' +
          '                    operator: In\n' +
          '                    values:\n' +
          '                      - "t4"\n' +
          '      imagePullSecrets:\n' +
          '        - name: harbor-hawkeye\n' 
      }else if((this.temp.content.idc == "俄勒冈" || this.temp.content.idc == '宁夏') && this.temp.content.serviceType == 'cpu'){
        yamlInfo +=
          '      tolerations:\n' +
          '        - key: app\n' +
          '          value: "doc-restore"\n' +
          '          operator: "Equal"\n' +
          '          effect: "NoSchedule"\n' +
          '      affinity:\n' +
          '        nodeAffinity:\n' +
          '          requiredDuringSchedulingIgnoredDuringExecution:\n' +
          '            nodeSelectorTerms:\n' +
          '              - matchExpressions:\n' +
          '                  - key: app\n' +
          '                    operator: In\n' +
          '                    values:\n' +
          '                      - doc-restore\n' +
          '      imagePullSecrets:\n' +
          '        - name: harbor-hawkeye\n' 

      }else{
        yamlInfo +=
          '      imagePullSecrets:\n' +
          '        - name: harbor-hawkeye\n'         
      }
      yamlInfo +=
        '---\n' +
        'apiVersion: v1\n' +
        'kind: Service\n' +
        'metadata:\n' +
        '  labels:\n' +
        '    app: ' +
        this.temp.content.serviceName +
        '\n' +
        '  name: ' +
        this.temp.content.serviceName +
        '\n' +
        'spec:\n' +
        '  ports:\n' +
        '  - port: ' +
        this.temp.content.servicePort.toString() +
        '\n' +
        '  selector:\n' +
        '    app: ' +
        this.temp.content.serviceName +
        '\n' +
        '---\n' +
        'apiVersion: networking.k8s.io/v1\n' +
        'kind: Ingress\n' +
        'metadata:\n' +
        '  annotations:\n' +
        '    kubernetes.io/ingress.class: nginx\n' +
        '    nginx.ingress.kubernetes.io/client-body-buffer-size: 10m\n' +
        "    nginx.ingress.kubernetes.io/proxy-body-size: '0'\n" +
        "    nginx.ingress.kubernetes.io/ssl-redirect: 'false'\n" +
        '  name: ' +
        this.temp.content.serviceName +
        '\n' +
        'spec:\n' +
        '  rules:\n' +
        '  - host: ' +
        this.temp.content.serviceName +
        '.ai-internal.intsig.net'+
        '\n' +
        '    http:\n' +
        '      paths:\n' +
        '      - backend:\n' +
        '          service:\n' +
        '            name: ' +
        astr +
        '            port:\n' +
        '              number: ' +
        numStr +
        '        pathType: ImplementationSpecific\n' +
        '---\n' +
        'apiVersion: autoscaling/v2\n' +
        'kind: HorizontalPodAutoscaler\n' +
        'metadata:\n' +
        '  name: ' +
        this.temp.content.serviceName +
        '\n'

      if (this.temp.content.autoCheck) {
        yamlInfo +=
          'spec:\n' +
          '  maxReplicas: ' +
          this.temp.content.maxReplicas.toString() +
          '\n' +
          '  minReplicas: ' +
          this.temp.content.minReplicas.toString() +
          '\n'
      } else {
        yamlInfo += 'spec:\n' + '  maxReplicas: 2\n' + '  minReplicas: 2\n'
      }
      yamlInfo +=
        '  scaleTargetRef:\n' +
        '    apiVersion: apps/v1\n' +
        '    kind: Deployment\n' +
        '    name: ' +
        this.temp.content.serviceName +
        '\n' +
        '  metrics:\n' +
        metricsStr +
        '  behavior:\n' +
        '    scaleDown:\n' +
        '      stabilizationWindowSeconds: 600\n' +
        '      policies:\n' +
        '      - type: Pods\n' +
        '        value: 1\n' +
        '        periodSeconds: 300\n' +
        '      selectPolicy: Min\n' +
        '    scaleUp:\n' +
        '      stabilizationWindowSeconds: 15\n' +
        '      policies:\n' +
        '       - type: Pods\n' +
        '         value: 2\n' +
        '         periodSeconds: 20\n' +
        '      selectPolicy: Max\n'
        
      if (this.temp.content.loadBalance) {
        let str =
          '---' +
          '\n' +
          'apiVersion: apps/v1\n' +
          'kind: Deployment\n' +
          'metadata:\n' +
          '  labels:\n' +
          '    app: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '  name: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          'spec:\n' +
          '  replicas: 3\n' +
          '  selector:\n' +
          '    matchLabels:\n' +
          '      app: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '  template:\n' +
          '    metadata:\n' +
          '      labels:\n' +
          '        app: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '      annotations:\n' +
          "        prometheus.io/port: '" +
          this.temp.content.servicePort.toString() +
          "'\n" +
          "        prometheus.io/scrape: 'true'\n" +
          '    spec:\n' +
          '      affinity:\n' +
          '        podAntiAffinity:\n' +
          '          preferredDuringSchedulingIgnoredDuringExecution:\n' +
          '            - podAffinityTerm:\n' +
          '                labelSelector:\n' +
          '                  matchExpressions:\n' +
          '                    - key: app\n' +
          '                      operator: In\n' +
          '                      values:\n' +
          '                        - lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '                topologyKey: kubernetes.io/hostname\n' +
          '              weight: 100\n' 
          if (this.temp.content.idc == "俄勒冈" || this.temp.content.idc == "宁夏"){
            yamlInfo += 
              '      tolerations:\n' +
              '        - key: server\n' +
              '          value: "lcp"\n' +
              '          operator: "Equal"\n' +
              '          effect: "NoSchedule"\n' +  
              '      affinity:\n' +
              '        nodeAffinity:\n' +
              '          requiredDuringSchedulingIgnoredDuringExecution:\n' +
              '            nodeSelectorTerms:\n' +
              '              - matchExpressions:\n' +
              '                  - key: app\n' +
              '                    operator: In\n' +
              '                    values:\n' +
              '                      - lcp\n'             
          }
          if (this.temp.content.idc == "俄勒冈"){
            yamlInfo +=
              '      containers:\n' +
              '      - image: 150184970070.dkr.ecr.us-west-2.amazonaws.com/aws_ecr/acg-sre/lcp:337aa73c\n'
          }else if (this.temp.content.idc == "宁夏"){
            yamlInfo +=
              '      containers:\n' +
              '      - image: 090646384768.dkr.ecr.cn-northwest-1.amazonaws.com.cn/aws_ecr/acg-sre/lcp:337aa73c\n'
          }else{
            yamlInfo +=
              '      containers:\n' +
              '      - image: registry.intsig.net/acg-sre/lcp:337aa73c\n'
          }
          yamlInfo += 
          '        name: lcp-least-conn-proxy\n' +
          '        ports:\n' +
          '        - containerPort: 30006\n' +
          '          protocol: TCP\n' +
          '        args:\n' +
          '        - "-service"\n' +
          '        - ' +
          this.temp.content.serviceName +
          '\n' +
          '        - "-lease-duration"\n' +
          '        - "3s"\n' +
          '        - "-renew-deadline"\n' +
          '        - "2s"\n' +
          '        - "-retry-period"\n' +
          '        - "1s"\n' +
          '        - "-p"\n' +
          '        - "30006"\n' +
          '        env:\n' +
          '        - name: LEASE_IDENTITY\n' +
          '          valueFrom:\n' +
          '            fieldRef:\n' +
          '              fieldPath: status.podIP\n' +
          '      imagePullSecrets:\n' +
          '      - name: harbor-hawkeye\n' +
          '      serviceAccountName: list-endpoints-sa\n' +
          '---\n' +
          'apiVersion: v1\n' +
          'kind: Service\n' +
          'metadata:\n' +
          '  name: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '  labels:\n' +
          '    app: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          'spec:\n' +
          '  ports:\n' +
          '    - port: 30006\n' +
          '  selector:\n' +
          '    app: lcp-' +
          this.temp.content.serviceName +
          '\n' +
          '---\n'
        yamlInfo = str.concat(yamlInfo)
      }
      this.temp.content.yaml = yamlInfo
    },
    updateComment(){
      const { idc, serviceName } = this.temp.content;
      if (!idc || !serviceName) {
        this.temp.comment = '';
        return;
      }
      switch (this.temp.content.idc) {
        case '松江':
          this.temp.comment =
            "服务请求方式：curl -H 'host: " +
            this.temp.content.serviceName +
            ".ai-internal.intsig.net"+
            "' **********:80/\n" +
            '服务监控地址：https://grafana-autom.intsig.net/d/vbEW1BgVz/ocr-online?orgId=5&from=now-24h&to=now&var-range=$__auto_interval_range&var-DS_PROMETHEUS=songjiang&var-ocr_app=' +
            this.temp.content.serviceName +
            '&var-cksource=ucloud\n' +
            '权限申请地址：https://cloud.intsig.net/workflow/grafana-auth 组织OCR\n' +
            '日志查询方式：https://doc.intsig.net/pages/viewpage.action?pageId=873234797'
          break
        case '颛桥':
          this.temp.comment =
            "服务请求方式：curl -H 'host: " +
            this.temp.content.serviceName +
            ".ai-internal.intsig.net"+
            "' **********:30080/\n" +
            '服务监控地址：https://grafana-autom.intsig.net/d/vbEW1BgVz/ocr-online?orgId=5&from=now-24h&to=now&var-range=$__auto_interval_range&var-DS_PROMETHEUS=green&var-ocr_app=' +
            this.temp.content.serviceName +
            '&var-cksource=ucloud\n' +
            '权限申请地址：https://cloud.intsig.net/workflow/grafana-auth 组织OCR\n' +
            '日志查询方式：https://doc.intsig.net/pages/viewpage.action?pageId=873234797'
          break
        case '俄勒冈':
          this.temp.comment =
            "服务请求方式：curl -H 'host: " +
            this.temp.content.serviceName +
            ".ai-internal.intsig.net"+
            "' k8s-ingressn-ingressn-b1ef1b174c-130cfe720ad0237f.elb.us-west-2.amazonaws.com/\n" +
            '服务监控地址：https://grafana-autom.intsig.net/d/vbEW1BgVz/ocr-online?orgId=5&refresh=5s&var-DS_PROMETHEUS=aei9ybdbloxdsa&var-cksource=KxWaPsQnk&var-ocr_app=All&var-range=$__auto_interval_range' +
            this.temp.content.serviceName +
            '&var-cksource=ck-oregon\n' +
            '权限申请地址：https://cloud.intsig.net/workflow/grafana-auth 组织OCR\n' +
            '日志查询方式：https://doc.intsig.net/pages/viewpage.action?pageId=873234797'
          break
        case '宁夏':
          this.temp.comment =
            "服务请求方式：curl -H 'host: " +
            this.temp.content.serviceName +
            ".ai-internal.intsig.net"+
            "' k8s-ingressn-ingressn-9a82968dfe-f5820bc6dd4562e8.elb.cn-northwest-1.amazonaws.com.cn/\n" +
            '服务监控地址：https://grafana-autom.intsig.net/d/vbEW1BgVz/ocr-online?orgId=5&refresh=5s&var-DS_PROMETHEUS=dek8f616x8zr4d&var-cksource=KxWaPsQnk&var-ocr_app=All&var-range=$__auto_interval_range' +
            this.temp.content.serviceName +
            '&var-cksource=ningxia\n' +
            '权限申请地址：https://cloud.intsig.net/workflow/grafana-auth 组织OCR\n' +
            '日志查询方式：https://doc.intsig.net/pages/viewpage.action?pageId=873234797'
          break
        case '松江切片':
          this.temp.comment =
            "服务请求方式：curl -H 'host: " +
            this.temp.content.serviceName +
            ".ai-internal.intsig.net"+
            "' http://************:30080\n" +
            '服务监控地址：https://grafana-autom.intsig.net/d/vbEW1BgVz/ocr-online?orgId=5&refresh=5s&var-DS_PROMETHEUS=d2c3fdd5-c38a-46f9-9b99-2bde321ff798&var-cksource=KxWaPsQnk&var-ocr_app=All&var-range=$__auto_interval_range' +
            this.temp.content.serviceName +
            '&var-cksource=ucloud\n' +
            '权限申请地址：https://cloud.intsig.net/workflow/grafana-auth 组织OCR\n' +
            '日志查询方式：https://doc.intsig.net/pages/viewpage.action?pageId=873234797'
          break
      }
    },
    createData(){
      this.serviceCheck()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if ( this.temp.content.recordType === 'add' && this.serviceNameCheck === false){
            notification.error({
              message: '服务名称已存在',
              description: '请重新输入服务名称',
            })
          }else if (this.temp.content.recordType === 'update' && this.serviceNameCheck === true) {
            notification.error({
              message: '服务不存在',
              description: '请新增该服务',
            })
          }else{
            this.Date()
            this.node_status = 0
            this.temp.node = 1
            this.temp.status = 1
            this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
            this.temp.content = { 
              data: JSON.stringify(this.temp.content),
            }
            createOrder(this.temp).then(response => {
              if (response === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else {
                notification.success({
                  message: '创建成功',
                  description: '工单创建成功',
                })
                this.node_status = 1
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                this.$router.push({ path: '/workflow/gpu-service-apply', query: { id: response.Data.id } })
                // 保存上一次提交的id
                this.saveLastOrderId(response.Data.id)
                console.log("doing add if ")
                console.log("here is this.temp.recordType",this.temp.recordType)
                console.log("here is this.temp.content.recordType",this.temp.content.recordType)
                if(this.temp.content.recordType === "add"){
                  console.log("add if success")
                  this.addRecord()
                }
                if(this.temp.content.recordType === "update"){
                  this.updateRecord()
                }
              }
            })
        }
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    copyDirData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          const requestParameters = {
            email: store.getters.email,
            content: JSON.stringify(this.temp.content),
          }
          gpuApplyCron(requestParameters).then(response => {
            if (response === undefined) {
              notification.error({
                message: '目录复制失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '目录复制成功',
                description: '目录复制任务执行处理中',
              })
            }
          })
        }
      })
    },
    imageCache() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          const requestParameters = {
            idc: this.temp.content.idc,
            image: this.temp.content.image,
            email: store.getters.email,
          }
          imageCacheCron(requestParameters).then(response => {
            if (response === undefined) {
              notification.error({
                message: '镜像缓存失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '镜像缓存成功',
                description: '镜像缓存任务执行处理中',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    // addCommandRow() {
    //   this.temp.content.command.push({
    //     value: '',
    //   })
    // },
    // removeCommandRow(i) {
    //   if (this.temp.content.command.length > 1) {
    //     this.temp.content.command.splice(i, 1)
    //   }
    // },
    // addArgsRow() {
    //   this.temp.content.args.push({
    //     value: '',
    //   })
    // },
    // removeArgsRow(i) {
    //   if (this.temp.content.args.length > 1) {
    //     this.temp.content.args.splice(i, 1)
    //   }
    // },
    addEnvRow() {
      this.temp.content.env.push({
        name: '',
        value: '',
      })
    },
    removeEnvRow(i) {
      if (this.temp.content.env.length > 1) {
        this.temp.content.env.splice(i, 1)
      }
    },
    addDirMountRow() {
      this.temp.content.dirMount.push({
        name: '',
        value: '',
        hostMenuRoot: '',
        defineName: '',
      })
    },
    removeDirMountRow(i) {
      if (this.temp.content.dirMount.length > 1) {
        this.temp.content.dirMount.splice(i, 1)
      }
    },
  },
  watch: {
    'temp.content.idc'(idc) {
      // 根据机房设置默认GPU类型
      switch(idc) {
        case '俄勒冈':
        case '宁夏':
          this.temp.content.gpuType = 't4';
          break;
        default:
          this.temp.content.gpuType = 'l4-a10';  // 其他机房默认值
      }
    },
    'temp.content.idc': {
      handler: 'updateComment',
      immediate: true
    },
  },

}
</script>

<style lang="less">
.myTextarea[disabled]:hover {
  background-color: #fff !important;
  border-right-width: 1px !important;
}

/* YAML差异比较样式 */
.diff-container {
  font-family: monospace;
  white-space: pre;
  font-size: 14px;
  line-height: 1.5;
  padding: 10px;
}

.diff-line {
  padding: 2px 5px;
}

.diff-added {
  background-color: #e6ffed;
  color: #22863a;
}

.diff-removed {
  background-color: #ffeef0;
  color: #cb2431;
}

.diff-unchanged {
  color: #24292e;
}


.ant-input[disabled] {
  border: none !important;
  background-color: #fff !important;
  border: none !important;
  box-shadow: none;
  color: rgba(0, 0, 0) !important;
  cursor: not-allowed;
  opacity: 1;
}
.hostMenus {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  transition: all 0.3s;
  border-right: 0;
  width: 170px !important;
  margin: 0;
  box-sizing: border-box;
}

.diff-container {
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.diff-line {
  padding: 2px 4px;
  margin: 1px 0;
  border-radius: 3px;
}

.diff-added {
  background-color: #e6ffec;
  border-left: 3px solid #28a745;
  color: #28a745;
}

.diff-removed {
  background-color: #ffeef0;
  border-left: 3px solid #d73a49;
  color: #d73a49;
}

.diff-unchanged {
  background-color: #f6f8fa;
  color: #586069;
}
</style>
