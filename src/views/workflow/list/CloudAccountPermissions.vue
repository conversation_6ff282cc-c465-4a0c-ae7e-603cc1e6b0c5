<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="用户提交申请理由" />
        <a-step title="上级领导填写内容" />
        <a-step title="隔级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        layout="horizontal"
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="云厂商" name="supplier">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-radio-group v-model:value="temp.content.supplier" button-style="solid" @change="supplerChange">
                <a-radio-button value="苏州Aliyun">启信宝-阿里云</a-radio-button>
                <a-radio-button value="Aliyun">合合-阿里云</a-radio-button>
              </a-radio-group>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="用户名称" name="accountName">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-input v-model:value="temp.content.accountName" placeholder="" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="权限" name="permissions">
          <a-row :gutter="24">
            <a-col :span="18">
              <a-transfer
                v-model:target-keys="temp.content.permissions"
                :data-source="supplerAllPermission"
                :list-style="{
                  width: '400px',
                  height: '300px',
                }"
                :titles="['可供选择', '已选择']"
                :operations="['添加', '撤回']"
                :render="item => item.description"
                show-search
                :filter-option="filterOption"
                @change="handleChange"
                @search="handleSearch"
              />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="控制台登陆" name="webLogin">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-radio-group v-model:value="temp.content.webLogin">
                <a-radio :value="1">不需要或已开通</a-radio>
                <a-radio :value="2">开启</a-radio>
              </a-radio-group>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="AKSK访问" name="accessKey">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-radio-group v-model:value="temp.content.accessKey">
                <a-radio :value="1">不需要或已有AKSK</a-radio>
                <a-radio :value="2">新增</a-radio>
              </a-radio-group>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/评论">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(2)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(3)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'

import { PublicAccountListPolicy } from '@/api/security/account'

const statusMap = {
  2: {
    status: 'error',
    text: '未执行',
  },
  1: {
    status: 'success',
    text: '成功',
  },
}

export default {
  name: 'CloudAccountPermissions',
  components: {},
  data: function () {
    const filterOption = (inputValue, option) => {
      return option.description.indexOf(inputValue) > -1
    }
    return {
      filterOption,
      fetching: false,
      userEmailList: [],
      value: [],
      pagination: {
        defaultPageSize: 1000,
        hideOnSinglePage: true,
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      size: 'small',
      loading: false,
      ipWhiteGroupNameList: [],
      supplerAllPermission: [],
      node_status: 1,
      temp: {
        id: '',
        orderType: '公有云账号权限',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          accountName: undefined,
          supplier: '苏州Aliyun',
          webLogin: 1,
          accessKey: 1,
          permissions: [],
          permissions_type: [],
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        supplier: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
        accountName: [{ required: true, message: '请输入用户名称', trigger: 'blur' }],
        permissions: [{ required: true, message: '请选择权限', trigger: 'blur' }],
        webLogin: [{ required: true, message: '请选择是否允许web登录', trigger: 'blur' }],
        accessKey: [{ required: true, message: '请选择是否允许访问密钥', trigger: 'blur' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
    this.temp.content.accountName = store.getters.email.split('@')[0] + '@bertadata.onaliyun.com'
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
          this.getAccountPolicyList()
        })
        this.nodeStatus = 1
      }
    },
    getAccountPolicyList() {
      let params = {}
      switch (this.temp.content.supplier) {
        case '苏州Aliyun':
          params = { Suppier: 'aliyun' }
          break
        case 'Aliyun':
          params = { Suppier: 'aliyun' }
          break
        case 'AWS(海外)':
          params = { Suppier: 'aws' }
          break
        case 'AWS(合合)':
          params = { Suppier: 'aws' }
          break
        case 'AWS(CS)':
          params = { Suppier: 'aws' }
          break
      }
      PublicAccountListPolicy(params).then(response => {
        this.supplerAllPermission = response.Data.data
      })
    },
    supplerChange() {
      let username = store.getters.email.split('@')[0]
      switch (this.temp.content.supplier) {
        case '苏州Aliyun':
          this.temp.content.accountName = username + '@bertadata.onaliyun.com'
          break
        case 'Aliyun':
          this.temp.content.accountName = username + '@intsig.onaliyun.com'
          break
      }
    },

    handleChange(keys, direction, moveKeys) {
      if (direction == 'left') {
        this.temp.content.permissions_type.forEach((item, index) => {
          if (moveKeys.includes(item.key)) {
            this.temp.content.permissions_type.splice(index, 1)
          }
        })
      } else {
        this.supplerAllPermission.forEach(item => {
          if (moveKeys.includes(item.key)) {
            this.temp.content.permissions_type.push(item)
          }
        })
      }
    },

    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/cloud-account-permission', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData(node) {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = node
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      this.Date()
      this.temp.node = 5
      this.temp.status = 20
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '审批完成',
            description: '该审批已被拒绝',
          })
        }
      })
    },
    revokeData() {
      this.temp.node = 5
      this.temp.status = 30
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '撤回失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          this.node_status = 1
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          notification.success({
            message: '撤回成功',
            description: '该工单被申请人撤回',
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less">
.dataBaseTable {
  /deep/.ant-table-header {
    font-size: 12px;
  }
  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  /deep/ .ant-table-tbody {
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.jsoneditor-vue {
  height: 100%;
}
</style>
