<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item label="目标机器" name="hosts">
              <a-textarea
                v-if="temp.node !== 0 || temp.rid !== ''"
                readOnly
                v-model:value="temp.content.hosts"
                :auto-size="{ minRows: 2, maxRows: 5 }"
              />
              <div v-if="temp.node === 0 && temp.rid === ''">
                <tx-button type="primary" @click="chooseAsset">选择目标机器</tx-button>
                <span style="margin-left: 8px">
                  <template v-if="hasSelected">
                    {{ `已选数量： ${selectedData.length}` }}
                  </template>
                </span>
              </div>
            </a-form-model-item>
            <a-form-model-item label="任务名称" name="task_id">
              <a-select
                v-model:value="temp.content.task_id"
                placeholder="请选择任务名称"
                :options="taskNameList"
                @change="taskChange"
                allowClear
              ></a-select>
            </a-form-model-item>
            <a-form-model-item name="extra_params">
              <template #label>
                额外参数
                <a-tooltip placement="right">
                  <template #title>
                    <span>
                      使用脚本的任务需要给脚本传递参数,英文逗号分隔
                      <br />
                      对于任务中配置的脚本,示例:python xx.py a1 a2 a3
                    </span>
                  </template>
                  <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle" />
                </a-tooltip>
              </template>
              <a-textarea
                v-model:value="temp.content.extra_params"
                placeholder="仅填入参数:a1,a2,a3,a4"
                :auto-size="{ minRows: 2, maxRows: 5 }"
              />
            </a-form-model-item>
            <a-form-model-item name="extra_var_map">
              <template #label>
                额外变量
                <a-tooltip placement="right">
                  <template #title>
                    <span>
                      ansible-playbook通过-e传入的变量
                      <br />
                      key,value的对象,示例:{"name":"xx"}
                    </span>
                  </template>
                  <a-icon type="question-circle-o" style="margin-left: 4px; vertical-align: middle" />
                </a-tooltip>
              </template>
              <a-textarea
                v-model:value="temp.content.extra_var_map"
                placeholder='请输入其他参数，示例：{"name":"xx"}'
                :auto-size="{ minRows: 2, maxRows: 5 }"
              />
            </a-form-model-item>
            <a-form-model-item name="is_admin_user" label="is_admin">
              <a-radio-group v-model:value="temp.content.is_admin_user" button-style="solid">
                <a-radio value="1">Yes</a-radio>
                <a-radio value="2">No</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item name="forks" label="Fork">
              <a-input v-model:value="temp.content.forks" placeholder="请输入 fork 数量" />
            </a-form-model-item>
            <a-form-model-item name="email" label="通知人">
              <a-input placeholder="<EMAIL>" v-model:value="temp.content.email" />
            </a-form-model-item>
            <a-form-model-item label="是否为紧急授权">
              <a-radio-group v-model:value="temp.content.isUrgent" button-style="solid">
                <a-popconfirm
                  title="紧急授权为TL审批通过后即执行任务，并通知运维管理员。"
                  ok-text="我已经知晓"
                  @confirm="confirm"
                >
                  <a-radio-button value="true">是</a-radio-button>
                </a-popconfirm>
                <a-radio-button value="false">否</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item name="comment" label="申请理由">
              <a-textarea v-model:value="temp.content.comment" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="detailVisible">
            <a-descriptions :column="2" title="任务信息">
              <a-descriptions-item label="ID">{{ detailData.id }}</a-descriptions-item>
              <a-descriptions-item label="任务名称">{{ detailData.name || '/' }}</a-descriptions-item>
              <a-descriptions-item label="创建用户">{{ detailData.creator }}</a-descriptions-item>
              <a-descriptions-item label="任务类别">
                {{
                  detailData.task_classification === 1
                    ? 'ansible'
                    : detailData.task_classification === 2
                      ? 'playbook'
                      : '未知'
                }}
              </a-descriptions-item>
              <a-descriptions-item label="模块名称">{{ detailData.module_name || '/' }}</a-descriptions-item>
              <a-descriptions-item label="脚本类型">
                {{
                  detailData.ansible_type === 0
                    ? '/'
                    : detailData.ansible_type === 1
                      ? 'shell'
                      : detailData.ansible_type === 2
                        ? 'python'
                        : '未知'
                }}
              </a-descriptions-item>
              <a-descriptions-item label="其他参数">
                <template v-for="(value, key) in detailData.module_var_map" :key="`${key}=${value}`">
                  <a-tooltip v-if="`${key}=${value}`.length > 20" :title="`${key}=${value}`" placement="left">
                    <a-tag style="margin: 2px" color="orange">
                      {{ `${`${key}=${value}`.slice(0, 20)}...` }}
                    </a-tag>
                  </a-tooltip>
                  <a-tag v-else style="margin: 2px" color="orange">
                    {{ `${key}=${value}` }}
                  </a-tag>
                </template>
                {{ detailData.module_var_map ? '' : '/' }}
              </a-descriptions-item>
              <a-descriptions-item label="备注信息">{{ detailData.result || '/' }}</a-descriptions-item>
            </a-descriptions>
            <a-descriptions :column="1">
              <a-descriptions-item label="模块参数" v-if="!detailEditorOptVisible">
                {{ detailData.module_params || '/' }}
              </a-descriptions-item>
              <a-descriptions-item label="任务内容" v-if="detailEditorOptVisible">
                {{ detailEditorOptVisible ? '' : detailData.content }}
              </a-descriptions-item>
            </a-descriptions>
            <div v-if="containerReload && detailEditorOptVisible" style="height: 310px">
              <ExecutionEditor :opts="detailEditorOpt" />
            </div>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24" style="text-align: center">
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/workflow/createWorkflow">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="
                temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-if="
                (temp.handlerEmail.includes(localUser) || temp.founderEmail.includes(localUser) || hasAdminRole) &&
                temp.node === 3 &&
                temp.status === 10
              "
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="viewResult">运行结果</tx-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <a-modal :zIndex="1049" :visible="chooseAssetVisible" width="80%" title="选择服务器" :closable="false">
        <div>
          <a-form layout="inline">
            <a-row :gutter="12">
              <a-col :md="8" :sm="24">
                <a-form-item label="模糊查询">
                  <a-input
                    v-model:value="assetListQuery.searchKey"
                    placeholder="服务器名/IP/公网IP/物理机IP/UUID"
                    style="width: 250px"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="IDC">
                  <a-select
                    v-model:value="assetListQuery.idc"
                    style="width: 250px"
                    placeholder="请选择"
                    :options="assetIdcList"
                    allowClear
                    showSearch
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item>
                  <tx-button type="primary" @click="$refs.assetTable.refresh(true)">查询</tx-button>
                  <tx-button
                    type="primary"
                    :disabled="!hasSelected"
                    :loading="loading"
                    @click="start"
                    style="margin-left: 20px"
                  >
                    清空已选
                  </tx-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="已选择ip：">
              <a-tag v-for="(tag, index) in selectedData" :key="index" color="blue" size="small" class="tag-item">
                {{ `${tag.ip}:${tag.uuid}` }}
              </a-tag>
            </a-form-item>
          </a-form>
          <s-table
            ref="assetTable"
            :rowKey="record => record.uuid"
            :columns="assetColumns"
            :data="loadAssetData"
            :rowSelection="rowSelection"
            height="350"
          ></s-table>
        </div>
        <template #footer>
          <tx-button key="submit" type="primary" @click="submitChooseAsset">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getCronScheduleTaskInfo, getCronScheduleTaskNameList } from '@/api/cron/cron_schedule_task'
import { getAssetListIdc, getAssetList } from '@/api/asset'
import ExecutionEditor from '@/views/cron/modules/comp/ExecutionEditor.vue'
import cloneDeep from 'lodash.clonedeep'
import { getUserList } from '@/api/permission/user'

const assetColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true,
  },
  {
    title: 'UUID',
    dataIndex: 'uuid',
  },
  {
    title: '服务器名',
    dataIndex: 'hostname',
  },
  {
    title: '机房',
    dataIndex: 'idc',
  },
  {
    title: 'ip地址',
    dataIndex: 'ip',
  },
  {
    title: '业务标签',
    dataIndex: 'business',
  },
  {
    title: '应用标签',
    dataIndex: 'application',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'CronJobExecute',
  components: {
    STable,
    ExecutionEditor,
  },
  provide() {
    return {
      reload: this.reload,
    }
  },
  data: function () {
    this.assetColumns = assetColumns
    this.pagination = pagination
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        rid: '',
        orderType: '作业执行',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          hosts: '',
          extra_params: '',
          extra_var_map: '',
          is_admin_user: '1',
          forks: '5',
          email: store.getters.email,
          isUrgent: 'false',
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.task_id': [{ required: true, message: '请选择任务', trigger: 'change' }],
        'content.forks': [{ required: true, message: '请输入 forks 数', trigger: 'blur' }],
        'content.email': [{ required: true, message: '请填写通知人', trigger: 'blur' }],
        'content.comment': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      loading: false,
      containerReload: true,
      detailData: {},
      detailVisible: false,
      detailEditorOptVisible: false,
      detailEditorOpt: {
        readOnly: true,
        language: 'yaml',
        value: '',
      },
      assetIdcList: [],
      taskNameList: [],
      selectedRowKeys: [],
      selectedRows: [],
      selectedData: [],
      currentPageKeys: [],
      chooseAssetVisible: false,
      totalAsset: 0,
      assetListQuery: {},
      arrIdLabelMap: {},
      hasAdminRole: false,
      loadAssetData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.assetListQuery)
        // 仅查询运行中的机器
        requestParameters.status = 1
        return getAssetList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            const rsp = {
              data: res.Data.data || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage,
            }
            this.currentPageKeys = []
            for (const record of rsp.data) {
              this.currentPageKeys.push(record.uuid)
            }
            const selectedRowKeys = []
            for (const record of this.selectedData) {
              if (this.currentPageKeys.includes(record.uuid)) {
                selectedRowKeys.push(record.uuid)
              }
            }
            this.selectedRowKeys = selectedRowKeys
            return rsp
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  mounted() {
    this.getTaskNameList()
    this.getAssetListIdc()
    this.getInfo()
    this.checkAdminRole()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
    hasSelected() {
      return this.selectedData.length > 0
    },
  },
  methods: {
    async reload() {
      this.containerReload = false
      await this.$nextTick()
      this.containerReload = true
    },
    start() {
      this.loading = true
      // ajax request after empty completing
      setTimeout(() => {
        this.loading = false
        this.selectedRowKeys = []
        this.selectedData = []
      }, 500)
    },
    checkAdminRole() {
      getUserList({ searchText: store.getters.email }).then(response => {
        const roles = response.Data.data[0].roles
        if (roles.includes('admin')) {
          this.hasAdminRole = true
        }
      })
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = this.handleGetInfo(response.Data)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp.rid = this.$route.query.rid
            const orderInfo = this.handleGetInfo(response.Data)
            this.temp.content = orderInfo.content
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    handleGetInfo(data) {
      data.content = JSON.parse(data.content['data'])
      delete data.content['data']
      if (data.content.task_id) {
        this.taskChange(data.content.task_id)
      }
      if (data.content.hosts) {
        data.content.hosts = data.content.hosts.join(',')
      }
      if (!data.content.isUrgent) {
        data.content.isUrgent = 'false'
      }
      return data
    },
    handleSubmitInfo(requestParameters) {
      requestParameters.handler = store.getters.name
      requestParameters.handlerEmail = store.getters.email
      requestParameters.content.hosts = requestParameters.content.hosts
        ? requestParameters.content.hosts.split(',')
        : null
      requestParameters.content = { data: JSON.stringify(requestParameters.content) }
      return requestParameters
    },
    getTaskNameList() {
      getCronScheduleTaskNameList().then(res => {
        if (!res.Data.task_names) {
          return
        }
        var arr = []
        var arrIdMap = {}
        var arrIdLabelMap = {}
        for (var i = 0, len = res.Data.task_names.length; i < len; i++) {
          const creator = res.Data.task_names[i].creator
          const label = res.Data.task_names[i].name + '-' + creator.substring(0, creator.indexOf('@'))
          arr.push(label)
          arrIdMap[label] = res.Data.task_names[i].id
          arrIdLabelMap[res.Data.task_names[i].id] = label
        }
        arr = Array.from(new Set(arr))
        for (i = 0, len = arr.length; i < len; i++) {
          this.taskNameList.push({ value: arrIdMap[arr[i]], label: arr[i] })
        }
        this.arrIdLabelMap = arrIdLabelMap
      })
    },
    getAssetListIdc() {
      getAssetListIdc().then(res => {
        if (!res.Data.idc) {
          return
        }
        for (var i = 0, len = res.Data.idc.length; i < len; i++) {
          var idc = {}
          idc.value = res.Data.idc[i]
          idc.label = res.Data.idc[i]
          this.assetIdcList.push(idc)
        }
      })
    },
    // 任务详细信息
    taskChange(taskId) {
      if (!taskId) {
        return
      }
      getCronScheduleTaskInfo(taskId).then(response => {
        this.detailData = response.Data
        const task = this.detailData
        if ((task.task_classification === 1 && task.module_name === 'script') || task.task_classification === 2) {
          this.detailEditorOptVisible = true
          let language = ''
          if (task.task_classification === 2) {
            language = 'yaml'
          } else if (task.ansible_type === 1) {
            language = 'shell'
          } else if (task.ansible_type === 2) {
            language = 'python'
          }
          this.detailEditorOpt = {
            language: language,
            value: task.content,
          }
        } else {
          this.detailEditorOptVisible = false
        }
        this.detailVisible = true
      })
    },
    submitChooseAsset() {
      this.chooseAssetVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      if (this.selectedData.length > 0) {
        this.selectedData = this.MergeArray(this.selectedData, selectedRows)
      } else {
        this.selectedData = selectedRows
      }
    },
    MergeArray(arr1, arr2) {
      var _arr = []
      for (var i = 0; i < arr1.length; i++) {
        _arr.push(arr1[i])
      }
      for (var x = 0; x < arr2.length; x++) {
        var flag = true
        for (var j = 0; j < arr1.length; j++) {
          if (arr2[x].uuid === arr1[j].uuid) {
            flag = false
            break
          }
        }
        if (flag) {
          _arr.push(arr2[x])
        }
      }
      _arr = this.spliceArray(_arr, this.selectedRowKeys)
      return _arr
    },
    // 删除未选择数据
    spliceArray(selectedData, selectedRowKeys) {
      const notChooseKeys = []
      for (const key of this.currentPageKeys) {
        if (!selectedRowKeys.includes(key)) {
          notChooseKeys.push(key)
        }
      }
      const _arr = []
      for (let x = 0; x < selectedData.length; x++) {
        let needDeleted = false
        for (let j = 0; j < notChooseKeys.length; j++) {
          if (selectedData[x].uuid === notChooseKeys[j]) {
            needDeleted = true
            break
          }
        }
        if (!needDeleted) {
          _arr.push(selectedData[x])
        }
      }
      return _arr
    },
    chooseAsset() {
      this.chooseAssetVisible = true
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.selectedData.length === 0 && this.temp.rid === '') {
            alert('请选择目标主机')
            return
          }
          if (this.temp.content.forks > 10) {
            alert('forks数不能超过10')
            return
          }
          this.Date()
          this.node_status = 0
          const requestParameters = cloneDeep(this.temp)
          try {
            JSON.parse(requestParameters.extra_var_map || '{}')
          } catch (e) {
            alert('额外变量格式错误, 示例：{"filename":"test"}\n' + e)
            return
          }
          requestParameters.node = 1
          requestParameters.status = 1
          requestParameters.timeline = [
            this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now,
          ]
          var hosts = []
          this.selectedData.forEach(item => {
            hosts.push(item.ip + ':' + item.uuid)
          })
          if (this.temp.rid !== '' && requestParameters.content.hosts !== '') {
            hosts = requestParameters.content.hosts.split(',')
          }
          requestParameters.content.hosts = hosts.length > 0 ? hosts : null
          const label = this.arrIdLabelMap[requestParameters.content.task_id]
          requestParameters.content.name = label.substring(0, label.lastIndexOf('-'))
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          createOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.temp.rid = ''
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = this.handleGetInfo(response.Data)
              this.node_status = 1
              this.$router.push({ path: '/workflow/job-execute', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 2
          requestParameters.status = 1
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 10
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 20
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 30
          approveOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    viewResult() {
      this.$router.push({ path: '/cron/schedule/execution' })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    confirm() {
      this.temp.content.isUrgent = 'true'
    },
  },
}
</script>

<style scoped></style>
