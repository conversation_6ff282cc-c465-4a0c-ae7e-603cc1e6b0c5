<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="运维预审" />
        <a-step title="安全审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px; margin-bottom: 8px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :form="form"
        :rules="rules"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item label='填写说明' :colon="false">
          <div>
            <a-tooltip
              placement="right"
              :overlayStyle="{ maxWidth: '50%', whiteSpace: 'normal' }"
            >
              <template #title>
                <div style="word-wrap: break-word;">
                  1、策略方向默认相对服务器IP为网卡入方向（重保系统必须填写出站规则）；优先级默认10，一般无需修改；<br>
                  2、授权对象仅支持IP+子网掩码长度形式：如************/30 表示************-19 四个ip地址、*************/32表示*************一个ip<br>
                  3、以下场景需额外说明原因或开通必要性(阐述不清可能被拒绝审批)：<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;a、测试环境需要与生产环境通信的<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;b、核心区（如数据库等）需要开通公网访问的<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;c、需要跨业务互相访问的<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;d、办公室需要直接与数据中心机房通信的（使用应用层网关代理除外）<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;e、涉及重保系统与重保系统外部通信并且需要ip协议全部放通的<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;f、授权对象子网掩码长度小于29的（IP数量大于8）
                </div>
              </template>
              <a-icon type="question-circle-o" style="margin-left: 1px;" />
            </a-tooltip>
          </div>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-card
          v-for="(policy, policyIndex) in this.temp.content.polices"
          :key="policyIndex"
          style="margin-top: 8px"
        >
          <a-form-model-item :name="['polices', policyIndex, 'ipList']" label="服务器IP">
            <a-select
              v-model:value="policy.ipList"
              mode="tags"
              @search="handleSearch"
              @change="(value, option) => handleChange(value, option, policyIndex)"
              placeholder="选择服务器IP"
            >
              <a-select-option v-for="i in info" :key="i.ip">
                {{ i.ip + '(' + i.hostname + ')' }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="通信场景">
            <a-tag v-for="tag in policy.sceneTags" :key="tag.key" :color="tag.color">{{ tag.key }}</a-tag>
          </a-form-model-item>
          <a-form-model-item label="操作">
            <a-radio-group v-model:value="policy.action" button-style="solid" disabled>
              <a-radio-button value="新增">新增</a-radio-button>
              <a-radio-button value="更新">更新</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item :name="['polices', policyIndex, 'isExpired']" label="有效期限">
            <a-radio-group v-model:value="policy.isExpired" button-style="solid">
              <a-radio-button value="long">长期</a-radio-button>
              <a-radio-button value="short">临时</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item v-if="policy.isExpired === 'short'" :name="['polices', policyIndex, 'expiration']"
                             label="使用截止日期">
            <a-date-picker v-model:value="policy.expiration" valueFormat="YYYY-MM-DD" />
          </a-form-model-item>

          <a-space
            v-for="(p, ruleIndex) in policy.data"
            :key="ruleIndex"
            style="display: flex; margin-bottom: 8px"
            align="baseline"
          >
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'direction']"
              :rules="[{ required: true, message: '请选择方向' }]"
            >
              <a-select
                v-model:value="p.direction"
                style="width: 100px"
                placeholder="方向"
              >
                <a-select-option value="in">入方向</a-select-option>
                <a-select-option value="out">出方向</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'authPolicy']"
              :rules="[{ required: true, message: '请选择授权策略' }]"
            >
              <a-select
                v-model:value="p.authPolicy"
                style="width: 120px"
                placeholder="授权策略"
              >
                <a-select-option value="allow">允许</a-select-option>
                <a-select-option value="reject">拒绝</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'priority']"
              :rules="[
            { required: true, message: '请输入优先级' },
            { type: 'number', min: 1, max: 100, message: '优先级范围1-100' }
          ]"
            >
              <a-input-number
                v-model:value="p.priority"
                placeholder="优先级"
                :min="1"
                :max="100"
              />
            </a-form-model-item>
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'protocolType']"
              :rules="[{ required: true, message: '请选择协议类型' }]"
            >
              <a-select
                v-model:value="p.protocolType"
                style="width: 100px"
                placeholder="协议类型"
              >
                <a-select-option value="tcp">TCP</a-select-option>
                <a-select-option value="udp">UDP</a-select-option>
                <a-select-option value="icmp">ICMP</a-select-option>
                <a-select-option value="all">全部</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'portRange']"
              :rules="[
            { required: true, message: '请输入端口范围' },
            { validator: validatePortRange }
          ]"
            >
              <a-input
                v-model:value="p.portRange"
                style="width: 150px"
                placeholder="端口范围，如80/80"
              />
            </a-form-model-item>
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'authorizedObject']"
              :rules="[
            { required: true, message: '请输入授权对象' },
            { validator: validateAuthorizedObject }
          ]"
            >
              <a-input
                v-model:value="p.authorizedObject"
                style="width: 180px"
                placeholder="授权对象，如10.0.0.1/32"
              />
            </a-form-model-item>
            <a-form-model-item
              :name="['polices', policyIndex,'data',ruleIndex, 'describe']"
              :rules="[{ required: true, message: '请输入描述' }]"
            >
              <a-input
                v-model:value="p.describe"
                placeholder="描述"
              />
            </a-form-model-item>
            <MinusCircleOutlined @click="removeRule(policyIndex,ruleIndex)" />
          </a-space>
          <a-form-item>
            <a-button type="dashed" style="width: 60%" @click="addRule(policyIndex)">
              <PlusOutlined />
              添加规则
            </a-button>
          </a-form-item>
          <MinusCircleOutlined @click="removePolicy(policyIndex)" />
        </a-card>
        <a-form-item>
          <a-button type="dashed" block @click="addPolicy" style="margin-top: 8px">
            <PlusOutlined />
            添加安全组策略
          </a-button>
        </a-form-item>
        <a-form-model-item name="reason" label="申请理由">
          <a-textarea v-model:value="temp.content.reason" placeholder="若协议选择全部，需填写全部放通的充分理由" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/评论">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="opsApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 5 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { getAllAssetsIp } from '@/api/workflow/asset_automation'
import { getSecurityGroupRecode } from '@/api/security/baseline'
import { getAssetInfo } from '@/api/asset'
import { value } from 'lodash/seq'
import ipaddr from 'ipaddr.js';

export default {
  name: 'SecurityGroupRelease',
  components: { PlusOutlined, MinusCircleOutlined },
  data () {
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '安全组放开',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          polices: [{
            id: Date.now(),
            ipList: [],
            action: '新增',
            isExpired: 'long',
            expiration: '',
            data: [{
              direction: 'in',
              authPolicy: undefined,
              priority: 10,
              protocolType: undefined,
              portRange: undefined,
              authorizedObject: undefined,
              describe: undefined
            }],
            sceneTags: []
          }],
          reason: '',
        },
        timeline: [],
        comment: ''
      },
      form: this.$form.createForm(this),
      rules: {
        ip: [{ required: true, message: '请填写IP', trigger: 'blur' }],
        protocol: [{ required: true, message: '请选择协议', trigger: 'blur' }],
        port: [{ required: true, message: '请填写端口范围', trigger: 'blur' }],
        authorized_object: [{ required: true, message: '请填写授权对象', trigger: 'blur' }],
        isExpired: [{ required: true, message: '请选择有效期限', trigger: 'blur' }],
        expiration: [{ required: true, message: '请填写截至日期', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }]
      },
      request: {
        pageNo: 1,
        pageSize: 10,
        search: '',
        isRequest: false,
        totalPage: -1,
        isAdmin: true
      },
      info: []
    }
  },
  created () {
    this.getInfo()
  },
  methods: {
    value,
    getInfo () {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.securityGroupId) {
        getSecurityGroupRecode({ 'id': this.$route.query.securityGroupId }).then(response => {
          if (response.Data) {
            let rules = []
            response.Data.rules.forEach(item => {
              rules.push({
                'direction': item.direction,
                'authPolicy': item.authPolicy,
                'priority': item.priority,
                'protocolType': item.protocolType,
                'portRange': item.portRange,
                'authorizedObject': item.authorizedObject,
                'describe': item.describe
              })
            })
            this.temp.content = {
              'polices': [{
                'ipList': [response.Data.ip],
                'isExpired': 'long',
                'expiration': '',
                'action': '更新',
                'data': rules
              }]
            }
            this.handleChange([response.Data.ip], {}, 0)
          }
        })
      }
      this.GetAssetIps()
    },
    createData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功'
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/security-group-release', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功'
              })
            }
          })
        }
      })
    },
    opsApproveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '运维预审成功'
              })
            }
          })
        }
      })
    },
    safeApproveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '安全审批成功'
              })
            }
          })
        }
      })
    },
    approveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 5
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
            }
          })
        }
      })
    },
    rejectData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝'
              })
            }
          })
        }
      })
    },
    revokeData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              response.Data.content = JSON.parse(response.Data.content.data)
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回'
              })
            }
          })
        }
      })
    },
    removePolicy (policyIndex) {
      let polices = this.temp.content.polices
      if (polices.length > 1) {
        polices.splice(policyIndex, 1)
      } else {
        this.$message.warning('至少保留一个安全策略')
      }
    },
    addPolicy () {
      this.temp.content.polices.push({
        id: Date.now(),
        ipList: [],
        isExpired: 'long',
        expiration: '',
        action: '新增',
        data: [{
          direction: 'in',
          authPolicy: undefined,
          priority: 10,
          protocolType: undefined,
          portRange: undefined,
          authorizedObject: undefined,
          describe: undefined
        }]
      })
    },
    removeRule (policyIndex, ruleIndex) {
      const policy = this.temp.content.polices[policyIndex]
      if (policy.data.length > 1) {
        policy.data.splice(ruleIndex, 1)
      } else {
        this.$message.warning('至少保留一个规则')
      }
    },
    addRule (policyIndex) {
      this.temp.content.polices[policyIndex].data.push({
        direction: 'in',
        authPolicy: undefined,
        priority: 10,
        protocolType: undefined,
        portRange: undefined,
        authorizedObject: undefined,
        describe: undefined
      })
    },
    GetAssetIps () {
      getAllAssetsIp(this.request).then(res => {
        this.info.push(...res.Data.info)
        this.request.pageNo++
        this.request.isRequest = false
      })
    },
    handleSearch (value) {
      this.request.search = value
      this.request.pageNo = 1
      this.request.pageSize = 20
      getAllAssetsIp(this.request).then(res => {
        this.info = res.Data.info
        this.request.pageNo++
        this.request.isRequest = false
        this.request.totalPage = res.Data.totalPage
      })
    },
    handleChange (value, option, policyIndex) {
      // 定义 IP 网段和对应的标签
      const ipNetworks = [
        {
          network: '*********/24',
          tag: { key: '重保系统', color: '#f50' }
        },
        {
          network: '***********/16',
          tag: { key: '办公室', color: '#108ee9' }
        },
        {
          networks: [
            '**********/23',  // 包括 ********** - ************
            '**********/24',
            '**********/24',
            '**********/24',
            '********/24',
            '*********/24',
            '*********/24',
            '*********/24',
            '*********/24',
            '*********/24',
            '**********/16',
            '*********/16'
          ],
          tag: { key: 'QXB', color: '#87d068' }
        },
        {
          networks: [
            '********/24',
            '********/24',
            '**********/23'  // 包括 ********** - ************
          ],
          tag: { key: '测试', color: '#4CAF50' }
        }
      ];
      // 存储标签的 Set，确保唯一性
      const tags = []
      // 遍历每个 IP
      value.forEach(ip => {
        let matched = false;
        // 遍历网段配置
        for (const config of ipNetworks) {
          const networks = config.networks ? config.networks : [config.network];
          // 检查 IP 是否在任何一个网段内
          for (const networkStr of networks) {
            const network = ipaddr.parseCIDR(networkStr);
            const parsedIP = ipaddr.parse(ip);
            if (parsedIP.match(network)) {
              tags.push(config.tag);
              matched = true;
              break;
            }
          }
          if (matched) {
            break;
          }
        }
        // 如果没有匹配，添加"其它"标签
        if (!matched) {
          tags.push({ key: '其它', color: 'gray' });
        }
      });
      this.temp.content.polices[policyIndex].sceneTags = Array.from(new Set(tags.map(JSON.stringify))).map(JSON.parse)
    },

    // 端口范围校验
    validatePortRange (rule, value, callback) {
      // 返回 Promise
      return new Promise((resolve, reject) => {
        if (!value) {
          reject(new Error('端口范围不能为空'))
          return
        }

        const portRangeRegex = /^(\d+)\/(\d+)$/
        const match = value.match(portRangeRegex)
        if (!match) {
          reject(new Error('端口范围必须是 a/b 格式'))
          return
        }
        const startPort = parseInt(match[1])
        const endPort = parseInt(match[2])
        if (startPort > endPort) {
          reject(new Error('起始端口必须小于等于结束端口'))
          return
        }
        if (startPort < 0 || startPort > 65535 || endPort < 0 || endPort > 65535) {
          reject(new Error('端口范围必须在 0-65535 之间'))
          return
        }
        resolve()
      })
    },

    // 授权对象校验
    validateAuthorizedObject (rule, value, callback) {
      // 返回 Promise
      return new Promise((resolve, reject) => {
        if (!value) {
          reject(new Error('授权对象不能为空'))
          return
        }
        const objectRegex = /^(\d+\.\d+\.\d+\.\d+)\/(\d+)$/
        const match = value.match(objectRegex)
        if (!match) {
          reject(new Error('授权对象必须是 IP/子网掩码 格式'))
          return
        }
        const subnetMask = parseInt(match[2])
        if (subnetMask < 0 || subnetMask > 32) {
          reject(new Error('子网掩码范围必须在 0-32 之间'))
          return
        }
        resolve()
      })
    },
    Date () {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds()
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    }
  }
}
</script>

<style scoped>

.ant-tooltip-inner {
  max-width: 400px !important; /* 设置最大宽度为400px */
}

</style>
