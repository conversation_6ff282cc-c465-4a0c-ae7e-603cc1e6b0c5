<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=589922500" style="text-decoration: none">
          云IDE文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="目标机器" name="hosts">
              <a-textarea
                v-if="temp.node !== 0"
                readOnly
                v-model:value="temp.content.hosts"
                style="width: 450px"
                :auto-size="{ minRows: 2, maxRows: 5 }"
              />
              <div v-if="temp.node === 0">
                <tx-button key="submit" type="primary" @click="chooseAsset">选择目标机器</tx-button>
                <br />
                <span>
                  <template v-if="hasSelected">
                    {{ `已选数量： ${selectedData.length}` }}
                  </template>
                </span>
                <br />
                <span>
                  <a-tag v-for="(tag, index) in selectedData" :key="index" color="blue" size="small" class="tag-item">
                    {{ `${tag.ip}` }}
                  </a-tag>
                </span>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" placeholder="申请的用途或理由说明" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/devops/ide">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>

      <a-modal :zIndex="1049" :visible="chooseAssetVisible" width="80%" title="选择服务器" :closable="false">
        <div>
          <a-form layout="inline">
            <a-row :gutter="12">
              <a-col :md="8" :sm="24">
                <a-form-item label="模糊查询">
                  <a-input
                    v-model:value="assetListQuery.searchKey"
                    placeholder="服务器名/IP/公网IP/物理机IP/UUID"
                    style="width: 250px"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="IDC">
                  <a-select
                    v-model:value="assetListQuery.idc"
                    style="width: 250px"
                    placeholder="请选择"
                    :options="assetIdcList"
                    allowClear
                    showSearch
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item>
                  <tx-button type="primary" @click="$refs.assetTable.refresh(true)">查询</tx-button>
                  <tx-button
                    type="primary"
                    :disabled="!hasSelected"
                    :loading="loading"
                    @click="start"
                    style="margin-left: 20px"
                  >
                    清空已选
                  </tx-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="已选择ip：">
              <a-tag v-for="(tag, index) in selectedData" :key="index" color="blue" size="small" class="tag-item">
                {{ `${tag.ip}:${tag.uuid}` }}
              </a-tag>
            </a-form-item>
          </a-form>
          <s-table
            ref="assetTable"
            :rowKey="record => record.uuid"
            :columns="assetColumns"
            :data="loadAssetData"
            :rowSelection="rowSelection"
            height="350"
          ></s-table>
        </div>
        <template #footer>
          <tx-button key="submit" type="primary" @click="submitChooseAsset">确定</tx-button>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { getAssetListIdc, getAssetList } from '@/api/asset'
import cloneDeep from 'lodash.clonedeep'

const assetColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true,
  },
  {
    title: 'IP',
    dataIndex: 'ip',
  },
  {
    title: '服务器名',
    dataIndex: 'hostname',
  },
  {
    title: 'OS',
    dataIndex: 'os',
  },
  {
    title: '机房',
    dataIndex: 'idc',
  },
  {
    title: 'UUID',
    dataIndex: 'uuid',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'CloudIde',
  components: {
    STable,
  },
  data() {
    this.assetColumns = assetColumns
    this.pagination = pagination
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: '云IDE申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          hosts: '',
          principalEmails: store.getters.email,
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      }),
      loading: false,
      assetIdcList: [],
      taskNameList: [],
      selectedRowKeys: [],
      selectedRows: [],
      selectedData: [],
      currentPageKeys: [],
      chooseAssetVisible: false,
      totalAsset: 0,
      assetListQuery: {},
      arrIdLabelMap: {},
      loadAssetData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.assetListQuery)
        // 仅查询运行中的机器
        requestParameters.status = 1
        return getAssetList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            const rsp = {
              data: res.Data.data || [],
              pageNo: res.Data.pageNo,
              pageSize: res.Data.pageSize,
              totalCount: res.Data.totalCount,
              totalPage: res.Data.totalPage,
            }
            this.currentPageKeys = []
            for (const record of rsp.data) {
              this.currentPageKeys.push(record.uuid)
            }
            const selectedRowKeys = []
            for (const record of this.selectedData) {
              if (this.currentPageKeys.includes(record.uuid)) {
                selectedRowKeys.push(record.uuid)
              }
            }
            this.selectedRowKeys = selectedRowKeys
            return rsp
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
    }
  },
  created() {
    this.getInfo()
    this.getAssetListIdcInfo()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
    hasSelected() {
      return this.selectedData.length > 0
    },
  },
  methods: {
    start() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.temp.content.selectedRowKeys = []
        this.temp.content.selectedData = []
      }, 500)
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === '云IDE申请') {
            this.temp = this.handleGetInfo(response.Data)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    handleGetInfo(data) {
      data.content = JSON.parse(data.content['data'])
      delete data.content['data']
      if (data.content.hosts) {
        data.content.hosts = data.content.hosts.join(',')
      }
      return data
    },
    handleSubmitInfo(requestParameters) {
      requestParameters.handler = store.getters.name
      requestParameters.handlerEmail = store.getters.email
      requestParameters.content.hosts = requestParameters.content.hosts
        ? requestParameters.content.hosts.split(',')
        : null
      requestParameters.content = { data: JSON.stringify(requestParameters.content) }
      return requestParameters
    },
    getAssetListIdcInfo() {
      getAssetListIdc().then(res => {
        if (!res.Data.idc) {
          return
        }
        for (var i = 0, len = res.Data.idc.length; i < len; i++) {
          var idc = {}
          idc.value = res.Data.idc[i]
          idc.label = res.Data.idc[i]
          this.assetIdcList.push(idc)
        }
      })
    },
    submitChooseAsset() {
      this.chooseAssetVisible = false
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      if (this.selectedData.length > 0) {
        this.selectedData = this.MergeArray(this.selectedData, selectedRows)
      } else {
        this.selectedData = selectedRows
      }
    },
    MergeArray(arr1, arr2) {
      var _arr = []
      for (var i = 0; i < arr1.length; i++) {
        _arr.push(arr1[i])
      }
      for (var x = 0; x < arr2.length; x++) {
        var flag = true
        for (var j = 0; j < arr1.length; j++) {
          if (arr2[x].uuid === arr1[j].uuid) {
            flag = false
            break
          }
        }
        if (flag) {
          _arr.push(arr2[x])
        }
      }
      _arr = this.spliceArray(_arr, this.selectedRowKeys)
      return _arr
    },
    // 删除未选择数据
    spliceArray(selectedData, selectedRowKeys) {
      const notChooseKeys = []
      for (const key of this.currentPageKeys) {
        if (!selectedRowKeys.includes(key)) {
          notChooseKeys.push(key)
        }
      }
      const _arr = []
      for (let x = 0; x < selectedData.length; x++) {
        let needDeleted = false
        for (let j = 0; j < notChooseKeys.length; j++) {
          if (selectedData[x].uuid === notChooseKeys[j]) {
            needDeleted = true
            break
          }
        }
        if (!needDeleted) {
          _arr.push(selectedData[x])
        }
      }
      return _arr
    },
    chooseAsset() {
      this.chooseAssetVisible = true
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          if (this.selectedData.length === 0) {
            alert('请选择目标主机')
            return
          }
          this.Date()
          this.node_status = 0
          const requestParameters = cloneDeep(this.temp)
          requestParameters.node = 1
          requestParameters.status = 1
          requestParameters.timeline = [
            this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now,
          ]
          var hosts = []
          this.selectedData.forEach(item => {
            hosts.push(item.ip + ':' + item.uuid + ':' + item.hostname + ':' + item.os)
          })
          requestParameters.content.hosts = hosts.length > 0 ? hosts : null
          requestParameters.content = { data: JSON.stringify(requestParameters.content) }
          createOrder(requestParameters).then(response => {
            if (response !== undefined && response.Code !== 200) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = this.handleGetInfo(response.Data)
              this.node_status = 1
              this.$router.push({ path: '/workflow/cloud-ide', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 2
          requestParameters.status = 1
          approveOrder(requestParameters).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 10
          approveOrder(requestParameters).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 20
          approveOrder(requestParameters).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let requestParameters = cloneDeep(this.temp)
          requestParameters = this.handleSubmitInfo(requestParameters)
          requestParameters.node = 3
          requestParameters.status = 30
          approveOrder(requestParameters).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = this.handleGetInfo(response.Data)
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>
