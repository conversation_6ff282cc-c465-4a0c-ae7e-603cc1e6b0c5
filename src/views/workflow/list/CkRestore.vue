<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="数据解冻DBA(24小时)" />
        <a-step title="数据恢复DBA" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules1"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="区域" name="idc">
          <a-select
            v-model:value="temp.content.idc"
            placeholder="选择区域名称"
            style="width: 400px"
            @change="SelectChange('idc')"
          >
            <a-select-option v-for="item in idcNameList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="库名" name="dbName">
          <a-select
            v-model:value="temp.content.dbName"
            placeholder="选择数据库名称"
            style="width: 400px"
            :showSearch="true"
            :filter-option="filterOption"
            @change="SelectChange('dbName')"
          >
            <a-select-option v-for="item in dbNamesList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="表名" name="tableName">
          <a-select
            v-model:value="temp.content.tableName"
            :showSearch="true"
            :filter-option="filterOption"
            placeholder="选择区域名称"
            style="width: 400px"
          >
            <a-select-option v-for="item in tableNameList" :key="item" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="时间范围" name="timeScope">
          <a-range-picker
            v-model:value="temp.content.timeScope"
            format="YYYY-MM-DD"
            :disabledDate="disabledDate"
            @change="handleCalendarChange"
          />
        </a-form-model-item>
        <a-form-model-item label="恢复数据生命周期" name="lifetime">
          <a-input-number v-model:value="temp.content.lifetime" placeholder="1" />
          天
        </a-form-model-item>
        <a-form-model-item label="执行错误原因" v-if="temp.content.errInfoData != ''">
          <a>{{ temp.content.errInfoData }}</a>
        </a-form-model-item>
        <a-form-model-item label="解冻执行时间" v-if="temp.node >= 3">
          <a>{{ temp.content.unfreezeTime }}</a>
        </a-form-model-item>
        <a-form-model-item label="执行结果统计" v-if="temp.node >= 3">
          <a-table
            ref="table"
            size="default"
            rowKey="id"
            :pagination="pagination"
            :columns="resultColumns"
            :data-source="resultData"
          ></a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/评论">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="unfreezeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1 && unfreezeTimeCheck"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="restoreApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import moment from 'moment'
import { filterLabelValue } from '@aim/helper'

import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getCkIdcDbsTables, getCkUnfreezeResult } from '@/api/db/dms'

const resultColumns = [
  { title: '归档数据日期', dataIndex: 'date' },
  { title: 'ClickHouse服务器IP', dataIndex: 'host' },
  // { title: 'keyId', dataIndex: 'key' },
  { title: '恢复存储对象数量', dataIndex: 'numbers' },
]
const pagination = {
  defaultPageSize: 1000,
  hideOnSinglePage: true,
}
export default {
  name: 'CkRestore',
  data: function () {
    this.resultColumns = resultColumns
    this.pagination = pagination
    return {
      fetching: false,
      resultData: [],
      userEmailList: [],
      value: [],
      confirmLoading: false,
      unfreezeTimeCheck: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      userKeyList: [],
      idcNameList: [],
      dbNamesList: [],
      tableNameList: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      checkPaaswd: '',
      temp: {
        id: '',
        orderType: 'ClickHouse归档数据恢复',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          idc: undefined,
          dbName: undefined,
          tableName: undefined,
          timeScope: undefined,
          lifetime: 3,
          reason: '',
          resultData: [],
          errInfoData: '',
          unfreezeTime: '',
        },
        timeline: [],
        comment: '',
      },
      rules1: antdFormRulesFormat({
        'content.idc': [{ required: true, message: '请输入ck区域' }],
        'content.dbName': [{ required: true, message: '请输入数据库名选择' }],
        'content.tableName': [{ required: true, message: '请输入sql语句, 以分号结尾' }],
        'content.timeScope': [{ required: true, message: '请输入' }],
        'content.lifetime': [{ required: true, message: '请输入恢复周期，多少天' }],
        'content.reason': [{ required: true, message: '请填写申请理由' }],
      }),
      dbList: [],
    }
  },
  created() {
    this.SelectChange()
    this.getInfo()
  },
  methods: {
    filterOption: filterLabelValue,

    SelectChange(type) {
      if (type === 'idc' && this.temp.content.idc !== '') {
        getCkIdcDbsTables({ idc: this.temp.content.idc }).then(response => {
          if (response.Data.dbNames !== null) {
            this.dbNamesList = response.Data.dbNames
          }
        })
        this.temp.content.dbName = undefined

        this.temp.content.tableName = undefined
        this.tableNameList = []
      } else if (type === 'dbName' && this.temp.content.dbName !== '') {
        getCkIdcDbsTables({ idc: this.temp.content.idc, dbName: this.temp.content.dbName }).then(response => {
          if (response.Data.tablesNames !== null) {
            this.tableNameList = response.Data.tablesNames
          }
        })
        this.temp.content.tableName = undefined
      } else {
        getCkIdcDbsTables().then(response => {
          if (response.Data.idcs !== null) {
            this.idcNameList = response.Data.idcs
          }
        })
        this.temp.content.idc = undefined
        this.temp.content.dbName = undefined
        this.temp.content.tableName = undefined
        this.dbNamesList = []
        this.tableNameList = []
      }
    },
    searchTypeChange() {
      this.temp.content.checkedValue = []
      this.dbList = []
    },

    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.node >= 3) {
              this.CheckUnfreezeTime(this.temp.content.unfreezeTime)
              this.resultData = this.temp.content.resultData
              this.getResultDate()
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    CheckUnfreezeTime(time) {
      let unfreezeTime = new Date(time)
      let currentTime = new Date()
      let timeDifference = currentTime - unfreezeTime
      let timeDifferenceInHours = Math.floor(timeDifference / (1000 * 60 * 60))
      console.log(timeDifferenceInHours)
      if (timeDifferenceInHours >= 12) {
        this.unfreezeTimeCheck = true
      }
    },

    getResultDate() {
      for (let i = 0; i < this.resultData.length; i++) {
        getCkUnfreezeResult({ key: this.resultData[i].key }).then(response => {
          if (response.Data !== null) {
            this.resultData[i].numbers = response.Data.count
          }
        })
      }
    },
    disabledDate(current) {
      // 禁止选择一个月前的时间和未来的时间
      const oneMonthAgo = moment().subtract(1, 'months')
      return current && current > oneMonthAgo.startOf('day')
    },
    handleCalendarChange() {
      // 如果选择的日期范围超过10天，则重置日期
      let dates = this.temp.content.timeScope
      if (dates.length === 2 && dates[1].diff(dates[0], 'days') > 10) {
        this.temp.content.timeScope = []
        this.$message.error('最多只能选择10天的日期范围')
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          createOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
              this.temp = response.Data
              this.$router.push({ path: '/workflow/ck-restore', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
              this.temp = response.Data
            }
          })
        }
      })
    },
    unfreezeApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
              this.temp = response.Data
            }
          })
        }
      })
    },
    restoreApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content.autoStep = 'secStep'
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let data = JSON.parse(JSON.stringify(this.temp))
          data.content = { data: JSON.stringify(data.content) }
          approveOrder(data).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              response.Data.content.timeScope = response.Data.content.timeScope.map(date => moment(date))
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.hljs {
  display: inline-block;
  padding: 0;
  background: transparent;
  vertical-align: middle;
  height: auto;
}
/deep/.d2h-diff-table {
  width: 100%;
  /* border-collapse: collapse; */
  font-family: Menlo, Consolas, monospace;
  font-size: 9px;
  line-height: normal;
}
</style>
