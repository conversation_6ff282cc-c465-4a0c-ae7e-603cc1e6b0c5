<template>
  <WorkflowStream
    @createData="createData"
    @revokeData="revokeData"
    @approve="approve"
    :steps="steps"
    ref="WorkflowStream"
  >
    <template v-slot:contents>
      <a-form-model
        ref="temp"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <routeForm
          :hiddenItem="hiddenItem"
          v-if="modalVisible"
          :routeInfos="routeInfos"
          :userList="userList"
          ref="routeForm"
          :envGroups="envGroups"
        />
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/评论" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
      </a-form-model>
    </template>
  </WorkflowStream>
</template>
<script>
import WorkflowStream from '@/views/comp/workFlowStream/index.vue'
import { getOrderInfo } from '@/api/workflow/order'
import store from '@/store' // secondary package based on el-pagination
import routeForm from '@/views/gateway/apiRoute/comps/handleRouteForm.vue'
import { groupNames } from '@/api/gateway/groupAndIp'
export default {
  components: { WorkflowStream, routeForm },
  provide() {
    return {
      workFlowData: this,
    }
  },
  data() {
    return {
      steps: [
        {
          title: '上级领导审批',
        },
        {
          title: '管理员审批',
        },
      ],
      hiddenItem: ['connectivity'], //设置隐藏项目
      rules: {},
      envGroups: [],
      userList: [], // 负责人列表
      routeInfos: {}, // 回显，更新路由
      modalVisible: true,
      temp: {
        id: '',
        orderType: '网关路由配置',
        founder: noc.user.getUserInfo().name,
        founderEmail: noc.user.getUserInfo().email,
        handlerEmail: '',
        node: 0,
        status: 0,
        time_now: '',
        handler: '',
        timeline: [],
        comment: '',
        content: {
          reason: '',
          routeForm: {},
        },
      },
    }
  },
  mounted() {
    this.initPage()
    console.log(noc.user.getUserInfo(), '= noc.user.getUserInfo()= noc.user.getUserInfo()')
  },
  methods: {
    initPage() {
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.routeInfos = JSON.parse(this.temp.content.routeForm)
            if (this.temp.node > 1) {
              this.hiddenItem = ['connectivity']
            } else {
              this.hiddenItem = ['env', 'connectivity']
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      } else {
        this.hiddenItem = ['env', 'connectivity']
      }
      groupNames()
        .then(res => {
          this.envGroups = res.Data.clusters
        })
        .catch(() => {})
    },
    createData() {
      console.log(this.$refs.routeForm, 'this.$refs.routeForm')
      this.temp.content.routeForm = JSON.stringify(this.$refs.routeForm.form)
    },
    approve() {
      this.temp.content.routeForm = JSON.stringify(this.$refs.routeForm.form)
    },
    revokeData() {
      this.temp.content.routeForm = JSON.stringify(this.$refs.routeForm.form)
    },
  },
}
</script>

<style></style>
