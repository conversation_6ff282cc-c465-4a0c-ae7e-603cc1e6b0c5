<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card v-if="temp.node === 0" style="margin-top: 12px" :bordered="false" title="SInfo">
      <a-table :pagination="false" :columns="columns" :data-source="tableData"></a-table>
      <!-- { position: ['none', 'none'] } -->
    </a-card>
    <div v-else-if="temp.node === 1">
      <a-card style="margin-top: 12px" :bordered="false" :title="`组内使用情况${titlePart}`">
        <a-table :pagination="false" :columns="columnsGroup" :data-source="tableGroup"></a-table>
      </a-card>
      <a-card style="margin-top: 12px" :bordered="false" :title="`各组使用情况${titlePart}`">
        <a-table :pagination="false" :columns="columnsAllGroup" :data-source="tableAllGroup"></a-table>
      </a-card>
    </div>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item name="taskType" label="任务类型">
          <a-radio-group
            v-model:value="temp.content.taskType"
            button-style="solid"
            @change="handleNodeNumByTaskType(temp.content.taskType)"
          >
            <a-tooltip v-if="groupInfo.preTraining">
              <template #title>{{ groupInfo.preTrainingInfo }}</template>
              <a-radio-button value="预训练" disabled>预训练</a-radio-button>
            </a-tooltip>
            <a-radio-button v-else value="预训练">预训练</a-radio-button>
            <a-tooltip v-if="groupInfo.debug && temp.node === 0">
              <template #title>{{ groupInfo.debugInfo }}</template>
              <a-radio-button value="DEBUG" disabled>DEBUG</a-radio-button>
            </a-tooltip>
            <a-radio-button v-else value="DEBUG">DEBUG</a-radio-button>
            <a-radio-button value="普通训练">普通训练</a-radio-button>
            <a-radio-button value="机器学习推理">机器学习推理</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item name="parts" label="公共分区">
          <a-tooltip>
            <template #title>每节点GPU最大16，CPU最大128</template>
            <a-checkable-tag class="partFirstTag" key="A10"
                             :checked="temp.content.parts && temp.content.parts.indexOf('A10') > -1"
                             @change="checked => handlePartChange('A10', checked)">A10
            </a-checkable-tag>
          </a-tooltip>
          <a-tooltip>
            <template #title>每节点GPU最大10，CPU最大128</template>
            <a-checkable-tag class="partTag" key="A100"
                             :checked="temp.content.parts && temp.content.parts.indexOf('A100') > -1"
                             @change="checked => handlePartChange('A100', checked)">A100
            </a-checkable-tag>
          </a-tooltip>
          <a-tooltip>
            <template #title>每节点GPU最大8，CPU最大128</template>
            <a-checkable-tag class="partTag" key="A800"
                             :checked="temp.content.parts && temp.content.parts.indexOf('A800') > -1"
                             @change="checked => handlePartChange('A800', checked)">A800
            </a-checkable-tag>
          </a-tooltip>
          <a-tooltip>
            <template #title>每节点GPU最大8，CPU最大224</template>
            <a-checkable-tag class="partTag" key="new-H800"
                             :checked="temp.content.parts && temp.content.parts.indexOf('new-H800') > -1"
                             @change="checked => handlePartChange('new-H800', checked)">new-H800
            </a-checkable-tag>
          </a-tooltip>
          <a-tooltip v-if="temp.content.taskType === 'DEBUG'">
            <template #title>每节点GPU最大1</template>
            <a-checkable-tag class="partTag" key="debug"
                             :checked="temp.content.parts && temp.content.parts.indexOf('debug') > -1"
                             @change="checked => handlePartChange('debug', checked)">debug
            </a-checkable-tag>
          </a-tooltip>
          <a-tooltip>
            <template #title>每节点GPU最大10，CPU最大80</template>
            <a-checkable-tag class="partTag" key="3090"
                             :checked="temp.content.parts && temp.content.parts.indexOf('3090') > -1"
                             @change="checked => handlePartChange('3090', checked)">3090
            </a-checkable-tag>
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item name="groupPart" label="私有分区(H800)">
          <a-radio-group
            v-model:value="temp.content.groupPart"
            button-style="solid"
            @change="handlePrivateNodeNumByTaskType"
          >
            <a-tooltip>
              <template #title>节点数：8</template>
              <a-radio-button value="NLP" v-if="groupInfo.aiGroup === 'nlp'">NLP</a-radio-button>
            </a-tooltip>
            <a-tooltip>
              <template #title>节点数：8</template>
              <a-radio-button value="IPT" v-if="groupInfo.aiGroup === 'ipt'">IPT</a-radio-button>
            </a-tooltip>
            <a-tooltip>
              <template #title>节点数：8</template>
              <a-radio-button value="CV" v-if="groupInfo.aiGroup === 'cv'">CV</a-radio-button>
            </a-tooltip>
            <a-tooltip>
              <template #title>节点数：4</template>
              <a-radio-button value="CV_Innovation" v-if="groupInfo.aiGroup === 'cv'">CV_Innovation</a-radio-button>
            </a-tooltip>
            <a-tooltip>
              <template #title>节点数：1</template>
              <a-radio-button value="ACG" v-if="groupInfo.aiGroup === 'acg'">ACG</a-radio-button>
            </a-tooltip>
            <a-radio-button value="other">其他</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item v-if="temp.content.groupPart === 'other'" name="otherPart" label="私有卡名称">
          <a-input style="width: 40%" v-model:value="temp.content.otherPart" placeholder="请输入的卡的名称"></a-input>
        </a-form-model-item>
        <a-form-model-item name="qosPlatErr" label="是否插队(平台错误)">
          <a-switch v-model:checked="temp.content.qosPlatErr" checked-children="开" un-checked-children="关" />
          <a-popover title="平台错误插队说明">
            <template #content>
              <p>
                因平台或者网络问题造成的任务错误，可走该选项，跳过上级直接由运维部门审批，且不会减少组内插队总次数。
              </p>
            </template>
            <a-icon style="margin-left: 12px; color: blue" theme="filled" type="question-circle" />
          </a-popover>
        </a-form-model-item>
        <a-form-model-item name="qos" label="是否插队" v-if="temp.content.groupPart !== ''">
          <a-switch
            v-model:checked="temp.content.qos"
            checked-children="开"
            un-checked-children="关"
          />
          <a-popover title="插队说明">
            <template #content>
              <p>插队不会中断运行中的任务，是将任务在排队队列中的次序前置。</p>
              <p>
                排队队列如果存在多个插队任务，遵循先插先执行的原则。后创建的插队任务不会抢占已创建的插队任务的次序。
              </p>
            </template>
            <a-icon style="margin-left: 12px; color: blue" theme="filled" type="question-circle" />
          </a-popover>
        </a-form-model-item>
        <a-form-model-item name="nodeNum" label="节点数">
          <a-input-number
            v-if="temp.content.taskType === 'DEBUG'"
            v-model:value="temp.content.nodeNum"
            :min="0"
            :max="2"
            addon-after="个"
          ></a-input-number>
          <a-input-number v-else v-model:value="temp.content.nodeNum" :min="0" addon-after="个"></a-input-number>
        </a-form-model-item>
        <a-form-model-item name="gpuNum" label="每节点GPU数">
          <a-input-number
            v-if="!privatePart"
            v-model:value="temp.content.gpuNum"
            :min="0"
            :max="gpuNumMax"
            addon-after="个"
            @change="handleCpuAndGpuNum()"
          ></a-input-number>
          <a-input-number v-else v-model:value="temp.content.gpuNum" addon-after="个"></a-input-number>
        </a-form-model-item>
        <a-form-model-item name="cpuNum" label="每节点CPU数">
          <a-input-number
            v-if="!privatePart"
            v-model:value="temp.content.cpuNum"
            :min="1"
            :max="cpuNumMax"
            addon-after="个"
          ></a-input-number>
          <a-input-number v-else v-model:value="temp.content.cpuNum" addon-after="个"></a-input-number>
        </a-form-model-item>
        <a-form-model-item
          v-if="temp.content.taskType !== 'DEBUG' && temp.content.parts && !temp.content.parts.includes('A10')">
          <template #label>
            JuiceFS目录加速
            <a-popover title="JuiceFS目录加速说明">
              <template #content>
                <p>我们会尽量让这些目录保留在缓存中，可以获得5倍以上的读取性能</p>
                <p>请尽可能提供精确的目录，以便发挥出最佳的性能</p>
                <p>
                  路径必须是以“/juicefs-algorithm”开头，且路径深度必须大于等于5，比如
                  /juicefs-algorithm/workspace/autom/yifan_tang/test
                </p>
                <p>目录加速功能不会影响任务调度，请放心使用</p>
              </template>
              <a-icon style="margin-left: 12px; color: blue" theme="filled" type="question-circle" />
            </a-popover>
          </template>
          <a-form name="command_item" :model="temp.content.juicefsPath">
            <a-row :gutter="24" v-for="(item, index) in temp.content.juicefsPath" v-bind="formItemLayoutWithOutLabel">
              <a-col :span="20">
                <a-form-item name="form.item[index].value">
                  <a-input
                    v-model:value="item.value"
                    :precision="4"
                    placeholder="选填，必须是以“/juicefs-algorithm”开头，且路径深度必须大于等于5，例如 /juicefs-algorithm/workspace/autom/yifan_tang/test"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item>
                  <tx-button
                    v-if="index === 0"
                    type="primary"
                    icon="plus"
                    style="width: 30px"
                    size="small"
                    @click="addjuicefsPathRow"
                  ></tx-button>
                  <tx-button
                    v-else
                    type="dashed"
                    size="small"
                    icon="minus"
                    style="width: 30px"
                    @click="removejuicefsPathRow(index)"
                  ></tx-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-form-model-item>
        <a-form-model-item name="outputLog" label="日志路径">
          <a-input v-model:value="temp.content.outputLog" addon-before="output" />
        </a-form-model-item>
        <a-form-model-item name="errorLog" label="日志路径">
          <a-input v-model:value="temp.content.errorLog" addon-before="error" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0 && contentTag === 1" name="taskContent" label="任务内容">
          <div style="height: 500px; width: 90%">
            <ExecutionEditor :opts="editorOpt" />
          </div>
        </a-form-model-item>
        <a-form-model-item v-else name="taskContent" label="任务内容">
          <a-textarea v-model:value="temp.content.taskContent" :rows="20" />
        </a-form-model-item>
        <a-form-model-item name="name" label="作业名">
          <a-input
            v-model:value="temp.content.name"
            placeholder="一律小写，空格用_代替，只包含小写英文和_和数字"
            v-on:input="limitInput"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item name="runTime" label="预计执行时长">
          <a-input-number
            v-if="temp.content.taskType === 'DEBUG'"
            v-model:value="temp.content.runTime"
            :min="1"
            :max="1"
            addon-after="小时"
            disabled
          ></a-input-number>
          <a-input-number v-else v-model:value="temp.content.runTime" :min="1" addon-after="小时"></a-input-number>
        </a-form-model-item>
        <a-form-model-item name="reason" label="申请理由">
          <a-textarea v-model:value="temp.content.reason" placeholder="申请的用途或理由说明" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/评论">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { STable } from '@/components'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import {
  getSlurmGroupCount,
  getSlurmSInfo,
  slurmJobList,
  updateSlurmGroupCount,
  SlurmUseInfo,
  checkGroupAndPublicResource
} from '@/api/ai/slurm'
import ExecutionEditor from '@/views/workflow/list/comp/SlurmExecutionEditor.vue'
import { deepClone } from '@/utils/util'

const columns = [
  {
    title: 'Partition',
    dataIndex: 'partition'
  },
  {
    title: 'Avail',
    dataIndex: 'avail'
  },
  {
    title: 'TimeLimit',
    dataIndex: 'timeLimit'
  },
  {
    title: 'Nodes',
    dataIndex: 'nodes'
  },
  {
    title: 'State',
    dataIndex: 'state'
  },
  {
    title: 'NodeList',
    dataIndex: 'nodeList'
  }
]

const columnsGroup = [
  {
    title: '任务ID',
    dataIndex: 'jobId'
  },
  {
    title: '分区',
    dataIndex: 'part'
  },
  {
    title: 'GPU数量',
    dataIndex: 'gpuUsageNum'
  },
  {
    title: '用户',
    dataIndex: 'user'
  },
  {
    title: '用户组',
    dataIndex: 'group'
  },
  {
    title: '开始时间',
    dataIndex: 'startRunTime'
  },
  {
    title: '预计结束时间',
    dataIndex: 'estimatedRunTime'
  }
]

const columnsAllGroup = [
  {
    title: '用户组',
    dataIndex: 'group'
  },
  {
    title: 'GPU数量',
    dataIndex: 'gpuUsageNum'
  },
  {
    title: '任务数量',
    dataIndex: 'jobNum'
  }
]

export default {
  inject: ['toogleFull'],
  name: 'SlurmJobCreate',
  components: { ExecutionEditor, STable },
  data () {
    this.columns = columns
    this.columnsGroup = columnsGroup
    this.columnsAllGroup = columnsAllGroup
    return {
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 }
        }
      },
      contentTag: 1,
      optDatas: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: '',
        orderType: 'Slurm任务创建',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          name: '',
          taskType: '普通训练',
          costBu: '',
          parts: [],
          choosePart: 'public',
          groupPart: '',
          displayPart: '',
          otherPart: '',
          nodeNum: 1,
          gpuNum: 1,
          cpuNum: 1,
          runTime: 1,
          juicefsPath: [
            {
              value: ''
            }
          ],
          outputLog: '',
          errorLog: '',
          taskName: '',
          taskContent: '',
          reason: '',
          qos: false,
          qosPlatErr: false,
          isSuspended: false,
          user: store.getters.email.split('@')[0]
        },
        timeline: [],
        comment: ''
      },
      editorOpt: {
        readOnly: false,
        language: 'shell',
        value: `#!/bin/bash\r# default bash`
      },
      groupInfo: {
        aiGroup: '',
        totalNum: 0,
        usedNum: 0,
        availableNum: 0,
        preTraining: false,
        preTrainingInfo: '',
        debug: false,
        debugInfo: ''
      },
      tableData: [],
      tableGroup: [],
      tableAllGroup: [],
      titlePart: '',
      privatePart: false,
      // parameter => {
      //   return getSlurmSInfo().then(res => {
      //     console.log(res, 'rrr')
      //     if (res.Data.hasOwnProperty('data')) {
      //       if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
      //         return {
      //           data: [],
      //           // pageNo: 1,
      //           // pageSize: 0,
      //           // totalCount: 0,
      //           // totalPage: 0,
      //         }
      //       } else {
      //         return {
      //           data: res.Data.data,
      //           // pageNo: 1,
      //           // pageSize: 10,
      //           // totalCount: res.Data.data.length,
      //           // totalPage: 1,
      //         }
      //       }
      //     } else {
      //       return {
      //         data: [],
      //         // , pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0
      //       }
      //     }
      //   })
      // },
      rules: {
        name: [{ required: true, message: '请填写作业名', trigger: 'blur' }],
        taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
        // costBu: [{ required: true, message: '请选择成本归宿', trigger: 'blur' }],
        part: [{ required: true, message: '请选择GPU卡类型', trigger: 'blur' }],
        otherPart: [{ required: true, message: '请输入私有卡名称', trigger: 'blur' }],
        nodeNum: [{ required: true, message: '请输入节点数', trigger: 'blur' }],
        gpuNum: [{ required: true, message: '请输入每节点GPU数', trigger: 'blur' }],
        cpuNum: [{ required: true, message: '请输入每节点CPU数', trigger: 'blur' }],
        outputLog: [{ required: true, message: '请输入output日志路径', trigger: 'blur' }],
        errorLog: [{ required: true, message: '请输入error日志路径', trigger: 'blur' }],
        runTime: [{ required: true, message: '请选择预计执行时长', trigger: 'blur' }],
        qos: [{ required: true, message: '请选择是否插队', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        qosPlatErr: [{ required: true, message: '请选择是否平台错误插队', trigger: 'blur' }]
      },
      cpuNumMax: 1,
      gpuNumMax: 1,
      selectedTags: [],
      groupPartAll: ['NLP', 'IPT', 'CV', 'CV_Innovation', 'ACG', 'other'],
      groupPartAllExceptOther: ['NLP', 'IPT', 'CV', 'CV_Innovation', 'ACG'],
      publicPartAll: ['A10', 'A100', 'A800', 'new-H800', '3090', 'debug']
    }
  },
  created () {
    this.getInfo()
    var ua = window.navigator.userAgent.toLowerCase()
    if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
      this.toogleFull()
    } else {
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
  },
  destroyed () {
    removeWatermark()
  },
  methods: {
    limitInput: function(event) {
      this.temp.content.name = event.target.value.replace(/[^0-9a-z_]/g, '')
    },
    getInfo () {
      getSlurmSInfo()
        .then(res => {
          if (res.Data.hasOwnProperty('data')) {
            if (res.Data.data === undefined || res.Data.data === null || res.Data.data.length <= 0) {
              this.tableData = []
            } else {
              this.tableData = res.Data.data
            }
          } else {
            this.tableData = res.Data.data = []
          }
        })
        .catch(err => {
        })

      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.editorOpt.value = response.Data.content.taskContent
            this.getInfoHandlePart()
            getSlurmGroupCount(this.temp.founderEmail).then(res => {
              if (res !== undefined) {
                this.groupInfo = res.Data
              }
            })
            if (this.temp.node === 1) {
              this.getSlurmUseInfo()
            } else {
              this.getDisplayPart()
            }
            this.handleCpuAndGpuNum()
            this.handleNodeNumByTaskType(this.temp.content.taskType)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.rid) {
        getOrderInfo(this.$route.query.rid).then(response => {
          this.contentTag = 2
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp.content = response.Data.content
            this.editorOpt.value = response.Data.content.taskContent
            this.getInfoHandlePart()
            getSlurmGroupCount(this.temp.founderEmail).then(res => {
              if (res !== undefined) {
                this.groupInfo = res.Data
                if (this.groupInfo.debug && this.temp.content.taskType === 'DEBUG' && this.temp.node === 0) {
                  this.temp.content.taskType = ''
                }
              }
            })
            if (this.temp.node === 1) {
              this.getSlurmUseInfo()
            } else {
              this.getDisplayPart()
            }
            this.handleCpuAndGpuNum()
            this.handleNodeNumByTaskType(this.temp.content.taskType)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      } else if (this.$route.query.name) {
        if (this.publicPartAll.includes(this.$route.query.part)) {
          this.temp.content.parts = [this.$route.query.part]
          this.temp.content.choosePart = 'public'
        } else if (this.groupPartAllExceptOther.includes(this.$route.query.part)) {
          this.temp.content.groupPart = this.$route.query.part
          this.temp.content.choosePart = 'group'
        } else {
          this.temp.content.groupPart = 'other'
          this.temp.content.otherPart = this.$route.query.part
          this.privatePart = true
          this.temp.content.choosePart = 'group'
        }
        this.temp.content.name = this.$route.query.name
        this.temp.content.taskType = this.$route.query.taskType
        // this.temp.content.costBu = this.$route.query.costBu
        this.temp.content.nodeNum = this.$route.query.nodeNum
        this.temp.content.gpuNum = this.$route.query.gpuNum
        this.temp.content.cpuNum = this.$route.query.cpuNum
        this.temp.content.outputLog = this.$route.query.outputLog
        this.temp.content.errorLog = this.$route.query.errorLog
        this.temp.content.taskName = this.$route.query.taskName
        this.temp.content.taskContent = this.$route.query.taskContent
        this.editorOpt.value = this.$route.query.taskContent
        getSlurmGroupCount(this.temp.founderEmail).then(res => {
          if (res !== undefined) {
            this.groupInfo = res.Data
            if (this.groupInfo.debug && this.temp.content.taskType === 'DEBUG' && this.temp.node === 0) {
              this.temp.content.taskType = ''
            }
          }
        })
        if (this.temp.node === 1) {
          this.getSlurmUseInfo()
        } else {
          this.getDisplayPart()
        }
        this.handleCpuAndGpuNum()
        this.handleNodeNumByTaskType(this.temp.content.taskType)
        this.nodeStatus = 1
      } else {
        getSlurmGroupCount(this.temp.founderEmail).then(res => {
          if (res !== undefined) {
            this.groupInfo = res.Data
            if (this.groupInfo.debug && this.temp.content.taskType === 'DEBUG' && this.temp.node === 0) {
              this.temp.content.taskType = ''
            }
          }
        })
        if (this.temp.node === 1) {
          this.getSlurmUseInfo()
        } else {
          this.getDisplayPart()
        }
        this.handleCpuAndGpuNum()
        this.handleNodeNumByTaskType(this.temp.content.taskType)
      }
    },
    getInfoHandlePart () {
      if (this.temp.content.choosePart === 'group' && !this.groupPartAll.includes(this.temp.content.groupPart)) {
        this.privatePart = true
        this.temp.content.groupPart = 'other'
      }
    },
    createData () {
      if (this.temp.content.parts && this.temp.content.parts.length === 0 && this.temp.content.groupPart === '') {
        notification.error({
          message: '校验失败',
          description: '请至少选择一个分区！'
        })
        return
      }
      if (this.temp.content.choosePart === 'group' && this.temp.content.groupPart === 'other') {
        if (this.publicPartAll.includes(this.temp.content.otherPart)) {
          notification.error({
            message: '校验失败',
            description: '自定义分区与公有分区名称相同，请调整！'
          })
          return
        } else if (this.groupPartAllExceptOther.includes(this.temp.content.otherPart)) {
          notification.error({
            message: '校验失败',
            description: '自定义分区与私有分区名称相同，请调整！'
          })
          return
        }
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          let choosePart = this.temp.content.choosePart
          let parts = this.temp.content.parts
          let aiGroup = this.groupInfo.aiGroup
          let needGpuNum = this.temp.content.gpuNum * this.temp.content.nodeNum
          // console.log(choosePart, parts, aiGroup)
          if (choosePart === 'public' && parts && parts.includes('new-H800')) {
            checkGroupAndPublicResource({
              part: 'new-H800',
              aiGroup: aiGroup,
              needGpuNum: needGpuNum
            }).then(response => {
              if (response === undefined) {
                notification.error({
                  message: '校验失败',
                  description: '后端接口错误，请联系运维开发排查~'
                })
              } else {
                // console.log(response)
                if (!response.Data.allowSubmission) {
                  notification.error({
                    message: '提交失败',
                    description: '当前组私有分区资源充足，请到私有分区提交'
                  })
                  this.temp.node = 0
                  this.temp.timeline = []
                } else {
                  this.submitCreateOrder()
                }
              }
            })
          } else {
            this.submitCreateOrder()
          }
        }
      })
    },
    submitCreateOrder () {
      this.Date()
      this.node_status = 0
      this.temp.node = 1
      this.temp.status = 1
      if (this.temp.content.groupPart === 'other') {
        this.temp.content.groupPart = this.temp.content.otherPart
      }
      // let qos = this.temp.content.qos
      // this.temp.content.taskName = paramsName
      this.temp.content.taskName = this.temp.content.name
      this.temp.content.taskContent = this.editorOpt.value
      this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      createOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '创建失败',
            description: '后端接口错误，请联系运维开发排查~'
          })
        } else {
          // if (qos) {
          //   updateSlurmGroupCount({
          //     email: this.temp.founderEmail
          //   })
          // }
          notification.success({
            message: '创建成功',
            description: '工单创建成功'
          })
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          if (this.temp.content.choosePart === 'group' && !this.groupPartAll.includes(this.temp.content.groupPart)) {
            this.privatePart = true
            this.temp.content.groupPart = 'other'
          }
          this.$router.push({ path: '/workflow/slurm-job-create', query: { id: response.Data.id } })
        }
      })
    },
    leaderApproveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功'
              })
            }
          })
        }
      })
    },
    approveData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功'
              })
            }
          })
        }
      })
    },
    rejectData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝'
              })
            }
          })
        }
      })
    },
    revokeData () {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~'
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回'
              })
            }
          })
        }
      })
    },
    Date () {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds()
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    handleChange (val) {
      this.temp.content.taskName = val
      let v = JSON.parse(this.temp.content.taskName)
      if (v.taskContent) {
        this.editorOpt = {
          readOnly: false,
          language: 'shell',
          value: v.taskContent
        }
      } else {
        this.editorOpt = {
          readOnly: false,
          language: 'shell',
          value: `#!/bin/bash\r# default bash`
        }
      }
      handleFetch(val, d => (this.optDatas = d))
    },
    handleSearch (val) {
      this.handleFetch(val, d => (this.optDatas = d))
    },
    handleFetch (value, callback) {
      if (this.timeout) {
        clearTimeout(this.timeout)
        this.timeout = null
      }
      let that = this
      this.currentValue = value

      function fake () {
        slurmJobList({
          pageNo: 1,
          pageSize: 10,
          taskName: value
        }).then(d => {
          if (that.currentValue === value) {
            const result = d.Data.data
            let data = []
            if (result) {
              result.forEach(r => {
                data.push(r)
              })
            } else {
              data = [
                {
                  taskName: value.replace(/[^0-9a-z_]/g, '')
                }
              ]
            }
            callback(data)
          }
        })
      }

      this.timeout = setTimeout(fake, 300)
    },
    addjuicefsPathRow () {
      this.temp.content.juicefsPath.push({
        value: ''
      })
    },
    removejuicefsPathRow (i) {
      if (this.temp.content.juicefsPath.length > 1) {
        this.temp.content.juicefsPath.splice(i, 1)
      }
    },
    getCpuAndGpuNum (part, gpuNumNow) {
      let gpuNumMax = 0
      let cpuNumMax = 0
      let coefficient = 8
      switch (part) {
        case 'A10':
          coefficient = 8
          gpuNumMax = 16
          break
        case 'A100':
          coefficient = 10
          gpuNumMax = 10
          break
        case 'A800':
          coefficient = 16
          gpuNumMax = 8
          break
        case 'H800':
          coefficient = 24
          gpuNumMax = 8
          break
        case 'new-H800':
          coefficient = 28
          gpuNumMax = 8
          break
        case '3090':
          coefficient = 8
          gpuNumMax = 10
          break
        case 'debug':
          gpuNumMax = 2
          break
      }
      if (gpuNumNow === 0) {
        switch (part) {
          case 'A10':
            cpuNumMax = 128
            break
          case 'A100':
            cpuNumMax = 128
            break
          case 'A800':
            cpuNumMax = 128
            break
          case 'H800':
            cpuNumMax = 192
            break
          case 'new-H800':
            cpuNumMax = 224
            break
          case '3090':
            cpuNumMax = 80
            break
        }
      } else {
        if (gpuNumNow > gpuNumMax) {
          gpuNumNow = gpuNumMax
        }
        cpuNumMax = gpuNumNow * coefficient
      }
      return [gpuNumMax, cpuNumMax]
    },
    handleCpuAndGpuNum () {
      // console.log('handleCpuAndGpuNum', this.privatePart)
      if (this.privatePart) {
        return
      }
      if (this.temp.node === 1) {
        this.getSlurmUseInfo()
      }
      let gpuNumMax = 1
      let cpuNumMax = 1
      let limitParts = []
      if (this.temp.content.choosePart === 'group') {
        limitParts = ['H800']
      } else {
        limitParts = this.temp.content.parts
      }
      limitParts.forEach((tag, index) => {
        [gpuNumMax, cpuNumMax] = this.getCpuAndGpuNum(tag, this.temp.content.gpuNum)
        if (this.gpuNumMax > gpuNumMax || index === 0) {
          this.gpuNumMax = gpuNumMax
        }
        if (this.cpuNumMax > cpuNumMax || index === 0) {
          this.cpuNumMax = cpuNumMax
        }
      })
      // console.log('handleCpuAndGpuNum', this.privatePart, 'gpuNumMax', this.gpuNumMax, 'gpuNumMax', this.cpuNumMax)
      // console.log('handleCpuAndGpuNum', this.privatePart, 'this.temp.content.gpuNum', gpuNumMax, 'this.temp.content.cpuNum', cpuNumMax)
      if (this.temp.content.gpuNum > this.gpuNumMax) {
        this.temp.content.gpuNum = this.gpuNumMax
      }
      // DEBUG 类型的 debug 分区，只限制 gpu，不限制 cpu
      if (this.temp.content.parts.includes('debug')) {
        return
      }
      if (this.temp.content.cpuNum > this.cpuNumMax) {
        this.temp.content.cpuNum = this.cpuNumMax
      }
    },
    handleNodeNumByTaskType (taskType) {
      if (taskType === 'DEBUG') {
        if (this.temp.content.nodeNum > 2) {
          this.temp.content.nodeNum = 2
        }
        this.temp.content.runTime = 1
      }
    },
    getSlurmUseInfo () {
      let displayPart = this.temp.content.groupPart
      if (this.temp.content.choosePart === 'group' || this.temp.content.parts.length === 0) {
        return
      }
      displayPart = this.temp.content.parts[0]
      if (this.temp.content.parts.includes('new-H800')) {
        this.titlePart = '(统计分区: A800,H800,new-H800)'
        displayPart = 'new-H800'
      } else if (this.temp.content.parts.includes('H800')) {
        this.titlePart = '(统计分区: A800,H800,new-H800)'
        displayPart = 'H800'
      } else if (this.temp.content.parts.includes('A800')) {
        this.titlePart = '(统计分区: A800,H800,new-H800)'
        displayPart = 'A800'
      } else if (this.temp.content.parts.includes('A100')) {
        this.titlePart = '(统计分区: A100)'
        displayPart = 'A100'
      } else if (this.temp.content.parts.includes('A10')) {
        this.titlePart = '(统计分区: A10)'
        displayPart = 'A10'
      } else {
        this.titlePart = '(统计分区: ' + this.temp.content.parts[0] + ')'
        displayPart = this.temp.content.parts[0]
      }
      this.temp.content.displayPart = displayPart
      let user = this.temp.founderEmail.split('@')[0]
      if (!this.temp.content.parts.includes('other')) {
        SlurmUseInfo({ part: this.temp.content.displayPart, user: user })
          .then(res => {
            this.tableGroup = res.Data.group || []
            this.tableAllGroup = res.Data.allGroup || []
          })
          .catch(err => {
          })
      }
    },
    handlePrivateNodeNumByTaskType () {
      // console.log(this.temp.content.groupPart)
      this.privatePart = this.temp.content.groupPart === 'other'
      this.handlePartChange('H800', true)
    },
    handlePartChange (tag, checked) {
      let displayPart = ''
      if (tag === 'H800') {
        displayPart = tag
        this.temp.content.parts = []
        this.temp.content.choosePart = 'group'
        // 4. 关联处理 gpuNum 和 cpuNum
        this.handleCpuAndGpuNum()
        // console.log('私有', this.temp.content.choosePart)
      } else {
        this.temp.content.choosePart = 'public'
        this.privatePart = false
        // console.log('公共', this.temp.content.choosePart)
        const selectedTags = this.temp.content.parts
        // 1. 私有卡或者debug不支持多选
        // 1.1 已选择私有卡或者debug卡后，选择其他卡时报错
        // 1.2 已选择别的卡，然后再选择私有卡或者debug卡
        if (((selectedTags.includes('other') || selectedTags.includes('debug')) && checked) ||
          (selectedTags.length > 0 && ((tag === 'other' || tag === 'debug') && checked))) {
          notification.error({
            message: '选择失败',
            description: '私有卡和debug不支持多选'
          })
          return
        }
        // 2. 选中或者取消
        let nextSelectedTags = checked ? [...selectedTags, tag] : selectedTags.filter(t => t !== tag)
        this.temp.content.parts = nextSelectedTags
        if (nextSelectedTags.length === 0) {
          // 4.3 没有选中则恢复默认数量为 1
          this.temp.content.gpuNum = 1
          this.temp.content.cpuNum = 1
          return
        } else {
          // 3.4 关联处理 gpuNum 和 cpuNum
          this.handleCpuAndGpuNum()
        }
        if (this.temp.content.groupPart !== '') {
          this.temp.content.groupPart = ''
        }
        displayPart = this.temp.content.parts[0]
        if (this.temp.content.parts.includes('new-H800')) {
          displayPart = 'new-H800'
        } else if (this.temp.content.parts.includes('H800')) {
          displayPart = 'H800'
        } else if (this.temp.content.parts.includes('A800')) {
          displayPart = 'A800'
        } else if (this.temp.content.parts.includes('A100')) {
          displayPart = 'A100'
        } else if (this.temp.content.parts.includes('A10')) {
          displayPart = 'A10'
        }
      }
      // 3 企微显示使用情况的目标 part
      this.temp.content.displayPart = displayPart
    },
    getDisplayPart () {
      let displayPart = this.temp.content.groupPart
      if (this.temp.content.parts.length !== 0) {
        displayPart = this.temp.content.parts[0]
        if (this.temp.content.parts.includes('new-H800')) {
          displayPart = 'new-H800'
        } else if (this.temp.content.parts.includes('H800')) {
          displayPart = 'H800'
        } else if (this.temp.content.parts.includes('A800')) {
          displayPart = 'A800'
        } else if (this.temp.content.parts.includes('A100')) {
          displayPart = 'A100'
        } else if (this.temp.content.parts.includes('A10')) {
          displayPart = 'A10'
        }
        this.temp.content.displayPart = displayPart
      }
    }
  }
}
</script>

<style scoped>
.partFirstTag {
  width: 75px;
  height: 32px;
  margin: 0 0 0 0;
  padding-top: 5px;
  font-size: 14px;
  text-align: center;
  border-color: #d9d9d9;
  border-left-width: 1px;
}

.partTag {
  width: 75px;
  height: 32px;
  margin: 0 0 0 0;
  padding-top: 5px;
  font-size: 14px;
  text-align: center;
  border-color: #d9d9d9;
  border-top-width: 1px;
  border-bottom-width: 1px;
  border-right-width: 0.5px;
  border-left-width: 0px;
//border-width: 1px;
}
</style>
