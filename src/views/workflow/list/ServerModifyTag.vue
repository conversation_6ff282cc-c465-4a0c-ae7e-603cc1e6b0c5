<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        :model="temp.content"
        ref="orderForm"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="IP">{{ temp.content.ip }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="服务器名">{{ temp.content.hostname }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="费用负责人" name="costUser">
              <a-select
                placeholder="请输入邮箱选择"
                v-model:value="temp.content.costUser"
                :showSearch="true"
                :allowClear="true"
                @search="searchUserEmailMethod"
                @select="getUserTeamListMethod"
              >
                <a-select-option v-for="email in userEmailList" :key="email" :value="email">
                  {{ email }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="负责人邮箱" name="principalEmails">
              <a-textarea
                v-model:value="temp.content.principalEmails"
                placeholder="负责人邮箱,，多人以|分割"
                auto-size
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="事业部/部门/团队" name="organization">
              <a-cascader v-model:value="temp.content.organization" :options="organizationOptions" change-on-select />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="费用承担业务方" name="business">
              <a-select v-model:value="temp.content.business" :allowClear="true" @change="changeCostAttr">
                <a-select-option v-for="business in businessList" :key="business" :value="business">
                  {{ business }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="项目" name="project">
              <a-select
                v-model:value="temp.content.project"
                :options="projects"
                :showSearch="true"
                :allowClear="true"
                placeholder="请输入项目名称并选择"
              ></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="环境" name="env">
              <a-select v-model:value="temp.content.env" placeholder="请选择环境">
                <a-select-option value="online">生产环境</a-select-option>
                <a-select-option value="pre">预发布环境</a-select-option>
                <a-select-option value="test">测试环境</a-select-option>
                <a-select-option value="dev">开发环境</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="团队" name="team">
              <a-select
                v-model:value="temp.content.team"
                :showSearch="true"
                :allowClear="true"
                placeholder="请输入团队名称并选择"
              >
                <a-select-option v-for="team in userTeamList" :key="team" :label="team" :value="team">
                  {{ team }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="使用人邮箱">
              <a-textarea v-model:value="temp.content.userEmails" placeholder="使用人邮箱,，多人以|分割" />
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="应用">
              <a-input v-model:value="temp.content.application" placeholder="应用名称，多个以|分割，选填" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="备注">
              <a-input v-model:value="temp.content.comment" placeholder="备注信息，选填" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="二次验证" name="mfa">
              <a-radio-group v-model:value="temp.content.mfa">
                <a-radio :value="1">开启</a-radio>
                <a-radio :value="2">关闭</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="连接方式" name="accessMethod">
              <a-radio-group v-model:value="temp.content.accessMethod">
                <a-radio :value="1">公网</a-radio>
                <a-radio :value="2">内网</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col
            :span="12"
            v-if="
              (temp.content.costAttr === '运营成本' &&
              temp.content.business !== '大数据' &&
              temp.content.business !== '搜索(DG)') || temp.content.costAttr === '开发支出'
            "
          >
            <a-form-model-item label="产品线" name="productLineList">
              <a-form name="productLineList_item" :model="temp.content.productLineList">
                <a-row
                  :gutter="24"
                  v-for="(productLine, index) in temp.content.productLineList"
                  v-bind="formItemLayoutWithOutLabel"
                >
                  <a-col :span="12">
                    <a-form-item name="form.productLineList[index].productLineList">
                      <a-cascader
                        v-model:value="productLine.productLineNameList"
                        :options="productLineOption"
                        change-on-select
                        placeholder="请选择产品线"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      name="form.productLineList[index].ratio"
                      :rules="[{ validator: (_, value, callback) => this.validateNumberSum(value, index, callback) }]"
                    >
                      <a-input-number
                        v-model:value="productLine.ratio"
                        :precision="4"
                        placeholder="比例"
                        :min="0"
                        :max="1"
                        :step="0.0001"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item>
                      <tx-button
                        v-if="index === 0"
                        type="primary"
                        icon="plus"
                        style="width: 30px"
                        size="small"
                        @click="addRow"
                      ></tx-button>
                      <tx-button
                        v-else
                        type="dashed"
                        size="small"
                        icon="minus"
                        style="width: 30px"
                        @click="removeRow(index)"
                      ></tx-button>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="是否DG数据资产" name="isDgDataAsset">
              <a-radio-group v-model:value="temp.content.isDgDataAsset" button-style="solid" @change="changeCostAttr">
                <a-radio-button :value="false">否</a-radio-button>
                <a-radio-button :value="true">是</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="Term代理">
              <a-select
                v-model:value="temp.content.termGatewayId"
                :showSearch="true"
                :allowClear="true"
                placeholder="请选择代理"
              >
                <a-select-option v-for="i in termGatewayInfo" :key="i.id">
                  {{ i.ip + '(' + i.name + ')' }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item
              label="DG数据资产类别"
              name="dgDataAssetClassification"
              v-if="temp.content.isDgDataAsset"
            >
              <a-select
                v-model:value="temp.content.dgDataAssetClassification"
                placeholder="请选择DG数据资产类别"
                @change="handleDGSpecificBusiness"
              >
                <a-select-option value="基础数据">基础数据</a-select-option>
                <a-select-option value="知识数据">知识数据</a-select-option>
                <a-select-option value="组件数据">组件数据</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="DG数据资产业务方" name="dgSpecificBusiness" v-if="temp.content.isDgBusiness">
              <a-select
                v-model:value="temp.content.dgSpecificBusiness"
                placeholder="请选择DG数据资产具体业务方"
                allowClear
              >
                <a-select-option value="AUTOM">AUTOM</a-select-option>
                <a-select-option value="CC">CC</a-select-option>
                <a-select-option value="CS">CS</a-select-option>
                <a-select-option value="DGB">DGB</a-select-option>
                <a-select-option value="DGC">DGC</a-select-option>
                <a-select-option value="DGG">DGG</a-select-option>
                <a-select-option value="SSG">SSG</a-select-option>
                <a-select-option value="公用">公用</a-select-option>
                <a-select-option value="数据中心">数据中心</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请理由" name="reason">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/asset-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <!--        <a-form-model-item v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 2 && node_status === 1" :wrapper-col="{ span: 16, offset: 4 }" >-->
        <!--          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>-->
        <!--        </a-form-model-item>-->
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { GetUserOrgAllInfo, getAssetInfo, getAssetListProject, getAssetListTeam } from '@/api/asset'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getUserList } from '@/api/permission/user'
import { allTermGatewayList } from '@/api/devops/term'
import { getProductLineList } from '@/api/cost/label'

const operation = [
  'CS-CC',
  'CS',
  'DG',
  'SSG',
  '搜索(DG)',
  '大数据',
  '市场',
  '招聘项目组',
  '营销云',
  '战略合作',
  '总裁办',
]

export default {
  name: 'ServerModifyTag',
  data() {
    this.operation = operation
    return {
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      organizationOptions: [],
      userEmailList: [],
      userTeamList: [],
      projects: [],
      termGatewayInfo: [],
      teams: [],
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      productLineOption: [],
      businessList: [],
      temp: {
        id: '',
        orderType: '服务器标签变更',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          id: undefined,
          ip: undefined,
          hostname: undefined,
          business: undefined,
          organization: [],
          env: undefined,
          principalEmails: undefined,
          userEmails: undefined,
          costUser: undefined,
          project: undefined,
          team: undefined,
          application: undefined,
          comment: undefined,
          reason: undefined,
          uuid: undefined,
          mfa: undefined,
          termGatewayId: undefined,
          costAttr: '运营成本',
          productLineList: [],
          isDgDataAsset: false,
          ownershipAsset: '合合',
          isDgBusiness: false,
          dgDataAssetClassification: '',
          dgSpecificBusiness: '',
          accessMethod: undefined,
        },
        timeline: [],
        comment: '',
      },
      rules: antdFormRulesFormat({
        'content.business': [{ required: true, message: '请选择费用承担业务方', trigger: 'blur' }],
        'content.organization': [{ required: true, message: '请选择事业部/部门', trigger: 'blur' }],
        'content.env': [{ required: true, message: '请选择环境', trigger: 'blur' }],
        'content.principalEmails': [{ required: true, message: '请填写负责人邮箱', trigger: 'blur' }],
        'content.costUser': [{ required: true, message: '请填写费用负责人', trigger: 'blur' }],
        'content.userEmails': [{ required: true, message: '请填写使用人邮箱', trigger: 'blur' }],
        'content.project': [{ required: true, message: '请填写项目', trigger: 'blur' }],
        'content.team': [{ required: true, message: '请填写团队', trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
        'content.mfa': [{ required: true, message: '请选择二次验证是否开启', trigger: 'change' }],
        'content.accessMethod': [{ required: true, message: '请选择连接方式', trigger: 'change' }],
        'content.productLineList': [{ required: true, message: '请选择产品线', trigger: 'change' }],
        'content.isDgDataAsset': [{ required: true, message: '请选择是否为DG数据资产', trigger: 'blur' }],
        'content.ownershipAsset': [{ required: true, message: '请选择资产归属', trigger: 'blur' }],
        'content.dgDataAssetClassification': [{ required: true, message: '请选择DG数据资产类别', trigger: 'blur' }],
      }),
      scrollPage: 1,
      valueData: '',
      treePageSize: 50,
    }
  },
  created() {
    this.getInfo()
    this.getBaseInfoList()
    this.getProductLineList()
  },
  methods: {
    getProductLineList() {
      getProductLineList().then(response => {
        if (response.Data !== undefined && response.Data !== null) {
          for (const productLine in response.Data.productLineMap) {
            if (Object.hasOwnProperty.call(response.Data.productLineMap, productLine)) {
              const secondaryProductLineList = response.Data.productLineMap[productLine].secondaryProductLine
              let childrenList = []
              for (let i = 0; i < secondaryProductLineList.length; i++) {
                let tmp = {
                  label: secondaryProductLineList[i],
                  value: secondaryProductLineList[i],
                }
                childrenList.push(tmp)
              }
              this.productLineOption.push({
                label: productLine,
                value: productLine,
                children: childrenList,
              })
            }
          }
        }
      })
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.assetId) {
        getAssetInfo(this.$route.query.assetId).then(response => {
          this.temp.content.id = response.Data.id + ''
          this.temp.content.ip = response.Data.ip
          this.temp.content.hostname = response.Data.hostname
          this.temp.content.business = response.Data.business
          this.temp.content.organization = [response.Data.org, response.Data.dep, response.Data.team]
          this.temp.content.principalEmails = response.Data.principalEmails
          this.temp.content.userEmails = response.Data.userEmails
          this.temp.content.costUser = response.Data.costUser
          this.temp.content.project = response.Data.project
          this.temp.content.team = response.Data.team
          this.temp.content.env = response.Data.env
          this.temp.content.application = response.Data.application
          this.temp.content.comment = response.Data.comment
          this.temp.content.uuid = response.Data.uuid
          this.temp.content.mfa = response.Data.mfa
          this.temp.content.ownershipAsset = response.Data.ownershipAsset
          this.temp.content.accessMethod = response.Data.accessMethod
          this.temp.content.termGatewayId = response.Data.termGatewayId
          let productLineListDisplay = []
          if (response.Data.productLine !== null && response.Data.productLine.length > 0) {
            let productLineLists = response.Data.productLine.split(',')
            for (let i = 0; i < productLineLists.length; i++) {
              let lineThroughIndex = productLineLists[i].indexOf('-')
              let colonIndex = productLineLists[i].indexOf(':')
              let primaryProductLine = productLineLists[i].slice(0, lineThroughIndex)
              if (colonIndex === -1) {
                productLineListDisplay.push({
                  productLineNameList: [primaryProductLine, productLineLists[i].slice(0, productLineLists[i].length)],
                  ratio: 1,
                })
              } else {
                productLineListDisplay.push({
                  productLineNameList: [primaryProductLine, productLineLists[i].slice(0, colonIndex)],
                  ratio: parseFloat(productLineLists[i].slice(colonIndex + 1, productLineLists[i].length)),
                })
              }
            }
          } else {
            productLineListDisplay.push({
              productLineNameList: [],
              ratio: 0,
            })
          }
          this.temp.content.productLineList = productLineListDisplay
          if (response.Data.dgDataAsset.length > 0) {
            const arr = response.Data.dgDataAsset.split('-')
            if (arr.length > 0) {
              this.temp.content.isDgDataAsset = arr[0] === '是'
            }
            if (arr.length > 1) {
              this.temp.content.dgDataAssetClassification = arr[1]
            }
            if (arr.length > 2) {
              this.temp.content.dgSpecificBusiness = arr[2]
            }
          }
          this.getUserTeamListMethod(this.temp.content.costUser)
        })
        this.nodeStatus = 1
      } else if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            let productLineListDisplay = []
            if (response.Data.productLine !== null && response.Data.productLine.length > 0) {
              let productLineLists = response.Data.productLine.split(',')
              for (let i = 0; i < productLineLists.length; i++) {
                let lineThroughIndex = productLineLists[i].indexOf('-')
                let colonIndex = productLineLists[i].indexOf(':')
                let primaryProductLine = productLineLists[i].slice(0, lineThroughIndex)
                if (colonIndex === -1) {
                  productLineListDisplay.push({
                    productLineNameList: [primaryProductLine, productLineLists[i].slice(0, productLineLists[i].length)],
                    ratio: 1,
                  })
                } else {
                  productLineListDisplay.push({
                    productLineNameList: [primaryProductLine, productLineLists[i].slice(0, colonIndex)],
                    ratio: parseFloat(productLineLists[i].slice(colonIndex + 1, productLineLists[i].length)),
                  })
                }
              }
            } else {
              productLineListDisplay.push({
                productLineNameList: [],
                ratio: 0,
              })
            }
            this.temp.content.productLineList = productLineListDisplay
            if (response.Data.dgDataAsset.length > 0) {
              const arr = response.Data.dgDataAsset.split('-')
              if (arr.length > 0) {
                this.temp.content.isDgDataAsset = arr[0] === '是'
              }
              if (arr.length > 1) {
                this.temp.content.dgDataAssetClassification = arr[1]
              }
              if (arr.length > 2) {
                this.temp.content.dgSpecificBusiness = arr[2]
              }
            }
            this.getUserTeamListMethod(this.temp.content.costUser)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    getBaseInfoList() {
      getAssetListProject().then(res => {
        for (let i = 0, len = res.Data.project.length; i < len; i++) {
          let pro = {}
          pro.value = res.Data.project[i]
          pro.label = res.Data.project[i]
          this.projects.push(pro)
        }
      })
      getAssetListTeam().then(res => {
        for (let i = 0, len = res.Data.team.length; i < len; i++) {
          let label = {}
          label.value = res.Data.team[i]
          label.label = res.Data.team[i]
          this.teams.push(label)
        }
      })
      allTermGatewayList().then(res => {
        this.termGatewayInfo = res.Data.data
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/server-modify-tag', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          try {
            this.checkProductLine()
          } catch (e) {
            this.$message.error(e.toString())
            return
          }
          this.temp.content.dgDataAsset = '否-'
          if (this.temp.content.isDgDataAsset) {
            this.temp.content.dgDataAsset = '是-' + this.temp.content.dgDataAssetClassification
          }
          if (this.temp.content.dgSpecificBusiness != null && this.temp.content.dgSpecificBusiness.length > 0) {
            this.temp.content.dgDataAsset += '-' + this.temp.content.dgSpecificBusiness
          } else {
            this.temp.content.dgDataAsset += '-'
          }
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      }).catch(() => {})
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 2
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        let arry = response.Data.data
        for (let i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    getUserTeamListMethod(userEmail) {
      this.userTeamList = []
      if (userEmail === '') {
        return
      }
      GetUserOrgAllInfo({ email: userEmail }).then(response => {
        this.businessList = response.Data.business
        this.temp.content.business = this.businessList[0]
        if (this.temp.content.business === 'DG') {
          this.temp.content.isDgBusiness = true
        }
        this.organizationOptions = response.Data.orgCas
        this.temp.content.organization = []
        if (this.organizationOptions[0].label !== undefined && this.organizationOptions[0].label !== null) {
          this.temp.content.organization.push(this.organizationOptions[0].label)
          if (this.organizationOptions[0].children !== null && this.organizationOptions[0].children[0].label !== null) {
            this.temp.content.organization.push(this.organizationOptions[0].children[0].label)
            if (
              this.organizationOptions[0].children[0].children !== null &&
              this.organizationOptions[0].children[0].children[0].label !== null
            ) {
              this.temp.content.organization.push(this.organizationOptions[0].children[0].children[0].label)
            }
          }
        }
        if (this.operation.includes(this.temp.content.business)) {
          this.temp.content.costAttr = '运营成本'
          if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
            this.temp.content.costAttr = '开发支出'
          }
          // this.temp.content.productLineList = [{
          //   productLineNameList: [],
          //   ratio: 0,
          // }]
        } else {
          this.temp.content.costAttr = '研发成本'
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    checkProductLine() {
      if (
        this.temp.content.costAttr === '研发成本' ||
        this.temp.content.business === '大数据' ||
        this.temp.content.business === '搜索(DG)'
      ) {
        return
      }
      if (this.temp.content.productLineList.length > 5) {
        throw new Error('目前最多支持5个产品线，如有需求请联系运维管理员')
      }
      let sumRatios = 0
      let productLineArr = []
      let existArr = []
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (
          this.temp.content.productLineList[i].productLineNameList === undefined ||
          this.temp.content.productLineList[i].productLineNameList.length < 2
        ) {
          throw new Error('请填充完所有产品线空格')
        }
        if (existArr.includes(this.temp.content.productLineList[i].productLineNameList[1])) {
          throw new Error('产品线重复：' + this.temp.content.productLineList[i].productLineNameList[1])
        }
        productLineArr.push(
          this.temp.content.productLineList[i].productLineNameList[1] + ':' + this.temp.content.productLineList[i].ratio
        )
        sumRatios += this.temp.content.productLineList[i].ratio
        existArr.push(this.temp.content.productLineList[i].productLineNameList[1])
      }
      sumRatios = sumRatios.toFixed(4)
      if (sumRatios !== '1.0000') {
        const errStr = '比例之和： ' + sumRatios + '≠ 1'
        throw new Error(errStr)
      }
      this.temp.content.productLine = productLineArr.join(',')
    },
    addRow() {
      this.temp.content.productLineList.push({
        productLineNameList: [],
        ratio: 0,
      })
    },
    // 移除某行
    removeRow(i) {
      if (this.temp.content.productLineList.length > 1) {
        this.temp.content.productLineList.splice(i, 1)
      }
    },
    handleDGSpecificBusiness() {
      if (this.temp.content.dgDataAssetClassification !== '') {
        this.temp.content.dgSpecificBusiness = '数据中心'
      }
    },
    validateNumberSum(_, currentIndex, callback) {
      callback = antdFormValidateCallback
      let total = 1
      for (let i = 0; i < this.temp.content.productLineList.length; i++) {
        if (i === currentIndex) {
          continue
        }
        total -= this.temp.content.productLineList[i].ratio
      }
      if ((total - this.temp.content.productLineList[currentIndex].ratio).toFixed(4) < 0) {
        this.temp.content.productLineList[currentIndex].ratio = 0
        const totalFixed = total.toFixed(4)
        return callback(new Error('不得超过' + totalFixed))
      }
      return callback()
    },
    changeCostAttr() {
      if (this.operation.includes(this.temp.content.business)) {
        this.temp.content.costAttr = '运营成本'
        if (this.temp.content.business === 'DG' && this.temp.content.isDgDataAsset) {
          this.temp.content.costAttr = '开发支出'
        }
        // this.temp.content.productLineList = [
        //   {
        //     productLineNameList: [],
        //     ratio: 0,
        //   },
        // ]
      } else {
        this.temp.content.costAttr = '研发成本'
      }
    }
  },
}
</script>

<style scoped></style>
