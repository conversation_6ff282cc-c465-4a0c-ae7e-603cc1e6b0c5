<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        layout="horizontal"
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" :gutter="24" label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="白名单表单">
          <tx-button type="primary" @click="addRow">新增</tx-button>
          <a-table
            style="margin-top: 18px"
            bordered
            :pagination="pagination"
            :columns="tablecolumns"
            :data-source="temp.content.redis"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'instanceId'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-select
                      v-model:value="record.instanceId"
                      placeholder="输入实例ID"
                      :showSearch="true"
                      :allowClear="true"
                      :filter-option="filterOption"
                      @change="instanceIdChange(record)"
                    >
                      <a-select-option v-for="item in instanceIdList" :key="item" :value="item">
                        {{ item }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-if="column.dataIndex == 'ipWhiteGroupName'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <a-select
                      v-model:value="record.ipWhiteGroupName"
                      placeholder="先选实例ID,再选择组,或者输入新值。"
                      :showSearch="true"
                      :allowClear="true"
                      :filter-option="false"
                      :mode="'tags'"
                      @change="ipWhiteGroupNameChange(record)"
                    >
                      <a-select-option v-for="item in ipWhiteGroupNameList" :key="item" :value="item">
                        {{ item }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-if="column.dataIndex == 'ipWhiteList'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table" label="IP白名单">
                    <a-textarea
                      v-model:value="record.ipWhiteList"
                      :autoSize="{ minRows: 2, maxRows: 20 }"
                      placeholder="IP之间以逗号隔开, 格式如下: 0.0.0.0/0,***********, 或者***********/24 (CIDR模式,无类域间路由,/24表示地址中前缀的长度,范围1-32)。"
                    />
                    <a
                      v-if="validateIpWhiteList(record.ipWhiteList)"
                      class="ant-form-item—table"
                      label="错误提示"
                      style="color: red"
                    >
                      IP白名单中不能包含 | 分割符，换行符。
                    </a>
                    <a v-if="validateIpWhiteList(record.ipWhiteList)" class="ant-form-item—table" style="color: green">
                      IP之间以逗号隔开, 格式如下: 0.0.0.0/0,***********, 或者***********/24
                      (CIDR模式,无类域间路由,/24表示地址中前缀的长度,范围1-32)。
                    </a>
                  </a-form-model-item>
                </a-form-model>
              </template>
              <template v-if="column.dataIndex == 'errorInfo'">
                <a>{{ record.errorInfo }}</a>
              </template>
              <template v-else-if="column.dataIndex == 'action'">
                <a-form-model :ref="record.index" :model="record">
                  <a-form-model-item class="ant-form-item—table">
                    <tx-button type="danger" size="small" @click="deleteRow(record)">删除</tx-button>
                  </a-form-model-item>
                </a-form-model>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/评论">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-textarea v-model:value="temp.comment" />
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { filterLabelValue } from '@aim/helper'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getInstanceGroup } from '@/api/db/redis'

const statusMap = {
  2: {
    status: 'error',
    text: '未执行',
  },
  1: {
    status: 'success',
    text: '成功',
  },
}

const columns = [
  {
    title: '操作',
    dataIndex: 'action',
    width: '80px',
    scopedSlots: { customRender: 'action' },
    align: 'center',
  },
  {
    title: 'redis实例',
    dataIndex: 'instanceId',
    width: '250px',
    scopedSlots: { customRender: 'instanceId' },
  },
  {
    title: 'IP白名单组名',
    dataIndex: 'ipWhiteGroupName',
    scopedSlots: { customRender: 'ipWhiteGroupName' },
  },
  {
    title: 'IP白名单列表',
    dataIndex: 'ipWhiteList',
    width: '500px',
    scopedSlots: { customRender: 'ipWhiteList' },
  },
  {
    title: '结果',
    dataIndex: 'errorInfo',
    scopedSlots: { customRender: 'errorInfo' },
  },
]
export default {
  name: 'AliyunRedisSafeGroup',
  components: {},
  filterOption: filterLabelValue,
  data: function () {
    this.tablecolumns = columns
    return {
      fetching: false,
      userEmailList: [],
      value: [],
      pagination: {
        defaultPageSize: 1000,
        hideOnSinglePage: true,
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      localUser: store.getters.email,
      time_now: '',
      size: 'small',
      loading: false,
      instanceIdList: [],
      ipWhiteGroupNameList: [],
      node_status: 1,
      temp: {
        id: '',
        orderType: '阿里云Redis白名单',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          redis: [
            { id: 0, instanceId: undefined, ipWhiteGroupName: undefined, ipWhiteList: undefined, errorInfo: '未执行' },
          ],
          reason: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      dbList: [],
    }
  },
  created() {
    this.getInfo()
    this.getInstance()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    addRow() {
      this.temp.content.redis.push({
        id: this.temp.content.redis.length,
        instanceId: undefined,
        ipWhiteGroupName: undefined,
        ipWhiteList: undefined,
        errorInfo: '未执行',
      })
    },
    deleteRow(record) {
      if (this.temp.content.redis.length > 1) {
        this.temp.content.redis.splice(record.id, 1)
        for (let i = record.id; i < this.temp.content.redis.length; i++) {
          this.temp.content.redis[i].id -= 1
        }
      }
    },
    ipWhiteGroupNameChange(record) {
      if (
        this.temp.content.redis[record.id].ipWhiteGroupName != undefined &&
        this.temp.content.redis[record.id].ipWhiteGroupName.length > 1
      ) {
        this.temp.content.redis[record.id].ipWhiteGroupName = [this.temp.content.redis[record.id].ipWhiteGroupName[1]]
      }
    },
    getInstance() {
      getInstanceGroup().then(response => {
        this.instanceIdList = response.Data.list
      })
    },

    instanceIdChange(record) {
      this.ipWhiteGroupNameList = []
      let sendBody = {
        instanceId: record.instanceId,
      }
      getInstanceGroup(sendBody).then(response => {
        this.ipWhiteGroupNameList = response.Data.list
      })
    },
    validateIpWhiteList(value) {
      const regex = /(\|)|(\n)/
      let m
      if ((m = regex.exec(value)) !== null) {
        return true
      }
      return false
    },
    createData() {
      for (let i = 0; i < this.temp.content.redis.length; i++) {
        if (this.validateIpWhiteList(this.temp.content.redis[i].ipWhiteList)) {
          return false
        }
      }

      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/redis-safegroup', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style lang="less">
.dataBaseTable {
  /deep/.ant-table-header {
    font-size: 12px;
  }
  /deep/ .col-one-line {
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  /deep/ .ant-table-tbody {
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 6px;
  }
}
.ant-form-item—table {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  vertical-align: top;
}

.jsoneditor-vue {
  height: 100%;
}
</style>
