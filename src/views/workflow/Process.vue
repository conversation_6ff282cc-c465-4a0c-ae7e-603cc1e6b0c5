<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2024-11-27 14:16:23
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2025-01-08 10:37:22
 * @FilePath: \cloud_web\src\views\workflow\Process.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <page-header-wrapper>
    <SecondaryVerification>
      <ProcessComp />
    </SecondaryVerification>
  </page-header-wrapper>
</template>

<script>
import SecondaryVerification from '@/components/Verification/verify.vue'
import ProcessComp from './workFLowComp/ProcessComp.vue'
export default {
  components: {
    SecondaryVerification,
    ProcessComp,
  },
}
</script>
