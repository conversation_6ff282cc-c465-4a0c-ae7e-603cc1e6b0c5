/*
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-10-30 14:10:51
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-06-26 18:18:14
 * @FilePath: \cloud_web\src\views\workflow\bigdata\originData.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
const originDataColumn = [
  {
    title: '数据库IP',
    dataIndex: 'db_ip',
    key: 'db_ip',
    // slots: { title: 'customTitle' },
    tips: '111111',
    scopedSlots: { customRender: 'db_ip' },
  },
  {
    title: '数据库名',
    dataIndex: 'db_name',
    key: 'db_name',
    scopedSlots: { customRender: 'db_name' },
  },
  {
    title: '表名',
    dataIndex: 'table_name',
    key: 'table_name',
    scopedSlots: { customRender: 'table_name' },
  },
  {
    title: '数据字段类型',
    dataIndex: 'db_field_type',
    key: 'db_field_type',
    scopedSlots: { customRender: 'db_field_type' },
  },
  {
    title: '数据字段名称',
    dataIndex: 'db_field',
    key: 'db_field',
    scopedSlots: { customRender: 'db_field' },
  },
  {
    title: '数据类型',
    dataIndex: 'db_type',
    key: 'db_type',
    scopedSlots: { customRender: 'db_type' },
  },
  {
    title: '抽取周期/次数',
    dataIndex: 'extraction_cycle',
    key: 'extraction_cycle',
    scopedSlots: { customRender: 'extraction_cycle' },
  },
  // {
  //   title: '数量',
  //   dataIndex: 'db_num',
  //   key: 'db_num',
  //   scopedSlots: { customRender: 'db_num' }
  // },
  {
    title: '服务产品',
    dataIndex: 'service_products',
    key: 'service_products',
    scopedSlots: { customRender: 'service_products' },
  },
  {
    title: '抽取类型',
    dataIndex: 'extraction_type',
    key: 'extraction_type',
    scopedSlots: { customRender: 'extraction_type' },
  },
  {
    title: '主键',
    dataIndex: 'primary_key',
    key: 'primary_key',
    scopedSlots: { customRender: 'primary_key' },
  },
  {
    title: '增量字段',
    dataIndex: 'incrementalField',
    key: 'incrementalField',
    scopedSlots: { customRender: 'incrementalField' },
    fixed: 'right',
  },
]
export { originDataColumn }
