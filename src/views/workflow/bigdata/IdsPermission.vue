<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="大数据负责人审批" />
        <!-- <a-step title="管理员审批" /> -->
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="deleteForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <!-- <a-row :gutter="24"></a-row> -->
        <a-row :gutter="24">
          <a-col :span="16">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
            <a-form-model-item label="页面权限">
              <a-tree
                v-if="authorityTree.length"
                checkable
                :defaultExpandAll="defaultExpandAll"
                ref="myTree"
                v-model:checkedKeys="temp.content.checkedKeys"
                :tree-data="authorityTree"
                :fieldNames="replaceFields"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item v-if="temp.node > 0" label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
            <a-form-model-item name="reason" label="理由">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
            <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/备注">
              <a-textarea v-model:value="temp.content.comment" />
            </a-form-model-item>
            <a-form-model-item label="审批节点" v-if="temp.node > 0">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
            <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
              <tx-button type="primary" @click="createData">提交</tx-button>
              <tx-button style="margin-left: 10px">
                <router-link to="/domain/dns-list">取消</router-link>
              </tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="leaderApproveData" style="margin-left: 100px">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 20px">拒绝</tx-button>
            </a-form-model-item>
            <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="bigdataApproveData" style="margin-left: 100px">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 20px">拒绝</tx-button>
            </a-form-model-item>
            <!-- <a-form-model-item
              v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
              :wrapper-col="{ span: 16, offset: 4 }"
            >
              <tx-button type="primary" @click="approveData" style="margin-left: 100px">同意</tx-button>
              <tx-button type="danger" @click="rejectData" style="margin-left: 20px">拒绝</tx-button>
            </a-form-model-item> -->
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { roleTrees } from '@/api/bigdata/bigdata_resource_approval'
export default {
  name: 'ServerDelete',
  data() {
    return {
      authorityTree: [],
      replaceFields: {
        title: 'label',
      },
      defaultExpandAll: false,
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      temp: {
        id: undefined,
        orderType: 'IDS模块权限申请',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        time_now: '',
        handler: '',
        handlerEmail: '',
        content: {
          checkedKeys: [],
          checkedLabels: [],
          checkeDisabled: [],
          reason: '',
          comment: '',
        },
        timeline: [],
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      modalFormPreServerDeleteVisible: false,
      preShutdownMsgs: [{}],
    }
  },
  created() {
    this.getTree()
  },
  methods: {
    handleTreeCheck() {
      roleTrees().then(res => {
        if (res.data.code === 0) {
          const userInfo = noc.user.getUserInfo()
          this.authorityTree = res.data.data.roles
          if (userInfo.email === this.temp.founderEmail) {
            this.temp.content.checkedKeys = [...res.data.data.checked, ...this.temp.content.checkedKeys]
            this.temp.content.checkeDisabled = [...res.data.data.checked]
            this.disabledCheck()
          }
          this.defaultExpandAll = true
        }
      })
    },
    getTree() {
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id)
          .then(response => {
            response.Data.content = JSON.parse(response.Data.content.data)
            if (response.Data.orderType === this.temp.orderType) {
              this.temp = response.Data
            } else {
              this.$router.push({ path: '/404' })
            }
          })
          .finally(() => {
            this.handleTreeCheck()
          })
      } else {
        this.handleTreeCheck()
      }
    },
    /**
     * 提交表单数据
     */
    // 禁用已选项目
    disabledCheck() {
      this.temp.content.checkeDisabled.forEach(item => {
        this.authorityTree.forEach(tree => {
          const findKey = node => {
            if (node.key === item) {
              node.disableCheckbox = true
            } else if (node.children) {
              node.children.forEach(child => {
                findKey(child)
              })
            }
          }

          tree.children.forEach(child => {
            findKey(child)
          })
        })
      })
    },
    getTreeCheckLabel() {
      this.temp.content.checkedLabels = []
      const findLabel = (tree, item) => {
        if (tree.key === item) {
          this.temp.content.checkedLabels.push(tree.label)
        } else if (tree.children) {
          tree.children.forEach(child => {
            findLabel(child, item)
          })
        }
      }
      let newCheckedKeys = this.temp.content.checkedKeys.filter(key => !this.temp.content.checkeDisabled.includes(key))

      newCheckedKeys.forEach(item => {
        this.authorityTree.forEach(tree => {
          findLabel(tree, item)
        })
      })
    },
    createData() {
      this.getTreeCheckLabel()
      // 检查是否成功获取到标签
      if (this.temp.content.checkedLabels.length === 0 || this.temp.content.checkedKeys.length == 0) {
        notification.warning({
          message: '未选择权限，或者 标签获取失败',
          description: '无法获取所选权限的标签信息，请刷新页面重试',
        })
        return
      }
      antdFormValidate(this.$refs.deleteForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              // this.$router.push({ path: '/workflow/ids-permission', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      this.getTreeCheckLabel()
      // 检查是否成功获取到标签
      if (this.temp.content.checkedLabels.length === 0 || this.temp.content.checkedKeys.length == 0) {
        notification.warning({
          message: '未选择权限，或者 标签获取失败',
          description: '无法获取所选权限的标签信息，请刷新页面重试',
        })
        return
      }
      antdFormValidate(this.$refs.deleteForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    bigdataApproveData() {
      this.getTreeCheckLabel()
      // 检查是否成功获取到标签
      if (this.temp.content.checkedLabels.length === 0 || this.temp.content.checkedKeys.length == 0) {
        notification.warning({
          message: '未选择权限，或者 标签获取失败',
          description: '无法获取所选权限的标签信息，请刷新页面重试',
        })
        return
      }
      antdFormValidate(this.$refs.deleteForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '请求已提交, 可稍后查询处理结果！',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    approveData() {
      this.getTreeCheckLabel()
      antdFormValidate(this.$refs.deleteForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '请求已提交, 可稍后查询处理结果！',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
            }
          })
        }
      })
    },
    rejectData() {
      this.getTreeCheckLabel()
      antdFormValidate(this.$refs.deleteForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 4
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
