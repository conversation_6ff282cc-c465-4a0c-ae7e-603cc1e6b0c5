<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{
          style: {
            width: '120px',
          },
        }"
      >
        <a-row :gutter="24" v-if="temp.node == 0">
          <a-col :span="10">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24" v-if="temp.node == 0">
          <a-col :span="10">
            <a-form-model-item label="资产所属">
              <a-checkbox
                @change="assetBelongsChange"
                :value="'self'"
                :checked="temp.content.assetBelongsTo.user == 'self'"
              >
                本人
              </a-checkbox>
              <a-checkbox
                :disabled="true"
                @change="assetBelongsChange"
                :value="'worker'"
                :checked="temp.content.assetBelongsTo.user == 'worker'"
              >
                名下员工
              </a-checkbox>
              <!-- <a-tree-select
                :disabled="temp.node != 0"
                v-if="temp.content.assetBelongsTo.user == 'worker'"
                :value="temp.content.assetBelongsTo.mail"
                style="width: calc(100% - 160px)"
                :tree-data="treeData"
                :show-checked-strategy="SHOW_PARENT"
                show-search
                :placeholder="'请选择用户'"
                @change="treeChange"
              /> -->
            </a-form-model-item>
          </a-col>
          <a-col :span="7">
            <a-form-model-item label="资产大类">
              <a-select
                @change="assetClassChange"
                :disabled="temp.node != 0"
                v-model:value="temp.content.assetClass"
                labelInValue="true"
                :size="'middle'"
                style="width: 200px"
                :options="optList"
              ></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="7">
            <a-form-model-item label="转交类型">
              <!-- <a-checkbox
                :disabled="temp.content.assetClass.label != 'Scriptis-脚本'"
                @change="handoverTypeChange"
                :value="'copy'"
                :checked="temp.content.handoverType == 'copy'"
              >
                复制
              </a-checkbox> -->
              <a-checkbox
                :disabled="temp.node != 0"
                @change="handoverTypeChange"
                :value="'transfer'"
                :checked="temp.content.handoverType == 'transfer'"
              >
                转移
              </a-checkbox>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24" v-if="temp.node == 0">
          <a-col :span="15">
            <a-form-model-item label="资产明细">
              <a-button type="primary" size="small" @click="checkAllAssets">取消/选中全部</a-button>

              <!--
            这里下拉的options 采用字典模式，前期获取好所有大类的options，用大类value值用作key
            根据已选择大类的value值来取options
           -->
              <a-select
                :disabled="temp.node != 0"
                :max-tag-count="3"
                v-model:value="temp.content.assetDetails"
                :options="options[temp.content.assetClass.value]"
                mode="tags"
                :size="'small'"
                placeholder="请选择资产明细"
                style="width: calc(100% - 120px); height: 22px; margin-left: 8px"
              ></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="9">
            <a-form-model-item label="转交对象">
              <!-- <a-tree-select
                :disabled="temp.node != 0"
                :value="temp.content.handOverUser"
                style="width: calc(100%)"
                :multiple="false"
                :tree-data="treeData"
                :show-checked-strategy="SHOW_PARENT"
                show-search
                :tree-checkable="true"
                :placeholder="'请选择用户'"
                @change="handOverChange"
              /> -->
              <a-select
                v-model:value="temp.content.handOverUser"
                style="width: calc(100%)"
                mode="multiple"
                show-search
                :placeholder="'请选择用户'"
                @change="handOverChange"
              >
                <a-select-option
                  v-for="i in treeData"
                  :value="
                    JSON.stringify({
                      email: i.email,
                      uid: Number(i.uid),
                      name: i.name,
                    })
                  "
                >
                  {{ i.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <p
          v-if="temp.node == 0"
          @click="addList"
          style="
            text-align: center;
            height: 30px;
            line-height: 30px;
            font-size: 16px;
            border: 1px dashed #ccc;
            cursor: pointer;
          "
        >
          添加
        </p>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="已选择资产转交">
              <p style="margin-top: 4px">剩余资产数量{{ temp.content.residueNum }}</p>
              <a-table :columns="columns" :data-source="temp.content.tableData">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'assetClass'">
                    {{ record.assetClass.label }}
                  </template>
                  <template v-if="column.key === 'handoverType'">
                    <a-checkbox
                      :disabled="temp.node != 0"
                      @change="recordHandoverTypeChange(record, 'transfer')"
                      :value="'transfer'"
                      :checked="record.handoverType == 'transfer'"
                    >
                      转移
                    </a-checkbox>
                  </template>
                  <template v-if="column.key === 'assetDetails'">
                    <a-select
                      :disabled="temp.node != 0"
                      v-model:value="record.assetDetails"
                      :options="options[record.assetClass.value]"
                      mode="tags"
                      :size="'small'"
                      placeholder="Please select"
                      style="width: 100%"
                    ></a-select>
                  </template>
                  <template v-if="column.key === 'handOverUser'">
                    <!-- <a-tree-select
                      :disabled="temp.node != 0"
                      :value="record.handOverUser"
                      style="width: calc(100%)"
                      :tree-data="treeData"
                      :show-checked-strategy="SHOW_PARENT"
                      show-search
                      :tree-checkable="true"
                      :placeholder="'请选择用户'"
                      @change="recprdHandOverChange($event, record)"
                    /> -->

                    <a-select
                      v-model:value="record.handOverUser"
                      style="width: calc(100%)"
                      mode="multiple"
                      show-search
                      :placeholder="'请选择用户'"
                      @change="recprdHandOverChange($event, record)"
                    >
                      <a-select-option
                        v-for="i in treeData"
                        :value="
                          JSON.stringify({
                            email: i.email,
                            uid: Number(i.uid),
                            name: i.name,
                          })
                        "
                      >
                        {{ i.name }}
                      </a-select-option>
                    </a-select>
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-popconfirm
                      title="是否确认删除"
                      ok-text="Yes"
                      cancel-text="No"
                      @confirm="confirm(record)"
                      @cancel=""
                    >
                      <a-button :disabled="temp.node != 0" type="primary" danger>删除</a-button>
                    </a-popconfirm>
                  </template>
                </template>
              </a-table>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="10">
            <a-form-model-item name="reason" label="问题描述">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/评论">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(false)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData(true)">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { TreeSelect } from 'ant-design-vue'
import { allOrgUser } from '@/api/ssoApi'
import store from '@/store'
// import cloneDeep from 'lodash.clonedeep'
import { deepClone } from '@/utils/util'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
const SHOW_PARENT = TreeSelect.SHOW_PARENT
import { assetsList } from '@/api/bigdata/bigdata_request'
import { template } from '@antv/g2plot/lib/utils'
const columns = [
  {
    title: '资产大类',
    dataIndex: 'assetClass',
    key: 'assetClass',
    width: 140,
  },
  {
    title: '转交类型',
    key: 'handoverType',
    width: 120,
  },
  {
    title: '资产明细',
    dataIndex: 'assetDetails',
    key: 'assetDetails',
  },
  {
    title: '转交对象',
    key: 'handOverUser',
    dataIndex: 'handOverUser',
    width: 260,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
  },
]
import { getUserEmailList, getUserList } from '@/api/permission/user'

export default {
  name: 'BigdataAssetChange',
  data() {
    return {
      SHOW_PARENT,
      columns,
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      catchLenth: 0,
      options: {
        scripts: [1],
        airflow_task: [2],
        quality_rules: [3],
        scripts_dir: [4],
      },
      optList: [
        {
          label: 'Scriptis-脚本 (脚本)',
          value: 'scripts',
          disabled: false,
        },
        {
          label: '作业上线任务',
          value: 'airflow_task',
        },
        {
          label: '质量规则-规则',
          value: 'quality_rules',
        },
        {
          label: 'Scripts-脚本 (目录)',
          value: 'scripts_dir',
          disabled: false,
        },
      ],
      temp: {
        id: '',
        orderType: '大数据资产转交',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          leaveUser: {
            uid: '',
            cname: '',
            email: '',
            ename: '',
          },
          tableData: [],
          reason: '',
          assetBelongsTo: {
            user: 'self',
            mail: [],
          },
          assetClass: {
            label: 'Scriptis-脚本',
            value: 'scripts',
          },
          handoverType: 'transfer',
          assetDetails: [],
          handOverUser: [],
          residueNum: 0,
          formatData: { items: [] },
        },
        timeline: [],
        comment: '',
      },
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      userlist: [],
      treeData: [],
    }
  },
  created() {
    this.getInfo()
  },
  watch: {
    'temp.content.tableData': {
      handler: function (n, v) {
        if (n) {
          let arr = []
          n.forEach(i => {
            arr.push(...i.assetDetails)
          })
          arr = [...new Set(arr)]
          // this.temp.content.residueNum = this.temp.content.residueNum - arr.length
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    getUserEmailList().then(res => {
      console.log(res, 'res')
      this.treeData = res.Data.items
    })

    // allOrgUser({ need_pre_entry: true, need_org: true, need_user: true })
    //   .then(res => {
    //     if (res.data.data && res.data.data.child_org) {
    //       this.userlist = res.data.data
    //       this.treeData = this.loopTreeData([this.userlist])
    //     } else {
    //       this.treeData = this.handleTreeData(res.data.data)
    //     }
    //   })
    //   .catch(() => {})
    // 一次性获取所有明细数据，用字典形式封装，也方便下面的表格进行更改
    Promise.all([
      this.queryAssets('scripts'),
      this.queryAssets('quality_rules'),
      this.queryAssets('airflow_task'),
      this.queryAssets('scripts_dir'),
    ])
  },
  methods: {
    queryAssets(asset) {
      assetsList({
        category: asset,
      }).then(res => {
        if (res.data.code == 0 && res.data.data && res.data.data.assets) {
          this.options[asset] = res.data.data.assets
        } else {
          this.options[asset] = []
        }
        this.catchLenth += this.options[asset].length
        if (this.temp.node == 0) {
          this.temp.content.residueNum += this.options[asset].length
        }
      })
    },

    loopTreeData(orgs) {
      const arr = []
      orgs.forEach(item => {
        const temp = { ...item }
        temp.label = item.name
        temp['title'] = item.name
        temp['value'] = item.oid
        // temp['key'] = item.oid
        temp.children = []
        delete temp.name
        delete temp.oid
        if (item.user_list && Array.isArray(item.user_list)) {
          for (let i = 0; i < item.user_list.length; i++) {
            temp.children = [
              ...temp.children,
              {
                label: item.user_list[i].name,
                title: item.user_list[i].name,
                value: JSON.stringify({
                  email: item.user_list[i].email,
                  uid: item.user_list[i].uid,
                  name: item.user_list[i].name,
                }),
                // : item.user_list[i].email,
                // value: item.user_list[i].email,
                // key: item.user_list[i].email,
              },
            ]
          }
        }
        arr.push(temp)
        if (item.child_org && Array.isArray(item.child_org)) {
          temp.children = [...temp.children, ...this.loopTreeData(item.child_org)]
        }
      })
      return arr
    },
    handleTreeData(list) {
      let arr = []
      for (let i = 0; i < list.length; i++) {
        arr.push({
          label: `${list[i].name}(${list[i].email})`,
          title: `${list[i].name}(${list[i].email})`,
          value: list[i].email,
          // key: list[i].email,
        })
      }
      return arr
    },
    treeChange(arg) {
      if (
        (!arg.length || String(arg[arg.length - 1]).includes('@') || typeof arg === 'string') &&
        typeof arg !== 'number'
      ) {
        this.temp.content.assetBelongsTo.mail = arg
        // this.$emit('selectChange', this.value)
      }
    },
    handOverChange(arg) {
      if (this.temp.content.handOverUser.length > 1) {
        this.temp.content.handOverUser = [this.temp.content.handOverUser[this.temp.content.handOverUser.length - 1]]
      }
      console.log(this.temp.content.handOverUser, 'this.temp.content.handOverUser')
    },
    recprdHandOverChange(arg, record) {
      // if (
      //   (!arg.length || String(arg[arg.length - 1]).includes('@') || typeof arg === 'string') &&
      //   typeof arg !== 'number'
      // ) {
      //   record.handOverUser = arg
      // }
      if (record.handOverUser.length > 1) {
        record.handOverUser = [record.handOverUser[record.handOverUser.length - 1]]
      }
    },

    // 添加填写内容到表格
    addList() {
      if (this.temp.node != 0) return
      if (!this.temp.content.assetDetails.length || !this.temp.content.handOverUser.length) {
        notification.warning({
          message: '关键信息未填写完整，请检查',
        })
      } else {
        console.log(this.temp.content.assetDetails, 'this.temp.content.assetDetails')
        if (this.temp.content.assetClass.value == 'scripts') {
          for (let i = 0; i < this.optList.length; i++) {
            this.optList[i].value == 'scripts_dir' ? (this.optList[i].disabled = true) : ''
          }
          this.temp.content.residueNum =
            this.temp.content.residueNum - this.options['scripts_dir'].length - this.temp.content.assetDetails.length
        } else if (this.temp.content.assetClass.value == 'scripts_dir') {
          for (let i = 0; i < this.optList.length; i++) {
            this.optList[i].value == 'scripts' ? (this.optList[i].disabled = true) : ''
          }
          this.temp.content.residueNum =
            this.temp.content.residueNum - this.options['scripts'].length - this.temp.content.assetDetails.length
        } else {
          this.temp.content.residueNum = this.temp.content.residueNum - this.temp.content.assetDetails.length
        }
        console.log(this.temp.content.residueNum, 'this.temp.content.residueNum')
        this.temp.content.tableData.push({
          assetClass: this.temp.content.assetClass,
          handoverType: this.temp.content.handoverType,
          assetDetails: this.temp.content.assetDetails,
          handOverUser: this.temp.content.handOverUser,
        })
        this.temp.content.assetDetails = []
      }
    },
    // 资产所属单选框
    assetBelongsChange(val) {
      if (val.target.checked) {
        this.temp.content.assetBelongsTo.user = val.target.value
        if (this.temp.content.assetBelongsTo.user == 'self') {
          this.temp.content.assetBelongsTo.mail = ['<EMAIL>']
        } else {
          this.temp.content.assetBelongsTo.mail = []
        }
      }
    },
    recordHandoverTypeChange(record, val) {
      record.handoverType = val
    },
    // 转交类型单选框
    handoverTypeChange(val) {
      if (val.target.checked) {
        this.temp.content.handoverType = val.target.value
      }
    },
    // 只有scriptis才可以复制和转移，其他情况只能转移
    assetClassChange(val) {
      this.temp.content.assetDetails = []
      this.temp.content.handOverUser = []
      if (val.value != 'scripts') {
        this.temp.content.handoverType = 'transfer'
      }
    },
    // 资产明细选中全部
    checkAllAssets() {
      console.log(this.temp.content.assetClass.value)
      if (this.temp.content.assetDetails.length == 0) {
        if (this.temp.content.assetClass.value) {
          this.options[this.temp.content.assetClass.value].forEach(item => {
            this.temp.content.assetDetails.push(item.value)
          })
        }
      } else {
        this.temp.content.assetDetails = []
      }
    },
    confirm(r) {
      console.log(this.temp.content.tableData, 'this.temp.content.tableData')
      if (r.assetClass.value == 'scripts') {
        let num = 0
        for (let i = 0; i < this.temp.content.tableData.length; i++) {
          if (this.temp.content.tableData[i].assetClass.value == 'scripts') {
            num++
          }
        }
        if (num > 1) {
          this.temp.content.residueNum = this.temp.content.residueNum + r.assetDetails.length
        } else {
          this.temp.content.residueNum =
            this.temp.content.residueNum + r.assetDetails.length + this.options['scripts_dir'].length
          for (let i = 0; i < this.optList.length; i++) {
            this.optList[i].value == 'scripts_dir' ? (this.optList[i].disabled = false) : ''
          }
        }
      } else if (r.assetClass.value == 'scripts_dir') {
        let num = 0
        for (let i = 0; i < this.temp.content.tableData.length; i++) {
          if (this.temp.content.tableData[i].assetClass.value == 'scripts_dir') {
            num++
          }
        }
        if (num > 1) {
          this.temp.content.residueNum = this.temp.content.residueNum + r.assetDetails.length
        } else {
          this.temp.content.residueNum =
            this.temp.content.residueNum + r.assetDetails.length + this.options['scripts'].length
          for (let i = 0; i < this.optList.length; i++) {
            this.optList[i].value == 'scripts' ? (this.optList[i].disabled = false) : ''
          }
        }
      } else {
        this.temp.content.residueNum = this.temp.content.residueNum + r.assetDetails.length
      }
      let index = this.temp.content.tableData.indexOf(r)
      this.temp.content.tableData.splice(index, 1)
      console.log(r, 'rrrr')
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.temp = response.Data
            this.temp.content.tableData = JSON.parse(this.temp.content.tableData)
            this.temp.content.assetDetails = JSON.parse(this.temp.content.assetDetails)
            this.temp.content.handOverUser = JSON.parse(this.temp.content.handOverUser)
            this.temp.content.assetBelongsTo = JSON.parse(this.temp.content.assetBelongsTo)
            this.temp.content.assetClass = JSON.parse(this.temp.content.assetClass)
            this.temp.content.residueNum = JSON.parse(this.temp.content.residueNum)
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    uniqueUserList(arr) {
      const assetSet = new Set() // 存储资产的集合
      const duplicateAssets = [] // 存储具有重复负责人的资产数组
      for (let i = 0; i < arr.length; i++) {
        const assetDetails = arr[i].assetDetails
        for (let j = 0; j < assetDetails.length; j++) {
          const asset = assetDetails[j]

          if (assetSet.has(asset)) {
            // 如果资产已存在于集合中，则将其添加到重复资产数组中
            duplicateAssets.push(asset)
          } else {
            // 如果资产是第一次出现，则将其添加到资产集合中
            assetSet.add(asset)
          }
        }
      }
      return duplicateAssets
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        console.log(this.temp.content.tableData, 'tableDatatableDatatableData')
        console.log(JSON.stringify(this.temp.content.tableData))

        const filteredArray = this.uniqueUserList(this.temp.content.tableData)
        console.log(filteredArray, 'filteredArrayfilteredArray')
        if (filteredArray.length > 0) {
          notification.error({
            message: '创建失败',
            description: '存在多个资产移交同一负责人',
          })
          return
        }
        if (!this.temp.content.tableData.length) {
          notification.error({
            message: '创建失败',
            description: '请添加需要转移的资产',
          })
          return
        }
        if (valid) {
          this.Date()
          this.temp.content.formatData = { items: [] }
          this.temp.content.leaveUser = {
            uid: '',
            cname: '',
            email: '',
            ename: '',
          }

          this.temp.content.tableData.forEach(item => {
            let userArr = []
            item.handOverUser.forEach(utem => {
              userArr.push({
                uid: JSON.parse(utem).uid,
                cn_name: JSON.parse(utem).name,
                email: JSON.parse(utem).email,
              })
            })
            let userAssets = []
            item.assetDetails.forEach(f => {
              userAssets.push({
                value: f,
              })
            })
            this.temp.content.formatData.items.push({
              category: item.assetClass.value,
              type: item.handoverType,
              acceptors: userArr,
              assets: userAssets,
            })
          })

          if (this.temp.content.assetBelongsTo.user == 'self') {
            this.temp.content.leaveUser = {
              uid: String(noc.user.getUserInfo().uid),
              cname: noc.user.getUserInfo().name,
              email: noc.user.getUserInfo().email,
              ename: noc.user.getUserInfo().email.split('@')[0],
            }
          } else {
            let users = JSON.parse(this.temp.content.assetBelongsTo.mail)
            this.temp.content.leaveUser = {
              uid: String(users.uid),
              cname: users.name,
              email: users.email,
              ename: users.email.split('@')[0],
            }
          }
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          let cloneContent = deepClone(this.temp)
          cloneContent.content.tableData = JSON.stringify(cloneContent.content.tableData)
          cloneContent.content.assetDetails = JSON.stringify(cloneContent.content.assetDetails)
          cloneContent.content.handOverUser = JSON.stringify(cloneContent.content.handOverUser)
          cloneContent.content.assetBelongsTo = JSON.stringify(cloneContent.content.assetBelongsTo)
          cloneContent.content.assetClass = JSON.stringify(cloneContent.content.assetClass)
          cloneContent.content.formatData = JSON.stringify(cloneContent.content.formatData)
          cloneContent.content.leaveUser = JSON.stringify(cloneContent.content.leaveUser)
          cloneContent.content.residueNum = JSON.stringify(cloneContent.content.residueNum)
          createOrder(cloneContent).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = response.Data
              this.temp.content.tableData = JSON.parse(this.temp.content.tableData)
              this.temp.content.assetDetails = JSON.parse(this.temp.content.assetDetails)
              this.temp.content.handOverUser = JSON.parse(this.temp.content.handOverUser)
              this.temp.content.assetBelongsTo = JSON.parse(this.temp.content.assetBelongsTo)
              this.temp.content.assetClass = JSON.parse(this.temp.content.assetClass)

              this.$router.push({ path: '/workflow/bigdata-asset-change', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData(isFinally) {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node += 1
          if (isFinally) {
            this.temp.status = 10
          } else {
            this.temp.status = 1
          }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          let cloneContent = deepClone(this.temp)
          cloneContent.content.tableData = JSON.stringify(cloneContent.content.tableData)
          cloneContent.content.assetDetails = JSON.stringify(cloneContent.content.assetDetails)
          cloneContent.content.handOverUser = JSON.stringify(cloneContent.content.handOverUser)
          cloneContent.content.assetBelongsTo = JSON.stringify(cloneContent.content.assetBelongsTo)
          cloneContent.content.assetClass = JSON.stringify(cloneContent.content.assetClass)
          cloneContent.content.residueNum = JSON.stringify(cloneContent.content.residueNum)
          approveOrder(cloneContent).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              this.temp.content.tableData = JSON.parse(this.temp.content.tableData)
              this.temp.content.assetDetails = JSON.parse(this.temp.content.assetDetails)
              this.temp.content.handOverUser = JSON.parse(this.temp.content.handOverUser)
              this.temp.content.assetBelongsTo = JSON.parse(this.temp.content.assetBelongsTo)
              this.temp.content.assetClass = JSON.parse(this.temp.content.assetClass)
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    //
    // approveData() {
    //   antdFormValidate(this.$refs.orderForm, valid => {
    //     if (valid) {
    //       this.node_status = 0
    //       this.temp.node = 3
    //       this.temp.status = 10
    //       this.temp.handler = store.getters.name
    //       this.temp.handlerEmail = store.getters.email
    //       approveOrder(this.temp).then(response => {
    //         if (response === undefined) {
    //           notification.error({
    //             message: '审批失败',
    //             description: '后端接口错误，请联系运维开发排查~',
    //           })
    //         } else if (response.Code !== 200) {
    //           notification.error({
    //             message: '审批执行报错',
    //             description: response.Code,
    //           })
    //         } else {
    //           this.node_status = 1
    //           this.temp = response.Data
    //           notification.success({
    //             message: '审批成功',
    //             description: '管理员审批成功',
    //           })
    //         }
    //       })
    //     }
    //   })
    // },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
