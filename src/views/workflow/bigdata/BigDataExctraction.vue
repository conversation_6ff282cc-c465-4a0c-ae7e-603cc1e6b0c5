<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="数据所属部门负责人审批" />
        <a-step title="大数据负责人审批" />
        <a-step title="数据安全负责人审批" />
        <a-step title="抽取负责人" />
        <a-step title="DBA审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" class="exctraCard" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        labelAlign="right"
        ref="orderForm"
        layout="horizontal"
        :model="temp.content.data"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 22 }"
      >
        <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="处理人">{{ temp.handler }}</a-form-model-item>
        <a-form-model-item label="状态">
          <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
          <a-tag v-else-if="temp.status === 2" color="orange">待Web端处理</a-tag>
          <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
          <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
          <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
          <a-tag v-else>未知</a-tag>
        </a-form-model-item>

        <a-form-model-item label="详情">
          <tx-button size="small" type="primary" icon="plus" @click="addTableData" />
          <tx-button size="small" type="primary" icon="copy" @click="copyTableData" />
          <tx-button size="small" type="primary" icon="delete" @click="deleteTableData" />
          <a-table
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :data-source="temp.content.data.tableData"
            :columns="dataColumns"
            :scroll="{ x: 1800 }"
            border
            stripe
            style="width: 100%"
          >
            <template #bodyCell="{ column: { dataIndex: item }, index, record, text }">
              <a-form-model-item
                v-if="
                  item !== 'service_products' &&
                  item !== 'db_field_type' &&
                  item !== 'db_type' &&
                  item !== 'extraction_type' &&
                  item !== 'extraction_cycle' &&
                  item !== 'security_level'
                "
                style="margin-bottom: 0px"
                :label-col="{ span: 0 }"
                :wrapper-col="{ span: 24 }"
                :name="index + '4'"
                :rules="
                  (item === 'db_field' && record.db_field_type === '全字段') ||
                  (item === 'primary_key' && !primaryKeyRules.includes(record.extraction_type)) ||
                  (item === 'incrementalField' && !incrementalFieldRules.includes(record.extraction_type))
                    ? {}
                    : {
                        required: true,
                        message: '请输入' + item,
                        trigger: 'blur',
                        validator: (rule, value, callback) => {
                          return numCheck(rule, value, callback, record, item)
                        },
                      }
                "
              >
                <a-input
                  style="width: 140px"
                  :disabled="
                    Boolean(item === 'db_field' && record.db_field_type === '全字段') ||
                    Boolean(item === 'primary_key' && !primaryKeyRules.includes(record.extraction_type)) ||
                    Boolean(item === 'incrementalField' && !incrementalFieldRules.includes(record.extraction_type))
                  "
                  :value="text"
                  @change="e => inputChange(e.target.value, record.key, item, record)"
                />
              </a-form-model-item>
              <a-form-model-item
                v-else
                style="margin-bottom: 0px"
                :key="index"
                :label-col="{ span: 0 }"
                :wrapper-col="{ span: 24 }"
                :name="index + '4'"
                :rules="{
                  required: true,
                  message: '请输入' + item,
                  trigger: 'change',
                  validator: (rule, value, callback) => {
                    return numCheck(rule, value, callback, record, item)
                  },
                }"
              >
                <a-select
                  v-if="item === 'db_field_type'"
                  v-model:value="record.db_field_type"
                  default-value
                  style="width: 100px"
                  @change="handleChange(...arguments, record.key, item, index, record)"
                >
                  <a-select-option v-for="utem in dbFieldTypeOptions" :value="utem.value" :key="utem.value">
                    <a-tooltip>
                      <template #title>{{ utem.value }}</template>
                      {{ utem.value }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-else-if="item === 'db_type'"
                  v-model:value="record.db_type"
                  default-value
                  style="width: 100px"
                  @change="handleChange(...arguments, record.key, item, index, record)"
                >
                  <a-select-option v-for="utem in dbTypeOptions" :value="utem.value" :key="utem.value">
                    <a-tooltip>
                      <template #title>{{ utem.value }}</template>
                      {{ utem.value }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-else-if="item === 'extraction_cycle'"
                  v-model:value="record.extraction_cycle"
                  default-value
                  style="width: 100px"
                  @change="handleChange(...arguments, record.key, item, index, record)"
                >
                  <a-select-option v-for="utem in extractionCycleOptions" :value="utem.value" :key="utem.value">
                    <a-tooltip>
                      <template #title>{{ utem.value }}</template>
                      {{ utem.value }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-else-if="item === 'service_products'"
                  v-model:value="record.service_products"
                  default-value=""
                  style="width: 100px"
                  @change="handleChange(...arguments, record.key, item, index, record)"
                >
                  <a-select-option v-for="utem in serviceProductsOptions" :value="utem" :key="utem">
                    <a-tooltip>
                      <template #title>{{ utem }}</template>
                      {{ utem }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-else-if="item === 'extraction_type'"
                  v-model:value="record.extraction_type"
                  default-value
                  style="width: 100px"
                  @change="handleChange(...arguments, record.key, item, index, record)"
                >
                  <a-select-option v-for="utem in extractionTypeOptions" :value="utem.value" :key="utem.value">
                    <a-tooltip>
                      <template #title>{{ utem.value }}</template>
                      {{ utem.value }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-else-if="item === 'security_level'"
                  v-model:value="record.security_level"
                  :disabled="temp.node < 2"
                  default-value
                  style="width: 100px"
                  @change="handleChange(...arguments, record.key, item, index, record)"
                >
                  <a-select-option v-for="utem in securityLeavelArr" :value="utem.level" :key="utem.key">
                    <a-tooltip>
                      <template #title>{{ utem.key }}</template>
                      {{ utem.key }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </template>
          </a-table>
        </a-form-model-item>

        <a-form-model-item label="数据所属部门负责人" name="dataDepartmentPrincipal">
          <!-- <AllUser
            :checkValue="temp.content.data.dataDepartmentPrincipal"
            dropdownHeight="500px"
            :getPopupContainer="popupParent"
            :orgSelectable="false"
            placeholder="负责人邮箱"
            @selectChange="orgChange"
          /> -->
          <a-select
            v-model:value="temp.content.data.dataDepartmentPrincipal"
            style="width: calc(100%)"
            show-search
            :placeholder="'请选择用户'"
          >
            <a-select-option v-for="i in treeData" :value="i.email">
              {{ i.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item name="data.reason" label="申请理由">
          <a-textarea v-model:value="temp.content.data.reason" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" name="comment" label="回复/备注">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">{{ activity }}</a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" :disabled="createDisabled" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 7 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="dataDepartmentPrincipalApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="bigdataApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="securityApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 5 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 6 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="dbApprove">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <!-- 撑高页面，方便显示 select 下拉框 -->
    <div style="height: 50vh"></div>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { popupParent } from '@aim/helper'
import { approveOrder, createOrder, getOrderInfo, getSecurity_level, getServedProducts } from '@/api/workflow/order'
import AllUser from '@/views/comp/allOrgUserOne/index.vue'
import cloneDeep from 'lodash.clonedeep'
import { originDataColumn } from './originData'
import { reject } from 'lodash'
const slotArr = [
  'db_ip',
  'db_name',
  'table_name',
  'table_name',
  'db_field_type',
  'db_field',
  'db_type',
  'extraction_cycle',
  // 'db_num',
  'extraction_type',
  'service_products',
  'primary_key',
  'incrementalField',
  'security_level',
]
const dataColumns = [
  {
    title: '数据库IP',
    dataIndex: 'db_ip',
    key: 'db_ip',
    // slots: { title: 'customTitle' },
    tips: '111111',
    scopedSlots: { customRender: 'db_ip' },
  },
  {
    title: '数据库名',
    dataIndex: 'db_name',
    key: 'db_name',
    scopedSlots: { customRender: 'db_name' },
  },
  {
    title: '表名',
    dataIndex: 'table_name',
    key: 'table_name',
    scopedSlots: { customRender: 'table_name' },
  },
  {
    title: '数据字段类型',
    dataIndex: 'db_field_type',
    key: 'db_field_type',
    scopedSlots: { customRender: 'db_field_type' },
  },
  {
    title: '数据字段名称',
    dataIndex: 'db_field',
    key: 'db_field',
    scopedSlots: { customRender: 'db_field' },
  },
  {
    title: '数据类型',
    dataIndex: 'db_type',
    key: 'db_type',
    scopedSlots: { customRender: 'db_type' },
  },
  {
    title: '抽取周期/次数',
    dataIndex: 'extraction_cycle',
    key: 'extraction_cycle',
    scopedSlots: { customRender: 'extraction_cycle' },
  },
  // {
  //   title: '数量',
  //   dataIndex: 'db_num',
  //   key: 'db_num',
  //   scopedSlots: { customRender: 'db_num' }
  // },
  {
    title: '服务产品',
    dataIndex: 'service_products',
    key: 'service_products',
    scopedSlots: { customRender: 'service_products' },
  },
  {
    title: '抽取类型',
    dataIndex: 'extraction_type',
    key: 'extraction_type',
    scopedSlots: { customRender: 'extraction_type' },
  },
  {
    title: '主键',
    dataIndex: 'primary_key',
    key: 'primary_key',
    scopedSlots: { customRender: 'primary_key' },
  },
  {
    title: '增量字段',
    dataIndex: 'incrementalField',
    key: 'incrementalField',
    scopedSlots: { customRender: 'incrementalField' },
  },
]

const statusOptions = [
  { key: 1, display_name: '待审核' },
  { key: 10, display_name: '已完成' },
  { key: 20, display_name: '被拒绝' },
]
const incrementalFieldRules = ['mysql增量', 'mysql日志', 'mysql增量拉链表', 'mongo增量']
const primaryKeyRules = ['mysql增量', 'mysql全量拉链表', 'mysql增量拉链表']
const securityLeavelArr = [
  { key: '公开', level: '0' },
  { key: '内部公开', level: '1' },
  { key: '机密', level: '2' },
  { key: '绝密', level: '3' },
]
const dbFieldTypeOptions = [{ value: '全字段' }, { value: '自定义字段' }]

const dbTypeOptions = [
  { value: '个人信息' },
  { value: '企业数据' },
  { value: '运营数据' },
  { value: '通讯录数据' },
  { value: '公共通用数据' },
  { value: '名片数据' },
  { value: '报表数据' },
  { value: '交易数据' },
  { value: '行为数据' },
  { value: '系统数据' },
]

const extractionCycleOptions = [
  { value: '仅一次' },
  { value: '每月' },
  { value: '每周' },
  { value: '每日' },
  { value: '每小时' },
  { value: '指定时间（请联系操作人）' },
]

const extractionTypeOptions = [
  { value: 'mysql全量' },
  { value: 'mysql增量' },
  { value: 'mysql日志' },
  { value: 'mysql增量拉链表' },
  { value: 'mysql全量拉链表' },
  { value: 'mongo全量' },
  { value: 'mongo增量' },
  { value: 'odps全量表' },
  { value: 'odps分区表' },
  { value: 'ots全量表' },
]

const tableSecurityLevelOptions = [{ value: '公开' }, { value: '内部公开' }, { value: '机密' }, { value: '绝密' }]
import { getUserEmailList } from '@/api/permission/user'
export default {
  name: 'ComplexTable',
  components: {
    AllUser,
  },
  data() {
    return {
      createDisabled: false,
      treeData: [],
      checkedColumn: [],
      slotArr,
      dataColumns: [],
      selectedRowKeys: [],
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      createButton: false,
      cNameLoading: false,
      extraction_type_rule: ['mysql增量', 'mysql增量拉链表'],
      listQuery: {
        page: 1,
        limit: 10,
        handler: store.getters.name,
        orderType: undefined,
        status: undefined,
      },
      local_name: store.getters.name,
      localUser: store.getters.email,
      primaryKeyRules,
      incrementalFieldRules,
      statusOptions,
      dbFieldTypeOptions,
      dbTypeOptions,
      extractionCycleOptions,
      extractionTypeOptions,
      serviceProductsOptions: [],
      tableSecurityLevelOptions,
      securityLeavelArr,
      userOptions: [],
      rules: {
        dataDepartmentPrincipal: [
          {
            required: true,
            triggers: blur,
            message: '请填写数据所属事业部负责人邮箱',
          },
        ],
        reason: [
          {
            required: true,
            triggers: blur,
            message: '请填写申请理由',
          },
        ],
      },
      node_status: 1,
      temp: {
        id: undefined,
        orderType: '大数据抽取申请',
        founder: store.getters.name,
        founderEmail: store.getters.email,
        timeNow: '',
        handler: '',
        timeline: [],
        comment: '',
        node: 0,
        status: 0,
        content: {
          data: {
            tableData: [
              {
                key: '',
                db_ip: '',
                primary_key: '',
                incrementalField: '',
                db_name: '',
                table_name: '',
                db_field_type: '',
                db_field: '',
                db_type: '',
                extraction_cycle: '',
                // db_num: '',
                extraction_type: '',
                service_products: '',
                security_level: '',
              },
            ],
            dataDepartmentPrincipal: '',
            founderDepartment: '运维',
            reason: '',
          },
        },
      },
      multipleSelection: [],
      tableDataLength: 0,
    }
  },
  created() {
    this.getList()
    getServedProducts().then(res => {
      this.serviceProductsOptions = res.data.payload
    })
  },
  mounted() {
    getUserEmailList().then(res => {
      console.log(res, 'res')
      this.treeData = res.Data.items
    })
  },
  methods: {
    orgChange(orgs) {
      this.temp.content.data.dataDepartmentPrincipal = orgs
    },
    popupParent,
    numCheck(rule, value, callback, record, key) {
      return new Promise((resolve, reject) => {
        if (!record[key]) {
          notification.open({
            message: '校验失败',
            description: `请滚动表格，检查内容是否填写完整。${rule.message}`,
            style: {
              border: '3px solid red',
              color: 'red',
              'border-radius': '6px',
            },
            class: 'notification-custom-class',
          })

          reject(new Error(rule.message))
        } else {
          // notification.warning({
          //   message: '校验失败',
          //   description: '请检查是否填写安全等级等必填项。',
          // })
          resolve()
        }
      })
    },
    /*
    numCheck(rule, value, callback, record, key) {
      if (!record[key]) {
        callback(new Error(rule.message))
      } else {
        callback()
      }
    },
    */
    handleChange() {
      console.log(arguments, '---')
      const value = arguments[0]
      const column = arguments[3]
      const key = arguments[2]
      const columnData = arguments[5]
      if (column === 'db_field_type' && value === '全字段') {
        columnData.db_field = ''
      } else if (column === 'extraction_type') {
        columnData.primary_key = ''
        columnData.incrementalField = ''
      }
      // console.log(value, 'value')
      // console.log(column, 'column')
      // console.log(key, 'key')
      // console.log(this.temp, 'temp')
      const newData = [...this.temp.content.data.tableData]
      const target = newData.find(item => key === item.key)
      if (target) {
        target[column] = value
        this.temp.content.data.tableData = newData
      }
    },
    onSelectChange(selectedRowKeys, column) {
      this.checkedColumn = column
      this.selectedRowKeys = selectedRowKeys
    },
    inputChange(value, key, column, columnData) {
      console.log(value, key, column, columnData)
      const newData = [...this.temp.content.data.tableData]
      const target = newData.find(item => key === item.key)
      if (target) {
        target[column] = value
        this.temp.content.data.tableData = newData
      }
    },
    getList() {
      this.dataColumns = cloneDeep(originDataColumn)
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content.data = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.node > 1) {
              this.dataColumns.push({
                title: '安全等级',
                dataIndex: 'security_level',
                key: 'security_level',
                scopedSlots: { customRender: 'security_level' },
                fixed: 'right',
              })
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    deleteTableData() {
      this.checkedColumn.forEach(item => {
        const index = this.temp.content.data.tableData.indexOf(item)
        this.temp.content.data.tableData.splice(index, 1)
      })
    },
    handleCancel() {
      this.$emit('deleteComp', this.tableInfoIndex)
    },
    tableSortChange(column) {
      this.listQuery.page = 1
      if (column.order === 'descending') {
        this.listQuery.sortby = column.prop
        this.listQuery.order = 'desc'
      } else {
        this.listQuery.sortby = column.prop
        this.listQuery.order = 'asc'
      }
      this.getList()
    },
    createData() {
      this.createDisabled = true
      antdFormValidate(this.$refs.orderForm, valid => {
        console.log(valid, 'valid')
        if (valid) {
          this.Date()
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.temp.timeNow]
          // 获取安全等级
          const tables = this.temp.content.data.tableData.map(item => {
            return { schema: item.db_name, table: item.table_name }
          })
          getSecurity_level({ tables }).then(securityData => {
            let myTemp = null
            this.temp.content.data.tableData.forEach(tableItem => {
              securityData.data.forEach(securityItem => {
                if (tableItem.db_name === securityItem.schema && tableItem.table_name === securityItem.table) {
                  tableItem.security_level = securityItem.security_level
                }
              })
              myTemp = cloneDeep(this.temp)
              myTemp.content.data = JSON.stringify(myTemp.content.data)
            })
            createOrder(myTemp)
              .then(response => {
                if (response === undefined) {
                  notification.error({
                    message: '创建失败',
                    description: '后端接口错误，请联系运维开发排查~',
                  })
                } else {
                  if (response.Code === 400) {
                    const msg = response.data.msg
                    notification.error({
                      message: '创建失败',
                      description: msg,
                    })
                  } else if (response.Code === 200) {
                    notification.success({
                      message: '创建成功',
                      description: '工单创建成功',
                    })
                    this.node_status = 1
                    response.Data.content = JSON.parse(response.Data.content.data)
                    this.temp = response.Data
                    this.$router.push({ path: '/workflow/bigdata-exctraction', query: { id: response.Data.id } })
                  }
                }
              })
              .finally(() => {
                this.createDisabled = false
              })
          })
        } else {
          console.log(111111111111)
        }
      })
        .then(res => {
          console.log(res, 'res')
        })
        .catch(err => {
          console.log(err, 'err')
        })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 2
          this.temp.status = 2
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    dataDepartmentPrincipalApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        console.log(valid, 'validvalid')
        if (valid) {
          this.Date()
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '数据所属部门负责人审批成功',
              })
            }
          })
        } else {
          notification.warning({
            message: '校验失败',
            description: '请检查是否填写安全等级等必填项。',
          })
        }
      })
    },
    bigdataApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 4
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '大数据负责人审批成功',
              })
            }
          })
        }
      })
    },
    securityApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '数据安全负责人审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 6
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '抽取负责人审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 7
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    dbApprove() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 7
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: 'DBA审批成功',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 7
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          // this.temp.content = { data: JSON.stringify(this.temp.content) }
          const myTemp = cloneDeep(this.temp)
          myTemp.content.data = JSON.stringify(myTemp.content.data)
          approveOrder(myTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.temp.timeNow =
        date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    addTableData() {
      this.temp.content.data.tableData.forEach(item => {
        if (item.key > this.tableDataLength) {
          this.tableDataLength = item.key
        }
      })
      this.temp.content.data.tableData.push({
        key: (this.tableDataLength += 1),
        db_ip: '',
        incrementalField: '',
        primary_key: '',
        db_name: '',
        table_name: '',
        db_field_type: '',
        db_field: '',
        db_type: '',
        extraction_cycle: '',
        // db_num: '',
        extraction_type: '',
        service_products: '',
        security_level: '',
      })
    },
    copyTableData() {
      const copyData = cloneDeep(this.checkedColumn)
      copyData.forEach(item => {
        item.key = Math.random()
        this.temp.content.data.tableData.push(item)
      })
    },
    renderHeader(h, { column }) {
      return h('div', {
        attrs: {
          class: 'cell',
        },
        domProps: {
          innerHTML: '<span style="color:red">*&nbsp;</span>' + column.label,
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
.ant-form-item {
  /* -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 20px;
  vertical-align: top; */
}

/deep/.exctraCard {
  .ant-card-body {
    padding-left: 0 !important;
  }
}
// .notification-custom-class {
//   color: rgb(245, 168, 168) !important;
// }
</style>

<style scoped></style>
