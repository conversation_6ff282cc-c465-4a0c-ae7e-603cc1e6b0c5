<!--
 * @Version:
 * @Author: Yanghd
 * @Date: 2022-04-20 15:10:24
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-06-06 17:40:26
 * @Descripttion: 查询和导出
-->
<template>
  <page-header-wrapper>
    <a-card class="box-card" style="margin-bottom: 20px">
      <!-- v-if="security_level === 3" -->
      <a-steps :current="Number(handlerActive)" style="margin-top: 20px">
        <a-step title="提交工单" />
        <a-step title="表负责人" />
        <a-step title="上级领导" />
        <a-step title="大数据负责人" />
        <a-step title="数据安全负责人" />
        <a-step title="数据所属事业部负责人" />
        <a-step title="IT负责人" />
        <a-step title="DLP负责人" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card v-if="Number(temp.node) === 0" class="box-card">
      <template #header>
        <span class="clearfix">{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :rules="rules" :model="temp.content">
        <a-form-model-item label="申请人">
          <a-tag>{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="content.reason">
          <a-textarea v-model:value="temp.content.reason" />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item>
          <a-row :gutter="24">
            <a-col :span="24" style="text-align: center">
              <!-- <tx-button type="primary" @click="createData" :disabled="createButton">提交</tx-button>
              <tx-button @click="handleCancel()">取消</tx-button> -->
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-card v-if="Number(temp.node) === 1" class="box-card">
      <template #header>
        <span class="clearfix">{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :model="temp.content" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">
          <a-tag type="info">{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="申请状态">
          <a-tag v-if="Number(temp.status) == 0" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 1" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 2" type="warning">处理中</a-tag>
          <a-tag v-else-if="Number(temp.status) == 10" type="success">审批通过</a-tag>
          <a-tag v-else-if="Number(temp.status) == 20" type="danger">审批拒绝</a-tag>
          <a-tag v-else type="info">未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" disabled />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item
          v-if="temp.handler.includes(local_name) && Number(temp.status) !== 10 && Number(temp.status) !== 20"
        >
          <a-row :gutter="24">
            <a-col :span="24" style="text-align: center">
              <tx-button type="primary" @click="bigdataTablePrincipalApproveData">同意</tx-button>
              <tx-button type="danger" style="margin-left: 16px" @click="rejectData()">拒绝</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-card v-if="Number(temp.node) === 2" class="box-card">
      <template #header>
        <span class="clearfix">{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :model="temp.content" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">
          <a-tag type="info">{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="申请状态">
          <a-tag v-if="Number(temp.status) == 0" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 1" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 2" type="warning">处理中</a-tag>
          <a-tag v-else-if="Number(temp.status) == 10" type="success">审批通过</a-tag>
          <a-tag v-else-if="Number(temp.status) == 20" type="danger">审批拒绝</a-tag>
          <a-tag v-else type="info">未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" disabled />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item
          v-if="temp.handler.includes(local_name) && Number(temp.status) !== 10 && Number(temp.status) !== 20"
        >
          <a-row :gutter="24">
            <a-col :span="24" style="text-align: center">
              <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
              <tx-button type="danger" style="margin-left: 16px" @click="rejectData()">拒绝</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-card v-if="Number(temp.node) === 3" class="box-card">
      <template #header>
        <span class="clearfix">{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :model="temp.content" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">
          <a-tag type="info">{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="申请状态">
          <a-tag v-if="Number(temp.status) == 0" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 1" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 2" type="warning">处理中</a-tag>
          <a-tag v-else-if="Number(temp.status) == 10" type="success">审批通过</a-tag>
          <a-tag v-else-if="Number(temp.status) == 20" type="danger">审批拒绝</a-tag>
          <a-tag v-else type="info">未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" disabled />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item
          v-if="temp.handler.includes(local_name) && Number(temp.status) !== 10 && Number(temp.status) !== 20"
        >
          <a-row :gutter="24">
            <a-col :span="24" style="text-align: center">
              <tx-button type="primary" @click="bigdataApproveData">同意</tx-button>
              <tx-button type="danger" style="margin-left: 16px" @click="rejectData()">拒绝</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-card v-if="Number(temp.node) === 4" class="box-card">
      <template #header>
        <span class="clearfix">{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :model="temp.content" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">
          <a-tag type="info">{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="申请状态">
          <a-tag v-if="Number(temp.status) == 0" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 1" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 2" type="warning">处理中</a-tag>
          <a-tag v-else-if="Number(temp.status) == 10" type="success">审批通过</a-tag>
          <a-tag v-else-if="Number(temp.status) == 20" type="danger">审批拒绝</a-tag>
          <a-tag v-else type="info">未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" disabled />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item
          v-if="temp.handler.includes(local_name) && Number(temp.status) !== 10 && Number(temp.status) !== 20"
        >
          <a-row :gutter="24">
            <a-col :span="24" style="text-align: center">
              <tx-button type="primary" @click="securityApproveData()">同意</tx-button>
              <tx-button style="margin-left: 16px" type="danger" @click="rejectData()">拒绝</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-card v-if="Number(temp.node) > 4 && Number(temp.node) < 8" class="box-card">
      <template #header>
        <span>class="clearfix"{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :model="temp.content" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">
          <a-tag type="info">{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="申请状态">
          <a-tag v-if="Number(temp.status) == 0" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 1" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 2" type="warning">处理中</a-tag>
          <a-tag v-else-if="Number(temp.status) == 10" type="success">审批通过</a-tag>
          <a-tag v-else-if="Number(temp.status) == 20" type="danger">审批拒绝</a-tag>
          <a-tag v-else type="info">未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" disabled />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
        <a-form-model-item
          v-if="temp.handler.includes(local_name) && Number(temp.status) !== 10 && Number(temp.status) !== 20"
        >
          <a-row :gutter="24">
            <a-col
              v-if="Number(temp.node) == 5 && temp.handlerEmail.includes(currentUser)"
              :span="24"
              style="text-align: center"
            >
              <tx-button type="primary" @click="approveData()">同意</tx-button>
              <tx-button style="margin-left: 16px" type="danger" @click="rejectData()">拒绝</tx-button>
            </a-col>
            <a-col
              v-if="Number(temp.node) == 6 && temp.handlerEmail.includes(currentUser)"
              :span="24"
              style="text-align: center"
            >
              <tx-button type="primary" @click="itApprove()">同意</tx-button>
              <tx-button style="margin-left: 16px" type="danger" @click="rejectData()">拒绝</tx-button>
            </a-col>
            <a-col
              v-if="Number(temp.node) == 7 && temp.handlerEmail.includes(currentUser)"
              :span="24"
              style="text-align: center"
            >
              <tx-button type="primary" @click="dlpApprove()">同意</tx-button>
              <tx-button style="margin-left: 16px" type="danger" @click="rejectData()">拒绝</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-card v-if="Number(temp.node) === 8" class="box-card">
      <template #header>
        <span class="clearfix">{{ this.temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form-model ref="temp" :model="temp.content" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="申请人">
          <a-tag type="info">{{ temp.founder }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-model-item>
        <a-form-model-item label="申请状态">
          <a-tag v-if="Number(temp.status) == 0" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 1" type="warning">待审核</a-tag>
          <a-tag v-else-if="Number(temp.status) == 2" type="warning">处理中</a-tag>
          <a-tag v-else-if="Number(temp.status) == 10" type="success">审批通过</a-tag>
          <a-tag v-else-if="Number(temp.status) == 20" type="danger">审批拒绝</a-tag>
          <a-tag v-else type="info">未知</a-tag>
        </a-form-model-item>
        <a-form-model-item label="详情">
          <a-table :columns="columns" :data-source="temp.content.tableData">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key == 'action'">
                <tx-button type="danger" @click="handleDelete(record)">删除</tx-button>
              </template>
            </template>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="申请理由" name="reason">
          <a-textarea v-model:value="temp.content.reason" disabled />
        </a-form-model-item>
        <a-form-model-item v-if="temp.node > 0" label="回复/备注" name="comment">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-form-model-item label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { getOrderInfo } from '@/api/workflow/order'
import { notification } from 'ant-design-vue'
import { workflowOrderApprove, workflowOrderCreate, workflowOrderId } from '@/api/bigdata/checkExport'
import store from '@/store' // secondary package based on el-pagination
import cloneDeep from 'lodash.clonedeep'

const statusOptions = [
  { key: 1, display_name: '待审核' },
  { key: 10, display_name: '已完成' },
  { key: 20, display_name: '被拒绝' },
]
const columns = [
  {
    title: '表名',
    dataIndex: 'table_name',
    key: 'table_name',
    width: 200,
  },
  {
    title: '表描述',
    dataIndex: 'comment',
    key: 'comment',
    width: 150,
  },
  {
    title: '表负责人',
    dataIndex: 'owner',
    key: 'owner',
    width: 100,
  },
  {
    title: '表产品线',
    dataIndex: 'product_tag',
    key: 'product_tag',
    width: 100,
  },
  {
    title: '安全等级',
    dataIndex: 'security_level',
    key: 'security_level',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: 100,
  },
]
export default {
  name: 'ComplexTable',
  props: {
    tableInfos: {
      type: Array,
      default: () => {
        return []
      },
    },
    backupInfo: {
      type: Array,
      default: () => {
        return []
      },
    },
    tableInfoIndex: {
      type: Number,
      default: () => {
        return 0
      },
    },
    types: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    const currentUser = store.getters.email
    return {
      columns,
      currentUser,
      handlerActive: 0,
      tableKey: 0,
      list: null,
      total: 0,
      tempBackupInfo: this.backupInfo,
      listLoading: true,
      createButton: false,
      listQuery: {
        page: 1,
        limit: 10,
        handler: store.getters.name,
        orderType: undefined,
        status: undefined,
      },
      local_name: store.getters.name,
      statusOptions,
      table_nameOptions: [],
      rules: {
        table_name: [{ required: true, message: '请输入表名', trigger: 'change' }],
        'content.reason': [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      temp: {
        id: undefined,
        orderType: '大数据表查询权限',
        founder: store.getters.name,
        founder_email: store.getters.email,
        time_now: '',
        handler: '',
        timeline: [],
        comment: '',
        node: 0,
        status: 0,
        content: {
          tableData: this.tableInfos,
          reason: '',
        },
      },
      security_level: 1,
    }
  },
  watch: {
    tableInfos: {
      handler: function (n) {
        const that = this
        if (n && n.length) {
          switch (n[0].security_level) {
            case '公开':
              that.security_level = 0
              break
            case '内部公开':
              that.security_level = 1
              break
            case '机密':
              that.security_level = 2
              break
            case '绝密':
              that.security_level = 3
              break
            default:
              that.security_level = 3
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.getList()
  },
  mounted() {
    this.getOrderTypeInfo()
  },
  methods: {
    stringifyData() {
      const cloneTemp = cloneDeep(this.temp)
      for (const key in cloneTemp.content) {
        if (cloneTemp.content[key]) {
          cloneTemp.content[key] = JSON.stringify(cloneTemp.content[key])
        }
      }
      return cloneTemp
    },
    getList() {
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id)
          .then(res => {
            if (res.Data.orderType === this.temp.orderType) {
              this.temp = res.Data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
              switch (this.temp.content.tableData[0].security_level) {
                case '公开':
                  this.security_level = 0
                  break
                case '内部公开':
                  this.security_level = 1
                  break
                case '机密':
                  this.security_level = 2
                  break
                case '绝密':
                  this.security_level = 3
                  break
                default:
                  this.security_level = 3
              }
              if (Number(res.Data.status) === 10 || Number(res.Data.status) === 20) {
                this.temp.node = 8
                this.temp.timeline.push('工单结束')
              }
              this.activeNodes()
            } else {
              this.$router.push({ path: '/404' })
            }
          })
          .catch(() => {})
      }
    },
    getOrderTypeInfo() {
      this.createButton = typeof this.tableInfos !== 'object'
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleDelete(row) {
      this.temp.content.tableData = this.temp.content.tableData.filter(res => {
        return res.guid !== row.guid
      })
      this.tempBackupInfo = this.tempBackupInfo.filter(res => {
        return res.guid !== row.guid
      })
      this.$emit('backupInfoUpdate', this.tempBackupInfo)
    },
    handleCancel() {
      this.$emit('deleteComp', this.tableInfoIndex)
      // this.$router.push({
      //   name: 'search',
      //   params: { backupInfo: this.tempBackupInfo }
      // })
    },
    tableSortChange(column) {
      this.listQuery.page = 1
      if (column.order === 'descending') {
        this.listQuery.sortby = column.prop
        this.listQuery.order = 'desc'
      } else {
        this.listQuery.sortby = column.prop
        this.listQuery.order = 'asc'
      }
      this.getList()
    },
    activeNodes() {
      this.handlerActive = this.temp.node

      // if (this.temp.status === 10 || this.temp.status === 20) {
      //   this.handlerActive = 6
      // } else {
      //   this.handlerActive = this.temp.node
      // }
    },
    createData() {
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          this.temp.node = 2
          this.temp.status = 1
          this.temp.timeline = [{ content: this.temp.founder + ' 创建工单', timestamp: this.temp.time_now }]
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderCreate(cloneTemp)
            .then(res => {
              if (res === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else {
                this.temp.handler = res.handler
                notification.success({
                  message: 'Success',
                  description: 'Approved Successfully',
                })
              }
            })
            .catch(() => {})
        }
      })
    },
    // tablepri同意
    bigdataTablePrincipalApproveData() {
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          this.temp.node = 2
          if (this.security_level === 1) {
            this.temp.status = 10
          } else {
            this.temp.status = 1
          }
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.temp.handler = response.data.handler
              notification.success({
                message: 'Success',
                description: 'Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    // tl同意
    leaderApproveData() {
      console.log('llllleaderapproval')
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          this.temp.node = 3
          this.temp.status = 1
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.temp.handler = response.data.handler
              notification.success({
                message: 'Success',
                description: 'Leader Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    // 大数据审批人
    bigdataApproveData() {
      console.log('大数据负责人')
      console.log(this.security_level, 'this.security_levelthis.security_level')
      console.log(this.temp, 'this.tempthis.tempthis.temp')
      // this.temp.content.userInfo.check_dlp = 0
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          if (this.security_level === 0 || this.security_level === 1) {
            // 内部公开、公开  直接结束流程
            this.temp.status = 10
            this.temp.node = 8
          } else if (this.security_level === 2) {
            // 机密
            if (this.temp.content.userInfo.level >= 1 && Number(this.temp.content.userInfo.check_dlp) === 0) {
              // level >=1 人员风险等级不为0，表示有风险
              // check_dlp===0 未安装dlp 需要走dlp验证
              this.temp.node = 6
              this.temp.status = 1
            } else {
              // 安全等级小于1 或 dlp！==0 无需dlp验证，直接结束
              this.temp.node = 8
              this.temp.status = 10
            }
          } else {
            // 绝密
            this.temp.status = 1
            this.temp.node = 4
          }
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.temp.handler = response.data.handler
              notification.success({
                message: 'Success',
                description: 'Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    // 安全负责人同意
    securityApproveData() {
      console.log('安全负责人')
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 1
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.temp.handler = response.data.handler
              notification.success({
                message: 'Success',
                description: 'Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    // business
    approveData() {
      console.log('所属事业部')
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          console.log(this.temp)

          // this.temp.content.userInfo.level = 1

          // level >= 1 安全等级大于等于1 且未安装dlp 则需要dlp验证

          if (this.temp.content.userInfo.level >= 1 && Number(this.temp.content.userInfo.check_dlp) === 0) {
            // 0为未安装dlp 需要IT验证
            this.temp.node = 6
            this.temp.status = 1
          } else {
            // 安全等级小于1或者已经安装dlp 无需dlp验证，直接结束
            this.temp.node = 8
            this.temp.status = 10
          }
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: 'Created Failed',
              })
            } else {
              this.temp.handler = store.getters.name
              this.temp.handler_email = store.getters.email
              this.temp.handler = ''
              notification.success({
                message: 'Success',
                description: 'Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    itApprove() {
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          console.log(this.temp)
          // // 安全等级大于等于1 则需要dlp验证
          this.temp.node = 7
          this.temp.status = 1
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: 'Created Failed',
              })
            } else {
              this.temp.handler = store.getters.name
              this.temp.handler_email = store.getters.email
              this.temp.handler = ''
              notification.success({
                message: 'Success',
                description: 'Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    // dlp
    dlpApprove() {
      console.log('dlp')
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          this.temp.node = 8
          this.temp.status = 10
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: 'Created Failed',
              })
            } else {
              this.temp.handler = store.getters.name
              this.temp.handler_email = store.getters.email
              this.temp.handler = ''
              notification.success({
                message: 'Success',
                description: 'Approved Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    rejectData() {
      this.$refs.temp.validate().then(valid => {
        if (valid) {
          this.Date()
          this.temp.node = 8
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handler_email = store.getters.email
          const cloneTemp = this.stringifyData()
          cloneTemp.orderType = '大数据表查询权限'
          this.activeNodes()
          workflowOrderApprove(cloneTemp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: 'Success',
                description: 'Rejected Successfully',
              })
              this.temp = response.data
              for (const key in this.temp.content) {
                if (this.temp.content[key]) {
                  this.temp.content[key] = JSON.parse(this.temp.content[key])
                }
              }
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.temp.time_now =
        date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>
