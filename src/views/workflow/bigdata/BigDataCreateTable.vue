<template>
  <page-header-wrapper @back="() => router.go(-1)">
    <a-card class="box-card" style="margin-bottom: 20px">
      <a-steps :current="Number(temp.node)" status="finish" style="margin-top: 20px">
        <a-step title="提交工单" />
        <a-step title="大数据负责人审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card class="box-card">
      <template #title>
        <span class="clearfix">{{ temp.orderType }}-申请内容</span>
      </template>
      <!--列表-->
      <a-form ref="tempRef" :model="temp">
        <a-form-item label="申请人">
          <a-tag>{{ temp.founder }}</a-tag>
        </a-form-item>
        <a-form-item v-if="temp.node === 1 || temp.node === 2" label="处理人">
          <a-tag>{{ temp.handler }}</a-tag>
        </a-form-item>
        <a-form-item label="申请状态">
          <a-tag v-if="temp.status === 0 || temp.status === 1" type="warning">待审核</a-tag>
          <a-tag v-if="temp.status === 2" type="warning">处理中</a-tag>
          <a-tag v-if="temp.status === 10" type="success">审批通过</a-tag>
          <a-tag v-if="temp.status === 20" type="danger">审批拒绝</a-tag>
        </a-form-item>
        <a-form-item label="tableName" name="tableName">
          <a-input disabled v-model:value="temp.content.tableName" />
        </a-form-item>
        <a-form-item label="表类型">
          <a-tag>{{ temp.content.currentType }}</a-tag>
        </a-form-item>
        <a-form-item label="SQL语句" name="ddl">
          <TxEditorCode v-model="temp.content.ddl" disabled height="400px" language="sql" />
        </a-form-item>
        <a-form-item label="备注" name="comment">
          <a-textarea disabled v-model="temp.comment" />
        </a-form-item>
        <a-form-item v-if="temp.node === 1 || temp.node === 2" label="审批节点">
          <a-timeline>
            <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index" :dot="activity.timestamp">
              {{ activity }}
            </a-timeline-item>
          </a-timeline>
        </a-form-item>
        <a-form-item v-if="temp.handler.includes(local_name) && temp.node === 1">
          <a-button type="primary" @click="approveData(10)">同意</a-button>
          <a-button type="danger" @click="approveData(20)">拒绝</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </page-header-wrapper>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import store from '@/store'
import { message } from 'ant-design-vue'

// 路由instance
const router = useRouter()
const route = useRoute()
// 对dom的引用
const tempRef = ref()
// 响应式数据
const local_name: string = store.getters.name
const temp = ref<any>({
  orderType: '大数据建表修改表申请',
  founder: store.getters.name,
  founderEmail: store.getters.email,
  handler: '',
  handlerEmail: '',
  timeline: [],
  comment: '',
  node: 0,
  status: 0,
  content: {
    tableName: '',
    currentType: '',
    tableInfoDict: {
      TABLE_SUBJECT: '',
      DIKI_TYPE: '',
      PARTITION_COL: '',
      TABLE_STORGE: '',
      CREATE_USER: '',
    },
    path: '',
    ddl: '',
  },
})

// 生命周期钩子
onMounted(() => getList())

// methods
const approveData = (status: number) => {
  temp.value.node = 2
  temp.value.status = status
  temp.value.handler = store.getters.name
  temp.value.handlerEmail = store.getters.email
  temp.value.content.tableInfoDict = temp.value.content.tableInfoDict
  approveOrder(temp.value).then(
    res => {
      temp.value.handler = ''
      message.success('Success')
    },
    () => {
      message.error('Failed')
    }
  )
}
const getList = () => {
  if (route.query.id) {
    getOrderInfo(route.query.id).then(res => {
      temp.value = res.Data
    })
  }
}
</script>

<style lang="scss"></style>
