<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2022-09-13 17:53:21
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2024-06-20 17:06:47
 * @FilePath: \cloud_web\src\views\workflow\CreateWorkflow.vue
 * @Description:
 *
 * Copyright (c) 2022 by huidong_yang <EMAIL>, All Rights Reserved.
-->
<template>
  <page-header-wrapper>
    <div class="app">
      <a-card>
        <a-select
          placeholder="搜索"
          v-model:value="searchValue"
          show-search
          style="width: 200px"
          @change="handleChange"
        >
          <a-select-opt-group v-for="i in serverList">
            <template #label>
              <span>
                <user-outlined />
                {{ i.type }}
              </span>
            </template>
            <a-select-option v-for="u in i.lists" :value="JSON.stringify(u)">{{ u.name }}</a-select-option>
          </a-select-opt-group>
        </a-select>
        <tx-button style="position: absolute; right: 0; margin-right: 20px" type="primary" @click="workflowAgent">
          审批流程委托
        </tx-button>
      </a-card>
      <a-card
        v-for="item in serverList"
        :key="item.id"
        :title="item.type"
        style="border-radius: 10px; margin-top: 16px"
      >
        <div class="flowContent"></div>
        <router-link v-for="i in item.lists" :key="i.id" :to="i.route" class="flowItem">
          <img :src="i.svg" alt="" class="flowIcon" />
          <p>
            {{ i.name }}
            <a-tooltip v-if="i.tooltip">
              <template #title>{{ i.tooltip }}</template>
              <a-icon type="question-circle" />
            </a-tooltip>
          </p>
        </router-link>

        <!-- <a v-for="i in item.lists" :key="i.id" :href="i.route" class="flowItem">
          <img :src="i.svg" alt="" class="flowIcon" />
          <p>{{ i.name }}</p>
        </a> -->
      </a-card>
    </div>
  </page-header-wrapper>
</template>

<script>
import { serverList } from './workFlowDatas'
export default {
  data() {
    return {
      serverList: serverList,
      searchValue: null,
    }
  },
  mounted() {
    console.log(this.serverList)
  },
  methods: {
    handleChange(v) {
      let r = JSON.parse(v)
      this.$router.push(r.route)
    },
    workflowAgent() {
      this.$router.push({ path: '/workflow/workflow-agent' })
    },
  },
}
</script>

<style lang="less" scoped>
.flowContent {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.flowItem {
  display: inline-block;
  width: 160px;
  height: 60px;
  margin-top: 16px;
  text-align: center;

  p {
    font-size: 14px;
    color: #000;
    margin: 0;
    margin-top: 12px;
  }
}

a:hover {
  text-decoration: none;
  outline: 0;
  cursor: pointer;
  color: inherit;
  text-decoration: none;

  p {
    color: rgb(101, 101, 255);
  }
}

.flowIcon {
  width: 30px;
  height: 30px;
  vertical-align: middle;
  border-style: none;
}
</style>
