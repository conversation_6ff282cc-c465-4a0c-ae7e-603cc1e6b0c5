<template>
    <div class="dynamic-wrap" :style="{ maxHeight: wrapHeight + 'px' }">
        <a-row v-for="(item, i) in arr" :gutter="24" :key="i">
          <a-col :span="3" class="pl-0"></a-col>
          <a-col :span="6">
            <a-form-item class="my-form-item" :label="'Node'+i" :label-col="{ span: 4}" :wrapper-col="{ span: 20 }"
              :name="['arr', i, 'ip']"
              :rules="[{ required: false, message: '请选择节点ip地址'}]"
            >
              <a-select
                v-model:value="item.ip"
                class="w-100"
                placeholder="IP"
                :showSearch="true"
                :allowClear="true"
                @search="remoteMethod"
              >
                <a-select-option v-for="item1 in ipOptions" :key="item1" :value="item1"> {{ item1 }} </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" class="pl-0">
            <a-form-item class="my-form-item"
              :name="['arr', i, 'port']"
              :rules="[{ required: false, message: '请输入端口号', pattern: '[0-9]' }]"
            >
              <a-input
                v-model:value="item.port"
                class="mr-0 w-100"
                placeholder="端口"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2" class="pl-0">
            <a-form-item class="my-form-item"
              :name="['arr', i, 'weight']"
              :rules="[{ required: false, message: '请输入权重' }]"
            >
              <a-input-number
                v-model:value="item.weight"
                class="w-100"
                placeholder="权重"
                :min="1"
              />
            </a-form-item>
          </a-col>
          <a-col :span="3" class="pl-0">
            <a-form-item :labelCol="{span: 0}" :wrapperCol="{span: 24}" class="my-form-item">
                <tx-button
                  v-if="item === 0"
                  type="dashed"
                  size="small"
                  icon="plus"
                  class="addRowBtn"
                  style="width: 30px"
                  @click="addRow"></tx-button>
                <tx-button
                  v-else
                  type="dashed"
                  size="small"
                  icon="delete"
                  class="minusRowBtn"
                  style="width: 30px"
                  @click="removeRow(item)"></tx-button>
            </a-form-item>
          </a-col>
        </a-row>
    </div>
</template>

<script>
import { getIpList } from '@/api/domain/domain'
export default {
  name: 'DnsNodesForm',
  props: {
    typeChoosetitle: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    wrapHeight: { // 表单容器的高度
      type: Number,
      default: 120
    },
    arr: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      ipOptions: [],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        }
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 }
        }
      }
    }
  },
  methods: {
    // 新增一行
    addRow() {
      this.arr.push({
        ip: '',
        port: '',
        weight: '',
      })
    },
    // 移除某行
    removeRow(i) {
      if (this.arr.length > 1) {
        this.arr.splice(i, 1)
      }
    },
    remoteMethod (query) {
        getIpList({ sKey: query }).then((response) => {
          this.ipOptions = response.Data.ips
        })
    }
  }
}
</script>

<style lang="less" scoped>
  .dynamic-wrap {
    padding-top: 10px;
    background-color: white;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 7px;
    }
    &::-webkit-scrollbar-thumb {
      background: #d8d8d8;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-track-piece {
      background: transparent;
    }
  }
  .minusRowBtn {
    color: #f5222d;
    background: #fff1f0;
    border-color: #ffa39e;
    padding-right: 7px;
    padding-left: 7px;
    height: 29px;
    margin-left: 10px;
  }
  .addRowBtn {
    color: #2a6af5;
    background: hsl(0, 0%, 99%);
    border-color: #86a6fd;
    padding-right: 7px;
    padding-left: 7px;
    height: 29px;
    margin-left: 10px;
  }
  .my-form-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 10px;
    vertical-align: top;
}
</style>
