<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="tempRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="域名">{{ temp.content.name }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="解析来源">
              {{ temp.content.source }}
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="类型" name="type" button-style="solid">
              <a-select
                v-model:value="temp.content.type"
                placeholder="请选择类型"
                :options="dnsTypeList"
                style="width: 200px"
              ></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form :model="temp.content" ref="formRef">
              <dynamic-form
                ref="testForm"
                :title="`${PARTONE}`"
                :wrapHeight="360"
                :arr="temp.content.resourceRecords"
                :typeChoosetitle="temp.content.type"
              />
            </a-form>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="地理位置" name="geolocation" button-style="solid">
              <a-select
                v-model:value="temp.content.geolocation"
                :allowClear="true"
                placeholder="选填，一般为空即可"
                :options="geolocationOptions"
                style="width: 200px"
                show-search
                :filter-option="filterOption"
              ></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="是否需要WAF" name="waf">
              <a-radio-group v-model:value="temp.content.waf" button-style="solid">
                <a-radio-button value="none">无</a-radio-button>
                <a-radio-button value="aliyun">国内-合合</a-radio-button>
                <a-radio-button value="aliyun-qxb">国内-启信宝</a-radio-button>
                <a-radio-button value="cloudflare">国外</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <!-- <div v-if="temp.content.waf != 'none'">
          <a-form-model :model="temp.content" :rules="tempRules" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
            <a-row :gutter="24">
              <a-col :span="2"></a-col>
              <a-col :span="6">
                <a-form-model-item label="有无CDN配置" name="hasCdn">
                  <a-radio-group v-model:value="temp.content.hasCdn" button-style="solid">
                    <a-radio-button :value="false">无</a-radio-button>
                    <a-radio-button :value="true">有</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="https强制跳转" name="needHttpsRedirect">
                  <a-radio-group v-model:value="temp.content.needHttpsRedirect" button-style="solid">
                    <a-radio-button :value="false">否</a-radio-button>
                    <a-radio-button :value="true">是</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </div> -->
        <!-- 用户在变更时无法理解 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="访问范围" name="scope">
              <a-radio-group v-model:value="temp.content.scope" button-style="solid" disabled>
                <a-radio-button :value="3">办公室+机房访问</a-radio-button>
                <a-radio-button :value="2">办公室访问</a-radio-button>
                <a-radio-button :value="4">机房访问</a-radio-button>
                <a-radio-button :value="1">公网访问</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="域名有效期" name="expiryDate">
              <a-radio-group v-model:value="temp.content.expiryDate" button-style="solid">
                <a-radio-button :value="31536000">一年</a-radio-button>
                <a-radio-button :value="63072000">二年</a-radio-button>
                <a-radio-button :value="94608000">三年</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="TTL" name="ttl" button-style="solid">
              <a-radio-group v-model:value="temp.content.ttl" button-style="solid">
                <a-radio-button :value="300">5分钟</a-radio-button>
                <a-radio-button :value="600">10分钟</a-radio-button>
                <a-radio-button :value="900">15分钟</a-radio-button>
                <a-radio-button :value="3600">1小时</a-radio-button>
                <a-radio-button :value="172800">2天</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请理由" name="reason">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="代理域名" name="proxyName">
              <a-input v-model:value="temp.content.proxyName" placeholder="选填，如果有代理，填写代理域名" />
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <a-row :gutter="24" v-if="temp.node > 0">
          <a-col :span="12">
            <a-form-model-item label="反馈信息" name="comment">
              <a-textarea v-model:value="temp.content.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="紧急审批" v-if="immediatelyList.includes(localUser) && temp.node === 0">
              <a-switch v-model:checked="immediatelySwitchOn" @change="onChange" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/domain/dns-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 4 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <!-- <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="auditApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item> -->
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="managementApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="reApply">重新申请</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { filterLabelValue } from '@aim/helper'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { geolocationOptionsApi } from '@/api/domain/dns'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { stringToObject } from '@/utils/helper'
import DynamicForm from './DynamicForm.vue'
const PARTONE = 'partOne'
export default {
  name: 'DnsModify',
  components: { DynamicForm },
  data() {
    return {
      dnsTypeList: [
        {
          label: 'A',
          value: 'A',
        },
        {
          label: 'CNAME',
          value: 'CNAME',
        },
        {
          label: 'AAAA',
          value: 'AAAA',
        },
        {
          label: 'TXT',
          value: 'TXT',
        },
      ],
      geolocationOptions: [],
      serverOption: [],
      serverOptionMini: [],
      immediatelySwitchOn: false,
      node_status: 1,
      tempRules: antdFormRulesFormat({
        'content.type': [{ required: true, message: '请选择类型', trigger: 'change' }],
        'content.scope': [{ required: true, message: '请选择访问范围', trigger: 'change' }],
        'content.ttl': [{ required: true, message: '请选择TTL', trigger: 'change' }],
        'content.resourceRecords': [{ required: true, message: '请输入解析记录地址', trigger: 'blur' }],
        'content.expiryDate': [{ required: true, message: '请选择有效期', trigger: 'change' }],
        'content.reason': [{ required: true, message: '请填写修改理由', trigger: 'blur' }],
        'content.remote_addrs': [{ required: true, message: '请选择访问范围', trigger: 'change' }],
        'content.route_expiry_date': [{ required: true, message: '请输入过期时间', trigger: 'blur' }],
      }),
      localUser: store.getters.email,
      immediatelyList: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      immediately: 'slow',
      temp: {
        id: '',
        orderType: '域名修改',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        time_now: '',
        handler: '',
        handlerEmail: '',
        content: {
          id: undefined,
          source: '',
          name: '',
          type: 'A',
          resourceRecords: [{ Value: '' }],
          recordId: '',
          ttl: 600,
          scope: 2,
          geolocation: '',
          business: '',
          project: '',
          waf: 'none',
          hasCdn: false,
          needHttpsRedirect: false,
          env: 'online',
          team: '',
          expiryDate: 31536000,
          reason: '',
          proxyName: '',
          comment: '',
        },
        timeline: [],
      },
      securityGroupList: [],
      pre_project_name_list: [],
      preStatus: false,
      vpcStatus: false,
      tipsStatus: false,
      modalFormBindServerVisible: false,
      vpc_index: 3,
      instance_index: 0,
      scrollPage: 1,
      serverScrollPage: 1,
      valueData: '',
      serverValueData: '',
      treePageSize: 50,
      modalFormPreBindServerVisible: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      PARTONE,
      form: this.$form.createForm(this, 'formRef'),
    }
  },
  created() {
    this.geolocationOptions = geolocationOptionsApi()
    this.getList()
  },
  methods: {
    filterOption: filterLabelValue,
    /*
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    */
    reApply() {
      this.$router.push({
        path: '/workflow/dns-edit',
      })
      this.temp = {
        ...this.temp,
        id: '',
        orderType: '域名修改',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        timeline: [],
        comment: '',
      }
    },
    getList() {
      if (this.$route.query.dnsId) {
        this.id = this.$route.query.id
        this.$route.query.dns = stringToObject(this.$route.query.dns)
        this.temp.content.scope = this.$route.query.dns.hasOwnProperty('scope') ? this.$route.query.dns.scope : 2
        if (this.temp.content.scope === 0) {
          this.temp.content.scope = 2
        }
        this.temp.content.waf = 'none'
        this.temp.content.expiryDate = this.$route.query.dns.hasOwnProperty('expiry_date')
          ? this.$route.query.dns.expiry_date
          : 31536000
        this.temp.content.type = this.$route.query.dns.type
        this.temp.content.ttl = this.$route.query.dns.ttl
        this.temp.content.id = this.$route.query.dns.id
        this.temp.content.name = this.$route.query.dns.name
        this.temp.content.source = this.$route.query.dns.source
        this.temp.content.recordId = this.$route.query.dns.recordId
        this.temp.content.geolocation = this.$route.query.dns.geolocation
        this.temp.content.resourceRecords = this.listFilter(this.$route.query.dns.resourceRecords)
      } else if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.temp.content.waf = 'none'
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    listFilter(value) {
      if (!value) return []
      let value1
      value1 = value.split('|')
      let res = []
      for (let j = 0; j < value1.length; j++) {
        res[j] = { value: value1[j] }
      }
      return res
    },
    DnsSave() {
      this.form.validateFields(values => {
        // 下列赋值无效，已同步到 resourceRecords
        const partOneArr = []
        values[`${PARTONE}value`].forEach((item, index) => {
          const obj = {
            value: item,
          }
          partOneArr.push(obj)
        })
        this.temp.content.resourceRecords = partOneArr
      })
    },
    onChange(checked) {
      if (checked) {
        this.immediately = 'run'
      } else {
        this.immediately = 'slow'
      }
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.DnsSave()
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.content.immediately = this.immediately
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/dns-edit', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    refreshZoneData(val) {
      this.$forceUpdate()
      this.instance_index = (this.instanceTypeList || []).findIndex(item => item.zone_id === val)
      this.temp.content.approval_data.instance_type = ''
    },
    refreshData(val) {
      this.$forceUpdate()
      this.vpc_index = (this.vpc_info || []).findIndex(item => item.vpc_id === val)
      this.temp.content.approval_data.default_security_group_id = ''
      this.temp.content.approval_data.default_v_switch_id = ''
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.DnsSave()
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
    },
    safeApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 1
          this.DnsSave()
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
    },
    // auditApproveData () {
    //   antdFormValidate(this.$refs.orderForm, (valid) => {
    //     if (valid) {
    //       this.temp.node = 4
    //       this.temp.status = 1
    //       this.DnsSave()
    //       this.temp.content = { data: JSON.stringify(this.temp.content) }
    //       this.temp.handler = store.getters.name
    //       this.temp.handlerEmail = store.getters.email
    //       approveOrder(this.temp).then((response) => {
    //         if (response === undefined) {
    //           notification.error({
    //             message: '审批失败',
    //             description: '后端接口错误，请联系运维开发排查~'
    //           })
    //         } else if (response.Code !== 200) {
    //           notification.error({
    //             message: '审批执行报错',
    //             description: response.Code
    //           })
    //         } else {
    //           notification.success({
    //             message: '审批成功',
    //             description: '管理员审批成功'
    //           })
    //           response.Data.content = JSON.parse(response.Data.content.data)
    //           this.temp = response.Data
    //           this.node_status = 1
    //         }
    //       })
    //     }
    //   })
    // },
    managementApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 10
          this.DnsSave()
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 20
          this.DnsSave()
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 30
          this.DnsSave()
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style>
.dynamic-delete-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
