<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node > 2" label="用户tcp代理地址" class="my-ant-form-item">
          <a-span>{{ tcpDomain }}:{{ temp.content.serverPort }}</a-span>
        </a-form-model-item>
        <a-form-model-item label="网关集群" class="my-ant-form-item" name="envId">
          <a-select
            showSearch
            optionFilterProp="label"
            :options="envAddList"
            v-model:value="temp.content.envId"
            placeholder="请选择集群环境"
            @change="changeStreamRoute"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item v-if="temp.content.envId" label="ServerPort" class="my-ant-form-item" name="serverPort">
          <a-select
            placeholder="8081"
            v-model:value="temp.content.serverPort"
            :showSearch="true"
            :allowClear="true"
            style="width: 200px"
            @search="StreamRoutePort"
            @dropdownVisibleChange="handleVisibleChange"
          >
            <a-select-option v-for="item1 in serverPortList" :key="item1" :value="item1">
              {{ item1 }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="RemoteAddr" class="my-ant-form-item">
          <a-input v-model:value="temp.content.remoteAddr" placeholder="ipv4/ipv4CRID" />
        </a-form-model-item>
        <a-form-model-item label="Upstream">
          <div class="nodes-container">
            <div v-for="(item, index) in temp.content.nodes" :key="index" class="nodes-container">
              <a-divider class="ant-divider-horizonta1l" />
              <a-row class="custom-row">
                <a-col :span="10">
                  <a-form-model-item label="IP" class="my-ant-form-item" name="">
                    <a-input v-model:value="item.ip" placeholder="" style="width: 240px" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="5">
                  <a-form-model-item label="端口" class="my-ant-form-item" name="">
                    <a-input-number v-model:value="item.port" :min="1" :max="65533" style="width: 80px" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="4">
                  <a-form-model-item label="权重" class="my-ant-form-item" name="">
                    <a-input-number v-model:value="item.weight" :min="1" :max="100" style="width: 60px" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="1">
                  <a-form-model-item label="" class="my-ant-form-item" v-if="index === 0">
                    <tx-button
                      type="primary"
                      icon="plus"
                      @click="rowServerAdd"
                      size="small"
                      style="min-height: 24px; line-height: 24px; padding: 0 5px"
                    ></tx-button>
                  </a-form-model-item>
                  <a-form-model-item label="" class="my-ant-form-item" v-else>
                    <tx-button
                      type="danger"
                      icon="minus"
                      @click="rowServerDelete(index)"
                      size="small"
                      style="min-height: 24px; line-height: 24px; padding: 0 5px"
                    ></tx-button>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-model-item>
        <a-form-model-item name="reason" label="申请理由">
          <a-textarea v-model:value="temp.content.reason" />
        </a-form-model-item>
        <a-form-model-item name="comment" v-if="temp.node > 0" label="回复/备注">
          <a-textarea v-model:value="temp.comment" />
        </a-form-model-item>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/server/asset-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { getStreamRoutePort } from '@/api/gateway/streamRoute'

import cloneDeep from 'lodash.clonedeep'
import { groupNames } from '@/api/gateway/groupAndIp'

// import pick from 'lodash.pick'
export default {
  name: 'GatewayStreamRoute',
  data() {
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      tcpDomain: '',
      checkLoading: false,
      envAddList: [],
      serverPortList: [],
      temp: {
        id: '',
        orderType: '网关StreamRoute配置',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          nodes: [{ ip: '127.0.0.1', port: 80, weight: 50 }],
          reason: undefined,
        },
        timeline: [],
        comment: '',
      },
      rules: {
        serverPort: [
          { required: true, message: '', trigger: 'blur' },
          { validator: this.validateServerPort, trigger: 'blur' },
        ],
        envId: [{ required: true, message: '请选择集群环境', trigger: 'blur' }],
        remoteAddr: [{ required: true, message: '请填写remoteAddr', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
    }
  },
  created() {},
  mounted() {
    this.getInfo()
    groupNames()
      .then(res => {
        this.envGroups = res.Data.clusters
        for (let i = 0, len = res.Data.clusters.length; i < len; i++) {
          this.envAddList.push({
            value: res.Data.clusters[i].envId,
            label: res.Data.clusters[i].env,
            domain: res.Data.clusters[i].domain,
          })
          if (res.Data.clusters[i].envId === this.temp.content.envId) {
            this.tcpDomain = res.Data.clusters[i].domain
          }
        }
      })
      .catch(() => {})
  },
  methods: {
    validateServerPort(rule, value, callback) {
      if ((value >= 10000 && value <= 11000) || (value >= 8000 && value <= 9000)) {
        callback() // 验证通过
      } else {
        callback(new Error('请填写serverPort(限制8000-9000, 10000-11000),特殊需求找管理员配置。'))
      }
    },
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            response.Data.content = JSON.parse(response.Data.content.data)
            this.temp = response.Data
            // this.temp.content.idc = this.temp.content.idc.split('|')
            this.node_status = 1
            this.envAddList.forEach(item => {
              if (item.value === this.temp.content.envId) {
                this.tcpDomain = item.domain
              }
            })
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    handleVisibleChange() {
      if (open) {
        getStreamRoutePort({ envId: this.temp.content.envId}).then(response => {
          let arry = response.Data.ports
          for (let i = 0; i < arry.length; i++) {
            if (this.serverPortList.indexOf(arry[i]) === -1) {
              this.serverPortList.push(arry[i])
            }
          }
        })
      }
    },
    StreamRoutePort(searchText) {
      this.serverPortList = []
      if (searchText !== '') {
        getStreamRoutePort({ envId: this.temp.content.envId, serverPort: searchText }).then(response => {
          let arry = response.Data.ports
          for (let i = 0; i < arry.length; i++) {
            if (this.serverPortList.indexOf(arry[i]) === -1) {
              this.serverPortList.push(arry[i])
            }
          }
        })
      }
    },
    changeStreamRoute() {
      this.temp.content.serverPort = undefined
      this.serverPortList=[]
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(this.temp.content) }
          createOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.$router.push({ path: '/workflow/steam_route', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email

          const obj = cloneDeep(this.temp)
          this.temp.content.env = this.envAddList.find(item => item.value === this.temp.content.envId).label
          let newNodes = {}
          this.temp.content.nodes.forEach(node => {
            newNodes[`${node.ip}:${node.port}`] = node.weight
          })
          this.temp.content.upstream = JSON.stringify({ type: 'roundrobin', nodes: newNodes })
          obj.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          const obj = cloneDeep(this.temp)
          obj.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(obj).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    rowServerAdd() {
      this.temp.content.nodes.push({ ip: '127.0.0.1', port: 80, weight: 50 })
    },
    rowServerDelete(index) {
      this.temp.content.nodes.splice(index, 1)
    },

    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    NowDate(shifting) {
      const timeStamp = new Date().getTime() - 24 * 60 * 60 * 1000 * shifting
      const date = new Date(timeStamp)
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      if (month < 10) {
        month = '0' + month
      }
      if (day < 10) {
        day = '0' + day
      }
      return year + '-' + month + '-' + day
    },
  },
}
</script>

<style lang="less" scoped>
.ant-divider-horizonta1l {
  clear: both;
  display: flex;
  margin: 10px 0;
  min-width: 100%;
  width: 100%;
}
.my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
}
.nodes-container {
  max-height: 350px;
  overflow-x: auto; /* 允许垂直方向上的滚动 */
}
</style>
