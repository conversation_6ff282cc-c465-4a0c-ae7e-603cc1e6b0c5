<template>
  <div :class="$style['dynamic-wrap']" :style="{ maxHeight: wrapHeight + 'px' }">
    <a-row v-for="(item, i) in arr" :gutter="24" :key="i">
      <a-col :span="2" class="pl-0"></a-col>
      <a-col :span="16">
        <a-form-item
          v-bind="i === 0 ? formItemLayout : formItemLayoutWithOutLabel"
          ref="formItems"
          :name="['resourceRecords', i, 'value']"
          :label="i === 0 ? (labelName ? labelName : '解析记录') : ''"
          :rules="[{ required: true, trigger: 'blur' }]"
        >
          <a-input
            v-model:value="item.value"
            class="mr-0 w-100"
            :placeholder="typeChoosetitle === 'A' ? '请输入IP，格式：**************' : '请输入解析的地址'"
          />
          <!-- @blur="item.value = item.value.trim().toLowerCase()" -->
        </a-form-item>
      </a-col>
      <a-col :span="2" class="pl-0">
        <a-form-item :labelCol="{ span: 0 }" :wrapperCol="{ span: 24 }">
          <tx-button
            v-if="i == 0"
            type="primary"
            icon="plus"
            style="width: 30px"
            size="small"
            @click="addRow"
          ></tx-button>
          <tx-button
            v-else
            type="dashed"
            size="small"
            icon="minus"
            style="width: 30px"
            @click="removeRow(i)"
          ></tx-button>
        </a-form-item>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { validateDomain, validateIntranetIP } from '@/utils/helper'

export default {
  name: 'DynamicForm',
  props: {
    typeChoosetitle: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    labelName: {
      type: String,
      default: '',
    },
    wrapHeight: {
      // 表单容器的高度
      type: Number,
      default: 120,
    },
    arr: {
      type: Array,
      default: function () {
        return []
      },
    },
    listData: {
      type: Array,
      default: function () {
        return []
      },
    },
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
    }
  },
  methods: {
    // 新增一行
    addRow() {
      this.arr.push({
        value: '',
      })
    },
    // 移除某行
    removeRow(i) {
      if (this.arr.length > 1) {
        this.arr.splice(i, 1)
      }
    },
    itemValidate(i) {
      // return noc.promise((resolve, reject) => {
      //   let item = this.arr[i]
      //   let message
      //   if (this.typeChoosetitle == 'A') {
      //     message = validateIntranetIP(item.value)
      //   } else {
      //     message = validateDomain(item.value)
      //   }
      //   message ? reject(message) : resolve()
      //   if (message) {
      //     Object.defineProperty(item, 'hasError', {
      //       configurable: true,
      //       value: true,
      //     })
      //   } else {
      //     delete item.hasError
      //   }
      // })
    },
    validate() {
      noc.loop(this.$refs.formItems, item => {
        item.onFieldBlur?.()
      })
    },
  },
}
</script>

<style lang="less" module>
.dynamic-wrap {
  padding-top: 10px;
  background-color: white;
  overflow-y: scroll;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 7px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }
}
</style>
