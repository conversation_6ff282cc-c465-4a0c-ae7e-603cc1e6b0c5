<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=482444436" style="text-decoration: none">
          流程文档
        </a>
      </tx-button>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全负责人审批" />
        <a-step title="Mike审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="所属部门">{{ temp.content.dep }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="domain" label="待购买域名">
              <a-input
                placeholder="输入域名，点击检查后才能提交"
                v-model:value="temp.content.domain"
                style="width: calc(100% - 100px)"
              />
              <tx-button type="primary" @click="checkDomain" style="margin-left: 10px">检查</tx-button>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item name="icp" label="备案支持">
              <a-input v-model:value="temp.content.icp" disabled />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="6">
          <a-col :span="11">
            <a-form-model-item name="range" label="使用范围">
              <a-tooltip placement="right" :arrow="mergedArrow">
                <template #title>
                  <span>国内域名需要备案</span>
                </template>
                <a-radio-group v-model:value="temp.content.range" button-style="solid">
                  <a-radio-button value="internal">国内</a-radio-button>
                  <a-radio-button value="oversea">海外</a-radio-button>
                  <a-radio-button value="all">全球使用</a-radio-button>
                </a-radio-group>
              </a-tooltip>
            </a-form-model-item>
          </a-col>
          <a-col :span="13">
            <a-form-model-item name="ssl" label="是否需要ssl证书">
              <a-radio-group v-model:value="temp.content.ssl" name="radioGroup" style="width: 100%">
                <a-radio value="true">是</a-radio>
                <a-radio value="false">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12" v-if="temp.content.ssl === 'true'">
            <a-form-model-item name="ssl" label="证书类型">
              <a-radio-group v-model:value="temp.content.sslType" button-style="solid">
                <a-radio-button value="single">单域名证书</a-radio-button>
                <a-radio-button value="multi">泛域名证书</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="temp.content.sslType === 'single' && temp.content.ssl === 'true'">
            <a-form-model-item name="sslInfo" label="证书信息">
              <a-textarea v-model:value="temp.content.sslInfo" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- <a-row :gutter="24">
          <a-col :span="12" v-if="temp.content.sslType==='single'">
            <a-form-model-item name="sslInfo" label="证书信息">
              <a-textarea v-model:value="temp.content.sslInfo" />
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/备注">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData" :disabled="submitStatus">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/workflow/createWorkflow">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="securityOfficerApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="mikeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node !== 0 && temp.node !== 5 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { domainPurchaseCheck } from '@/api/domain/domain'

export default {
  name: 'DomainPurchase',
  data() {
    return {
      localUser: store.getters.email,
      time_now: '',
      node_status: 1,
      submitStatus: true,
      temp: {
        id: '',
        orderType: '域名购买',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        content: {
          domain: '',
          icp: '',
          dep: store.getters.org + '-' + store.getters.dep,
          reason: '',
          range: '',
          ssl: false,
          sslType: '',
          sslInfo: '',
        },
        timeline: [],
        comment: '',
      },
      rules: {
        domain: [{ required: true, message: '请填写需购买的域名', trigger: 'blur' }],
        range: [{ required: true, message: '请选择域名范围', trigger: 'blur' }],
        ssl: [{ required: true, message: '请选择是否需要ssl证书', trigger: 'blur' }],
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      checkData: {
        domainName: '',
      },
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.nodeStatus = 0
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
          } else {
            this.$router.push({ path: '/404' })
          }
        })
        this.nodeStatus = 1
      }
    },
    checkDomain() {
      this.submitStatus = true
      this.checkData.domainName = this.temp.content.domain
      domainPurchaseCheck(this.checkData).then(response => {
        const existData = response.Data.message
        console.log(existData)
        if (existData.indexOf('china') !== -1) {
          this.$notification['success']({
            message: '域名检查',
            description: this.checkData.domainName + '域名可以购买使用，可国内备案。工单结束后会通知Mike',
          })
          this.temp.content.icp = '支持国内备案'
          this.submitStatus = false
        } else if (existData.indexOf('oversea') !== -1) {
          
          let message = this.checkData.domainName
          if (this.checkData.domainName && /\.ai$/i.test(this.checkData.domainName.trim())) {
            message = message+ '.ai域名无法备案，不支持地域级解析，且价格较高，需要Mike审批'
          } else {
            message = message + '暂不支持该域名后缀检测，不能国内备案。需要特殊说明下理由，需要Mike审批'
          }

          this.$notification['warning']({
            message: '域名检查',
            description: message,

          })
          this.temp.content.icp = '不支持国内备案'
          this.submitStatus = false
        } else {
          this.$notification['error']({
            message: '域名检查',
            description: this.checkData.domainName + ' : 域名已被注册购买或检测异常，请更换域名。',
          })
          this.temp.content.icp = '支持国内备案'
          // this.submitStatus = false
          this.submitStatus = true
        }
      })
    },
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.temp = response.Data
              this.$router.push({ path: '/workflow/domain-purchase', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    securityOfficerApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '安全负责人审批成功',
              })
            }
          })
        }
      })
    },
    mikeApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 4
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: 'Mike审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 5
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
