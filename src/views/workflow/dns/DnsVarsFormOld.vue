<template>
  <div>
    <div class="dynamic-wrap" :style="{ maxHeight: wrapHeight + 'px' }">
      <div v-for="item in keysList" :key="item">
        <a-row :gutter="24">
          <a-col :span="3" style="padding-left: 0px"></a-col>
          <a-col :span="6">
            <a-form-item class="my-form-item" :label="'Vars'+item" :label-col="{ span: 4}" :wrapper-col="{ span: 20 }">
              <a-input
                placeholder="参数名称"
                style="width: 100%; margin-right: 0px"
                v-decorator="[
                  `${title}var_name[${item}]`,
                  {
                    initialValue: arr[item] ? arr[item].var_name : undefined,
                    rules: [{ required: false, message: '请输入参数名称.', pattern: [0-9] }]
                  }
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2" style="padding-left: 0px">
            <a-form-item class="my-form-item">
              <a-select
                placeholder="运算符"
                style="width: 100%"
                v-decorator="[
                  `${title}var_operator[${item}]`,
                  {
                    initialValue: arr[item] ? arr[item].var_operator : undefined,
                    rules: [{ required: false, message: '请选择运算符.'}]
                  }
                ]"
              >
                <a-select-option value="==">等于</a-select-option>
                <a-select-option value="~=">不等于</a-select-option>
                <a-select-option value=">">大于</a-select-option>
                <a-select-option value="<">小于</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" style="padding-left: 0px">
            <a-form-item class="my-form-item">
              <a-input
                placeholder="参数值"
                style="width: 100%; margin-right: 0px"
                v-decorator="[
                  `${title}var_value[${item}]`,
                  {
                    initialValue: arr[item] ? arr[item].var_value : undefined,
                    rules: [{ required: false, message: '请输入参数值.' }]
                  }
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2" style="padding-left: 0px">
            <a-form-item :labelCol="{span: 0}" :wrapperCol="{span: 24}" class="my-form-item">
              <template v-if="keysList.length > 1 && item !==0">
                <tx-button
                  type="dashed"
                  size="small"
                  icon="delete"
                  class="minusRowBtn"
                  style="width: 30px"
                  @click="removeRow(item)"></tx-button>
              </template>
              <template v-if="item === 0">
                <tx-button
                  type="dashed"
                  size="small"
                  icon="plus"
                  class="addRowBtn"
                  style="width: 30px"
                  @click="addRow"></tx-button>
              </template>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DnsVarsForm',
  props: {
    typeChoosetitle: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    wrapHeight: { // 表单容器的高度
      type: Number,
      default: 120
    },
    arr: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      id: 0,
      keysList: [],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        }
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 }
        }
      }
    }
  },
  created () {
    this.form = this.$form.createForm(this, 'formRef')
    this.init()
  },
  methods: {
    // 初始化
    init () {
      const arr = [0]
      if (this.arr.length !== 0) {
        for (let i = 1; i < (this.arr).length; i++) {
          arr.push(i)
          this.id = this.id + 1
        }
      }
      this.keysList = arr
    },
    // 移除某行
    removeRow (k) {
      if (this.keysList.length === 1) { // 如果存在可以移除所有行的情况，把条件改为this.keysList.length === 0即可
        return
      }
      this.keysList = this.keysList.filter(item => item !== k)
    },
    // 新增一行
    addRow () {
      this.id = this.id + 1
      this.keysList = this.keysList.concat(this.id)
    }
  }
}
</script>

<style lang="less" scoped>
  .dynamic-wrap {
    padding-top: 10px;
    background-color: white;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 7px;
    }
    &::-webkit-scrollbar-thumb {
      background: #d8d8d8;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-track-piece {
      background: transparent;
    }
  }
  .minusRowBtn {
    color: #f5222d;
    background: #fff1f0;
    border-color: #ffa39e;
    padding-right: 7px;
    padding-left: 7px;
    height: 29px;
    margin-left: 10px;
  }
  .addRowBtn {
    color: #2a6af5;
    background: hsl(0, 0%, 99%);
    border-color: #86a6fd;
    padding-right: 7px;
    padding-left: 7px;
    height: 29px;
    margin-left: 10px;
  }
  .my-form-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin-bottom: 10px;
    vertical-align: top;
}
</style>
