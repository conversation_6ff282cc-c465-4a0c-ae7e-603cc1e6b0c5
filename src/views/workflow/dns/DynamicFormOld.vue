<template>
  <div>
    <div class="dynamic-wrap" :style="{ maxHeight: wrapHeight + 'px' }">
      <div v-for="item in keysList" :key="item">
        <a-row :gutter="24">
          <a-col :span="2" style="padding-left: 0px"></a-col>
          <a-col :span="16">
            <a-form-item
              :label="item === 0 ? (labelName ? labelName : '解析记录') : ''"
              v-bind="item === 0 ? formItemLayout : formItemLayoutWithOutLabel"
            >
              <a-input
                :placeholder="typeChoosetitle === 'A' ? '请输入IP, 格式:**************' : '请输入解析的地址'"
                style="width: 100%; margin-right: 0px"
                v-decorator="[
                  `${title}value[${item}]`,
                  {
                    initialValue: arr[item] ? arr[item].value : undefined,
                    rules: [{ required: true, message: '请输入解析记录', trigger: 'blur' }],
                  },
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2" style="padding-left: 0px">
            <a-form-item :labelCol="{ span: 0 }" :wrapperCol="{ span: 24 }">
              <template v-if="keysList.length > 1 && item !== 0">
                <tx-button
                  type="dashed"
                  size="small"
                  icon="minus"
                  style="width: 30px"
                  @click="removeRow(item)"
                ></tx-button>
              </template>
              <template v-if="item === 0">
                <tx-button type="primary" icon="plus" style="width: 30px" size="small" @click="addRow"></tx-button>
              </template>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script>
import { notification } from 'ant-design-vue'
export default {
  name: 'DynamicForm',
  props: {
    typeChoosetitle: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    labelName: {
      type: String,
      default: ''
    },
    wrapHeight: {
      // 表单容器的高度
      type: Number,
      default: 120
    },
    arr: {
      type: Array,
      default: function () {
        return []
      }
    },
    listData: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      id: 0,
      keysList: [],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        }
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 }
        }
      }
    }
  },
  created () {
    this.form = this.$form.createForm(this, 'formRef')
  },
  mounted () {
    // this.init()
  },
  watch: {
    arr: {
      handler: function (n) {
        if (n) {
          console.log(n, 'nnnn')
          this.init()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化
    init () {
      this.$nextTick(() => {
        const arr = [0]
        console.log(this.arr, 'this.arr')
        if (this.arr.length !== 0) {
          for (let i = 1; i < this.arr.length; i++) {
            arr.push(i)
            this.id = this.id + 1
          }
        }

        this.keysList = arr
      })
    },
    // 移除某行
    removeRow (k) {
      if (this.keysList.length === 1) {
        // 如果存在可以移除所有行的情况，把条件改为this.keysList.length === 0即可
        return
      }
      this.keysList = this.keysList.filter((item) => item !== k)
    },
    // 新增一行
    addRow () {
      this.id = this.id + 1
      this.keysList = this.keysList.concat(this.id)
    },
    // 解析记录校验
    contentNameCheckChange1 (rules, val, callback) {
      // , validator: (rules, value, callback) => { this.contentNameCheckChange1(rules, value, callback) }
      callback = antdFormValidateCallback
      if (val.startsWith('10.') || val.startsWith('172.16.') || val.startsWith('192.168')) {
        notification.error({
          message: '域名语法格式不正确, 请填写一个域名, 如: cloud.intsig.com'
        })
      }
      return callback()
    }
  }
}
</script>

<style lang="less" scoped>
.dynamic-wrap {
  padding-top: 10px;
  background-color: white;
  overflow-y: scroll;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 7px;
  }
  &::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }
}
.minusRowBtn {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
  padding-right: 7px;
  padding-left: 7px;
  height: 29px;
  margin-left: 10px;
}
.addRowBtn {
  width: 70%;
  color: #1890ff;
  border-color: #91d5ff;
  margin: 0px 0px 20px 70px;
}
</style>
