<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 12px" :bordered="false" :title="`${temp.orderType}-申请内容`">
      <a-form-model
        ref="orderForm"
        :model="temp.content"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
      <a-divider orientation="left" v-if="temp.node === 2">域名解析信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="域名">{{ temp.content.name }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="解析类型">{{ temp.content.type }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="解析记录">{{ temp.content.resourceRecords }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="地理位置">{{ temp.content.geolocation }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="解析来源">{{ temp.content.source }}</a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="资源ID">{{ temp.content.id }}</a-form-model-item>
          </a-col>
        </a-row>
        <div v-if="temp.node === 2">
          <a-row :gutter="24">
            <a-col :span="18">
              <!-- <tx-button type="primary" @click="dnsWafGatewayCheck">Waf与网关配置检索</tx-button> -->
            </a-col>
            <a-col :span="6">
              <tx-button type="primary" @click="dnsDeleteCheck" style="margin-left: 10px">域名解析下线检查</tx-button>
              <tx-button type="danger" @click="dnsDelete" style="margin-left: 10px">域名解析注销</tx-button>
            </a-col>
          </a-row>
          <a-divider orientation="left" v-if="wafData.length > 0">Waf配置信息</a-divider>
          <a-row :gutter="24" v-if="wafData.length > 0">
            <a-col :span="1"></a-col>
            <a-col :span="20">
              <a-table
                ref="table1"
                size="default"
                rowKey="id"
                :pagination="pagination"
                :columns="Wafcolumns"
                :data-source="wafData"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex == 'action'">
                    <a-popconfirm title="确定删除waf配置?" @confirm="handleWafDel(record)">
                      <template #icon>
                        <a-icon type="question-circle-o" style="color: red" />
                      </template>
                      <tx-button type="danger" style="margin-left: 10px">删除</tx-button>
                    </a-popconfirm>
                  </template>
                </template>
              </a-table>
            </a-col>
          </a-row>
          <a-divider orientation="left" v-if="apisixData.length > 0">Apisix网关配置信息</a-divider>
          <a-row :gutter="24" v-if="apisixData.length > 0">
            <a-col :span="1"></a-col>
            <a-col :span="20">
              <a-table
                ref="table2"
                size="default"
                rowKey="id"
                :pagination="pagination"
                :columns="Apisixcolumns"
                :data-source="apisixData"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex == 'action'">
                    <a-popconfirm title="确定下线Apisix配置?" @confirm="handleApisixStatus(record)">
                      <template #icon>
                        <a-icon type="question-circle-o" style="color: red" />
                      </template>
                      <tx-button type="danger" style="margin-left: 10px">下线</tx-button>
                    </a-popconfirm>
                  </template>
                </template>
              </a-table>
            </a-col>
          </a-row>
        </div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item name="reason" label="申请理由">
              <a-textarea v-model:value="temp.content.reason" />
            </a-form-model-item>
          </a-col>
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item name="comment" label="回复/备注">
              <a-textarea v-model:value="temp.comment" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="紧急审批" v-if="immediatelyList.includes(localUser) && temp.node === 0">
              <a-switch v-model:checked="immediatelySwitchOn" @change="onChange" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="12">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0" :wrapper-col="{ span: 16, offset: 4 }">
          <tx-button type="primary" @click="createData">提交</tx-button>
          <tx-button style="margin-left: 10px">
            <router-link to="/domain/dns-list">取消</router-link>
          </tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 3 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="approveData" style="margin-left: 10px">下线完成</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal v-model:visible="dnsDeleteCheckVisible" title="DNS近期CK日志查询" :footer="null">
      <p>解析记录： {{ this.temp.content.name }}</p>
      <p>查询周期： 一{{ this.timePeriod }}</p>
      <p>查询结果： {{ this.count }} 条</p>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { checkDnsDelete, getDnsWafGateway, deleteWaf, deleteDns } from '@/api/domain/dns'
import { changeState } from '@/api/gateway/apiRoute'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { stringToObject } from '@/utils/helper'

const Wafcolumns = [
  { title: '供应商', dataIndex: 'supplier' },
  { title: 'From域名', dataIndex: 'domain' },
  { title: 'To域名', dataIndex: 'sourceDomain' },
  { title: '实例Id', dataIndex: 'instanceId' },
  { title: '操作', dataIndex: 'action', width: '120px', align: 'center', scopedSlots: { customRender: 'action' } },
]
const Apisixcolumns = [
  { title: '记录Id', dataIndex: 'id' },
  { title: '网关集群', dataIndex: 'env' },
  { title: '路由Id', dataIndex: 'routeId' },
  { title: '操作', dataIndex: 'action', width: '120px', align: 'center', scopedSlots: { customRender: 'action' } },
]
const pagination = {
  defaultPageSize: 1000,
  hideOnSinglePage: true,
}
export default {
  name: 'ServerDelete',
  data() {
    this.Wafcolumns = Wafcolumns
    this.Apisixcolumns = Apisixcolumns
    this.pagination = pagination
    return {
      localUser: store.getters.email,
      time_now: '',
      dnsDeleteCheckVisible: false,
      immediatelySwitchOn: false,
      count: 0,
      timePeriod: '月',
      node_status: 1,
      temp: {
        id: undefined,
        orderType: '域名注销',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        time_now: '',
        handler: '',
        handlerEmail: '',
        content: {
          id: '',
          name: '',
          resourceRecords: '',
          reason: '',
          immediately: 'slow',
        },
        timeline: [],
        comment: '',
      },
      immediatelyList: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      rules: {
        reason: [{ required: true, message: '请填写申请理由', trigger: 'blur' }],
      },
      modalFormPreServerDeleteVisible: false,
      preShutdownMsgs: [{}],
      wafData: [],
      apisixData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (this.$route.query.dnsId) {
        this.$route.query.dns = stringToObject(this.$route.query.dns)
        this.temp.content.id = this.$route.query.dns.id.toString()
        this.temp.content.name = this.$route.query.dns.name
        this.temp.content.resourceRecords = this.$route.query.dns.resourceRecords
        this.temp.content.type = this.$route.query.dns.type
        this.temp.content.geolocation = this.$route.query.dns.geolocation
        this.temp.content.source = this.$route.query.dns.source
      } else if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.node == 2) {
              this.dnsWafGatewayCheck()
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    dnsDeleteCheck() {
      checkDnsDelete({ dnsName: this.temp.content.name }).then(response => {
        this.dnsDeleteCheckVisible = true
        if (response.Data.count !== undefined) {
          this.count = response.Data.count
        }
        if (response.Data.timePeriod === 'week') {
          this.timePeriod = '周'
        }
      })
    },

    onChange(checked) {
      if (checked) {
        this.temp.content.immediately = 'run'
      } else {
        this.temp.content.immediately = 'slow'
      }
    },
    dnsWafGatewayCheck() {
      getDnsWafGateway({ dnsId: this.temp.content.id }).then(response => {
        if (response.Data.wafList != null) {
          this.wafData = [response.Data.wafList]
        }
        if (response.Data.apisixList != null) {
          this.apisixData = response.Data.apisixList
        }
      })
    },
    dnsDelete() {
      deleteDns(this.temp.content.id).then(response => {
        if (response.Data.message == 'ok') {
          this.$message.info('域名解析注销成功')
        }
      })
    },
    handleWafDel(record) {
      let data = {
        wafId: record.wafId,
        instanceId: record.instanceId,
        domain: record.domain,
        sourceDomain: record.sourceDomain,
        supplier: record.supplier,
      }
      deleteWaf(data).then(response => {
        if (response.Data.message == 'ok') {
          this.dnsWafGatewayCheck()
          this.$message.info('删除成功')
        }
      })
    },
    handleApisixStatus(record) {
      changeState({ id: record.id, state: '0', source: 'dns_delete_order' }).then(response => {
        if (response.Data.messageg == 'ok') {
          this.$message.info('下线成功')
          this.dnsWafGatewayCheck()
        }
      })
    },
    /**
     * 提交表单数据
     */
    createData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '创建成功',
                description: '工单创建成功',
              })
              this.$router.push({ path: '/workflow/dns-del', query: { id: response.Data.id } })
            }
          })
        }
      })
    },
    leaderApproveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    approveData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.node_status = 0
          this.temp.node = 3
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 3
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 3
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              this.node_status = 1
              this.temp = response.Data
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds
      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
  },
}
</script>

<style scoped></style>
