<template>
  <div>
    <a-form-model
      ref="orderForm"
      :model="temp.content"
      :rules="tempRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <div>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="域名" name="name">
              <template v-if="temp.node === 3">
                <a-input
                  v-model:value="temp.content.name"
                  style="width: 100%"
                  placeholder="格式: xxx.intsig.net(不带http或https)"
                  @change="contentNameChange"
                />
              </template>
              <template v-else>
                <a-input
                  v-model:value="temp.content.name"
                  style="width: 100%"
                  placeholder="格式: xxx.intsig.net(不带http或https)"
                />
              </template>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form :model="temp.content" ref="formRef">
              <dynamic-form
                ref="testForm"
                :title="`${PARTONE}`"
                :wrapHeight="360"
                :arr="temp.content.resourceRecords"
                :typeChoosetitle="temp.content.type"
                :labelName="'业务地址'"
              />
            </a-form>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="申请理由" name="reason">
              <a-input v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </div>
      <div>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="DNS配置" name="highlvlSetting">
              <a-radio-group v-model:value="temp.highlvlSetting" button-style="solid" @change="moreConfigVisControl">
                <a-radio-button value="no">默认</a-radio-button>
                <a-radio-button value="yes">自定义</a-radio-button>
              </a-radio-group>
              <a-tooltip>
                <template #title>DNS自定义功能开关</template>
                <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
              </a-tooltip>
            </a-form-model-item>
            <div v-if="temp.content.moreConfigVis">
              <a-form-model-item label="类型" name="type">
                <a-select
                  v-model:value="temp.content.type"
                  placeholder="请选择类型"
                  :options="dnsTypeList"
                  style="width: 180px"
                ></a-select>
                <a-tooltip>
                  <template #title>DNS解析类型，包括CNAME,A,AAAA,TXT,MX等等</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
              <a-form-model-item label="域名有效期" name="expiryDate">
                <a-radio-group v-model:value="temp.content.expiryDate" button-style="solid">
                  <a-radio-button :value="31536000">一年</a-radio-button>
                  <a-radio-button :value="63072000">二年</a-radio-button>
                  <a-radio-button :value="94608000">三年</a-radio-button>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="TTL" name="ttl">
                <a-radio-group v-model:value="temp.content.ttl" button-style="solid">
                  <a-radio-button :value="300">5分钟</a-radio-button>
                  <a-radio-button :value="600">10分钟</a-radio-button>
                  <a-radio-button :value="900">15分钟</a-radio-button>
                  <a-radio-button :value="3600">1小时</a-radio-button>
                  <a-radio-button :value="172800">2天</a-radio-button>
                </a-radio-group>

                <a-tooltip>
                  <template #title>DNS TTL缓存过期时间</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
              <a-form-model-item label="地理位置" name="geolocation">
                <a-select
                  v-model:value="temp.content.geolocation"
                  placeholder="选填，一般为空即可"
                  :options="geolocationOptions"
                  style="width: 200px"
                  :allowClear="true"
                ></a-select>

                <a-tooltip>
                  <template #title>DNS地区配置，即该配置只针对特定区域生效</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
            </div>
          </a-col>
        </a-row>
      </div>
      <div>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-model-item label="网关路由配置">
                <a-radio-group
                  v-model:value="temp.content.apiRouteCheck"
                  button-style="solid"
                  @change="gateWayVisControl"
                >
                  <a-radio-button value="no">不需要</a-radio-button>
                  <a-radio-button value="yes">需要</a-radio-button>
                </a-radio-group>
                <a-tooltip>
                  <template #title>
                    是否需要网关代理配置？
                    <br />
                    选择不需要时：域名直接解析到上一步所指向的IP地址
                    <br />
                    选择需要时：需要填写如下域名代理配置，一般用于将服务发布至公网或者内网，供其他服务调用
                  </template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
            </a-form-model>
          </a-col>
        </a-row>
        <div v-if="!temp.content.gateWayVis">
          <a-form-model :model="temp.content" :rules="tempRules" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <RouteForm
              ref="routeForms"
              :envGroups="envGroups"
              :hiddenItem="hiddenItem"
              :routeInfos="temp.content.route"
              @envChange="envChange"
            />
          </a-form-model>
        </div>
        <div v-if="temp.node > 0">
          <div>&nbsp;</div>
          <div>&nbsp;</div>
          <a-row :gutter="24">
            <a-col :span="24" v-if="temp.node >= 4">
              <a-form-model-item label="代理域名" name="proxyName">
                <a-input v-model:value="temp.content.proxyName" placeholder="选填，如果有代理,填写代理域名" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
      </div>
      <div>
        <a-form-model-item label="是否需要WAF" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-radio-group v-model:value="temp.content.waf" button-style="solid">
            <a-radio-button value="none">无</a-radio-button>
            <a-radio-button value="aliyun">国内-合合</a-radio-button>
            <a-radio-button value="aliyun-qxb">国内-启信宝</a-radio-button>
            <a-radio-button value="cloudflare">国外</a-radio-button>
          </a-radio-group>

          <a-tooltip>
            <template #title>是否需要WAF功能（基本不需要）</template>
            <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
          </a-tooltip>
        </a-form-model-item>
      </div>
      <div>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="其他负责人" name="otherPrincipal">
              <a-select
                placeholder="请输入邮箱选择"
                v-model:value="temp.content.otherPrincipal"
                style="width: 200px"
                :showSearch="true"
                :allowClear="true"
                @search="searchUserEmailMethod"
              >
                <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                  {{ item1 }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="费用负责人" name="">
              <a-input style="width: 200px" v-model:value="temp.content.costUser" disabled />
            </a-form-model-item>
            <a-form-model-item label="事业部/部门" name="organization">
              <a-cascader
                style="width: 200px"
                v-model:value="temp.content.organization"
                :options="organizationOptions"
                change-on-select
              />
            </a-form-model-item>
            <a-form-model-item label="所属项目" name="project">
              <a-select
                v-if="temp.content.org === 'CS' && temp.content.dep === '技术'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListCS" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === 'CC' && temp.content.dep === '后端'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListCC" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === 'DG' && temp.content.dep === '数据技术'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListQXB" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === '战略合作' && temp.content.dep === '创新项目组'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListZhaoPin" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === '战略合作' && temp.content.dep === '产研'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListOversea" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === 'SSG' && temp.content.dep === '项目研发'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListSSG" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === 'ACG' && temp.content.dep === '工程与技术'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListAiPlatform" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === 'ACG' && temp.content.dep === '智能计算'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListOcr" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-select
                v-else-if="temp.content.org === 'ACG' && temp.content.dep === '产品研发'"
                v-model:value="temp.content.project"
                placeholder="请选择"
                style="width: 350px"
              >
                <a-select-option v-for="item in projectListJuzijianzhi" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-input v-else v-model:value="temp.content.project" style="width: 200px" placeholder="请输入所属项目" />
              <a-tooltip>
                <template #title>所属项目组</template>
                <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
              </a-tooltip>
            </a-form-model-item>
            <a-form-model-item label="环境" name="env">
              <a-radio-group v-model:value="temp.content.env" button-style="solid">
                <a-radio-button value="online">生产环境</a-radio-button>
                <a-radio-button value="pre">预发布环境</a-radio-button>
                <a-radio-button value="test">测试环境</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item label="访问范围" name="scope">
              <a-radio-group v-model:value="temp.content.scope" button-style="solid">
                <a-radio-button :value="3">公司内部访问</a-radio-button>
                <a-radio-button :value="2">办公室访问</a-radio-button>
                <a-radio-button :value="4">机房访问</a-radio-button>
                <a-radio-button :value="1">公网访问</a-radio-button>
              </a-radio-group>
              <a-tooltip>
                <template #title>访问范围，即该配置只针对特定区域生效</template>
                <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
              </a-tooltip>
            </a-form-model-item>
            <div v-if="temp.node > 0">
              <a-form :model="temp.content">
                <dynamic-form
                  :title="`${PARTONE}`"
                  :wrapHeight="360"
                  :arr="temp.content.resourceRecords"
                  :typeChoosetitle="temp.content.type"
                />
                <a-row :gutter="24">
                  <a-col :span="24">
                    <a-form-model-item
                      label="反馈信息"
                      name="comment"
                      :label-col="{ span: 6 }"
                      :wrapper-col="{ span: 18 }"
                    >
                      <a-textarea v-model:value="temp.content.comment" :auto-size="{ minRows: 2, maxRows: 6 }" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-form-model>
  </div>
</template>
<script>
import store from '@/store'
import { notification } from 'ant-design-vue'
import { createOrder, getOrderInfo, approveOrder } from '@/api/workflow/order'
import { upstreamIdList, envDomainList, DomainCheck } from '@/api/domain/domain'
// import { geolocationOptionsApi } from '@/api/domain/dns'
import { getAssetListOrganization, getAssetListDepartment } from '@/api/asset'
import { getUserList } from '@/api/permission/user'
import DynamicForm from './DynamicForm.vue'
import DnsNodesForm from './DnsNodesForm.vue'
// import { groupNames } from '@/api/gateway/groupAndIp'
import DnsVarsForm from './DnsVarsForm.vue'
import RouteForm from '@/views/gateway/apiRoute/comps/handleRouteForm.vue'
const PARTONE = 'partOne'
const PARTVARS = 'partVars'
const PARTNODE = 'partNode'
export default {
  name: 'DnsModify',
  components: {
    DynamicForm,
    DnsNodesForm,
    DnsVarsForm,
    RouteForm,
  },
  props: {
    content: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      pageNode: '申请域名信息',
      pageNodeNum: 0,
      hasJsonFlag: true,
      envOptions: [],
      envdomainlist: [],
      userEmailList: [],
      dnsTypeList: [
        {
          label: 'A',
          value: 'A',
        },
        {
          label: 'CNAME',
          value: 'CNAME',
        },
        {
          label: 'AAAA',
          value: 'AAAA',
        },
        {
          label: 'TXT',
          value: 'TXT',
        },
      ],
      envdomainMap: {},
      OrgList: [],
      DepList: [],
      principalList: [],
      projectListCS: [
        'CS-C端-CS高级账户',
        'CS-C端-一次性费用',
        'CS-C端-其他',
        'CS-B端-CSB',
        'CS-B端-CS商城',
        'CS-B端-广告',
        'CS-B端-广告',
        'CS-B端-移动终端厂商',
        'CS-B端-渠道商',
      ],
      projectListCC: [
        'CC-C端-CC高级账户',
        'CC-C端-一次性费用',
        'CC-C端-其他',
        'CC-B端-CCB',
        'CC-B端-广告',
        'CC-B端-移动终端厂商',
        'CC-B端-渠道商',
        'CC-B端-扫名片技术服务费',
        'CC-B端-企业数字名片',
        'CC-C端-海外企业查询',
      ],
      projectListQXB: [
        '启信宝-C端-启信宝VIP',
        '启信宝-C端-其他',
        '启信宝-C端-一次性费用',
        '启信宝-C端-数据报告',
        '启信宝-C端-企业认证高级认证服务费',
        '启信宝-B端-标准版',
        '启信宝-B端-数据库',
        '启信宝-B端-SAAS',
        '启信宝-B端-营销云',
        '启信宝-B端-广告',
        '启信宝-B端-插件部署',
        '启信宝-B端-前端私有化',
        '启信宝-B端-商业报告',
        '启信宝-B端-数据开放平台充值',
        '启信宝-B端-企业认证服务',
        '启信宝-B端-数据API',
        '启信宝-B端-标准插件',
        '人民启信-C端-启信宝VIP',
        '人民启信-C端-一次性费用',
        '人民启信-C端-其他',
        '人民启信-C端-其他',
        '人民启信-C端-数据报告',
        '人民启信-C端-企业认证高级认证服务费',
        '人民启信-C端-CC高级账户',
        '人民启信-C端-CS高级账户',
      ],
      projectListZhaoPin: ['六度推-B端-平台收入', '六度推-B端-内推充值', '六度推-B端-服务收入'],
      projectListOversea: ['战略合作'],
      projectListSSG: [
        '荃英荟-B端-展会收入',
        '解决方案-知识图谱-构建工具',
        '解决方案-其他解决方案',
        '解决方案-企业大数据应用平台',
        '解决方案-企业大数据可视化插件',
        '解决方案-企业大数据行业定制报告',
        '解决方案-企业大数据行业可视化大屏',
        '解决方案-企业大数据建模咨询及建设',
        '解决方案-企业大数据建模咨询及建设',
        '解决方案-企业大数据标签制定及咨询',
        '解决方案-G端-区域经济运行监测平台',
        '解决方案-G端-数字招商平台',
        '解决方案-G端-政务产品可视化插件',
        '解决方案-G端-政务产品可视化插件',
        '解决方案-G端-企业服务平台',
        '解决方案-G端-定制咨询报告',
        '解决方案-G端-政务可视化大屏',
        '解决方案-G端-其他',
        '解决方案-G端-其他',
        '手机厂商授权-B端-国内',
        '手机厂商授权-B端-国外',
        '硬件销售-B端服务器',
        '硬件销售-B端扫描仪',
        '硬件销售-B端其它',
      ],
      projectListAiPlatform: [
        'Textin-C端-国内',
        'Textin-B端-国内',
        'Textin-通用文字识别（通用文字识别、通用表格识别、通用文档识别、印章识别）',
        'Textin-智能人机识别',
        'Textin-图像处理',
        'Textin-图像质量检测（图像质量检测、存在性判断）',
        'Textin-扫描笔文字识别SDK',
        'Textin-条码识别',
        'Textin-名片识别',
        'Textin-银行卡识别',
        'Textin-物流单据识别',
        'Textin-海外ID证件识别（港澳台身份证、海外身份证件等）',
        'Textin-常用证照识别（国内身份证、驾驶证识别、营业执照识别、税务登记证识别、户口本识别等）',
        'Textin-其他证照识别（除了常用证照以外的其他证照识别）',
        'Textin-智能文档分类',
        'Textin-财务票据识别',
        'Textin-医疗票据识别',
        'Textin-银行票据识别',
        'Textin-商场小票识别',
        'Textin-定制/其他识别（包含车辆VIN码、车牌号识别）',
        'Textin-发票验真',
        'Textin-人脸识别',
        'Textin-财报机器人',
        'Textin-合同机器人',
        'Textin-票据机器人扫描仪版',
        'Textin-AI管理平台私有化部署',
        'Textin-AI训练平台私有化部署',
        'TS-C端-TS高级账户',
        'TS-B端-广告',
      ],
      projectListOcr: [
        'OCR-C端',
        'OCR-B端-国内',
        'OCR-B端-国外',
        'OCR-B端-硬件',
        'OCR-B端-公有化部署（AI模块）包年计费',
        'OCR-B端-公有化部署（AI模块）按次计费',
        'OCR-B端-私有化部署（AI模块）',
        'OCR-B端-SDK部署（AI模块）',
        'OCR-B端-公有化部署（基础模块）包年计费',
        'OCR-B端-公有化部署（基础模块）按次计费',
        'OCR-B端-私有化部署（基础模块）',
        'OCR-B端-SDK部署（基础模块）',
      ],
      projectListJuzijianzhi: ['桔子兼职-B端-众包业务', '桔子兼职-B端-样本分成服务'],
      upstreamIdListOptions: [],
      tempRules: antdFormRulesFormat({
        // 'content.name': [{ required: true, message: '域名格式不对', pattern: /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/, trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请原因', trigger: 'blur' }],
        'content.name': [
          {
            required: true,
            message: '请选择输入域名',
            trigger: 'blur',
            validator: (rules, value, callback) => {
              this.contentNameCheckChange(rules, value, callback)
            },
          },
        ],
        'content.address': [{ required: true, message: '请填写业务地址', trigger: 'blur' }],
        'content.type': [{ required: true, message: '请选择类型', trigger: 'change' }],
        'content.scope': [{ required: true, message: '请选择访问范围', trigger: 'change' }],
        'content.ttl': [{ required: true, message: '请选择TTL', trigger: 'change' }],
        'content.business': [{ required: true, message: '请选择所属业务', trigger: 'change' }],
        'content.project': [{ required: true, message: '请选择所属项目', trigger: 'change' }],
        'content.env': [{ required: true, message: '请选择环境', trigger: 'change' }],
        'content.expiryDate': [{ required: true, message: '请选择有效期', trigger: 'change' }],
        // 以下未使用
        /*
        'content.route.hosts': [{ required: true, message: '请输入Host', trigger: 'blur' }],
        'content.route.uris': [{ required: true, message: '请输入Uri', trigger: 'blur' }],
        'content.route.remote_addrs': [{ required: true, message: '请选择访问范围', trigger: 'change' }],
        'content.route.upstream.name': [{ required: true, message: '请选择Upstream', trigger: 'change' }],
        'content.route.methods': [{ required: true, message: '请选择Methods方法', trigger: 'change' }],
        'content.route.priority': [{ required: true, message: '请输入优先级', trigger: 'blur' }],
        'content.route.expiry_date': [{ required: true, message: '请输入过期时间', trigger: 'blur' }],
        'content.route.enable_websocket': [{ required: true, message: '请选择WebSocket', trigger: 'change' }],
        'content.route.plugins_check': [{ required: true, message: '请选择插件类型', trigger: 'change' }],
        'content.route.health_check': [{ required: true, message: '请选择健康检查类型', trigger: 'change' }],
        */
        'content.hasCdn': [{ required: true, message: '请选择有无CDN配置', trigger: 'change' }],
        'content.needHttpsRedirect': [{ required: true, message: '请选择是否需要https强制跳转', trigger: 'change' }],
      }),
      requestWay: ['GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      remoteAddrOptions: [
        { value: 'none', label: '无限制', disabled: false },
        {
          value: '192.168.32.0/24',
          label: '云立方(192.168.32.0/24)',
          disabled: false,
        },
        { value: '1*******/16', label: '混合云(1*******/16)', disabled: false },
        {
          value: '**********/24',
          label: '内网(**********/24)',
          disabled: false,
        },
        {
          value: '***********/16',
          label: '内网(***********/16)',
          disabled: true,
        },
        { value: '10.0.0.0/8', label: '内网(10.0.0.0/8)', disabled: false },
      ],
      immediatelyList: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ],
      immediately: 'slow',
      hostOptions: [{ value: '', label: '' }],
      urisOptions: [{ value: '', label: '' }],
      geolocationOptions: [],
      principalNameToEmail: {},
      principalListMini: [],
      projectList: [],
      applicationList: [],
      organizationList: [],
      departmentList: [],
      serverOption: [],
      serverOptionMini: [],
      localUser: store.getters.email,
      node_status: 1,
      hiddenItem: ['env', 'principal','connectivity'],
      envGroups: [],
      temp: {
        id: undefined,
        orderType: '域名解析',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        rightDomain: '',
        time_now: '',
        content: {
          moreConfigVis: false,

          name: '',
          address: '',
          type: 'A',
          resourceRecords: [{ Value: '' }],
          ttl: 600,
          scope: 2,
          geolocation: '',
          org: '',
          dep: '',
          project: '',
          env: 'online',
          team: '',
          expiryDate: 31536000,
          waf: 'none',
          hasCdn: false,
          needHttpsRedirect: false,
          otherPrincipal: '',
          reason: '',
          comment: '',
          proxyName: '',
          apiRouteCheck: 'no',
          gateWayVis: true,
          route: {},
        },
        timeline: [],
        highlvlSetting: 'no',
      },
      securityGroupList: [],
      pre_project_name_list: [],
      preStatus: false,
      vpcStatus: false,
      tipsStatus: false,
      modalFormBindServerVisible: false,
      vpc_index: 3,
      instance_index: 0,
      scrollPage: 1,
      serverScrollPage: 1,
      valueData: '',
      serverValueData: '',
      treePageSize: 50,
      modalFormPreBindServerVisible: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      PARTONE,
      PARTNODE,
      PARTVARS,
      form: this.$form.createForm(this),
      // form1: this.$form.createForm(this, ),
      // form2: this.$form.createForm(this, )
    }
  },
  created() {
    this.temp = this.content
    // this.geolocationOptions = geolocationOptionsApi()
    // this.getBaseInfo()
    // this.getList()
    // groupNames()
    //   .then((res) => {
    //     this.envGroups = res.Data.clusters
    //   })
    //   .catch(() => {})
  },
  mounted() {
    this.$nextTick(() => {
      // this.$refs.routeForms.form = this.temp.content.route
      // this.$refs.routeForms.form.hosts = [this.temp.content.name]
      // const arr = this.temp.content.resourceRecords.map((item) => {
      //   return {
      //     ip: item.value,
      //     port: '',
      //     weight: ''
      //   }
      // })
      // this.$refs.routeForms.form.upstream.nodes = arr
    })
  },
  watch: {
    temp: {
      handler: function (val) {
        if (val && val.node === 4) {
          this.hiddenItem = ['principal']
        } else {
          this.hiddenItem = ['env', 'principal','connectivity']
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    reApply() {
      this.$router.push({
        path: '/workflow/dns-add',
      })
      this.temp = {
        ...this.temp,
        id: '',
        orderType: '域名解析',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        timeline: [],
        comment: '',
      }
    },
    envChange(envId) {
      this.temp.content.route.envId = envId
      if (envId) {
        this.envGroups.forEach(item => {
          if (item.envId === envId) {
            this.temp.content.proxyName = item.domain
          }
        })
      } else {
        this.temp.content.proxyName = ''
      }
    },
    getList() {
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            if (this.temp.node > 0) {
              this.temp.highlvlSetting = 'yes'
              this.temp.content.moreConfigVis = true
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      }
    },
    getBaseInfo() {
      getAssetListOrganization().then(res => {
        for (var i = 0, len = res.Data.organization.length; i < len; i++) {
          var organization = {}
          organization.value = res.Data.organization[i]
          organization.label = res.Data.organization[i]
          this.OrgList.push(organization)
        }
      })
      getAssetListDepartment().then(res => {
        for (var i = 0, len = res.Data.department.length; i < len; i++) {
          var department = {}
          department.value = res.Data.department[i]
          department.label = res.Data.department[i]
          this.DepList.push(department)
        }
      })
      upstreamIdList().then(response => {
        this.upstreamIdListOptions = response.Data.UpstreamId
      })
      envDomainList().then(response => {
        this.envOptions = response.Data.clusters
        this.envdomainMap = JSON.parse(JSON.stringify(this.envOptions))
        for (let i = 0; i < this.envdomainMap.length; ++i) {
          delete this.envdomainMap[i].env
        }
      })
    },
    selectOneRouteEnv(val) {
      for (let i = 0; i < this.envdomainMap.length; i++) {
        if (this.envdomainMap[i].envId === val) {
          this.temp.content.proxyName = this.envdomainMap[i].domain
        }
      }
    },
    contentNameChange() {
      if (this.temp.content.route.hosts.length === 0) {
        this.temp.content.route.hosts.push(this.temp.content.name)
      }
      if (this.temp.content.route.hosts.length === 1) {
        this.temp.content.route.hosts[0] = this.temp.content.name
      }
    },
    listFilter(value) {
      if (!value) return []
      var value1
      value1 = value.split('|')
      var res = []
      for (var j = 0; j < value1.length; j++) {
        res[j] = { value: value1[j] }
      }
      return res
    },
    nextStep(number) {
      if (number - 1 < this.pageNodeNum) {
        if (number === '1') {
          this.pageNode = '申请域名信息'
          this.pageNodeNum = 0
        } else if (number === '2') {
          this.pageNode = 'DNS配置信息'
          this.pageNodeNum = 1
        } else if (number === '3') {
          this.pageNode = '网关路由配置信息'
          this.pageNodeNum = 2
        } else if (number === '4') {
          this.pageNode = '申请WAF信息'
          this.pageNodeNum = 3
        } else {
          this.pageNode = '申请人/项目信息'
          this.pageNodeNum = 4
          // 保存数据
          this.inputDataSave()
          if (this.$refs.routeForm) {
            this.temp.content.route = this.$refs.routeForm.form
          }
        }
      } else {
        if (number == 2) {
          this.$refs.testForm?.validate()
        }
        let hasError = noc.promiseArray(this.temp.content.resourceRecords).find(item => item.hasError)
        antdFormValidate(this.$refs.orderForm, valid => {
          if (hasError) {
            return
          }
          console.log(valid)
          return
          if (valid) {
            if (number === '1') {
              this.pageNode = '申请域名信息'
              this.pageNodeNum = 0
            } else if (number === '2') {
              this.nextDnsSave()
              this.pageNode = 'DNS配置信息'
              this.pageNodeNum = 1
            } else if (number === '3') {
              this.pageNode = '网关路由配置信息'
              this.pageNodeNum = 2
            } else if (number === '4') {
              this.pageNode = '申请WAF信息'
              this.pageNodeNum = 3
            } else {
              this.pageNode = '申请人/项目信息'
              this.pageNodeNum = 4
              // 保存数据
              this.inputDataSave()
              if (this.$refs.routeForm) {
                this.temp.content.route = this.$refs.routeForm.form
              }
            }
          } else {
            return false
          }
        })
      }
    },
    inputDataSave() {
      this.nextDnsSave()
      // this.nextNodeSave()
      // this.nextVarsSave()
    },
    nextDnsSave() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          const partOneArr = []

          if (values[`${PARTONE}value`]) {
            values[`${PARTONE}value`].forEach((item, index) => {
              const obj = {
                value: item,
              }
              partOneArr.push(obj)
            })
          }
          this.temp.content.resourceRecords = partOneArr
        }
      })
    },
    /*
    nextNodeSave () {
      const {
        form1: { validateFields }
      } = this
      validateFields((errors, values) => {
        if (!errors) {
          const partOneArr = []
          values[`${PARTNODE}ip`].forEach((item, index) => {
            const obj = {
              ip: item,
              port: values[`${PARTNODE}port`][index],
              weight: values[`${PARTNODE}weight`][index]
            }
            partOneArr.push(obj)
          })
          this.temp.content.route.upstream.nodes = partOneArr
        }
      })
    },
    */
    /*
    nextVarsSave () {
      const {
        form2: { validateFields }
      } = this
      validateFields((errors, values) => {
        if (!errors) {
          const partOneArr = []
          values[`${PARTVARS}var_name`].forEach((item, index) => {
            const obj = {
              var_name: item,
              var_operator: values[`${PARTVARS}var_operator`][index],
              var_value: values[`${PARTVARS}var_value`][index]
            }
            partOneArr.push(obj)
          })
          this.temp.content.route.vars = partOneArr
        }
      })
    },
    */
    contentNameCheckChange(rules, val, callback) {
      callback = antdFormValidateCallback
      if (val) {
        const zReg =
          /^((\*\.)?[a-zA-Z0-9_]+([-.]{1}[a-zA-Z0-_9]+)*\.[a-z_A-Z]{2,}|[a-zA-Z0-9_]+([-.]{1}[_a-zA-Z0-9]+)*\.[a-zA-Z_]{2,}\.[a-zA-Z_]{2,})(:[0-9]{1,5})?(.*)?$/gim
        if (!zReg.test(val)) {
          this.temp.rightDomain = false
          notification.error({
            message: '域名语法格式不正确，请填写一个域名，如: cloud.intsig.com',
          })
          return callback()
        }
        return callback()
      }
      var postData = { domainName: val, type: this.temp.content.type, geolocation: this.temp.content.geolocation }
      DomainCheck(postData).then(response => {
        if (response.Data.message === 'ok') {
          this.temp.rightDomain = true
          if (this.temp.content.hosts && this.temp.content.hosts.length === 0) {
            this.temp.content.hosts.push(val)
          }
          if (this.temp.content.hosts && this.temp.content.hosts.length === 1) {
            this.temp.content.hosts[0] = val
          }
        } else {
          this.temp.rightDomain = false
          notification.error({
            message: '域名不可用' + response.Data.message,
          })
        }
      })
      return callback()
    },
    moreConfigVisControl(e) {
      if (e.target.value === 'yes') {
        this.temp.content.moreConfigVis = true
      } else {
        this.temp.content.moreConfigVis = false
      }
    },
    gateWayVisControl(e) {
      // this.nextDnsSave()
      if (e.target.value === 'yes') {
        this.$nextTick(() => {
          this.$refs.routeForm.form.hosts = [this.temp.content.name]
          const arr = this.temp.content.resourceRecords.map(item => {
            return {
              ip: item.value,
              port: '',
              weight: '',
            }
          })
          this.$refs.routeForm.form.upstream.nodes = arr
        })
        this.temp.content.gateWayVis = false
      } else {
        this.temp.content.gateWayVis = true
      }
    },
    getUpstreamIdListInfo() {
      upstreamIdList().then(response => {
        this.upstreamIdListOptions = response.Data.UpstreamId
      })
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        var arry = response.Data.data
        for (var i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    onChange(checked) {
      if (checked) {
        this.immediately = 'run'
      } else {
        this.immediately = 'slow'
      }
    },
    createData() {
      this.inputDataSave()
      if (!this.temp.rightDomain) {
        notification.error({
          message: '请提交正确的域名',
        })
        return
      }
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.content.immediately = this.immediately
          createOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '创建失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              if (response.Code === 400) {
                const msg = response.data.msg
                notification.error({
                  message: '创建失败',
                  description: msg,
                })
              } else if (response.Code === 200) {
                response.Data.content = JSON.parse(response.Data.content.data)
                this.temp = response.Data
                this.node_status = 1
                notification.success({
                  message: '创建成功',
                  description: '工单创建成功',
                })
                this.$router.push({ path: '/workflow/dns-add', query: { id: response.Data.id } })
              }
            }
          })
        }
      })
    },
    refreshZoneData(val) {
      this.$forceUpdate()
      this.instance_index = (this.instanceTypeList || []).findIndex(item => item.zone_id === val)
      this.temp.content.approval_data.instance_type = ''
    },
    refreshData(val) {
      this.$forceUpdate()
      this.vpc_index = (this.vpc_info || []).findIndex(item => item.vpc_id === val)
      this.temp.content.approval_data.default_security_group_id = ''
      this.temp.content.approval_data.default_v_switch_id = ''
    },
    leaderApproveData() {
      this.inputDataSave()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.node_status = 0
          this.temp.node = 2
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '上级领导审批成功',
              })
            }
          })
        }
      })
    },
    safeApproveData() {
      this.inputDataSave()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 3
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.Code,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    auditApproveData() {
      this.inputDataSave()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 4
          this.temp.status = 1
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    managementApproveData() {
      this.inputDataSave()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 10
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code !== 200) {
              notification.error({
                message: '审批执行报错',
                description: response.data.msg,
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批成功',
                description: '管理员审批成功',
              })
            }
          })
        }
      })
    },
    rejectData() {
      this.inputDataSave()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.temp.node = 5
          this.temp.status = 20
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '审批失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else if (response.Code === 200) {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '审批完成',
                description: '该审批已被拒绝',
              })
            }
          })
        }
      })
    },
    revokeData() {
      this.inputDataSave()
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 5
          this.temp.status = 30
          this.temp.handler = store.getters.name
          this.temp.handlerEmail = store.getters.email
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          approveOrder(this.temp).then(response => {
            if (response === undefined) {
              notification.error({
                message: '撤回失败',
                description: '后端接口错误，请联系运维开发排查~',
              })
            } else {
              response.Data.content = JSON.parse(response.Data.content.data)
              this.temp = response.Data
              this.node_status = 1
              notification.success({
                message: '撤回成功',
                description: '该工单被申请人撤回',
              })
            }
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    healthCheckActiveChange(checked) {
      if (checked === 'false') {
        this.temp.content.upstream.checks.active.https_verify_certificate = false
      } else {
        this.temp.content.upstream.checks.active.https_verify_certificate = true
      }
    },
    onJsonChange(value) {
      this.onJsonSave(value)
    },
    onJsonSave(value) {
      this.temp.plugins.other = value
      this.hasJsonFlag = true
    },
    onError(value) {
      this.hasJsonFlag = false
    },
  },
}
</script>

<style>
.ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 20px;
  vertical-align: top;
}
.my-dns-divider {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 2px;
  margin: 8px 0;
}
</style>
