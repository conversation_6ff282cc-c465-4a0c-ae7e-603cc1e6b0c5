<template>
  <page-header-wrapper>
    <template #content>
      <tx-button icon="solution" size="small">
        <a href="https://doc.intsig.net/pages/viewpage.action?pageId=483295317" style="text-decoration: none">
          流程文档
        </a>
      </tx-button>
      域名解析新增

      <span style="color: red">参数配置信息等请联系邓斌询问</span>
    </template>
    <a-card :bordered="false">
      <a-steps :current="temp.node">
        <a-step title="提交工单" />
        <a-step title="上级领导审批" />
        <a-step title="安全审批" />
        <a-step title="管理员审批" />
        <a-step title="完成" />
      </a-steps>
    </a-card>
    <a-card style="margin-top: 24px" :bordered="false" :title="`${temp.orderType}-申请内容 `">
      <a-row :gutter="24" v-if="temp.node == 0">
        <a-col :span="4"></a-col>
        <a-col :span="16">
          <a-steps :current="pageNodeNum" disabled="true" size="small" labelPlacement="vertical">
            <a-step title="申请域名信息" />
            <a-step title="DNS配置信息" />
            <a-step title="网关路由配置信息" />
            <a-step title="申请WAF信息" />
            <a-step title="申请人/项目信息" />
          </a-steps>
        </a-col>
      </a-row>
      <a-form-model
        style="margin-top: 16px"
        ref="orderForm"
        :model="temp.content"
        :rules="tempRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="24" v-if="temp.node > 0 || pageNode == '申请人/项目信息'">
          <a-col :span="24">
            <a-form-model-item label="申请人">{{ temp.founder }}</a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="temp.node > 0" :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="处理人">{{ temp.handler }}</a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态">
              <a-tag v-if="temp.status === 0" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 1" color="orange">待审核</a-tag>
              <a-tag v-else-if="temp.status === 2" color="orange">处理中</a-tag>
              <a-tag v-else-if="temp.status === 10" color="green">审批通过</a-tag>
              <a-tag v-else-if="temp.status === 20" color="red">审批被拒绝</a-tag>
              <a-tag v-else-if="temp.status === 30" color="red">申请人撤回</a-tag>
              <a-tag v-else>未知</a-tag>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div v-if="pageNode == '申请域名信息' || temp.node > 0">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model-item label="解析类型">
                <a-radio-group v-model:value="temp.content.cornDns" button-style="solid">
                  <a-radio-button value="">公有解析</a-radio-button>
                  <a-radio-button value="office_etcd">私有(办公室)</a-radio-button>
                  <a-radio-button value="aliyun-qxb-private">私有(启信宝)</a-radio-button>
                  <!-- <a-radio-button value="bigdata_etcd">私有(大数据)</a-radio-button> -->
                </a-radio-group>
                <a-tooltip>
                  <template #title>
                    <!-- prompt text -->
                  </template>
                  <small>
                    <br />
                    <span style="font-weight: bold">公有解析：</span>
                    可在公网解析(解析功能托管于aliyun、aws),
                    <span style="color: red">默认选择公有解析</span>
                    ;
                    <br style="margin-bottom: 1px" />
                    <span style="font-weight: bold">私有解析——办公室：</span>
                    办公室是指仅使用办公室DNS(************以及************)可以解析。
                    <br style="margin-bottom: 1px" />
                    <span style="font-weight: bold">私有解析-阿里云(启信宝):</span>
                    提供阿里云启信宝机房内部的解析。
                    <br />
                    <span style="font-weight: bold">注意1：</span>
                    同一个域名私有解析与公有解析都可以添加(分多次提交)不冲突。
                    <br />
                    <span style="font-weight: bold">注意2：</span>
                    提交泛域名解析通常会被拒绝，如果特别需要请说明详情原因。
                    <br />
                    <span style="font-weight: bold">注意3：</span>
                    测试域名的网关配置, 无IP白名单限制不能开放到公网。
                  </small>
                </a-tooltip>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model-item label="费用负责人" name="costUser" v-if="temp.node === 0">
                <a-select
                  placeholder="请输入邮箱选择"
                  v-model:value="temp.content.costUser"
                  style="width: 95%"
                  :showSearch="true"
                  @search="searchUserEmailMethod"
                  @change="costUserChange"
                >
                  <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                    {{ item1 }}
                  </a-select-option>
                </a-select>
                <a-tooltip>
                  <template #title>费用负责人与事业部、部门相关联</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
              <a-form-model-item class="mb-0" label="域名" name="name">
                <template v-if="temp.node === 1">
                  <a-input
                    v-model:value="temp.content.name"
                    style="width: 95%"
                    placeholder="格式: xxx.intsig.net(不带http或https)"
                    @change="contentNameChange"
                  />
                </template>
                <template v-else>
                  <a-input
                    v-model:value="temp.content.name"
                    style="width: 95%"
                    placeholder="格式: xxx.intsig.net(不带http或https)"
                    @change="contentNameChange"
                  />
                </template>
              </a-form-model-item>
              <br />
              <a-form-model-item label="类型" name="type">
                <a-select
                  v-model:value="temp.content.type"
                  placeholder="请选择类型"
                  :options="dnsTypeList"
                  style="width: 95%"
                  @change="getDnsThirdServeManulCheck"
                ></a-select>
                <a-tooltip>
                  <template #title>DNS解析类型，包括CNAME,A,AAAA,TXT,MX等等</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24" v-if="temp.node === 0 || (temp.node > 0 && temp.content.gateWayVis)">
            <a-col :span="24">
              <a-form :model="temp.content" ref="formRef">
                <dynamic-form
                  ref="testForm"
                  :title="`${PARTONE}`"
                  :wrapHeight="360"
                  :arr="temp.content.resourceRecords"
                  :typeChoosetitle="temp.content.type"
                  :labelName="'业务地址'"
                />
              </a-form>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model-item label="申请理由" name="reason">
                <a-input v-model:value="temp.content.reason" placeholder="作用用途，信息补充" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24" v-if="temp.node == 0">
            <a-col :span="24" class="bottonAlign">
              <tx-button>
                <router-link to="/domain/dns-list">取消</router-link>
              </tx-button>
              <tx-button style="margin-left: 8px" type="primary" @click="nextStep('2')">下一步</tx-button>
              <tx-button style="margin-left: 8px" type="primary" @click="nextStep('5')">简易模式</tx-button>
            </a-col>
            <!-- <a-col :span="2">

            </a-col>
            <a-col style="margin-left:8px" :span="2">
            </a-col>
            <a-col style="margin-left:8px" :span="2">
            </a-col> -->
          </a-row>
        </div>
        <div v-if="pageNode == 'DNS配置信息' || temp.node > 0">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model-item label="DNS配置" name="highlvlSetting">
                <a-radio-group v-model:value="temp.highlvlSetting" button-style="solid" @change="moreConfigVisControl">
                  <a-radio-button value="no">默认</a-radio-button>
                  <a-radio-button value="yes">自定义</a-radio-button>
                </a-radio-group>
                <a-tooltip>
                  <template #title>DNS自定义功能开关</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
              <div v-if="temp.content.moreConfigVis">
                <a-form-model-item label="域名有效期" name="expiryDate">
                  <a-radio-group v-model:value="temp.content.expiryDate" button-style="solid">
                    <a-radio-button :value="31536000">一年</a-radio-button>
                    <a-radio-button :value="63072000">二年</a-radio-button>
                    <a-radio-button :value="94608000">三年</a-radio-button>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="TTL" name="ttl">
                  <a-radio-group v-model:value="temp.content.ttl" button-style="solid">
                    <a-radio-button :value="300">5分钟</a-radio-button>
                    <a-radio-button :value="600">10分钟</a-radio-button>
                    <a-radio-button :value="900">15分钟</a-radio-button>
                    <a-radio-button :value="3600">1小时</a-radio-button>
                    <a-radio-button :value="172800">2天</a-radio-button>
                  </a-radio-group>
                  <a-tooltip>
                    <template #title>DNS TTL缓存过期时间</template>
                    <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                  </a-tooltip>
                </a-form-model-item>
                <a-form-model-item label="地理位置" name="geolocation">
                  <a-select
                    show-search
                    v-model:value="temp.content.geolocation"
                    placeholder="选填，一般为空即可"
                    :options="geolocationOptions"
                    style="width: 200px"
                    :allowClear="true"
                    :filter-option="filterOption"
                  ></a-select>

                  <a-tooltip>
                    <template #title>DNS地区配置，即该配置只针对特定区域生效</template>
                    <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                  </a-tooltip>
                </a-form-model-item>
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24" v-if="temp.node == 0">
            <a-col :span="10"></a-col>
            <a-col :span="2">
              <tx-button type="danger" @click="nextStep('1')">上一步</tx-button>
            </a-col>
            <a-col :span="2">
              <tx-button type="primary" @click="nextStep('3')" style="margin-left: 10px">下一步</tx-button>
            </a-col>
          </a-row>
        </div>
        <div v-if="pageNode == '网关路由配置信息' || temp.node > 0">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                <a-form-model-item label="网关路由配置">
                  <a-radio-group
                    v-model:value="temp.content.apiRouteCheck"
                    button-style="solid"
                    @change="gateWayVisControl"
                  >
                    <a-radio-button value="no">不需要</a-radio-button>
                    <a-radio-button value="yes">需要</a-radio-button>
                  </a-radio-group>
                  <a-tooltip>
                    <template #title>
                      是否需要网关代理配置？
                      <br />
                      选择不需要时：域名直接解析到上一步所指向的IP地址
                      <br />
                      选择需要时：需要填写如下域名代理配置，一般用于将服务发布至公网或者内网，供其他服务调用
                    </template>
                    <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                  </a-tooltip>
                </a-form-model-item>
              </a-form-model>
            </a-col>
          </a-row>
          <div v-if="!temp.content.gateWayVis">
            <div class="routeFormBox">
              <RouteForm
                class="routeForm"
                ref="routeForm"
                :envGroups="envGroups"
                :hiddenItem="hiddenItem"
                :routeInfos="temp.content.route"
                @envChange="envChange"
              />
            </div>
          </div>
          <a-row :gutter="24" v-if="temp.node == 0">
            <a-col :span="10"></a-col>
            <a-col :span="2">
              <tx-button type="danger" @click="nextStep('2')">上一步</tx-button>
            </a-col>
            <a-col :span="2">
              <tx-button type="primary" @click="nextStep('4')" style="margin-left: 10px">下一步</tx-button>
            </a-col>
          </a-row>
        </div>
        <div v-if="pageNode == '申请WAF信息' || temp.node > 0">
          <a-form-model-item label="是否需要WAF" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-radio-group v-model:value="temp.content.waf" button-style="solid">
              <a-radio-button value="none">无</a-radio-button>
              <a-radio-button value="aliyun">国内-合合</a-radio-button>
              <a-radio-button value="aliyun-qxb">国内-启信宝</a-radio-button>
              <a-radio-button value="cloudflare">国外</a-radio-button>
            </a-radio-group>

            <a-tooltip>
              <template #title>是否需要WAF功能（基本不需要）</template>
              <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
            </a-tooltip>
          </a-form-model-item>
          <a-row :gutter="24" v-if="temp.node == 0">
            <a-col :span="10"></a-col>
            <a-col :span="2">
              <tx-button type="danger" @click="nextStep('3')">上一步</tx-button>
            </a-col>
            <a-col :span="2">
              <tx-button type="primary" @click="nextStep('5')" style="margin-left: 10px">下一步</tx-button>
            </a-col>
          </a-row>
        </div>
        <div v-if="pageNode == '申请人/项目信息' || temp.node > 0">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model-item label="其他负责人" name="otherPrincipal">
                <a-select
                  placeholder="请输入邮箱选择"
                  v-model:value="temp.content.otherPrincipal"
                  style="width: 200px"
                  :showSearch="true"
                  :allowClear="true"
                  @search="searchUserEmailMethod"
                >
                  <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                    {{ item1 }}
                  </a-select-option>
                </a-select>
                <a-tooltip>
                  <template #title>其他负责人邮箱</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
              <a-form-model-item label="费用负责人" name="">
                <a-input style="width: 200px" v-model:value="temp.content.costUser" disabled />
              </a-form-model-item>
              <a-form-model-item label="事业部/部门" name="organization">
                <a-cascader
                  style="width: 200px"
                  v-model:value="temp.content.organization"
                  :options="organizationOptions"
                  change-on-select
                />
              </a-form-model-item>
              <a-form-model-item label="所属项目" name="project">
                <a-input v-model:value="temp.content.project" style="width: 200px" placeholder="请输入所属项目" />
              </a-form-model-item>
              <a-form-model-item label="环境" name="env">
                <a-radio-group v-model:value="temp.content.env" button-style="solid">
                  <a-radio-button value="online">生产环境</a-radio-button>
                  <a-radio-button value="pre">预发布环境</a-radio-button>
                  <a-radio-button value="test">测试环境</a-radio-button>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="访问范围" name="scope">
                <a-radio-group v-model:value="temp.content.scope" button-style="solid">
                  <a-radio-button :value="3">办公室+机房访问</a-radio-button>
                  <a-radio-button :value="2">办公室访问</a-radio-button>
                  <a-radio-button :value="4">机房访问</a-radio-button>
                  <a-radio-button :value="1">公网访问</a-radio-button>
                </a-radio-group>
                <a-tooltip>
                  <template #title>访问范围，即该配置只针对特定区域生效</template>
                  <a-icon style="margin-left: 8px; font-size: 20px" type="question-circle" />
                </a-tooltip>
              </a-form-model-item>
              <!-- <div v-if="temp.node > 0 && temp.content.gateWayVis">
                <a-form :model="form">
                  <dynamic-form
                    :title="`${PARTONE}`"
                    :wrapHeight="360"
                    :arr="temp.content.resourceRecords"
                    :typeChoosetitle="temp.content.type"
                  />
                </a-form>
              </div> -->
              <div v-if="temp.node > 0">
                <div>&nbsp;</div>
                <div>&nbsp;</div>
                审批
                <a-row :gutter="24">
                  <a-col :span="24" v-if="temp.node >= 3">
                    <a-form-model-item label="代理域名" name="proxyName">
                      <a-input
                        style="width: 50%"
                        v-model:value="temp.content.proxyName"
                        placeholder="选填，如果有代理,填写代理域名"
                      />
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="24">
                    <a-form-model-item
                      label="反馈信息"
                      name="comment"
                      :label-col="{ span: 6 }"
                      :wrapper-col="{ span: 18 }"
                    >
                      <a-textarea
                        style="width: 50%"
                        v-model:value="temp.content.comment"
                        :auto-size="{ minRows: 2, maxRows: 6 }"
                      />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
              <a-row :gutter="24">
                <a-col :span="24">
                  <a-form-model-item label="紧急审批" v-if="immediatelyList.includes(localUser) && temp.node === 0">
                    <a-switch v-model:checked="immediatelySwitchOn" @change="onChange" />
                  </a-form-model-item>
                    <a-form-model-item label="紧急审批转让负责人" v-if="immediatelyList.includes(localUser) && immediatelySwitchOn">
                    <a-select
                      placeholder="请输入邮箱选择"
                      v-model:value="temp.content.immediateUser"
                      style="width: 45%"
                      :showSearch="true"
                      @search="searchUserEmailMethod"
                    >
                      <a-select-option v-for="item1 in userEmailList" :key="item1" :value="item1">
                        {{ item1 }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
        <a-row :gutter="24">
          <a-col v-if="temp.node > 0" :span="24">
            <a-form-model-item label="审批节点">
              <a-timeline>
                <a-timeline-item v-for="(activity, index) in temp.timeline" :key="index">
                  {{ activity }}
                </a-timeline-item>
              </a-timeline>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item v-if="temp.node === 0 && pageNode === '申请人/项目信息'">
          <p>&nbsp;</p>
          <a-row :gutter="24">
            <a-col :span="15">&nbsp;</a-col>
            <a-col :span="4">
              <tx-button type="danger" @click="nextStep('4')">上一步</tx-button>
            </a-col>
            <a-col :span="4">
              <tx-button type="primary" @click="createData" style="margin-left: 10px; width: 150%">提交</tx-button>
            </a-col>
          </a-row>
        </a-form-model-item>

        <a-form-model-item
          v-else-if="
            temp.founderEmail.includes(localUser) &&
            !temp.handlerEmail.includes(localUser) &&
            temp.node !== 0 &&
            temp.node !== 4 &&
            node_status === 1
          "
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="danger" icon="rollback" @click="revokeData">撤回</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 1 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="leaderApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 2 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="safeApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <!-- <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="auditApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item> -->
        <a-form-model-item
          v-else-if="temp.handlerEmail.includes(localUser) && temp.node === 3 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="managementApproveData">同意</tx-button>
          <tx-button type="danger" @click="rejectData" style="margin-left: 10px">拒绝</tx-button>
        </a-form-model-item>
        <!--   -->
        <a-form-model-item
          v-else-if="temp.founderEmail.includes(localUser) && temp.node === 4 && node_status === 1"
          :wrapper-col="{ span: 16, offset: 4 }"
        >
          <tx-button type="primary" @click="reApply">重新申请</tx-button>
        </a-form-model-item>
      </a-form-model>
    </a-card>
    <a-modal title="域名信息确认" :visible="moadlVisable" :width="'70%'" @cancel="handleCancel" @ok="handleOk">
      <template #footer>
        <tx-button @click="handleCancel">取消</tx-button>
        <tx-button type="primary" @click="handleOk">确认提交</tx-button>
      </template>
      <DnsDialog :content="temp" />
      <!-- <p>{{ ModalText }}</p> -->
    </a-modal>
  </page-header-wrapper>
</template>
<script>
import { filterLabelValue } from '@aim/helper'
import DnsDialog from './DnsDialogContent.vue'
import store from '@/store'
import { notification } from 'ant-design-vue'
import { approveOrder, createOrder, getOrderInfo } from '@/api/workflow/order'
import { DomainCheck, envDomainList, upstreamIdList } from '@/api/domain/domain'
import { geolocationOptionsApi, thirdServerCheck } from '@/api/domain/dns'
import { GetUserOrgAllInfo, getAssetListDepartment, getAssetListOrganization } from '@/api/asset'
import { getUserList } from '@/api/permission/user'
import DynamicForm from './DynamicForm.vue'
// import DnsNodesForm from './DnsNodesForm.vue'
import { groupNames } from '@/api/gateway/groupAndIp'
// import DnsVarsForm from './DnsVarsForm.vue'
import RouteForm from '@/views/gateway/apiRoute/comps/handleRouteForm.vue'
const PARTONE = 'partOne'
const PARTVARS = 'partVars'
const PARTNODE = 'partNode'
export default {
  name: 'DnsModify',
  components: {
    DynamicForm,
    // DnsNodesForm,
    // DnsVarsForm,
    RouteForm,
    DnsDialog,
  },
  data() {
    return {
      immediatelySwitchOn: false,
      moadlVisable: false,
      pageNode: '申请域名信息',
      pageNodeNum: 0,

      hasJsonFlag: true,
      envOptions: [],
      envdomainlist: [],
      userEmailList: [],
      dnsTypeList: [
        {
          label: 'A',
          value: 'A',
        },
        {
          label: 'CNAME',
          value: 'CNAME',
        },
        {
          label: 'AAAA',
          value: 'AAAA',
        },
        {
          label: 'TXT',
          value: 'TXT',
        },
        {
          label: 'MX',
          value: 'MX',
        },
      ],
      envdomainMap: {},
      OrgList: [],
      DepList: [],
      principalList: [],
      upstreamIdListOptions: [],
      tempRules: antdFormRulesFormat({
        // 'content.name': [{ required: true, message: '域名格式不对', pattern: /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/, trigger: 'blur' }],
        'content.reason': [{ required: true, message: '请填写申请原因', trigger: 'blur' }],
        'content.costUser': [{ required: true, message: '请选择费用', trigger: 'blur' }],
        'content.name': [
          {
            trigger: 'blur',
            validator: (rule, value, callback) => {
              return this.contentNameCheckChange(rule, value, callback)
            },
          },
        ],
        'content.type': [{ required: true, message: '请选择类型', trigger: 'change' }],
        'content.scope': [{ required: true, message: '请选择访问范围', trigger: 'change' }],
        'content.org': [{ required: true, message: '请选择事业部', trigger: 'change' }],
        'content.dep': [{ required: true, message: '请选择组织', trigger: 'change' }],
        'content.ttl': [{ required: true, message: '请选择TTL', trigger: 'change' }],
        'content.business': [{ required: true, message: '请选择所属业务', trigger: 'change' }],
        'content.project': [{ required: true, message: '请选择所属项目', trigger: 'change' }],
        'content.env': [{ required: true, message: '请选择环境', trigger: 'change' }],
        'content.expiryDate': [{ required: true, message: '请选择有效期', trigger: 'change' }],
        // 以下未使用
        /*
        'content.route.hosts': [{ required: true, message: '请输入Host', trigger: 'blur' }],
        'content.route.uris': [{ required: true, message: '请输入Uri', trigger: 'blur' }],
        'content.route.remote_addrs': [{ required: true, message: '请选择访问范围', trigger: 'change' }],
        'content.route.upstream.name': [{ required: true, message: '请选择Upstream', trigger: 'change' }],
        'content.route.methods': [{ required: true, message: '请选择Methods方法', trigger: 'change' }],
        'content.route.priority': [{ required: true, message: '请输入优先级', trigger: 'blur' }],
        'content.route.expiry_date': [{ required: true, message: '请输入过期时间', trigger: 'blur' }],
        'content.route.enable_websocket': [{ required: true, message: '请选择WebSocket', trigger: 'change' }],
        'content.route.plugins_check': [{ required: true, message: '请选择插件类型', trigger: 'change' }],
        'content.route.health_check': [{ required: true, message: '请选择健康检查类型', trigger: 'change' }],
        */
        'content.hasCdn': [{ required: true, message: '请选择有无CDN配置', trigger: 'change' }],
        'content.needHttpsRedirect': [{ required: true, message: '请选择是否需要https强制跳转', trigger: 'change' }],
      }),
      requestWay: ['GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      remoteAddrOptions: [
        { value: 'none', label: '无限制', disabled: false },
        {
          value: '192.168.32.0/24',
          label: '云立方(192.168.32.0/24)',
          disabled: false,
        },
        { value: '10.2.0.0/16', label: '混合云(10.2.0.0/16)', disabled: false },
        {
          value: '**********/24',
          label: '内网(**********/24)',
          disabled: false,
        },
        {
          value: '***********/16',
          label: '内网(***********/16)',
          disabled: true,
        },
        { value: '10.0.0.0/8', label: '内网(10.0.0.0/8)', disabled: false },
      ],
      immediatelyList: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      immediately: 'slow',
      hostOptions: [{ value: '', label: '' }],
      urisOptions: [{ value: '', label: '' }],
      geolocationOptions: [],
      principalNameToEmail: {},
      principalListMini: [],
      projectList: [],
      applicationList: [],
      organizationList: [],
      departmentList: [],
      serverOption: [],
      serverOptionMini: [],
      localUser: store.getters.email,
      node_status: 1,
      hiddenItem: ['env', 'principal', 'upstream_id', 'none_upstream', 'connectivity'],
      envGroups: [],
      temp: {
        id: undefined,
        orderType: '域名解析',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        rightDomain: '',
        time_now: '',
        content: {
          moreConfigVis: false,
          costUser: store.getters.email,
          name: '',
          type: 'A',
          resourceRecords: [{ value: '' }],
          thirdServe: 'intsig',
          ttl: 600,
          cornDns: '',
          scope: 2,
          geolocation: '',
          org: undefined,
          dep: undefined,
          project: '',
          env: 'online',
          team: '',
          expiryDate: 31536000,
          waf: 'none',
          hasCdn: false,
          needHttpsRedirect: false,
          otherPrincipal: undefined,
          reason: '',
          comment: '',
          proxyName: '',
          apiRouteCheck: 'no',
          gateWayVis: true,
          route: {},
        },
        timeline: [],
        highlvlSetting: 'no',
      },
      securityGroupList: [],
      pre_project_name_list: [],
      organizationOptions: [],
      preStatus: false,
      vpcStatus: false,
      tipsStatus: false,
      modalFormBindServerVisible: false,
      vpc_index: 3,
      instance_index: 0,
      scrollPage: 1,
      serverScrollPage: 1,
      valueData: '',
      serverValueData: '',
      treePageSize: 50,
      modalFormPreBindServerVisible: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: { span: 24, offset: 0 },
          sm: { span: 18, offset: 6 },
        },
      },
      PARTONE,
      PARTNODE,
      PARTVARS,
      form: this.$form.createForm(this, 'formRef'),
      // form1: this.$form.createForm(this, ),
      // form2: this.$form.createForm(this, ),
    }
  },
  created() {
    this.geolocationOptions = geolocationOptionsApi()
    this.getBaseInfo()
    this.getList()
    groupNames()
      .then(res => {
        this.envGroups = res.Data.clusters
      })
      .catch(() => {})
  },
  watch: {
    temp: {
      handler: function (val) {
        if (val && val.node === 3) {
          this.hiddenItem = ['principal', 'upstream_id', 'none_upstream']
        } else {
          this.hiddenItem = ['env', 'principal', 'upstream_id', 'none_upstream', 'connectivity']
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    filterOption: filterLabelValue,
    /*
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    */
    handleOk() {
      antdFormValidate(this.$refs.orderForm, valid => {
        if (valid) {
          this.Date()
          this.temp.node = 1
          this.temp.status = 1
          this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
          this.temp.content = { data: JSON.stringify(this.temp.content) }
          this.temp.content.immediately = this.immediately
          createOrder(this.temp)
            .then(response => {
              if (response === undefined) {
                notification.error({
                  message: '创建失败',
                  description: '后端接口错误，请联系运维开发排查~',
                })
              } else {
                if (response.Code === 400) {
                  const msg = response.data.msg
                  notification.error({
                    message: '创建失败',
                    description: msg,
                  })
                } else if (response.Code === 200) {
                  response.Data.content = JSON.parse(response.Data.content.data)
                  this.temp = response.Data
                  this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
                  this.node_status = 1
                  notification.success({
                    message: '创建成功',
                    description: '工单创建成功',
                  })
                  this.$router.push({ path: '/workflow/dns-add', query: { id: response.Data.id } })
                }
              }
            })
            .finally(() => {
              this.moadlVisable = false
            })
        }
      })
    },
    handleCancel() {
      this.moadlVisable = false
    },
    reApply() {
      this.$router.push({
        path: '/workflow/dns-add',
      })
      this.temp = {
        ...this.temp,
        id: '',
        orderType: '域名解析',
        node: 0,
        status: 0,
        founderEmail: store.getters.email,
        founder: store.getters.name,
        handler: '',
        handlerEmail: '',
        timeline: [],
        comment: '',
      }
    },
    envChange(envId) {
      this.temp.content.route.envId = envId
      console.log(envId, 'envId')
      if (envId) {
        this.envGroups.forEach(item => {
          if (item.envId === envId) {
            this.temp.content.proxyName = item.domain
          }
        })
      } else {
        this.temp.content.proxyName = ''
      }
    },
    getList() {
      if (this.$route.query.id) {
        getOrderInfo(this.$route.query.id).then(response => {
          response.Data.content = JSON.parse(response.Data.content.data)
          this.getCostUserOrg(response.Data.founderEmail)
          if (response.Data.orderType === this.temp.orderType) {
            this.temp = response.Data
            this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
            if (this.temp.node > 0) {
              this.temp.highlvlSetting = 'yes'
              this.temp.content.moreConfigVis = true
            }
          } else {
            this.$router.push({ path: '/404' })
          }
        })
      } else {
        this.getCostUserOrg(this.temp.founderEmail)
      }
    },
    getBaseInfo() {
      upstreamIdList().then(response => {
        this.upstreamIdListOptions = response.Data.UpstreamId
      })
      envDomainList().then(response => {
        this.envOptions = response.Data.clusters
        this.envdomainMap = JSON.parse(JSON.stringify(this.envOptions))
        for (let i = 0; i < this.envdomainMap.length; ++i) {
          delete this.envdomainMap[i].env
        }
      })
    },
    selectOneRouteEnv(val) {
      for (let i = 0; i < this.envdomainMap.length; i++) {
        if (this.envdomainMap[i].envId === val) {
          this.temp.content.proxyName = this.envdomainMap[i].domain
        }
      }
    },
    contentNameChange(e) {
      this.temp.content.name = e.target.value.trim()
      if (this.temp.content.route.hosts.length === 0) {
        this.temp.content.route.hosts.push(this.temp.content.name)
      }
      if (this.temp.content.route.hosts && this.temp.content.route.hosts.length === 1) {
        this.temp.content.route.hosts[0] = this.temp.content.name
      }
    },
    listFilter(value) {
      if (!value) return []
      let value1
      value1 = value.split('|')
      let res = []
      for (let j = 0; j < value1.length; j++) {
        res[j] = { value: value1[j] }
      }
      return res
    },
    nextStep(number) {
      if (number - 1 < this.pageNodeNum) {
        if (number === '1') {
          this.pageNode = '申请域名信息'
          this.pageNodeNum = 0
        } else if (number === '2') {
          this.pageNode = 'DNS配置信息'
          this.pageNodeNum = 1
        } else if (number === '3') {
          this.pageNode = '网关路由配置信息'
          this.pageNodeNum = 2
        } else if (number === '4') {
          this.pageNode = '申请WAF信息'
          this.pageNodeNum = 3
        } else {
          this.pageNode = '申请人/项目信息'
          this.pageNodeNum = 4
          // 保存数据
          this.inputDataSave()
          if (this.$refs.routeForm) {
            this.temp.content.route = this.$refs.routeForm.form
          }
        }
      } else {
        if (number == 2) {
          this.$refs.testForm?.validate()
        }
        let hasError = noc.promiseArray(this.temp.content.resourceRecords).find(item => item.hasError)
        antdFormValidate(this.$refs.orderForm, valid => {
          if (hasError) {
            return
          }
          if (valid) {
            if (number === '1') {
              this.pageNode = '申请域名信息'
              this.pageNodeNum = 0
            } else if (number === '2') {
              this.nextDnsSave()
              // 校验dns信息
              this.getDnsThirdServeCheck()
              this.pageNode = 'DNS配置信息'
              this.pageNodeNum = 1
            } else if (number === '3') {
              this.pageNode = '网关路由配置信息'
              this.pageNodeNum = 2
            } else if (number === '4') {
              if (this.$refs.routeForm) {
                this.temp.content.route = this.$refs.routeForm.form
              }
              this.pageNode = '申请WAF信息'
              this.pageNodeNum = 3
            } else {
              this.pageNode = '申请人/项目信息'
              this.pageNodeNum = 4
              // 保存数据
              this.inputDataSave()
            }
          } else {
            return false
          }
        })
      }
    },
    costUserChange(value) {
      this.getCostUserOrg(value)
    },
    getCostUserOrg(useremail) {
      GetUserOrgAllInfo({ email: useremail }).then(response => {
        this.organizationOptions = response.Data.orgCas
        this.organizationOptions.forEach(level1Option => {
          if (level1Option.children) {
            level1Option.children.forEach(level2Option => {
              // 移除第三层数据
              delete level2Option.children
            })
          }
        })
        this.temp.content.organization = []
        if (this.organizationOptions[0].label !== undefined && this.organizationOptions[0].label !== null) {
          this.temp.content.organization.push(this.organizationOptions[0].label)
          if (this.organizationOptions[0].children !== null && this.organizationOptions[0].children[0].label !== null) {
            this.temp.content.organization.push(this.organizationOptions[0].children[0].label)
          }
        }
      })
    },
    getDnsThirdServeCheck() {
      thirdServerCheck({ records: this.temp.content.resourceRecords }).then(response => {
        this.temp.content.thirdServe = response.Data.type
      })
    },
    getDnsThirdServeManulCheck(value) {
      const _this = this
      if (value === 'CNAME') {
        this.$confirm({
          title: '请判断',
          content: '解析值是否为第三方供应商服务器地址',
          okText: '是',
          cancelText: '否',
          onOk() {
            _this.temp.content.thirdServe = 'third'
          },
          onCancel() {
            _this.temp.content.thirdServe = 'intsig'
          },
        })
      }
    },
    inputDataSave() {
      this.nextDnsSave()
      // this.nextNodeSave()
      // this.nextVarsSave()
    },
    nextDnsSave() {
      antdFormValidate(this.$refs.orderForm, values => {
        // 下列赋值无效，已同步到 resourceRecords
        const partOneArr = []

        if (values[`${PARTONE}value`]) {
          values[`${PARTONE}value`].forEach((item, index) => {
            const obj = {
              value: item,
            }
            partOneArr.push(obj)
          })
        }
        if (partOneArr.length) {
          this.temp.content.resourceRecords = partOneArr
        }
      })
    },
    /*
    nextNodeSave () {
      const {
        form1: { validateFields }
      } = this
      validateFields((errors, values) => {
        if (!errors) {
          const partOneArr = []
          values[`${PARTNODE}ip`].forEach((item, index) => {
            const obj = {
              ip: item,
              port: values[`${PARTNODE}port`][index] ? values[`${PARTNODE}port`][index] : 80,
              weight: values[`${PARTNODE}weight`][index] ? values[`${PARTNODE}weight`][index] : 1
            }
            partOneArr.push(obj)
          })
          if (partOneArr.length) {
            this.temp.content.route.upstream.nodes = partOneArr
          }
        }
      })
    },
    */
    /*
    nextVarsSave () {
      const {
        form2: { validateFields }
      } = this
      validateFields((errors, values) => {
        if (!errors) {
          const partOneArr = []
          values[`${PARTVARS}var_name`].forEach((item, index) => {
            const obj = {
              var_name: item,
              var_operator: values[`${PARTVARS}var_operator`][index],
              var_value: values[`${PARTVARS}var_value`][index]
            }
            partOneArr.push(obj)
          })
          if (partOneArr.length) {
            this.temp.content.route.vars = partOneArr
          }
        }
      })
    },
    */
    contentNameCheckChange(rule, val, callback) {
      callback = antdFormValidateCallback
      if (val) {
        const zReg =
          /^((\*\.)?[a-zA-Z0-9_]+([-.]{1}[a-zA-Z0-_9]+)*\.[a-z_A-Z]{2,}|[a-zA-Z0-9_]+([-.]{1}[_a-zA-Z0-9]+)*\.[a-zA-Z_]{2,}\.[a-zA-Z_]{2,})(:[0-9]{1,5})?(.*)?$/gim
        if (!zReg.test(val)) {
          this.temp.rightDomain = false
          return callback('域名语法格式不正确，请填写一个域名，如: cloud.intsig.com')
        }
      } else {
        return callback('请输入域名')
      }
      // 由于 etcd aliyunPrivate 的解析与 第三方供应商解析存在 重复情况，因此取消重复校验
      if (this.temp.content.cornDns === 'office_etcd' || this.temp.content.cornDns === 'aliyun-qxb-private') {
        this.temp.rightDomain = true
        return callback()
      } else {
        return noc.promise((resolve, reject) => {
          let postData = {
            domainName: val,
            type: this.temp.content.type,
            geolocation: this.temp.content.geolocation,
            costUser: this.temp.content.costUser,
          }
          DomainCheck(postData).then(response => {
            if (response.Data.message === 'ok') {
              this.temp.rightDomain = true
              if (this.temp.content.hosts && this.temp.content.hosts.length === 0) {
                this.temp.content.hosts.push(val)
              }
              if (this.temp.content.hosts && this.temp.content.hosts.length === 1) {
                this.temp.content.hosts[0] = val
              }
              resolve()
            } else {
              this.temp.rightDomain = false
              reject(`域名不可用 ${response.Data.message}`)
            }
          })
        })
      }
    },
    moreConfigVisControl(e) {
      if (e.target.value === 'yes') {
        this.temp.content.moreConfigVis = true
      } else {
        this.temp.content.moreConfigVis = false
      }
    },
    gateWayVisControl(e) {
      // this.nextDnsSave()
      if (e.target.value === 'yes') {
        this.$nextTick(() => {
          this.$refs.routeForm.form.hosts = [this.temp.content.name]
          const arr = this.temp.content.resourceRecords.map(item => {
            return {
              ip: item.value,
              port: '80',
              weight: 1,
            }
          })
          this.$refs.routeForm.form.upstream.nodes = arr
        })
        this.temp.content.gateWayVis = false
      } else {
        this.temp.content.gateWayVis = true
      }
    },
    getUpstreamIdListInfo() {
      upstreamIdList().then(response => {
        this.upstreamIdListOptions = response.Data.UpstreamId
      })
    },
    searchUserEmailMethod(search) {
      if (search === '') {
        this.userEmailList = []
      }
      getUserList({ searchText: search, pageNo: 1, pageSize: 50 }).then(response => {
        let arry = response.Data.data
        for (let i = 0; i < arry.length; i++) {
          if (this.userEmailList.indexOf(arry[i].email) === -1) {
            this.userEmailList.push(arry[i].email)
          }
        }
      })
    },
    onChange(checked) {
      if (checked) {
        this.immediately = 'run'
      } else {
        this.immediately = 'slow'
      }
    },
    createData() {
      this.inputDataSave()
      if (!this.temp.rightDomain) {
        notification.error({
          message: '请提交正确的域名',
        })
        return
      }
      this.moadlVisable = true

      // antdFormValidate(this.$refs.orderForm, (valid) => {
      //   if (valid) {
      //     this.Date()
      //     this.temp.node = 1
      //     this.temp.status = 1
      //     this.temp.timeline = [this.temp.founder + '(' + this.temp.founderEmail + ') 创建工单 ' + this.time_now]
      //     this.temp.content = { data: JSON.stringify(this.temp.content) }
      //     this.temp.content.immediately = this.immediately
      //     createOrder(this.temp).then((response) => {
      //       if (response === undefined) {
      //         notification.error({
      //           message: '创建失败',
      //           description: '后端接口错误，请联系运维开发排查~'
      //         })
      //       } else {
      //         if (response.Code === 400) {
      //           const msg = response.data.msg
      //           notification.error({
      //             message: '创建失败',
      //             description: msg
      //           })
      //         } else if (response.Code === 200) {
      //           response.Data.content = JSON.parse(response.Data.content.data)
      //           this.temp = response.Data
      //           this.node_status = 1
      //           notification.success({
      //             message: '创建成功',
      //             description: '工单创建成功'
      //           })
      //           this.$router.push({ path: '/workflow/dns-add', query: { id: response.Data.id } })
      //         }
      //       }
      //     })
      //   }
      // })
    },
    refreshZoneData(val) {
      this.$forceUpdate()
      this.instance_index = (this.instanceTypeList || []).findIndex(item => item.zone_id === val)
      this.temp.content.approval_data.instance_type = ''
    },
    refreshData(val) {
      this.$forceUpdate()
      this.vpc_index = (this.vpc_info || []).findIndex(item => item.vpc_id === val)
      this.temp.content.approval_data.default_security_group_id = ''
      this.temp.content.approval_data.default_v_switch_id = ''
    },
    leaderApproveData() {
      this.inputDataSave()
      this.node_status = 0
      this.temp.node = 2
      this.temp.status = 1
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
          this.node_status = 1
          notification.success({
            message: '审批成功',
            description: '上级领导审批成功',
          })
        }
      })
    },
    safeApproveData() {
      this.inputDataSave()
      this.temp.node = 3
      this.temp.status = 1
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code !== 200) {
          notification.error({
            message: '审批执行报错',
            description: response.Code,
          })
        } else if (response.Code === 200) {
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
          this.node_status = 1
          notification.success({
            message: '审批成功',
            description: '管理员审批成功',
          })
        }
      })
    },
    // auditApproveData () {
    //   this.inputDataSave()
    //   antdFormValidate(this.$refs.orderForm, (valid) => {
    //     if (valid) {
    //       this.temp.node = 4
    //       this.temp.status = 1
    //       this.temp.handler = store.getters.name
    //       this.temp.handlerEmail = store.getters.email
    //       this.temp.content = { data: JSON.stringify(this.temp.content) }
    //       approveOrder(this.temp).then((response) => {
    //         if (response === undefined) {
    //           notification.error({
    //             message: '审批失败',
    //             description: '后端接口错误，请联系运维开发排查~'
    //           })
    //         } else if (response.Code !== 200) {
    //           notification.error({
    //             message: '审批执行报错',
    //             description: response.data.msg
    //           })
    //         } else if (response.Code === 200) {
    //           response.Data.content = JSON.parse(response.Data.content.data)
    //           this.temp = response.Data
    //           this.node_status = 1
    //           notification.success({
    //             message: '审批成功',
    //             description: '管理员审批成功'
    //           })
    //         }
    //       })
    //     }
    //   })
    // },
    managementApproveData() {
      this.inputDataSave()
      if (this.$refs.routeForm) {
        this.temp.content.route = this.$refs.routeForm.form
      }
      this.temp.node = 4
      this.temp.status = 10
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code !== 200) {
          notification.error({
            message: '审批执行报错',
            description: response.data.msg,
          })
        } else if (response.Code === 200) {
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
          this.node_status = 1
          notification.success({
            message: '审批成功',
            description: '管理员审批成功',
          })
        }
      })
    },
    rejectData() {
      this.inputDataSave()
      this.temp.node = 4
      this.temp.status = 20
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '审批失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else if (response.Code === 200) {
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
          this.node_status = 1
          notification.success({
            message: '审批完成',
            description: '该审批已被拒绝',
          })
        }
      })
    },
    revokeData() {
      this.inputDataSave()
      this.Date()
      this.temp.node = 4
      this.temp.status = 30
      this.temp.handler = store.getters.name
      this.temp.handlerEmail = store.getters.email
      this.temp.content = { data: JSON.stringify(this.temp.content) }
      approveOrder(this.temp).then(response => {
        if (response === undefined) {
          notification.error({
            message: '撤回失败',
            description: '后端接口错误，请联系运维开发排查~',
          })
        } else {
          response.Data.content = JSON.parse(response.Data.content.data)
          this.temp = response.Data
          this.temp.content.organization = [response.Data.temp.content.org, response.Data.temp.content.dep]
          this.node_status = 1
          notification.success({
            message: '撤回成功',
            description: '该工单被申请人撤回',
          })
        }
      })
      // this.$refs.orderForm.refresh()
    },
    Date() {
      const nowDate = new Date()
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      }
      const newmonth = date.month >= 10 ? date.month : '0' + date.month
      const newday = date.date >= 10 ? date.date : '0' + date.date
      const newminutes = date.minutes >= 10 ? date.minutes : '0' + date.minutes
      const newseconds = date.seconds >= 10 ? date.seconds : '0' + date.seconds

      this.time_now = date.year + '-' + newmonth + '-' + newday + ' ' + date.hours + ':' + newminutes + ':' + newseconds
    },
    healthCheckActiveChange(checked) {
      if (checked === 'false') {
        this.temp.content.upstream.checks.active.https_verify_certificate = false
      } else {
        this.temp.content.upstream.checks.active.https_verify_certificate = true
      }
    },
    onJsonChange(value) {
      this.onJsonSave(value)
    },
    onJsonSave(value) {
      this.temp.plugins.other = value
      this.hasJsonFlag = true
    },
    onError(value) {
      this.hasJsonFlag = false
    },
  },
}
</script>

<style lang="less" scoped>
/deep/ .ant-form-item-control {
  line-height: 20px;
}

/deep/ .bottonAlign {
  display: flex;
  justify-content: center;
}
.routeFormBox {
  width: 100%;
  display: flex;
  justify-content: center;
  .routeForm {
    width: 80%;
  }
}
</style>
<style>
.ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 20px;
  vertical-align: top;
}

.my-dns-divider {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 2px;
  margin: 8px 0;
}
</style>
