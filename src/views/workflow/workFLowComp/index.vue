<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="流程类型">
              <x-select
                v-model:value="queryParam.orderType"
                :options="orderTypeList"
                placeholder="请选择流程"
              ></x-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="状态">
              <a-select
                v-model:value="queryParam.status"
                placeholder="工作流状态"
                :options="statusOptions"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="内容">
              <a-input
                v-model:value="queryParam.contentText"
                placeholder="模糊匹配"
                allowClear
                @pressEnter="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="申请人">
              <a-input
                v-model:value="queryParam.founderEmail"
                placeholder="申请人邮箱"
                allowClear
                @pressEnter="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="处理人">
              <a-input
                v-model:value="queryParam.handlerEmail"
                placeholder="处理人邮箱"
                allowClear
                @pressEnter="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 6) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button
                style="margin-left: 8px"
                @click="
                  () =>
                    (this.queryParam = {
                      orderType: undefined,
                      contentText: undefined,
                      ...this.params,
                    })
                "
              >
                重置
              </tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'node'">
          <a-tag class="text" @click="showRe(record)" color="blue">节点 {{ text + 1 }}</a-tag>
          <a-tooltip>
            <template #title>{{ test(record) }}</template>
            <a-tag
              style="
                  display: inline-block;
                  max-width: 160px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
              :color="record.status == 10 ? 'green' : 'orange'"
              v-if="test(record)"
            >
              {{ test(record) }}
            </a-tag>
          </a-tooltip>
        </template>
        <template v-else-if="column.dataIndex == 'status'">
          <a-badge :status="statusTypeFilter(text)" :text="statusFilter(text)" />
        </template>
        <template v-else-if="column.dataIndex == 'action'">
          <a @click="clickLinkTo(record)">详情</a>
        </template>
      </template>
    </s-table>
  </a-card>
</template>
<script>
import moment from 'moment'
import { Ellipsis, STable } from '@/components'
import { getOrderList, getOrderTypeList } from '@/api/workflow/order'
import { removeWatermark, setWaterMark } from '@/utils/watermark'
import store from '@/store'

const columns = [
  {
    title: '类型',
    dataIndex: 'orderType',
    sorter: true
  },
  {
    title: '审批节点',
    dataIndex: 'node',
    // customRender: ({ text }) => '节点 ' + (text + 1),
    scopedSlots: { customRender: 'node' },
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
    sorter: true
  },
  {
    title: '申请人',
    dataIndex: 'founder',
    sorter: true
  },
  {
    title: '处理人',
    dataIndex: 'handler',
    sorter: true
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' }
  }
]

const statusMap = {
  1: {
    status: 'processing',
    text: '待审核'
  },
  2: {
    status: 'processing',
    text: '待Web端处理'
  },
  5: {
    status: 'warning',
    text: '自动化处理中'
  },
  10: {
    status: 'success',
    text: '已完成'
  },
  20: {
    status: 'error',
    text: '被拒绝'
  },
  30: {
    status: 'warning',
    text: '申请人撤回'
  },
  40: {
    status: 'success',
    text: '数据已回收'
  }
}

const statusOptions = [
  {
    value: 1,
    label: '待审核'
  },
  {
    value: 2,
    label: '待Web端处理'
  },
  {
    value: 5,
    label: '自动化处理中'
  },
  {
    value: 10,
    label: '已完成'
  },
  {
    value: 20,
    label: '被拒绝'
  },
  {
    value: 30,
    label: '申请人撤回'
  },
  {
    value: 40,
    label: '数据已回收'
  }
]

const pagination = {
  showTotal: total => `共 ${total} 条`
}

export default {
  name: 'WorkflowComponents',
  components: {
    STable,
    Ellipsis
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    this.columns = columns
    this.pagination = pagination
    return {
      queryParam: {
        orderType: undefined,
        contentText: undefined,
        ...this.params
      },
      orderTypeList: [],
      statusOptions,
      advanced: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getOrderList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  mounted () {
    const email = store.getters.email
    const name = store.getters.name
    if (email) {
      setWaterMark(email, name)
    }
    this.getOrderTypeInfo()
  },
  unmounted () {
    removeWatermark()
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    test (record) {
      let text = ''
      if (record.orderType == '数据库资源申请' || record.orderType == '数据库查询申请') {
        let arr = []
        record.content && record.content.checkedValue ? (arr = JSON.parse(record.content.checkedValue)) : (arr = [])

        arr.forEach(item => {
          record.orderType == '数据库资源申请'
            ? (text += `${JSON.parse(item.key).dbName},`)
            : (text += `${JSON.parse(item.key).ip},`)
        })
        text = text.slice(0, text.length - 1)
      } else if (record.orderType == '数据库DCL申请') {
        let arr = []
        record.content && record.content.data ? (arr = JSON.parse(record.content.data).mysqls) : (arr = [])
        arr.forEach(item => {
          text += item.dbNames.join(',')
        })
        text = text.slice(0, text.length - 1)
      } else if (record.orderType == '数据库DML申请' || record.orderType == '数据库DDL申请') {
        let arr = []
        record.content && record.content.data ? (arr = JSON.parse(record.content.data).mysqls) : (arr = [])
        arr.forEach(item => {
          text += `${item.ip},`
        })
        text = text.slice(0, text.length - 1)
      } else if (record.orderType == '服务器应用部署') {
        let arr = []
        record.content && record.content.data ? (arr = JSON.parse(record.content.data).hosts) : (arr = [])
        arr.forEach(item => {
          text += `${item},`
        })
        text = text.slice(0, text.length - 1)
      } else if (record.orderType == '域名解析' || record.orderType == '域名修改') {
        record.content && record.content.data && JSON.parse(record.content.data).name
          ? (text = JSON.parse(record.content.data).name)
          : (text = '')
      } else if (
        record.orderType == '数据库服务器新增' ||
        record.orderType == '服务器新增' ||
        record.orderType == '服务器创建'
      ) {
        record.content && record.content.data && JSON.parse(record.content.data).projectName
          ? (text = JSON.parse(record.content.data).projectName)
          : (text = '')
      } else if (record.orderType == '域名购买') {
        record.content && record.content.domain ? (text = record.content.domain) : (text = '')
      } else if (record.orderType == '域名注销') {
        record.content && record.content.name ? (text = record.content.name) : (text = '')
      } else if (record.orderType.includes('服务器')) {
        if (record.orderType == '服务器配置变更' || record.orderType == '服务器注销') {
          record.content && record.content.ip ? (text = record.content.ip) : (text = '')
        } else if (record.orderType == '服务器权限操作' || record.orderType == '服务器授权'|| record.orderType == '服务器管理员授权') {
          record.content && record.content.host_list ? (text = record.content.host_list) : (text = '')
        } else if (record.orderType == '服务器数据下载') {
          record.content && record.content.ip ? (text = record.content.ip) : (text = '')
        } else if (record.orderType == 'DF取数') {
          record.content && record.content.ip ? (text = record.content.ip) : (text = '')
        } else {
          record.content && record.content.data && JSON.parse(record.content.data).ip
            ? (text = JSON.parse(record.content.data).ip)
            : (text = '')
        }
      } else if (record.orderType == 'Consul数据库配置') {
        record.content && record.content.data ? (text = JSON.parse(record.content.data).items.join(',')) : (text = '')
      } else if (record.orderType == 'Consul密钥配置申请') {
        record.content && record.content.consulData
          ? (text = JSON.parse(record.content.consulData).consulKey)
          : (text = '')
      } else if (record.orderType == '数据集下载') {
        record.content && record.content.data ? (text = JSON.parse(record.content.data).name) : (text = '')
      } else if (record.orderType == 'GPU服务发布') {
        record.content && record.content.data ? (text = JSON.parse(record.content.data).serviceName) : (text = '')
      }
      return text
    },
    showRe (re) {
      let text = ''
    },
    clickLinkTo (record) {
      let routeUrl
      if (record.orderType === '服务器新增') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-add', query: { id: record.id } })
      } else if (record.orderType === '服务器创建') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-create', query: { id: record.id } })
      } else if (record.orderType === '服务器标签变更') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-modify-tag', query: { id: record.id } })
      } else if (record.orderType === '服务器配置变更') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-modify-config', query: { id: record.id } })
      } else if (record.orderType === '服务器注销') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-del', query: { id: record.id } })
      } else if (record.orderType === '远程工作站') {
        routeUrl = this.$router.resolve({ path: '/workflow/cloud-desktop', query: { id: record.id } })
      } else if (record.orderType === 'DLP云桌面') {
        routeUrl = this.$router.resolve({ path: '/workflow/dlp-desktop', query: { id: record.id } })
      } else if (record.orderType === 'DLP云桌面场景') {
        routeUrl = this.$router.resolve({ path: '/workflow/dlp-desktop-policy', query: { id: record.id } })
      } else if (record.orderType === '服务器云桌面') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-desktop', query: { id: record.id } })
      } else if (record.orderType === '远程办公云桌面申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/remote-desktop', query: { id: record.id } })
      } else if (record.orderType === '远程办公URL白名单') {
        routeUrl = this.$router.resolve({ path: '/workflow/remote-desktop-url', query: { id: record.id } })
      } else if (record.orderType === '云桌面注销') {
        routeUrl = this.$router.resolve({ path: '/workflow/desktop-destroy', query: { id: record.id } })
      } else if (record.orderType === '服务器授权') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-permission-operation', query: { id: record.id } })
      } else if (record.orderType === '服务器管理员授权') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-admin-auth', query: { id: record.id } })
      } else if (record.orderType === '运维人工协助') {
        routeUrl = this.$router.resolve({ path: '/workflow/ops-help', query: { id: record.id } })
      } else if (record.orderType === '域名解析') {
        routeUrl = this.$router.resolve({ path: '/workflow/dns-add', query: { id: record.id } })
      } else if (record.orderType === '域名修改') {
        routeUrl = this.$router.resolve({ path: '/workflow/dns-edit', query: { id: record.id } })
      } else if (record.orderType === '域名注销') {
        routeUrl = this.$router.resolve({ path: '/workflow/dns-del', query: { id: record.id } })
      } else if (record.orderType === 'Coder账号申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/coder', query: { id: record.id } })
      } else if (record.orderType === '代码成本检测申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/code-cost-detection', query: { id: record.id } })
      } else if (record.orderType === '基线检查-资源类') {
        routeUrl = this.$router.resolve({ path: '/workflow/baseline-check-resources', query: { id: record.id } })
      } else if (record.orderType === 'SecretsManager') {
        routeUrl = this.$router.resolve({ path: '/workflow/secrets-manager', query: { id: record.id } })
      } else if (record.orderType === '数据查询申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/db-approval', query: { id: record.id } })
      } else if (record.orderType === 'DCL申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/dcl', query: { id: record.id } })
      } else if (record.orderType === 'DDL申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/ddl', query: { id: record.id } })
      } else if (record.orderType === 'DDL申请(QA)') {
        routeUrl = this.$router.resolve({ path: '/workflow/ddl-qa', query: { id: record.id } })
      } else if (record.orderType === 'DML申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/dml', query: { id: record.id } })
      } else if (record.orderType === 'DML申请(QA)') {
        routeUrl = this.$router.resolve({ path: '/workflow/dml-qa', query: { id: record.id } })
      } else if (record.orderType === '数据库报警配置') {
        routeUrl = this.$router.resolve({ path: '/workflow/dbalter', query: { id: record.id } })
      } else if (record.orderType === 'Superset权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/superset', query: { id: record.id } })
      } else if (record.orderType === '域名购买') {
        routeUrl = this.$router.resolve({ path: '/workflow/domain-purchase', query: { id: record.id } })
      } else if (record.orderType === '数据导出申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/db-source', query: { id: record.id } })
      } else if (record.orderType === '大数据抽取申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/bigdata-exctraction', query: { id: record.id } })
      } else if (record.orderType === 'IDS模块权限申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/ids-permission', query: { id: record.id } })
      } else if (record.orderType === '大数据共享主题库') {
        routeUrl = this.$router.resolve({ path: '/workflow/shared-theme', query: { id: record.id } })
      } else if (record.orderType === '数据操作申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/db-manipulation', query: { id: record.id } })
      } else if (record.orderType === 'Consul密钥配置申请') {
        routeUrl = this.$router.resolve({
          path: '/workflow/consul',
          query: { id: record.id, orderType: record.orderType }
        })
      } else if (record.orderType === 'Consul数据库配置') {
        routeUrl = this.$router.resolve({ path: '/workflow/consul-database', query: { id: record.id } })
      } else if (record.orderType === 'ClickHouse表权限申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/ck-auth', query: { id: record.id } })
      } else if (record.orderType === 'ClickHouse资源导出') {
        routeUrl = this.$router.resolve({ path: '/workflow/ck-source', query: { id: record.id } })
      } else if (record.orderType === 'ClickHouse归档数据恢复') {
        routeUrl = this.$router.resolve({ path: '/workflow/ck-restore', query: { id: record.id } })
      } else if (record.orderType === '大数据表查询权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/bigdata-query', query: { id: record.id } })
      } else if (record.orderType === '大数据表导出权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/bigdata-export', query: { id: record.id } })
      } else if (record.orderType === '对象存储个人版申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/app-objectStorage-personal', query: { id: record.id } })
      } else if (record.orderType === '作业执行') {
        routeUrl = this.$router.resolve({ path: '/workflow/job-execute', query: { id: record.id } })
      } else if (record.orderType === '网关路由配置') {
        routeUrl = this.$router.resolve({ path: '/workflow/gateway-route-config', query: { id: record.id } })
      } else if (record.orderType === '运维资源申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/ops-source', query: { id: record.id } })
      } else if (record.orderType === '新增共享云存储') {
        routeUrl = this.$router.resolve({ path: '/workflow/share-storage-create', query: { id: record.id } })
      } else if (record.orderType === '加入已有共享云存储') {
        routeUrl = this.$router.resolve({ path: '/workflow/share-storage-add', query: { id: record.id } })
      } else if (record.orderType === '云IDE申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/cloud-ide', query: { id: record.id } })
      } else if (record.orderType === 'AI开发IDE申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/ai-ide-apply', query: { id: record.id } })
      } else if (record.orderType === 'IDE申请GPU卡配额') {
        routeUrl = this.$router.resolve({ path: '/workflow/ai-ide-gpu-quota', query: { id: record.id } })
      } else if (record.orderType === '敏感数据解密Token权限申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/sd-decryption-token', query: { id: record.id } })
      } else if (record.orderType === '敏感数据解密申请(大数据平台)') {
        routeUrl = this.$router.resolve({ path: '/workflow/sd-decryption', query: { id: record.id } })
      } else if (record.orderType === '敏感数据使用(运营平台)申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/sd-use', query: { id: record.id } })
      } else if (record.orderType === '阿里云Redis白名单') {
        routeUrl = this.$router.resolve({ path: '/workflow/redis-safegroup', query: { id: record.id } })
      } else if (record.orderType === '公有云账号权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/cloud-account-permission', query: { id: record.id } })
      } else if (record.orderType === '公有云个人账号权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/person-account-permission', query: { id: record.id } })
      } else if (record.orderType === '公有云业务API权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/business-account-permission', query: { id: record.id } })
      } else if (record.orderType === '公有云业务API权限(vault)') {
        routeUrl = this.$router.resolve({ path: '/workflow/cloud-access-vault', query: { id: record.id } })
      } else if (record.orderType === '公有云个人账号权限(keycloak)') {
        routeUrl = this.$router.resolve({ path: '/workflow/cloud-access-keycloak', query: { id: record.id } })
      } else if (record.orderType === 'Jenkins权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/jenkins-auth', query: { id: record.id } })
      } else if (record.orderType === 'Jenkins新建Job项目') {
        routeUrl = this.$router.resolve({ path: '/workflow/jenkins-job', query: { id: record.id } })
      } else if (record.orderType === 'Jenkins删除Job项目') {
        routeUrl = this.$router.resolve({ path: '/workflow/jenkins-job-del', query: { id: record.id } })
      } else if (record.orderType === '数据集下载') {
        routeUrl = this.$router.resolve({ path: '/workflow/data-set-download', query: { id: record.id } })
      } else if (record.orderType === '安全组解绑') {
        routeUrl = this.$router.resolve({ path: '/workflow/sg-unbind', query: { id: record.id } })
      } else if (record.orderType === '大数据网络互通') {
        routeUrl = this.$router.resolve({ path: '/workflow/bigdata-net', query: { id: record.id } })
      } else if (record.orderType === '数据超限导出申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/bigdata-download', query: { id: record.id } })
      } else if (record.orderType === '服务器数据下载') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-data-download', query: { id: record.id } })
      } else if (record.orderType === 'DF取数') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-data-download-df', query: { id: record.id } })
      } else if (record.orderType === '安全组放开') {
        routeUrl = this.$router.resolve({ path: '/workflow/security-group-release', query: { id: record.id } })
      } else if (record.orderType === '服务器应用部署') {
        routeUrl = this.$router.resolve({ path: '/workflow/server-app-deploy', query: { id: record.id } })
      } else if (record.orderType === '大数据建表修改表申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/BigDataCreateTable', query: { id: record.id } })
      } else if (record.orderType === 'FTP账号申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/security-ftp-account', query: { id: record.id } })
      } else if (record.orderType === '数据库服务器新增') {
        routeUrl = this.$router.resolve({ path: '/workflow/db-server-add', query: { id: record.id } })
      } else if (record.orderType === '数据库服务器创建') {
        routeUrl = this.$router.resolve({ path: '/workflow/db-server-create', query: { id: record.id } })
      } else if (record.orderType === '启信宝OPS权限申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/qxb-ops-auth', query: { id: record.id } })
      } else if (record.orderType === 'Slurm权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/slurm-auth', query: { id: record.id } })
      } else if (record.orderType === 'Slurm任务创建') {
        routeUrl = this.$router.resolve({ path: '/workflow/slurm-job-create', query: { id: record.id } })
      } else if (record.orderType === 'Grafana权限') {
        routeUrl = this.$router.resolve({ path: '/workflow/grafana-auth', query: { id: record.id } })
      } else if (record.orderType === '云平台权限申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/user-route-approve', query: { id: record.id } })
      } else if (record.orderType === '账单标签变更') {
        routeUrl = this.$router.resolve({ path: '/workflow/cost-label', query: { id: record.id } })
      } else if (record.orderType === 'ClickHouse高配额查询') {
        routeUrl = this.$router.resolve({ path: '/workflow/ck-high-quota', query: { id: record.id } })
      } else if (record.orderType === '大数据资产转交') {
        routeUrl = this.$router.resolve({ path: '/workflow/bigdata-asset-change', query: { id: record.id } })
      } else if (record.orderType === '域名证书下载申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/domain-ssl-download', query: { id: record.id } })
      } else if (record.orderType === 'GPU服务发布') {
        routeUrl = this.$router.resolve({ path: '/workflow/gpu-service-apply', query: { id: record.id } })
      } else if (record.orderType === '网关IP封禁') {
        routeUrl = this.$router.resolve({ path: '/workflow/ip-ban', query: { id: record.id } })
      } else if (record.orderType === 'CS同步库扫描') {
        routeUrl = this.$router.resolve({ path: '/workflow/cs-scan', query: { id: record.id } })
      } else if (record.orderType === '虚拟机配置变更') {
        routeUrl = this.$router.resolve({ path: '/workflow/vm-asset-modify-config', query: { id: record.id } })
      } else if (record.orderType === '虚拟机注销') {
        routeUrl = this.$router.resolve({ path: '/workflow/vm-asset-del', query: { id: record.id } })
      } else if (record.orderType === '网关StreamRoute配置') {
        routeUrl = this.$router.resolve({ path: '/workflow/steam_route', query: { id: record.id } })
      } else if (record.orderType === '混合云Redis白名单') {
        routeUrl = this.$router.resolve({ path: '/workflow/hybird-redis-safegroup', query: { id: record.id } })
      } else if (record.orderType === '运维费用分摊比例') {
        routeUrl = this.$router.resolve({ path: '/workflow/cost-split-ratio', query: { id: record.id } })
      } else if (record.orderType === '海外项目通信加速申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/overseas-acceleration', query: { id: record.id } })
      } else if (record.orderType === '大模型密钥申请') {
        routeUrl = this.$router.resolve({ path: '/workflow/api-key', query: { id: record.id } })
      } else if (record.orderType === 'BatchDclForMysqlPwdChange') {
        routeUrl = this.$router.resolve({ path: '/workflow/batch-dcl-for-mysql-pwd-change', query: { id: record.id } })
      } else {
        routeUrl = this.$router.resolve({ path: '/404' })
      }
      window.open(routeUrl.href, '_blank')
    },
    getOrderTypeInfo () {
      getOrderTypeList().then(res => {
        for (let i = 0, len = res.Data.orderType.length; i < len; i++) {
          let orderType = {}
          orderType.value = res.Data.orderType[i]
          orderType.label = res.Data.orderType[i]
          this.orderTypeList.push(orderType)
        }
      })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    statusFilter (type) {
      return statusMap[type]?.text || type
    },
    statusTypeFilter (type) {
      return statusMap[type]?.status || type
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    resetSearchForm () {
      this.queryParam = {
        date: moment(new Date())
      }
    }
  }
}
</script>
<style scoped lang="less">
.text {
  display: inline-block;
  /* 固定宽度 */
  max-width: 100px;
  /* 强制文字不换行 */
  white-space: nowrap;
  /* 将溢出部分进行隐藏 */
  overflow: hidden;
  /* 溢出文本转换为省略号 */
  text-overflow: ellipsis;
}
</style>
