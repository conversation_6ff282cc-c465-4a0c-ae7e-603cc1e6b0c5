<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="流程类型">
              <x-select
                v-model:value="queryParam.orderType"
                :options="orderTypeList"
                placeholder="请选择流程"
              ></x-select>
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <tx-button type="primary" @click="$refs.table.refresh(true)">查询</tx-button>
              <tx-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</tx-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator">
      <tx-button type="primary" icon="plus" @click="handleAdd">新建</tx-button>
    </div>
    <s-table
      ref="table"
      size="default"
      :rowKey="record => record.id"
      :pagination="pagination"
      :columns="columns"
      :data="loadData"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'action'">
          <a @click="handleUpdate(record)">更新</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除？" @confirm="handleDel(record)">
            <template #icon>
              <a-icon type="question-circle-o" style="color: red" />
            </template>
            <a>删除</a>
          </a-popconfirm>
        </template>
      </template>
    </s-table>

    <a-modal
      title="新建流程"
      :visible="visible"
      :confirm-loading="confirmLoading"
      width="60%"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="类型" name="orderType">
          <a-input v-model:value="form.orderType" />
        </a-form-model-item>
        <a-form-model-item label="名称" name="urlName">
          <a-input v-model:value="form.urlName" />
        </a-form-model-item>
        <a-form-model-item label="总节点数" name="nodes">
          <a-input-number v-model:value="form.nodes" />
        </a-form-model-item>
        <a-form-model-item label="类型" name="handlerContent">
          <NocEditorJson
            v-model="form.handlerContent"
            :show-btns="false"
            :mode="'code'"
            lang="zh"
            @json-change="onJsonChange"
            @json-save="onJsonSave"
            @has-error="onError"
          ></NocEditorJson>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <a-modal
      title="流程更新"
      :visible="updateVisible"
      :confirm-loading="confirmLoading"
      width="60%"
      @ok="handleUpdateOk"
      @cancel="handleUpdateCancel"
    >
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="类型" name="orderType">
          <a-input v-model:value="form.orderType" />
        </a-form-model-item>
        <a-form-model-item label="名称" name="urlName">
          <a-input v-model:value="form.urlName" />
        </a-form-model-item>
        <a-form-model-item label="总节点数" name="nodes">
          <a-input-number v-model:value="form.nodes" />
        </a-form-model-item>
        <a-form-model-item label="类型" name="handlerContent">
          <NocEditorJson
            v-model="form.handlerContent"
            :show-btns="false"
            :mode="'code'"
            lang="zh"
            @json-change="onJsonChange"
            @json-save="onJsonSave"
            @has-error="onError"
          ></NocEditorJson>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import {
  getProcessList,
  getProcessOrderTypeList,
  createProcess,
  getProcessInfo,
  updateProcess,
  deleteProcess,
} from '@/api/workflow/process'

const columns = [
  {
    title: '类型',
    dataIndex: 'orderType',
    sorter: true,
  },
  {
    title: '名称',
    dataIndex: 'urlName',
    sorter: true,
  },
  {
    title: '总节点数',
    dataIndex: 'nodes',
    sorter: true,
  },
  {
    title: '处理人信息',
    dataIndex: 'handlerContent',
    width: '650px',
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    width: '120px',
    align: 'center',
  },
]

const pagination = {
  showTotal: total => `共 ${total} 条`,
}

export default {
  name: 'Process',
  components: {
    STable,
    Ellipsis,
  },
  data() {
    this.columns = columns
    this.pagination = pagination
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      orderTypeList: [],
      queryParam: {},
      advanced: false,
      form: {
        orderType: undefined,
        urlName: undefined,
        nodes: 2,
        handlerContent: {},
      },
      visible: false,
      updateVisible: false,
      confirmLoading: false,
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return getProcessList(requestParameters).then(res => {
          if (res.Data.hasOwnProperty('data')) {
            return res.Data
          } else {
            return { data: [], pageNo: 1, pageSize: 10, totalCount: 0, totalPage: 0 }
          }
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      rules: {
        orderType: [{ required: true, message: '请填写流程类型', trigger: 'change' }],
        urlName: [{ required: true, message: '请填写流程名称', trigger: 'change' }],
        nodes: [{ required: true, message: '请输入流程节点', trigger: 'change' }],
        handlerContent: [{ required: true, message: '请填写处理人信息', trigger: 'change' }],
      },
    }
  },
  mounted() {
    this.getProcessOrderTypeListInfo()
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
  },
  methods: {
    getProcessOrderTypeListInfo() {
      getProcessOrderTypeList().then(res => {
        for (var i = 0, len = res.Data.orderType.length; i < len; i++) {
          var orderType = {}
          orderType.value = res.Data.orderType[i]
          orderType.label = res.Data.orderType[i]
          this.orderTypeList.push(orderType)
        }
      })
    },
    handleAdd() {
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          createProcess(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('创建失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.visible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('创建成功')
            }
          })
        } else {
          this.$message.error('创建失败')
          this.confirmLoading = false
        }
      })
    },
    handleCancel(e) {
      this.visible = false
    },
    handleUpdate(record) {
      getProcessInfo(record.id).then(res => {
        this.form = res.Data
      })
      this.updateVisible = true
    },
    handleUpdateOk(e) {
      this.confirmLoading = true
      antdFormValidate(this.$refs.ruleForm, valid => {
        if (valid) {
          updateProcess(this.form).then(res => {
            if (res === undefined) {
              this.confirmLoading = false
              this.$message.error('更新失败,后端接口错误，请联系运维开发排查~')
            } else {
              this.updateVisible = false
              this.confirmLoading = false
              this.$refs.table.refresh()
              this.$message.success('更新成功')
            }
          })
        } else {
          this.$message.error('更新失败')
          this.confirmLoading = false
        }
      })
    },
    handleUpdateCancel(e) {
      this.updateVisible = false
    },
    handleDel(record) {
      deleteProcess(record.id).then(res => {
        if (res.Data.message === 'ok') {
          this.$refs.table.refresh(true)
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date()),
      }
    },
    onJsonChange(value) {
      this.onJsonSave(value)
    },
    onJsonSave(value) {
      this.form.handlerContent = value
      this.hasJsonFlag = true
    },
    onError(value) {
      this.hasJsonFlag = false
    },
  },
}
</script>
