declare module 'vue' {
  export interface GlobalComponents {
    CodeDiff: typeof import('src/suite/CodeDiff/index.vue')
    EditorJson: typeof import('src/suite/EditorJson/index.vue')
    PageContainer: typeof import('src/suite/PageContainer/index.vue')
    RadioEnv: typeof import('src/suite/RadioEnv/index.vue')
    SelectDepartment: typeof import('src/suite/SelectDepartment/index.vue')
    SelectOrganization: typeof import('src/suite/SelectOrganization/index.vue')
    XSelect: typeof import('ant-design-vue/lib/select')
  }
}

export {}
