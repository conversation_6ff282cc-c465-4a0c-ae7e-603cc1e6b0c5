/**
 * vue 文件模块定义，禁止使用顶层 import / export，让 IDE 识别到 .vue 文件全局声明
 */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 全局JsDiff类型声明
interface Window {
  JsDiff?: {
    diffLines: (oldStr: string, newStr: string) => Array<{
      value: string
      added?: boolean
      removed?: boolean
    }>
  }
  diffLines?: (oldStr: string, newStr: string) => Array<{
    value: string
    added?: boolean
    removed?: boolean
  }>
}
