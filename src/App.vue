<!--
 * @Author: huidong_yang <EMAIL>
 * @Date: 2023-06-25 11:35:55
 * @LastEditors: huidong_yang <EMAIL>
 * @LastEditTime: 2023-08-29 18:10:54
 * @FilePath: \cloud_web\src\App.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <a-config-provider :autoInsertSpaceInButton="false" :locale="locale">
    <div v-if="routerReload" id="app">
      <router-view />
    </div>
  </a-config-provider>
</template>

<script>
import locale from 'ant-design-vue/lib/locale-provider/zh_CN'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { feedbackInit } from '@/utils/feedback'

moment.locale('zh-cn')
export default {
  provide() {
    return {
      reload: this.myReload,
    }
  },
  data() {
    return {
      locale,
      routerReload: true, // 刷新
    }
  },
  mounted() {
    feedbackInit()
  },
  methods: {
    // 刷新功能
    myReload() {
      this.routerReload = false
      this.$nextTick(() => {
        this.routerReload = true
      })
    },
  },
}
</script>
