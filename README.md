# 云平台前端项目说明

[[_TOC_]]

## 环境说明

| 环境       | 内网机器 IP            | Web 网址                       |
| ---------- | ---------------------- | ------------------------------ |
| 线上环境   | **********, ********** | https://cloud.intsig.net/      |
| 预发布环境 | **********, ********** | https://cloud-pre.intsig.net/  |
| 测试环境   | **********, ********** | https://cloud-test.intsig.net/ |

**Nginx 配置**，如需修改请前往 [noc-web-nginx-conf](https://gitlab.intsig.net/NOC/noc-web-nginx-conf)。

预发布环境**目前未使用**。

## 1. 开发

### 1.1 开发环境工作目录

将 cloud_web clone 到本地目录。

- 安装全局 pnpm
  - `npm add pnpm -f -g`
- 安装项目依赖
  - `pnpm install`

### 1.2 启动服务

- 打开命令行，运行命令以下任意命令：
  - `npm run serve`
  - `pnpm serve`
  - `tx serve`
- 或者使用快捷操作，运行项目下的 `dev-serve.bat`。

### 1.3 Git 约定

**环境与分支**：

- 开发分支
  - develop | feature/xxx
- 测试分支
  - test
- 预发布分支
  - pre
  - **目前未使用**
- 线上分支
  - main

**项目部署**，以开发分支 develop 为例，将其合并到环境对应的分支：

- 送测
  - develop -> test
- 预发布
  - develop -> pre
  - **目前未使用**
- 上线
  - develop -> main
- 开发新功能，建新分支
  - main -> feature/yyy

**开发分支说明**：

- 有明确版本要求时，使用 feature/xxx 进行命名；
- 无明确版本要求时，全部使用 develop 作为开发分支；
- 使用作者名称作为开发分支时，需要时常从 main 分支合并代码，避免本地仓库与主分支差异过大。

## 2. 送测

- 将开发分支代码合并到 test 分支；
- 前往 https://jenkins.intsig.net/view/Autom/job/autom_DEVops_test/job/cloud_web_test/
- 选择 `test 服务器` 和 `test 分支` 执行构建和部署测试环境。

## 3. 预发布

**目前未使用**。

- 将开发分支代码合并到 pre 分支；
- 前往 https://jenkins.intsig.net/view/Autom/job/autom_DEVops_test/job/cloud_web_test/
- 选择 `pre 服务器` 和 `pre 分支` 执行构建和部署预发布环境。

## 4. 上线

- 将开发分支代码合并到 main 分支；
- 前往 https://jenkins.intsig.net/view/Autom/job/autom_DEVops_online/job/cloud_web_online/
- 选择 `online 服务器` 和 `main 分支` 执行构建和部署线上环境。

---

如果安装了 [tx](https://tianxuan.intsig.com/cli.md) 脚手架，也可以在 cloud_web 目录使用命令进行部署：

```shell
# 部署测试环境
tx d test

# 部署预发布环境
tx d pre

# 部署线上环境
tx d online
```

部署线上环境后会收到企业微信通知。
