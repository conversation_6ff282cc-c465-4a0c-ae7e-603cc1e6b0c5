@import 'antd.min.css';

.app-loading {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: radial-gradient(#9ed1ff, transparent);
  opacity: 1;
  transition: opacity .2s;
  z-index: 100;
}

.app-loading.fade-out {
  opacity: 0;
}

.app-loading > .ant-spin {
  position: absolute;
  height: 40px;
  width: 40px;
  left: 50%;
  top: 31%;
  margin-left: -20px;
  margin-top: 0 !important;
}

.ant-spin-dot-spin > i {
  position: absolute;
  display: block;
  height: 9px;
  width: 9px;
  animation: antSpinMove 1s linear infinite alternate;
  background-color: #1890ff;
  border-radius: 100%;
  opacity: .3;
  transform: scale(.75);
  transform-origin: 50% 50%;
}

.ant-spin-dot-spin > i:nth-child(1) {
  left: 0;
  top: 0;
}

.ant-spin-dot-spin > i:nth-child(2) {
  top: 0;
  right: 0;
  animation-delay: .4s;
}

.ant-spin-dot-spin > i:nth-child(3) {
  right: 0;
  bottom: 0;
  animation-delay: .8s;
}

.ant-spin-dot-spin > i:nth-child(4) {
  bottom: 0;
  left: 0;
  animation-delay: 1.2s;
}
