var Guacamole;(Guacamole=Guacamole||{}).ArrayBufferReader=function(e){var t=this;e.onblob=function(e){for(var n=window.atob(e),a=new ArrayBuffer(n.length),o=new Uint8Array(a),i=0;i<n.length;i++)o[i]=n.charCodeAt(i);t.ondata&&t.ondata(a)},e.onend=function(){t.onend&&t.onend()},this.ondata=null,this.onend=null},(Guacamole=Guacamole||{}).ArrayBufferWriter=function(e){var t=this;function n(t){for(var n="",a=0;a<t.byteLength;a++)n+=String.fromCharCode(t[a]);e.sendBlob(window.btoa(n))}e.onack=function(e){t.onack&&t.onack(e)},this.blobLength=Guacamole.ArrayBufferWriter.DEFAULT_BLOB_LENGTH,this.sendData=function(e){var a=new Uint8Array(e);if(a.length<=t.blobLength)n(a);else for(var o=0;o<a.length;o+=t.blobLength)n(a.subarray(o,o+t.blobLength))},this.sendEnd=function(){e.sendEnd()},this.onack=null},Guacamole.ArrayBufferWriter.DEFAULT_BLOB_LENGTH=6048,(Guacamole=Guacamole||{}).AudioContextFactory={singleton:null,getAudioContext:function(){var e=window.AudioContext||window.webkitAudioContext;if(e)try{return Guacamole.AudioContextFactory.singleton||(Guacamole.AudioContextFactory.singleton=new e),Guacamole.AudioContextFactory.singleton}catch(e){}return null}},(Guacamole=Guacamole||{}).AudioPlayer=function(){this.sync=function(){}},Guacamole.AudioPlayer.isSupportedType=function(e){return Guacamole.RawAudioPlayer.isSupportedType(e)},Guacamole.AudioPlayer.getSupportedTypes=function(){return Guacamole.RawAudioPlayer.getSupportedTypes()},Guacamole.AudioPlayer.getInstance=function(e,t){return Guacamole.RawAudioPlayer.isSupportedType(t)?new Guacamole.RawAudioPlayer(e,t):null},Guacamole.RawAudioPlayer=function(e,t){var n=Guacamole.RawAudioFormat.parse(t),a=Guacamole.AudioContextFactory.getAudioContext(),o=a.currentTime,i=new Guacamole.ArrayBufferReader(e),r=1===n.bytesPerSample?window.Int8Array:window.Int16Array,s=1===n.bytesPerSample?128:32768,u=[],l=function(){var e=function(e){if(e.length<=1)return e[0];var t=0;e.forEach((function(e){t+=e.length}));var n=0,a=new r(t);return e.forEach((function(e){a.set(e,n),n+=e.length})),a}(u);return e?(u=function(e){for(var t=Number.MAX_VALUE,a=e.length,o=Math.floor(e.length/n.channels),i=Math.floor(.02*n.rate),s=Math.max(n.channels*i,n.channels*(o-i));s<e.length;s+=n.channels){for(var u=0,l=0;l<n.channels;l++)u+=Math.abs(e[s+l]);u<=t&&(a=s+n.channels,t=u)}return a===e.length?[e]:[new r(e.buffer.slice(0,a*n.bytesPerSample)),new r(e.buffer.slice(a*n.bytesPerSample))]}(e),e=u.shift()):null};i.ondata=function(e){!function(e){u.push(new r(e))}(new r(e));var t=l();if(t){var i=a.currentTime;o<i&&(o=i);var c=a.createBufferSource();c.connect(a.destination),c.start||(c.start=c.noteOn),c.buffer=function(e){var t=e.length/n.channels,i=a.currentTime;o<i&&(o=i);for(var r=a.createBuffer(n.channels,t,n.rate),u=0;u<n.channels;u++)for(var l=r.getChannelData(u),c=u,h=0;h<t;h++)l[h]=e[c]/s,c+=n.channels;return r}(t),c.start(o),o+=t.length/n.channels/n.rate}},this.sync=function(){var e=a.currentTime;o=Math.min(o,e+.3)}},Guacamole.RawAudioPlayer.prototype=new Guacamole.AudioPlayer,Guacamole.RawAudioPlayer.isSupportedType=function(e){return!!Guacamole.AudioContextFactory.getAudioContext()&&null!==Guacamole.RawAudioFormat.parse(e)},Guacamole.RawAudioPlayer.getSupportedTypes=function(){return Guacamole.AudioContextFactory.getAudioContext()?["audio/L8","audio/L16"]:[]},(Guacamole=Guacamole||{}).AudioRecorder=function(){this.onclose=null,this.onerror=null},Guacamole.AudioRecorder.isSupportedType=function(e){return Guacamole.RawAudioRecorder.isSupportedType(e)},Guacamole.AudioRecorder.getSupportedTypes=function(){return Guacamole.RawAudioRecorder.getSupportedTypes()},Guacamole.AudioRecorder.getInstance=function(e,t){return Guacamole.RawAudioRecorder.isSupportedType(t)?new Guacamole.RawAudioRecorder(e,t):null},Guacamole.RawAudioRecorder=function(e,t){var n=this,a=Guacamole.RawAudioFormat.parse(t),o=Guacamole.AudioContextFactory.getAudioContext();navigator.mediaDevices||(navigator.mediaDevices={}),navigator.mediaDevices.getUserMedia||(navigator.mediaDevices.getUserMedia=(navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia).bind(navigator));var i=new Guacamole.ArrayBufferWriter(e),r=1===a.bytesPerSample?window.Int8Array:window.Int16Array,s=1===a.bytesPerSample?128:32768,u=0,l=0,c=null,h=null,d=null,f=function(e){if(0===e)return 1;var t=Math.PI*e;return Math.sin(t)/t},m=function(e,t){for(var n,a,o=(e.length-1)*t,i=Math.floor(o)-3+1,r=Math.floor(o)+3,s=0,u=i;u<=r;u++)s+=(e[u]||0)*(-(a=3)<(n=o-u)&&n<a?f(n)*f(n/a):0);return s},p=function(e){(d=o.createScriptProcessor(2048,a.channels,a.channels)).connect(o.destination),d.onaudioprocess=function(e){i.sendData(function(e){var t=e.length;u+=t;var n=Math.round(u*a.rate/e.sampleRate)-l;l+=n;for(var o=new r(n*a.channels),i=0;i<a.channels;i++)for(var c=e.getChannelData(i),h=i,d=0;d<n;d++)o[h]=m(c,d/(n-1))*s,h+=a.channels;return o}(e.inputBuffer).buffer)},(h=o.createMediaStreamSource(e)).connect(d),"suspended"===o.state&&o.resume(),c=e},v=function(){i.sendEnd(),n.onerror&&n.onerror()};i.onack=function(e){var t;e.code!==Guacamole.Status.Code.SUCCESS||c?(!function(){if(h&&h.disconnect(),d&&d.disconnect(),c)for(var e=c.getTracks(),t=0;t<e.length;t++)e[t].stop();d=null,h=null,c=null,i.sendEnd()}(),i.onack=null,e.code===Guacamole.Status.Code.RESOURCE_CLOSED?n.onclose&&n.onclose():n.onerror&&n.onerror()):(t=navigator.mediaDevices.getUserMedia({audio:!0},p,v))&&t.then&&t.then(p,v)}},Guacamole.RawAudioRecorder.prototype=new Guacamole.AudioRecorder,Guacamole.RawAudioRecorder.isSupportedType=function(e){return!!Guacamole.AudioContextFactory.getAudioContext()&&null!==Guacamole.RawAudioFormat.parse(e)},Guacamole.RawAudioRecorder.getSupportedTypes=function(){return Guacamole.AudioContextFactory.getAudioContext()?["audio/L8","audio/L16"]:[]},(Guacamole=Guacamole||{}).BlobReader=function(e,t){var n,a=this,o=0;n=window.BlobBuilder?new BlobBuilder:window.WebKitBlobBuilder?new WebKitBlobBuilder:window.MozBlobBuilder?new MozBlobBuilder:new function(){var e=[];this.append=function(n){e.push(new Blob([n],{type:t}))},this.getBlob=function(){return new Blob(e,{type:t})}},e.onblob=function(t){for(var i=window.atob(t),r=new ArrayBuffer(i.length),s=new Uint8Array(r),u=0;u<i.length;u++)s[u]=i.charCodeAt(u);n.append(r),o+=r.byteLength,a.onprogress&&a.onprogress(r.byteLength),e.sendAck("OK",0)},e.onend=function(){a.onend&&a.onend()},this.getLength=function(){return o},this.getBlob=function(){return n.getBlob()},this.onprogress=null,this.onend=null},(Guacamole=Guacamole||{}).BlobWriter=function(e){var t=this,n=new Guacamole.ArrayBufferWriter(e);n.onack=function(e){t.onack&&t.onack(e)};this.sendBlob=function(e){var a=0,o=new FileReader,i=function(){if(a>=e.size)t.oncomplete&&t.oncomplete(e);else{var i=function(e,t,n){var a=(e.slice||e.webkitSlice||e.mozSlice).bind(e),o=n-t;if(o!==n){var i=a(t,o);if(i.size===o)return i}return a(t,n)}(e,a,a+n.blobLength);a+=n.blobLength,o.readAsArrayBuffer(i)}};o.onload=function(){n.sendData(o.result),n.onack=function(o){t.onack&&t.onack(o),o.isError()||(t.onprogress&&t.onprogress(e,a-n.blobLength),i())}},o.onerror=function(){t.onerror&&t.onerror(e,a,o.error)},i()},this.sendEnd=function(){n.sendEnd()},this.onack=null,this.onerror=null,this.onprogress=null,this.oncomplete=null},(Guacamole=Guacamole||{}).Client=function(e){var t=this,n=Guacamole.Client.State.IDLE,a=0,o=null,i=0,r={0:"butt",1:"round",2:"square"},s={0:"bevel",1:"miter",2:"round"},u=new Guacamole.Display,l={},c={},h={},d=[],f=[],m=[],p=new Guacamole.IntegerPool,v=[];function g(e){e!=n&&(n=e,t.onstatechange&&t.onstatechange(n))}function y(){return n==Guacamole.Client.State.CONNECTED||n==Guacamole.Client.State.WAITING}this.exportState=function(e){var t={currentState:n,currentTimestamp:a,layers:{}},o={};for(var i in l)o[i]=l[i];u.flush((function(){for(var n in o){var a=parseInt(n),i=o[n],r=i.toCanvas(),s={width:i.width,height:i.height};i.width&&i.height&&(s.url=r.toDataURL("image/png")),a>0&&(s.x=i.x,s.y=i.y,s.z=i.z,s.alpha=i.alpha,s.matrix=i.matrix,s.parent=S(i.parent)),t.layers[n]=s}e(t)}))},this.importState=function(e,t){var o,i;for(o in n=e.currentState,a=e.currentTimestamp,u.cancel(),l)(i=parseInt(o))>0&&l[o].dispose();for(o in l={},e.layers){i=parseInt(o);var r=e.layers[o],s=w(i);if(u.resize(s,r.width,r.height),r.url&&(u.setChannelMask(s,Guacamole.Layer.SRC),u.draw(s,0,0,r.url)),i>0&&r.parent>=0){var c=w(r.parent);u.move(s,c,r.x,r.y,r.z),u.shade(s,r.alpha);var h=r.matrix;u.distort(s,h[0],h[1],h[2],h[3],h[4],h[5])}}u.flush(t)},this.getDisplay=function(){return u},this.sendSize=function(t,n){y()&&e.sendMessage("size",t,n)},this.sendKeyEvent=function(t,n){y()&&e.sendMessage("key",n,t)},this.sendMouseState=function(t,n){if(y()){var a=t.x,o=t.y;n&&(a/=u.getScale(),o/=u.getScale()),u.moveCursor(Math.floor(a),Math.floor(o));var i=0;t.left&&(i|=1),t.middle&&(i|=2),t.right&&(i|=4),t.up&&(i|=8),t.down&&(i|=16),e.sendMessage("mouse",Math.floor(a),Math.floor(o),i)}},this.sendTouchState=function(t,n){if(y()){var a=t.x,o=t.y;n&&(a/=u.getScale(),o/=u.getScale()),e.sendMessage("touch",t.id,Math.floor(a),Math.floor(o),Math.floor(t.radiusX),Math.floor(t.radiusY),t.angle,t.force)}},this.createOutputStream=function(){var e=p.next();return v[e]=new Guacamole.OutputStream(t,e)},this.createAudioStream=function(n){var a=t.createOutputStream();return e.sendMessage("audio",a.index,n),a},this.createFileStream=function(n,a){var o=t.createOutputStream();return e.sendMessage("file",o.index,n,a),o},this.createPipeStream=function(n,a){var o=t.createOutputStream();return e.sendMessage("pipe",o.index,n,a),o},this.createClipboardStream=function(n){var a=t.createOutputStream();return e.sendMessage("clipboard",a.index,n),a},this.createArgumentValueStream=function(n,a){var o=t.createOutputStream();return e.sendMessage("argv",o.index,n,a),o},this.createObjectOutputStream=function(n,a,o){var i=t.createOutputStream();return e.sendMessage("put",n,i.index,a,o),i},this.requestObjectInputStream=function(t,n){y()&&e.sendMessage("get",t,n)},this.sendAck=function(t,n,a){y()&&e.sendMessage("ack",t,n,a)},this.sendBlob=function(t,n){y()&&e.sendMessage("blob",t,n)},this.endStream=function(t){y()&&(e.sendMessage("end",t),v[t]&&(p.free(t),delete v[t]))},this.onstatechange=null,this.onname=null,this.onerror=null,this.onmsg=null,this.onjoin=null,this.onleave=null,this.onaudio=null,this.onvideo=null,this.onmultitouch=null,this.onargv=null,this.onclipboard=null,this.onfile=null,this.onfilesystem=null,this.onpipe=null,this.onrequired=null,this.onsync=null;var w=function(e){var t=l[e];return t||(t=0===e?u.getDefaultLayer():e>0?u.createLayer():u.createBuffer(),l[e]=t),t},S=function(e){if(!e)return null;for(var t in l)if(e===l[t])return parseInt(t);return null};var G={"miter-limit":function(e,t){u.setMiterLimit(e,parseFloat(t))},"multi-touch":function(e,n){t.onmultitouch&&e instanceof Guacamole.Display.VisibleLayer&&t.onmultitouch(e,parseInt(n))}},T={ack:function(e){var t=parseInt(e[0]),n=e[1],a=parseInt(e[2]),o=v[t];o&&(o.onack&&o.onack(new Guacamole.Status(a,n)),a>=256&&v[t]===o&&(p.free(t),delete v[t]))},arc:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),i=parseFloat(e[4]),r=parseFloat(e[5]),s=parseInt(e[6]);u.arc(t,n,a,o,i,r,0!=s)},argv:function(e){var n=parseInt(e[0]),a=e[1],o=e[2];if(t.onargv){var i=f[n]=new Guacamole.InputStream(t,n);t.onargv(i,a,o)}else t.sendAck(n,"Receiving argument values unsupported",256)},audio:function(e){var n=parseInt(e[0]),a=e[1],o=f[n]=new Guacamole.InputStream(t,n),i=null;t.onaudio&&(i=t.onaudio(o,a)),i||(i=Guacamole.AudioPlayer.getInstance(o,a)),i?(c[n]=i,t.sendAck(n,"OK",0)):t.sendAck(n,"BAD TYPE",783)},blob:function(e){var t=parseInt(e[0]),n=e[1],a=f[t];a&&a.onblob&&a.onblob(n)},body:function(e){var n=parseInt(e[0]),a=m[n],o=parseInt(e[1]),i=e[2],r=e[3];if(a&&a.onbody){var s=f[o]=new Guacamole.InputStream(t,o);a.onbody(s,i,r)}else t.sendAck(o,"Receipt of body unsupported",256)},cfill:function(e){var t=parseInt(e[0]),n=w(parseInt(e[1])),a=parseInt(e[2]),o=parseInt(e[3]),i=parseInt(e[4]),r=parseInt(e[5]);u.setChannelMask(n,t),u.fillColor(n,a,o,i,r)},clip:function(e){var t=w(parseInt(e[0]));u.clip(t)},clipboard:function(e){var n=parseInt(e[0]),a=e[1];if(t.onclipboard){var o=f[n]=new Guacamole.InputStream(t,n);t.onclipboard(o,a)}else t.sendAck(n,"Clipboard unsupported",256)},close:function(e){var t=w(parseInt(e[0]));u.close(t)},copy:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),i=parseInt(e[4]),r=parseInt(e[5]),s=w(parseInt(e[6])),l=parseInt(e[7]),c=parseInt(e[8]);u.setChannelMask(s,r),u.copy(t,n,a,o,i,s,l,c)},cstroke:function(e){var t=parseInt(e[0]),n=w(parseInt(e[1])),a=r[parseInt(e[2])],o=s[parseInt(e[3])],i=parseInt(e[4]),l=parseInt(e[5]),c=parseInt(e[6]),h=parseInt(e[7]),d=parseInt(e[8]);u.setChannelMask(n,t),u.strokeColor(n,a,o,i,l,c,h,d)},cursor:function(e){var t=parseInt(e[0]),n=parseInt(e[1]),a=w(parseInt(e[2])),o=parseInt(e[3]),i=parseInt(e[4]),r=parseInt(e[5]),s=parseInt(e[6]);u.setCursor(t,n,a,o,i,r,s)},curve:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),i=parseInt(e[4]),r=parseInt(e[5]),s=parseInt(e[6]);u.curveTo(t,n,a,o,i,r,s)},disconnect:function(e){t.disconnect()},dispose:function(e){var t=parseInt(e[0]);if(t>0){var n=w(t);u.dispose(n),delete l[t]}else t<0&&delete l[t]},distort:function(e){var t=parseInt(e[0]),n=parseFloat(e[1]),a=parseFloat(e[2]),o=parseFloat(e[3]),i=parseFloat(e[4]),r=parseFloat(e[5]),s=parseFloat(e[6]);if(t>=0){var l=w(t);u.distort(l,n,a,o,i,r,s)}},error:function(e){var n=e[0],a=parseInt(e[1]);t.onerror&&t.onerror(new Guacamole.Status(a,n)),t.disconnect()},end:function(e){var t=parseInt(e[0]),n=f[t];n&&(n.onend&&n.onend(),delete f[t])},file:function(e){var n=parseInt(e[0]),a=e[1],o=e[2];if(t.onfile){var i=f[n]=new Guacamole.InputStream(t,n);t.onfile(i,a,o)}else t.sendAck(n,"File transfer unsupported",256)},filesystem:function(e){var n=parseInt(e[0]),a=e[1];if(t.onfilesystem){var o=m[n]=new Guacamole.Object(t,n);t.onfilesystem(o,a)}},identity:function(e){var t=w(parseInt(e[0]));u.setTransform(t,1,0,0,1,0,0)},img:function(e){var n=parseInt(e[0]),a=parseInt(e[1]),o=w(parseInt(e[2])),i=e[3],r=parseInt(e[4]),s=parseInt(e[5]),l=f[n]=new Guacamole.InputStream(t,n);u.setChannelMask(o,a),u.drawStream(o,r,s,l,i)},jpeg:function(e){var t=parseInt(e[0]),n=w(parseInt(e[1])),a=parseInt(e[2]),o=parseInt(e[3]),i=e[4];u.setChannelMask(n,t),u.draw(n,a,o,"data:image/jpeg;base64,"+i)},lfill:function(e){var t=parseInt(e[0]),n=w(parseInt(e[1])),a=w(parseInt(e[2]));u.setChannelMask(n,t),u.fillLayer(n,a)},line:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]);u.lineTo(t,n,a)},lstroke:function(e){var t=parseInt(e[0]),n=w(parseInt(e[1])),a=w(parseInt(e[2]));u.setChannelMask(n,t),u.strokeLayer(n,a)},mouse:function(e){var t=parseInt(e[0]),n=parseInt(e[1]);u.showCursor(!0),u.moveCursor(t,n)},move:function(e){var t=parseInt(e[0]),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),i=parseInt(e[4]);if(t>0&&n>=0){var r=w(t),s=w(n);u.move(r,s,a,o,i)}},msg:function(e){var n,a,o=!0,i=parseInt(e[0]);if(t.onmsg&&void 0===(o=t.onmsg(i,e.slice(1)))&&(o=!0),o)switch(i){case Guacamole.Client.Message.USER_JOINED:n=e[1],a=e[2],t.onjoin&&t.onjoin(n,a);break;case Guacamole.Client.Message.USER_LEFT:n=e[1],a=e[2],t.onleave&&t.onleave(n,a)}},name:function(e){t.onname&&t.onname(e[0])},nest:function(t){(function(t){var n=d[t];return null==n&&((n=d[t]=new Guacamole.Parser).oninstruction=e.oninstruction),n})(parseInt(t[0])).receive(t[1])},pipe:function(e){var n=parseInt(e[0]),a=e[1],o=e[2];if(t.onpipe){var i=f[n]=new Guacamole.InputStream(t,n);t.onpipe(i,a,o)}else t.sendAck(n,"Named pipes unsupported",256)},png:function(e){var t=parseInt(e[0]),n=w(parseInt(e[1])),a=parseInt(e[2]),o=parseInt(e[3]),i=e[4];u.setChannelMask(n,t),u.draw(n,a,o,"data:image/png;base64,"+i)},pop:function(e){var t=w(parseInt(e[0]));u.pop(t)},push:function(e){var t=w(parseInt(e[0]));u.push(t)},rect:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),i=parseInt(e[4]);u.rect(t,n,a,o,i)},required:function(e){t.onrequired&&t.onrequired(e)},reset:function(e){var t=w(parseInt(e[0]));u.reset(t)},set:function(e){var t=w(parseInt(e[0])),n=e[1],a=e[2],o=G[n];o&&o(t,a)},shade:function(e){var t=parseInt(e[0]),n=parseInt(e[1]);if(t>=0){var a=w(t);u.shade(a,n)}},size:function(e){var t=parseInt(e[0]),n=w(t),a=parseInt(e[1]),o=parseInt(e[2]);u.resize(n,a,o)},start:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]);u.moveTo(t,n,a)},sync:function(o){var i=parseInt(o[0]),r=o[1]?parseInt(o[1]):0;u.flush((function(){for(var t in c){var n=c[t];n&&n.sync()}i!==a&&(e.sendMessage("sync",i),a=i)}),i,r),n===Guacamole.Client.State.WAITING&&g(Guacamole.Client.State.CONNECTED),t.onsync&&t.onsync(i,r)},transfer:function(e){var t=w(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),i=parseInt(e[4]),r=parseInt(e[5]),s=w(parseInt(e[6])),l=parseInt(e[7]),c=parseInt(e[8]);3===r?u.put(t,n,a,o,i,s,l,c):5!==r&&u.transfer(t,n,a,o,i,s,l,c,Guacamole.Client.DefaultTransferFunction[r])},transform:function(e){var t=w(parseInt(e[0])),n=parseFloat(e[1]),a=parseFloat(e[2]),o=parseFloat(e[3]),i=parseFloat(e[4]),r=parseFloat(e[5]),s=parseFloat(e[6]);u.transform(t,n,a,o,i,r,s)},undefine:function(e){var t=parseInt(e[0]),n=m[t];n&&n.onundefine&&n.onundefine()},video:function(e){var n=parseInt(e[0]),a=w(parseInt(e[1])),o=e[2],i=f[n]=new Guacamole.InputStream(t,n),r=null;t.onvideo&&(r=t.onvideo(i,a,o)),r||(r=Guacamole.VideoPlayer.getInstance(i,a,o)),r?(h[n]=r,t.sendAck(n,"OK",0)):t.sendAck(n,"BAD TYPE",783)}},E=function(){e.sendMessage("nop"),i=(new Date).getTime()},I=function(){window.clearTimeout(o);var e=(new Date).getTime(),t=Math.max(i+5e3-e,0);t>0?o=window.setTimeout(E,t):E()};e.oninstruction=function(e,t){var n=T[e];n&&n(t),I()},this.disconnect=function(){n!=Guacamole.Client.State.DISCONNECTED&&n!=Guacamole.Client.State.DISCONNECTING&&(g(Guacamole.Client.State.DISCONNECTING),window.clearTimeout(o),e.sendMessage("disconnect"),e.disconnect(),g(Guacamole.Client.State.DISCONNECTED))},this.connect=function(t){g(Guacamole.Client.State.CONNECTING);try{e.connect(t)}catch(e){throw g(Guacamole.Client.State.IDLE),e}I(),g(Guacamole.Client.State.WAITING)}},Guacamole.Client.State={IDLE:0,CONNECTING:1,WAITING:2,CONNECTED:3,DISCONNECTING:4,DISCONNECTED:5},Guacamole.Client.DefaultTransferFunction={0:function(e,t){t.red=t.green=t.blue=0},15:function(e,t){t.red=t.green=t.blue=255},3:function(e,t){t.red=e.red,t.green=e.green,t.blue=e.blue,t.alpha=e.alpha},5:function(e,t){},12:function(e,t){t.red=255&~e.red,t.green=255&~e.green,t.blue=255&~e.blue,t.alpha=e.alpha},10:function(e,t){t.red=255&~t.red,t.green=255&~t.green,t.blue=255&~t.blue},1:function(e,t){t.red=e.red&t.red,t.green=e.green&t.green,t.blue=e.blue&t.blue},14:function(e,t){t.red=255&~(e.red&t.red),t.green=255&~(e.green&t.green),t.blue=255&~(e.blue&t.blue)},7:function(e,t){t.red=e.red|t.red,t.green=e.green|t.green,t.blue=e.blue|t.blue},8:function(e,t){t.red=255&~(e.red|t.red),t.green=255&~(e.green|t.green),t.blue=255&~(e.blue|t.blue)},6:function(e,t){t.red=e.red^t.red,t.green=e.green^t.green,t.blue=e.blue^t.blue},9:function(e,t){t.red=255&~(e.red^t.red),t.green=255&~(e.green^t.green),t.blue=255&~(e.blue^t.blue)},4:function(e,t){t.red=~e.red&t.red&255,t.green=~e.green&t.green&255,t.blue=~e.blue&t.blue&255},13:function(e,t){t.red=255&(~e.red|t.red),t.green=255&(~e.green|t.green),t.blue=255&(~e.blue|t.blue)},2:function(e,t){t.red=e.red&~t.red&255,t.green=e.green&~t.green&255,t.blue=e.blue&~t.blue&255},11:function(e,t){t.red=255&(e.red|~t.red),t.green=255&(e.green|~t.green),t.blue=255&(e.blue|~t.blue)}},Guacamole.Client.Message={USER_JOINED:1,USER_LEFT:2},(Guacamole=Guacamole||{}).DataURIReader=function(e,t){var n=this,a="data:"+t+";base64,";e.onblob=function(e){a+=e},e.onend=function(){n.onend&&n.onend()},this.getURI=function(){return a},this.onend=null},(Guacamole=Guacamole||{}).Display=function(){var e=this,t=0,n=0,a=1,o=document.createElement("div");o.style.position="relative",o.style.width=t+"px",o.style.height=n+"px",o.style.transformOrigin=o.style.webkitTransformOrigin=o.style.MozTransformOrigin=o.style.OTransformOrigin=o.style.msTransformOrigin="0 0";var i=new Guacamole.Display.VisibleLayer(t,n),r=new Guacamole.Display.VisibleLayer(0,0);r.setChannelMask(Guacamole.Layer.SRC),o.appendChild(i.getElement()),o.appendChild(r.getElement());var s=document.createElement("div");s.style.position="relative",s.style.width=t*a+"px",s.style.height=n*a+"px",s.appendChild(o),this.cursorHotspotX=0,this.cursorHotspotY=0,this.cursorX=0,this.cursorY=0,this.statisticWindow=0,this.onresize=null,this.oncursor=null,this.onstatistics=null;var u=[],l=[],c=null,h=function(){for(var e=0,t=0,n=0,a=0;a<l.length;){var o=l[a];if(!o.isReady())break;o.flush(),e=o.localTimestamp,t=o.remoteTimestamp,n+=o.logicalFrames,a++}l.splice(0,a),a&&m(e,t,n)},d=function(){c||(c=window.requestAnimationFrame((function e(){if(c=null,l.length){if(l[0].isReady()){var t=l.shift();t.flush(),m(t.localTimestamp,t.remoteTimestamp,t.logicalFrames)}l.length&&(c=window.requestAnimationFrame(e))}})))},f=[],m=function(t,n,a){if(e.statisticWindow){for(var o=(new Date).getTime(),i=0;i<f.length&&!(o-f[i].timestamp<=e.statisticWindow);i++);f.splice(0,i-1),f.push({localTimestamp:t,remoteTimestamp:n,timestamp:o,frames:a});var r=(f[f.length-1].timestamp-f[0].timestamp)/1e3,s=(f[f.length-1].remoteTimestamp-f[0].remoteTimestamp)/1e3,u=f.length,l=f.reduce((function(e,t){return e+t.frames}),0),c=f.reduce((function(e,t){return e+Math.max(0,t.frames-1)}),0),h=new Guacamole.Display.Statistics({processingLag:o-t,desktopFps:s&&l?l/s:null,clientFps:r?u/r:null,serverFps:s?u/s:null,dropRate:s?c/s:null});e.onstatistics&&e.onstatistics(h)}};function p(){window.requestAnimationFrame&&document.hasFocus()?d():h()}window.addEventListener("blur",(function(){c&&!document.hasFocus()&&(window.cancelAnimationFrame(c),c=null,h())}),!0);var v=function(e,t,n,a){this.localTimestamp=(new Date).getTime(),this.remoteTimestamp=n||this.localTimestamp,this.logicalFrames=a||0,this.cancel=function(){e=null,t.forEach((function(e){e.cancel()})),t=[]},this.isReady=function(){for(var e=0;e<t.length;e++)if(t[e].blocked)return!1;return!0},this.flush=function(){for(var n=0;n<t.length;n++)t[n].execute();e&&e()}};function g(e,t){var n=this;this.blocked=t,this.cancel=function(){n.blocked=!1,e=null},this.unblock=function(){n.blocked&&(n.blocked=!1,p())},this.execute=function(){e&&e()}}function y(e,t){var n=new g(e,t);return u.push(n),n}this.getElement=function(){return s},this.getWidth=function(){return t},this.getHeight=function(){return n},this.getDefaultLayer=function(){return i},this.getCursorLayer=function(){return r},this.createLayer=function(){var e=new Guacamole.Display.VisibleLayer(t,n);return e.move(i,0,0,0),e},this.createBuffer=function(){var e=new Guacamole.Layer(0,0);return e.autosize=1,e},this.flush=function(e,t,n){l.push(new v(e,u,t,n)),u=[],p()},this.cancel=function(){l.forEach((function(e){e.cancel()})),l=[],u.forEach((function(e){e.cancel()})),u=[]},this.setCursor=function(t,n,a,o,i,s,u){y((function(){e.cursorHotspotX=t,e.cursorHotspotY=n,r.resize(s,u),r.copy(a,o,i,s,u,0,0),e.moveCursor(e.cursorX,e.cursorY),e.oncursor&&e.oncursor(r.toCanvas(),t,n)}))},this.showCursor=function(e){var t=r.getElement(),n=t.parentNode;!1===e?n&&n.removeChild(t):n!==o&&o.appendChild(t)},this.moveCursor=function(t,n){r.translate(t-e.cursorHotspotX,n-e.cursorHotspotY),e.cursorX=t,e.cursorY=n},this.resize=function(r,u,l){y((function(){r.resize(u,l),r===i&&(t=u,n=l,o.style.width=t+"px",o.style.height=n+"px",s.style.width=t*a+"px",s.style.height=n*a+"px",e.onresize&&e.onresize(u,l))}))},this.drawImage=function(e,t,n,a){y((function(){e.drawImage(t,n,a)}))},this.drawBlob=function(e,t,n,a){var o;if(window.createImageBitmap){var i;o=y((function(){e.drawImage(t,n,i)}),!0),window.createImageBitmap(a).then((function(e){i=e,o.unblock()}))}else{var r=URL.createObjectURL(a);o=y((function(){s.width&&s.height&&e.drawImage(t,n,s),URL.revokeObjectURL(r)}),!0);var s=new Image;s.onload=o.unblock,s.onerror=o.unblock,s.src=r}},this.drawStream=function(t,n,a,o,i){var r;window.createImageBitmap?(r=new Guacamole.BlobReader(o,i)).onend=function(){e.drawBlob(t,n,a,r.getBlob())}:(r=new Guacamole.DataURIReader(o,i)).onend=function(){e.draw(t,n,a,r.getURI())}},this.draw=function(e,t,n,a){var o=y((function(){i.width&&i.height&&e.drawImage(t,n,i)}),!0),i=new Image;i.onload=o.unblock,i.onerror=o.unblock,i.src=a},this.play=function(e,t,n,a){var o=document.createElement("video");o.type=t,o.src=a,o.addEventListener("play",(function(){!function t(){e.drawImage(0,0,o),o.ended||window.setTimeout(t,20)}()}),!1),y(o.play)},this.transfer=function(e,t,n,a,o,i,r,s,u){y((function(){i.transfer(e,t,n,a,o,r,s,u)}))},this.put=function(e,t,n,a,o,i,r,s){y((function(){i.put(e,t,n,a,o,r,s)}))},this.copy=function(e,t,n,a,o,i,r,s){y((function(){i.copy(e,t,n,a,o,r,s)}))},this.moveTo=function(e,t,n){y((function(){e.moveTo(t,n)}))},this.lineTo=function(e,t,n){y((function(){e.lineTo(t,n)}))},this.arc=function(e,t,n,a,o,i,r){y((function(){e.arc(t,n,a,o,i,r)}))},this.curveTo=function(e,t,n,a,o,i,r){y((function(){e.curveTo(t,n,a,o,i,r)}))},this.close=function(e){y((function(){e.close()}))},this.rect=function(e,t,n,a,o){y((function(){e.rect(t,n,a,o)}))},this.clip=function(e){y((function(){e.clip()}))},this.strokeColor=function(e,t,n,a,o,i,r,s){y((function(){e.strokeColor(t,n,a,o,i,r,s)}))},this.fillColor=function(e,t,n,a,o){y((function(){e.fillColor(t,n,a,o)}))},this.strokeLayer=function(e,t,n,a,o){y((function(){e.strokeLayer(t,n,a,o)}))},this.fillLayer=function(e,t){y((function(){e.fillLayer(t)}))},this.push=function(e){y((function(){e.push()}))},this.pop=function(e){y((function(){e.pop()}))},this.reset=function(e){y((function(){e.reset()}))},this.setTransform=function(e,t,n,a,o,i,r){y((function(){e.setTransform(t,n,a,o,i,r)}))},this.transform=function(e,t,n,a,o,i,r){y((function(){e.transform(t,n,a,o,i,r)}))},this.setChannelMask=function(e,t){y((function(){e.setChannelMask(t)}))},this.setMiterLimit=function(e,t){y((function(){e.setMiterLimit(t)}))},this.dispose=function(e){y((function(){e.dispose()}))},this.distort=function(e,t,n,a,o,i,r){y((function(){e.distort(t,n,a,o,i,r)}))},this.move=function(e,t,n,a,o){y((function(){e.move(t,n,a,o)}))},this.shade=function(e,t){y((function(){e.shade(t)}))},this.scale=function(e){o.style.transform=o.style.WebkitTransform=o.style.MozTransform=o.style.OTransform=o.style.msTransform="scale("+e+","+e+")",a=e,s.style.width=t*a+"px",s.style.height=n*a+"px"},this.getScale=function(){return a},this.flatten=function(){var e=document.createElement("canvas");e.width=i.width,e.height=i.height;var t=e.getContext("2d");return function e(n,a,o){if(n.width>0&&n.height>0){var i=t.globalAlpha;t.globalAlpha*=n.alpha/255,t.drawImage(n.getCanvas(),a,o);for(var r=function(e){var t=[];for(var n in e.children)t.push(e.children[n]);return t.sort((function(e,t){var n=e.z-t.z;if(0!==n)return n;var a=e.getElement(),o=t.getElement().compareDocumentPosition(a);return o&Node.DOCUMENT_POSITION_PRECEDING?-1:o&Node.DOCUMENT_POSITION_FOLLOWING?1:0})),t}(n),s=0;s<r.length;s++){var u=r[s];e(u,a+u.x,o+u.y)}t.globalAlpha=i}}(i,0,0),e}},Guacamole.Display.VisibleLayer=function(e,t){Guacamole.Layer.apply(this,[e,t]);var n=this;this.__unique_id=Guacamole.Display.VisibleLayer.__next_id++,this.alpha=255,this.x=0,this.y=0,this.z=0,this.matrix=[1,0,0,1,0,0],this.parent=null,this.children={};var a=n.getCanvas();a.style.position="absolute",a.style.left="0px",a.style.top="0px";var o=document.createElement("div");o.appendChild(a),o.style.width=e+"px",o.style.height=t+"px",o.style.position="absolute",o.style.left="0px",o.style.top="0px",o.style.overflow="hidden";var i=this.resize;this.resize=function(e,t){o.style.width=e+"px",o.style.height=t+"px",i(e,t)},this.getElement=function(){return o};var r="translate(0px, 0px)",s="matrix(1, 0, 0, 1, 0, 0)";this.translate=function(e,t){n.x=e,n.y=t,r="translate("+e+"px,"+t+"px)",o.style.transform=o.style.WebkitTransform=o.style.MozTransform=o.style.OTransform=o.style.msTransform=r+" "+s},this.move=function(e,t,a,i){n.parent!==e&&(n.parent&&delete n.parent.children[n.__unique_id],n.parent=e,e.children[n.__unique_id]=n,e.getElement().appendChild(o));n.translate(t,a),n.z=i,o.style.zIndex=i},this.shade=function(e){n.alpha=e,o.style.opacity=e/255},this.dispose=function(){n.parent&&(delete n.parent.children[n.__unique_id],n.parent=null),o.parentNode&&o.parentNode.removeChild(o)},this.distort=function(e,t,a,i,u,l){n.matrix=[e,t,a,i,u,l],s="matrix("+e+","+t+","+a+","+i+","+u+","+l+")",o.style.transform=o.style.WebkitTransform=o.style.MozTransform=o.style.OTransform=o.style.msTransform=r+" "+s}},Guacamole.Display.VisibleLayer.__next_id=0,Guacamole.Display.Statistics=function(e){e=e||{},this.processingLag=e.processingLag,this.desktopFps=e.desktopFps,this.serverFps=e.serverFps,this.clientFps=e.clientFps,this.dropRate=e.dropRate},(Guacamole=Guacamole||{}).Event=function(e){this.type=e,this.timestamp=(new Date).getTime(),this.getAge=function(){return(new Date).getTime()-this.timestamp},this.invokeLegacyHandler=function(e){}},Guacamole.Event.DOMEvent=function(e,t){Guacamole.Event.call(this,e),t=t||[],Array.isArray(t)||(t=[t]),this.preventDefault=function(){t.forEach((function(e){e.preventDefault&&e.preventDefault(),e.returnValue=!1}))},this.stopPropagation=function(){t.forEach((function(e){e.stopPropagation()}))}},Guacamole.Event.DOMEvent.cancelEvent=function(e){e.stopPropagation(),e.preventDefault&&e.preventDefault(),e.returnValue=!1},Guacamole.Event.Target=function(){var e={};this.on=function(t,n){var a=e[t];a||(e[t]=a=[]),a.push(n)},this.onEach=function(e,t){e.forEach((function(e){this.on(e,t)}),this)},this.dispatch=function(t){t.invokeLegacyHandler(this);var n=e[t.type];if(n)for(var a=0;a<n.length;a++)n[a](t,this)},this.off=function(t,n){var a=e[t];if(!a)return!1;for(var o=0;o<a.length;o++)if(a[o]===n)return a.splice(o,1),!0;return!1},this.offEach=function(e,t){var n=!1;return e.forEach((function(e){n|=this.off(e,t)}),this),n}},(Guacamole=Guacamole||{}).InputSink=function(){var e=this,t=document.createElement("textarea");t.style.position="fixed",t.style.outline="none",t.style.border="none",t.style.margin="0",t.style.padding="0",t.style.height="0",t.style.width="0",t.style.left="0",t.style.bottom="0",t.style.resize="none",t.style.background="transparent",t.style.color="transparent",t.addEventListener("keypress",(function(e){t.value=""}),!1),t.addEventListener("compositionend",(function(e){e.data&&(t.value="")}),!1),t.addEventListener("input",(function(e){e.data&&!e.isComposing&&(t.value="")}),!1),t.addEventListener("focus",(function(){window.setTimeout((function(){t.click(),t.select()}),0)}),!0),this.focus=function(){window.setTimeout((function(){t.focus()}),0)},this.getElement=function(){return t},document.addEventListener("keydown",(function(t){var n=document.activeElement;if(n&&n!==document.body){var a=n.getBoundingClientRect();if(a.left+a.width>0&&a.top+a.height>0)return}e.focus()}),!0)},(Guacamole=Guacamole||{}).InputStream=function(e,t){var n=this;this.index=t,this.onblob=null,this.onend=null,this.sendAck=function(t,a){e.sendAck(n.index,t,a)}},(Guacamole=Guacamole||{}).IntegerPool=function(){var e=this,t=[];this.next_int=0,this.next=function(){return t.length>0?t.shift():e.next_int++},this.free=function(e){t.push(e)}},(Guacamole=Guacamole||{}).JSONReader=function(e){var t=this,n=new Guacamole.StringReader(e),a="";this.getLength=function(){return a.length},this.getJSON=function(){return JSON.parse(a)},n.ontext=function(e){a+=e,t.onprogress&&t.onprogress(e.length)},n.onend=function(){t.onend&&t.onend()},this.onprogress=null,this.onend=null},(Guacamole=Guacamole||{}).Keyboard=function(e){var t=this,n="_GUAC_KEYBOARD_HANDLED_BY_"+Guacamole.Keyboard._nextID++;this.onkeydown=null,this.onkeyup=null;var a={keyupUnreliable:!1,altIsTypableOnly:!1,capsLockKeyupUnreliable:!1};navigator&&navigator.platform&&(navigator.platform.match(/ipad|iphone|ipod/i)?a.keyupUnreliable=!0:navigator.platform.match(/^mac/i)&&(a.altIsTypableOnly=!0,a.capsLockKeyupUnreliable=!0));var o=function(e){var t=this;this.keyCode=e?e.which||e.keyCode:0,this.keyIdentifier=e&&e.keyIdentifier,this.key=e&&e.key,this.location=e?A(e):0,this.modifiers=e?Guacamole.Keyboard.ModifierState.fromKeyboardEvent(e):new Guacamole.Keyboard.ModifierState,this.timestamp=(new Date).getTime(),this.defaultPrevented=!1,this.keysym=null,this.reliable=!1,this.getAge=function(){return(new Date).getTime()-t.timestamp}},i=function(e){o.call(this,e),this.keysym=w(this.key,this.location)||G(this.keyCode,this.location),this.keyupReliable=!a.keyupUnreliable,this.keysym&&!y(this.keysym)&&(this.reliable=!0),!this.keysym&&T(this.keyCode,this.keyIdentifier)&&(this.keysym=w(this.keyIdentifier,this.location,this.modifiers.shift)),(this.modifiers.meta&&65511!==this.keysym&&65512!==this.keysym||65509===this.keysym&&a.capsLockKeyupUnreliable)&&(this.keyupReliable=!1);var t=!this.modifiers.ctrl&&!a.altIsTypableOnly;(!this.modifiers.alt&&this.modifiers.ctrl||t&&this.modifiers.alt||this.modifiers.meta||this.modifiers.hyper)&&(this.reliable=!0),m[this.keyCode]=this.keysym};i.prototype=new o;var r=function(e){o.call(this,e),this.keysym=S(this.keyCode),this.reliable=!0};r.prototype=new o;var s=function(e){o.call(this,e),this.keysym=G(this.keyCode,this.location)||w(this.key,this.location),t.pressed[this.keysym]||(this.keysym=m[this.keyCode]||this.keysym),this.reliable=!0};s.prototype=new o;var u=[],l={8:[65288],9:[65289],12:[65291,65291,65291,65461],13:[65293],16:[65505,65505,65506],17:[65507,65507,65508],18:[65513,65513,65027],19:[65299],20:[65509],27:[65307],32:[32],33:[65365,65365,65365,65465],34:[65366,65366,65366,65459],35:[65367,65367,65367,65457],36:[65360,65360,65360,65463],37:[65361,65361,65361,65460],38:[65362,65362,65362,65464],39:[65363,65363,65363,65462],40:[65364,65364,65364,65458],45:[65379,65379,65379,65456],46:[65535,65535,65535,65454],91:[65511],92:[65512],93:[65383],96:[65456],97:[65457],98:[65458],99:[65459],100:[65460],101:[65461],102:[65462],103:[65463],104:[65464],105:[65465],106:[65450],107:[65451],109:[65453],110:[65454],111:[65455],112:[65470],113:[65471],114:[65472],115:[65473],116:[65474],117:[65475],118:[65476],119:[65477],120:[65478],121:[65479],122:[65480],123:[65481],144:[65407],145:[65300],225:[65027]},c={Again:[65382],AllCandidates:[65341],Alphanumeric:[65328],Alt:[65513,65513,65027],Attn:[64782],AltGraph:[65027],ArrowDown:[65364],ArrowLeft:[65361],ArrowRight:[65363],ArrowUp:[65362],Backspace:[65288],CapsLock:[65509],Cancel:[65385],Clear:[65291],Convert:[65313],Copy:[64789],Crsel:[64796],CrSel:[64796],CodeInput:[65335],Compose:[65312],Control:[65507,65507,65508],ContextMenu:[65383],Delete:[65535],Down:[65364],End:[65367],Enter:[65293],EraseEof:[64774],Escape:[65307],Execute:[65378],Exsel:[64797],ExSel:[64797],F1:[65470],F2:[65471],F3:[65472],F4:[65473],F5:[65474],F6:[65475],F7:[65476],F8:[65477],F9:[65478],F10:[65479],F11:[65480],F12:[65481],F13:[65482],F14:[65483],F15:[65484],F16:[65485],F17:[65486],F18:[65487],F19:[65488],F20:[65489],F21:[65490],F22:[65491],F23:[65492],F24:[65493],Find:[65384],GroupFirst:[65036],GroupLast:[65038],GroupNext:[65032],GroupPrevious:[65034],FullWidth:null,HalfWidth:null,HangulMode:[65329],Hankaku:[65321],HanjaMode:[65332],Help:[65386],Hiragana:[65317],HiraganaKatakana:[65319],Home:[65360],Hyper:[65517,65517,65518],Insert:[65379],JapaneseHiragana:[65317],JapaneseKatakana:[65318],JapaneseRomaji:[65316],JunjaMode:[65336],KanaMode:[65325],KanjiMode:[65313],Katakana:[65318],Left:[65361],Meta:[65511,65511,65512],ModeChange:[65406],NumLock:[65407],PageDown:[65366],PageUp:[65365],Pause:[65299],Play:[64790],PreviousCandidate:[65342],PrintScreen:[65377],Redo:[65382],Right:[65363],RomanCharacters:null,Scroll:[65300],Select:[65376],Separator:[65452],Shift:[65505,65505,65506],SingleCandidate:[65340],Super:[65515,65515,65516],Tab:[65289],UIKeyInputDownArrow:[65364],UIKeyInputEscape:[65307],UIKeyInputLeftArrow:[65361],UIKeyInputRightArrow:[65363],UIKeyInputUpArrow:[65362],Up:[65362],Undo:[65381],Win:[65511,65511,65512],Zenkaku:[65320],ZenkakuHankaku:[65322]},h={65027:!0,65505:!0,65506:!0,65507:!0,65508:!0,65509:!0,65511:!0,65512:!0,65513:!0,65514:!0,65515:!0,65516:!0};this.modifiers=new Guacamole.Keyboard.ModifierState,this.pressed={};var d={},f={},m={},p=null,v=null,g=function(e,t){return e?e[t]||e[0]:null},y=function(e){return e>=0&&e<=255||16777216==(4294901760&e)};function w(e,t,n){if(!e)return null;var a,o=e.indexOf("U+");if(o>=0){var i=e.substring(o+2);a=String.fromCharCode(parseInt(i,16))}else{if(1!==e.length||3===t)return g(c[e],t);a=e}return!0===n?a=a.toUpperCase():!1===n&&(a=a.toLowerCase()),S(a.charCodeAt(0))}function S(e){return function(e){return e<=31||e>=127&&e<=159}(e)?65280|e:e>=0&&e<=255?e:e>=256&&e<=1114111?16777216|e:null}function G(e,t){return g(l[e],t)}var T=function(e,t){if(!t)return!1;var n=t.indexOf("U+");return-1===n||(e!==parseInt(t.substring(n+2),16)||(e>=65&&e<=90||e>=48&&e<=57))};this.press=function(e){if(null!==e){if(!t.pressed[e]&&(t.pressed[e]=!0,t.onkeydown)){var n=t.onkeydown(e);return f[e]=n,window.clearTimeout(p),window.clearInterval(v),h[e]||(p=window.setTimeout((function(){v=window.setInterval((function(){t.onkeyup(e),t.onkeydown(e)}),50)}),500)),n}return f[e]||!1}},this.release=function(e){t.pressed[e]&&(delete t.pressed[e],delete d[e],window.clearTimeout(p),window.clearInterval(v),null!==e&&t.onkeyup&&t.onkeyup(e))},this.type=function(e){for(var n=0;n<e.length;n++){var a=S(e.codePointAt?e.codePointAt(n):e.charCodeAt(n));t.press(a),t.release(a)}},this.reset=function(){for(var e in t.pressed)t.release(parseInt(e));u=[]};var E=function(e,n,a){var o,i=a.modifiers[e],r=t.modifiers[e];if(-1===n.indexOf(a.keysym))if(r&&!1===i)for(o=0;o<n.length;o++)t.release(n[o]);else if(!r&&i){for(o=0;o<n.length;o++)if(t.pressed[n[o]])return;var s=n[0];a.keysym&&(d[s]=!0),t.press(s)}},I=function(e){E("alt",[65513,65514,65027],e),E("shift",[65505,65506],e),E("ctrl",[65507,65508],e),E("meta",[65511,65512],e),E("hyper",[65515,65516],e),t.modifiers=e.modifiers},b=function(){for(var e in t.pressed)if(!d[e])return!1;return!0};function C(){var e,n=k();if(!n)return!1;do{e=n,n=k()}while(null!==n);return b()&&t.reset(),e.defaultPrevented}var k=function(){var e=u[0];if(!e)return null;if(!(e instanceof i))return e instanceof s&&!a.keyupUnreliable?(n=e.keysym)?(t.release(n),delete m[e.keyCode],e.defaultPrevented=!0,I(e),u.shift()):(t.reset(),e):u.shift();var n=null,o=[];if(65511===e.keysym||65512===e.keysym){if(1===u.length)return null;if(u[1].keysym!==e.keysym){if(!u[1].modifiers.meta)return u.shift()}else if(u[1]instanceof i)return u.shift()}if(e.reliable?(n=e.keysym,o=u.splice(0,1)):u[1]instanceof r?(n=u[1].keysym,o=u.splice(0,2)):u[1]&&(n=e.keysym,o=u.splice(0,1)),o.length>0){if(I(e),n){!function(e){t.modifiers.ctrl&&t.modifiers.alt&&(e>=65&&e<=90||e>=97&&e<=122||(e<=255||16777216==(4278190080&e))&&(t.release(65507),t.release(65508),t.release(65513),t.release(65514)))}(n);var l=!t.press(n);m[e.keyCode]=n,e.keyupReliable||t.release(n);for(var c=0;c<o.length;c++)o[c].defaultPrevented=l}return e}return null},A=function(e){return"location"in e?e.location:"keyLocation"in e?e.keyLocation:0},L=function(e){return!e[n]&&(e[n]=!0,!0)};this.listenTo=function(e){e.addEventListener("keydown",(function(e){if(t.onkeydown&&L(e)){var n=new i(e);229!==n.keyCode&&(u.push(n),C()&&e.preventDefault())}}),!0),e.addEventListener("keypress",(function(e){(t.onkeydown||t.onkeyup)&&L(e)&&(u.push(new r(e)),C()&&e.preventDefault())}),!0),e.addEventListener("keyup",(function(e){t.onkeyup&&L(e)&&(e.preventDefault(),u.push(new s(e)),C())}),!0);var n=function(n){(t.onkeydown||t.onkeyup)&&L(n)&&n.data&&!n.isComposing&&(e.removeEventListener("compositionend",a,!1),t.type(n.data))},a=function(a){(t.onkeydown||t.onkeyup)&&L(a)&&a.data&&(e.removeEventListener("input",n,!1),t.type(a.data))};e.addEventListener("input",n,!1),e.addEventListener("compositionend",a,!1)},e&&t.listenTo(e)},Guacamole.Keyboard._nextID=0,Guacamole.Keyboard.ModifierState=function(){this.shift=!1,this.ctrl=!1,this.alt=!1,this.meta=!1,this.hyper=!1},Guacamole.Keyboard.ModifierState.fromKeyboardEvent=function(e){var t=new Guacamole.Keyboard.ModifierState;return t.shift=e.shiftKey,t.ctrl=e.ctrlKey,t.alt=e.altKey,t.meta=e.metaKey,e.getModifierState&&(t.hyper=e.getModifierState("OS")||e.getModifierState("Super")||e.getModifierState("Hyper")||e.getModifierState("Win")),t},(Guacamole=Guacamole||{}).Layer=function(e,t){var n=this,a=document.createElement("canvas"),o=a.getContext("2d");o.save();var i=!0,r=!0,s=0,u={1:"destination-in",2:"destination-out",4:"source-in",6:"source-atop",8:"source-out",9:"destination-atop",10:"xor",11:"destination-over",12:"copy",14:"source-over",15:"lighter"},l=function(e,t){e=e||0,t=t||0;var r=64*Math.ceil(e/64),u=64*Math.ceil(t/64);if(a.width!==r||a.height!==u){var l=null;if(!i&&0!==a.width&&0!==a.height)(l=document.createElement("canvas")).width=Math.min(n.width,e),l.height=Math.min(n.height,t),l.getContext("2d").drawImage(a,0,0,l.width,l.height,0,0,l.width,l.height);var c=o.globalCompositeOperation;a.width=r,a.height=u,l&&o.drawImage(l,0,0,l.width,l.height,0,0,l.width,l.height),o.globalCompositeOperation=c,s=0,o.save()}else n.reset();n.width=e,n.height=t};function c(e,t,a,o){var i,r,s=a+e,u=o+t;i=s>n.width?s:n.width,r=u>n.height?u:n.height,n.resize(i,r)}this.autosize=!1,this.width=e,this.height=t,this.getCanvas=function(){return a},this.toCanvas=function(){var e=document.createElement("canvas");return e.width=n.width,e.height=n.height,e.getContext("2d").drawImage(n.getCanvas(),0,0),e},this.resize=function(e,t){e===n.width&&t===n.height||l(e,t)},this.drawImage=function(e,t,a){n.autosize&&c(e,t,a.width,a.height),o.drawImage(a,e,t),i=!1},this.transfer=function(e,t,a,r,s,u,l,h){var d=e.getCanvas();if(!(t>=d.width||a>=d.height)&&(t+r>d.width&&(r=d.width-t),a+s>d.height&&(s=d.height-a),0!==r&&0!==s)){n.autosize&&c(u,l,r,s);for(var f=e.getCanvas().getContext("2d").getImageData(t,a,r,s),m=o.getImageData(u,l,r,s),p=0;p<r*s*4;p+=4){var v=new Guacamole.Layer.Pixel(f.data[p],f.data[p+1],f.data[p+2],f.data[p+3]),g=new Guacamole.Layer.Pixel(m.data[p],m.data[p+1],m.data[p+2],m.data[p+3]);h(v,g),m.data[p]=g.red,m.data[p+1]=g.green,m.data[p+2]=g.blue,m.data[p+3]=g.alpha}o.putImageData(m,u,l),i=!1}},this.put=function(e,t,a,r,s,u,l){var h=e.getCanvas();if(!(t>=h.width||a>=h.height)&&(t+r>h.width&&(r=h.width-t),a+s>h.height&&(s=h.height-a),0!==r&&0!==s)){n.autosize&&c(u,l,r,s);var d=e.getCanvas().getContext("2d").getImageData(t,a,r,s);o.putImageData(d,u,l),i=!1}},this.copy=function(e,t,a,r,s,u,l){var h=e.getCanvas();t>=h.width||a>=h.height||(t+r>h.width&&(r=h.width-t),a+s>h.height&&(s=h.height-a),0!==r&&0!==s&&(n.autosize&&c(u,l,r,s),o.drawImage(h,t,a,r,s,u,l,r,s),i=!1))},this.moveTo=function(e,t){r&&(o.beginPath(),r=!1),n.autosize&&c(e,t,0,0),o.moveTo(e,t)},this.lineTo=function(e,t){r&&(o.beginPath(),r=!1),n.autosize&&c(e,t,0,0),o.lineTo(e,t)},this.arc=function(e,t,a,i,s,u){r&&(o.beginPath(),r=!1),n.autosize&&c(e,t,0,0),o.arc(e,t,a,i,s,u)},this.curveTo=function(e,t,a,i,s,u){r&&(o.beginPath(),r=!1),n.autosize&&c(s,u,0,0),o.bezierCurveTo(e,t,a,i,s,u)},this.close=function(){o.closePath(),r=!0},this.rect=function(e,t,a,i){r&&(o.beginPath(),r=!1),n.autosize&&c(e,t,a,i),o.rect(e,t,a,i)},this.clip=function(){o.clip(),r=!0},this.strokeColor=function(e,t,n,a,s,u,l){o.lineCap=e,o.lineJoin=t,o.lineWidth=n,o.strokeStyle="rgba("+a+","+s+","+u+","+l/255+")",o.stroke(),i=!1,r=!0},this.fillColor=function(e,t,n,a){o.fillStyle="rgba("+e+","+t+","+n+","+a/255+")",o.fill(),i=!1,r=!0},this.strokeLayer=function(e,t,n,a){o.lineCap=e,o.lineJoin=t,o.lineWidth=n,o.strokeStyle=o.createPattern(a.getCanvas(),"repeat"),o.stroke(),i=!1,r=!0},this.fillLayer=function(e){o.fillStyle=o.createPattern(e.getCanvas(),"repeat"),o.fill(),i=!1,r=!0},this.push=function(){o.save(),s++},this.pop=function(){s>0&&(o.restore(),s--)},this.reset=function(){for(;s>0;)o.restore(),s--;o.restore(),o.save(),o.beginPath(),r=!1},this.setTransform=function(e,t,n,a,i,r){o.setTransform(e,t,n,a,i,r)},this.transform=function(e,t,n,a,i,r){o.transform(e,t,n,a,i,r)},this.setChannelMask=function(e){o.globalCompositeOperation=u[e]},this.setMiterLimit=function(e){o.miterLimit=e},l(e,t),a.style.zIndex=-1},Guacamole.Layer.ROUT=2,Guacamole.Layer.ATOP=6,Guacamole.Layer.XOR=10,Guacamole.Layer.ROVER=11,Guacamole.Layer.OVER=14,Guacamole.Layer.PLUS=15,Guacamole.Layer.RIN=1,Guacamole.Layer.IN=4,Guacamole.Layer.OUT=8,Guacamole.Layer.RATOP=9,Guacamole.Layer.SRC=12,Guacamole.Layer.Pixel=function(e,t,n,a){this.red=e,this.green=t,this.blue=n,this.alpha=a},(Guacamole=Guacamole||{}).Mouse=function(e){Guacamole.Mouse.Event.Target.call(this);var t=this;this.touchMouseThreshold=3,this.scrollThreshold=53,this.PIXELS_PER_LINE=18,this.PIXELS_PER_PAGE=16*this.PIXELS_PER_LINE;var n=[Guacamole.Mouse.State.Buttons.LEFT,Guacamole.Mouse.State.Buttons.MIDDLE,Guacamole.Mouse.State.Buttons.RIGHT],a=0,o=0;function i(){a=t.touchMouseThreshold}function r(e){var n=e.deltaY||-e.wheelDeltaY||-e.wheelDelta;if(n?1===e.deltaMode?n=e.deltaY*t.PIXELS_PER_LINE:2===e.deltaMode&&(n=e.deltaY*t.PIXELS_PER_PAGE):n=e.detail*t.PIXELS_PER_LINE,(o+=n)<=-t.scrollThreshold){do{t.click(Guacamole.Mouse.State.Buttons.UP),o+=t.scrollThreshold}while(o<=-t.scrollThreshold);o=0}if(o>=t.scrollThreshold){do{t.click(Guacamole.Mouse.State.Buttons.DOWN),o-=t.scrollThreshold}while(o>=t.scrollThreshold);o=0}Guacamole.Event.DOMEvent.cancelEvent(e)}e.addEventListener("contextmenu",(function(e){Guacamole.Event.DOMEvent.cancelEvent(e)}),!1),e.addEventListener("mousemove",(function(n){if(a)return Guacamole.Event.DOMEvent.cancelEvent(n),void a--;t.move(Guacamole.Position.fromClientPosition(e,n.clientX,n.clientY),n)}),!1),e.addEventListener("mousedown",(function(e){if(a)Guacamole.Event.DOMEvent.cancelEvent(e);else{var o=n[e.button];o&&t.press(o,e)}}),!1),e.addEventListener("mouseup",(function(e){if(a)Guacamole.Event.DOMEvent.cancelEvent(e);else{var o=n[e.button];o&&t.release(o,e)}}),!1),e.addEventListener("mouseout",(function(n){n||(n=window.event);for(var a=n.relatedTarget||n.toElement;a;){if(a===e)return;a=a.parentNode}t.reset(n),t.out(n)}),!1),e.addEventListener("selectstart",(function(e){Guacamole.Event.DOMEvent.cancelEvent(e)}),!1),e.addEventListener("touchmove",i,!1),e.addEventListener("touchstart",i,!1),e.addEventListener("touchend",i,!1),e.addEventListener("DOMMouseScroll",r,!1),e.addEventListener("mousewheel",r,!1),e.addEventListener("wheel",r,!1);var s=function(){var e=document.createElement("div");if(!("cursor"in e.style))return!1;try{e.style.cursor="url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEX///+nxBvIAAAACklEQVQI12NgAAAAAgAB4iG8MwAAAABJRU5ErkJggg==) 0 0, auto"}catch(e){return!1}return/\burl\([^()]*\)\s+0\s+0\b/.test(e.style.cursor||"")}();this.setCursor=function(t,n,a){if(s){var o=t.toDataURL("image/png");return e.style.cursor="url("+o+") "+n+" "+a+", auto",!0}return!1}},Guacamole.Mouse.State=function(e){e=arguments.length>1?function(e,t,n,a,o,i,r){return{x:e,y:t,left:n,middle:a,right:o,up:i,down:r}}.apply(this,arguments):e||{},Guacamole.Position.call(this,e),this.left=e.left||!1,this.middle=e.middle||!1,this.right=e.right||!1,this.up=e.up||!1,this.down=e.down||!1},Guacamole.Mouse.State.Buttons={LEFT:"left",MIDDLE:"middle",RIGHT:"right",UP:"up",DOWN:"down"},Guacamole.Mouse.Event=function(e,t,n){Guacamole.Event.DOMEvent.call(this,e,n);var a="on"+this.type;this.state=t,this.invokeLegacyHandler=function(e){e[a]&&(this.preventDefault(),this.stopPropagation(),e[a](this.state))}},Guacamole.Mouse.Event.Target=function(){Guacamole.Event.Target.call(this),this.currentState=new Guacamole.Mouse.State,this.press=function(e,t){this.currentState[e]||(this.currentState[e]=!0,this.dispatch(new Guacamole.Mouse.Event("mousedown",this.currentState,t)))},this.release=function(e,t){this.currentState[e]&&(this.currentState[e]=!1,this.dispatch(new Guacamole.Mouse.Event("mouseup",this.currentState,t)))},this.click=function(e,t){this.press(e,t),this.release(e,t)},this.move=function(e,t){this.currentState.x===e.x&&this.currentState.y===e.y||(this.currentState.x=e.x,this.currentState.y=e.y,this.dispatch(new Guacamole.Mouse.Event("mousemove",this.currentState,t)))},this.out=function(e){this.dispatch(new Guacamole.Mouse.Event("mouseout",this.currentState,e))},this.reset=function(e){for(var t in Guacamole.Mouse.State.Buttons)this.release(Guacamole.Mouse.State.Buttons[t],e)}},Guacamole.Mouse.Touchpad=function(e){Guacamole.Mouse.Event.Target.call(this);var t=this;this.scrollThreshold=20*(window.devicePixelRatio||1),this.clickTimingThreshold=250,this.clickMoveThreshold=10*(window.devicePixelRatio||1),this.currentState=new Guacamole.Mouse.State;var n=0,a=0,o=0,i=0,r=0,s={1:"left",2:"right",3:"middle"},u=!1,l=null;e.addEventListener("touchend",(function(e){if(e.preventDefault(),u&&0===e.touches.length){var a=(new Date).getTime(),o=s[n];t.currentState[o]&&(t.release(o,e),l&&(window.clearTimeout(l),l=null)),a-i<=t.clickTimingThreshold&&r<t.clickMoveThreshold&&(t.press(o,e),l=window.setTimeout((function(){t.release(o,e),u=!1}),t.clickTimingThreshold)),l||(u=!1)}}),!1),e.addEventListener("touchstart",(function(e){if(e.preventDefault(),n=Math.min(e.touches.length,3),l&&(window.clearTimeout(l),l=null),!u){u=!0;var t=e.touches[0];a=t.clientX,o=t.clientY,i=(new Date).getTime(),r=0}}),!1),e.addEventListener("touchmove",(function(s){s.preventDefault();var u=s.touches[0],l=u.clientX-a,c=u.clientY-o;if(r+=Math.abs(l)+Math.abs(c),1===n){var h=1+r/((new Date).getTime()-i),d=new Guacamole.Position(t.currentState);d.x+=l*h,d.y+=c*h,d.x=Math.min(Math.max(0,d.x),e.offsetWidth-1),d.y=Math.min(Math.max(0,d.y),e.offsetHeight-1),t.move(d,s),a=u.clientX,o=u.clientY}else if(2===n){var f;if(Math.abs(c)>=t.scrollThreshold)f=c>0?"down":"up",t.click(f,s),a=u.clientX,o=u.clientY}}),!1)},Guacamole.Mouse.Touchscreen=function(e){Guacamole.Mouse.Event.Target.call(this);var t=this,n=!1,a=null,o=null,i=null,r=null;function s(e){var n=e.touches[0]||e.changedTouches[0],i=n.clientX-a,r=n.clientY-o;return Math.sqrt(i*i+r*r)>=t.clickMoveThreshold}function u(){window.clearTimeout(i),window.clearTimeout(r),n=!1}this.scrollThreshold=20*(window.devicePixelRatio||1),this.clickTimingThreshold=250,this.clickMoveThreshold=16*(window.devicePixelRatio||1),this.longPressThreshold=500,e.addEventListener("touchend",(function(a){if(n)if(0===a.touches.length&&1===a.changedTouches.length){if(window.clearTimeout(r),t.release(Guacamole.Mouse.State.Buttons.LEFT,a),!s(a)&&(a.preventDefault(),!t.currentState.left)){var o=a.changedTouches[0];t.move(Guacamole.Position.fromClientPosition(e,o.clientX,o.clientY)),t.press(Guacamole.Mouse.State.Buttons.LEFT,a),i=window.setTimeout((function(){t.release(Guacamole.Mouse.State.Buttons.LEFT,a),u()}),t.clickTimingThreshold)}}else u()}),!1),e.addEventListener("touchstart",(function(s){1===s.touches.length?(s.preventDefault(),function(e){var t=e.touches[0];n=!0,a=t.clientX,o=t.clientY}(s),window.clearTimeout(i),r=window.setTimeout((function(){var n=s.touches[0];t.move(Guacamole.Position.fromClientPosition(e,n.clientX,n.clientY)),t.click(Guacamole.Mouse.State.Buttons.RIGHT,s),u()}),t.longPressThreshold)):u()}),!1),e.addEventListener("touchmove",(function(a){if(n)if(s(a)&&window.clearTimeout(r),1===a.touches.length){if(t.currentState.left){a.preventDefault();var o=a.touches[0];t.move(Guacamole.Position.fromClientPosition(e,o.clientX,o.clientY),a)}}else u()}),!1)},(Guacamole=(Guacamole=Guacamole||{})||{}).Object=function(e,t){var n=this,a={};this.index=t,this.onbody=function(e,t,n){var o=function(e){var t=a[e];if(!t)return null;var n=t.shift();return 0===t.length&&delete a[e],n}(n);o&&o(e,t)},this.onundefine=null,this.requestInputStream=function(t,o){o&&function(e,t){var n=a[e];n||(n=[],a[e]=n),n.push(t)}(t,o),e.requestObjectInputStream(n.index,t)},this.createOutputStream=function(t,a){return e.createObjectOutputStream(n.index,t,a)}},Guacamole.Object.ROOT_STREAM="/",Guacamole.Object.STREAM_INDEX_MIMETYPE="application/vnd.glyptodon.guacamole.stream-index+json",(Guacamole=Guacamole||{}).OnScreenKeyboard=function(e){var t=this,n={},a={},o=[],i=function(e,t){e.classList?e.classList.add(t):e.className+=" "+t},r=function(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(/([^ ]+)[ ]*/g,(function(e,n){return n===t?"":e}))},s=0,u=function(e,t,n,a){this.width=t,this.height=n,this.scale=function(o){e.style.width=t*o+"px",e.style.height=n*o+"px",a&&(e.style.lineHeight=n*o+"px",e.style.fontSize=o+"px")}},l=function(e){for(var t=0;t<e.length;t++){if(!(e[t]in n))return!1}return!0},c=function(e){var n=t.keys[e];if(!n)return null;for(var a=n.length-1;a>=0;a--){var o=n[a];if(l(o.requires))return o}return null},h=function(e,o){if(!a[e]){i(o,"guac-keyboard-pressed");var s=c(e);if(s.modifier){var u="guac-keyboard-modifier-"+p(s.modifier),l=n[s.modifier];void 0===l?(i(f,u),n[s.modifier]=s.keysym,s.keysym&&t.onkeydown&&t.onkeydown(s.keysym)):(r(f,u),delete n[s.modifier],l&&t.onkeyup&&t.onkeyup(l))}else t.onkeydown&&t.onkeydown(s.keysym);a[e]=!0}},d=function(e,n){if(a[e]){r(n,"guac-keyboard-pressed");var o=c(e);!o.modifier&&t.onkeyup&&t.onkeyup(o.keysym),a[e]=!1}},f=document.createElement("div");f.className="guac-keyboard",f.onselectstart=f.onmousemove=f.onmouseup=f.onmousedown=function(e){return s&&s--,e.stopPropagation(),!1},this.touchMouseThreshold=3,this.onkeydown=null,this.onkeyup=null,this.layout=new Guacamole.OnScreenKeyboard.Layout(e),this.getElement=function(){return f},this.resize=function(e){for(var n=Math.floor(10*e/t.layout.width)/10,a=0;a<o.length;a++){o[a].scale(n)}};var m=function(e,t){if(t instanceof Array){for(var n=[],a=0;a<t.length;a++)n.push(new Guacamole.OnScreenKeyboard.Key(t[a],e));return n}return"number"==typeof t?[new Guacamole.OnScreenKeyboard.Key({name:e,keysym:t})]:"string"==typeof t?[new Guacamole.OnScreenKeyboard.Key({name:e,title:t})]:[new Guacamole.OnScreenKeyboard.Key(t,e)]};this.keys=function(t){var n={};for(var a in e.keys)n[a]=m(a,t[a]);return n}(e.keys);var p=function(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[^A-Za-z0-9]+/g,"-").toLowerCase()};!function e(n,a,r){var l,c=document.createElement("div");if(r&&i(c,"guac-keyboard-"+p(r)),a instanceof Array)for(i(c,"guac-keyboard-group"),l=0;l<a.length;l++)e(c,a[l]);else if(a instanceof Object){i(c,"guac-keyboard-group");var f=Object.keys(a).sort();for(l=0;l<f.length;l++){r=f[l];e(c,a[r],r)}}else if("number"==typeof a)i(c,"guac-keyboard-gap"),o.push(new u(c,a,a));else if("string"==typeof a){var m=a;1===m.length&&(m="0x"+m.charCodeAt(0).toString(16)),i(c,"guac-keyboard-key-container");var v=document.createElement("div");v.className="guac-keyboard-key guac-keyboard-key-"+p(m);var g=t.keys[a];if(g)for(l=0;l<g.length;l++){var y=g[l],w=document.createElement("div");w.className="guac-keyboard-cap",w.textContent=y.title;for(var S=0;S<y.requires.length;S++){var G=y.requires[S];i(w,"guac-keyboard-requires-"+p(G)),i(v,"guac-keyboard-uses-"+p(G))}v.appendChild(w)}c.appendChild(v),o.push(new u(c,t.layout.keyWidths[a]||1,1,!0));var T=function(e){e.preventDefault(),0===s&&d(a,v)};v.addEventListener("touchstart",(function(e){e.preventDefault(),s=t.touchMouseThreshold,h(a,v)}),!0),v.addEventListener("touchend",(function(e){e.preventDefault(),s=t.touchMouseThreshold,d(a,v)}),!0),v.addEventListener("mousedown",(function(e){e.preventDefault(),0===s&&h(a,v)}),!0),v.addEventListener("mouseup",T,!0),v.addEventListener("mouseout",T,!0)}n.appendChild(c)}(f,e.layout)},Guacamole.OnScreenKeyboard.Layout=function(e){this.language=e.language,this.type=e.type,this.keys=e.keys,this.layout=e.layout,this.width=e.width,this.keyWidths=e.keyWidths||{}},Guacamole.OnScreenKeyboard.Key=function(e,t){this.name=t||e.name,this.title=e.title||this.name,this.keysym=e.keysym||function(e){if(!e||1!==e.length)return null;var t=e.charCodeAt(0);return t>=0&&t<=255?t:t>=256&&t<=1114111?16777216|t:null}(this.title),this.modifier=e.modifier,this.requires=e.requires||[]},(Guacamole=Guacamole||{}).OutputStream=function(e,t){var n=this;this.index=t,this.onack=null,this.sendBlob=function(t){e.sendBlob(n.index,t)},this.sendEnd=function(){e.endStream(n.index)}},(Guacamole=Guacamole||{}).Parser=function(){var e=this,t="",n=[],a=-1,o=0;this.receive=function(i){for(o>4096&&a>=o&&(t=t.substring(o),a-=o,o=0),t+=i;a<t.length;){if(a>=o){var r=t.substring(o,a),s=t.substring(a,a+1);if(n.push(r),";"==s){var u=n.shift();null!=e.oninstruction&&e.oninstruction(u,n),n.length=0}else if(","!=s)throw new Error("Illegal terminator.");o=a+1}var l=t.indexOf(".",o);if(-1==l){o=t.length;break}var c=parseInt(t.substring(a+1,l));if(isNaN(c))throw new Error("Non-numeric character in element length.");a=(o=l+1)+c}},this.oninstruction=null},(Guacamole=Guacamole||{}).Position=function(e){e=e||{},this.x=e.x||0,this.y=e.y||0,this.fromClientPosition=function(e,t,n){this.x=t-e.offsetLeft,this.y=n-e.offsetTop;for(var a=e.offsetParent;a&&a!==document.body;)this.x-=a.offsetLeft-a.scrollLeft,this.y-=a.offsetTop-a.scrollTop,a=a.offsetParent;if(a){var o=document.body.scrollLeft||document.documentElement.scrollLeft,i=document.body.scrollTop||document.documentElement.scrollTop;this.x-=a.offsetLeft-o,this.y-=a.offsetTop-i}}},Guacamole.Position.fromClientPosition=function(e,t,n){var a=new Guacamole.Position;return a.fromClientPosition(e,t,n),a},(Guacamole=Guacamole||{}).RawAudioFormat=function(e){this.bytesPerSample=e.bytesPerSample,this.channels=e.channels,this.rate=e.rate},Guacamole.RawAudioFormat.parse=function(e){var t,n=null,a=1;if("audio/L8;"===e.substring(0,9))e=e.substring(9),t=1;else{if("audio/L16;"!==e.substring(0,10))return null;e=e.substring(10),t=2}for(var o=e.split(","),i=0;i<o.length;i++){var r=o[i],s=r.indexOf("=");if(-1===s)return null;var u=r.substring(0,s),l=r.substring(s+1);switch(u){case"channels":a=parseInt(l);break;case"rate":n=parseInt(l);break;default:return null}}return null===n?null:new Guacamole.RawAudioFormat({bytesPerSample:t,channels:a,rate:n})},(Guacamole=Guacamole||{}).SessionRecording=function(e){var t,n=this,a=null,o=262144,i=[],r=0,s=new Guacamole.SessionRecording._PlaybackTunnel,u=new Guacamole.Client(s),l=-1,c=null,h=null,d=null,f=0,m=0,p=!1,v=null,g=function(e,a,i){if(!p||e!==t){var r=new Guacamole.Parser;r.oninstruction=a;var s=0,u=new FileReader,l=function(){if(!p||e!==t){if(2===u.readyState)try{r.receive(u.result)}catch(e){return void(n.onerror&&n.onerror(e.message))}if(s>=e.size)i&&i();else{var a=e.slice(s,s+o);s+=a.size,u.readAsText(a)}}};u.onload=l,l()}},y=function(e){for(var t=e.length,n=t+3;t>=10;)n++,t=Math.floor(t/10);return n};u.connect(),u.getDisplay().showCursor(!1);var w=function(e,t){m+=y(e);for(var a=0;a<t.length;a++)m+=y(t[a]);if("sync"===e){var o=parseInt(t[0]),s=new Guacamole.SessionRecording._Frame(o,f,m);i.push(s),f=m,(1===i.length||m-i[r].start>=16384&&o-i[r].timestamp>=5e3)&&(s.keyframe=!0,r=i.length-1),n.onprogress&&n.onprogress(n.getDuration(),m)}},S=function(){n.onload&&n.onload()};if(e instanceof Blob)g(t,w,S);else{a=e,t=new Blob;var G=!1,T="";a.oninstruction=function(e,n){T+=e.length+"."+e,n.forEach((function(e){T+=","+e.length+"."+e})),(T+=";").length>=o&&(t=new Blob([t,T]),T=""),w(e,n)},a.onerror=function(e){G=!0,n.onerror&&n.onerror(e.message)},a.onstatechange=function(e){e===Guacamole.Tunnel.State.CLOSED&&(T.length&&(t=new Blob([t,T]),T=""),G||S())}}var E=function(e){return 0===i.length?0:e-i[0].timestamp},I=function e(t,n,a){if(t===n)return t;var o=Math.floor((t+n)/2),r=E(i[o].timestamp);return a<r&&o>t?e(t,o-1,a):a>r&&o<n?e(o+1,n,a):o},b=function(e,a,o){C();for(var r=d={aborted:!1},c=e,h=function o(){n.onseek&&l>c&&n.onseek(E(i[l].timestamp),l-c,e-c),r.aborted||(l<e?function(e,n){var a=i[e];g(t.slice(a.start,a.end),(function(e,t){s.receiveInstruction(e,t)}),(function(){a.keyframe&&!a.clientState&&u.exportState((function(e){a.clientState=new Blob([JSON.stringify(e)])})),l=e,n&&n()}))}(l+1,o):a())},f=function(){var e=o?Math.max(o-(new Date).getTime(),0):0;e?window.setTimeout(h,e):h()};c>=0;c--){var m=i[c];if(c===l)break;if(m.clientState)return void m.clientState.text().then((function(e){u.importState(JSON.parse(e)),l=c,f()}))}f()},C=function(){d&&(d.aborted=!0,d=null)},k=function e(){if(l+1<i.length){var t=i[l+1].timestamp-c+h;b(l+1,(function(){e()}),t)}else n.pause()};this.onload=null,this.onerror=null,this.onabort=null,this.onprogress=null,this.onplay=null,this.onpause=null,this.onseek=null,this.connect=function(e){a&&a.connect(e)},this.disconnect=function(){a&&a.disconnect()},this.abort=function(){p||(p=!0,n.onabort&&n.onabort(),a&&a.disconnect())},this.getDisplay=function(){return u.getDisplay()},this.isPlaying=function(){return!!c},this.getPosition=function(){return-1===l?0:E(i[l].timestamp)},this.getDuration=function(){return 0===i.length?0:E(i[i.length-1].timestamp)},this.play=function(){if(!n.isPlaying()&&l+1<i.length){n.onplay&&n.onplay();var e=i[l+1];c=e.timestamp,h=(new Date).getTime(),k()}},this.seek=function(e,t){if(0!==i.length){n.cancel();var a=n.isPlaying();n.pause(),v=function(){v=null,a&&(n.play(),a=null),t&&t()},b(I(0,i.length-1,e),v)}},this.cancel=function(){v&&(C(),v())},this.pause=function(){C(),n.isPlaying()&&(n.onpause&&n.onpause(),c=null,h=null)}},Guacamole.SessionRecording._Frame=function(e,t,n){this.keyframe=!1,this.timestamp=e,this.start=t,this.end=n,this.clientState=null},Guacamole.SessionRecording._PlaybackTunnel=function(){var e=this;this.connect=function(e){},this.sendMessage=function(e){},this.disconnect=function(){},this.receiveInstruction=function(t,n){e.oninstruction&&e.oninstruction(t,n)}},(Guacamole=Guacamole||{}).Status=function(e,t){var n=this;this.code=e,this.message=t,this.isError=function(){return n.code<0||n.code>255}},Guacamole.Status.Code={SUCCESS:0,UNSUPPORTED:256,SERVER_ERROR:512,SERVER_BUSY:513,UPSTREAM_TIMEOUT:514,UPSTREAM_ERROR:515,RESOURCE_NOT_FOUND:516,RESOURCE_CONFLICT:517,RESOURCE_CLOSED:518,UPSTREAM_NOT_FOUND:519,UPSTREAM_UNAVAILABLE:520,SESSION_CONFLICT:521,SESSION_TIMEOUT:522,SESSION_CLOSED:523,CLIENT_BAD_REQUEST:768,CLIENT_UNAUTHORIZED:769,CLIENT_FORBIDDEN:771,CLIENT_TIMEOUT:776,CLIENT_OVERRUN:781,CLIENT_BAD_TYPE:783,CLIENT_TOO_MANY:797},Guacamole.Status.Code.fromHTTPCode=function(e){switch(e){case 400:return Guacamole.Status.Code.CLIENT_BAD_REQUEST;case 403:return Guacamole.Status.Code.CLIENT_FORBIDDEN;case 404:return Guacamole.Status.Code.RESOURCE_NOT_FOUND;case 429:return Guacamole.Status.Code.CLIENT_TOO_MANY;case 503:return Guacamole.Status.Code.SERVER_BUSY}return Guacamole.Status.Code.SERVER_ERROR},Guacamole.Status.Code.fromWebSocketCode=function(e){switch(e){case 1e3:return Guacamole.Status.Code.SUCCESS;case 1006:case 1015:return Guacamole.Status.Code.UPSTREAM_NOT_FOUND;case 1001:case 1012:case 1013:case 1014:return Guacamole.Status.Code.UPSTREAM_UNAVAILABLE}return Guacamole.Status.Code.SERVER_ERROR},(Guacamole=Guacamole||{}).StringReader=function(e){var t=this,n=new Guacamole.UTF8Parser,a=new Guacamole.ArrayBufferReader(e);a.ondata=function(e){var a=n.decode(e);t.ontext&&t.ontext(a)},a.onend=function(){t.onend&&t.onend()},this.ontext=null,this.onend=null},(Guacamole=Guacamole||{}).StringWriter=function(e){var t=this,n=new Guacamole.ArrayBufferWriter(e),a=new Uint8Array(8192),o=0;function i(e){var t,n;if(e<=127)t=0,n=1;else if(e<=2047)t=192,n=2;else if(e<=65535)t=224,n=3;else{if(!(e<=2097151))return void i(65533);t=240,n=4}!function(e){if(o+e>=a.length){var t=new Uint8Array(2*(o+e));t.set(a),a=t}o+=e}(n);for(var r=o-1,s=1;s<n;s++)a[r--]=128|63&e,e>>=6;a[r]=t|e}n.onack=function(e){t.onack&&t.onack(e)},this.sendText=function(e){e.length&&n.sendData(function(e){for(var t=0;t<e.length;t++)i(e.charCodeAt(t));if(o>0){var n=a.subarray(0,o);return o=0,n}}(e))},this.sendEnd=function(){n.sendEnd()},this.onack=null},(Guacamole=Guacamole||{}).Touch=function(e){Guacamole.Event.Target.call(this);var t=this,n=Math.floor(16*window.devicePixelRatio);this.touches={},this.activeTouches=0,e.addEventListener("touchstart",(function(a){for(var o=0;o<a.changedTouches.length;o++){var i=a.changedTouches[o],r=i.identifier;if(!t.touches[r]){var s=t.touches[r]=new Guacamole.Touch.State({id:r,radiusX:i.radiusX||n,radiusY:i.radiusY||n,angle:i.angle||0,force:i.force||1});t.activeTouches++,s.fromClientPosition(e,i.clientX,i.clientY),t.dispatch(new Guacamole.Touch.Event("touchmove",a,s))}}}),!1),e.addEventListener("touchmove",(function(a){for(var o=0;o<a.changedTouches.length;o++){var i=a.changedTouches[o],r=i.identifier,s=t.touches[r];s&&(i.force&&(s.force=i.force),s.angle=i.angle||0,s.radiusX=i.radiusX||n,s.radiusY=i.radiusY||n,s.fromClientPosition(e,i.clientX,i.clientY),t.dispatch(new Guacamole.Touch.Event("touchmove",a,s)))}}),!1),e.addEventListener("touchend",(function(n){for(var a=0;a<n.changedTouches.length;a++){var o=n.changedTouches[a],i=o.identifier,r=t.touches[i];r&&(delete t.touches[i],t.activeTouches--,r.force=0,r.fromClientPosition(e,o.clientX,o.clientY),t.dispatch(new Guacamole.Touch.Event("touchend",n,r)))}}),!1)},Guacamole.Touch.State=function(e){e=e||{},Guacamole.Position.call(this,e),this.id=e.id||0,this.radiusX=e.radiusX||0,this.radiusY=e.radiusY||0,this.angle=e.angle||0,this.force=e.force||1},Guacamole.Touch.Event=function(e,t,n){Guacamole.Event.DOMEvent.call(this,e,[t]),this.state=n},(Guacamole=Guacamole||{}).Tunnel=function(){this.connect=function(e){},this.disconnect=function(){},this.sendMessage=function(e){},this.setState=function(e){e!==this.state&&(this.state=e,this.onstatechange&&this.onstatechange(e))},this.setUUID=function(e){this.uuid=e,this.onuuid&&this.onuuid(e)},this.isConnected=function(){return this.state===Guacamole.Tunnel.State.OPEN||this.state===Guacamole.Tunnel.State.UNSTABLE},this.state=Guacamole.Tunnel.State.CLOSED,this.receiveTimeout=15e3,this.unstableThreshold=1500,this.uuid=null,this.onuuid=null,this.onerror=null,this.onstatechange=null,this.oninstruction=null},Guacamole.Tunnel.INTERNAL_DATA_OPCODE="",Guacamole.Tunnel.State={CONNECTING:0,OPEN:1,CLOSED:2,UNSTABLE:3},Guacamole.HTTPTunnel=function(e,t,n){var a=this,o=e+"?connect",i=e+"?read:",r=e+"?write:",s=1,u=0,l=s,c=!1,h="",d=!!t,f=null,m=null,p=null,v=n||{},g="Guacamole-Tunnel-Token",y=null;function w(e,t){for(var n in t)e.setRequestHeader(n,t[n])}var S=function(){window.clearTimeout(f),window.clearTimeout(m),a.state===Guacamole.Tunnel.State.UNSTABLE&&a.setState(Guacamole.Tunnel.State.OPEN),f=window.setTimeout((function(){G(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_TIMEOUT,"Server timeout."))}),a.receiveTimeout),m=window.setTimeout((function(){a.setState(Guacamole.Tunnel.State.UNSTABLE)}),a.unstableThreshold)};function G(e){window.clearTimeout(f),window.clearTimeout(m),window.clearInterval(p),a.state!==Guacamole.Tunnel.State.CLOSED&&(e.code!==Guacamole.Status.Code.SUCCESS&&a.onerror&&(a.state!==Guacamole.Tunnel.State.CONNECTING&&e.code===Guacamole.Status.Code.RESOURCE_NOT_FOUND||a.onerror(e)),c=!1,a.setState(Guacamole.Tunnel.State.CLOSED))}function T(){if(a.isConnected())if(h.length>0){c=!0;var e=new XMLHttpRequest;e.open("POST",r+a.uuid),e.withCredentials=d,w(e,v),e.setRequestHeader("Content-type","application/octet-stream"),e.setRequestHeader(g,y),e.onreadystatechange=function(){4===e.readyState&&(S(),200!==e.status?E(e):T())},e.send(h),h=""}else c=!1}function E(e){var t=parseInt(e.getResponseHeader("Guacamole-Status-Code"));if(t){var n=e.getResponseHeader("Guacamole-Error-Message");G(new Guacamole.Status(t,n))}else e.status?G(new Guacamole.Status(Guacamole.Status.Code.fromHTTPCode(e.status),e.statusText)):G(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_NOT_FOUND))}function I(e){var t=null,n=null,o=0,i=-1,r=0,c=new Array;function h(){if(a.isConnected()){if(!(e.readyState<2)){var o;try{o=e.status}catch(e){o=200}if(n||200!==o||(n=C()),3===e.readyState||4===e.readyState){if(S(),l===s&&(3!==e.readyState||t?4===e.readyState&&t&&clearInterval(t):t=setInterval(h,30)),0===e.status)return void a.disconnect();if(200!==e.status)return void E(e);var u;try{u=e.responseText}catch(e){return}for(;i<u.length;){if(i>=r){var d=u.substring(r,i),f=u.substring(i,i+1);if(c.push(d),";"===f){var m=c.shift();a.oninstruction&&a.oninstruction(m,c),c.length=0}r=i+1}var p=u.indexOf(".",r);if(-1===p){r=u.length;break}var v=parseInt(u.substring(i+1,p));if(0===v){t&&clearInterval(t),e.onreadystatechange=null,e.abort(),n&&I(n);break}i=(r=p+1)+v}}}}else null!==t&&clearInterval(t)}e.onreadystatechange=l===s?function(){3===e.readyState&&++o>=2&&(l=u,e.onreadystatechange=h),h()}:h,h()}this.sendMessage=function(){if(a.isConnected()&&0!==arguments.length){for(var e=n(arguments[0]),t=1;t<arguments.length;t++)e+=","+n(arguments[t]);h+=e+=";",c||T()}function n(e){var t=new String(e);return t.length+"."+t}};var b=0;function C(){var e=new XMLHttpRequest;return e.open("GET",i+a.uuid+":"+b++),e.setRequestHeader(g,y),e.withCredentials=d,w(e,v),e.send(null),e}this.connect=function(e){S(),a.setState(Guacamole.Tunnel.State.CONNECTING);var t=new XMLHttpRequest;t.onreadystatechange=function(){4===t.readyState&&(200===t.status?(S(),a.setUUID(t.responseText),(y=t.getResponseHeader(g))?(a.setState(Guacamole.Tunnel.State.OPEN),p=setInterval((function(){a.sendMessage("nop")}),500),I(C())):G(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_NOT_FOUND))):E(t))},t.open("POST",o,!0),t.withCredentials=d,w(t,v),t.setRequestHeader("Content-type","application/x-www-form-urlencoded; charset=UTF-8"),t.send(e)},this.disconnect=function(){G(new Guacamole.Status(Guacamole.Status.Code.SUCCESS,"Manually closed."))}},Guacamole.HTTPTunnel.prototype=new Guacamole.Tunnel,Guacamole.WebSocketTunnel=function(e){var t=this,n=null,a=null,o=null,i=null,r=0;if("ws:"!==e.substring(0,3)&&"wss:"!==e.substring(0,4)){var s={"http:":"ws:","https:":"wss:"}[window.location.protocol];if("/"===e.substring(0,1))e=s+"//"+window.location.host+e;else{var u=window.location.pathname.lastIndexOf("/"),l=window.location.pathname.substring(0,u+1);e=s+"//"+window.location.host+l+e}}var c=function(){var e=(new Date).getTime();t.sendMessage(Guacamole.Tunnel.INTERNAL_DATA_OPCODE,"ping",e),r=e},h=function(){window.clearTimeout(a),window.clearTimeout(o),window.clearTimeout(i),t.state===Guacamole.Tunnel.State.UNSTABLE&&t.setState(Guacamole.Tunnel.State.OPEN),a=window.setTimeout((function(){d(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_TIMEOUT,"Server timeout."))}),t.receiveTimeout),o=window.setTimeout((function(){t.setState(Guacamole.Tunnel.State.UNSTABLE)}),t.unstableThreshold);var e=(new Date).getTime(),n=Math.max(r+500-e,0);n>0?i=window.setTimeout(c,n):c()};function d(e){window.clearTimeout(a),window.clearTimeout(o),window.clearTimeout(i),t.state!==Guacamole.Tunnel.State.CLOSED&&(e.code!==Guacamole.Status.Code.SUCCESS&&t.onerror&&t.onerror(e),t.setState(Guacamole.Tunnel.State.CLOSED),n.close())}this.sendMessage=function(e){if(t.isConnected()&&0!==arguments.length){for(var a=i(arguments[0]),o=1;o<arguments.length;o++)a+=","+i(arguments[o]);a+=";",n.send(a)}function i(e){var t=new String(e);return t.length+"."+t}},this.connect=function(a){h(),t.setState(Guacamole.Tunnel.State.CONNECTING),(n=new WebSocket(e+"?"+a,"guacamole")).onopen=function(e){h()},n.onclose=function(e){e.reason?d(new Guacamole.Status(parseInt(e.reason),e.reason)):e.code?d(new Guacamole.Status(Guacamole.Status.Code.fromWebSocketCode(e.code))):d(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_NOT_FOUND))},n.onmessage=function(e){h();var n,a=e.data,o=0,i=[];do{var r=a.indexOf(".",o);if(-1!==r)n=(o=r+1)+parseInt(a.substring(n+1,r));else d(new Guacamole.Status(Guacamole.Status.Code.SERVER_ERROR,"Incomplete instruction."));var s=a.substring(o,n),u=a.substring(n,n+1);if(i.push(s),";"===u){var l=i.shift();null===t.uuid&&(l===Guacamole.Tunnel.INTERNAL_DATA_OPCODE&&1===i.length&&t.setUUID(i[0]),t.setState(Guacamole.Tunnel.State.OPEN)),l!==Guacamole.Tunnel.INTERNAL_DATA_OPCODE&&t.oninstruction&&t.oninstruction(l,i),i.length=0}o=n+1}while(o<a.length)}},this.disconnect=function(){d(new Guacamole.Status(Guacamole.Status.Code.SUCCESS,"Manually closed."))}},Guacamole.WebSocketTunnel.prototype=new Guacamole.Tunnel,Guacamole.ChainedTunnel=function(e){for(var t,n=this,a=[],o=null,i=0;i<arguments.length;i++)a.push(arguments[i]);function r(e){n.disconnect=e.disconnect,n.sendMessage=e.sendMessage;var i=function(t){if(t&&t.code===Guacamole.Status.Code.UPSTREAM_TIMEOUT)return a=[],null;var n=a.shift();return n&&(e.onerror=null,e.oninstruction=null,e.onstatechange=null,r(n)),n};function s(){e.onstatechange=n.onstatechange,e.oninstruction=n.oninstruction,e.onerror=n.onerror,e.uuid&&n.setUUID(e.uuid),e.onuuid=function(e){n.setUUID(e)},o=e}e.onstatechange=function(e){switch(e){case Guacamole.Tunnel.State.OPEN:s(),n.onstatechange&&n.onstatechange(e);break;case Guacamole.Tunnel.State.CLOSED:!i()&&n.onstatechange&&n.onstatechange(e)}},e.oninstruction=function(e,t){s(),n.oninstruction&&n.oninstruction(e,t)},e.onerror=function(e){!i(e)&&n.onerror&&n.onerror(e)},e.connect(t)}this.connect=function(e){t=e;var i=o||a.shift();i?r(i):n.onerror&&n.onerror(Guacamole.Status.Code.SERVER_ERROR,"No tunnels to try.")}},Guacamole.ChainedTunnel.prototype=new Guacamole.Tunnel,Guacamole.StaticHTTPTunnel=function(e,t,n){var a=this,o=null,i=n||{};this.size=null,this.sendMessage=function(e){},this.connect=function(n){a.disconnect(),a.setState(Guacamole.Tunnel.State.CONNECTING);var r=new Guacamole.Parser,s=new Guacamole.UTF8Parser;r.oninstruction=function(e,t){a.oninstruction&&a.oninstruction(e,t)},o=new AbortController,fetch(e,{headers:i,credentials:t?"include":"same-origin",signal:o.signal}).then((function(e){if(!e.ok)return a.onerror&&a.onerror(new Guacamole.Status(Guacamole.Status.Code.fromHTTPCode(e.status),e.statusText)),void a.disconnect();a.size=e.headers.get("Content-Length"),a.setState(Guacamole.Tunnel.State.OPEN);var t=e.body.getReader();t.read().then((function e(n){n.done?a.disconnect():(r.receive(s.decode(n.value)),t.read().then(e))}))}))},this.disconnect=function(){o&&(o.abort(),o=null),a.setState(Guacamole.Tunnel.State.CLOSED)}},Guacamole.StaticHTTPTunnel.prototype=new Guacamole.Tunnel,(Guacamole=Guacamole||{}).UTF8Parser=function(){var e=0,t=0;this.decode=function(n){for(var a="",o=new Uint8Array(n),i=0;i<o.length;i++){var r=o[i];0===e?127==(127|r)?a+=String.fromCharCode(r):223==(31|r)?(t=31&r,e=1):239==(15|r)?(t=15&r,e=2):247==(7|r)?(t=7&r,e=3):a+="�":191==(63|r)?(t=t<<6|63&r,0===--e&&(a+=String.fromCharCode(t))):(e=0,a+="�")}return a}},(Guacamole=Guacamole||{}).API_VERSION="1.5.0",(Guacamole=Guacamole||{}).VideoPlayer=function(){this.sync=function(){}},Guacamole.VideoPlayer.isSupportedType=function(e){return!1},Guacamole.VideoPlayer.getSupportedTypes=function(){return[]},Guacamole.VideoPlayer.getInstance=function(e,t,n){return null},globalThis.Guacamole=Guacamole;
