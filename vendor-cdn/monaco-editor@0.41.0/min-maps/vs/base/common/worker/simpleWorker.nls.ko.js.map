{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.ko.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.41.0(38e1e3d097f84e336c311d071a9ffb5191d4ffd1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.ko\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"배열\",\n\t\t\"부울\",\n\t\t\"클래스\",\n\t\t\"상수\",\n\t\t\"생성자\",\n\t\t\"열거형\",\n\t\t\"열거형 멤버\",\n\t\t\"이벤트\",\n\t\t\"필드\",\n\t\t\"파일\",\n\t\t\"함수\",\n\t\t\"인터페이스\",\n\t\t\"키\",\n\t\t\"메서드\",\n\t\t\"모듈\",\n\t\t\"네임스페이스\",\n\t\t\"Null\",\n\t\t\"숫자\",\n\t\t\"개체\",\n\t\t\"연산자\",\n\t\t\"패키지\",\n\t\t\"속성\",\n\t\t\"문자열\",\n\t\t\"구조체\",\n\t\t\"형식 매개 변수\",\n\t\t\"변수\",\n\t\t\"{0}({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,eACA,eACA,qBACA,eACA,qBACA,qBACA,kCACA,qBACA,eACA,eACA,eACA,iCACA,SACA,qBACA,eACA,uCACA,OACA,eACA,eACA,qBACA,qBACA,eACA,qBACA,qBACA,yCACA,eACA,UACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.ko.js"}