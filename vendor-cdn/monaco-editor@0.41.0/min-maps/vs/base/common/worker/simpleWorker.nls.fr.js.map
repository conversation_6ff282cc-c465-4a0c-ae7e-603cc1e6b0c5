{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.fr.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.41.0(38e1e3d097f84e336c311d071a9ffb5191d4ffd1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.fr\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"tableau\",\n\t\t\"booléen\",\n\t\t\"classe\",\n\t\t\"constante\",\n\t\t\"constructeur\",\n\t\t\"énumération\",\n\t\t\"membre d\\'énumération\",\n\t\t\"événement\",\n\t\t\"champ\",\n\t\t\"fichier\",\n\t\t\"fonction\",\n\t\t\"interface\",\n\t\t\"clé\",\n\t\t\"méthode\",\n\t\t\"module\",\n\t\t\"espace de noms\",\n\t\t\"NULL\",\n\t\t\"nombre\",\n\t\t\"objet\",\n\t\t\"opérateur\",\n\t\t\"package\",\n\t\t\"propriété\",\n\t\t\"chaîne\",\n\t\t\"struct\",\n\t\t\"paramètre de type\",\n\t\t\"variable\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,UACA,aACA,SACA,YACA,eACA,oBACA,6BACA,kBACA,QACA,UACA,WACA,YACA,SACA,aACA,SACA,iBACA,OACA,SACA,QACA,eACA,UACA,kBACA,YACA,SACA,uBACA,WACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.fr.js"}