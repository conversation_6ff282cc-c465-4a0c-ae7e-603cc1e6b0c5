{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.zh-cn.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.41.0(38e1e3d097f84e336c311d071a9ffb5191d4ffd1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.zh-cn\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"数组\",\n\t\t\"布尔值\",\n\t\t\"类\",\n\t\t\"常数\",\n\t\t\"构造函数\",\n\t\t\"枚举\",\n\t\t\"枚举成员\",\n\t\t\"事件\",\n\t\t\"字段\",\n\t\t\"文件\",\n\t\t\"函数\",\n\t\t\"接口\",\n\t\t\"键\",\n\t\t\"方法\",\n\t\t\"模块\",\n\t\t\"命名空间\",\n\t\t\"Null\",\n\t\t\"数字\",\n\t\t\"对象\",\n\t\t\"运算符\",\n\t\t\"包\",\n\t\t\"属性\",\n\t\t\"字符串\",\n\t\t\"结构\",\n\t\t\"类型参数\",\n\t\t\"变量\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+CAAgD,CACtD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,eACA,qBACA,SACA,eACA,2BACA,eACA,2BACA,eACA,eACA,eACA,eACA,eACA,SACA,eACA,eACA,2BACA,OACA,eACA,eACA,qBACA,SACA,eACA,qBACA,eACA,2BACA,eACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.zh-cn.js"}