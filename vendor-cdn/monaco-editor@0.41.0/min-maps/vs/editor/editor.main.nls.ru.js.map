{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.ru.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.41.0(38e1e3d097f84e336c311d071a9ffb5191d4ffd1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.ru\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"входные данные\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"С учетом регистра\",\n\t\t\"Слово целиком\",\n\t\t\"Использовать регулярное выражение\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"входные данные\",\n\t\t\"Сохранить регистр\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"Загрузка…\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"Ошибка: {0}\",\n\t\t\"Предупреждение: {0}\",\n\t\t\"Информация: {0}\",\n\t\t\"для журнала\",\n\t\t\"Очищенные входные данные\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"свободный\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Поле выбора\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"Дополнительные действия...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"Фильтр\",\n\t\t\"Нечеткое совпадение\",\n\t\t\"Введите текст для фильтра\",\n\t\t\"Ввод для поиска\",\n\t\t\"Ввод для поиска\",\n\t\t\"Закрыть\",\n\t\t\"Элементы не найдены.\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(пусто)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"Произошла системная ошибка ({0})\",\n\t\t\"Произошла неизвестная ошибка. Подробные сведения см. в журнале.\",\n\t\t\"Произошла неизвестная ошибка. Подробные сведения см. в журнале.\",\n\t\t\"{0} (всего ошибок: {1})\",\n\t\t\"Произошла неизвестная ошибка. Подробные сведения см. в журнале.\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"CTRL\",\n\t\t\"SHIFT\",\n\t\t\"ALT\",\n\t\t\"Windows\",\n\t\t\"CTRL\",\n\t\t\"SHIFT\",\n\t\t\"ALT\",\n\t\t\"Super\",\n\t\t\"CTRL\",\n\t\t\"SHIFT\",\n\t\t\"Параметр\",\n\t\t\"Команда\",\n\t\t\"CTRL\",\n\t\t\"SHIFT\",\n\t\t\"ALT\",\n\t\t\"Windows\",\n\t\t\"CTRL\",\n\t\t\"SHIFT\",\n\t\t\"ALT\",\n\t\t\"Super\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"редактор\",\n\t\t\"Сейчас редактор недоступен.\",\n\t\t\"{0} Чтобы включить оптимизированный режим читателя, используйте {1}\",\n\t\t\"{0} Чтобы включить оптимизированный режим читателя, откройте быстрый выбор с помощью {1} и выполните команду \\\"Активация режима специальных возможностей для читателя\\\", который сейчас не может быть активирован с помощью клавиатуры.\",\n\t\t\"{0} Назначьте сочетание клавиш для команды \\\"Активация режима специальных возможностей для читателя\\\", используя редактор настраиваемых сочетаний клавиш с помощью {1} и запустите его.\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"Размещать на конце даже для более длинных строк\",\n\t\t\"Размещать на конце даже для более длинных строк\",\n\t\t\"Дополнительные курсоры удалены.\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"&&Отменить\",\n\t\t\"Отменить\",\n\t\t\"&&Повторить\",\n\t\t\"Вернуть\",\n\t\t\"&&Выделить все\",\n\t\t\"Выбрать все\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"Число курсоров ограничено {0}. Для проведения крупных изменений рекомендуется использовать [поиск и замену](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) или увеличить значение параметра ограничения нескольких курсоров в редакторе.\",\n\t\t\"Увеличить значение ограничения нескольких курсоров\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor.contribution\": [\n\t\t\"Доступное представление различий\",\n\t\t\"Перейти к следующему различию\",\n\t\t\"Открыть средство просмотра с поддержкой специальных возможностей инструмента сравнений\",\n\t\t\"Перейти к предыдущему различию\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget\": [\n\t\t\"Оформление строки для вставок в редакторе несовпадений.\",\n\t\t\"Оформление строки для удалений в редакторе несовпадений.\",\n\t\t\" используйте SHIFT + F7 для навигации по изменениям\",\n\t\t\"Нельзя сравнить файлы, потому что один из файлов слишком большой.\",\n\t\t\"Выберите, чтобы отменить изменение\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/accessibleDiffViewer\": [\n\t\t\"Значок \\\"Вставить\\\" в средстве просмотра с поддержкой специальных возможностей инструмента сравнений\",\n\t\t\"Значок \\\"Удалить\\\" в средстве просмотра с поддержкой специальных возможностей инструмента сравнений\",\n\t\t\"Значок \\\"Закрыть\\\" в средстве просмотра с поддержкой специальных возможностей инструмента сравнений\",\n\t\t\"Закрыть\",\n\t\t\"Доступное средство просмотра инструмента сравнения. Используйте клавиши СТРЕЛКА ВВЕРХ и СТРЕЛКА ВНИЗ для перемещения.\",\n\t\t\"строки не изменены\",\n\t\t\"1 строка изменена\",\n\t\t\"{0} строк изменено\",\n\t\t\"Различие {0} из {1}: исходная строка {2}, {3}, измененная строка {4}, {5}\",\n\t\t\"пустой\",\n\t\t\"{0} неизмененная строка {1}\",\n\t\t\"{0} исходная строка {1} измененная строка {2}\",\n\t\t\"+ {0} измененная строка {1}\",\n\t\t\"- {0} исходная строка {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/colors\": [\n\t\t\"Цвет границы для текста, перемещенного в редакторе несовпадений.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/decorations\": [\n\t\t\"Оформление строки для вставок в редакторе несовпадений.\",\n\t\t\"Оформление строки для удалений в редакторе несовпадений.\",\n\t\t\"Выберите, чтобы отменить изменение\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/diffEditorEditors\": [\n\t\t\" используйте {0}, чтобы открыть справку по специальным возможностям.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/inlineDiffDeletedCodeMargin\": [\n\t\t\"Копировать удаленные строки\",\n\t\t\"Копировать удаленную строку\",\n\t\t\"Копировать измененные строки\",\n\t\t\"Копировать измененную строку\",\n\t\t\"Копировать удаленную строку ({0})\",\n\t\t\"Копировать измененную строку ({0})\",\n\t\t\"Отменить это изменение\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/unchangedRanges\": [\n\t\t\"Свернуть неизмененную область\",\n\t],\n\t\"vs/editor/browser/widget/diffReview\": [\n\t\t\"Значок для кнопки \\\"Вставить\\\" в окне проверки несовпадений.\",\n\t\t\"Значок для кнопки \\\"Удалить\\\" в окне проверки несовпадений.\",\n\t\t\"Значок для кнопки \\\"Закрыть\\\" в окне проверки несовпадений.\",\n\t\t\"Закрыть\",\n\t\t\"нет измененных строк\",\n\t\t\"1 строка изменена\",\n\t\t\"Строк изменено: {0}\",\n\t\t\"Различие {0} из {1}: исходная строка {2}, {3}, измененная строка {4}, {5}\",\n\t\t\"пустой\",\n\t\t\"{0} неизмененная строка {1}\",\n\t\t\"{0} исходная строка {1} измененная строка {2}\",\n\t\t\"+ {0} измененная строка {1}\",\n\t\t\"- {0} исходная строка {1}\",\n\t],\n\t\"vs/editor/browser/widget/inlineDiffMargin\": [\n\t\t\"Копировать удаленные строки\",\n\t\t\"Копировать удаленную строку\",\n\t\t\"Копировать измененные строки\",\n\t\t\"Копировать измененную строку\",\n\t\t\"Копировать удаленную строку ({0})\",\n\t\t\"Копировать измененную строку ({0})\",\n\t\t\"Отменить это изменение\",\n\t\t\"Копировать удаленную строку ({0})\",\n\t\t\"Копировать измененную строку ({0})\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"Редактор\",\n\t\t\"Число пробелов, соответствующее табуляции. Этот параметр переопределяется на основе содержимого файла, если включен параметр {0}.\",\n\t\t\"Число пробелов, используемых для отступа, либо `\\\"tabSize\\\"` для использования значения из \\\"#editor.tabSize#\\\". Этот параметр переопределяется на основе содержимого файла, если включен параметр \\\"#editor.detectIndentation#\\\".\",\n\t\t\"Вставлять пробелы при нажатии клавиши TAB. Этот параметр переопределяется на основе содержимого файла, если включен параметр {0}.\",\n\t\t\"На основе содержимого файла определяет, будут ли {0} и {1} автоматически обнаружены при открытии файла.\",\n\t\t\"Удалить автоматически вставляемый конечный пробел.\",\n\t\t\"Специальная обработка для больших файлов с отключением некоторых функций, которые интенсивно используют память.\",\n\t\t\"Определяет, следует ли оценивать завершения на основе слов в документе.\",\n\t\t\"Предложение слов только из активного документа.\",\n\t\t\"Предложение слов из всех открытых документов на одном языке.\",\n\t\t\"Предложение слов из всех открытых документов.\",\n\t\t\"Определяет, из каких документов будут вычисляться завершения на основе слов.\",\n\t\t\"Семантическое выделение включено для всех цветовых тем.\",\n\t\t\"Семантическое выделение отключено для всех цветовых тем.\",\n\t\t\"Семантическое выделение настраивается с помощью параметра \\\"semanticHighlighting\\\" текущей цветовой темы.\",\n\t\t\"Определяет показ семантической подсветки для языков, поддерживающих ее.\",\n\t\t\"Оставлять быстрый редактор открытым даже при двойном щелчке по его содержимому и при нажатии ESC.\",\n\t\t\"Строки, длина которых превышает указанное значение, не будут размечены из соображений производительности\",\n\t\t\"Определяет, должна ли разметка происходить асинхронно в рабочей роли.\",\n\t\t\"Определяет, следует ли регистрировать асинхронную разметку. Только для отладки.\",\n\t\t\"Определяет, должна ли асинхронная разметка проверяться по отношению к устаревшей фоновой разметке. Может замедлить разметку. Только для отладки.\",\n\t\t\"Определяет символы скобок, увеличивающие или уменьшающие отступ.\",\n\t\t\"Открывающий символ скобки или строковая последовательность.\",\n\t\t\"Закрывающий символ скобки или строковая последовательность.\",\n\t\t\"Определяет пары скобок, цвет которых зависит от их уровня вложения, если включена опция выделения цветом.\",\n\t\t\"Открывающий символ скобки или строковая последовательность.\",\n\t\t\"Закрывающий символ скобки или строковая последовательность.\",\n\t\t\"Время ожидания в миллисекундах, по истечении которого вычисление несовпадений отменяется. Укажите значение 0, чтобы не использовать время ожидания.\",\n\t\t\"Максимальный размер файла в МБ для вычисления различий. Используйте 0 без ограничений.\",\n\t\t\"Определяет, как редактор несовпадений отображает отличия: рядом или в тексте.\",\n\t\t\"Если этот параметр включен, в редакторе несовпадений на поле глифа отображаются стрелки для отмены изменений.\",\n\t\t\"Когда параметр включен, редактор несовпадений игнорирует изменения начального или конечного пробела.\",\n\t\t\"Определяет, должны ли в редакторе отображаться индикаторы +/- для добавленных или удаленных изменений.\",\n\t\t\"Определяет, отображается ли CodeLens в редакторе.\",\n\t\t\"Строки не будут переноситься никогда.\",\n\t\t\"Строки будут переноситься по ширине окна просмотра.\",\n\t\t\"Строки будут переноситься в соответствии с настройкой {0}.\",\n\t\t\"Использует устаревший алгоритм сравнения.\",\n\t\t\"Использует расширенный алгоритм сравнения.\",\n\t\t\"Определяет, отображает ли редактор несовпадений неизмененные регионы. Работает только при настройке параметра {0}.\",\n\t\t\"Определяет, должен ли редактор несовпадений показывать обнаруженные перемещения кода. Работает только при настройке параметра {0}.\",\n\t\t\"Определяет реализацию, используемую редактором несовпадений (новая или старая реализация).\",\n\t\t\"Определяет, отображает ли редактор несовпадений пустые элементы оформления, чтобы увидеть, где вставлены или удалены символы.\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"Использовать API-интерфейсы платформы, чтобы определять, подключено ли средство чтения с экрана\",\n\t\t\"Оптимизировать для использования со средством чтения с экрана\",\n\t\t\"Предполагать, что средство чтения с экрана не подключено\",\n\t\t\"Определяет, следует ли запустить пользовательский интерфейс в режиме оптимизации для средства чтения с экрана.\",\n\t\t\"Определяет, вставляется ли пробел при комментировании.\",\n\t\t\"Определяет, должны ли пустые строки игнорироваться с помощью действий переключения, добавления или удаления для комментариев к строкам.\",\n\t\t\"Управляет тем, копируется ли текущая строка при копировании без выделения.\",\n\t\t\"Определяет, должен ли курсор перемещаться для поиска совпадений при вводе.\",\n\t\t\"Никогда не вставлять начальные значения в строку поиска из выделенного фрагмента редактора.\",\n\t\t\"Всегда вставлять начальные значения в строку поиска из выделенного фрагмента редактора, включая слова в позиции курсора.\",\n\t\t\"Вставлять начальные значения в строку поиска только из выделенного фрагмента редактора.\",\n\t\t\"Определяет, можно ли передать строку поиска в мини-приложение поиска из текста, выделенного в редакторе.\",\n\t\t\"Никогда не включать функцию «Найти в выделении» автоматически (по умолчанию).\",\n\t\t\"Всегда включать функцию «Найти в выделении» автоматически.\",\n\t\t\"Автоматическое включение функции «Найти в выделении» при выборе нескольких строк содержимого.\",\n\t\t\"Управляет условием автоматического включения функции «Найти в выделении».\",\n\t\t\"Определяет, должно ли мини-приложение поиска считывать или изменять общий буфер обмена поиска в macOS.\",\n\t\t\"Определяет, должно ли мини-приложение поиска добавлять дополнительные строки в начале окна редактора. Если задано значение true, вы можете прокрутить первую строку при отображаемом мини-приложении поиска.\",\n\t\t\"Определяет, будет ли поиск автоматически перезапускаться с начала (или с конца), если не найдено никаких других соответствий.\",\n\t\t\"Включает или отключает лигатуры шрифтов (характеристики шрифта \\\"calt\\\" и \\\"liga\\\"). Измените этот параметр на строку для детального управления свойством CSS \\\"font-feature-settings\\\".\",\n\t\t\"Явное свойство CSS \\\"font-feature-settings\\\". Если необходимо только включить или отключить лигатуры, вместо него можно передать логическое значение.\",\n\t\t\"Настраивает лигатуры или характеристики шрифта. Можно указать логическое значение, чтобы включить или отключить лигатуры, или строку для значения свойства CSS \\\"font-feature-settings\\\".\",\n\t\t\"Включает или отключает преобразование из параметра font-weight в font-variation-settings. Измените этот параметр на строку для детального управления свойством CSS font-variation-settings.\",\n\t\t\"Явное свойство CSS font-variation-settings. Если необходимо лишь преобразовать параметр font-weight в параметр font-variation-settings, вместо этого свойства можно передать логическое значение.\",\n\t\t\"Настраивает варианты шрифтов. Может представлять собой логическое значение для включения или отключения преобразования из параметра font-weight в параметр font-variation-settings или строку, содержащую значение свойства CSS font-variation-settings.\",\n\t\t\"Определяет размер шрифта в пикселях.\",\n\t\t\"Допускаются только ключевые слова \\\"normal\\\" или \\\"bold\\\" и числа в диапазоне от 1 до 1000.\",\n\t\t\"Управляет насыщенностью шрифта. Допустимые значения: ключевые слова \\\"normal\\\" или \\\"bold\\\", а также числа в диапазоне от 1 до 1000.\",\n\t\t\"Показать предварительные результаты (по умолчанию)\",\n\t\t\"Перейти к основному результату и показать быстрый редактор\",\n\t\t\"Перейти к основному результату и включить быструю навигацию для остальных\",\n\t\t\"Этот параметр устарел. Используйте вместо него отдельные параметры, например, \\'editor.editor.gotoLocation.multipleDefinitions\\' или \\'editor.editor.gotoLocation.multipleImplementations\\'.\",\n\t\t\"Управляет поведением команды \\\"Перейти к определению\\\" при наличии нескольких целевых расположений.\",\n\t\t\"Управляет поведением команды \\\"Перейти к определению типа\\\" при наличии нескольких целевых расположений.\",\n\t\t\"Управляет поведением команды \\\"Перейти к объявлению\\\" при наличии нескольких целевых расположений.\",\n\t\t\"Управляет поведением команды \\\"Перейти к реализациям\\\" при наличии нескольких целевых расположений.\",\n\t\t\"Управляет поведением команды \\\"Перейти к ссылкам\\\" при наличии нескольких целевых расположений.\",\n\t\t\"Идентификатор альтернативной команды, выполняемой в том случае, когда результатом операции \\\"Перейти к определению\\\" является текущее расположение.\",\n\t\t\"Идентификатор альтернативной команды, которая выполняется в том случае, если результатом операции \\\"Перейти к определению типа\\\" является текущее расположение.\",\n\t\t\"Идентификатор альтернативный команды, выполняемой в том случае, когда результатом операции \\\"Перейти к объявлению\\\" является текущее расположение.\",\n\t\t\"Идентификатор альтернативный команды, выполняемой, когда результатом команды \\\"Перейти к реализации\\\" является текущее расположение.\",\n\t\t\"Идентификатор альтернативной команды, выполняемой в том случае, когда результатом выполнения операции \\\"Перейти к ссылке\\\" является текущее расположение.\",\n\t\t\"Управляет тем, отображается ли наведение.\",\n\t\t\"Определяет время задержки в миллисекундах перед отображением наведения.\",\n\t\t\"Управляет тем, должно ли наведение оставаться видимым при наведении на него курсора мыши.\",\n\t\t\"Предпочитать отображать наведение над строкой, если есть место.\",\n\t\t\"Предполагает, что все символы имеют одинаковую ширину. Это быстрый алгоритм, который работает правильно для моноширинных шрифтов и некоторых скриптов (например, латинских символов), где глифы имеют одинаковую ширину.\",\n\t\t\"Делегирует вычисление точек переноса браузеру. Это медленный алгоритм, который может привести к зависаниям при обработке больших файлов, но работает правильно во всех случаях.\",\n\t\t\"Управляет алгоритмом, который вычисляет точки переноса. Обратите внимание, что в режиме специальных возможностей будет использован расширенный алгоритм, чтобы обеспечить наибольшее удобство работы.\",\n\t\t\"Включает значок лампочки для действия кода в редакторе.\",\n\t\t\"Отображает вложенные текущие области во время прокрутки в верхней части редактора.\",\n\t\t\"Определяет максимальное число залипающих линий для отображения.\",\n\t\t\"Определяет модель, используемую для определения строк залипания. Если модель структуры не существует, она откатится к модели поставщика свертывания, которая откатывается к модели отступов. Этот порядок соблюдается во всех трех случаях.\",\n\t\t\"Включает встроенные указания в редакторе.\",\n\t\t\"Вложенные подсказки включены.\",\n\t\t\"Вложенные подсказки отображаются по умолчанию и скрываются удержанием клавиш {0}.\",\n\t\t\"Вложенные подсказки по умолчанию скрыты и отображаются при удержании {0}.\",\n\t\t\"Вложенные подсказки отключены.\",\n\t\t\"Управляет размером шрифта вложенных подсказок в редакторе. По умолчанию {0} используется, когда сконфигурированное значение меньше {1} или больше размера шрифта редактора.\",\n\t\t\"Управляет семейством шрифтов для вложенных подсказок в редакторе. Если значение не задано, используется {0}.\",\n\t\t\"Включает поля вокруг встроенных указаний в редакторе.\",\n\t\t\"Определяет высоту строки. \\r\\n– Используйте 0, чтобы автоматически вычислить высоту строки на основе размера шрифта.\\r\\n– Значения от 0 до 8 будут использоваться в качестве множителя для размера шрифта.\\r\\n– Значения больше или равные 8 будут использоваться в качестве действующих значений.\",\n\t\t\"Определяет, отображается ли мини-карта.\",\n\t\t\"Определяет, скрыта ли мини-карта автоматически.\",\n\t\t\"Мини-карта имеет такой же размер, что и содержимое редактора (возможна прокрутка).\",\n\t\t\"Мини-карта будет растягиваться или сжиматься по мере необходимости, чтобы заполнить редактор по высоте (без прокрутки).\",\n\t\t\"Миникарта будет уменьшаться по мере необходимости, чтобы никогда не быть больше, чем редактор (без прокрутки).\",\n\t\t\"Управляет размером миникарты.\",\n\t\t\"Определяет, с какой стороны будет отображаться мини-карта.\",\n\t\t\"Определяет, когда отображается ползунок мини-карты.\",\n\t\t\"Масштаб содержимого, нарисованного на мини-карте: 1, 2 или 3.\",\n\t\t\"Отображает фактические символы в строке вместо цветных блоков.\",\n\t\t\"Ограничивает ширину мини-карты, чтобы количество отображаемых столбцов не превышало определенное количество.\",\n\t\t\"Задает пространство между верхним краем редактора и первой строкой.\",\n\t\t\"Задает пространство между нижним краем редактора и последней строкой.\",\n\t\t\"Включает всплывающее окно с документацией по параметру и сведениями о типе, которое отображается во время набора.\",\n\t\t\"Определяет, меню подсказок остается открытым или закроется при достижении конца списка.\",\n\t\t\"Экспресс-предложения отображаются в мини-приложении рекомендаций\",\n\t\t\"Экспресс-предложения отображаются как едва различимый текст\",\n\t\t\"Экспресс-предложения отключены\",\n\t\t\"Разрешение кратких предложений в строках.\",\n\t\t\"Разрешение кратких предложений в комментариях.\",\n\t\t\"Разрешение кратких предложений вне строк и комментариев.\",\n\t\t\"Определяет, должны ли предложения автоматически отображаться при вводе. Этот параметр можно выбрать при вводе примечаний, строк и другого кода. Быстрые предложения можно настроить для отображения в виде фантомного текста или в мини-приложении предложений. Необходимо также помнить о параметре {0}, который управляет активированием предложений специальными символами.\",\n\t\t\"Номера строк не отображаются.\",\n\t\t\"Отображаются абсолютные номера строк.\",\n\t\t\"Отображаемые номера строк вычисляются как расстояние в строках до положения курсора.\",\n\t\t\"Номера строк отображаются каждые 10 строк.\",\n\t\t\"Управляет отображением номеров строк.\",\n\t\t\"Число моноширинных символов, при котором будет отрисовываться линейка этого редактора.\",\n\t\t\"Цвет линейки этого редактора.\",\n\t\t\"Отображать вертикальные линейки после определенного числа моноширинных символов. Для отображения нескольких линеек укажите несколько значений. Если не указано ни одного значения, вертикальные линейки отображаться не будут.\",\n\t\t\"Вертикальная полоса прокрутки будет видна только при необходимости.\",\n\t\t\"Вертикальная полоса прокрутки всегда будет видна.\",\n\t\t\"Вертикальная полоса прокрутки всегда будет скрыта.\",\n\t\t\"Управляет видимостью вертикальной полосы прокрутки.\",\n\t\t\"Горизонтальная полоса прокрутки будет видна только при необходимости.\",\n\t\t\"Горизонтальная полоса прокрутки всегда будет видна.\",\n\t\t\"Горизонтальная полоса прокрутки всегда будет скрыта.\",\n\t\t\"Управляет видимостью горизонтальной полосы прокрутки.\",\n\t\t\"Ширина вертикальной полосы прокрутки.\",\n\t\t\"Высота горизонтальной полосы прокрутки.\",\n\t\t\"Управляет прокруткой при нажатии страницы или переходом к позиции щелчка.\",\n\t\t\"Управляет выделением всех нестандартных символов ASCII. Базовыми ASCII считаются только символы между U+0020 и U+007E, табуляция, перевод строки и возврат каретки.\",\n\t\t\"Определяет, выделяются ли символы, которые просто резервируют пространство или вообще не имеют ширины.\",\n\t\t\"Управляет выделением символов, которые можно спутать с основными символами ASCII, кроме тех, которые являются общими в текущем языковом стандарте пользователя.\",\n\t\t\"Определяет, должны ли символы в комментариях также выделяться в Юникоде.\",\n\t\t\"Определяет, должны ли символы в строках также выделяться в Юникоде.\",\n\t\t\"Определяет разрешенные символы, которые не выделяются.\",\n\t\t\"Символы Юникода, распространенные в разрешенных языках, не выделяются.\",\n\t\t\"Определяет, следует ли автоматически показывать встроенные предложения в редакторе.\",\n\t\t\"Отображать панель инструментов встроенного предложения при каждом отображении встроенного предложения.\",\n\t\t\"Отображать панель инструментов предложений при наведении указателя мыши на встроенное предложение.\",\n\t\t\"Определяет, когда отображать встроенную панель инструментов предложений.\",\n\t\t\"Управляет взаимодействием встроенных предложений с мини-приложением предложений. Если этот параметр включен, мини-приложение предложений не отображается автоматически, когда доступны встроенные предложения.\",\n\t\t\"Определяет, включена ли раскраска пар скобок. Используйте {0} для переопределения цветов выделения скобок.\",\n\t\t\"Определяет, имеет ли каждый тип скобок собственный независимый пул цветов.\",\n\t\t\"Включение направляющих для пар скобок.\",\n\t\t\"Включение направляющих для пар скобок только для активной пары скобок.\",\n\t\t\"Отключение направляющих для пар скобок.\",\n\t\t\"Определяет, включены ли направляющие пар скобок.\",\n\t\t\"Включение горизонтальных направляющих в дополнение к вертикальным направляющим для пар скобок.\",\n\t\t\"Включение горизонтальных направляющих только для активной пары скобок.\",\n\t\t\"Отключение горизонтальных направляющих для пар скобок.\",\n\t\t\"Определяет, включены ли горизонтальные направляющие для скобок.\",\n\t\t\"Управляет тем, должна ли выделяться активная пара квадратных скобок в редакторе.\",\n\t\t\"Определяет, должны ли в редакторе отображаться направляющие отступа.\",\n\t\t\"Выделяет активную направляющую отступа.\",\n\t\t\"Выделяет активную направляющую отступа, даже если выделены направляющие скобок.\",\n\t\t\"Не выделять активную направляющую отступа.\",\n\t\t\"Управляет тем, должна ли выделяться активная направляющая отступа в редакторе.\",\n\t\t\"Вставить предложение без перезаписи текста справа от курсора.\",\n\t\t\"Вставить предложение и перезаписать текст справа от курсора.\",\n\t\t\"Определяет, будут ли перезаписываться слова при принятии вариантов завершения. Обратите внимание, что это зависит от расширений, использующих эту функцию.\",\n\t\t\"Управляет тем, допускаются ли небольшие опечатки в предложениях фильтрации и сортировки.\",\n\t\t\"Определяет, следует ли учитывать при сортировке слова, расположенные рядом с курсором.\",\n\t\t\"Определяет, используются ли сохраненные варианты выбора предложений совместно несколькими рабочими областями и окнами (требуется \\\"#editor.suggestSelection#\\\").\",\n\t\t\"Всегда выбирать предложение при автоматической активации IntelliSense.\",\n\t\t\"Никогда не выбирать предложение при автоматической активации IntelliSense.\",\n\t\t\"Выбирать предложение только при активации IntelliSense с помощью триггерного символа.\",\n\t\t\"Выбирать предложение только при активации IntelliSense по мере ввода.\",\n\t\t\"Определяет, выбирается ли предложение при отображении мини-приложения. Обратите внимание, что этот параметр применяется только к автоматически активированным предложениям (\\\"#editor.quickSuggestions#\\\" и \\\"#editor.suggestOnTriggerCharacters#\\\"), и что предложение всегда выбирается при явном вызове, например с помощью сочетания клавиш \\\"CTRL+ПРОБЕЛ\\\".\",\n\t\t\"Определяет, запрещает ли активный фрагмент кода экспресс-предложения.\",\n\t\t\"Указывает, нужно ли отображать значки в предложениях.\",\n\t\t\"Определяет видимость строки состояния в нижней части виджета предложений.\",\n\t\t\"Определяет, следует ли просматривать результат предложения в редакторе.\",\n\t\t\"Определяет, отображаются ли сведения о предложении в строке вместе с меткой или только в мини-приложении сведений.\",\n\t\t\"Этот параметр является нерекомендуемым. Теперь размер мини-приложения предложений можно изменить.\",\n\t\t\"Этот параметр устарел. Используйте вместо него отдельные параметры, например, \\'editor.suggest.showKeywords\\' или \\'editor.suggest.showSnippets\\'.\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"method\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"function\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"constructor\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"deprecated\\\".\",\n\t\t\"При включении фильтрации IntelliSense необходимо, чтобы первый символ совпадал в начале слова, например \\\"c\\\" в \\\"Console\\\" или \\\"WebContext\\\", но _не_ в \\\"description\\\". Если параметр отключен, IntelliSense отображает больше результатов, но по-прежнему сортирует их по качеству соответствия.\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"field\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"variable\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"class\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"struct\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"interface\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"module\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"property\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"event\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"operator\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"unit\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"value\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"constant\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"enum\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"enumMember\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"keyword\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"text\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"color\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"file\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"reference\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"customcolor\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"folder\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"typeParameter\\\".\",\n\t\t\"Когда параметр включен, в IntelliSense отображаются предложения \\\"snippet\\\".\",\n\t\t\"Во включенном состоянии IntelliSense показывает предложения типа \\\"пользователи\\\".\",\n\t\t\"Во включенном состоянии IntelliSense отображает предложения типа \\\"проблемы\\\".\",\n\t\t\"Должны ли всегда быть выбраны начальный и конечный пробелы.\",\n\t\t\"Следует ли выбирать вложенные слова (например, \\\"foo\\\" в \\\"fooBar\\\" или \\\"foo_bar\\\").\",\n\t\t\"Без отступа. Перенос строк начинается со столбца 1.\",\n\t\t\"Перенесенные строки получат тот же отступ, что и родительская строка.\",\n\t\t\"Перенесенные строки получат отступ, увеличенный на единицу по сравнению с родительской строкой. \",\n\t\t\"Перенесенные строки получат отступ, увеличенный на два по сравнению с родительской строкой.\",\n\t\t\"Управляет отступом строк с переносом по словам.\",\n\t\t\"Определяет, можно ли перетаскивать файл в редактор, удерживая нажатой клавишу SHIFT (вместо открытия файла в самом редакторе).\",\n\t\t\"Определяет, отображается ли мини-приложение при сбросе файлов в редактор. Это мини-приложение позволяет управлять тем, как сбрасывается файл.\",\n\t\t\"Отображать мини-приложение выбора сброса после сброса файла в редактор.\",\n\t\t\"Никогда не показывать мини-приложение выбора сброса. Вместо этого всегда используется поставщик сброса по умолчанию.\",\n\t\t\"Определяет, можно ли вставлять содержимое различными способами.\",\n\t\t\"Определяет, отображается ли мини-приложение при вставке содержимого в редактор. Это мини-приложение позволяет управлять тем, как вставляется файл.\",\n\t\t\"Отображать мини-приложение выбора вставки после вставки содержимого в редактор.\",\n\t\t\"Никогда не показывать мини-приложение выбора вставки. Вместо этого всегда используется действие вставки по умолчанию.\",\n\t\t\"Определяет, будут ли предложения приниматься при вводе символов фиксации. Например, в JavaScript точка с запятой (\\\";\\\") может быть символом фиксации, при вводе которого предложение принимается.\",\n\t\t\"Принимать предложение при нажатии клавиши ВВОД только в том случае, если оно изменяет текст.\",\n\t\t\"Определяет, будут ли предложения приниматься клавишей ВВОД в дополнение к клавише TAB. Это помогает избежать неоднозначности между вставкой новых строк и принятием предложений.\",\n\t\t\"Управляет числом строк в редакторе, которые могут быть прочитаны средством чтения с экрана за один раз. При обнаружении средства чтения с экрана автоматически устанавливается значение по умолчанию 500. Внимание! При указании числа строк, превышающего значение по умолчанию, возможно снижение производительности.\",\n\t\t\"Содержимое редактора\",\n\t\t\"Управляйте тем, объявляются ли встроенные предложения средством чтения экрана.\",\n\t\t\"Использовать конфигурации языка для автоматического закрытия скобок.\",\n\t\t\"Автоматически закрывать скобки только в том случае, если курсор находится слева от пробела.\",\n\t\t\"Определяет, должен ли редактор автоматически добавлять закрывающую скобку при вводе пользователем открывающей скобки.\",\n\t\t\"Удалять соседние закрывающие кавычки и квадратные скобки только в том случае, если они были вставлены автоматически.\",\n\t\t\"Определяет, должен ли редактор удалять соседние закрывающие кавычки или квадратные скобки при удалении.\",\n\t\t\"Заменять закрывающие кавычки и скобки при вводе только в том случае, если кавычки или скобки были вставлены автоматически.\",\n\t\t\"Определяет, должны ли в редакторе заменяться закрывающие кавычки или скобки при вводе.\",\n\t\t\"Использовать конфигурации языка для автоматического закрытия кавычек.\",\n\t\t\"Автоматически закрывать кавычки только в том случае, если курсор находится слева от пробела.\",\n\t\t\"Определяет, должен ли редактор автоматически закрывать кавычки, если пользователь добавил открывающую кавычку.\",\n\t\t\"Редактор не будет вставлять отступы автоматически.\",\n\t\t\"Редактор будет сохранять отступ текущей строки.\",\n\t\t\"Редактор будет сохранять отступы текущей строки и учитывать скобки в соответствии с синтаксисом языка.\",\n\t\t\"Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки и вызывать специальные правила onEnterRules, определяемые языками.\",\n\t\t\"Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки, вызывать специальные правила onEnterRules, определяемые языками и учитывать правила отступа indentationRules, определяемые языками.\",\n\t\t\"Определяет, должен ли редактор автоматически изменять отступы, когда пользователи вводят, вставляют или перемещают текст или изменяют отступы строк.\",\n\t\t\"Использовать конфигурации языка для автоматического обрамления выделений.\",\n\t\t\"Обрамлять с помощью кавычек, а не скобок.\",\n\t\t\"Обрамлять с помощью скобок, а не кавычек.\",\n\t\t\"Определяет, должен ли редактор автоматически обрамлять выделения при вводе кавычек или квадратных скобок.\",\n\t\t\"Эмулировать поведение выделения для символов табуляции при использовании пробелов для отступа. Выделение будет применено к позициям табуляции.\",\n\t\t\"Определяет, отображается ли CodeLens в редакторе.\",\n\t\t\"Управляет семейством шрифтов для CodeLens.\",\n\t\t\"Определяет размер шрифта в пикселях для CodeLens. Если задано значение 0, то используется 90% от размера #editor.fontSize#.\",\n\t\t\"Определяет, должны ли в редакторе отображаться внутренние декораторы цвета и средство выбора цвета.\",\n\t\t\"Показывать палитру при щелчке и при наведении указателя на декоратор цвета\",\n\t\t\"Показывать палитру при наведении указателя на декоратор цвета\",\n\t\t\"Показывать палитру при щелчке декоратора цвета\",\n\t\t\"Управляет условием отображения палитры в декораторе цвета\",\n\t\t\"Управляет максимальным количеством цветовых декораторов, которые можно отрисовать в редакторе одновременно.\",\n\t\t\"Включение того, что выбор с помощью клавиатуры и мыши приводит к выбору столбца.\",\n\t\t\"Определяет, будет ли текст скопирован в буфер обмена с подсветкой синтаксиса.\",\n\t\t\"Управляет стилем анимации курсора.\",\n\t\t\"Плавная анимация курсора отключена.\",\n\t\t\"Плавная анимация курсора включена, только если пользователь перемещает курсор явным жестом.\",\n\t\t\"Плавная анимация курсора всегда включена.\",\n\t\t\"Управляет тем, следует ли включить плавную анимацию курсора.\",\n\t\t\"Управляет стилем курсора.\",\n\t\t\"Определяет минимальное число видимых начальных линий (минимум 0) и конечных линий (минимум 1), окружающих курсор. Этот параметр имеет название \\\"scrollOff\\\" или \\\"scrollOffset\\\" в некоторых других редакторах.\",\n\t\t\"\\\"cursorSurroundingLines\\\" применяется только при запуске с помощью клавиатуры или API.\",\n\t\t\"\\\"cursorSurroundingLines\\\" принудительно применяется во всех случаях.\",\n\t\t\"Определяет, когда необходимо применять \\\"cursorSurroundingLines\\\".\",\n\t\t\"Управляет шириной курсора, когда для параметра \\\"#editor.cursorStyle#\\\" установлено значение \\'line\\'\",\n\t\t\"Определяет, следует ли редактору разрешить перемещение выделенных элементов с помощью перетаскивания.\",\n\t\t\"Использовать новый метод отрисовки с SVG.\",\n\t\t\"Использовать новый метод отрисовки с символами шрифта.\",\n\t\t\"Использовать стабильный метод отрисовки.\",\n\t\t\"Определяет, отрисовывается ли пробел с использованием нового экспериментального метода.\",\n\t\t\"Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.\",\n\t\t\"Определяет, включено ли свертывание кода в редакторе.\",\n\t\t\"Используйте стратегию свертывания для конкретного языка, если она доступна, в противном случае используйте стратегию на основе отступов.\",\n\t\t\"Используйте стратегию свертывания на основе отступов.\",\n\t\t\"Управляет стратегией для вычисления свертываемых диапазонов.\",\n\t\t\"Определяет, должен ли редактор выделять сложенные диапазоны.\",\n\t\t\"Определяет, будет ли редактор автоматически сворачивать диапазоны импорта.\",\n\t\t\"Максимальное количество свертываемых регионов. Увеличение этого значения может привести к снижению скорости отклика редактора, если текущий источник содержит большое количество свертываемых регионов.\",\n\t\t\"Определяет, будет ли щелчок пустого содержимого после свернутой строки развертывать ее.\",\n\t\t\"Определяет семейство шрифтов.\",\n\t\t\"Определяет, будет ли редактор автоматически форматировать вставленное содержимое. Модуль форматирования должен быть доступен и иметь возможность форматировать диапазон в документе.\",\n\t\t\"Управляет параметром, определяющим, должен ли редактор автоматически форматировать строку после ввода.\",\n\t\t\"Управляет отображением вертикальных полей глифа в редакторе. Поля глифа в основном используются для отладки.\",\n\t\t\"Управляет скрытием курсора в обзорной линейке.\",\n\t\t\"Управляет интервалом между буквами в пикселях.\",\n\t\t\"Определяет, включена ли поддержка связанного редактирования в редакторе. В зависимости от языка, связанные символы, например теги HTML, обновляются при редактировании.\",\n\t\t\"Определяет, должен ли редактор определять ссылки и делать их доступными для щелчка.\",\n\t\t\"Выделять соответствующие скобки.\",\n\t\t\"Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.\",\n\t\t\"Изменение размера шрифта в редакторе при нажатой клавише CTRL и движении колесика мыши.\",\n\t\t\"Объединить несколько курсоров, когда они перекрываются.\",\n\t\t\"Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.\",\n\t\t\"Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.\",\n\t\t\"Модификатор, который будет использоваться для добавления нескольких курсоров с помощью мыши. Жесты мыши \\\"Перейти к определению\\\" и \\\"Открыть ссылку\\\" будут изменены так, чтобы они не конфликтовали c [multicursor modifier](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).\",\n\t\t\"Каждый курсор вставляет одну строку текста.\",\n\t\t\"Каждый курсор вставляет полный текст.\",\n\t\t\"Управляет вставкой, когда число вставляемых строк соответствует числу курсоров.\",\n\t\t\"Управляет максимальным числом курсоров, которые могут одновременно отображаться в активном редакторе.\",\n\t\t\"Определяет, должен ли редактор выделять экземпляры семантических символов.\",\n\t\t\"Определяет, должна ли отображаться граница на обзорной линейке.\",\n\t\t\"Фокусировка на дереве при открытии обзора\",\n\t\t\"Фокусировка на редакторе при открытии обзора\",\n\t\t\"Определяет, следует ли переключить фокус на встроенный редактор или дерево в виджете обзора.\",\n\t\t\"Определяет, всегда ли жест мышью для перехода к определению открывает мини-приложение быстрого редактирования.\",\n\t\t\"Управляет длительностью задержки (в мс) перед отображением кратких предложений.\",\n\t\t\"Определяет, выполняет ли редактор автоматическое переименование по типу.\",\n\t\t\"Не рекомендуется; используйте вместо этого параметр \\\"editor.linkedEditing\\\".\",\n\t\t\"Определяет, должны ли в редакторе отображаться управляющие символы.\",\n\t\t\"Отображение номера последней строки, когда файл заканчивается новой строкой.\",\n\t\t\"Выделяет поле и текущую строку.\",\n\t\t\"Определяет, должен ли редактор выделять текущую строку.\",\n\t\t\"Определяет, должен ли редактор отрисовывать выделение текущей строки, только когда он находится в фокусе.\",\n\t\t\"Отрисовка пробелов, кроме одиночных пробелов между словами.\",\n\t\t\"Отображать пробелы только в выделенном тексте.\",\n\t\t\"Отображать только конечные пробелы.\",\n\t\t\"Определяет, должны ли в редакторе отображаться пробелы.\",\n\t\t\"Управляет тем, необходимо ли отображать скругленные углы для выделения.\",\n\t\t\"Управляет количеством дополнительных символов, на которое содержимое редактора будет прокручиваться по горизонтали.\",\n\t\t\"Определяет, будет ли содержимое редактора прокручиваться за последнюю строку.\",\n\t\t\"Прокрутка только вдоль основной оси при прокрутке по вертикали и горизонтали одновременно. Предотвращает смещение по горизонтали при прокрутке по вертикали на трекпаде.\",\n\t\t\"Контролирует, следует ли поддерживать первичный буфер обмена Linux.\",\n\t\t\"Определяет, должен ли редактор выделять совпадения, аналогичные выбранному фрагменту.\",\n\t\t\"Всегда показывать свертываемые элементы управления.\",\n\t\t\"Никогда не показывать элементы управления свертыванием и уменьшать размер переплета.\",\n\t\t\"Показывать только элементы управления свертывания, когда указатель мыши находится над переплетом.\",\n\t\t\"Определяет, когда элементы управления свертывания отображаются на переплете.\",\n\t\t\"Управляет скрытием неиспользуемого кода.\",\n\t\t\"Управляет перечеркиванием устаревших переменных.\",\n\t\t\"Отображать предложения фрагментов поверх других предложений.\",\n\t\t\"Отображать предложения фрагментов под другими предложениями.\",\n\t\t\"Отображать предложения фрагментов рядом с другими предложениями.\",\n\t\t\"Не отображать предложения фрагментов.\",\n\t\t\"Управляет отображением фрагментов вместе с другими предложениями и их сортировкой.\",\n\t\t\"Определяет, будет ли использоваться анимация при прокрутке содержимого редактора\",\n\t\t\"Размер шрифта для мини-приложения предложений. Если установлено {0}, используется значение {1}.\",\n\t\t\"Высота строки для мини-приложения предложений. Если установлено {0}, используется значение {1}. Минимальное значение — 8.\",\n\t\t\"Определяет, должны ли при вводе триггерных символов автоматически отображаться предложения.\",\n\t\t\"Всегда выбирать первое предложение.\",\n\t\t\"Выбор недавних предложений, если только дальнейший ввод не приводит к использованию одного из них, например \\\"console.| -> console.log\\\", так как \\\"log\\\" недавно использовался для завершения.\",\n\t\t\"Выбор предложений с учетом предыдущих префиксов, использованных для завершения этих предложений, например \\\"co -> console\\\" и \\\"con -> const\\\".\",\n\t\t\"Управляет предварительным выбором предложений при отображении списка предложений.\",\n\t\t\"При использовании дополнения по TAB будет добавляться наилучшее предложение при нажатии клавиши TAB.\",\n\t\t\"Отключить дополнение по TAB.\",\n\t\t\"Вставка дополнений по TAB при совпадении их префиксов. Функция работает оптимально, если параметр \\\"quickSuggestions\\\" отключен.\",\n\t\t\"Включает дополнения по TAB.\",\n\t\t\"Необычные символы завершения строки автоматически удаляются.\",\n\t\t\"Необычные символы завершения строки игнорируются.\",\n\t\t\"Для необычных символов завершения строки запрашивается удаление.\",\n\t\t\"Удалите необычные символы завершения строки, которые могут вызвать проблемы.\",\n\t\t\"Вставка и удаление пробелов после позиции табуляции\",\n\t\t\"Использовать правило разрыва строк по умолчанию.\",\n\t\t\"Не следует использовать разрывы слов для текста на китайском, японском или корейском языке (CJK). Для других текстов используется обычное поведение.\",\n\t\t\"Управляет правилами разбиения по словам, используемыми для текста на китайском,японском и корейском языке (CJK).\",\n\t\t\"Символы, которые будут использоваться как разделители слов при выполнении навигации или других операций, связанных со словами.\",\n\t\t\"Строки не будут переноситься никогда.\",\n\t\t\"Строки будут переноситься по ширине окна просмотра.\",\n\t\t\"Строки будут переноситься по \\\"#editor.wordWrapColumn#\\\".\",\n\t\t\"Строки будут перенесены по минимальному значению из двух: ширина окна просмотра и \\\"#editor.wordWrapColumn#\\\".\",\n\t\t\"Управляет тем, как следует переносить строки.\",\n\t\t\"Определяет столбец переноса редактора, если значение \\\"#editor.wordWrap#\\\" — \\\"wordWrapColumn\\\" или \\\"bounded\\\".\",\n\t\t\"Определяет, должны ли отображаться встроенные цветовые оформления с использованием поставщика цвета документа по умолчанию.\",\n\t\t\"Определяет, получает ли редактор вкладки или откладывает ли их в рабочую среду для навигации.\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"Цвет фона для выделения строки в позиции курсора.\",\n\t\t\"Цвет фона границ вокруг строки в позиции курсора.\",\n\t\t\"Цвет фона для выделенных диапазонов, например при использовании функций Quick Open или поиска. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона обводки выделения.\",\n\t\t\"Цвет фона выделенного символа, например, в функциях \\\"Перейти к определению\\\" или \\\"Перейти к следующему/предыдущему символу\\\". Цвет должен быть прозрачным, чтобы не скрывать оформление текста под ним.\",\n\t\t\"Цвет фона для границы вокруг выделенных символов.\",\n\t\t\"Цвет курсора редактора.\",\n\t\t\"Цвет фона курсора редактора. Позволяет настраивать цвет символа, перекрываемого прямоугольным курсором.\",\n\t\t\"Цвет пробелов в редакторе.\",\n\t\t\"Цвет номеров строк редактора.\",\n\t\t\"Цвет направляющих для отступов редактора.\",\n\t\t\"Свойство \\\"editorIndentGuide.background\\\" является нерекомендуемым. Вместо этого используйте \\\"editorIndentGuide.background1\\\".\",\n\t\t\"Цвет активных направляющих для отступов редактора.\",\n\t\t\"Свойство \\\"editorIndentGuide.activeBackground\\\" является нерекомендуемым. Вместо этого используйте \\\"editorIndentGuide.activeBackground1\\\".\",\n\t\t\"Цвет направляющих для отступов редактора (1).\",\n\t\t\"Цвет направляющих для отступов редактора (2).\",\n\t\t\"Цвет направляющих для отступов редактора (3).\",\n\t\t\"Цвет направляющих для отступов редактора (4).\",\n\t\t\"Цвет направляющих для отступов редактора (5).\",\n\t\t\"Цвет направляющих для отступов редактора (6).\",\n\t\t\"Цвет активных направляющих для отступов редактора (1).\",\n\t\t\"Цвет активных направляющих для отступов редактора (2).\",\n\t\t\"Цвет активных направляющих для отступов редактора (3).\",\n\t\t\"Цвет активных направляющих для отступов редактора (4).\",\n\t\t\"Цвет активных направляющих для отступов редактора (5).\",\n\t\t\"Цвет активных направляющих для отступов редактора (6).\",\n\t\t\"Цвет номера активной строки редактора\",\n\t\t\"Параметр \\'Id\\' является устаревшим. Используйте вместо него параметр \\'editorLineNumber.activeForeground\\'.\",\n\t\t\"Цвет номера активной строки редактора\",\n\t\t\"Цвет последней строки редактора, когда editor.renderFinalNewline имеет значение dimmed.\",\n\t\t\"Цвет линейки редактора.\",\n\t\t\"Цвет переднего плана элемента CodeLens в редакторе\",\n\t\t\"Цвет фона парных скобок\",\n\t\t\"Цвет прямоугольников парных скобок\",\n\t\t\"Цвет границы для линейки в окне просмотра.\",\n\t\t\"Цвет фона обзорной линейки редактора.\",\n\t\t\"Цвет фона поля в редакторе. В поле размещаются отступы глифов и номера строк.\",\n\t\t\"Цвет границы для ненужного (неиспользуемого) исходного кода в редакторе.\",\n\t\t\"Непрозрачность ненужного (неиспользуемого) исходного кода в редакторе. Например, \\\"#000000c0\\\" отображает код с непрозрачностью 75 %. В высококонтрастных темах для выделения ненужного кода вместо затенения используйте цвет темы \\\"editorUnnecessaryCode.border\\\".\",\n\t\t\"Цвет границы для едва различимого текста в редакторе.\",\n\t\t\"Цвет переднего плана для едва различимого текста в редакторе.\",\n\t\t\"Цвет фона для едва различимого текста в редакторе.\",\n\t\t\"Цвет маркера обзорной линейки для выделения диапазонов. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет метки линейки в окне просмотра для ошибок.\",\n\t\t\"Цвет метки линейки в окне просмотра для предупреждений.\",\n\t\t\"Цвет метки линейки в окне просмотра для информационных сообщений.\",\n\t\t\"Цвет переднего плана для скобок (1). Требуется включить раскраску парных скобок.\",\n\t\t\"Цвет переднего плана для скобок (2). Требуется включить раскраску парных скобок.\",\n\t\t\"Цвет переднего плана для скобок (3). Требуется включить раскраску парных скобок.\",\n\t\t\"Цвет переднего плана для скобок (4). Требуется включить раскраску парных скобок.\",\n\t\t\"Цвет переднего плана для скобок (5). Требуется включить раскраску парных скобок.\",\n\t\t\"Цвет переднего плана для скобок (6). Требуется включить раскраску парных скобок.\",\n\t\t\"Цвет переднего плана непредвиденных скобок.\",\n\t\t\"Цвет фона неактивных направляющих пар скобок (1). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона неактивных направляющих пар скобок (2). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона неактивных направляющих пар скобок (3). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона неактивных направляющих пар скобок (4). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона неактивных направляющих пар скобок (5). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона неактивных направляющих пар скобок (6). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона активных направляющих пар скобок (1). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона активных направляющих пар скобок (2). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона активных направляющих пар скобок (3). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона активных направляющих пар скобок (4). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона активных направляющих пар скобок (5). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет фона активных направляющих пар скобок (6). Требуется включить направляющие пар скобок.\",\n\t\t\"Цвет границы, используемый для выделения символов Юникода.\",\n\t\t\"Цвет фона, используемый для выделения символов Юникода.\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"Находится ли фокус на тексте в редакторе (курсор мигает)\",\n\t\t\"Находится ли фокус на редакторе или на мини-приложении редактора (например, фокус находится на мини-приложении поиска)\",\n\t\t\"Находится ли фокус на редакторе или на поле ввода форматированного текста (курсор мигает)\",\n\t\t\"Является ли редактор доступным только для чтения\",\n\t\t\"Является ли контекст редактором несовпадений\",\n\t\t\"Является ли контекст внедренным редактором несовпадений\",\n\t\t\"Отображается ли средство просмотра с поддержкой специальных возможностей инструмента сравнений\",\n\t\t\"Включен ли параметр \\\"editor.columnSelection\\\"\",\n\t\t\"Есть ли в редакторе выбранный текст\",\n\t\t\"Есть ли в редакторе множественный выбор\",\n\t\t\"Перемещается ли фокус с редактора при нажатии клавиши TAB\",\n\t\t\"Является ли наведение в редакторе видимым\",\n\t\t\"Находится ли в фокусе наведение в редакторе\",\n\t\t\"Находится ли залипание прокрутки в фокусе\",\n\t\t\"Отображается ли залипание прокрутки\",\n\t\t\"Видна ли автономная палитра цветов\",\n\t\t\"Сфокусирована ли автономная палитра цветов\",\n\t\t\"Является ли редактор частью большего редактора (например, записных книжек)\",\n\t\t\"Идентификатор языка редактора\",\n\t\t\"Есть ли в редакторе поставщик элементов завершения\",\n\t\t\"Есть ли в редакторе поставщик действий с кодом\",\n\t\t\"Есть ли в редакторе поставщик CodeLens\",\n\t\t\"Есть ли в редакторе поставщик определений\",\n\t\t\"Есть ли в редакторе поставщик объявлений\",\n\t\t\"Есть ли в редакторе поставщик реализации\",\n\t\t\"Есть ли в редакторе поставщик определений типов\",\n\t\t\"Есть ли в редакторе поставщик наведения\",\n\t\t\"Есть ли в редакторе поставщик выделения документов\",\n\t\t\"Есть ли в редакторе поставщик символов документа\",\n\t\t\"Есть ли в редакторе поставщик ссылок\",\n\t\t\"Есть ли в редакторе поставщик переименования\",\n\t\t\"Есть ли в редакторе поставщик справки по сигнатурам\",\n\t\t\"Есть ли в редакторе поставщик встроенных подсказок\",\n\t\t\"Есть ли в редакторе поставщик форматирования документов\",\n\t\t\"Есть ли в редакторе поставщик форматирования для выделения документов\",\n\t\t\"Есть ли в редакторе несколько поставщиков форматирования документов\",\n\t\t\"Есть ли в редакторе несколько поставщиков форматирования для выделения документов\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"массив\",\n\t\t\"логическое значение\",\n\t\t\"класс\",\n\t\t\"константа\",\n\t\t\"конструктор\",\n\t\t\"перечисление\",\n\t\t\"элемент перечисления\",\n\t\t\"событие\",\n\t\t\"поле\",\n\t\t\"файл\",\n\t\t\"функция\",\n\t\t\"интерфейс\",\n\t\t\"ключ\",\n\t\t\"метод\",\n\t\t\"модуль\",\n\t\t\"пространство имен\",\n\t\t\"NULL\",\n\t\t\"число\",\n\t\t\"объект\",\n\t\t\"оператор\",\n\t\t\"пакет\",\n\t\t\"свойство\",\n\t\t\"строка\",\n\t\t\"структура\",\n\t\t\"параметр типа\",\n\t\t\"Переменная\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"Простой текст\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"Ввод\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"Разработчик: проверить токены\",\n\t\t\"Перейти к строке/столбцу...\",\n\t\t\"Показать всех поставщиков быстрого доступа\",\n\t\t\"Палитра команд\",\n\t\t\"Показать и выполнить команды\",\n\t\t\"Перейти к символу...\",\n\t\t\"Перейти к символу по категориям...\",\n\t\t\"Содержимое редактора\",\n\t\t\"Нажмите ALT+F1 для доступа к параметрам специальных возможностей.\",\n\t\t\"Переключить высококонтрастную тему\",\n\t\t\"Внесено изменений в файлах ({1}): {0}.\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"Показать больше ({0})\",\n\t\t\"Символы: {0}\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"Начальная точка выделения\",\n\t\t\"Начальная точка установлена в {0}:{1}\",\n\t\t\"Установить начальную точку выделения\",\n\t\t\"Перейти к начальной точке выделения\",\n\t\t\"Выделить текст от начальной точки выделения до курсора\",\n\t\t\"Отменить начальную точку выделения\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"Цвет метки линейки в окне просмотра для пар скобок.\",\n\t\t\"Перейти к скобке\",\n\t\t\"Выбрать скобку\",\n\t\t\"Удалить скобки\",\n\t\t\"Перейти к &&скобке\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"Переместить выделенный текст влево\",\n\t\t\"Переместить выделенный текст вправо\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"Транспортировать буквы\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"&&Вырезать\",\n\t\t\"Вырезать\",\n\t\t\"Вырезать\",\n\t\t\"Вырезать\",\n\t\t\"&&Копировать\",\n\t\t\"Копирование\",\n\t\t\"Копирование\",\n\t\t\"Копирование\",\n\t\t\"Копировать как\",\n\t\t\"Копировать как\",\n\t\t\"Поделиться\",\n\t\t\"Поделиться\",\n\t\t\"Поделиться\",\n\t\t\"&&Вставить\",\n\t\t\"Вставить\",\n\t\t\"Вставить\",\n\t\t\"Вставить\",\n\t\t\"Копировать с выделением синтаксиса\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"При применении действия кода произошла неизвестная ошибка\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"Тип запускаемого действия кода.\",\n\t\t\"Определяет, когда применяются возвращенные действия.\",\n\t\t\"Всегда применять первое возвращенное действие кода.\",\n\t\t\"Применить первое действие возвращенного кода, если оно является единственным.\",\n\t\t\"Не применять действия возвращенного кода.\",\n\t\t\"Определяет, следует ли возвращать только предпочтительные действия кода.\",\n\t\t\"Быстрое исправление...\",\n\t\t\"Доступные действия кода отсутствуют\",\n\t\t\"Нет доступных предпочтительных действий кода для \\\"{0}\\\".\",\n\t\t\"Действия кода для \\\"{0}\\\" недоступны\",\n\t\t\"Нет доступных предпочтительных действий кода\",\n\t\t\"Доступные действия кода отсутствуют\",\n\t\t\"Рефакторинг...\",\n\t\t\"Нет доступных предпочтительных рефакторингов для \\\"{0}\\\"\",\n\t\t\"Нет доступного рефакторинга для \\\"{0}\\\"\",\n\t\t\"Нет доступных предпочтительных рефакторингов\",\n\t\t\"Доступные операции рефакторинга отсутствуют\",\n\t\t\"Действие с исходным кодом...\",\n\t\t\"Нет доступных предпочтительных действий источника для \\'{0}\\'\",\n\t\t\"Нет доступных исходных действий для \\\"{0}\\\"\",\n\t\t\"Предпочтительные действия источника недоступны\",\n\t\t\"Доступные исходные действия отсутствуют\",\n\t\t\"Организация импортов\",\n\t\t\"Действие для упорядочения импортов отсутствует\",\n\t\t\"Исправить все\",\n\t\t\"Нет доступного действия по общему исправлению\",\n\t\t\"Автоисправление...\",\n\t\t\"Нет доступных автоисправлений\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"Включить или отключить отображение заголовков групп в меню действий кода.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"Скрыть отключенные\",\n\t\t\"Показать отключенные\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"Дополнительные действия...\",\n\t\t\"Быстрое исправление...\",\n\t\t\"Извлечь...\",\n\t\t\"Встроенная...\",\n\t\t\"Повторно создать...\",\n\t\t\"Переместить…\",\n\t\t\"Разместить во фрагменте...\",\n\t\t\"Действие с исходным кодом...\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"Показать действия кода. Доступно предпочтительное быстрое исправление ({0})\",\n\t\t\"Показать действия кода ({0})\",\n\t\t\"Показать действия кода\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"Показать команды CodeLens для текущей строки\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"Щелкните, чтобы переключить параметры цвета (RGB/HSL/HEX)\",\n\t\t\"Значок для закрытия палитры\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"Показать или выделить автономный выбор цвета\",\n\t\t\"&&Показать или выделить автономный выбор цвета\",\n\t\t\"Скрыть палитру цветов\",\n\t\t\"Вставка цвета с помощью автономной палитры цветов\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"Закомментировать или раскомментировать строку\",\n\t\t\"Переключить комментарий &&строки\",\n\t\t\"Закомментировать строку\",\n\t\t\"Раскомментировать строку\",\n\t\t\"Закомментировать или раскомментировать блок\",\n\t\t\"Переключить комментарий &&блока\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"Мини-карта\",\n\t\t\"Отрисовка символов\",\n\t\t\"Размер по вертикали\",\n\t\t\"Пропорционально\",\n\t\t\"Заполнить\",\n\t\t\"Подогнать\",\n\t\t\"Ползунок\",\n\t\t\"Наведение указателя мыши\",\n\t\t\"Всегда\",\n\t\t\"Показать контекстное меню редактора\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"Отмена действия курсора\",\n\t\t\"Повтор действия курсора\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"Вставить как...\",\n\t\t\"Идентификатор изменения вставки для попытки применения. Если этот параметр не указан, в редакторе будет отображаться средство выбора.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"Отображается ли мини-приложение вставки\",\n\t\t\"Показать параметры вставки...\",\n\t\t\"Запускаются обработчики вставки. Щелкните для отмены\",\n\t\t\"Выберите действие вставки\",\n\t\t\"Запуск обработчиков вставки\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"Встроено\",\n\t\t\"Вставить обычный текст\",\n\t\t\"Вставить URI\",\n\t\t\"Вставить URI\",\n\t\t\"Вставить пути\",\n\t\t\"Вставить путь\",\n\t\t\"Вставить относительные пути\",\n\t\t\"Вставить относительный путь\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"Отображается ли мини-приложение сброса\",\n\t\t\"Показать параметры сброса...\",\n\t\t\"Запускаются обработчики сброса. Щелкните для отмены\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"Выполняются ли в редакторе операции, допускающие отмену, например, \\\"Показать ссылки\\\"\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"Найти\",\n\t\t\"&&Найти\",\n\t\t\"Переопределяет флаг \\\"Использовать регулярное выражение\\\".\\r\\nЭтот флаг не будет сохранен на будущее.\\r\\n0: бездействие\\r\\n1: true\\r\\n2: false\",\n\t\t\"Переопределяет флаг \\\"Слово целиком\\\".\\r\\nЭтот флаг не будет сохранен на будущее.\\r\\n0: бездействие\\r\\n1: true\\r\\n2: false\",\n\t\t\"Переопределяет флаг \\\"Учитывать регистр\\\".\\r\\nЭтот флаг не будет сохранен на будущее.\\r\\n0: бездействие\\r\\n1: true\\r\\n2: false\",\n\t\t\"Переопределяет флаг \\\"Сохранить регистр\\\".\\r\\nЭтот флаг не будет сохранен на будущее.\\r\\n0: бездействие\\r\\n1: true\\r\\n2: false\",\n\t\t\"Найти с аргументами\",\n\t\t\"Найти в выбранном\",\n\t\t\"Найти далее\",\n\t\t\"Найти ранее\",\n\t\t\"Перейти к совпадению...\",\n\t\t\"Нет совпадений. Попробуйте найти что-нибудь другое.\",\n\t\t\"Введите число, чтобы перейти к определенному совпадению (от 1 до {0})\",\n\t\t\"Введите число от 1 до {0}\",\n\t\t\"Введите число от 1 до {0}\",\n\t\t\"Найти следующее выделение\",\n\t\t\"Найти предыдущее выделение\",\n\t\t\"Заменить\",\n\t\t\"&&Заменить\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"Значок для кнопки \\\"Найти в выбранном\\\" в мини-приложении поиска в редакторе.\",\n\t\t\"Значок, указывающий, что мини-приложение поиска в редакторе свернуто.\",\n\t\t\"Значок, указывающий, что мини-приложение поиска в редакторе развернуто.\",\n\t\t\"Значок для кнопки \\\"Заменить\\\" в мини-приложении поиска в редакторе.\",\n\t\t\"Значок для кнопки \\\"Заменить все\\\" в мини-приложении поиска в редакторе.\",\n\t\t\"Значок для кнопки \\\"Найти ранее\\\" в мини-приложении поиска в редакторе.\",\n\t\t\"Значок для кнопки \\\"Найти далее\\\" в мини-приложении поиска в редакторе.\",\n\t\t\"Поиск и замена\",\n\t\t\"Найти\",\n\t\t\"Найти\",\n\t\t\"Предыдущее совпадение\",\n\t\t\"Следующее совпадение\",\n\t\t\"Найти в выделении\",\n\t\t\"Закрыть\",\n\t\t\"Заменить\",\n\t\t\"Заменить\",\n\t\t\"Заменить\",\n\t\t\"Заменить все\",\n\t\t\"Переключение замены\",\n\t\t\"Отображаются только первые {0} результатов, но все операции поиска выполняются со всем текстом.\",\n\t\t\"{0} из {1}\",\n\t\t\"Результаты отсутствуют\",\n\t\t\"{0} обнаружено\",\n\t\t\"{0} найден для \\\"{1}\\\"\",\n\t\t\"{0} найден для \\\"{1}\\\", в {2}\",\n\t\t\"{0} найден для \\\"{1}\\\"\",\n\t\t\"Теперь при нажатии клавиш CTRL+ВВОД вставляется символ перехода на новую строку вместо замены всего текста. Вы можете изменить сочетание клавиш editor.action.replaceAll, чтобы переопределить это поведение.\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"Развернуть\",\n\t\t\"Развернуть рекурсивно\",\n\t\t\"Свернуть\",\n\t\t\"Переключить свертывание\",\n\t\t\"Свернуть рекурсивно\",\n\t\t\"Свернуть все блоки комментариев\",\n\t\t\"Свернуть все регионы\",\n\t\t\"Развернуть все регионы\",\n\t\t\"Свернуть все регионы, кроме выбранных\",\n\t\t\"Развернуть все регионы, кроме выбранных\",\n\t\t\"Свернуть все\",\n\t\t\"Развернуть все\",\n\t\t\"Перейти к родительскому свертыванию\",\n\t\t\"Перейти к предыдущему диапазону сложенных данных\",\n\t\t\"Перейти к следующему диапазону сложенных данных\",\n\t\t\"Создать диапазон свертывания из выделенного фрагмента\",\n\t\t\"Удалить диапазоны свертывания вручную\",\n\t\t\"Уровень папки {0}\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"Цвет фона за свернутыми диапазонами. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже декоративные элементы.\",\n\t\t\"Цвет элемента управления свертыванием во внутреннем поле редактора.\",\n\t\t\"Значок для развернутых диапазонов на поле глифов редактора.\",\n\t\t\"Значок для свернутых диапазонов на поле глифов редактора.\",\n\t\t\"Значок для свернутых вручную диапазонов на полях глифа редактора.\",\n\t\t\"Значок для развернутых вручную диапазонов на полях глифа редактора.\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"Увеличить шрифт редактора\",\n\t\t\"Уменьшить шрифт редактора\",\n\t\t\"Сбросить масштаб шрифта редактора\",\n\t],\n\t\"vs/editor/contrib/format/browser/format\": [\n\t\t\"Внесена одна правка форматирования в строке {0}.\",\n\t\t\"Внесены правки форматирования ({0}) в строке {1}.\",\n\t\t\"Внесена одна правка форматирования между строками {0} и {1}.\",\n\t\t\"Внесены правки форматирования ({0}) между строками {1} и {2}.\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"Форматировать документ\",\n\t\t\"Форматировать выделенный фрагмент\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"Перейти к Следующей Проблеме (Ошибке, Предупреждению, Информации)\",\n\t\t\"Значок для перехода к следующему маркеру.\",\n\t\t\"Перейти к Предыдущей Проблеме (Ошибке, Предупреждению, Информации)\",\n\t\t\"Значок для перехода к предыдущему маркеру.\",\n\t\t\"Перейти к следующей проблеме в файлах (ошибки, предупреждения, информационные сообщения)\",\n\t\t\"Следующая &&проблема\",\n\t\t\"Перейти к предыдущей проблеме в файлах (ошибки, предупреждения, информационные сообщения)\",\n\t\t\"Предыдущая &&проблема\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"Ошибка\",\n\t\t\"Предупреждение\",\n\t\t\"Информация\",\n\t\t\"Указание\",\n\t\t\"{0} в {1}. \",\n\t\t\"Проблемы: {0} из {1}\",\n\t\t\"Проблемы: {0} из {1}\",\n\t\t\"Цвет ошибки в мини-приложении навигации по меткам редактора.\",\n\t\t\"Фон заголовка ошибки в мини-приложении навигации по меткам редактора.\",\n\t\t\"Цвет предупреждения в мини-приложении навигации по меткам редактора.\",\n\t\t\"Фон заголовка предупреждения в мини-приложении навигации по меткам редактора.\",\n\t\t\"Цвет информационного сообщения в мини-приложении навигации по меткам редактора.\",\n\t\t\"Фон заголовка информационного сообщения в мини-приложении навигации по меткам редактора.\",\n\t\t\"Фон мини-приложения навигации по меткам редактора.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"Обзор\",\n\t\t\"Определения\",\n\t\t\"Определение для \\\"{0}\\\" не найдено.\",\n\t\t\"Определения не найдены.\",\n\t\t\"Перейти к определению\",\n\t\t\"Перейти к &&определению\",\n\t\t\"Открыть определение сбоку\",\n\t\t\"Показать определение\",\n\t\t\"Объявления\",\n\t\t\"Объявление для \\\"{0}\\\" не найдено.\",\n\t\t\"Объявление не найдено\",\n\t\t\"Перейти к объявлению\",\n\t\t\"Перейти к &&объявлению\",\n\t\t\"Объявление для \\\"{0}\\\" не найдено.\",\n\t\t\"Объявление не найдено\",\n\t\t\"Просмотреть объявление\",\n\t\t\"Определения типов\",\n\t\t\"Не найдено определение типа для \\\"{0}\\\".\",\n\t\t\"Не найдено определение типа.\",\n\t\t\"Перейти к определению типа\",\n\t\t\"Перейти к &&определению типа\",\n\t\t\"Показать определение типа\",\n\t\t\"Реализации\",\n\t\t\"Не найдена реализация для \\\"{0}\\\".\",\n\t\t\"Не найдена реализация.\",\n\t\t\"Перейти к реализациям\",\n\t\t\"Перейти к &&реализациям\",\n\t\t\"Просмотреть реализации\",\n\t\t\"Ссылки для \\\"{0}\\\" не найдены\",\n\t\t\"Ссылки не найдены\",\n\t\t\"Перейти к ссылкам\",\n\t\t\"Перейти к &&ссылкам\",\n\t\t\"Ссылки\",\n\t\t\"Показать ссылки\",\n\t\t\"Ссылки\",\n\t\t\"Перейти к любому символу\",\n\t\t\"Расположения\",\n\t\t\"Нет результатов для \\\"{0}\\\"\",\n\t\t\"Ссылки\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"Щелкните, чтобы отобразить определения ({0}).\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"Открыто ли окно просмотра ссылок, например, \\\"Ссылки для просмотра\\\" или \\\"Определение просмотра\\\"\",\n\t\t\"Загрузка...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"Ссылок: {0}\",\n\t\t\"{0} ссылка\",\n\t\t\"Ссылки\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"предварительный просмотр недоступен\",\n\t\t\"Результаты отсутствуют\",\n\t\t\"Ссылки\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"в {0} в строке {1} в столбце {2}\",\n\t\t\"{0} в {1} в строке {2} в столбце {3}\",\n\t\t\"1 символ в {0}, полный путь: {1}\",\n\t\t\"{0} символов в {1}, полный путь: {2} \",\n\t\t\"Результаты не найдены\",\n\t\t\"Обнаружен 1 символ в {0}\",\n\t\t\"Обнаружено {0} символов в {1}\",\n\t\t\"Обнаружено {0} символов в {1} файлах\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"Существуют ли расположения символов, к которым можно перейти только с помощью клавиатуры\",\n\t\t\"Символ {0} из {1}, {2} для следующего\",\n\t\t\"Символ {0} из {1}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"Показать наведение или перевести на него фокус\",\n\t\t\"Проверьте этот аспект в доступном представлении с помощью {0}\",\n\t\t\"Проверьте этот аспект в доступном представлении с помощью команды \\\"Открыть доступное представление\\\", которая в настоящее время недоступна для активации с помощью сочетания клавиш\",\n\t\t\"Отображать предварительный просмотр определения при наведении курсора мыши\",\n\t\t\"Прокрутить наведение вверх\",\n\t\t\"Прокрутить наведение вниз\",\n\t\t\"Прокрутить наведение влево\",\n\t\t\"Прокрутить наведение вправо\",\n\t\t\"Перейти на страницу вверх в наведении\",\n\t\t\"Перейти на страницу вниз в наведении\",\n\t\t\"Перейти к верхнему наведению\",\n\t\t\"Перейти к нижнему наведению\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"Загрузка...\",\n\t\t\"Отрисовка приостановлена для длинной строки из соображений производительности. Это можно настроить с помощью параметра editor.stopRenderingLineAfter.\",\n\t\t\"Разметка пропускается для длинных строк из соображений производительности. Это можно настроить с помощью \\\"editor.maxTokenizationLineLength\\\".\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"Просмотреть проблему\",\n\t\t\"Исправления недоступны\",\n\t\t\"Проверка наличия исправлений...\",\n\t\t\"Исправления недоступны\",\n\t\t\"Быстрое исправление...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"Заменить предыдущим значением\",\n\t\t\"Заменить следующим значением\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"Преобразовать отступ в пробелы\",\n\t\t\"Преобразовать отступ в шаги табуляции\",\n\t\t\"Настроенный размер шага табуляции\",\n\t\t\"Размер табуляции по умолчанию\",\n\t\t\"Текущий размер табуляции\",\n\t\t\"Выбрать размер шага табуляции для текущего файла\",\n\t\t\"Отступ с использованием табуляции\",\n\t\t\"Отступ с использованием пробелов\",\n\t\t\"Изменить отображаемый размер табуляции\",\n\t\t\"Определение отступа от содержимого\",\n\t\t\"Повторно расставить отступы строк\",\n\t\t\"Повторно расставить отступы для выбранных строк\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"Дважды щелкните, чтобы вставить\",\n\t\t\"CMD + щелчок\",\n\t\t\"CTRL + щелчок\",\n\t\t\"OPTION + щелчок\",\n\t\t\"ALT + щелчок\",\n\t\t\"Перейти к определению ({0}), щелкните правой кнопкой мыши для просмотра дополнительных сведений\",\n\t\t\"Перейти к определению ({0})\",\n\t\t\"Выполнить команду\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"Показывать следующее встроенное предложение\",\n\t\t\"Показать предыдущее встроенное предложение\",\n\t\t\"Активировать встроенное предложение\",\n\t\t\"Принять следующее слово встроенного предложения\",\n\t\t\"Принять Word\",\n\t\t\"Принять следующую строку встроенного предложения\",\n\t\t\"Принять строку\",\n\t\t\"Принять встроенное предложение\",\n\t\t\"Принять\",\n\t\t\"Скрыть встроенное предложение\",\n\t\t\"Всегда отображать панель инструментов\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"Предложение:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"Отображается ли встроенное предложение\",\n\t\t\"Начинается ли встроенное предложение с пробела\",\n\t\t\"Проверяет, не является ли пробел перед встроенной рекомендацией короче, чем текст, вставляемый клавишей TAB\",\n\t\t\"Следует ли подавлять предложения для текущего предложения\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"Значок для отображения подсказки следующего параметра.\",\n\t\t\"Значок для отображения подсказки предыдущего параметра.\",\n\t\t\"{0} ({1})\",\n\t\t\"Назад\",\n\t\t\"Далее\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"Развернуть выделение строки\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"Копировать строку сверху\",\n\t\t\"&&Копировать на строку выше\",\n\t\t\"Копировать строку снизу\",\n\t\t\"Копировать на строку &&ниже\",\n\t\t\"Дублировать выбранное\",\n\t\t\"&&Дублировать выбранное\",\n\t\t\"Переместить строку вверх\",\n\t\t\"Переместить на с&&троку выше\",\n\t\t\"Переместить строку вниз\",\n\t\t\"&&Переместить на строку ниже\",\n\t\t\"Сортировка строк по возрастанию\",\n\t\t\"Сортировка строк по убыванию\",\n\t\t\"Удалить дублирующиеся строки\",\n\t\t\"Удалить конечные символы-разделители\",\n\t\t\"Удалить строку\",\n\t\t\"Увеличить отступ\",\n\t\t\"Уменьшить отступ\",\n\t\t\"Вставить строку выше\",\n\t\t\"Вставить строку ниже\",\n\t\t\"Удалить все слева\",\n\t\t\"Удалить все справа\",\n\t\t\"_Объединить строки\",\n\t\t\"Транспонировать символы вокруг курсора\",\n\t\t\"Преобразовать в верхний регистр\",\n\t\t\"Преобразовать в нижний регистр\",\n\t\t\"Преобразовать в заглавные буквы\",\n\t\t\"Преобразовать в написание с подчеркиваниями\",\n\t\t\"Преобразовать в \\\"верблюжий\\\" стиль\",\n\t\t\"Преобразовать в кебаб-кейс\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"Запустить связанное редактирование\",\n\t\t\"Цвет фона при автоматическом переименовании типа редактором.\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"Не удалось открыть ссылку, так как она имеет неправильный формат: {0}\",\n\t\t\"Не удалось открыть ссылку, у нее отсутствует целевой объект.\",\n\t\t\"Выполнить команду\",\n\t\t\"перейти по ссылке\",\n\t\t\"Кнопка CMD и щелчок левой кнопкой мыши\",\n\t\t\"Кнопка CTRL и щелчок левой кнопкой мыши\",\n\t\t\"Кнопка OPTION и щелчок левой кнопкой мыши\",\n\t\t\"Кнопка ALT и щелчок левой кнопкой мыши\",\n\t\t\"Выполнение команды {0}\",\n\t\t\"Открыть ссылку\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"Отображается ли сейчас в редакторе внутреннее сообщение\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"Курсор добавлен: {0}\",\n\t\t\"Курсоры добавлены: {0}\",\n\t\t\"Добавить курсор выше\",\n\t\t\"Добавить курсор &&выше\",\n\t\t\"Добавить курсор ниже\",\n\t\t\"Добавить курсор &&ниже\",\n\t\t\"Добавить курсоры к окончаниям строк\",\n\t\t\"Добавить курсоры в &&окончания строк\",\n\t\t\"Добавить курсоры ниже\",\n\t\t\"Добавить курсоры выше\",\n\t\t\"Добавить выделение в следующее найденное совпадение\",\n\t\t\"Добавить &&следующее вхождение\",\n\t\t\"Добавить выделенный фрагмент в предыдущее найденное совпадение\",\n\t\t\"Добавить &&предыдущее вхождение\",\n\t\t\"Переместить последнее выделение в следующее найденное совпадение\",\n\t\t\"Переместить последний выделенный фрагмент в предыдущее найденное совпадение\",\n\t\t\"Выбрать все вхождения найденных совпадений\",\n\t\t\"Выбрать все &&вхождения\",\n\t\t\"Изменить все вхождения\",\n\t\t\"Фокусировка на следующем курсоре\",\n\t\t\"Фокусируется на следующем курсоре\",\n\t\t\"Фокусировка на предыдущем курсоре\",\n\t\t\"Фокусируется на предыдущем курсоре\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"Переключить подсказки к параметрам\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"Значок для отображения подсказки следующего параметра.\",\n\t\t\"Значок для отображения подсказки предыдущего параметра.\",\n\t\t\"{0}, указание\",\n\t\t\"Цвет переднего плана активного элемента в указании параметра.\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"Встроен ли текущий редактор кода в окно просмотра\",\n\t\t\"Закрыть\",\n\t\t\"Цвет фона области заголовка быстрого редактора.\",\n\t\t\"Цвет заголовка быстрого редактора.\",\n\t\t\"Цвет сведений о заголовке быстрого редактора.\",\n\t\t\"Цвет границ быстрого редактора и массива.\",\n\t\t\"Цвет фона в списке результатов представления быстрого редактора.\",\n\t\t\"Цвет переднего плана узлов строки в списке результатов быстрого редактора.\",\n\t\t\"Цвет переднего плана узлов файла в списке результатов быстрого редактора.\",\n\t\t\"Цвет фона выбранной записи в списке результатов быстрого редактора.\",\n\t\t\"Цвет переднего плана выбранной записи в списке результатов быстрого редактора.\",\n\t\t\"Цвет фона быстрого редактора.\",\n\t\t\"Цвет фона поля в окне быстрого редактора.\",\n\t\t\"Цвет фона залипания прокрутки в окне быстрого редактора.\",\n\t\t\"Цвет выделения совпадений в списке результатов быстрого редактора.\",\n\t\t\"Цвет выделения совпадений в быстром редакторе.\",\n\t\t\"Граница выделения совпадений в быстром редакторе.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"Чтобы перейти к строке, сначала откройте текстовый редактор.\",\n\t\t\"Перейдите к строке {0} и столбцу {1}.\",\n\t\t\"Перейти к строке {0}.\",\n\t\t\"Текущая строка: {0}, символ: {1}. Введите номер строки между 1 и {2} для перехода.\",\n\t\t\"Текущая строка: {0}, символ: {1}. Введите номер строки для перехода.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"Чтобы перейти к символу, сначала откройте текстовый редактор с символьной информацией.\",\n\t\t\"Активный текстовый редактор не предоставляет символьную информацию.\",\n\t\t\"Нет совпадающих символов редактора\",\n\t\t\"Нет символов редактора\",\n\t\t\"Открыть сбоку\",\n\t\t\"Открыть внизу\",\n\t\t\"символы ({0})\",\n\t\t\"свойства ({0})\",\n\t\t\"методы ({0})\",\n\t\t\"функции ({0})\",\n\t\t\"конструкторы ({0})\",\n\t\t\"переменные ({0})\",\n\t\t\"классы ({0})\",\n\t\t\"структуры ({0})\",\n\t\t\"события ({0})\",\n\t\t\"операторы ({0})\",\n\t\t\"интерфейсы ({0})\",\n\t\t\"пространства имен ({0})\",\n\t\t\"пакеты ({0})\",\n\t\t\"параметры типа ({0})\",\n\t\t\"модули ({0})\",\n\t\t\"свойства ({0})\",\n\t\t\"перечисления ({0})\",\n\t\t\"элемента перечисления ({0})\",\n\t\t\"строки ({0})\",\n\t\t\"файлы ({0})\",\n\t\t\"массивы ({0})\",\n\t\t\"числа ({0})\",\n\t\t\"логические значения ({0})\",\n\t\t\"объекты ({0})\",\n\t\t\"ключи ({0})\",\n\t\t\"поля ({0})\",\n\t\t\"константы ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"Не удается внести изменения во входные данные только для чтения\",\n\t\t\"Не удается выполнить изменение в редакторе только для чтения\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"Результаты отсутствуют.\",\n\t\t\"Произошла неизвестная ошибка при определении расположения после переименования\",\n\t\t\"Переименование \\\"{0}\\\" в \\\"{1}\\\"\",\n\t\t\"Переименование {0} в {1}\",\n\t\t\"«{0}» успешно переименован в «{1}». Сводка: {2}\",\n\t\t\"Операции переименования не удалось применить правки\",\n\t\t\"Операции переименования не удалось вычислить правки\",\n\t\t\"Переименовать символ\",\n\t\t\"Включить/отключить возможность предварительного просмотра изменений перед переименованием\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"Отображается ли мини-приложение переименования входных данных\",\n\t\t\"Введите новое имя для входных данных и нажмите клавишу ВВОД для подтверждения.\",\n\t\t\"Нажмите {0} для переименования, {1} для просмотра.\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"Развернуть выбранный фрагмент\",\n\t\t\"&&Развернуть выделение\",\n\t\t\"Уменьшить выделенный фрагмент\",\n\t\t\"&&Сжать выделение\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"Находится ли текущий редактор в режиме фрагментов\",\n\t\t\"Указывает, существует ли следующая позиция табуляции в режиме фрагментов\",\n\t\t\"Указывает, существует ли предыдущая позиция табуляции в режиме фрагментов\",\n\t\t\"Перейти к следующему заполнителю...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"воскресенье\",\n\t\t\"понедельник\",\n\t\t\"вторник\",\n\t\t\"среда\",\n\t\t\"четверг\",\n\t\t\"пятница\",\n\t\t\"суббота\",\n\t\t\"Вс\",\n\t\t\"Пн\",\n\t\t\"Вт\",\n\t\t\"Ср\",\n\t\t\"Чт\",\n\t\t\"Пт\",\n\t\t\"Сб\",\n\t\t\"Январь\",\n\t\t\"Февраль\",\n\t\t\"Март\",\n\t\t\"Апрель\",\n\t\t\"Май\",\n\t\t\"Июнь\",\n\t\t\"Июль\",\n\t\t\"Август\",\n\t\t\"Сентябрь\",\n\t\t\"Октябрь\",\n\t\t\"Ноябрь\",\n\t\t\"Декабрь\",\n\t\t\"Янв\",\n\t\t\"Фев\",\n\t\t\"Мар\",\n\t\t\"Апр\",\n\t\t\"Май\",\n\t\t\"Июн\",\n\t\t\"Июл\",\n\t\t\"Авг\",\n\t\t\"Сен\",\n\t\t\"Окт\",\n\t\t\"Ноя\",\n\t\t\"Дек\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"Переключить залипание прокрутки\",\n\t\t\"&&Переключить залипание прокрутки\",\n\t\t\"Залипание прокрутки\",\n\t\t\"&&Залипание прокрутки\",\n\t\t\"Фокус на залипании прокрутки\",\n\t\t\"&&Фокус на залипании прокрутки\",\n\t\t\"Выбрать следующую строку залипания прокрутки\",\n\t\t\"Выбрать предыдущую строку залипания прокрутки\",\n\t\t\"Перейти к строке залипания прокрутки, которая находится в фокусе\",\n\t\t\"Выберите редактор\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"Находится ли какое-либо предложение в фокусе\",\n\t\t\"Отображаются ли сведения о предложениях\",\n\t\t\"Существует ли несколько предложений для выбора\",\n\t\t\"Приводит ли вставка текущего предложения к изменению или все уже было введено\",\n\t\t\"Вставляются ли предложения при нажатии клавиши ВВОД\",\n\t\t\"Есть ли у текущего предложения варианты поведения \\\"вставка\\\" и \\\"замена\\\"\",\n\t\t\"Является ли текущее поведение поведением \\\"вставка\\\" или \\\"замена\\\"\",\n\t\t\"Поддерживает ли текущее предложение разрешение дополнительных сведений\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"Принятие \\\"{0}\\\" привело к внесению дополнительных правок ({1})\",\n\t\t\"Переключить предложение\",\n\t\t\"Вставить\",\n\t\t\"Вставить\",\n\t\t\"Заменить\",\n\t\t\"Заменить\",\n\t\t\"Вставить\",\n\t\t\"показать меньше\",\n\t\t\"показать больше\",\n\t\t\"Сброс предложения размера мини-приложения\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"Цвет фона виджета подсказок.\",\n\t\t\"Цвет границ виджета подсказок.\",\n\t\t\"Цвет переднего плана мини-приложения предложений.\",\n\t\t\"Цвет переднего плана выбранной записи в мини-приложении предложений.\",\n\t\t\"Цвет переднего плана значка выбранной записи в мини-приложении предложений.\",\n\t\t\"Фоновый цвет выбранной записи в мини-приложении предложений.\",\n\t\t\"Цвет выделения соответствия в мини-приложении предложений.\",\n\t\t\"Цвет совпадения выделяется в мини-приложениях предложений, когда элемент находится в фокусе.\",\n\t\t\"Цвет переднего плана для состояния рекомендации мини-приложения.\",\n\t\t\"Загрузка...\",\n\t\t\"Предложения отсутствуют.\",\n\t\t\"Предложить\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, документы: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"Закрыть\",\n\t\t\"Загрузка...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"Значок для получения дополнительных сведений в мини-приложении предложений.\",\n\t\t\"Подробнее\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"Цвет переднего плана для символов массива. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для логических символов. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов класса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов цвета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов константы. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов конструктора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов члена перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов события. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов поля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов файла. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов папки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов функции. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов интерфейса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов ключа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов ключевого слова. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов метода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов модуля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов пространства имен. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов NULL. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов числа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов объекта. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов оператора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов пакета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов свойства. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов ссылки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов фрагмента кода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов строки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов структуры. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов текста. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов типа параметров. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов единиц. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t\t\"Цвет переднего плана для символов переменной. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"Переключение клавиши TAB перемещает фокус.\",\n\t\t\"При нажатии клавиши TAB фокус перейдет на следующий элемент, который может получить фокус\",\n\t\t\"Теперь при нажатии клавиши TAB будет вставлен символ табуляции\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"Разработчик: принудительная повторная установка токенов\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"Значок, отображаемый с предупреждением в редакторе расширений.\",\n\t\t\"Этот документ содержит много нестандартных символов Юникода ASCII\",\n\t\t\"Этот документ содержит много неоднозначных символов Юникода\",\n\t\t\"Этот документ содержит много невидимых символов Юникода\",\n\t\t\"Символ {0} можно спутать с символом ASCII {1}, который чаще встречается в исходном коде.\",\n\t\t\"Символ {0} можно спутать с символом {1}, который чаще встречается в исходном коде.\",\n\t\t\"Символ {0} невидим.\",\n\t\t\"Символ {0} не является базовым символом ASCII.\",\n\t\t\"Настройка параметров\",\n\t\t\"Отключить выделение в комментариях\",\n\t\t\"Отключить выделение символов в комментариях\",\n\t\t\"Отключить выделение в строках\",\n\t\t\"Отключить выделение символов в строках\",\n\t\t\"Отключить неоднозначное выделение\",\n\t\t\"Отключить выделение неоднозначных символов\",\n\t\t\"Отключить невидимое выделение\",\n\t\t\"Отключить выделение невидимых символов\",\n\t\t\"Отключить выделение, отличное от ASCII\",\n\t\t\"Отключить выделение нестандартных символов ASCII\",\n\t\t\"Показать параметры исключения\",\n\t\t\"Исключить {0} (невидимый символ) из выделения\",\n\t\t\"Исключить {0} из выделения\",\n\t\t\"Разрешите символы Юникода, более распространенные в языке \\\"{0}\\\".\",\n\t\t\"Настройка параметров выделения Юникода\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"Необычные символы завершения строки\",\n\t\t\"Обнаружены необычные символы завершения строки\",\n\t\t\"Файл \\\"{0}\\\" содержит один или несколько необычных символов завершения строки, таких как разделитель строк (LS) или разделитель абзацев (PS).\\r\\n\\r\\nРекомендуется удалить их из файла. Удаление этих символов можно настроить с помощью параметра \\\"editor.unusualLineTerminators\\\".\",\n\t\t\"&&Удалить необычные символы завершения строки\",\n\t\t\"Пропустить\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"Цвет фона символа при доступе на чтение, например, при чтении переменной. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона для символа во время доступа на запись, например при записи в переменную. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона текстового вхождения символа. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.\",\n\t\t\"Цвет границы символа при доступе на чтение, например, при считывании переменной.\",\n\t\t\"Цвет границы символа при доступе на запись, например, при записи переменной. \",\n\t\t\"Цвет границы текстового вхождения символа.\",\n\t\t\"Цвет маркера обзорной линейки для выделения символов. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.\",\n\t\t\"Цвет маркера обзорной линейки для выделения символов доступа на запись. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет маркера обзорной линейки текстового вхождения символа. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"Перейти к следующему выделению символов\",\n\t\t\"Перейти к предыдущему выделению символов\",\n\t\t\"Включить или отключить выделение символов\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"Удалить слово\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"Представление\",\n\t\t\"Справка\",\n\t\t\"Тест\",\n\t\t\"Файл\",\n\t\t\"Параметры\",\n\t\t\"Разработчик\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0}, чтобы применить, {1} для предварительного просмотра\",\n\t\t\"{0}, чтобы применить\",\n\t\t\"{0}, причина отключения: {1}\",\n\t\t\"Мини-приложения действий\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"Отображается ли список мини-приложений действий\",\n\t\t\"Скрыть мини-приложение действия\",\n\t\t\"Выбрать предыдущее действие\",\n\t\t\"Выбрать следующее действие\",\n\t\t\"Принять выбранное действие\",\n\t\t\"Предварительный просмотр выбранного действия\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"Скрыть\",\n\t\t\"Сбросить меню\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"Скрыть \\\"{0}\\\"\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"Ошибка в строке\",\n\t\t\"Предупреждение в строке\",\n\t\t\"Сложенная область в строке\",\n\t\t\"Точка останова в строке\",\n\t\t\"Встроенная рекомендация в строке\",\n\t\t\"Быстрое исправление терминала\",\n\t\t\"Отладчик остановлен в точке останова\",\n\t\t\"Отсутствие встроенных подсказок в строке\",\n\t\t\"Задача завершена\",\n\t\t\"Сбой задачи\",\n\t\t\"Сбой команды терминала\",\n\t\t\"Звонок терминала\",\n\t\t\"Ячейка записной книжки выполнена\",\n\t\t\"Сбой ячейки записной книжки\",\n\t\t\"Вставлена разностная строка\",\n\t\t\"Удалена разностная строка\",\n\t\t\"Изменена строка различий\",\n\t\t\"Отправлен запрос на чат\",\n\t\t\"Получен ответ чата\",\n\t\t\"Ожидание ответа чата\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"Переопределения конфигурации языка по умолчанию\",\n\t\t\"Настройка переопределяемых параметров для языка {0}.\",\n\t\t\"Настройка параметров редактора, переопределяемых для языка.\",\n\t\t\"Этот параметр не поддерживает настройку для отдельных языков.\",\n\t\t\"Настройка параметров редактора, переопределяемых для языка.\",\n\t\t\"Этот параметр не поддерживает настройку для отдельных языков.\",\n\t\t\"Не удается зарегистрировать пустое свойство\",\n\t\t\"Невозможно зарегистрировать \\\"{0}\\\". Оно соответствует шаблону свойства \\'\\\\\\\\[.*\\\\\\\\]$\\' для описания параметров редактора, определяемых языком. Используйте участие configurationDefaults.\",\n\t\t\"Невозможно зарегистрировать \\\"{0}\\\". Это свойство уже зарегистрировано.\",\n\t\t\"Невозможно зарегистрировать \\\"{0}\\\". Уже имеется регистрация {2} для связанной политики {1}.\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"Команда, возвращающая сведения о ключах контекста\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"Пустое выражение ключа контекста\",\n\t\t\"Вы забыли записать выражение? Вы также можете поместить \\\"false\\\" или \\\"true\\\", чтобы всегда оценивать по значению false или true соответственно.\",\n\t\t\"\\\"in\\\" после \\\"not\\\".\",\n\t\t\"закрывающая круглая скобка \\\")\\\"\",\n\t\t\"Непредвиденный маркер\",\n\t\t\"Возможно, вы забыли поместить && или || перед маркером?\",\n\t\t\"Неожиданный конец выражения\",\n\t\t\"Возможно, вы забыли поместить ключ контекста?\",\n\t\t\"Ожидается: {0}\\r\\nПолучено: \\\"{1}\\\".\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"Используется ли операционная система macOS\",\n\t\t\"Используется ли операционная система Linux\",\n\t\t\"Используется ли операционная система Windows\",\n\t\t\"Является ли платформа браузерной\",\n\t\t\"Используется ли операционная система macOS на платформе, отличной от браузерной\",\n\t\t\"Используется ли операционная система IOS\",\n\t\t\"Является ли платформа мобильным браузером\",\n\t\t\"Тип качества VS Code\",\n\t\t\"Находится ли фокус клавиатуры в поле ввода\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"Вы имели в виду {0}?\",\n\t\t\"Вы имели в виду {0} или {1}?\",\n\t\t\"Вы имели в виду {0}, {1} или {2}?\",\n\t\t\"Вы забыли открыть или закрыть цитату?\",\n\t\t\"Вы забыли экранировать символ \\\"/\\\" (косая черта)? Чтобы экранировать, поместите перед символом две обратные косые черты, например \\\"\\\\\\\\/\\\".\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"Отображаются ли предложения\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"Была нажата клавиша {0}. Ожидание нажатия второй клавиши сочетания...\",\n\t\t\"Была нажата клавиша ({0}). Ожидание нажатия следующей клавиши сочетания...\",\n\t\t\"Сочетание клавиш ({0} и {1}) не является командой.\",\n\t\t\"Сочетание клавиш ({0} и {1}) не является командой.\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"Рабочее место\",\n\t\t\"Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.\",\n\t\t\"Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.\",\n\t\t\"Модификатор, который будет использоваться для добавления элементов в деревьях и списках в элемент множественного выбора с помощью мыши (например, в проводнике, в открытых редакторах и в представлении scm). Жесты мыши \\\"Открыть сбоку\\\" (если они поддерживаются) будут изменены таким образом, чтобы они не конфликтовали с модификатором элемента множественного выбора.\",\n\t\t\"Управляет тем, как открывать элементы в деревьях и списках с помощью мыши (если поддерживается). Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним.\",\n\t\t\"Определяет, поддерживают ли горизонтальную прокрутку списки и деревья на рабочем месте. Предупреждение! Включение этого параметра может повлиять на производительность.\",\n\t\t\"Определяет, следует ли щелкать полосу прокрутки постранично.\",\n\t\t\"Определяет отступ для дерева в пикселях.\",\n\t\t\"Определяет, нужно ли в дереве отображать направляющие отступа.\",\n\t\t\"Управляет тем, используется ли плавная прокрутка для списков и деревьев.\",\n\t\t\"Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.\",\n\t\t\"Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.\",\n\t\t\"При поиске необходимо выделять элементы. При дальнейшей навигации вверх и вниз выполняется обход только выделенных элементов.\",\n\t\t\"Фильтруйте элементы при поиске.\",\n\t\t\"Управляет режимом поиска по умолчанию для списков и деревьев в Workbench.\",\n\t\t\"Про простой навигации с клавиатуры выбираются элементы, соответствующие вводимым с клавиатуры данным. Сопоставление осуществляется только по префиксам.\",\n\t\t\"Функция подсветки навигации с клавиатуры выделяет элементы, соответствующие вводимым с клавиатуры данным. При дальнейшей навигации вверх и вниз выполняется обход только выделенных элементов.\",\n\t\t\"Фильтр навигации с клавиатуры позволяет отфильтровать и скрыть все элементы, не соответствующие вводимым с клавиатуры данным.\",\n\t\t\"Управляет стилем навигации с клавиатуры для списков и деревьев в Workbench. Доступен простой режим, режим выделения и режим фильтрации.\",\n\t\t\"Вместо этого используйте \\\"workbench.list.defaultFindMode\\\" и \\\"workbench.list.typeNavigationMode\\\".\",\n\t\t\"Использовать нечеткое соответствие при поиске.\",\n\t\t\"Использовать непрерывное сопоставление при поиске.\",\n\t\t\"Управляет типом сопоставления, используемым при поиске списков и деревьев в Workbench.\",\n\t\t\"Управляет тем, как папки дерева разворачиваются при нажатии на имена папок. Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним.\",\n\t\t\"Управляет навигацией по типам в списках и деревьях в рабочей среде. Если установлено значение \\\"триггер\\\", навигация по типу начинается после запуска команды \\\"list.triggerTypeNavigation\\\".\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"Ошибка\",\n\t\t\"Предупреждение\",\n\t\t\"Информация\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"недавно использованные\",\n\t\t\"часто используемые\",\n\t\t\"другие команды\",\n\t\t\"{0}, {1}\",\n\t\t\"Команда \\\"{0}\\\" привела к ошибке\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"Назад\",\n\t\t\"Нажмите клавишу ВВОД, чтобы подтвердить введенные данные, или ESCAPE для отмены\",\n\t\t\"{0} / {1}\",\n\t\t\"Введите текст, чтобы уменьшить число результатов.\",\n\t\t\"Переключить все флажки\",\n\t\t\"Результаты: {0}\",\n\t\t\"{0} выбрано\",\n\t\t\"ОК\",\n\t\t\"Другой\",\n\t\t\"Назад ({0})\",\n\t\t\"Назад\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"Быстрый ввод\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"Щелкните, чтобы выполнить команду \\\"{0}\\\"\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"Общий цвет переднего плана. Этот цвет используется, только если его не переопределит компонент.\",\n\t\t\"Общий цвет переднего плана для отключенных элементов. Этот цвет используется только в том случае, если он не переопределен компонентом.\",\n\t\t\"Общий цвет переднего плана для сообщений об ошибках. Этот цвет используется только если его не переопределяет компонент.\",\n\t\t\"Цвет текста элемента, содержащего пояснения, например, для метки.\",\n\t\t\"Цвет по умолчанию для значков на рабочем месте.\",\n\t\t\"Общий цвет границ для элементов с фокусом. Этот цвет используется только в том случае, если не переопределен в компоненте.\",\n\t\t\"Дополнительная граница вокруг элементов, которая отделяет их от других элементов для улучшения контраста.\",\n\t\t\"Дополнительная граница вокруг активных элементов, которая отделяет их от других элементов для улучшения контраста.\",\n\t\t\"Цвет фона выделенного текста в рабочей области (например, в полях ввода или в текстовых полях). Не применяется к выделенному тексту в редакторе.\",\n\t\t\"Цвет для разделителей текста.\",\n\t\t\"Цвет переднего плана для ссылок в тексте.\",\n\t\t\"Цвет переднего плана для ссылок в тексте при щелчке и при наведении курсора мыши.\",\n\t\t\"Цвет текста фиксированного формата.\",\n\t\t\"Цвет фона для блоков с цитатами в тексте.\",\n\t\t\"Цвет границ для блоков с цитатами в тексте.\",\n\t\t\"Цвет фона для программного кода в тексте.\",\n\t\t\"Цвет тени мини-приложений редактора, таких как \\\"Найти/заменить\\\".\",\n\t\t\"Цвет границы мини-приложений редактора, таких как \\\"Найти/заменить\\\".\",\n\t\t\"Фон поля ввода.\",\n\t\t\"Передний план поля ввода.\",\n\t\t\"Граница поля ввода.\",\n\t\t\"Цвет границ активированных параметров в полях ввода.\",\n\t\t\"Цвет фона активированных параметров в полях ввода.\",\n\t\t\"Цвет фонового наведения параметров в полях ввода.\",\n\t\t\"Цвет переднего плана активированных параметров в полях ввода.\",\n\t\t\"Цвет фона поясняющего текста в элементе ввода.\",\n\t\t\"Фоновый цвет проверки ввода для уровня серьезности \\\"Сведения\\\".\",\n\t\t\"Цвет переднего плана области проверки ввода для уровня серьезности \\\"Сведения\\\".\",\n\t\t\"Цвет границы проверки ввода для уровня серьезности \\\"Сведения\\\".\",\n\t\t\"Фоновый цвет проверки ввода для уровня серьезности \\\"Предупреждение\\\".\",\n\t\t\"Цвет переднего плана области проверки ввода для уровня серьезности \\\"Предупреждение\\\".\",\n\t\t\"Цвет границы проверки ввода для уровня серьезности \\\"Предупреждение\\\".\",\n\t\t\"Фоновый цвет проверки ввода для уровня серьезности \\\"Ошибка\\\".\",\n\t\t\"Цвет переднего плана области проверки ввода для уровня серьезности \\\"Ошибка\\\".\",\n\t\t\"Цвет границы проверки ввода для уровня серьезности \\\"Ошибка\\\".\",\n\t\t\"Фон раскрывающегося списка.\",\n\t\t\"Цвет фона раскрывающегося списка.\",\n\t\t\"Передний план раскрывающегося списка.\",\n\t\t\"Граница раскрывающегося списка.\",\n\t\t\"Цвет переднего плана кнопки.\",\n\t\t\"Цвет разделителя кнопок.\",\n\t\t\"Цвет фона кнопки.\",\n\t\t\"Цвет фона кнопки при наведении.\",\n\t\t\"Цвет границы кнопки.\",\n\t\t\"Цвет переднего плана вторичной кнопки.\",\n\t\t\"Цвет фона вторичной кнопки.\",\n\t\t\"Цвет фона вторичной кнопки при наведении курсора мыши.\",\n\t\t\"Цвет фона бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.\",\n\t\t\"Цвет текста бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.\",\n\t\t\"Цвет тени полосы прокрутки, которая свидетельствует о том, что содержимое прокручивается.\",\n\t\t\"Цвет фона для ползунка полосы прокрутки.\",\n\t\t\"Цвет фона ползунка полосы прокрутки при наведении курсора.\",\n\t\t\"Цвет фона ползунка полосы прокрутки при щелчке по нему.\",\n\t\t\"Цвет фона индикатора выполнения, который может отображаться для длительных операций.\",\n\t\t\"Цвет фона для текста ошибки в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет волнистой линии для выделения ошибок в редакторе.\",\n\t\t\"Если задано, цвет двойного подчеркивания ошибок в редакторе.\",\n\t\t\"Цвет фона для текста предупреждения в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет волнистой линии для выделения предупреждений в редакторе.\",\n\t\t\"Если задано, цвет двойного подчеркивания предупреждений в редакторе.\",\n\t\t\"Цвет фона для текста информационного сообщения в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет волнистой линии для выделения информационных сообщений в редакторе.\",\n\t\t\"Если задано, цвет двойного подчеркивания информационных сообщений в редакторе.\",\n\t\t\"Цвет волнистой линии для выделения подсказок в редакторе.\",\n\t\t\"Если задано, цвет двойного подчеркивания указаний в редакторе.\",\n\t\t\"Цвет границы активных лент.\",\n\t\t\"Цвет фона редактора.\",\n\t\t\"Цвет переднего плана редактора по умолчанию.\",\n\t\t\"Цвет фона для прокрутки с залипанием в редакторе\",\n\t\t\"Цвет фона для прокрутки с залипанием при наведении курсора в редакторе\",\n\t\t\"Цвет фона виджетов редактора, таких как найти/заменить.\",\n\t\t\"Цвет переднего плана мини-приложений редактора, таких как \\\"Поиск/замена\\\".\",\n\t\t\"Цвет границы мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница и если этот цвет не переопределен мини-приложением.\",\n\t\t\"Цвет границы панели изменения размера мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница для изменения размера и если этот цвет не переопределен мини-приложением.\",\n\t\t\"Цвет фона для средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.\",\n\t\t\"Цвет переднего плана для средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.\",\n\t\t\"Цвет фона для заголовка средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.\",\n\t\t\"Цвет средства быстрого выбора для группировки меток.\",\n\t\t\"Цвет средства быстрого выбора для группировки границ.\",\n\t\t\"Цвет фона метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\n\t\t\"Цвет переднего плана метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\n\t\t\"Цвет границы метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\n\t\t\"Цвет нижней границы метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\n\t\t\"Цвет выделения редактора.\",\n\t\t\"Цвет выделенного текста в режиме высокого контраста.\",\n\t\t\"Цвет выделения в неактивном редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет для областей, содержимое которых совпадает с выбранным фрагментом. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет границы регионов с тем же содержимым, что и в выделении.\",\n\t\t\"Цвет текущего поиска совпадений.\",\n\t\t\"Цвет других совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет границы текущего результата поиска.\",\n\t\t\"Цвет границы других результатов поиска.\",\n\t\t\"Цвет границы для диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет соответствий для запроса в редакторе поиска.\",\n\t\t\"Цвет границы для соответствующих запросов в редакторе поиска.\",\n\t\t\"Цвет текста в поиске сообщения завершения вьюлета.\",\n\t\t\"Выделение под словом, для которого отображается меню при наведении курсора. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона при наведении указателя на редактор.\",\n\t\t\"Цвет переднего плана для наведения указателя на редактор.\",\n\t\t\"Цвет границ при наведении указателя на редактор.\",\n\t\t\"Цвет фона строки состояния при наведении в редакторе.\",\n\t\t\"Цвет активных ссылок.\",\n\t\t\"Цвет переднего плана встроенных указаний\",\n\t\t\"Цвет фона встроенных указаний\",\n\t\t\"Цвет переднего плана встроенных указаний для шрифтов\",\n\t\t\"Цвет фона встроенных указаний для шрифтов\",\n\t\t\"Цвет переднего плана встроенных указаний для параметров\",\n\t\t\"Цвет фона встроенных указаний для параметров\",\n\t\t\"Цвет, используемый для значка действий в меню лампочки.\",\n\t\t\"Цвет, используемый для значка действий автоматического исправления в меню лампочки.\",\n\t\t\"Цвет фона для вставленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона для удаленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона для вставленных строк. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона для удаленных строк. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет фона для поля, где вставлены строки.\",\n\t\t\"Цвет фона для поля, где удалены строки.\",\n\t\t\"Передний план обзорной линейки различий для вставленного содержимого.\",\n\t\t\"Передний план обзорной линейки различий для удаленного содержимого.\",\n\t\t\"Цвет контура для добавленных строк.\",\n\t\t\"Цвет контура для удаленных строк.\",\n\t\t\"Цвет границы между двумя текстовыми редакторами.\",\n\t\t\"Цвет диагональной заливки для редактора несовпадений. Диагональная заливка используется в размещаемых рядом представлениях несовпадений.\",\n\t\t\"Цвет фона неизмененных блоков в редакторе несовпадений.\",\n\t\t\"Цвет переднего плана неизмененных блоков в редакторе несовпадений.\",\n\t\t\"Цвет фона неизмененного кода в редакторе несовпадений.\",\n\t\t\"Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет переднего плана находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен и выбран. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет переднего плана выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет переднего плана значка списка или дерева для выбранного элемента, когда список или дерево активны. Активный список или дерево находятся в фокусе клавиатуры, а неактивный — нет.\",\n\t\t\"Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет текста выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет переднего плана значка списка или дерева для выбранного элемента, когда список или дерево неактивны. Активный список или дерево находятся в фокусе клавиатуры, а неактивный — нет.\",\n\t\t\"Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\n\t\t\"Фоновый цвет элементов List/Tree при наведении курсора мыши.\",\n\t\t\"Цвет переднего плана элементов List/Tree при наведении курсора мыши.\",\n\t\t\"Фоновый цвет элементов List/Tree при перемещении с помощью мыши.\",\n\t\t\"Цвет переднего плана для выделения соответствия при поиске по элементу List/Tree.\",\n\t\t\"Цвет переднего плана для выделения соответствия выделенных элементов при поиске по элементу List/Tree.\",\n\t\t\"Цвет переднего плана списка/дерева для недопустимых элементов, например, для неразрешенного корневого узла в проводнике.\",\n\t\t\"Цвет переднего плана элементов списка, содержащих ошибки.\",\n\t\t\"Цвет переднего плана элементов списка, содержащих предупреждения.\",\n\t\t\"Цвет фона для мини-приложения фильтра типов в списках и деревьях.\",\n\t\t\"Цвет контура для мини-приложения фильтра типов в списках и деревьях.\",\n\t\t\"Цвет контура для мини-приложения фильтра типов в списках и деревьях при отсутствии совпадений.\",\n\t\t\"Цвет тени для мини-приложения фильтра типов в списках и деревьях.\",\n\t\t\"Цвет фона для отфильтрованного совпадения.\",\n\t\t\"Цвет границы для отфильтрованного совпадения.\",\n\t\t\"Цвет штриха дерева для направляющих отступа.\",\n\t\t\"Цвет штриха дерева для неактивных направляющих отступа.\",\n\t\t\"Цвет границы таблицы между столбцами.\",\n\t\t\"Цвет фона для нечетных строк таблицы.\",\n\t\t\"Цвет переднего плана в списке/дереве для элементов, выделение которых отменено.\",\n\t\t\"Цвет фона мини-приложения флажка.\",\n\t\t\"Цвет фона виджета флажка при выборе элемента, в котором он находится.\",\n\t\t\"Цвет переднего плана мини-приложения флажка.\",\n\t\t\"Цвет границы мини-приложения флажка.\",\n\t\t\"Цвет границы виджета флажка, когда выбран элемент, в котором он находится.\",\n\t\t\"Рекомендуется использовать quickInputList.focusBackground.\",\n\t\t\"Цвет переднего плана средства быстрого выбора для элемента, на котором находится фокус.\",\n\t\t\"Цвет переднего плана значка средства быстрого выбора для элемента, на котором находится фокус.\",\n\t\t\"Цвет фона средства быстрого выбора для элемента, на котором находится фокус.\",\n\t\t\"Цвет границ меню.\",\n\t\t\"Цвет переднего плана пунктов меню.\",\n\t\t\"Цвет фона пунктов меню.\",\n\t\t\"Цвет переднего плана выбранного пункта меню в меню.\",\n\t\t\"Цвет фона для выбранного пункта в меню.\",\n\t\t\"Цвет границы для выбранного пункта в меню.\",\n\t\t\"Цвет разделителя меню в меню.\",\n\t\t\"Фон панели инструментов при наведении указателя мыши на действия\",\n\t\t\"Контур панели инструментов при наведении указателя мыши на действия\",\n\t\t\"Фон панели инструментов при удержании указателя мыши над действиями\",\n\t\t\"Цвет фона выделения в позиции табуляции фрагмента.\",\n\t\t\"Цвет границы выделения в позиции табуляции фрагмента.\",\n\t\t\"Цвет фона выделения в последней позиции табуляции фрагмента.\",\n\t\t\"Выделение цветом границы в последней позиции табуляции фрагмента.\",\n\t\t\"Цвет элементов навигации, находящихся в фокусе.\",\n\t\t\"Фоновый цвет элементов навигации.\",\n\t\t\"Цвет элементов навигации, находящихся в фокусе.\",\n\t\t\"Цвет выделенных элементов навигации.\",\n\t\t\"Фоновый цвет средства выбора элементов навигации.\",\n\t\t\"Текущий цвет фона заголовка при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Фон текущего содержимого при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Фон входящего заголовка при внутренних конфликтах объединения. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Фон входящего содержимого при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Фон заголовка общего предка во внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Фон содержимого общего предка во внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет границы заголовков и разделителя во внутренних конфликтах слияния.\",\n\t\t\"Цвет переднего плана линейки текущего окна во внутренних конфликтах слияния.\",\n\t\t\"Цвет переднего плана линейки входящего окна во внутренних конфликтах слияния.\",\n\t\t\"Цвет переднего плана для обзорной линейки для общего предка во внутренних конфликтах слияния. \",\n\t\t\"Цвет маркера обзорной линейки для совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Маркер обзорной линейки для выделения выбранного фрагмента. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\n\t\t\"Цвет маркера мини-карты для поиска совпадений.\",\n\t\t\"Цвет маркера мини-карты для повторяющихся выделений редактора.\",\n\t\t\"Цвет маркера мини-карты для выбора редактора.\",\n\t\t\"Цвет маркера миникарты для ошибок.\",\n\t\t\"Цвет маркера миникарты для предупреждений.\",\n\t\t\"Цвет фона мини-карты.\",\n\t\t\"Прозрачность элементов переднего плана, отображаемая га мини-карте. Например, \\\"#000000c0\\\" отображает элементы с прозрачностью 75%.\",\n\t\t\"Цвет фона ползунка мини-карты.\",\n\t\t\"Цвет фона ползунка мини-карты при наведении на него указателя.\",\n\t\t\"Цвет фона ползунка мини-карты при его щелчке.\",\n\t\t\"Цвет, используемый для значка ошибки, указывающего на наличие проблем.\",\n\t\t\"Цвет, используемый для предупреждающего значка, указывающего на наличие проблем.\",\n\t\t\"Цвет, используемый для информационного значка, указывающего на наличие проблем.\",\n\t\t\"Цвет переднего плана на диаграммах.\",\n\t\t\"Цвет горизонтальных линий на диаграммах.\",\n\t\t\"Красный цвет, используемый в визуализациях диаграмм.\",\n\t\t\"Синий цвет, используемый в визуализациях диаграмм.\",\n\t\t\"Желтый цвет, используемый в визуализациях диаграмм.\",\n\t\t\"Оранжевый цвет, используемый в визуализациях диаграмм.\",\n\t\t\"Зеленый цвет, используемый в визуализациях диаграмм.\",\n\t\t\"Лиловый цвет, используемый в визуализациях диаграмм.\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"Идентификатор используемого шрифта. Если параметр не задан, используется шрифт, определенный первым.\",\n\t\t\"Символ шрифта, связанный с определением значка.\",\n\t\t\"Значок для действия закрытия в мини-приложениях.\",\n\t\t\"Значок для перехода к предыдущему расположению в редакторе.\",\n\t\t\"Значок для перехода к следующему расположению в редакторе.\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"Следующие файлы были закрыты и изменены на диске: {0}.\",\n\t\t\"Следующие файлы были изменены несовместимым образом: {0}.\",\n\t\t\"Не удалось отменить \\\"{0}\\\" для всех файлов. {1}\",\n\t\t\"Не удалось отменить \\\"{0}\\\" для всех файлов. {1}\",\n\t\t\"Не удалось отменить операцию \\\"{0}\\\" для всех файлов, так как были внесены изменения в {1}\",\n\t\t\"Не удалось отменить действие \\\"{0}\\\" для всех файлов, так как в {1} уже выполняется операция отмены или повтора действия\",\n\t\t\"Не удалось отменить действие \\\"{0}\\\" для всех файлов, так как уже выполнялась операция отмены или повтора действия\",\n\t\t\"Вы хотите отменить \\\"{0}\\\" для всех файлов?\",\n\t\t\"&&Отменить действие в файлах {0}\",\n\t\t\"Отменить этот &&файл\",\n\t\t\"Не удалось отменить действие \\\"{0}\\\", так как уже выполняется операция отмены или повтора действия\",\n\t\t\"Вы хотите отменить \\\"{0}\\\"?\",\n\t\t\"&&Да\",\n\t\t\"Нет\",\n\t\t\"Не удалось повторить операцию \\\"{0}\\\" для всех файлов. {1}\",\n\t\t\"Не удалось повторить операцию \\\"{0}\\\" для всех файлов. {1}\",\n\t\t\"Не удалось повторить операцию \\\"{0}\\\" для всех файлов, так как были внесены изменения в {1}\",\n\t\t\"Не удалось повторить действие \\\"{0}\\\" для всех файлов, так как для {1} уже выполняется операция отмены или повтора действия.\",\n\t\t\"Не удалось повторить действие \\\"{0}\\\" для всех файлов, так как уже выполнялась операция отмены или повтора действия\",\n\t\t\"Не удалось повторить действие \\\"{0}\\\", так как уже выполняется операция отмены или повтора действия\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Рабочая область кода\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,iFACD,EACA,gDAAiD,CAChD,+FACA,4EACA,8LACD,EACA,4CAA6C,CAC5C,kFACA,mGACD,EACA,8CAA+C,CAC9C,wDACD,EACA,uCAAwC,CACvC,4CACA,4FACA,oEACA,gEACA,wIACD,EACA,qDAAsD,CACrD,wDACD,EACA,+CAAgD,CAC/C,+DACD,EACA,qCAAsC,CACrC,0IACD,EACA,uCAAwC,CACvC,uCACA,gHACA,0IACA,mFACA,mFACA,6CACA,2GACD,EACA,yBAA0B,CACzB,kCACD,EACA,8BAA+B,CAC9B,WACA,2JACA,2UACA,2UACA,iFACA,0UACD,EACA,kCAAmC,CAClC,OACA,QACA,MACA,UACA,OACA,QACA,MACA,QACA,OACA,QACA,mDACA,6CACA,OACA,QACA,MACA,UACA,OACA,QACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,mDACA,sJACA,+UACA,ypCACA,g6BACD,EACA,iCAAkC,CACjC,0PACA,0PACA,6KACD,EACA,qCAAsC,CACrC,qDACA,mDACA,2DACA,6CACA,wEACA,+DACD,EACA,4CAA6C,CAC5C,qhCACA,0RACD,EACA,mDAAoD,CACnD,yLACA,kKACA,+dACA,uKACD,EACA,4CAA6C,CAC5C,0SACA,gTACA,qOACA,kVACA,0LACD,EACA,kEAAmE,CAClE,mhBACA,6gBACA,6gBACA,6CACA,snBACA,qGACA,0FACA,sFACA,oRACA,uCACA,wHACA,sMACA,8GACA,iGACD,EACA,oDAAqD,CACpD,qVACD,EACA,yDAA0D,CACzD,0SACA,gTACA,0LACD,EACA,+DAAgE,CAC/D,yVACD,EACA,yEAA0E,CACzE,2JACA,2JACA,iKACA,iKACA,iKACA,uKACA,4HACD,EACA,6DAA8D,CAC7D,sKACD,EACA,sCAAuC,CACtC,6SACA,uSACA,uSACA,6CACA,iHACA,0FACA,uFACA,oRACA,uCACA,wHACA,sMACA,8GACA,iGACD,EACA,4CAA6C,CAC5C,2JACA,2JACA,iKACA,iKACA,iKACA,uKACA,6HACA,iKACA,sKACD,EACA,oDAAqD,CACpD,mDACA,gqBACA,u4BACA,4oBACA,+fACA,sRACA,ulBACA,sXACA,+PACA,8TACA,mPACA,oZACA,0SACA,gTACA,6cACA,gYACA,+eACA,kjBACA,+WACA,saACA,0vBACA,2VACA,uUACA,uUACA,oiBACA,uUACA,uUACA,uwBACA,icACA,qZACA,ikBACA,+hBACA,khBACA,8NACA,wMACA,kRACA,wSACA,qOACA,2OACA,qlBACA,2qBACA,meACA,spBACD,EACA,wCAAyC,CACxC,6eACA,8UACA,2SACA,4kBACA,oSACA,8sBACA,wYACA,wYACA,yeACA,8nBACA,sdACA,8hBACA,iZACA,wTACA,sfACA,6YACA,8fACA,iiCACA,woBACA,yvBACA,opBACA,m1BACA,gtBACA,+uBACA,2hCACA,kMACA,mWACA,sjBACA,iRACA,4TACA,4YACA,ohBACA,wgBACA,iiBACA,kgBACA,wgBACA,gfACA,qwBACA,m0BACA,+vBACA,0rBACA,oyBACA,2NACA,qYACA,mdACA,gVACA,ymCACA,25BACA,+hCACA,qSACA,wbACA,0VACA,stCACA,gOACA,kKACA,waACA,wXACA,wKACA,o3BACA,4iBACA,8RACA;AAAA;AAAA;AAAA,ybACA,+MACA,0PACA,oaACA,8mBACA,wjBACA,kKACA,kTACA,kRACA,iSACA,+UACA,0kBACA,wWACA,oXACA,olBACA,idACA,qWACA,uUACA,6KACA,gOACA,8PACA,gTACA,o4DACA,6JACA,6MACA,ocACA,uNACA,6MACA,gdACA,6JACA,sqCACA,6WACA,2QACA,iRACA,4RACA,yXACA,uRACA,6RACA,wSACA,6MACA,yNACA,uYACA,+uBACA,4hBACA,izBACA,4XACA,8VACA,oSACA,qXACA,mcACA,qjBACA,whBACA,2YACA,0lCACA,qiBACA,wYACA,8MACA,qXACA,oNACA,gQACA,ggBACA,+XACA,ySACA,qVACA,uaACA,8WACA,yNACA,2aACA,sOACA,gaACA,yUACA,mUACA,4yBACA,udACA,scACA,stBACA,wUACA,2VACA,mZACA,wTACA,q+CACA,oXACA,yRACA,4YACA,gYACA,qlBACA,6gBACA,0eACA,wSACA,0SACA,6SACA,4SACA,ypCACA,uSACA,0SACA,uSACA,wSACA,2SACA,wSACA,0SACA,uSACA,0SACA,sSACA,uSACA,0SACA,sSACA,4SACA,ySACA,sSACA,uSACA,sSACA,2SACA,6SACA,wSACA,+SACA,ySACA,qXACA,6VACA,wTACA,ySACA,mQACA,0WACA,6fACA,oeACA,0PACA,qnBACA,muBACA,2XACA,2mBACA,qVACA,iwBACA,2aACA,inBACA,u6BACA,geACA,u5BACA,wlDACA,sHACA,qaACA,wXACA,+dACA,2nBACA,smBACA,uiBACA,goBACA,scACA,8XACA,qeACA,ilBACA,iRACA,+PACA,iiBACA,kvBACA,sgCACA,kxBACA,sZACA,iNACA,iNACA,mjBACA,6vBACA,8NACA,8LACA,qfACA,+gBACA,6YACA,8UACA,8PACA,2TACA,ykBACA,6ZACA,qZACA,2LACA,iMACA,yeACA,gOACA,8TACA,0IACA,o6BACA,wUACA,+PACA,6OACA,2YACA,qiBACA,4MACA,oSACA,+NACA,2dACA,wVACA,yRACA,otBACA,mSACA,6UACA,mUACA,kZACA,qjCACA,idACA,kKACA,68BACA,siBACA,gkBACA,yPACA,yPACA,41BACA,obACA,oLACA,qZACA,wbACA,0SACA,qOACA,mOACA,+kCACA,uOACA,wMACA,2aACA,qiBACA,kZACA,gVACA,gOACA,kPACA,qeACA,4kBACA,saACA,sYACA,+SACA,wWACA,yZACA,oKACA,qSACA,yiBACA,6TACA,yPACA,iMACA,qSACA,2XACA,onBACA,+ZACA,g4BACA,+UACA,0cACA,4RACA,ycACA,6gBACA,8ZACA,+NACA,+QACA,6UACA,6UACA,gWACA,6MACA,6bACA,sbACA,ydACA,ulBACA,8eACA,iMACA,w0BACA,+lBACA,icACA,4fACA,wIACA,yjBACA,kIACA,6UACA,gRACA,gWACA,yZACA,uRACA,qQACA,ovBACA,0jBACA,6pBACA,wMACA,kRACA,uLACA,wcACA,8OACA,uWACA,0pBACA,qeACD,EACA,4CAA6C,CAC5C,iQACA,iQACA,05BACA,uJACA,k/BACA,sQACA,8HACA,4iBACA,2IACA,6JACA,gOACA,uYACA,iRACA,mZACA,oOACA,oOACA,oOACA,oOACA,oOACA,oOACA,qRACA,qRACA,qRACA,qRACA,qRACA,qRACA,6MACA,8XACA,6MACA,gUACA,8HACA,yOACA,8HACA,gMACA,4NACA,wMACA,2YACA,4XACA,woCACA,yRACA,oUACA,uQACA,gwBACA,qPACA,qSACA,4VACA,wZACA,wZACA,wZACA,wZACA,wZACA,wZACA,4OACA,ieACA,ieACA,ieACA,ieACA,ieACA,ieACA,qdACA,qdACA,qdACA,qdACA,qdACA,qdACA,uTACA,oSACD,EACA,qCAAsC,CACrC,iSACA,wmBACA,mdACA,qQACA,uPACA,oTACA,0gBACA,oIACA,4LACA,oNACA,kSACA,gOACA,uOACA,gOACA,sMACA,2LACA,2OACA,wYACA,uKACA,iRACA,oPACA,sKACA,gOACA,0NACA,0NACA,+PACA,oNACA,iRACA,qQACA,kMACA,kPACA,kRACA,iRACA,+SACA,yXACA,kXACA,2bACD,EACA,6BAA8B,CAC7B,uCACA,gHACA,iCACA,yDACA,qEACA,2EACA,sHACA,6CACA,2BACA,2BACA,6CACA,yDACA,2BACA,iCACA,uCACA,oGACA,OACA,iCACA,uCACA,mDACA,iCACA,mDACA,uCACA,yDACA,4EACA,+DACA,WACD,EACA,2CAA4C,CAC3C,2EACD,EACA,mCAAoC,CACnC,0BACD,EACA,qCAAsC,CACrC,kKACA,uIACA,2OACA,kFACA,4JACA,kGACA,4KACA,sHACA,mUACA,qMACA,2JACD,EACA,+CAAgD,CAC/C,8FACA,iDACD,EACA,sDAAuD,CACtD,+IACA,0KACA,4MACA,iMACA,oSACA,+LACD,EACA,4DAA6D,CAC5D,wQACA,yFACA,kFACA,kFACA,0FACD,EACA,4DAA6D,CAC5D,gMACA,qMACD,EACA,sDAAuD,CACtD,iIACD,EACA,gDAAiD,CAChD,qDACA,mDACA,mDACA,mDACA,iEACA,qEACA,qEACA,qEACA,kFACA,kFACA,+DACA,+DACA,+DACA,qDACA,mDACA,mDACA,mDACA,+LACD,EACA,kDAAmD,CAClD,0TACD,EACA,0DAA2D,CAC1D,yKACA,6RACA,uRACA,+ZACA,gOACA,sYACA,mHACA,sMACA,iRACA,kKACA,uPACA,sMACA,wEACA,qRACA,oLACA,4PACA,sPACA,6IACA,8SACA,uMACA,wQACA,8NACA,sHACA,mQACA,4EACA,wPACA,gGACA,sKACD,EACA,+DAAgE,CAC/D,sYACD,EACA,4DAA6D,CAC5D,0GACA,qHACD,EACA,sDAAuD,CACtD,2IACA,mHACA,gDACA,kEACA,iGACA,2EACA,sIACA,4IACD,EACA,uDAAwD,CACvD,oYACA,mIACA,4HACD,EACA,wDAAyD,CACxD,yMACD,EACA,0DAA2D,CAC1D,0PACA,qJACD,EACA,qEAAsE,CACrE,kPACA,oPACA,uHACA,0QACD,EACA,4CAA6C,CAC5C,kQACA,+KACA,wIACA,8IACA,sPACA,wKACD,EACA,oDAAqD,CACpD,0DACA,0GACA,2GACA,6FACA,yDACA,yDACA,mDACA,yIACA,uCACA,qMACD,EACA,kDAAmD,CAClD,mIACA,kIACD,EACA,kEAAmE,CAClE,yEACA,isBACD,EACA,gEAAiE,CAChE,yNACA,wJACA,6RACA,+IACA,0JACD,EACA,6DAA8D,CAC7D,mDACA,6HACA,uDACA,uDACA,4EACA,4EACA,2JACA,0JACD,EACA,qEAAsE,CACrE,mNACA,kJACA,sRACD,EACA,+DAAgE,CAC/D,obACD,EACA,gDAAiD,CAChD,iCACA,mCACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,2GACA,+FACA,gEACA,gEACA,oHACA,6QACA,4UACA,4GACA,4GACA,+IACA,qJACA,mDACA,oDACD,EACA,4CAA6C,CAC5C,+XACA,0WACA,sXACA,mVACA,sWACA,gWACA,gWACA,6EACA,iCACA,iCACA,4HACA,sHACA,+FACA,6CACA,mDACA,mDACA,mDACA,sEACA,gHACA,weACA,uBACA,kIACA,mEACA,oEACA,gFACA,oEACA,w7BACD,EACA,4CAA6C,CAC5C,+DACA,4HACA,mDACA,wIACA,gHACA,8KACA,iHACA,6HACA,wMACA,oNACA,sEACA,kFACA,sMACA,0QACA,oQACA,wSACA,kNACA,+EACD,EACA,uDAAwD,CACvD,ysBACA,6WACA,6TACA,iTACA,4VACA,uWACD,EACA,8CAA+C,CAC9C,+IACA,+IACA,yLACD,EACA,0CAA2C,CAC1C,iPACA,8NACA,gSACA,4QACD,EACA,iDAAkD,CACjD,kIACA,8LACD,EACA,gDAAiD,CAChD,uVACA,2NACA,6VACA,iOACA,kdACA,4GACA,wdACA,iHACD,EACA,sDAAuD,CACtD,uCACA,uFACA,+DACA,mDACA,mBACA,yEACA,yEACA,8TACA,+WACA,8WACA,+ZACA,2aACA,4dACA,2QACD,EACA,oDAAqD,CACpD,iCACA,qEACA,uJACA,8HACA,uHACA,yHACA,+IACA,sHACA,+DACA,iJACA,uHACA,iHACA,mHACA,iJACA,uHACA,kIACA,oGACA,gLACA,uJACA,gJACA,kJACA,+IACA,+DACA,iJACA,wHACA,uHACA,yHACA,kIACA,wHACA,+FACA,+FACA,iGACA,uCACA,wFACA,uCACA,oIACA,2EACA,iHACA,sCACD,EACA,qEAAsE,CACrE,yNACD,EACA,iEAAkE,CACjE,keACA,sDACA,WACD,EACA,2DAA4D,CAC3D,4CACA,2CACA,sCACD,EACA,6DAA8D,CAC7D,2MACA,kIACA,sCACD,EACA,uDAAwD,CACvD,mHACA,uHACA,wHACA,uIACA,uHACA,2GACA,+HACA,mKACD,EACA,wDAAyD,CACxD,udACA,iJACA,2DACD,EACA,wCAAyC,CACxC,yPACA,0TACA,k7BACA,4ZACA,qJACA,+IACA,qJACA,2JACA,wMACA,kMACA,4JACA,qJACD,EACA,2DAA4D,CAC3D,sDACA,gqBACA,qlBACD,EACA,yDAA0D,CACzD,sHACA,kIACA,oKACA,kIACA,kHACD,EACA,0DAA2D,CAC1D,uKACA,gKACD,EACA,oDAAqD,CACpD,wKACA,6MACA,0LACA,kKACA,yIACA,qQACA,0LACA,oLACA,wNACA,gMACA,0LACA,mQACD,EACA,uDAAwD,CACvD,yKACA,6CACA,8CACA,gDACA,6CACA,weACA,6HACA,mGACD,EACA,uDAAwD,CACvD,sPACA,gPACA,2MACA,yQACA,kDACA,+QACA,kFACA,6KACA,6CACA,uKACA,iNACD,EACA,+DAAgE,CAC/D,qEACD,EACA,0EAA2E,CAC1E,wNACA,8PACA,siBACA,0TACD,EACA,2EAA4E,CAC3E,ySACA,+SACA,YACA,iCACA,gCACD,EACA,wDAAyD,CACxD,0JACD,EACA,4DAA6D,CAC5D,yIACA,4IACA,mIACA,4IACA,4HACA,8HACA,yIACA,kJACA,mIACA,kJACA,8KACA,4JACA,iKACA,4MACA,kFACA,8FACA,8FACA,iHACA,iHACA,+FACA,qGACA,qGACA,wNACA,8KACA,wKACA,8KACA,iPACA,gLACA,+IACD,EACA,wDAAyD,CACxD,qMACA,uUACD,EACA,wCAAyC,CACxC,sVACA,yTACA,oGACA,+FACA,0LACA,2LACA,6LACA,0LACA,8GACA,iFACD,EACA,sDAAuD,CACtD,8SACD,EACA,oDAAqD,CACpD,6FACA,yGACA,iHACA,mHACA,iHACA,mHACA,iMACA,6LACA,uHACA,uHACA,4RACA,mKACA,yVACA,yKACA,qWACA,kaACA,2OACA,yHACA,6HACA,oLACA,0LACA,0LACA,+LACD,EACA,0DAA2D,CAC1D,+LACD,EACA,gEAAiE,CAChE,ySACA,+SACA,wDACA,wUACD,EACA,8CAA+C,CAC9C,sQACA,6CACA,+PACA,2LACA,mPACA,2NACA,2VACA,6YACA,uYACA,wWACA,qaACA,6JACA,sNACA,2SACA,uWACA,yPACA,0QACD,EACA,4DAA6D,CAC5D,8TACA,gKACA,8FACA,mWACA,sTACD,EACA,8DAA+D,CAC9D,2cACA,kXACA,gMACA,6HACA,4EACA,4EACA,mDACA,yDACA,6CACA,mDACA,iFACA,qEACA,6CACA,+DACA,mDACA,+DACA,qEACA,0GACA,6CACA,wFACA,6CACA,yDACA,iFACA,kIACA,6CACA,uCACA,mDACA,uCACA,sHACA,mDACA,uCACA,iCACA,8DACD,EACA,yDAA0D,CACzD,gVACA,kUACD,EACA,0CAA2C,CAC1C,mIACA,obACA,0GACA,sGACA,gMACA,4RACA,4RACA,sHACA,qfACD,EACA,oDAAqD,CACpD,mVACA,2ZACA,wOACD,EACA,oDAAqD,CACpD,uKACA,wHACA,uKACA,yFACD,EACA,uDAAwD,CACvD,2QACA,sYACA,4YACA,sLACD,EACA,qDAAsD,CACrD,qEACA,qEACA,6CACA,iCACA,6CACA,6CACA,6CACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,uCACA,6CACA,2BACA,uCACA,qBACA,2BACA,2BACA,uCACA,mDACA,6CACA,uCACA,6CACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,oBACD,EACA,6DAA8D,CAC7D,mLACA,qLACA,gHACA,kHACA,4JACA,8JACA,uPACA,6PACA,sVACA,mGACD,EACA,4CAA6C,CAC5C,6OACA,yNACA,8PACA,0ZACA,uRACA,sWACA,sUACA,wYACD,EACA,sDAAuD,CACtD,4RACA,wIACA,mDACA,mDACA,mDACA,mDACA,mDACA,wFACA,wFACA,oOACD,EACA,kDAAmD,CAClD,uJACA,mKACA,2QACA,8WACA,mZACA,mUACA,4TACA,0eACA,2VACA,sDACA,yIACA,+DACA,eACA,UACA,WACA,kEACD,EACA,yDAA0D,CACzD,6CACA,qDACD,EACA,0DAA2D,CAC1D,wZACA,wDACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,grBACA,ksBACA,0qBACA,oqBACA,4rBACA,8sBACA,otBACA,mvBACA,grBACA,8pBACA,oqBACA,oqBACA,grBACA,ksBACA,oqBACA,2tBACA,0qBACA,0qBACA,uuBACA,0oBACA,oqBACA,grBACA,4rBACA,0qBACA,srBACA,0qBACA,qtBACA,0qBACA,4rBACA,0qBACA,2tBACA,0qBACA,isBACD,EACA,kEAAmE,CAClE,uNACA,ycACA,+TACD,EACA,sDAAuD,CACtD,mTACD,EACA,kEAAmE,CAClE,+UACA,6UACA,uUACA,+SACA,iZACA,2YACA,uFACA,4MACA,sHACA,gMACA,iPACA,kKACA,mNACA,+LACA,gPACA,uKACA,wNACA,qLACA,sPACA,uKACA,+NACA,iIACA,6TACA,uNACD,EACA,0EAA2E,CAC1E,sMACA,mQACA;AAAA;AAAA,ghBACA,mPACA,8DACD,EACA,iEAAkE,CACjE,80BACA,g4BACA,+sBACA,kaACA,2YACA,sOACA,yxBACA,i1BACA,4zBACD,EACA,4DAA6D,CAC5D,yNACA,+NACA,oOACD,EACA,0DAA2D,CAC1D,2EACD,EACA,mDAAoD,CACnD,iFACA,6CACA,2BACA,2BACA,yDACA,oEACD,EACA,8CAA+C,CAC9C,6QACA,6FACA,oHACA,wIACD,EACA,gDAAiD,CAChD,oQACA,8KACA,2JACA,qJACA,qJACA,2PACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,uCACA,2EACD,EACA,yCAA0C,CACzC,4CACD,EACA,gDAAiD,CAChD,mFACA,mIACA,gJACA,8HACA,oLACA,uKACA,uMACA,+NACA,8FACA,gEACA,6HACA,8FACA,oLACA,sJACA,2JACA,+IACA,yIACA,8HACA,qGACA,gHACD,EACA,yDAA0D,CACzD,yQACA,8QACA,kUACA,yUACA,kUACA,yUACA,iPACA,kyBACA,gWACA,2aACD,EACA,mDAAoD,CACnD,0QACD,EACA,2CAA4C,CAC3C,oLACA,qnBACA,6CACA,yJACA,4HACA,uQACA,2JACA,8OACA;AAAA,yDACD,EACA,4CAA6C,CAC5C,kNACA,kNACA,oNACA,oLACA,kZACA,gNACA,qOACA,8EACA,gOACD,EACA,wCAAyC,CACxC,mFACA,0GACA,+GACA,mMACA,mpBACD,EACA,yDAA0D,CACzD,0JACD,EACA,0DAA2D,CAC1D,sVACA,0WACA,+NACA,8NACD,EACA,uCAAwC,CACvC,4EACA,qOACA,mOACA,+0DACA,qlCACA,+3BACA,mUACA,qNACA,0UACA,4XACA,qZACA,wVACA,4pBACA,yKACA,qVACA,8yBACA,0/BACA,4pBACA,upBACA,sNACA,8PACA,sRACA,8ZACA,g/BACA,q0BACD,EACA,qCAAsC,CACrC,uCACA,uFACA,8DACD,EACA,qDAAsD,CACrD,kIACA,0GACA,kFACA,WACA,yIACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,iCACA,mYACA,YACA,sQACA,6HACA,oEACA,iDACA,eACA,uCACA,uCACA,gCACD,EACA,gDAAiD,CAChD,qEACD,EACA,iDAAkD,CACjD,0LACD,EACA,yCAA0C,CACzC,kfACA,osBACA,ynBACA,kVACA,qPACA,snBACA,mjBACA,omBACA,iuBACA,6JACA,sNACA,maACA,iMACA,iNACA,6NACA,sNACA,uUACA,yVACA,8EACA,qIACA,sGACA,wRACA,4QACA,sQACA,yUACA,oPACA,qUACA,2ZACA,qUACA,yWACA,+bACA,yWACA,yTACA,+YACA,yTACA,sJACA,qLACA,6MACA,8KACA,uJACA,oIACA,0FACA,oKACA,4GACA,8MACA,iJACA,+RACA,qlBACA,imBACA,wdACA,qNACA,uTACA,gSACA,ocACA,sqBACA,+RACA,8TACA,stBACA,+UACA,8WACA,mxBACA,sYACA,qaACA,iTACA,0UACA,iJACA,4GACA,6OACA,gQACA,qXACA,gSACA,wXACA,y3BACA,qnCACA,stBACA,mxBACA,6wBACA,wRACA,8RACA,itBACA,8wBACA,muBACA,wwBACA,0IACA,wRACA,oqBACA,i1BACA,qTACA,+KACA,4oBACA,oqBACA,0NACA,oNACA,kuBACA,sQACA,yUACA,4QACA,o2BACA,oPACA,iTACA,gQACA,yRACA,kHACA,+NACA,kKACA,6RACA,gOACA,+SACA,kPACA,gSACA,8bACA,4oBACA,goBACA,goBACA,onBACA,iNACA,qMACA,yXACA,6WACA,4LACA,gLACA,qQACA,wuBACA,0SACA,uWACA,oSACA,2sBACA,svBACA,8sBACA,uvBACA,mpBACA,8rBACA,26BACA,+pBACA,ypBACA,u7BACA,wtBACA,2tBACA,sRACA,iUACA,ySACA,qYACA,yfACA,8nBACA,iTACA,iWACA,kVACA,oWACA,ifACA,kVACA,sOACA,wPACA,6OACA,0SACA,wMACA,mMACA,iaACA,gLACA,qWACA,6OACA,kMACA,8XACA,0LACA,4cACA,ifACA,+YACA,0FACA,sLACA,yHACA,6QACA,0MACA,4NACA,wJACA,2VACA,6WACA,6WACA,4QACA,8RACA,mUACA,iWACA,0PACA,qLACA,0PACA,uMACA,2QACA,qyBACA,wxBACA,0yBACA,8xBACA,+xBACA,2yBACA,gYACA,yZACA,+ZACA,ifACA,iwBACA,wxBACA,oPACA,+UACA,8OACA,sLACA,sOACA,6GACA,+mBACA,8JACA,qUACA,yOACA,gXACA,4aACA,saACA,4LACA,0NACA,wRACA,4QACA,kRACA,oSACA,wRACA,uRACD,EACA,wCAAyC,CACxC,qhBACA,0PACA,gQACA,6TACA,sTACD,EACA,8CAA+C,CAC9C,sQACA,kSACA,uMACA,uMACA,gaACA,gkBACA,qjBACA,6LACA,sJACA,uGACA,oeACA,4GACA,iBACA,qBACA,8PACA,8PACA,saACA,mlBACA,2jBACA,yeACD,EACA,yCAA0C,CACzC,gHACD,CACD,CAAC", "names": [], "file": "editor.main.nls.ru.js"}