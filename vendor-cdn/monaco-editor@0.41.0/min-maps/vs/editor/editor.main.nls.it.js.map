{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.it.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.41.0(38e1e3d097f84e336c311d071a9ffb5191d4ffd1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.it\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"input\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"Maiuscole/minuscole\",\n\t\t\"Parola intera\",\n\t\t\"Usa espressione regolare\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"input\",\n\t\t\"Mantieni maiuscole/minuscole\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"Caricamento...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"Errore: {0}\",\n\t\t\"Avviso: {0}\",\n\t\t\"Info: {0}\",\n\t\t\"per la cronologia\",\n\t\t\"Input cancellato\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"Non associato\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Casella di selezione\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"Altre azioni...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"Filtro\",\n\t\t\"Corrispondenza fuzzy\",\n\t\t\"Digitare per filtrare\",\n\t\t\"Digitare per la ricerca\",\n\t\t\"Digitare per la ricerca\",\n\t\t\"Chiudi\",\n\t\t\"Non sono stati trovati elementi.\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(vuoto)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"Si è verificato un errore di sistema ({0})\",\n\t\t\"Si è verificato un errore sconosciuto. Per altri dettagli, vedere il log.\",\n\t\t\"Si è verificato un errore sconosciuto. Per altri dettagli, vedere il log.\",\n\t\t\"{0} ({1} errori in totale)\",\n\t\t\"Si è verificato un errore sconosciuto. Per altri dettagli, vedere il log.\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"CTRL\",\n\t\t\"MAIUSC\",\n\t\t\"ALT\",\n\t\t\"Windows\",\n\t\t\"CTRL\",\n\t\t\"MAIUSC\",\n\t\t\"ALT\",\n\t\t\"Super\",\n\t\t\"CTRL\",\n\t\t\"MAIUSC\",\n\t\t\"Opzione\",\n\t\t\"Comando\",\n\t\t\"CTRL\",\n\t\t\"MAIUSC\",\n\t\t\"ALT\",\n\t\t\"Windows\",\n\t\t\"CTRL\",\n\t\t\"MAIUSC\",\n\t\t\"ALT\",\n\t\t\"Super\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"editor\",\n\t\t\"L\\'editor non è accessibile in questo momento.\",\n\t\t\"{0} Per abilitare la modalità ottimizzata per l\\'utilità per la lettura dello schermo usare {1}\",\n\t\t\"{0} Per abilitare la modalità ottimizzata per l\\'utilità per la lettura dello schermo, aprire la selezione rapida con {1} ed eseguire il comando Attiva/Disattiva modalità di accessibilità dell\\'utilità per la lettura dello schermo, attualmente non attivabile tramite tastiera.\",\n\t\t\"{0} Assegnare un tasto di scelta rapida per il comando Attiva/Disattiva modalità di accessibilità dell\\'utilità per la lettura dello schermo accedendo all\\'editor dei tasti di scelta rapida con {1} ed eseguirlo.\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"Si attiene alla fine anche quando si passa a righe più lunghe\",\n\t\t\"Si attiene alla fine anche quando si passa a righe più lunghe\",\n\t\t\"Cursori secondari rimossi\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"&&Annulla\",\n\t\t\"Annulla azione\",\n\t\t\"&&Ripeti\",\n\t\t\"Ripeti\",\n\t\t\"&&Seleziona tutto\",\n\t\t\"Seleziona tutto\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"Il numero di cursori è stato limitato a {0}. Provare a usare [Trova e sostituisci](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) per modifiche di dimensioni maggiori o aumentare l\\'impostazione del limite di più cursori dell\\'editor.\",\n\t\t\"Aumentare limite multi-cursore\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor.contribution\": [\n\t\t\"Visualizzatore differenze accessibile\",\n\t\t\"Vai alla differenza successiva\",\n\t\t\"Apri Visualizzatore differenze accessibile\",\n\t\t\"Vai alla differenza precedente\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget\": [\n\t\t\"Effetto di riga per gli inserimenti nell\\'editor diff.\",\n\t\t\"Effetto di riga per le rimozioni nell\\'editor diff.\",\n\t\t\" usa MAIUSC +F7 per esplorare le modifiche\",\n\t\t\"Non è possibile confrontare i file perché uno è troppo grande.\",\n\t\t\"Fare clic per annullare la modifica\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/accessibleDiffViewer\": [\n\t\t\"Icona per \\\"Inserisci\\\" nel visualizzatore differenze accessibile.\",\n\t\t\"Icona per \\\"Rimuovi\\\" nel visualizzatore differenze accessibile.\",\n\t\t\"Icona per \\\"Chiudi\\\" nel visualizzatore differenze accessibile.\",\n\t\t\"Chiudi\",\n\t\t\"Visualizzatore differenze accessibile. Usare le frecce SU e GIÙ per spostarsi.\",\n\t\t\"nessuna riga modificata\",\n\t\t\"1 riga modificata\",\n\t\t\"{0} righe modificate\",\n\t\t\"Differenza {0} di {1}: riga originale {2}, {3}, riga modificata {4}, {5}\",\n\t\t\"vuota\",\n\t\t\"{0} riga non modificata {1}\",\n\t\t\"{0} riga originale {1} riga modificata {2}\",\n\t\t\"+ {0} riga modificata {1}\",\n\t\t\"- {0} riga originale {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/colors\": [\n\t\t\"Colore del bordo per il testo spostato nell\\'editor diff.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/decorations\": [\n\t\t\"Effetto di riga per gli inserimenti nell\\'editor diff.\",\n\t\t\"Effetto di riga per le rimozioni nell\\'editor diff.\",\n\t\t\"Fare clic per annullare la modifica\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/diffEditorEditors\": [\n\t\t\" utilizzare {0} per aprire la Guida all\\'accessibilità.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/inlineDiffDeletedCodeMargin\": [\n\t\t\"Copia le righe eliminate\",\n\t\t\"Copia la riga eliminata\",\n\t\t\"Copia righe modificate\",\n\t\t\"Copia riga modificata\",\n\t\t\"Copia la riga eliminata ({0})\",\n\t\t\"Copia riga modificata ({0})\",\n\t\t\"Ripristina questa modifica\",\n\t],\n\t\"vs/editor/browser/widget/diffEditorWidget2/unchangedRanges\": [\n\t\t\"Ridurre area non modificata\",\n\t],\n\t\"vs/editor/browser/widget/diffReview\": [\n\t\t\"Icona per \\'Inserisci\\' nella revisione diff.\",\n\t\t\"Icona per \\'Rimuovi\\' nella revisione diff.\",\n\t\t\"Icona per \\'Chiudi\\' nella revisione diff.\",\n\t\t\"Chiudi\",\n\t\t\"nessuna riga modificata\",\n\t\t\"1 riga modificata\",\n\t\t\"{0} righe modificate\",\n\t\t\"Differenza {0} di {1}: riga originale {2}, {3}, riga modificata {4}, {5}\",\n\t\t\"vuota\",\n\t\t\"{0} riga non modificata {1}\",\n\t\t\"{0} riga originale {1} riga modificata {2}\",\n\t\t\"+ {0} riga modificata {1}\",\n\t\t\"- {0} riga originale {1}\",\n\t],\n\t\"vs/editor/browser/widget/inlineDiffMargin\": [\n\t\t\"Copia le righe eliminate\",\n\t\t\"Copia la riga eliminata\",\n\t\t\"Copia righe modificate\",\n\t\t\"Copia riga modificata\",\n\t\t\"Copia la riga eliminata ({0})\",\n\t\t\"Copia riga modificata ({0})\",\n\t\t\"Ripristina questa modifica\",\n\t\t\"Copia la riga eliminata ({0})\",\n\t\t\"Copia riga modificata ({0})\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"Editor\",\n\t\t\"Numero di spazi a cui è uguale una scheda. Questa impostazione viene sottoposta a override in base al contenuto del file quando {0} è attivo.\",\n\t\t\"Numero di spazi utilizzati per il rientro o `\\\"tabSize\\\"` per usare il valore di `#editor.tabSize#`. Questa impostazione viene sostituita in base al contenuto del file quando `#editor.detectIndentation#` è attivo.\",\n\t\t\"Inserire spazi quando si preme \\'TAB\\'. Questa impostazione viene sottoposta a override in base al contenuto del file quando {0} è attivo.\",\n\t\t\"Controlla se {0} e {1} verranno rilevati automaticamente quando un file viene aperto in base al contenuto del file.\",\n\t\t\"Rimuovi gli spazi finali inseriti automaticamente.\",\n\t\t\"Gestione speciale dei file di grandi dimensioni per disabilitare alcune funzionalità che fanno un uso intensivo della memoria.\",\n\t\t\"Controlla se calcolare i completamenti in base alle parole presenti nel documento.\",\n\t\t\"Suggerisci parole solo dal documento attivo.\",\n\t\t\"Suggerisci parole da tutti i documenti aperti della stessa lingua.\",\n\t\t\"Suggerisci parole da tutti i documenti aperti.\",\n\t\t\"Controlla i documenti da cui vengono calcolati i completamenti basati su parole.\",\n\t\t\"L\\'evidenziazione semantica è abilitata per tutti i temi colore.\",\n\t\t\"L\\'evidenziazione semantica è disabilitata per tutti i temi colore.\",\n\t\t\"La configurazione dell\\'evidenziazione semantica è gestita tramite l\\'impostazione `semanticHighlighting` del tema colori corrente.\",\n\t\t\"Controlla se l\\'evidenziazione semanticHighlighting è visualizzata per i linguaggi che la supportano.\",\n\t\t\"Consente di mantenere aperti gli editor rapidi anche quando si fa doppio clic sul contenuto o si preme \\'ESC\\'.\",\n\t\t\"Per motivi di prestazioni le righe di lunghezza superiore non verranno tokenizzate\",\n\t\t\"Controlla se la tokenizzazione deve essere eseguita in modo asincrono in un web worker.\",\n\t\t\"Controlla se deve essere registrata la tokenizzazione asincrona. Solo per il debug.\",\n\t\t\"Controlla se la tokenizzazione asincrona deve essere verificata rispetto alla tokenizzazione legacy in background. Potrebbe rallentare la tokenizzazione. Solo per il debug.\",\n\t\t\"Definisce i simboli di parentesi quadra che aumentano o riducono il rientro.\",\n\t\t\"Sequenza di stringa o carattere parentesi quadra di apertura.\",\n\t\t\"Sequenza di stringa o carattere parentesi quadra di chiusura.\",\n\t\t\"Definisce le coppie di bracket colorate in base al livello di annidamento se è abilitata la colorazione delle coppie di bracket.\",\n\t\t\"Sequenza di stringa o carattere parentesi quadra di apertura.\",\n\t\t\"Sequenza di stringa o carattere parentesi quadra di chiusura.\",\n\t\t\"Timeout in millisecondi dopo il quale il calcolo delle differenze viene annullato. Usare 0 per indicare nessun timeout.\",\n\t\t\"Dimensioni massime del file in MB per cui calcolare le differenze. Usare 0 per nessun limite.\",\n\t\t\"Controlla se l\\'editor diff mostra le differenze affiancate o incorporate.\",\n\t\t\"Se questa opzione è abilitata, l\\'editor diff mostra le frecce nel margine del glifo per ripristinare le modifiche.\",\n\t\t\"Se abilitato, l\\'editor differenze ignora le modifiche relative a spazi vuoti iniziali e finali.\",\n\t\t\"Controlla se l\\'editor diff mostra gli indicatori +/- per le modifiche aggiunte/rimosse.\",\n\t\t\"Controlla se l\\'editor visualizza CodeLens.\",\n\t\t\"Il ritorno a capo automatico delle righe non viene mai applicato.\",\n\t\t\"Il ritorno a capo automatico delle righe viene applicato in corrispondenza della larghezza del viewport.\",\n\t\t\"Le righe andranno a capo in base all\\'impostazione {0}.\",\n\t\t\"Usare l\\'algoritmo diffing legacy.\",\n\t\t\"Usare l\\'algoritmo diffing avanzato.\",\n\t\t\"Controlla se l\\'editor diff mostra aree non modificate. Funziona solo quando è impostato {0}.\",\n\t\t\"Controlla se l\\'editor diff debba mostrare gli spostamenti di codice rilevati. Funziona solo quando è impostato {0}.\",\n\t\t\"Controlla se l\\'editor diff usa la nuova o la precedente implementazione.\",\n\t\t\"Controlla se l\\'editor diff mostra decorazioni vuote per vedere dove sono stati inseriti o eliminati caratteri.\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"Usare le API della piattaforma per rilevare quando viene collegata un\\'utilità per la lettura dello schermo\",\n\t\t\"Ottimizzare l\\'utilizzo con un\\'utilità per la lettura dello schermo\",\n\t\t\"Si presuppone che un\\'utilità per la lettura dello schermo non sia collegata\",\n\t\t\"Controllare se l\\'interfaccia utente deve essere eseguito in una modalità ottimizzata per le utilità per la lettura dello schermo.\",\n\t\t\"Consente di controllare se viene inserito uno spazio quando si aggiungono commenti.\",\n\t\t\"Controlla se ignorare le righe vuote con le opzioni per attivare/disattivare, aggiungere o rimuovere relative ai commenti di riga.\",\n\t\t\"Controlla se, quando si copia senza aver effettuato una selezione, viene copiata la riga corrente.\",\n\t\t\"Controlla se il cursore deve passare direttamente alla ricerca delle corrispondenze durante la digitazione.\",\n\t\t\"Non fornire mai la stringa di ricerca dalla selezione dell\\'editor.\",\n\t\t\"Fornisci sempre la stringa di ricerca dalla selezione dell\\'editor, inclusa la parola alla posizione del cursore.\",\n\t\t\"Fornisci la stringa di ricerca solo dalla selezione dell\\'editor.\",\n\t\t\"Controlla se inizializzare la stringa di ricerca nel Widget Trova con il testo selezionato nell\\'editor.\",\n\t\t\"Non attivare mai automaticamente la funzione Trova nella selezione (impostazione predefinita).\",\n\t\t\"Attiva sempre automaticamente la funzione Trova nella selezione.\",\n\t\t\"Attiva automaticamente la funzione Trova nella selezione quando sono selezionate più righe di contenuto.\",\n\t\t\"Controlla la condizione per attivare automaticamente la funzione Trova nella selezione.\",\n\t\t\"Controlla se il widget Trova deve leggere o modificare gli appunti di ricerca condivisi in macOS.\",\n\t\t\"Controlla se il widget Trova deve aggiungere altre righe nella parte superiore dell\\'editor. Quando è true, è possibile scorrere oltre la prima riga quando il widget Trova è visibile.\",\n\t\t\"Controlla se la ricerca viene riavviata automaticamente dall\\'inizio o dalla fine quando non è possibile trovare ulteriori corrispondenze.\",\n\t\t\"Abilita/Disabilita i caratteri legatura (funzionalità dei tipi di carattere \\'calt\\' e \\'liga\\'). Impostare su una stringa per un controllo più specifico sulla proprietà CSS \\'font-feature-settings\\'.\",\n\t\t\"Proprietà CSS \\'font-feature-settings\\' esplicita. Se è necessario solo attivare/disattivare le legature, è possibile passare un valore booleano.\",\n\t\t\"Consente di configurare i caratteri legatura o le funzionalità dei tipi di carattere. Può essere un valore booleano per abilitare/disabilitare le legature o una stringa per il valore della proprietà CSS \\'font-feature-settings\\'.\",\n\t\t\"Abilita/disabilita la conversione dada font-weight a font-variation-settings. Modificare questa impostazione in una stringa per il controllo con granularità fine della proprietà CSS Font-variation.\",\n\t\t\"Proprietà CSS esplicita \\'font-variation-settings\\'. È invece possibile passare un valore booleano se è sufficiente convertire font-weight in font-variation-settings.\",\n\t\t\"Configura le varianti di carattere. Può essere un valore booleano per abilitare/disabilitare la conversione da font-weight a font-variation-settings o una stringa per il valore della proprietà \\'font-variation-settings\\' CSS.\",\n\t\t\"Controlla le dimensioni del carattere in pixel.\",\n\t\t\"Sono consentiti solo le parole chiave \\\"normal\\\" e \\\"bold\\\" o i numeri compresi tra 1 e 1000.\",\n\t\t\"Controlla lo spessore del carattere. Accetta le parole chiave \\\"normal\\\" e \\\"bold\\\" o i numeri compresi tra 1 e 1000.\",\n\t\t\"Mostra la visualizzazione in anteprima dei risultati (impostazione predefinita)\",\n\t\t\"Passa al risultato principale e mostra una visualizzazione in anteprima\",\n\t\t\"Passa al risultato principale e abilita l\\'esplorazione senza anteprima per gli altri\",\n\t\t\"Questa impostazione è deprecata. In alternativa, usare impostazioni diverse, come \\'editor.editor.gotoLocation.multipleDefinitions\\' o \\'editor.editor.gotoLocation.multipleImplementations\\'.\",\n\t\t\"Controlla il comportamento del comando \\'Vai alla definizione\\' quando esistono più posizioni di destinazione.\",\n\t\t\"Controlla il comportamento del comando \\'Vai alla definizione di tipo\\' quando esistono più posizioni di destinazione.\",\n\t\t\"Controlla il comportamento del comando \\'Vai a dichiarazione\\' quando esistono più posizioni di destinazione.\",\n\t\t\"Controlla il comportamento del comando \\'Vai a implementazioni\\' quando esistono più posizioni di destinazione.\",\n\t\t\"Controlla il comportamento del comando \\'Vai a riferimenti\\' quando esistono più posizioni di destinazione.\",\n\t\t\"ID comando alternativo eseguito quando il risultato di \\'Vai alla definizione\\' è la posizione corrente.\",\n\t\t\"ID comando alternativo eseguito quando il risultato di \\'Vai alla definizione di tipo\\' è la posizione corrente.\",\n\t\t\"ID comando alternativo eseguito quando il risultato di \\'Vai a dichiarazione\\' è la posizione corrente.\",\n\t\t\"ID comando alternativo eseguito quando il risultato di \\'Vai a implementazione\\' è la posizione corrente.\",\n\t\t\"ID comando alternativo eseguito quando il risultato di \\'Vai a riferimento\\' è la posizione corrente.\",\n\t\t\"Controlla se mostrare l\\'area sensibile al passaggio del mouse.\",\n\t\t\"Controlla il ritardo in millisecondi dopo il quale viene mostrato il passaggio del mouse.\",\n\t\t\"Controlla se l\\'area sensibile al passaggio del mouse deve rimanere visibile quando vi si passa sopra con il puntatore del mouse.\",\n\t\t\"Preferisci la visualizzazione al passaggio del mouse sopra la riga, se c\\'è spazio.\",\n\t\t\"Presuppone che la larghezza sia identica per tutti caratteri. Si tratta di un algoritmo veloce che funziona correttamente per i tipi di carattere a spaziatura fissa e determinati script (come i caratteri latini) in cui i glifi hanno larghezza identica.\",\n\t\t\"Delega il calcolo dei punti di ritorno a capo al browser. Si tratta di un algoritmo lento che potrebbe causare blocchi con file di grandi dimensioni, ma funziona correttamente in tutti gli altri casi.\",\n\t\t\"Controlla l\\'algoritmo che calcola i punti di wrapping. Si noti che quando è attiva la modalità di accessibilità, la modalità avanzata verrà usata per un\\'esperienza ottimale.\",\n\t\t\"Abilita la lampadina delle azioni codice nell\\'editor.\",\n\t\t\"Mostra gli ambiti correnti annidati durante lo scorrimento nella parte superiore dell\\'editor.\",\n\t\t\"Definisce il numero massimo di righe permanenti da mostrare.\",\n\t\t\"Definisce il modello da utilizzare per determinare quali linee applicare. Se il modello di struttura non esiste, verrà eseguito il fallback sul modello del provider di riduzione che rientra nel modello di rientro. Questo ordine viene rispettato in tutti e tre i casi.\",\n\t\t\"Abilita i suggerimenti incorporati nell\\'Editor.\",\n\t\t\"Gli hint di inlay sono abilitati\",\n\t\t\"Gli hint di inlay vengono visualizzati per impostazione predefinita e vengono nascosti quando si tiene premuto {0}\",\n\t\t\"Gli hint di inlay sono nascosti per impostazione predefinita e vengono visualizzati solo quando si tiene premuto {0}\",\n\t\t\"Gli hint di inlay sono disabilitati\",\n\t\t\"Controlla le dimensioni del carattere dei suggerimenti di inlay nell\\'editor. Per impostazione predefinita, {0} viene usato quando il valore configurato è minore di {1} o maggiore delle dimensioni del carattere dell\\'editor.\",\n\t\t\"Controlla la famiglia di caratteri dei suggerimenti inlay nell\\'editor. Se impostato su vuoto, viene usato {0}.\",\n\t\t\"Abilita il riempimento attorno ai suggerimenti incorporati nell\\'editor.\",\n\t\t\"Controlla l\\'altezza della riga. \\r\\n - Usare 0 per calcolare automaticamente l\\'altezza della riga dalle dimensioni del carattere.\\r\\n - I valori compresi tra 0 e 8 verranno usati come moltiplicatore con le dimensioni del carattere.\\r\\n - I valori maggiori o uguali a 8 verranno usati come valori effettivi.\",\n\t\t\"Controlla se la minimappa è visualizzata.\",\n\t\t\"Controlla se la minimappa viene nascosta automaticamente.\",\n\t\t\"La minimappa ha le stesse dimensioni del contenuto dell\\'editor (e potrebbe supportare lo scorrimento).\",\n\t\t\"Se necessario, la minimappa si ridurrà o si ingrandirà in modo da adattarsi all\\'altezza dell\\'editor (nessuno scorrimento).\",\n\t\t\"Se necessario, la minimappa si ridurrà in modo che la larghezza non superi mai quella dell\\'editor (nessuno scorrimento).\",\n\t\t\"Controlla le dimensioni della minimappa.\",\n\t\t\"Definisce il lato in cui eseguire il rendering della minimappa.\",\n\t\t\"Controlla se il dispositivo di scorrimento della minimappa è visualizzato.\",\n\t\t\"Scala del contenuto disegnato nella minimappa: 1, 2 o 3.\",\n\t\t\"Esegue il rendering dei caratteri effettivi di una riga in contrapposizione ai blocchi colore.\",\n\t\t\"Limita la larghezza della minimappa in modo da eseguire il rendering al massimo di un certo numero di colonne.\",\n\t\t\"Controlla la quantità di spazio tra il bordo superiore dell\\'editor e la prima riga.\",\n\t\t\"Controlla la quantità di spazio tra il bordo inferiore dell\\'editor e l\\'ultima riga.\",\n\t\t\"Abilita un popup che mostra documentazione sui parametri e informazioni sui tipi mentre si digita.\",\n\t\t\"Controlla se il menu dei suggerimenti per i parametri esegue un ciclo o si chiude quando viene raggiunta la fine dell\\'elenco.\",\n\t\t\"I suggerimenti rapidi vengono visualizzati all\\'interno del widget dei suggerimenti\",\n\t\t\"I suggerimenti rapidi vengono visualizzati come testo fantasma\",\n\t\t\"I suggerimenti rapidi sono disabilitati\",\n\t\t\"Abilita i suggerimenti rapidi all\\'interno di stringhe.\",\n\t\t\"Abilita i suggerimenti rapidi all\\'interno di commenti.\",\n\t\t\"Abilita i suggerimenti rapidi all\\'esterno di stringhe e commenti.\",\n\t\t\"Controlla se i suggerimenti devono essere visualizzati automaticamente durante la digitazione. Può essere controllato per la digitazione in commenti, stringhe e altro codice. Il suggerimento rapido può essere configurato per essere visualizzato come testo fantasma o con il widget dei suggerimenti. Tenere anche conto dell\\'impostazione \\'{0}\\' che controlla se i suggerimenti vengono attivati dai caratteri speciali.\",\n\t\t\"I numeri di riga non vengono visualizzati.\",\n\t\t\"I numeri di riga vengono visualizzati come numeri assoluti.\",\n\t\t\"I numeri di riga vengono visualizzati come distanza in linee alla posizione del cursore.\",\n\t\t\"I numeri di riga vengono visualizzati ogni 10 righe.\",\n\t\t\"Controlla la visualizzazione dei numeri di riga.\",\n\t\t\"Numero di caratteri a spaziatura fissa in corrispondenza del quale verrà eseguito il rendering di questo righello dell\\'editor.\",\n\t\t\"Colore di questo righello dell\\'editor.\",\n\t\t\"Esegue il rendering dei righelli verticali dopo un certo numero di caratteri a spaziatura fissa. Usare più valori per più righelli. Se la matrice è vuota, non viene disegnato alcun righello.\",\n\t\t\"La barra di scorrimento verticale sarà visibile solo quando necessario.\",\n\t\t\"La barra di scorrimento verticale sarà sempre visibile.\",\n\t\t\"La barra di scorrimento verticale sarà sempre nascosta.\",\n\t\t\"Controlla la visibilità della barra di scorrimento verticale.\",\n\t\t\"La barra di scorrimento orizzontale sarà visibile solo quando necessario.\",\n\t\t\"La barra di scorrimento orizzontale sarà sempre visibile.\",\n\t\t\"La barra di scorrimento orizzontale sarà sempre nascosta.\",\n\t\t\"Controlla la visibilità della barra di scorrimento orizzontale.\",\n\t\t\"Larghezza della barra di scorrimento verticale.\",\n\t\t\"Altezza della barra di scorrimento orizzontale.\",\n\t\t\"Controlla se i clic consentono di attivare lo scorrimento per pagina o di passare direttamente alla posizione di clic.\",\n\t\t\"Controlla se tutti i caratteri ASCII non di base sono evidenziati. Solo i caratteri compresi tra U+0020 e U+007E, tabulazione, avanzamento riga e ritorno a capo sono considerati ASCII di base.\",\n\t\t\"Controlla se i caratteri che riservano spazio o non hanno larghezza sono evidenziati.\",\n\t\t\"Controlla se i caratteri che possono essere confusi con i caratteri ASCII di base sono evidenziati, ad eccezione di quelli comuni nelle impostazioni locali dell\\'utente corrente.\",\n\t\t\"Controlla se anche i caratteri nei commenti devono essere soggetti a evidenziazione Unicode.\",\n\t\t\"Controlla se anche i caratteri nelle stringhe devono essere soggetti all\\'evidenziazione Unicode.\",\n\t\t\"Definisce i caratteri consentiti che non vengono evidenziati.\",\n\t\t\"I caratteri Unicode comuni nelle impostazioni locali consentite non vengono evidenziati.\",\n\t\t\"Controlla se visualizzare automaticamente i suggerimenti inline nell\\'Editor.\",\n\t\t\"Mostra la barra degli strumenti dei suggerimenti in linea ogni volta che viene visualizzato un suggerimento in linea.\",\n\t\t\"Mostra la barra degli strumenti dei suggerimenti in linea quando al passaggio del mouse su un suggerimento in linea.\",\n\t\t\"Controlla quando mostrare la barra dei suggerimenti in linea.\",\n\t\t\"Controlla la modalità di interazione dei suggerimenti inline con il widget dei suggerimenti. Se questa opzione è abilitata, il widget dei suggerimenti non viene visualizzato automaticamente quando sono disponibili suggerimenti inline.\",\n\t\t\"Controlla se la colorazione delle coppie di parentesi è abilitata. Usare {0} per eseguire l\\'override dei colori di evidenziazione delle parentesi.\",\n\t\t\"Controlla se ogni tipo di parentesi ha un pool di colori indipendente.\",\n\t\t\"Abilita le guide per coppie di parentesi quadre.\",\n\t\t\"Abilita le guide delle coppie di parentesi solo per la coppia di parentesi attive.\",\n\t\t\"Disabilita le guide per coppie di parentesi quadre.\",\n\t\t\"Controlla se le guide delle coppie di parentesi sono abilitate o meno.\",\n\t\t\"Abilita le guide orizzontali come aggiunta alle guide per coppie di parentesi verticali.\",\n\t\t\"Abilita le guide orizzontali solo per la coppia di parentesi attive.\",\n\t\t\"Disabilita le guide per coppie di parentesi orizzontali.\",\n\t\t\"Controlla se le guide orizzontali delle coppie di parentesi sono abilitate o meno.\",\n\t\t\"Controlla se l\\'editor debba evidenziare la coppia di parentesi attive.\",\n\t\t\"Controlla se l\\'editor deve eseguire il rendering delle guide con rientro.\",\n\t\t\"Evidenzia la guida di rientro attiva.\",\n\t\t\"Evidenzia la guida di rientro attiva anche se le guide delle parentesi quadre sono evidenziate.\",\n\t\t\"Non evidenziare la guida di rientro attiva.\",\n\t\t\"Controlla se l\\'editor deve evidenziare la guida con rientro attiva.\",\n\t\t\"Inserisce il suggerimento senza sovrascrivere il testo a destra del cursore.\",\n\t\t\"Inserisce il suggerimento e sovrascrive il testo a destra del cursore.\",\n\t\t\"Controlla se le parole vengono sovrascritte quando si accettano i completamenti. Tenere presente che questa opzione dipende dalle estensioni che accettano esplicitamente questa funzionalità.\",\n\t\t\"Controlla se i suggerimenti di filtro e ordinamento valgono per piccoli errori di battitura.\",\n\t\t\"Controlla se l\\'ordinamento privilegia le parole che appaiono più vicine al cursore.\",\n\t\t\"Controlla se condividere le selezioni dei suggerimenti memorizzati tra aree di lavoro e finestre (richiede `#editor.suggestSelection#`).\",\n\t\t\"Selezionare sempre un suggerimento quando si attiva automaticamente IntelliSense.\",\n\t\t\"Non selezionare mai un suggerimento quando si attiva automaticamente IntelliSense.\",\n\t\t\"Selezionare un suggerimento solo quando si attiva IntelliSense da un carattere di trigger.\",\n\t\t\"Selezionare un suggerimento solo quando si attiva IntelliSense durante la digitazione.\",\n\t\t\"Controlla se viene selezionato un suggerimento quando viene visualizzato il widget. Si noti che questo si applica solo ai suggerimenti attivati automaticamente (\\'#editor.quickSuggestions#\\' e \\'#editor.suggestOnTriggerCharacters#\\') e che un suggerimento viene sempre selezionato quando viene richiamato in modo esplicito, ad esempio tramite \\'CTRL+BARRA SPAZIATRICE\\'.\",\n\t\t\"Controlla se un frammento attivo impedisce i suggerimenti rapidi.\",\n\t\t\"Controlla se mostrare o nascondere le icone nei suggerimenti.\",\n\t\t\"Controlla la visibilità della barra di stato nella parte inferiore del widget dei suggerimenti.\",\n\t\t\"Controlla se visualizzare in anteprima il risultato del suggerimento nell\\'Editor.\",\n\t\t\"Controlla se i dettagli del suggerimento vengono visualizzati inline con l\\'etichetta o solo nel widget dei dettagli.\",\n\t\t\"Questa impostazione è deprecata. Il widget dei suggerimenti può ora essere ridimensionato.\",\n\t\t\"Questa impostazione è deprecata. In alternativa, usare impostazioni diverse, come \\'editor.suggest.showKeywords\\' o \\'editor.suggest.showSnippets\\'.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `method`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `function`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `constructor`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `deprecated`.\",\n\t\t\"Quando è abilitato, il filtro IntelliSense richiede che il primo carattere corrisponda all\\'inizio di una parola, ad esempio \\'c\\' per \\'Console\\' o \\'WebContext\\' ma _non_ per \\'description\\'. Quando è disabilitato, IntelliSense mostra più risultati, ma li ordina comunque in base alla qualità della corrispondenza.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `field`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `variable`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `class`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `struct`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `interface`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `module`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `property`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `event`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `operator`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `unit`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `value`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `constant`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `enum`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `enumMember`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `keyword`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `text`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `color`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `file`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `reference`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `customcolor`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `folder`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `typeParameter`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `snippet`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `user`.\",\n\t\t\"Se è abilitata, IntelliSense mostra i suggerimenti relativi a `issues`.\",\n\t\t\"Indica se gli spazi vuoti iniziali e finali devono essere sempre selezionati.\",\n\t\t\"Indica se è necessario selezionare le sottoparole ( come \\'foo\\' in \\'fooBar\\' o \\'foo_bar\\').\",\n\t\t\"Nessun rientro. Le righe con ritorno a capo iniziano dalla colonna 1. \",\n\t\t\"Le righe con ritorno a capo hanno lo stesso rientro della riga padre.\",\n\t\t\"Le righe con ritorno a capo hanno un rientro di +1 rispetto alla riga padre.\",\n\t\t\"Le righe con ritorno a capo hanno un rientro di +2 rispetto alla riga padre.\",\n\t\t\"Controlla il rientro delle righe con ritorno a capo.\",\n\t\t\"Controlla se è possibile trascinare un file in un editor di testo tenendo premuto MAIUSC (invece di aprire il file in un editor).\",\n\t\t\"Controlla se viene visualizzato un widget quando si rilasciano file nell\\'editor. Questo widget consente di controllare la modalità di rilascio del file.\",\n\t\t\"Mostra il widget del selettore di rilascio dopo il rilascio di un file nell\\'editor.\",\n\t\t\"Non visualizzare mai il widget del selettore di rilascio. Usare sempre il provider di rilascio predefinito.\",\n\t\t\"Controlla se è possibile incollare il contenuto in modi diversi.\",\n\t\t\"Controlla se viene visualizzato un widget quando si incolla il contenuto nell\\'editor. Questo widget consente di controllare il modo in cui il file viene incollato.\",\n\t\t\"Mostra il widget del selettore dell\\'operazione Incolla dopo che il contenuto è stato incollato nell\\'editor.\",\n\t\t\"Non visualizzare mai il widget del selettore dell\\'operazione Incolla. Usare sempre il comportamento dell\\'operazione Incolla predefinito.\",\n\t\t\"Controlla se accettare i suggerimenti con i caratteri di commit. Ad esempio, in JavaScript il punto e virgola (\\';\\') può essere un carattere di commit che accetta un suggerimento e digita tale carattere.\",\n\t\t\"Accetta un suggerimento con \\'Invio\\' solo quando si apporta una modifica al testo.\",\n\t\t\"Controlla se i suggerimenti devono essere accettati con \\'INVIO\\' in aggiunta a \\'TAB\\'. In questo modo è possibile evitare ambiguità tra l\\'inserimento di nuove righe e l\\'accettazione di suggerimenti.\",\n\t\t\"Controlla il numero di righe nell\\'Editor che possono essere lette alla volta da un utilità per la lettura dello schermo. Quando viene rilevata un\\'utilità per la lettura dello schermo, questo valore viene impostato su 500 per impostazione predefinita. Avviso: questa opzione può influire sulle prestazioni se il numero di righe è superiore a quello predefinito.\",\n\t\t\"Contenuto editor\",\n\t\t\"Controllare se i suggerimenti inline vengono annunciati da un\\'utilità per la lettura dello schermo.\",\n\t\t\"Usa le configurazioni del linguaggio per determinare la chiusura automatica delle parentesi.\",\n\t\t\"Chiudi automaticamente le parentesi solo quando il cursore si trova alla sinistra di uno spazio vuoto.\",\n\t\t\"Controlla se l\\'editor deve chiudere automaticamente le parentesi quadre dopo che sono state aperte.\",\n\t\t\"Rimuove le virgolette o le parentesi quadre di chiusura adiacenti solo se sono state inserite automaticamente.\",\n\t\t\"Controlla se l\\'editor deve rimuovere le virgolette o le parentesi quadre di chiusura adiacenti durante l\\'eliminazione.\",\n\t\t\"Digita sopra le virgolette o le parentesi quadre di chiusura solo se sono state inserite automaticamente.\",\n\t\t\"Controlla se l\\'editor deve digitare su virgolette o parentesi quadre.\",\n\t\t\"Usa le configurazioni del linguaggio per determinare la chiusura automatica delle virgolette.\",\n\t\t\"Chiudi automaticamente le virgolette solo quando il cursore si trova alla sinistra di uno spazio vuoto.\",\n\t\t\"Controlla se l\\'editor deve chiudere automaticamente le citazioni dopo che sono state aperte.\",\n\t\t\"L\\'editor non inserirà automaticamente il rientro.\",\n\t\t\"L\\'editor manterrà il rientro della riga corrente.\",\n\t\t\"L\\'editor manterrà il rientro della riga corrente e rispetterà le parentesi definite dalla lingua.\",\n\t\t\"L\\'editor manterrà il rientro della riga corrente, rispetterà le parentesi definite dalla lingua e richiamerà le regole onEnterRules speciali definite dalle lingue.\",\n\t\t\"L\\'editor manterrà il rientro della riga corrente, rispetterà le parentesi definite dalla lingua, richiamerà le regole onEnterRules speciali definite dalle lingue e rispetterà le regole indentationRules definite dalle lingue.\",\n\t\t\"Controlla se l\\'editor deve regolare automaticamente il rientro quando gli utenti digitano, incollano, spostano le righe o applicano il rientro.\",\n\t\t\"Usa le configurazioni del linguaggio per determinare quando racchiudere automaticamente le selezioni tra parentesi quadre o virgolette.\",\n\t\t\"Racchiude la selezione tra virgolette ma non tra parentesi quadre.\",\n\t\t\"Racchiude la selezione tra parentesi quadre ma non tra virgolette.\",\n\t\t\"Controlla se l\\'editor deve racchiudere automaticamente le selezioni quando si digitano virgolette o parentesi quadre.\",\n\t\t\"Emula il comportamento di selezione dei caratteri di tabulazione quando si usano gli spazi per il rientro. La selezione verrà applicata alle tabulazioni.\",\n\t\t\"Controlla se l\\'editor visualizza CodeLens.\",\n\t\t\"Controlla la famiglia di caratteri per CodeLens.\",\n\t\t\"Controlla le dimensioni del carattere in pixel per CodeLens. Quando è impostata su 0, viene usato il 90% del valore di \\'#editor.fontSize#\\'.\",\n\t\t\"Controlla se l\\'editor deve eseguire il rendering della selezione colori e degli elementi Decorator di tipo colore inline.\",\n\t\t\"Fare in modo che la selezione colori venga visualizzata sia al clic che al passaggio del mouse sull’elemento Decorator colore\",\n\t\t\"Fare in modo che la selezione colori venga visualizzata al passaggio del mouse sull\\'elemento Decorator colore\",\n\t\t\"Fare in modo che la selezione colori venga visualizzata quando si fa clic sull\\'elemento Decorator colore\",\n\t\t\"Controlla la condizione in modo che venga visualizzata la selezione colori da un elemento Decorator colore.\",\n\t\t\"Controlla il numero massimo di elementi Decorator a colori di cui è possibile eseguire il rendering in un editor contemporaneamente.\",\n\t\t\"Abilita l\\'uso di mouse e tasti per la selezione delle colonne.\",\n\t\t\"Controlla se l\\'evidenziazione della sintassi deve essere copiata negli Appunti.\",\n\t\t\"Controllo dello stile di animazione del cursore.\",\n\t\t\"L\\'animazione con cursore arrotondato è disabilitata.\",\n\t\t\"L\\'animazione con cursore uniforme è abilitata solo quando l\\'utente sposta il cursore con un movimento esplicito.\",\n\t\t\"L\\'animazione con cursore uniforme è sempre abilitata.\",\n\t\t\"Controlla se l\\'animazione del cursore con anti-aliasing deve essere abilitata.\",\n\t\t\"Controlla lo stile del cursore.\",\n\t\t\"Controllare il numero minimo di linee iniziali visibili (minimo 0) e finali (minimo 1) visibili che circondano il cursore. Noto come \\'scrollOff\\' o \\'scrollOffset\\' in altri editor.\",\n\t\t\"`cursorSurroundingLines` viene applicato solo quando è attivato tramite la tastiera o l\\'API.\",\n\t\t\"`cursorSurroundingLines` viene sempre applicato.\",\n\t\t\"Controlla quando deve essere applicato `cursorSurroundingLines`.\",\n\t\t\"Controlla la larghezza del cursore quando `#editor.cursorStyle#` è impostato su `line`.\",\n\t\t\"Controlla se l\\'editor deve consentire lo spostamento di selezioni tramite trascinamento della selezione.\",\n\t\t\"Usare un nuovo metodo di rendering con svgs.\",\n\t\t\"Usare un nuovo metodo di rendering con tipi di caratteri.\",\n\t\t\"Usare il metodo di rendering stabile.\",\n\t\t\"Controlla se viene eseguito il rendering degli spazi vuoti con un nuovo metodo sperimentale.\",\n\t\t\"Moltiplicatore della velocità di scorrimento quando si preme `Alt`.\",\n\t\t\"Controlla se per l\\'editor è abilitata la riduzione del codice.\",\n\t\t\"Usa una strategia di riduzione specifica della lingua, se disponibile; altrimenti ne usa una basata sui rientri.\",\n\t\t\"Usa la strategia di riduzione basata sui rientri.\",\n\t\t\"Controlla la strategia per il calcolo degli intervalli di riduzione.\",\n\t\t\"Controlla se l\\'editor deve evidenziare gli intervalli con riduzione del codice.\",\n\t\t\"Controlla se l\\'editor comprime automaticamente gli intervalli di importazione.\",\n\t\t\"Numero massimo di aree riducibili. Se si aumenta questo valore, l\\'editor potrebbe diventare meno reattivo quando l\\'origine corrente contiene un numero elevato di aree riducibili.\",\n\t\t\"Controlla se, facendo clic sul contenuto vuoto dopo una riga ridotta, la riga viene espansa.\",\n\t\t\"Controlla la famiglia di caratteri.\",\n\t\t\"Controlla se l\\'editor deve formattare automaticamente il contenuto incollato. Deve essere disponibile un formattatore che deve essere in grado di formattare un intervallo in un documento.\",\n\t\t\"Controlla se l\\'editor deve formattare automaticamente la riga dopo la digitazione.\",\n\t\t\"Controlla se l\\'editor deve eseguire il rendering del margine verticale del glifo. Il margine del glifo viene usato principalmente per il debug.\",\n\t\t\"Controlla se il cursore deve essere nascosto nel righello delle annotazioni.\",\n\t\t\"Controlla la spaziatura tra le lettere in pixel.\",\n\t\t\"Controlla se la modifica collegata è abilitata per l\\'editor. A seconda del linguaggio, i simboli correlati, ad esempio i tag HTML, vengono aggiornati durante la modifica.\",\n\t\t\"Controlla se l\\'editor deve individuare i collegamenti e renderli selezionabili.\",\n\t\t\"Evidenzia le parentesi graffe corrispondenti.\",\n\t\t\"Moltiplicatore da usare sui valori `deltaX` e `deltaY` degli eventi di scorrimento della rotellina del mouse.\",\n\t\t\"Ingrandisce il carattere dell\\'editor quando si usa la rotellina del mouse e si tiene premuto \\'CTRL\\'.\",\n\t\t\"Unire i cursori multipli se sovrapposti.\",\n\t\t\"Rappresenta il tasto \\'Control\\' in Windows e Linux e il tasto \\'Comando\\' in macOS.\",\n\t\t\"Rappresenta il tasto \\'Alt\\' in Windows e Linux e il tasto \\'Opzione\\' in macOS.\",\n\t\t\"Modificatore da usare per aggiungere più cursori con il mouse. I movimenti del mouse Vai alla definizione e Apri collegamento si adatteranno in modo da non entrare in conflitto con il [modificatore di selezione multipla](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).\",\n\t\t\"Ogni cursore incolla una singola riga del testo.\",\n\t\t\"Ogni cursore incolla il testo completo.\",\n\t\t\"Controlla l\\'operazione Incolla quando il conteggio delle righe del testo incollato corrisponde al conteggio dei cursori.\",\n\t\t\"Controlla il numero massimo di cursori che possono essere presenti in un editor attivo contemporaneamente.\",\n\t\t\"Controlla se l\\'editor deve evidenziare le occorrenze di simboli semantici.\",\n\t\t\"Controlla se deve essere disegnato un bordo intorno al righello delle annotazioni.\",\n\t\t\"Sposta lo stato attivo sull\\'albero quando si apre l\\'anteprima\",\n\t\t\"Sposta lo stato attivo sull\\'editor quando si apre l\\'anteprima\",\n\t\t\"Controlla se spostare lo stato attivo sull\\'editor inline o sull\\'albero nel widget di anteprima.\",\n\t\t\"Controlla se il movimento del mouse Vai alla definizione consente sempre di aprire il widget di anteprima.\",\n\t\t\"Controlla il ritardo in millisecondi dopo il quale verranno visualizzati i suggerimenti rapidi.\",\n\t\t\"Controlla se l\\'editor viene rinominato automaticamente in base al tipo.\",\n\t\t\"Deprecata. In alternativa, usare `editor.linkedEditing`.\",\n\t\t\"Controlla se l\\'editor deve eseguire il rendering dei caratteri di controllo.\",\n\t\t\"Esegue il rendering dell\\'ultimo numero di riga quando il file termina con un carattere di nuova riga.\",\n\t\t\"Mette in evidenza sia la barra di navigazione sia la riga corrente.\",\n\t\t\"Controlla in che modo l\\'editor deve eseguire il rendering dell\\'evidenziazione di riga corrente.\",\n\t\t\"Controlla se l\\'editor deve eseguire il rendering dell\\'evidenziazione della riga corrente solo quando l\\'editor ha lo stato attivo.\",\n\t\t\"Esegue il rendering dei caratteri di spazio vuoto ad eccezione dei singoli spazi tra le parole.\",\n\t\t\"Esegui il rendering dei caratteri di spazio vuoto solo nel testo selezionato.\",\n\t\t\"Esegui il rendering solo dei caratteri di spazio vuoto finali.\",\n\t\t\"Controlla in che modo l\\'editor deve eseguire il rendering dei caratteri di spazio vuoto.\",\n\t\t\"Controlla se le selezioni devono avere gli angoli arrotondati.\",\n\t\t\"Controlla il numero di caratteri aggiuntivi oltre i quali l\\'editor scorrerà orizzontalmente.\",\n\t\t\"Controlla se l\\'editor scorrerà oltre l\\'ultima riga.\",\n\t\t\"Scorre solo lungo l\\'asse predominante durante lo scorrimento verticale e orizzontale simultaneo. Impedisce la deviazione orizzontale quando si scorre in verticale su un trackpad.\",\n\t\t\"Controlla se gli appunti primari di Linux devono essere supportati.\",\n\t\t\"Controlla se l\\'editor deve evidenziare gli elementi corrispondenti simili alla selezione.\",\n\t\t\"Mostra sempre i comandi di riduzione.\",\n\t\t\"Non visualizzare mai i controlli di riduzione e diminuire le dimensioni della barra di navigazione.\",\n\t\t\"Mostra i comandi di riduzione solo quando il mouse è posizionato sul margine della barra di scorrimento.\",\n\t\t\"Controlla se i controlli di riduzione sul margine della barra di scorrimento vengono visualizzati.\",\n\t\t\"Controllo dissolvenza del codice inutilizzato.\",\n\t\t\"Controlla le variabili deprecate barrate.\",\n\t\t\"Visualizza i suggerimenti del frammento prima degli altri suggerimenti.\",\n\t\t\"Visualizza i suggerimenti del frammento dopo gli altri suggerimenti.\",\n\t\t\"Visualizza i suggerimenti del frammento insieme agli altri suggerimenti.\",\n\t\t\"Non mostrare i suggerimenti del frammento.\",\n\t\t\"Controlla se i frammenti di codice sono visualizzati con altri suggerimenti e il modo in cui sono ordinati.\",\n\t\t\"Controlla se per lo scorrimento dell\\'editor verrà usata un\\'animazione.\",\n\t\t\"Dimensioni del carattere per il widget dei suggerimenti. Se impostato su {0}, viene usato il valore di {1}.\",\n\t\t\"Altezza della riga per il widget dei suggerimenti. Se impostato su {0}, viene usato il valore {1}. Il valore minimo è 8.\",\n\t\t\"Controlla se i suggerimenti devono essere visualizzati automaticamente durante la digitazione dei caratteri trigger.\",\n\t\t\"Consente di selezionare sempre il primo suggerimento.\",\n\t\t\"Consente di selezionare suggerimenti recenti a meno che continuando a digitare non ne venga selezionato uno, ad esempio `console.| ->; console.log` perché `log` è stato completato di recente.\",\n\t\t\"Consente di selezionare i suggerimenti in base a prefissi precedenti che hanno completato tali suggerimenti, ad esempio `co ->; console` e `con -> const`.\",\n\t\t\"Controlla la modalità di preselezione dei suggerimenti durante la visualizzazione dell\\'elenco dei suggerimenti.\",\n\t\t\"La funzionalità di completamento con tasto TAB inserirà il migliore suggerimento alla pressione del tasto TAB.\",\n\t\t\"Disabilita le funzionalità di completamento con tasto TAB.\",\n\t\t\"Completa i frammenti con il tasto TAB quando i rispettivi prefissi corrispondono. Funziona in modo ottimale quando \\'quickSuggestions\\' non è abilitato.\",\n\t\t\"Abilità la funzionalità di completamento con tasto TAB.\",\n\t\t\"I caratteri di terminazione di riga insoliti vengono rimossi automaticamente.\",\n\t\t\"I caratteri di terminazione di riga insoliti vengono ignorati.\",\n\t\t\"Prompt per i caratteri di terminazione di riga insoliti da rimuovere.\",\n\t\t\"Rimuovi caratteri di terminazione di riga insoliti che potrebbero causare problemi.\",\n\t\t\"Inserimento ed eliminazione dello spazio vuoto dopo le tabulazioni.\",\n\t\t\"Usare la regola di interruzione di riga predefinita.\",\n\t\t\"Le interruzioni di parola non devono essere usate per il testo cinese/giapponese/coreano (CJK). Il comportamento del testo non CJK è uguale a quello normale.\",\n\t\t\"Controlla le regole di interruzione delle parole usate per il testo cinese/giapponese/coreano (CJK).\",\n\t\t\"Caratteri che verranno usati come separatori di parola quando si eseguono operazioni o spostamenti correlati a parole.\",\n\t\t\"Il ritorno a capo automatico delle righe non viene mai applicato.\",\n\t\t\"Il ritorno a capo automatico delle righe viene applicato in corrispondenza della larghezza del viewport.\",\n\t\t\"Il ritorno a capo automatico delle righe viene applicato in corrispondenza di `#editor.wordWrapColumn#`.\",\n\t\t\"Il ritorno a capo automatico delle righe viene applicato in corrispondenza della larghezza minima del viewport e di `#editor.wordWrapColumn#`.\",\n\t\t\"Controlla il ritorno a capo automatico delle righe.\",\n\t\t\"Controlla la colonna per il ritorno a capo automatico dell\\'editor quando il valore di `#editor.wordWrap#` è `wordWrapColumn` o `bounded`.\",\n\t\t\"Controllare se visualizzare le decorazioni colori incorporate usando il provider colori predefinito del documento\",\n\t\t\"Controlla se l\\'editor riceve le schede o le rinvia al workbench per lo spostamento.\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"Colore di sfondo per l\\'evidenziazione della riga alla posizione del cursore.\",\n\t\t\"Colore di sfondo per il bordo intorno alla riga alla posizione del cursore.\",\n\t\t\"Colore di sfondo degli intervalli evidenziati, ad esempio dalle funzionalità Quick Open e Trova. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo del bordo intorno agli intervalli selezionati.\",\n\t\t\"Colore di sfondo del simbolo evidenziato, ad esempio per passare alla definizione o al simbolo successivo/precedente. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo del bordo intorno ai simboli selezionati.\",\n\t\t\"Colore del cursore dell\\'editor.\",\n\t\t\"Colore di sfondo del cursore editor. Permette di personalizzare il colore di un carattere quando sovrapposto da un blocco cursore.\",\n\t\t\"Colore dei caratteri di spazio vuoto nell\\'editor.\",\n\t\t\"Colore dei numeri di riga dell\\'editor.\",\n\t\t\"Colore delle guide per i rientri dell\\'editor.\",\n\t\t\"\\'editorIndentGuide.background\\' è deprecato. Usare \\'editorIndentGuide.background1\\'.\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo\",\n\t\t\"\\'editorIndentGuide.activeBackground\\' è deprecato. Usare \\'editorIndentGuide.activeBackground1\\'.\",\n\t\t\"Colore delle guide per i rientri dell\\'editor (1).\",\n\t\t\"Colore delle guide per i rientri dell\\'editor (2).\",\n\t\t\"Colore delle guide per i rientri dell\\'editor (3).\",\n\t\t\"Colore delle guide per i rientri dell\\'editor (4).\",\n\t\t\"Colore delle guide per i rientri dell\\'editor (5).\",\n\t\t\"Colore delle guide per i rientri dell\\'editor (6).\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo (1).\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo (2).\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo (3).\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo (4).\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo (5).\",\n\t\t\"Colore delle guide di indentazione dell\\'editor attivo (6).\",\n\t\t\"Colore del numero di riga attivo dell\\'editor\",\n\t\t\"Id è deprecato. In alternativa usare \\'editorLineNumber.activeForeground\\'.\",\n\t\t\"Colore del numero di riga attivo dell\\'editor\",\n\t\t\"Colore della riga dell\\'editor finale quando editor.renderFinalNewline è impostato su in grigio.\",\n\t\t\"Colore dei righelli dell\\'editor.\",\n\t\t\"Colore primo piano delle finestre di CodeLens dell\\'editor\",\n\t\t\"Colore di sfondo delle parentesi corrispondenti\",\n\t\t\"Colore delle caselle di parentesi corrispondenti\",\n\t\t\"Colore del bordo del righello delle annotazioni.\",\n\t\t\"Colore di sfondo del righello delle annotazioni dell\\'editor.\",\n\t\t\"Colore di sfondo della barra di navigazione dell\\'editor. La barra contiene i margini di glifo e i numeri di riga.\",\n\t\t\"Colore del bordo del codice sorgente non necessario (non usato) nell\\'editor.\",\n\t\t\"Opacità del codice sorgente non necessario (non usato) nell\\'editor. Ad esempio, con \\\"#000000c0\\\" il rendering del codice verrà eseguito con il 75% di opacità. Per i temi a contrasto elevato, usare il colore del tema \\'editorUnnecessaryCode.border\\' per sottolineare il codice non necessario invece di opacizzarlo.\",\n\t\t\"Colore del bordo del testo fantasma nell\\'Editor.\",\n\t\t\"Colore primo piano del testo fantasma nell\\'Editor.\",\n\t\t\"Colore di sfondo del testo fantasma nell\\'editor.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per le evidenziazioni degli intervalli. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per gli errori.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per gli avvisi.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per i messaggi di tipo informativo.\",\n\t\t\"Colore primo piano delle parentesi quadre (1). Richiede l\\'abilitazione della colorazione delle coppie di parentesi quadre.\",\n\t\t\"Colore primo piano delle parentesi quadre (2). Richiede l\\'abilitazione della colorazione delle coppie di parentesi quadre.\",\n\t\t\"Colore primo piano delle parentesi quadre (3). Richiede l\\'abilitazione della colorazione delle coppie di parentesi quadre.\",\n\t\t\"Colore primo piano delle parentesi quadre (4). Richiede l\\'abilitazione della colorazione delle coppie di parentesi quadre.\",\n\t\t\"Colore primo piano delle parentesi quadre (5). Richiede l\\'abilitazione della colorazione delle coppie di parentesi quadre.\",\n\t\t\"Colore primo piano delle parentesi quadre (6). Richiede l\\'abilitazione della colorazione delle coppie di parentesi quadre.\",\n\t\t\"Colore di primo piano delle parentesi impreviste.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi inattive (1). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi inattive (2). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi inattive (3). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi inattive (4). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi inattive (5). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi inattive (6). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi attive (1). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi attive (2). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi attive (3). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi attive (4). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi attive (5). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore di sfondo delle guide per coppie di parentesi attive (6). Richiede l\\'abilitazione delle guide per coppie di parentesi.\",\n\t\t\"Colore del bordo utilizzato per evidenziare i caratteri Unicode.\",\n\t\t\"Colore di sfondo usato per evidenziare i caratteri Unicode.\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"Indica se il testo dell\\'editor ha lo stato attivo (il cursore lampeggia)\",\n\t\t\"Indica se l\\'editor o un widget dell\\'editor ha lo stato attivo (ad esempio, lo stato attivo si trova nel widget di ricerca)\",\n\t\t\"Indica se un editor o un input RTF ha lo stato attivo (il cursore lampeggia)\",\n\t\t\"Indica se l\\'editor è di sola lettura\",\n\t\t\"Indica se il contesto è un editor diff\",\n\t\t\"Indica se il contesto è un editor diff incorporato\",\n\t\t\"Indica se il visualizzatore differenze accessibile è visibile\",\n\t\t\"Indica se `editor.columnSelection` è abilitato\",\n\t\t\"Indica se per l\\'editor esiste testo selezionato\",\n\t\t\"Indica se per l\\'editor esistono più selezioni\",\n\t\t\"Indica se premendo `TAB`, lo stato attivo verrà spostato all\\'esterno dell\\'editor\",\n\t\t\"Indica se il passaggio del puntatore nell\\'editor è visibile\",\n\t\t\"Indica se l\\'area sensibile al passaggio del mouse dell\\'edito è attivata\",\n\t\t\"Indica se lo scorrimento permanente è attivo\",\n\t\t\"Indica se lo scorrimento permanente è visibile\",\n\t\t\"Indicare se la selezione colori autonoma è visibile\",\n\t\t\"Indicare se la selezione colori autonoma è evidenziata\",\n\t\t\"Indica se l\\'editor fa parte di un editor più esteso (ad esempio notebook)\",\n\t\t\"Identificatore lingua dell\\'editor\",\n\t\t\"Indica se per l\\'editor esiste un provider di voci di completamento\",\n\t\t\"Indica se per l\\'editor esiste un provider di azioni codice\",\n\t\t\"Indica se per l\\'editor esiste un provider di CodeLens\",\n\t\t\"Indica se per l\\'editor esiste un provider di definizioni\",\n\t\t\"Indica se per l\\'editor esiste un provider di dichiarazioni\",\n\t\t\"Indica se per l\\'editor esiste un provider di implementazioni\",\n\t\t\"Indica se per l\\'editor esiste un provider di definizioni di tipo\",\n\t\t\"Indica se per l\\'editor esiste un provider di passaggi del mouse\",\n\t\t\"Indica se per l\\'editor esiste un provider di evidenziazione documenti\",\n\t\t\"Indica se per l\\'editor esiste un provider di simboli di documenti\",\n\t\t\"Indica se per l\\'editor esiste un provider di riferimenti\",\n\t\t\"Indica se per l\\'editor esiste un provider di ridenominazione\",\n\t\t\"Indica se per l\\'editor esiste un provider della guida per la firma\",\n\t\t\"Indica se per l\\'editor esiste un provider di suggerimenti inline\",\n\t\t\"Indica se per l\\'editor esiste un provider di formattazione documenti\",\n\t\t\"Indica se per l\\'editor esiste un provider di formattazione di selezioni documento\",\n\t\t\"Indica se per l\\'editor esistono più provider di formattazione documenti\",\n\t\t\"Indica se per l\\'editor esistono più provider di formattazione di selezioni documento\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"matrice\",\n\t\t\"valore booleano\",\n\t\t\"classe\",\n\t\t\"costante\",\n\t\t\"costruttore\",\n\t\t\"enumerazione\",\n\t\t\"membro di enumerazione\",\n\t\t\"evento\",\n\t\t\"campo\",\n\t\t\"file\",\n\t\t\"funzione\",\n\t\t\"interfaccia\",\n\t\t\"chiave\",\n\t\t\"metodo\",\n\t\t\"modulo\",\n\t\t\"spazio dei nomi\",\n\t\t\"Null\",\n\t\t\"numero\",\n\t\t\"oggetto\",\n\t\t\"operatore\",\n\t\t\"pacchetto\",\n\t\t\"proprietà\",\n\t\t\"stringa\",\n\t\t\"struct\",\n\t\t\"parametro di tipo\",\n\t\t\"variabile\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"Testo normale\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"Digitazione\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"Sviluppatore: Controlla token\",\n\t\t\"Vai a Riga/Colonna...\",\n\t\t\"Mostra tutti i provider di accesso rapido\",\n\t\t\"Riquadro comandi\",\n\t\t\"Mostra ed esegui comandi\",\n\t\t\"Vai al simbolo...\",\n\t\t\"Vai al simbolo per categoria...\",\n\t\t\"Contenuto editor\",\n\t\t\"Premere ALT+F1 per le opzioni di accessibilità.\",\n\t\t\"Attiva/disattiva tema a contrasto elevato\",\n\t\t\"Effettuate {0} modifiche in {1} file\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"Mostra di più ({0})\",\n\t\t\"{0} caratteri\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"Ancoraggio della selezione\",\n\t\t\"Ancoraggio impostato alla posizione {0}:{1}\",\n\t\t\"Imposta ancoraggio della selezione\",\n\t\t\"Vai ad ancoraggio della selezione\",\n\t\t\"Seleziona da ancoraggio a cursore\",\n\t\t\"Annulla ancoraggio della selezione\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"Colore del marcatore del righello delle annotazioni per la corrispondenza delle parentesi.\",\n\t\t\"Vai alla parentesi quadra\",\n\t\t\"Seleziona fino alla parentesi\",\n\t\t\"Rimuovi parentesi quadre\",\n\t\t\"Vai alla parentesi &&quadra\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"Sposta testo selezionato a sinistra\",\n\t\t\"Sposta testo selezionato a destra\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"Trasponi lettere\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"&&Taglia\",\n\t\t\"Taglia\",\n\t\t\"Taglia\",\n\t\t\"Taglia\",\n\t\t\"&&Copia\",\n\t\t\"Copia\",\n\t\t\"Copia\",\n\t\t\"Copia\",\n\t\t\"Copia con nome\",\n\t\t\"Copia con nome\",\n\t\t\"Condividi\",\n\t\t\"Condividi\",\n\t\t\"Condividi\",\n\t\t\"&&Incolla\",\n\t\t\"Incolla\",\n\t\t\"Incolla\",\n\t\t\"Incolla\",\n\t\t\"Copia con evidenziazione sintassi\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"Si è verificato un errore sconosciuto durante l\\'applicazione dell\\'azione del codice\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"Tipo dell\\'azione codice da eseguire.\",\n\t\t\"Controlla quando vengono applicate le azioni restituite.\",\n\t\t\"Applica sempre la prima azione codice restituita.\",\n\t\t\"Applica la prima azione codice restituita se è l\\'unica.\",\n\t\t\"Non applicare le azioni codice restituite.\",\n\t\t\"Controlla se devono essere restituite solo le azioni codice preferite.\",\n\t\t\"Correzione rapida...\",\n\t\t\"Azioni codice non disponibili\",\n\t\t\"Non sono disponibili azioni codice preferite per \\'{0}\\'\",\n\t\t\"Non sono disponibili azioni codice per \\'{0}\\'\",\n\t\t\"Non sono disponibili azioni codice preferite\",\n\t\t\"Azioni codice non disponibili\",\n\t\t\"Effettua refactoring...\",\n\t\t\"Non sono disponibili refactoring preferiti per \\'{0}\\'\",\n\t\t\"Non sono disponibili refactoring per \\'{0}\\'\",\n\t\t\"Non sono disponibili refactoring preferiti\",\n\t\t\"Refactoring non disponibili\",\n\t\t\"Azione origine...\",\n\t\t\"Non sono disponibili azioni origine preferite per \\'{0}\\'\",\n\t\t\"Non sono disponibili azioni origine per \\'{0}\\'\",\n\t\t\"Non sono disponibili azioni origine preferite\",\n\t\t\"Azioni origine non disponibili\",\n\t\t\"Organizza import\",\n\t\t\"Azioni di organizzazione Imports non disponibili\",\n\t\t\"Correggi tutto\",\n\t\t\"Non è disponibile alcuna azione Correggi tutto\",\n\t\t\"Correzione automatica...\",\n\t\t\"Non sono disponibili correzioni automatiche\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"Abilita/disabilita la visualizzazione delle intestazioni gruppo nel menu Azione codice.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"Nascondi elementi disabilitati\",\n\t\t\"Mostra elementi disabilitati\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"Altre azioni...\",\n\t\t\"Correzione rapida...\",\n\t\t\"Estrai...\",\n\t\t\"Inline...\",\n\t\t\"Riscrivi...\",\n\t\t\"Sposta...\",\n\t\t\"Racchiudi con...\",\n\t\t\"Azione di origine...\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"Mostra azioni codice. Correzione rapida preferita disponibile ({0})\",\n\t\t\"Mostra Azioni codice ({0})\",\n\t\t\"Mostra Azioni codice\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"Mostra comandi di CodeLens per la riga corrente\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"Fare clic per attivare/disattivare le opzioni di colore (rgb/hsl/hex)\",\n\t\t\"Icona per chiudere la selezione colori\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"Mostra o sposta lo stato attivo su Selezione colori autonomo\",\n\t\t\"&&Mostra o sposta lo stato attivo su Selezione colori autonomo\",\n\t\t\"Nascondere la Selezione colori\",\n\t\t\"Inserire colore con Selezione colori autonomo\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"Attiva/disattiva commento per la riga\",\n\t\t\"Attiva/Disattiva commento per la &&riga\",\n\t\t\"Aggiungi commento per la riga\",\n\t\t\"Rimuovi commento per la riga\",\n\t\t\"Attiva/Disattiva commento per il blocco\",\n\t\t\"Attiva/Disattiva commento per il &&blocco\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"Minimappa\",\n\t\t\"Esegui rendering dei caratteri\",\n\t\t\"Dimensioni verticali\",\n\t\t\"Proporzionale\",\n\t\t\"Riempimento\",\n\t\t\"Adatta\",\n\t\t\"Dispositivo di scorrimento\",\n\t\t\"Passaggio del mouse\",\n\t\t\"Sempre\",\n\t\t\"Mostra il menu di scelta rapida editor\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"Cursore - Annulla\",\n\t\t\"Cursore - Ripeti\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"Incolla come...\",\n\t\t\"ID della modifica dell\\'operazione Incolla da provare ad applicare. Se non viene specificato, l\\'editor mostrerà un controllo di selezione.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"Indica se il widget dell\\'operazione Incolla viene visualizzato\",\n\t\t\"Mostra opzioni operazione Incolla...\",\n\t\t\"Esecuzione dei gestori del comando Incolla. Fare clic per annullare\",\n\t\t\"Seleziona azione Incolla\",\n\t\t\"Esecuzione dei gestori Incolla in corso\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"Predefinita\",\n\t\t\"Inserire testo normale\",\n\t\t\"Inserire l\\'URL\",\n\t\t\"Inserire l\\'Uri\",\n\t\t\"Inserire percorsi\",\n\t\t\"Inserire percorso\",\n\t\t\"Inserire percorsi relativi\",\n\t\t\"Inserire percorso relativo\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"Indica se il widget di rilascio viene visualizzato\",\n\t\t\"Mostra opzioni di rilascio...\",\n\t\t\"Esecuzione dei gestori di rilascio. Fare clic per annullare\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"Indica se l\\'editor esegue un\\'operazione annullabile, ad esempio \\'Anteprima riferimenti\\'\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"Trova\",\n\t\t\"&&Trova\",\n\t\t\"Esegue l\\'override del contrassegno \\\"Usa espressione regolare\\\".\\r\\nIl contrassegno non verrà salvato per il futuro.\\r\\n0: Non eseguire alcuna operazione\\r\\n1: Vero\\r\\n2: Falso\",\n\t\t\"Esegue l\\'override del contrassegno \\\"Corrispondenza parola intera\\\".\\r\\nIl contrassegno non verrà salvato per il futuro.\\r\\n0: Non eseguire alcuna operazione\\r\\n1: Vero\\r\\n2: Falso\",\n\t\t\"Esegue l\\'override del contrassegno \\\"Fai corrispondere maiuscole/minuscole\\\".\\r\\nIl contrassegno non verrà salvato per il futuro.\\r\\n0: Non eseguire alcuna operazione\\r\\n1: Vero\\r\\n2: Falso\",\n\t\t\"Esegue l\\'override del contrassegno \\\"Mantieni maiuscole/minuscole\\\".\\r\\nIl contrassegno non verrà salvato per il futuro.\\r\\n0: Non eseguire alcuna operazione\\r\\n1: Vero\\r\\n2: Falso\",\n\t\t\"Trova con gli argomenti\",\n\t\t\"Trova con selezione\",\n\t\t\"Trova successivo\",\n\t\t\"Trova precedente\",\n\t\t\"Andare a Corrispondenza...\",\n\t\t\"Nessuna corrispondenza. Provare a cercare qualcos\\'altro.\",\n\t\t\"Digitare un numero per passare a una corrispondenza specifica (tra 1 e {0})\",\n\t\t\"Digitare un numero compreso tra 1 e {0}\",\n\t\t\"Digitare un numero compreso tra 1 e {0}\",\n\t\t\"Trova selezione successiva\",\n\t\t\"Trova selezione precedente\",\n\t\t\"Sostituisci\",\n\t\t\"&&Sostituisci\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"Icona per \\'Trova nella selezione\\' nel widget di ricerca dell\\'editor.\",\n\t\t\"Icona per indicare che il widget di ricerca dell\\'editor è compresso.\",\n\t\t\"Icona per indicare che il widget di ricerca dell\\'editor è espanso.\",\n\t\t\"Icona per \\'Sostituisci\\' nel widget di ricerca dell\\'editor.\",\n\t\t\"Icona per \\'Sostituisci tutto\\' nel widget di ricerca dell\\'editor.\",\n\t\t\"Icona per \\'Trova precedente\\' nel widget di ricerca dell\\'editor.\",\n\t\t\"Icona per \\'Trova successivo\\' nel widget di ricerca dell\\'editor.\",\n\t\t\"Trova/Sostituisci\",\n\t\t\"Trova\",\n\t\t\"Trova\",\n\t\t\"Risultato precedente\",\n\t\t\"Risultato successivo\",\n\t\t\"Trova nella selezione\",\n\t\t\"Chiudi\",\n\t\t\"Sostituisci\",\n\t\t\"Sostituisci\",\n\t\t\"Sostituisci\",\n\t\t\"Sostituisci tutto\",\n\t\t\"Attiva/Disattiva sostituzione\",\n\t\t\"Solo i primi {0} risultati vengono evidenziati, ma tutte le operazioni di ricerca funzionano su tutto il testo.\",\n\t\t\"{0} di {1}\",\n\t\t\"Nessun risultato\",\n\t\t\"{0} trovato\",\n\t\t\"{0} trovati per \\'{1}\\'\",\n\t\t\"{0} trovati per \\'{1}\\' alla posizione {2}\",\n\t\t\"{0} trovati per \\'{1}\\'\",\n\t\t\"Il tasto di scelta rapida CTRL+INVIO ora consente di inserire l\\'interruzione di linea invece di sostituire tutto. Per eseguire l\\'override di questo comportamento, è possibile modificare il tasto di scelta rapida per editor.action.replaceAll.\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"Espandi\",\n\t\t\"Espandi in modo ricorsivo\",\n\t\t\"Riduci\",\n\t\t\"Attiva/Disattiva riduzione\",\n\t\t\"Riduci in modo ricorsivo\",\n\t\t\"Riduci tutti i blocchi commento\",\n\t\t\"Riduci tutte le regioni\",\n\t\t\"Espandi tutte le regioni\",\n\t\t\"Riduci tutte le regioni eccetto quelle selezionate\",\n\t\t\"Espandi tutte le regioni eccetto quelle selezionate\",\n\t\t\"Riduci tutto\",\n\t\t\"Espandi tutto\",\n\t\t\"Vai alla cartella principale\",\n\t\t\"Passa all\\'intervallo di riduzione precedente\",\n\t\t\"Passa all\\'intervallo di riduzione successivo\",\n\t\t\"Creare intervallo di riduzione dalla selezione\",\n\t\t\"Rimuovi intervalli di riduzione manuale\",\n\t\t\"Livello riduzione {0}\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"Colore di sfondo degli intervalli con riduzione. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del controllo di riduzione nella barra di navigazione dell\\'editor.\",\n\t\t\"Icona per gli intervalli espansi nel margine del glifo dell\\'editor.\",\n\t\t\"Icona per gli intervalli compressi nel margine del glifo dell\\'editor.\",\n\t\t\"Icona per gli intervalli compressi nel margine del glifo dell\\'editor.\",\n\t\t\"Icona per gli intervalli espansi manualmente nel margine del glifo dell\\'editor.\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"Zoom avanti tipo di carattere editor\",\n\t\t\"Zoom indietro tipo di carattere editor\",\n\t\t\"Reimpostazione zoom tipo di carattere editor\",\n\t],\n\t\"vs/editor/contrib/format/browser/format\": [\n\t\t\"È stata apportata 1 modifica di formattazione a riga {0}\",\n\t\t\"Sono state apportate {0} modifiche di formattazione a riga {1}\",\n\t\t\"È stata apportata 1 modifica di formattazione tra le righe {0} e {1}\",\n\t\t\"Sono state apportate {0} modifiche di formattazione tra le righe {1} e {2}\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"Formatta documento\",\n\t\t\"Formatta selezione\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"Vai al problema successivo (Errore, Avviso, Informazioni)\",\n\t\t\"Icona per il marcatore Vai a successivo.\",\n\t\t\"Vai al problema precedente (Errore, Avviso, Informazioni)\",\n\t\t\"Icona per il marcatore Vai a precedente.\",\n\t\t\"Vai al problema successivo nei file (Errore, Avviso, Informazioni)\",\n\t\t\"&&Problema successivo\",\n\t\t\"Vai al problema precedente nei file (Errore, Avviso, Informazioni)\",\n\t\t\"&&Problema precedente\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"Errore\",\n\t\t\"Avviso\",\n\t\t\"Info\",\n\t\t\"Suggerimento\",\n\t\t\"{0} a {1}. \",\n\t\t\"{0} di {1} problemi\",\n\t\t\"{0} di {1} problema\",\n\t\t\"Colore per gli errori del widget di spostamento tra marcatori dell\\'editor.\",\n\t\t\"Intestazione errore per lo sfondo del widget di spostamento tra marcatori dell\\'editor.\",\n\t\t\"Colore per gli avvisi del widget di spostamento tra marcatori dell\\'editor.\",\n\t\t\"Intestazione avviso per lo sfondo del widget di spostamento tra marcatori dell\\'editor.\",\n\t\t\"Colore delle informazioni del widget di navigazione marcatori dell\\'editor.\",\n\t\t\"Intestazione informativa per lo sfondo del widget di spostamento tra marcatori dell\\'editor.\",\n\t\t\"Sfondo del widget di spostamento tra marcatori dell\\'editor.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"Anteprima\",\n\t\t\"Definizioni\",\n\t\t\"Non è stata trovata alcuna definizione per \\'{0}\\'\",\n\t\t\"Non è stata trovata alcuna definizione\",\n\t\t\"Vai alla definizione\",\n\t\t\"Vai alla &&definizione\",\n\t\t\"Apri definizione lateralmente\",\n\t\t\"Visualizza in anteprima la definizione\",\n\t\t\"Dichiarazioni\",\n\t\t\"Non è stata trovata alcuna dichiarazione per \\'{0}\\'\",\n\t\t\"Dichiarazione non trovata\",\n\t\t\"Vai a dichiarazione\",\n\t\t\"Vai a &&dichiarazione\",\n\t\t\"Non è stata trovata alcuna dichiarazione per \\'{0}\\'\",\n\t\t\"Dichiarazione non trovata\",\n\t\t\"Anteprima dichiarazione\",\n\t\t\"Definizioni di tipo\",\n\t\t\"Non sono state trovate definizioni di tipi per \\'{0}\\'\",\n\t\t\"Non sono state trovate definizioni di tipi\",\n\t\t\"Vai alla definizione di tipo\",\n\t\t\"Vai alla &&definizione di tipo\",\n\t\t\"Anteprima definizione di tipo\",\n\t\t\"Implementazioni\",\n\t\t\"Non sono state trovate implementazioni per \\'{0}\\'\",\n\t\t\"Non sono state trovate implementazioni\",\n\t\t\"Vai a implementazioni\",\n\t\t\"Vai a &&Implementazioni\",\n\t\t\"Visualizza implementazioni\",\n\t\t\"Non sono stati trovati riferimenti per \\'{0}\\'\",\n\t\t\"Non sono stati trovati riferimenti\",\n\t\t\"Vai a Riferimenti\",\n\t\t\"Vai a &&riferimenti\",\n\t\t\"Riferimenti\",\n\t\t\"Anteprima riferimenti\",\n\t\t\"Riferimenti\",\n\t\t\"Vai a qualsiasi simbolo\",\n\t\t\"Posizioni\",\n\t\t\"Nessun risultato per \\'{0}\\'\",\n\t\t\"Riferimenti\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"Fare clic per visualizzare {0} definizioni.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"Indica se l\\'anteprima riferimenti è visibile, come \\'Visualizza in anteprima riferimenti\\' o \\'Visualizza in anteprima la definizione\\'\",\n\t\t\"Caricamento...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} riferimenti\",\n\t\t\"{0} riferimento\",\n\t\t\"Riferimenti\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"anteprima non disponibile\",\n\t\t\"Nessun risultato\",\n\t\t\"Riferimenti\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"in {0} alla riga {1} della colonna {2}\",\n\t\t\"{0} in {1} alla riga {2} della colonna {3}\",\n\t\t\"1 simbolo in {0}, percorso completo {1}\",\n\t\t\"{0} simboli in {1}, percorso completo {2}\",\n\t\t\"Non sono stati trovati risultati\",\n\t\t\"Trovato 1 simbolo in {0}\",\n\t\t\"Trovati {0} simboli in {1}\",\n\t\t\"Trovati {0} simboli in {1} file\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"Indica se sono presenti posizioni dei simboli a cui è possibile passare solo tramite la tastiera.\",\n\t\t\"Simbolo {0} di {1}, {2} per il successivo\",\n\t\t\"Simbolo {0} di {1}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"Mostra o sposta lo stato attivo al passaggio del mouse\",\n\t\t\"Ispezionarlo nella visualizzazione accessibile con {0}\",\n\t\t\"Ispezionarlo nella visualizzazione accessibile tramite il comando Apri visualizzazione accessibile che attualmente non è attivabile tramite il tasto di scelta rapida\",\n\t\t\"Mostra anteprima definizione al passaggio del mouse\",\n\t\t\"Scorri verso l\\'alto al passaggio del mouse\",\n\t\t\"Scorri verso il basso al passaggio del mouse\",\n\t\t\"Scorri a sinistra al passaggio del mouse\",\n\t\t\"Scorri a destra al passaggio del mouse\",\n\t\t\"Vai alla pagina precedente al passaggio del mouse\",\n\t\t\"Vai alla pagina successiva al passaggio del mouse\",\n\t\t\"Vai in alto al passaggio del mouse\",\n\t\t\"Vai in basso al passaggio del mouse\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"Caricamento...\",\n\t\t\"Rendering sospeso per una linea lunga per motivi di prestazioni. Può essere configurato tramite \\'editor.stopRenderingLineAfter\\'.\",\n\t\t\"Per motivi di prestazioni la tokenizzazione viene ignorata per le righe lunghe. È possibile effettuare questa configurazione tramite `editor.maxTokenizationLineLength`.\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"Visualizza problema\",\n\t\t\"Non sono disponibili correzioni rapide\",\n\t\t\"Verifica disponibilità correzioni rapide...\",\n\t\t\"Non sono disponibili correzioni rapide\",\n\t\t\"Correzione rapida...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"Sostituisci con il valore precedente\",\n\t\t\"Sostituisci con il valore successivo\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"Converti rientro in spazi\",\n\t\t\"Converti rientro in tabulazioni\",\n\t\t\"Dimensione tabulazione configurata\",\n\t\t\"Dimensioni predefinite della scheda\",\n\t\t\"Dimensioni della scheda corrente\",\n\t\t\"Seleziona dimensione tabulazione per il file corrente\",\n\t\t\"Imposta rientro con tabulazioni\",\n\t\t\"Imposta rientro con spazi\",\n\t\t\"Modifica dimensioni visualizzazione scheda\",\n\t\t\"Rileva rientro dal contenuto\",\n\t\t\"Imposta nuovo rientro per righe\",\n\t\t\"Re-Indenta le Linee Selezionate\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"Fare doppio clic per inserire\",\n\t\t\"CMD+clic\",\n\t\t\"CTRL+clic\",\n\t\t\"Opzione+clic\",\n\t\t\"ALT+clic\",\n\t\t\"Vai alla definizione ({0}), fai clic con il pulsante destro del mouse per altre informazioni\",\n\t\t\"Vai alla definizione ({0})\",\n\t\t\"Esegui il comando\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"Mostrare suggerimento inline successivo\",\n\t\t\"Mostrare suggerimento inline precedente\",\n\t\t\"Trigger del suggerimento inline\",\n\t\t\"Accettare suggerimento inline per la parola successiva\",\n\t\t\"Accetta parola\",\n\t\t\"Accetta la riga successiva del suggerimento in linea\",\n\t\t\"Accetta riga\",\n\t\t\"Accetta il suggerimento in linea\",\n\t\t\"Accetta\",\n\t\t\"Nascondi suggerimento inline\",\n\t\t\"Mostra sempre la barra degli strumenti\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"Suggerimento:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"Se è visibile un suggerimento inline\",\n\t\t\"Se il suggerimento in linea inizia con spazi vuoti\",\n\t\t\"Indica se il suggerimento inline inizia con uno spazio vuoto minore di quello che verrebbe inserito dalla tabulazione\",\n\t\t\"Indica se i suggerimenti devono essere eliminati per il suggerimento corrente\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"Icona per visualizzare il suggerimento del parametro successivo.\",\n\t\t\"Icona per visualizzare il suggerimento del parametro precedente.\",\n\t\t\"{0} ({1})\",\n\t\t\"Indietro\",\n\t\t\"Avanti\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"Espandere selezione riga\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"Copia la riga in alto\",\n\t\t\"&&Copia la riga in alto\",\n\t\t\"Copia la riga in basso\",\n\t\t\"Co&&pia la riga in basso\",\n\t\t\"Duplica selezione\",\n\t\t\"&&Duplica selezione\",\n\t\t\"Sposta la riga in alto\",\n\t\t\"Sposta la riga in &&alto\",\n\t\t\"Sposta la riga in basso\",\n\t\t\"Sposta la riga in &&basso\",\n\t\t\"Ordinamento righe crescente\",\n\t\t\"Ordinamento righe decrescente\",\n\t\t\"Elimina righe duplicate\",\n\t\t\"Taglia spazio vuoto finale\",\n\t\t\"Elimina riga\",\n\t\t\"Imposta un rientro per la riga\",\n\t\t\"Riduci il rientro per la riga\",\n\t\t\"Inserisci la riga sopra\",\n\t\t\"Inserisci la riga sotto\",\n\t\t\"Elimina tutto a sinistra\",\n\t\t\"Elimina tutto a destra\",\n\t\t\"Unisci righe\",\n\t\t\"Trasponi caratteri intorno al cursore\",\n\t\t\"Converti in maiuscolo\",\n\t\t\"Converti in minuscolo\",\n\t\t\"Trasforma in Tutte Iniziali Maiuscole\",\n\t\t\"Trasforma in snake case\",\n\t\t\"Trasforma in caso Camel\",\n\t\t\"Trasformare in caso Kebab\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"Avvia modifica collegata\",\n\t\t\"Colore di sfondo quando l\\'editor viene rinominato automaticamente in base al tipo.\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"Non è stato possibile aprire questo collegamento perché il formato non è valido: {0}\",\n\t\t\"Non è stato possibile aprire questo collegamento perché manca la destinazione.\",\n\t\t\"Esegui il comando\",\n\t\t\"Visita il collegamento\",\n\t\t\"CMD+clic\",\n\t\t\"CTRL+clic\",\n\t\t\"Opzione+clic\",\n\t\t\"ALT+clic\",\n\t\t\"Esegue il comando {0}\",\n\t\t\"Apri collegamento\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"Indica se l\\'editor visualizza attualmente un messaggio inline\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"Cursore aggiunto: {0}\",\n\t\t\"Cursori aggiunti: {0}\",\n\t\t\"Aggiungi cursore sopra\",\n\t\t\"&&Aggiungi cursore sopra\",\n\t\t\"Aggiungi cursore sotto\",\n\t\t\"A&&ggiungi cursore sotto\",\n\t\t\"Aggiungi cursori a fine riga\",\n\t\t\"Aggiungi c&&ursori a fine riga\",\n\t\t\"Aggiungi cursori alla fine\",\n\t\t\"Aggiungi cursori all\\'inizio\",\n\t\t\"Aggiungi selezione a risultato ricerca successivo\",\n\t\t\"Aggiungi &&occorrenza successiva\",\n\t\t\"Aggiungi selezione a risultato ricerca precedente\",\n\t\t\"Aggiungi occorrenza &&precedente\",\n\t\t\"Sposta ultima selezione a risultato ricerca successivo\",\n\t\t\"Sposta ultima selezione a risultato ricerca precedente\",\n\t\t\"Seleziona tutte le occorrenze del risultato ricerca\",\n\t\t\"Seleziona &&tutte le occorrenze\",\n\t\t\"Cambia tutte le occorrenze\",\n\t\t\"Attival cursore successivo\",\n\t\t\"Attiva il cursore successivo\",\n\t\t\"Cursore precedente stato attivo\",\n\t\t\"Imposta lo stato attivo sul cursore precedente\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"Attiva i suggerimenti per i parametri\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"Icona per visualizzare il suggerimento del parametro successivo.\",\n\t\t\"Icona per visualizzare il suggerimento del parametro precedente.\",\n\t\t\"{0}, suggerimento\",\n\t\t\"Colore di primo piano dell’articolo attivo nel suggerimento di parametro.\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"Indica se l\\'editor di codice corrente è incorporato nell\\'anteprima\",\n\t\t\"Chiudi\",\n\t\t\"Colore di sfondo dell\\'area del titolo della visualizzazione rapida.\",\n\t\t\"Colore del titolo della visualizzazione rapida.\",\n\t\t\"Colore delle informazioni del titolo della visualizzazione rapida.\",\n\t\t\"Colore dei bordi e della freccia della visualizzazione rapida.\",\n\t\t\"Colore di sfondo dell\\'elenco risultati della visualizzazione rapida.\",\n\t\t\"Colore primo piano dei nodi riga nell\\'elenco risultati della visualizzazione rapida.\",\n\t\t\"Colore primo piano dei nodi file nell\\'elenco risultati della visualizzazione rapida.\",\n\t\t\"Colore di sfondo della voce selezionata nell\\'elenco risultati della visualizzazione rapida.\",\n\t\t\"Colore primo piano della voce selezionata nell\\'elenco risultati della visualizzazione rapida.\",\n\t\t\"Colore di sfondo dell\\'editor di visualizzazioni rapide.\",\n\t\t\"Colore di sfondo della barra di navigazione nell\\'editor visualizzazione rapida.\",\n\t\t\"Colore di sfondo della barra di scorrimento permanente nell\\'editor visualizzazione rapida.\",\n\t\t\"Colore dell\\'evidenziazione delle corrispondenze nell\\'elenco risultati della visualizzazione rapida.\",\n\t\t\"Colore dell\\'evidenziazione delle corrispondenze nell\\'editor di visualizzazioni rapide.\",\n\t\t\"Bordo dell\\'evidenziazione delle corrispondenze nell\\'editor di visualizzazioni rapide.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"Aprire prima un editor di testo per passare a una riga.\",\n\t\t\"Vai a riga {0} e carattere {1}.\",\n\t\t\"Vai alla riga {0}.\",\n\t\t\"Riga corrente: {0}, carattere: {1}. Digitare un numero di riga a cui passare compreso tra 1 e {2}.\",\n\t\t\"Riga corrente: {0}, Carattere: {1}. Digitare un numero di riga a cui passare.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"Per passare a un simbolo, aprire prima un editor di testo con informazioni sui simboli.\",\n\t\t\"L\\'editor di testo attivo non fornisce informazioni sui simboli.\",\n\t\t\"Non ci sono simboli dell\\'editor corrispondenti\",\n\t\t\"Non ci sono simboli dell\\'editor\",\n\t\t\"Apri lateralmente\",\n\t\t\"Apri in basso\",\n\t\t\"simboli ({0})\",\n\t\t\"proprietà ({0})\",\n\t\t\"metodi ({0})\",\n\t\t\"funzioni ({0})\",\n\t\t\"costruttori ({0})\",\n\t\t\"variabili ({0})\",\n\t\t\"classi ({0})\",\n\t\t\"struct ({0})\",\n\t\t\"eventi ({0})\",\n\t\t\"operatori ({0})\",\n\t\t\"interfacce ({0})\",\n\t\t\"spazi dei nomi ({0})\",\n\t\t\"pacchetti ({0})\",\n\t\t\"parametri di tipo ({0})\",\n\t\t\"moduli ({0})\",\n\t\t\"proprietà ({0})\",\n\t\t\"enumerazioni ({0})\",\n\t\t\"membri di enumerazione ({0})\",\n\t\t\"stringhe ({0})\",\n\t\t\"file ({0})\",\n\t\t\"matrici ({0})\",\n\t\t\"numeri ({0})\",\n\t\t\"valori booleani ({0})\",\n\t\t\"oggetti ({0})\",\n\t\t\"chiavi ({0})\",\n\t\t\"campi ({0})\",\n\t\t\"costanti ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"Non è possibile modificare nell\\'input di sola lettura\",\n\t\t\"Non è possibile modificare nell\\'editor di sola lettura\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"Nessun risultato.\",\n\t\t\"Si è verificato un errore sconosciuto durante la risoluzione del percorso di ridenominazione\",\n\t\t\"Ridenominazione di \\'{0}\\' in \\'{1}\\'\",\n\t\t\"Ridenominazione di {0} in {1}\",\n\t\t\"Correttamente rinominato \\'{0}\\' in \\'{1}\\'. Sommario: {2}\",\n\t\t\"La ridenominazione non è riuscita ad applicare le modifiche\",\n\t\t\"La ridenominazione non è riuscita a calcolare le modifiche\",\n\t\t\"Rinomina simbolo\",\n\t\t\"Abilita/Disabilita l\\'opzione per visualizzare le modifiche in anteprima prima della ridenominazione\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"Indica se il widget di ridenominazione input è visibile\",\n\t\t\"Consente di rinominare l\\'input. Digitare il nuovo nome e premere INVIO per eseguire il commit.\",\n\t\t\"{0} per rinominare, {1} per visualizzare in anteprima\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"Espandi selezione\",\n\t\t\"Espan&&di selezione\",\n\t\t\"Riduci selezione\",\n\t\t\"&&Riduci selezione\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"Indica se l\\'editor è quello corrente nella modalità frammenti\",\n\t\t\"Indica se è presente una tabulazione successiva in modalità frammenti\",\n\t\t\"Indica se è presente una tabulazione precedente in modalità frammenti\",\n\t\t\"Vai al segnaposto successivo...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"Domenica\",\n\t\t\"Lunedì\",\n\t\t\"Martedì\",\n\t\t\"Mercoledì\",\n\t\t\"Giovedì\",\n\t\t\"Venerdì\",\n\t\t\"Sabato\",\n\t\t\"Dom\",\n\t\t\"Lun\",\n\t\t\"Mar\",\n\t\t\"Mer\",\n\t\t\"Gio\",\n\t\t\"Ven\",\n\t\t\"Sab\",\n\t\t\"Gennaio\",\n\t\t\"Febbraio\",\n\t\t\"Marzo\",\n\t\t\"Aprile\",\n\t\t\"Mag\",\n\t\t\"Giugno\",\n\t\t\"Luglio\",\n\t\t\"Agosto\",\n\t\t\"Settembre\",\n\t\t\"Ottobre\",\n\t\t\"Novembre\",\n\t\t\"Dicembre\",\n\t\t\"Gen\",\n\t\t\"Feb\",\n\t\t\"Mar\",\n\t\t\"Apr\",\n\t\t\"Mag\",\n\t\t\"Giu\",\n\t\t\"Lug\",\n\t\t\"Ago\",\n\t\t\"Set\",\n\t\t\"Ott\",\n\t\t\"Nov\",\n\t\t\"Dic\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"Alternanza scorrimento permanente\",\n\t\t\"&&Alternanza scorrimento permanente\",\n\t\t\"Scorrimento permanente\",\n\t\t\"&&Scorrimento permanente\",\n\t\t\"Sposta stato attivo su Scorrimento permanente\",\n\t\t\"&&Sposta stato attivo su Scorrimento permanente\",\n\t\t\"Seleziona la riga di scorrimento permanente successiva\",\n\t\t\"Seleziona riga di scorrimento permanente precedente\",\n\t\t\"Vai alla linea di scorrimento permanente attiva\",\n\t\t\"Selezionare l\\'editor\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"Indica se i suggerimenti sono evidenziati\",\n\t\t\"Indica se i dettagli dei suggerimenti sono visibili\",\n\t\t\"Indica se sono presenti più suggerimenti da cui scegliere\",\n\t\t\"Indica se l\\'inserimento del suggerimento corrente comporta una modifica oppure se completa già l\\'input\",\n\t\t\"Indica se i suggerimenti vengono inseriti quando si preme INVIO\",\n\t\t\"Indica se il suggerimento corrente include il comportamento di inserimento e sostituzione\",\n\t\t\"Indica se il comportamento predefinito è quello di inserimento o sostituzione\",\n\t\t\"Indica se il suggerimento corrente supporta la risoluzione di ulteriori dettagli\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"In seguito all\\'accettazione di \\'{0}\\' sono state apportate altre {1} modifiche\",\n\t\t\"Attiva suggerimento\",\n\t\t\"Inserisci\",\n\t\t\"Inserisci\",\n\t\t\"Sostituisci\",\n\t\t\"Sostituisci\",\n\t\t\"Inserisci\",\n\t\t\"nascondi dettagli\",\n\t\t\"mostra dettagli\",\n\t\t\"Reimposta le dimensioni del widget dei suggerimenti\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"Colore di sfondo del widget dei suggerimenti.\",\n\t\t\"Colore del bordo del widget dei suggerimenti.\",\n\t\t\"Colore primo piano del widget dei suggerimenti.\",\n\t\t\"Colore primo piano della voce selezionata del widget dei suggerimenti.\",\n\t\t\"Colore primo piano dell’icona della voce selezionata del widget dei suggerimenti.\",\n\t\t\"Colore di sfondo della voce selezionata del widget dei suggerimenti.\",\n\t\t\"Colore delle evidenziazioni corrispondenze nel widget dei suggerimenti.\",\n\t\t\"Colore delle evidenziazioni corrispondenze nel widget dei suggerimenti quando lo stato attivo si trova su un elemento.\",\n\t\t\"Colore primo piano dello stato del widget dei suggerimenti.\",\n\t\t\"Caricamento...\",\n\t\t\"Non ci sono suggerimenti.\",\n\t\t\"Suggerisci\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, documenti: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"Chiudi\",\n\t\t\"Caricamento...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"Icona per visualizzare altre informazioni nel widget dei suggerimenti.\",\n\t\t\"Altre informazioni\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"Colore primo piano per i simboli di matrice. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli booleani. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di classe. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di colore. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di costante. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di costruttore. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di enumeratore. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di membro di enumeratore. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di evento. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di campo. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di file. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di cartella. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di funzione. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di interfaccia. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di chiave. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di parola chiave. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di metodo. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di modulo. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di spazio dei nomi. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli Null. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli numerici. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di oggetto. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di operatore. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di pacchetto. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di proprietà. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di riferimento. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di frammento. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di stringa. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di struct. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di testo. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di parametro di tipo. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di unità. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t\t\"Colore primo piano per i simboli di variabile. Questi simboli vengono visualizzati nella struttura, nell\\'elemento di navigazione e nel widget dei suggerimenti.\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"Attiva/Disattiva l\\'uso di TAB per spostare lo stato attivo\",\n\t\t\"Se si preme TAB, lo stato attivo verrà spostato sull\\'elemento con stato attivabile successivo.\",\n\t\t\"Se si preme TAB, verrà inserito il carattere di tabulazione\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"Sviluppatore: Forza retokenizzazione\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"Icona visualizzata con un messaggio di avviso nell\\'editor delle estensioni.\",\n\t\t\"Questo documento contiene molti caratteri Unicode ASCII non di base\",\n\t\t\"Il documento contiene molti caratteri Unicode ambigui\",\n\t\t\"Questo documento contiene molti caratteri Unicode invisibili\",\n\t\t\"Il carattere {0} potrebbe essere confuso con il carattere ASCII {1}, che è più comune nel codice sorgente.\",\n\t\t\"Il carattere {0} potrebbe essere confuso con il carattere {1}, che è più comune nel codice sorgente.\",\n\t\t\"Il carattere {0} è invisibile.\",\n\t\t\"Il carattere {0} non è un carattere ASCII di base.\",\n\t\t\"Modificare impostazioni\",\n\t\t\"Disabilita evidenziazione nei commenti\",\n\t\t\"Disabilita l\\'evidenziazione dei caratteri nei commenti\",\n\t\t\"Disabilita evidenziazione nelle stringhe\",\n\t\t\"Disabilita l\\'evidenziazione dei caratteri nelle stringhe\",\n\t\t\"Disabilitare evidenziazione ambigua\",\n\t\t\"Disabilitare l\\'evidenziazione dei caratteri ambigui\",\n\t\t\"Disabilitare evidenziazione invisibile\",\n\t\t\"Disabilitare l\\'evidenziazione dei caratteri invisibili\",\n\t\t\"Disabilitare evidenziazione non ASCII\",\n\t\t\"Disabilitare l\\'evidenziazione di caratteri ASCII non di base\",\n\t\t\"Mostrare opzioni di esclusione\",\n\t\t\"Escludere {0} (carattere invisibile) dall\\'evidenziazione\",\n\t\t\"Escludere {0} dall’essere evidenziata\",\n\t\t\"Consentire i caratteri Unicode più comuni nel linguaggio \\\"{0}\\\".\",\n\t\t\"Configurare opzioni evidenziazione Unicode\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"Caratteri di terminazione di riga insoliti\",\n\t\t\"Sono stati rilevati caratteri di terminazione di riga insoliti\",\n\t\t\"Il file \\\"\\r\\n\\\" contiene uno o più caratteri di terminazione di riga insoliti, ad esempio separatore di riga (LS) o separatore di paragrafo (PS).{0}\\r\\nÈ consigliabile rimuoverli dal file. È possibile configurare questa opzione tramite `editor.unusualLineTerminators`.\",\n\t\t\"&&Rimuovi i caratteri di terminazione di riga insoliti\",\n\t\t\"Ignora\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"Colore di sfondo di un simbolo durante l\\'accesso in lettura, ad esempio durante la lettura di una variabile. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo di un simbolo durante l\\'accesso in scrittura, ad esempio durante la scrittura in una variabile. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo di un\\'occorrenza testuale per un simbolo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del bordo di un simbolo durante l\\'accesso in lettura, ad esempio durante la lettura di una variabile.\",\n\t\t\"Colore del bordo di un simbolo durante l\\'accesso in scrittura, ad esempio durante la scrittura in una variabile.\",\n\t\t\"Colore del bordo di un\\'occorrenza testuale per un simbolo.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per le evidenziazioni dei simboli. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per le evidenziazioni dei simboli di accesso in scrittura. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del marcatore del righello delle annotazioni di un\\'occorrenza testuale per un simbolo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"Vai al prossimo simbolo evidenziato\",\n\t\t\"Vai al precedente simbolo evidenziato\",\n\t\t\"Attiva/disattiva evidenziazione simbolo\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"Elimina parola\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"Visualizza\",\n\t\t\"Guida\",\n\t\t\"Test\",\n\t\t\"FILE\",\n\t\t\"Preferenze\",\n\t\t\"Sviluppatore\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} per Applica, {1} per Anteprima\",\n\t\t\"{0} da applicare\",\n\t\t\"{0}, Motivo disabilitato: {1}\",\n\t\t\"Widget azione\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"Indica se l\\'elenco di widget azione è visibile\",\n\t\t\"Nascondi widget azione\",\n\t\t\"Seleziona azione precedente\",\n\t\t\"Seleziona azione successiva\",\n\t\t\"Accetta l\\'azione selezionata\",\n\t\t\"Anteprima azione selezionata\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"Nascondi\",\n\t\t\"Reimposta menu\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"Nascondi \\'{0}\\'\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"Errore sulla riga\",\n\t\t\"Avviso sulla riga\",\n\t\t\"Area piegata sulla linea\",\n\t\t\"Punto di interruzione sulla riga\",\n\t\t\"Suggerimento inline sulla riga\",\n\t\t\"Correzione rapida terminale\",\n\t\t\"Debugger arrestato sul punto di interruzione\",\n\t\t\"Nessun suggerimento per l\\'inlay nella riga\",\n\t\t\"Attività completata\",\n\t\t\"Attività non riuscita\",\n\t\t\"Comando terminale non riuscito\",\n\t\t\"Campanello terminale\",\n\t\t\"Cella del notebook completata\",\n\t\t\"La cella del notebook ha avuto esito negativo\",\n\t\t\"Riga diff inserita\",\n\t\t\"Riga diff eliminata\",\n\t\t\"Riga diff modificata\",\n\t\t\"Richiesta chat inviata\",\n\t\t\"Risposta chat ricevuta\",\n\t\t\"Risposta chat in sospeso\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"Override configurazione predefinita del linguaggio\",\n\t\t\"Consente di configurare le impostazioni di cui eseguire l\\'override per il linguaggio {0}.\",\n\t\t\"Consente di configurare le impostazioni dell\\'editor di cui eseguire l\\'override per un linguaggio.\",\n\t\t\"Questa impostazione non supporta la configurazione per lingua.\",\n\t\t\"Consente di configurare le impostazioni dell\\'editor di cui eseguire l\\'override per un linguaggio.\",\n\t\t\"Questa impostazione non supporta la configurazione per lingua.\",\n\t\t\"Non è possibile registrare una proprietà vuota\",\n\t\t\"Non è possibile registrare \\'{0}\\'. Corrisponde al criterio di proprietà \\'\\\\\\\\[.*\\\\\\\\]$\\' per la descrizione delle impostazioni dell\\'editor specifiche del linguaggio. Usare il contributo \\'configurationDefaults\\'.\",\n\t\t\"Non è possibile registrare \\'{0}\\'. Questa proprietà è già registrata.\",\n\t\t\"Impossibile registrare \\'{0}\\'. Il {1} dei criteri associato è già registrato con {2}.\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"Comando che restituisce informazioni sulle chiavi di contesto\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"Espressione chiave di contesto vuota\",\n\t\t\"Si è dimenticato di scrivere un\\'espressione? È anche possibile inserire \\'false\\' o \\'true\\' per restituire sempre rispettivamente false o true.\",\n\t\t\"\\'in\\' dopo \\'not\\'.\",\n\t\t\"Parentesi chiusa \\')\\'\",\n\t\t\"Token imprevisto\",\n\t\t\"Si è dimenticato di inserire && o || prima del token?\",\n\t\t\"Fine imprevista dell\\'espressione\",\n\t\t\"Si è dimenticato di inserire una chiave di contesto?\",\n\t\t\"Previsto: {0}\\r\\nRicevuto: \\'{1}\\'.\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"Indica se il sistema operativo è macOS\",\n\t\t\"Indica se il sistema operativo è Linux\",\n\t\t\"Indica se il sistema operativo è Windows\",\n\t\t\"Indica se la piattaforma è un Web browser\",\n\t\t\"Indica se il sistema operativo è macOS in una piattaforma non basata su browser\",\n\t\t\"Indica se il sistema operativo è iOS\",\n\t\t\"Indica se la piattaforma è un Web browser per dispositivi mobili\",\n\t\t\"Tipo di qualità del VS Code\",\n\t\t\"Indica se lo stato attivo della tastiera si trova all\\'interno di una casella di input\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"Si intendeva {0}?\",\n\t\t\"Si intendeva {0} o {1}?\",\n\t\t\"Si intendeva {0}, {1} o {2}?\",\n\t\t\"Si è dimenticato di aprire o chiudere la citazione?\",\n\t\t\"Si è dimenticato di eseguire il carattere di escape \\'/\\' (slash)? Inserire due barre rovesciate prima del carattere di escape, ad esempio \\'\\\\\\\\/\\'.\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"Indica se i suggerimenti sono visibili\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"È stato premuto ({0}). In attesa del secondo tasto...\",\n\t\t\"È stato premuto ({0}). In attesa del prossimo tasto...\",\n\t\t\"La combinazione di tasti ({0}, {1}) non è un comando.\",\n\t\t\"La combinazione di tasti ({0}, {1}) non è un comando.\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"Workbench\",\n\t\t\"Rappresenta il tasto \\'Control\\' in Windows e Linux e il tasto \\'Comando\\' in macOS.\",\n\t\t\"Rappresenta il tasto \\'Alt\\' in Windows e Linux e il tasto \\'Opzione\\' in macOS.\",\n\t\t\"Il modificatore da utilizzare per aggiungere un elemento di alberi e liste ad una selezione multipla con il mouse (ad esempio in Esplora Risorse, apre gli editor e le viste scm). Le gesture del mouse \\'Apri a lato\\' - se supportate - si adatteranno in modo da non creare conflitti con il modificatore di selezione multipla.\",\n\t\t\"Controlla l\\'apertura degli elementi di alberi ed elenchi tramite il mouse (se supportato). Tenere presente che alcuni alberi ed elenchi potrebbero scegliere di ignorare questa impostazione se non è applicabile.\",\n\t\t\"Controlla se elenchi e alberi supportano lo scorrimento orizzontale nell\\'area di lavoro. Avviso: l\\'attivazione di questa impostazione può influire sulle prestazioni.\",\n\t\t\"Controlla se i clic nella barra di scorrimento scorrono pagina per pagina.\",\n\t\t\"Controlla il rientro dell\\'albero in pixel.\",\n\t\t\"Controlla se l\\'albero deve eseguire il rendering delle guide per i rientri.\",\n\t\t\"Controlla se elenchi e alberi prevedono lo scorrimento uniforme.\",\n\t\t\"Moltiplicatore da usare sui valori `deltaX` e `deltaY` degli eventi di scorrimento della rotellina del mouse.\",\n\t\t\"Moltiplicatore della velocità di scorrimento quando si preme `Alt`.\",\n\t\t\"Evidenziare gli elementi durante la ricerca. L\\'ulteriore spostamento verso l\\'alto e verso il basso attraverserà solo gli elementi evidenziati.\",\n\t\t\"Filtra gli elementi durante la ricerca.\",\n\t\t\"Controlla la modalità di ricerca predefinita per elenchi e alberi nel workbench.\",\n\t\t\"Con lo stile di spostamento da tastiera simple lo stato attivo si trova sugli elementi che corrispondono all\\'input da tastiera. L\\'abbinamento viene effettuato solo in base ai prefissi.\",\n\t\t\"Con lo stile di spostamento da tastiera highlight vengono evidenziati gli elementi corrispondenti all\\'input da tastiera. Spostandosi ulteriormente verso l\\'alto o verso il basso ci si sposterà solo negli elementi evidenziati.\",\n\t\t\"Con lo stile di spostamento da tastiera filter verranno filtrati e nascosti tutti gli elementi che non corrispondono all\\'input da tastiera.\",\n\t\t\"Controlla lo stile di spostamento da tastiera per elenchi e alberi nel workbench. Le opzioni sono: simple, highlight e filter.\",\n\t\t\"In alternativa, usare \\'workbench.list.defaultFindMode\\' e \\'workbench.list.typeNavigationMode\\'.\",\n\t\t\"Usa la corrispondenza fuzzy durante la ricerca.\",\n\t\t\"Usa corrispondenza contigua durante la ricerca.\",\n\t\t\"Controlla il tipo di corrispondenza usato per la ricerca di elenchi e alberi nel workbench.\",\n\t\t\"Controlla l\\'espansione delle cartelle di alberi quando si fa clic sui nomi delle cartelle. Tenere presente che alcuni alberi ed elenchi potrebbero scegliere di ignorare questa impostazione se non è applicabile.\",\n\t\t\"Controllare il funzionamento dello spostamento dei tipi in elenchi e alberi nel workbench. Se impostato su \\'trigger\\', l\\'esplorazione del tipo inizia dopo l\\'esecuzione del comando \\'list.triggerTypeNavigation\\'.\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"Errore\",\n\t\t\"Avviso\",\n\t\t\"Info\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"usate di recente\",\n\t\t\"più usato\",\n\t\t\"altri comandi\",\n\t\t\"{0}, {1}\",\n\t\t\"Il comando \\'{0}\\' ha restituito un errore\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"Indietro\",\n\t\t\"Premere \\'INVIO\\' per confermare l\\'input oppure \\'ESC\\' per annullare\",\n\t\t\"{0}/{1}\",\n\t\t\"Digitare per ridurre il numero di risultati.\",\n\t\t\"Attivare/Disattivare tutte le caselle di controllo\",\n\t\t\"{0} risultati\",\n\t\t\"{0} selezionati\",\n\t\t\"OK\",\n\t\t\"Personalizzato\",\n\t\t\"Indietro ({0})\",\n\t\t\"Indietro\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"Input rapido\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"Fare clic per eseguire il comando \\'{0}\\'\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"Colore primo piano generale. Questo colore viene usato solo se non è sostituito da quello di un componente.\",\n\t\t\"Primo piano generale per gli elementi disabilitati. Questo colore viene usato solo e non è sostituito da quello di un componente.\",\n\t\t\"Colore primo piano globale per i messaggi di errore. Questo colore viene usato solo se non è sostituito da quello di un componente.\",\n\t\t\"Colore primo piano del testo che fornisce informazioni aggiuntive, ad esempio per un\\'etichetta di testo.\",\n\t\t\"Colore predefinito per le icone nel workbench.\",\n\t\t\"Colore del bordo globale per gli elementi evidenziati. Questo colore viene usato solo se non è sostituito da quello di un componente.\",\n\t\t\"Un bordo supplementare attorno agli elementi per contrastarli maggiormente rispetto agli altri.\",\n\t\t\"Un bordo supplementare intorno agli elementi attivi per contrastarli maggiormente rispetto agli altri.\",\n\t\t\"Il colore di sfondo delle selezioni di testo in workbench (ad esempio per i campi di input o aree di testo). Si noti che questo non si applica alle selezioni all\\'interno dell\\'editor.\",\n\t\t\"Colore dei separatori di testo.\",\n\t\t\"Colore primo piano dei link nel testo.\",\n\t\t\"Colore primo piano per i collegamenti nel testo quando vengono selezionati o al passaggio del mouse.\",\n\t\t\"Colore primo piano dei segmenti di testo preformattato.\",\n\t\t\"Colore di sfondo per le citazioni nel testo.\",\n\t\t\"Colore del bordo per le citazioni nel testo.\",\n\t\t\"Colore di sfondo per i blocchi di codice nel testo.\",\n\t\t\"Colore ombreggiatura dei widget, ad es. Trova/Sostituisci all\\'interno dell\\'editor.\",\n\t\t\"Colore del bordo dei widget, ad es. Trova/Sostituisci all\\'interno dell\\'editor.\",\n\t\t\"Sfondo della casella di input.\",\n\t\t\"Primo piano della casella di input.\",\n\t\t\"Bordo della casella di input.\",\n\t\t\"Colore del bordo di opzioni attivate nei campi di input.\",\n\t\t\"Colore di sfondo di opzioni attivate nei campi di input.\",\n\t\t\"Colore di sfondo al passaggio del mouse delle opzioni nei campi di input.\",\n\t\t\"Colore primo piano di opzioni attivate nei campi di input.\",\n\t\t\"Colore primo piano di casella di input per il testo segnaposto.\",\n\t\t\"Colore di sfondo di convalida dell\\'input di tipo Informazione.\",\n\t\t\"Colore primo piano di convalida dell\\'input di tipo Informazione.\",\n\t\t\"Colore del bordo della convalida dell\\'input di tipo Informazione.\",\n\t\t\"Colore di sfondo di convalida dell\\'input di tipo Avviso.\",\n\t\t\"Colore primo piano di convalida dell\\'input di tipo Avviso.\",\n\t\t\"Colore del bordo della convalida dell\\'input di tipo Avviso.\",\n\t\t\"Colore di sfondo di convalida dell\\'input di tipo Errore.\",\n\t\t\"Colore primo piano di convalida dell\\'input di tipo Errore.\",\n\t\t\"Colore del bordo della convalida dell\\'input di tipo Errore.\",\n\t\t\"Sfondo dell\\'elenco a discesa.\",\n\t\t\"Sfondo dell\\'elenco a discesa.\",\n\t\t\"Primo piano dell\\'elenco a discesa.\",\n\t\t\"Bordo dell\\'elenco a discesa.\",\n\t\t\"Colore primo piano del pulsante.\",\n\t\t\"Colore del separatore pulsante.\",\n\t\t\"Colore di sfondo del pulsante.\",\n\t\t\"Colore di sfondo del pulsante al passaggio del mouse.\",\n\t\t\"Colore del bordo del pulsante.\",\n\t\t\"Colore primo piano secondario del pulsante.\",\n\t\t\"Colore di sfondo secondario del pulsante.\",\n\t\t\"Colore di sfondo secondario del pulsante al passaggio del mouse.\",\n\t\t\"Colore di sfondo del badge. I badge sono piccole etichette informative, ad esempio per mostrare il conteggio dei risultati della ricerca.\",\n\t\t\"Colore primo piano del badge. I badge sono piccole etichette informative, ad esempio per mostrare il conteggio dei risultati di una ricerca.\",\n\t\t\"Ombra della barra di scorrimento per indicare lo scorrimento della visualizzazione.\",\n\t\t\"Colore di sfondo del cursore della barra di scorrimento.\",\n\t\t\"Colore di sfondo del cursore della barra di scorrimento al passaggio del mouse.\",\n\t\t\"Colore di sfondo del cursore della barra di scorrimento quando si fa clic con il mouse.\",\n\t\t\"Colore di sfondo dell\\'indicatore di stato che può essere mostrato per operazioni a esecuzione prolungata.\",\n\t\t\"Colore di sfondo del testo dell\\'errore nell\\'editor. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore primo piano degli indicatori di errore nell\\'editor.\",\n\t\t\"Se impostato, colore delle doppie sottolineature per gli errori nell\\'editor.\",\n\t\t\"Colore di sfondo del testo dell\\'avviso nell\\'editor. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore primo piano degli indicatori di avviso nell\\'editor.\",\n\t\t\"Se impostato, colore delle doppie sottolineature per gli avvisi nell\\'editor.\",\n\t\t\"Colore di sfondo del testo delle informazioni nell\\'editor. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore primo piano degli indicatori di informazioni nell\\'editor.\",\n\t\t\"Se impostato, colore delle doppie sottolineature per i messaggi informativi nell\\'editor.\",\n\t\t\"Colore primo piano degli indicatori di suggerimento nell\\'editor.\",\n\t\t\"Se impostato, colore delle doppie sottolineature per i suggerimenti nell\\'editor.\",\n\t\t\"Colore dei bordi di ridimensionamento attivi.\",\n\t\t\"Colore di sfondo dell\\'editor.\",\n\t\t\"Colore primo piano predefinito dell\\'editor.\",\n\t\t\"Colore di sfondo dello scorrimento permanente per l\\'editor\",\n\t\t\"Colore di sfondo dello scorrimento permanente al passaggio del mouse per l\\'editor\",\n\t\t\"Colore di sfondo dei widget dell\\'editor, ad esempio Trova/Sostituisci.\",\n\t\t\"Colore primo piano dei widget dell\\'editor, ad esempio Trova/Sostituisci.\",\n\t\t\"Colore del bordo dei widget dell\\'editor. Il colore viene usato solo se il widget sceglie di avere un bordo e se il colore non è sottoposto a override da un widget.\",\n\t\t\"Colore del bordo della barra di ridimensionamento dei widget dell\\'editor. Il colore viene usato solo se il widget sceglie di avere un bordo di ridimensionamento e se il colore non è sostituito da quello di un widget.\",\n\t\t\"Colore di sfondo di Selezione rapida. Il widget Selezione rapida è il contenitore di selezioni quali il riquadro comandi.\",\n\t\t\"Colore primo piano di Selezione rapida. Il widget Selezione rapida è il contenitore di selezioni quali il riquadro comandi.\",\n\t\t\"Colore di sfondo del titolo di Selezione rapida. Il widget Selezione rapida è il contenitore di selezioni quali il riquadro comandi.\",\n\t\t\"Colore di selezione rapida per il raggruppamento delle etichette.\",\n\t\t\"Colore di selezione rapida per il raggruppamento dei bordi.\",\n\t\t\"Colore di sfondo dell\\'etichetta del tasto di scelta rapida. L\\'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.\",\n\t\t\"Colore primo piano dell\\'etichetta del tasto di scelta rapida. L\\'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.\",\n\t\t\"Colore del bordo dell\\'etichetta del tasto di scelta rapida. L\\'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.\",\n\t\t\"Colore inferiore del bordo dell\\'etichetta del tasto di scelta rapida. L\\'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.\",\n\t\t\"Colore della selezione dell\\'editor.\",\n\t\t\"Colore del testo selezionato per il contrasto elevato.\",\n\t\t\"Colore della selezione in un editor inattivo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore delle aree con lo stesso contenuto della selezione. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del bordo delle regioni con lo stesso contenuto della selezione.\",\n\t\t\"Colore della corrispondenza di ricerca corrente.\",\n\t\t\"Colore degli altri risultati della ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore dell\\'intervallo di limite della ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del bordo della corrispondenza della ricerca corrente.\",\n\t\t\"Colore del bordo delle altre corrispondenze della ricerca.\",\n\t\t\"Colore del bordo dell\\'intervallo che limita la ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore delle corrispondenze query dell\\'editor della ricerca.\",\n\t\t\"Colore del bordo delle corrispondenze query dell\\'editor della ricerca.\",\n\t\t\"Colore del testo nel messaggio di completamento del viewlet di ricerca.\",\n\t\t\"Evidenziazione sotto la parola per cui è visualizzata un\\'area sensibile al passaggio del mouse. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo dell\\'area sensibile al passaggio del mouse dell\\'editor.\",\n\t\t\"Colore primo piano dell\\'area sensibile al passaggio del mouse dell\\'editor.\",\n\t\t\"Colore del bordo dell\\'area sensibile al passaggio del mouse dell\\'editor.\",\n\t\t\"Colore di sfondo della barra di stato sensibile al passaggio del mouse dell\\'editor.\",\n\t\t\"Colore dei collegamenti attivi.\",\n\t\t\"Colore primo piano dei suggerimenti inline\",\n\t\t\"Colore di sfondo dei suggerimenti inline\",\n\t\t\"Colore primo piano dei suggerimenti inline per i tipi\",\n\t\t\"Colore di sfondo dei suggerimenti inline per i tipi\",\n\t\t\"Colore primo piano dei suggerimenti inline per i parametri\",\n\t\t\"Colore di sfondo dei suggerimenti inline per i parametri\",\n\t\t\"Colore usato per l\\'icona delle azioni con lampadina.\",\n\t\t\"Colore usato per l\\'icona delle azioni di correzione automatica con lampadina.\",\n\t\t\"Colore di sfondo per il testo che è stato inserito. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo per il testo che è stato rimosso. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo per le righe che sono state inserite. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo per le righe che sono state rimosse. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore di sfondo per il margine in cui sono state inserite le righe.\",\n\t\t\"Colore di sfondo per il margine in cui sono state rimosse le righe.\",\n\t\t\"Primo piano del righello delle annotazioni delle differenze per il contenuto inserito.\",\n\t\t\"Primo piano del righello delle annotazioni delle differenze per il contenuto rimosso.\",\n\t\t\"Colore del contorno del testo che è stato inserito.\",\n\t\t\"Colore del contorno del testo che è stato rimosso.\",\n\t\t\"Colore del bordo tra due editor di testo.\",\n\t\t\"Colore del riempimento diagonale dell\\'editor diff. Il riempimento diagonale viene usato nelle visualizzazioni diff affiancate.\",\n\t\t\"Colore di sfondo dei blocchi non modificati nell\\'editor diff.\",\n\t\t\"Colore di primo piano dei blocchi non modificati nell\\'editor diff.\",\n\t\t\"Colore di sfondo del codice non modificato nell\\'editor diff.\",\n\t\t\"Colore di sfondo dell\\'elenco/albero per l\\'elemento con lo stato attivo quando l\\'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore primo piano dell\\'elenco/albero per l\\'elemento con lo stato attivo quando l\\'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore del contorno dell\\'elenco/albero per l\\'elemento con lo stato attivo quando l\\'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore del contorno dell\\'elenco/albero per l\\'elemento con lo stato attivo quando l\\'elenco/albero è attivo e selezionato. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore di sfondo dell\\'elenco/albero per l\\'elemento selezionato quando l\\'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore primo piano dell\\'elenco/albero per l\\'elemento selezionato quando l\\'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore primo piano dell’icona dell\\'elenco/albero per l\\'elemento selezionato quando l\\'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore di sfondo dell\\'elenco/albero per l\\'elemento selezionato quando l\\'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore primo piano dell\\'elenco/albero per l\\'elemento selezionato quando l\\'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore primo piano dell’icona dell\\'elenco/albero per l\\'elemento selezionato quando l\\'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Colore di sfondo dell\\'elenco/albero per l\\'elemento con lo stato attivo quando l\\'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, uno inattivo no.\",\n\t\t\"Colore del contorno dell\\'elenco/albero per l\\'elemento con lo stato attivo quando l\\'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.\",\n\t\t\"Sfondo dell\\'elenco/albero al passaggio del mouse sugli elementi.\",\n\t\t\"Primo piano dell\\'elenco/albero al passaggio del mouse sugli elementi.\",\n\t\t\"Sfondo dell\\'elenco/albero durante il trascinamento degli elementi selezionati.\",\n\t\t\"Colore primo piano Elenco/Struttura ad albero delle occorrenze trovate durante la ricerca nell\\'Elenco/Struttura ad albero.\",\n\t\t\"Colore primo piano Elenco/Struttura ad albero delle occorrenze trovate in elementi con lo stato attivo durante la ricerca nell\\'Elenco/Struttura ad albero.\",\n\t\t\"Colore primo piano dell\\'elenco/albero delle occorrenze trovate durante la ricerca nell\\'elenco/albero.\",\n\t\t\"Colore primo piano delle voci di elenco contenenti errori.\",\n\t\t\"Colore primo piano delle voci di elenco contenenti avvisi.\",\n\t\t\"Colore di sfondo del widget del filtro per tipo in elenchi e alberi.\",\n\t\t\"Colore del contorno del widget del filtro per tipo in elenchi e alberi.\",\n\t\t\"Colore del contorno del widget del filtro per tipo in elenchi e alberi quando non sono presenti corrispondenze.\",\n\t\t\"Colore ombreggiatura del widget del filtro sul tipo negli elenchi e alberi.\",\n\t\t\"Colore di sfondo della corrispondenza filtrata.\",\n\t\t\"Colore del bordo della corrispondenza filtrata.\",\n\t\t\"Colore del tratto dell\\'albero per le guide per i rientri.\",\n\t\t\"Colore del tratto dell\\'albero per le guide di rientro non attive.\",\n\t\t\"Colore del bordo della tabella tra le colonne.\",\n\t\t\"Colore di sfondo per le righe di tabella dispari.\",\n\t\t\"Colore primo piano dell\\'elenco/albero per gli elementi non evidenziati.\",\n\t\t\"Colore di sfondo del widget della casella di controllo.\",\n\t\t\"Colore di sfondo del widget della casella di controllo quando è selezionato l\\'elemento in cui si trova.\",\n\t\t\"Colore primo piano del widget della casella di controllo.\",\n\t\t\"Colore del bordo del widget della casella di controllo.\",\n\t\t\"Colore del bordo del widget della casella di controllo quando è selezionato l\\'elemento in cui si trova.\",\n\t\t\"In alternativa, usare quickInputList.focusBackground\",\n\t\t\"Colore primo piano di Selezione rapida per l\\'elemento con lo stato attivo.\",\n\t\t\"Colore primo piano dell’icona di Selezione rapida per l\\'elemento con lo stato attivo.\",\n\t\t\"Colore di sfondo di Selezione rapida per l\\'elemento con lo stato attivo.\",\n\t\t\"Colore del bordo del menu.\",\n\t\t\"Colore primo piano delle voci di menu.\",\n\t\t\"Colore di sfondo delle voci di menu.\",\n\t\t\"Colore primo piano della voce di menu selezionata nei menu.\",\n\t\t\"Colore di sfondo della voce di menu selezionata nei menu.\",\n\t\t\"Colore del bordo della voce di menu selezionata nei menu.\",\n\t\t\"Colore di un elemento separatore delle voci di menu.\",\n\t\t\"Sfondo della barra degli strumenti al passaggio del mouse sulle azioni\",\n\t\t\"Contorno della barra degli strumenti al passaggio del mouse sulle azioni\",\n\t\t\"Sfondo della barra degli strumenti quando si tiene premuto il mouse sulle azioni\",\n\t\t\"Colore di sfondo dell\\'evidenziazione della tabulazione di un frammento.\",\n\t\t\"Colore del bordo dell\\'evidenziazione della tabulazione di un frammento.\",\n\t\t\"Colore di sfondo dell\\'evidenziazione della tabulazione finale di un frammento.\",\n\t\t\"Colore del bordo dell\\'evidenziazione della tabulazione finale di un frammento.\",\n\t\t\"Colore degli elementi di navigazione in evidenza.\",\n\t\t\"Colore di sfondo degli elementi di navigazione.\",\n\t\t\"Colore degli elementi di navigazione in evidenza.\",\n\t\t\"Colore degli elementi di navigazione selezionati.\",\n\t\t\"Colore di sfondo del controllo di selezione elementi di navigazione.\",\n\t\t\"Sfondo dell\\'intestazione delle modifiche correnti nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Sfondo del contenuto delle modifiche correnti nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Sfondo dell\\'intestazione delle modifiche in ingresso nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Sfondo del contenuto delle modifiche in ingresso nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Sfondo dell\\'intestazione del predecessore comune nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Sfondo del contenuto del predecessore comune nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del bordo nelle intestazioni e sulla barra di divisione di conflitti di merge in linea.\",\n\t\t\"Colore primo piano del righello delle annotazioni delle modifiche correnti per i conflitti di merge inline.\",\n\t\t\"Colore primo piano del righello delle annotazioni delle modifiche in ingresso per i conflitti di merge inline.\",\n\t\t\"Colore primo piano del righello delle annotazioni del predecessore comune per i conflitti di merge inline.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per la ricerca di corrispondenze. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del marcatore del righello delle annotazioni per le evidenziazioni delle selezioni. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.\",\n\t\t\"Colore del marcatore della minimappa per la ricerca delle corrispondenze.\",\n\t\t\"Colore del marcatore della minimappa per le selezioni ripetute dell\\'editor.\",\n\t\t\"Colore del marcatore della minimappa per la selezione dell\\'editor.\",\n\t\t\"Colore del marcatore della minimappa per gli errori.\",\n\t\t\"Colore del marcatore della minimappa per gli avvisi.\",\n\t\t\"Colore di sfondo della minimappa.\",\n\t\t\"Opacità degli elementi in primo piano di cui è stato eseguito il rendering nella minimappa. Ad esempio, con \\\"#000000c0\\\" il rendering degli elementi verrà eseguito con il 75% di opacità.\",\n\t\t\"Colore di sfondo del dispositivo di scorrimento della minimappa.\",\n\t\t\"Colore di sfondo del dispositivo di scorrimento della minimappa al passaggio del mouse.\",\n\t\t\"Colore di sfondo del dispositivo di scorrimento della minimappa quando si fa clic con il mouse.\",\n\t\t\"Colore usato per l\\'icona di errore dei problemi.\",\n\t\t\"Colore usato per l\\'icona di avviso dei problemi.\",\n\t\t\"Colore usato per l\\'icona informazioni dei problemi.\",\n\t\t\"Colore primo piano usato nei grafici.\",\n\t\t\"Colore usato per le linee orizzontali nei grafici.\",\n\t\t\"Colore rosso usato nelle visualizzazioni grafico.\",\n\t\t\"Colore blu usato nelle visualizzazioni grafico.\",\n\t\t\"Colore giallo usato nelle visualizzazioni grafico.\",\n\t\t\"Colore arancione usato nelle visualizzazioni grafico.\",\n\t\t\"Colore verde usato nelle visualizzazioni grafico.\",\n\t\t\"Colore viola usato nelle visualizzazioni grafico.\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"ID del tipo di carattere da usare. Se non è impostato, viene usato il tipo di carattere definito per primo.\",\n\t\t\"Tipo di carattere associato alla definizione di icona.\",\n\t\t\"Icona dell\\'azione di chiusura nei widget.\",\n\t\t\"Icona per la posizione di Vai a editor precedente.\",\n\t\t\"Icona per la posizione di Vai a editor successivo.\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"I file seguenti sono stati chiusi e modificati nel disco: {0}.\",\n\t\t\"I file seguenti sono stati modificati in modo incompatibile: {0}.\",\n\t\t\"Non è stato possibile annullare \\'{0}\\' in tutti i file. {1}\",\n\t\t\"Non è stato possibile annullare \\'{0}\\' in tutti i file. {1}\",\n\t\t\"Non è stato possibile annullare \\'{0}\\' in tutti i file perché sono state apportate modifiche a {1}\",\n\t\t\"Non è stato possibile annullare \\'{0}\\' su tutti i file perché è già in esecuzione un\\'operazione di annullamento o ripetizione su {1}\",\n\t\t\"Non è stato possibile annullare \\'{0}\\' su tutti i file perché nel frattempo è stata eseguita un\\'operazione di annullamento o ripetizione\",\n\t\t\"Annullare \\'{0}\\' in tutti i file?\",\n\t\t\"&&Annulla in {0} file\",\n\t\t\"Annulla questo &&file\",\n\t\t\"Non è stato possibile annullare \\'{0}\\' perché è già in esecuzione un\\'operazione di annullamento o ripetizione.\",\n\t\t\"Annullare \\'{0}\\'?\",\n\t\t\"&&Sì\",\n\t\t\"No\",\n\t\t\"Non è stato possibile ripetere \\'{0}\\' in tutti i file. {1}\",\n\t\t\"Non è stato possibile ripetere \\'{0}\\' in tutti i file. {1}\",\n\t\t\"Non è stato possibile ripetere \\'{0}\\' in tutti i file perché sono state apportate modifiche a {1}\",\n\t\t\"Non è stato possibile ripetere l\\'operazione \\'{0}\\' su tutti i file perché è già in esecuzione un\\'operazione di annullamento o ripetizione sull\\'elenco di file {1}\",\n\t\t\"Non è stato possibile ripetere \\'{0}\\' su tutti i file perché nel frattempo è stata eseguita un\\'operazione di annullamento o ripetizione\",\n\t\t\"Non è stato possibile ripetere \\'{0}\\' perché è già in esecuzione un\\'operazione di annullamento o ripetizione.\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Area di lavoro del codice\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,OACD,EACA,gDAAiD,CAChD,sBACA,gBACA,0BACD,EACA,4CAA6C,CAC5C,QACA,8BACD,EACA,8CAA+C,CAC9C,gBACD,EACA,uCAAwC,CACvC,cACA,cACA,YACA,oBACA,kBACD,EACA,qDAAsD,CACrD,eACD,EACA,+CAAgD,CAC/C,sBACD,EACA,qCAAsC,CACrC,iBACD,EACA,uCAAwC,CACvC,SACA,uBACA,wBACA,0BACA,0BACA,SACA,kCACD,EACA,yBAA0B,CACzB,SACD,EACA,8BAA+B,CAC9B,WACA,gDACA,+EACA,+EACA,6BACA,8EACD,EACA,kCAAmC,CAClC,OACA,SACA,MACA,UACA,OACA,SACA,MACA,QACA,OACA,SACA,UACA,UACA,OACA,SACA,MACA,UACA,OACA,SACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,SACA,mDACA,uGACA,oSACA,4NACD,EACA,iCAAkC,CACjC,mEACA,mEACA,2BACD,EACA,qCAAsC,CACrC,YACA,iBACA,WACA,SACA,oBACA,iBACD,EACA,4CAA6C,CAC5C,0QACA,gCACD,EACA,mDAAoD,CACnD,wCACA,iCACA,6CACA,gCACD,EACA,4CAA6C,CAC5C,wDACA,qDACA,6CACA,0EACA,qCACD,EACA,kEAAmE,CAClE,mEACA,iEACA,gEACA,SACA,oFACA,0BACA,oBACA,uBACA,2EACA,QACA,8BACA,6CACA,4BACA,0BACD,EACA,oDAAqD,CACpD,0DACD,EACA,yDAA0D,CACzD,wDACA,qDACA,qCACD,EACA,+DAAgE,CAC/D,2DACD,EACA,yEAA0E,CACzE,2BACA,0BACA,yBACA,wBACA,gCACA,8BACA,4BACD,EACA,6DAA8D,CAC7D,6BACD,EACA,sCAAuC,CACtC,8CACA,4CACA,2CACA,SACA,0BACA,oBACA,uBACA,2EACA,QACA,8BACA,6CACA,4BACA,0BACD,EACA,4CAA6C,CAC5C,2BACA,0BACA,yBACA,wBACA,gCACA,8BACA,6BACA,gCACA,6BACD,EACA,oDAAqD,CACpD,SACA,sJACA,yNACA,8IACA,sHACA,qDACA,oIACA,qFACA,+CACA,qEACA,iDACA,mFACA,qEACA,wEACA,uIACA,0GACA,gHACA,qFACA,0FACA,sFACA,+KACA,+EACA,gEACA,gEACA,sIACA,gEACA,gEACA,0HACA,gGACA,4EACA,wHACA,kGACA,0FACA,6CACA,oEACA,2GACA,yDACA,oCACA,sCACA,kGACA,yHACA,2EACA,gHACD,EACA,wCAAyC,CACxC,gHACA,wEACA,iFACA,0IACA,sFACA,qIACA,qGACA,8GACA,qEACA,mHACA,mEACA,0GACA,iGACA,mEACA,8GACA,0FACA,oGACA,kMACA,+IACA,8MACA,2JACA,+OACA,8MACA,gLACA,wOACA,kDACA,4FACA,oHACA,kFACA,0EACA,uFACA,gMACA,kHACA,0HACA,iHACA,mHACA,+GACA,4GACA,oHACA,2GACA,6GACA,yGACA,iEACA,4FACA,mIACA,wFACA,+PACA,2MACA,+LACA,wDACA,gGACA,+DACA,iRACA,kDACA,mCACA,qHACA,uHACA,sCACA,oOACA,iHACA,0EACA;AAAA;AAAA;AAAA,yEACA,+CACA,4DACA,yGACA,mIACA,8HACA,2CACA,kEACA,gFACA,2DACA,iGACA,iHACA,yFACA,yFACA,qGACA,gIACA,qFACA,iEACA,0CACA,yDACA,yDACA,oEACA,uaACA,6CACA,8DACA,2FACA,uDACA,mDACA,oIACA,yCACA,0MACA,6EACA,6DACA,6DACA,mEACA,+EACA,+DACA,+DACA,qEACA,kDACA,kDACA,yHACA,mMACA,wFACA,oLACA,+FACA,mGACA,gEACA,2FACA,+EACA,wHACA,uHACA,gEACA,mPACA,wJACA,yEACA,mDACA,qFACA,sDACA,yEACA,2FACA,uEACA,2DACA,qFACA,yEACA,4EACA,wCACA,kGACA,8CACA,sEACA,+EACA,yEACA,oMACA,+FACA,yFACA,2IACA,oFACA,qFACA,6FACA,yFACA,+WACA,oEACA,gEACA,qGACA,oFACA,uHACA,mGACA,sJACA,6EACA,+EACA,kFACA,iFACA,kUACA,4EACA,+EACA,4EACA,6EACA,gFACA,6EACA,+EACA,4EACA,+EACA,2EACA,4EACA,+EACA,2EACA,iFACA,8EACA,2EACA,4EACA,2EACA,gFACA,kFACA,6EACA,oFACA,8EACA,2EACA,6EACA,gFACA,8FACA,yEACA,wEACA,+EACA,+EACA,uDACA,uIACA,8JACA,sFACA,8GACA,sEACA,sKACA,iHACA,2IACA,gNACA,oFACA,6MACA,uXACA,mBACA,yGACA,+FACA,yGACA,sGACA,iHACA,yHACA,4GACA,wEACA,gGACA,0GACA,+FACA,uDACA,uDACA,0GACA,+KACA,+OACA,kJACA,0IACA,qEACA,qEACA,wHACA,+JACA,6CACA,mDACA,iJACA,4HACA,qIACA,gHACA,2GACA,8GACA,0IACA,iEACA,kFACA,mDACA,0DACA,sHACA,2DACA,iFACA,kCACA,qLACA,kGACA,mDACA,mEACA,6FACA,2GACA,+CACA,4DACA,wCACA,+FACA,yEACA,oEACA,mHACA,oDACA,uEACA,kFACA,iFACA,qLACA,+FACA,sCACA,8LACA,qFACA,kJACA,+EACA,mDACA,gLACA,kFACA,gDACA,gHACA,uGACA,2CACA,mFACA,+EACA,+SACA,mDACA,0CACA,2HACA,6GACA,6EACA,qFACA,gEACA,gEACA,kGACA,6GACA,kGACA,0EACA,2DACA,+EACA,wGACA,sEACA,kGACA,oIACA,kGACA,gFACA,iEACA,2FACA,iEACA,kGACA,yDACA,qLACA,sEACA,4FACA,wCACA,sGACA,8GACA,qGACA,iDACA,4CACA,0EACA,uEACA,2EACA,6CACA,8GACA,4EACA,8GACA,8HACA,uHACA,wDACA,wMACA,6JACA,qHACA,uHACA,gEACA,4JACA,gEACA,gFACA,iEACA,wEACA,sFACA,sEACA,uDACA,mKACA,uGACA,yHACA,oEACA,2GACA,2GACA,iJACA,sDACA,+IACA,oHACA,qFACD,EACA,4CAA6C,CAC5C,+EACA,8EACA,4LACA,kEACA,8MACA,6DACA,kCACA,qIACA,oDACA,yCACA,gDACA,wFACA,wDACA,oGACA,oDACA,oDACA,oDACA,oDACA,oDACA,oDACA,6DACA,6DACA,6DACA,6DACA,6DACA,6DACA,+CACA,+EACA,+CACA,qGACA,mCACA,4DACA,kDACA,mDACA,mDACA,+DACA,oHACA,+EACA,kUACA,mDACA,qDACA,mDACA,oLACA,sEACA,sEACA,0FACA,6HACA,6HACA,6HACA,6HACA,6HACA,6HACA,oDACA,kIACA,kIACA,kIACA,kIACA,kIACA,kIACA,gIACA,gIACA,gIACA,gIACA,gIACA,gIACA,mEACA,6DACD,EACA,qCAAsC,CACrC,2EACA,6HACA,+EACA,0CACA,4CACA,wDACA,mEACA,oDACA,kDACA,mDACA,sFACA,iEACA,6EACA,kDACA,oDACA,yDACA,4DACA,+EACA,oCACA,qEACA,6DACA,wDACA,2DACA,6DACA,+DACA,mEACA,kEACA,wEACA,oEACA,2DACA,+DACA,qEACA,mEACA,uEACA,oFACA,6EACA,yFACD,EACA,6BAA8B,CAC7B,UACA,kBACA,SACA,WACA,cACA,eACA,yBACA,SACA,QACA,OACA,WACA,cACA,SACA,SACA,SACA,kBACA,OACA,SACA,UACA,YACA,YACA,eACA,UACA,SACA,oBACA,YACA,WACD,EACA,2CAA4C,CAC3C,eACD,EACA,mCAAoC,CACnC,aACD,EACA,qCAAsC,CACrC,gCACA,wBACA,4CACA,mBACA,2BACA,oBACA,kCACA,mBACA,qDACA,4CACA,sCACD,EACA,+CAAgD,CAC/C,yBACA,eACD,EACA,sDAAuD,CACtD,6BACA,8CACA,qCACA,oCACA,oCACA,oCACD,EACA,4DAA6D,CAC5D,6FACA,4BACA,gCACA,2BACA,6BACD,EACA,4DAA6D,CAC5D,sCACA,mCACD,EACA,sDAAuD,CACtD,kBACD,EACA,gDAAiD,CAChD,WACA,SACA,SACA,SACA,UACA,QACA,QACA,QACA,iBACA,iBACA,YACA,YACA,YACA,YACA,UACA,UACA,UACA,mCACD,EACA,kDAAmD,CAClD,wFACD,EACA,0DAA2D,CAC1D,uCACA,2DACA,oDACA,6DACA,6CACA,yEACA,uBACA,gCACA,yDACA,+CACA,+CACA,gCACA,0BACA,uDACA,6CACA,6CACA,8BACA,oBACA,0DACA,gDACA,gDACA,iCACA,mBACA,mDACA,iBACA,oDACA,2BACA,6CACD,EACA,+DAAgE,CAC/D,yFACD,EACA,4DAA6D,CAC5D,iCACA,8BACD,EACA,sDAAuD,CACtD,kBACA,uBACA,YACA,YACA,cACA,YACA,mBACA,sBACD,EACA,uDAAwD,CACvD,sEACA,6BACA,sBACD,EACA,wDAAyD,CACxD,iDACD,EACA,0DAA2D,CAC1D,wEACA,wCACD,EACA,qEAAsE,CACrE,+DACA,iEACA,iCACA,+CACD,EACA,4CAA6C,CAC5C,wCACA,0CACA,gCACA,+BACA,0CACA,2CACD,EACA,oDAAqD,CACpD,YACA,iCACA,uBACA,gBACA,cACA,SACA,6BACA,sBACA,SACA,wCACD,EACA,kDAAmD,CAClD,oBACA,kBACD,EACA,kEAAmE,CAClE,kBACA,8IACD,EACA,gEAAiE,CAChE,iEACA,uCACA,sEACA,2BACA,yCACD,EACA,6DAA8D,CAC7D,cACA,yBACA,iBACA,iBACA,oBACA,oBACA,6BACA,4BACD,EACA,qEAAsE,CACrE,qDACA,gCACA,6DACD,EACA,+DAAgE,CAC/D,yFACD,EACA,gDAAiD,CAChD,QACA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,0BACA,sBACA,mBACA,mBACA,6BACA,2DACA,8EACA,0CACA,0CACA,6BACA,6BACA,cACA,eACD,EACA,4CAA6C,CAC5C,uEACA,0EACA,wEACA,6DACA,mEACA,kEACA,kEACA,oBACA,QACA,QACA,uBACA,uBACA,wBACA,SACA,cACA,cACA,cACA,oBACA,gCACA,kHACA,aACA,mBACA,cACA,wBACA,2CACA,wBACA,sPACD,EACA,4CAA6C,CAC5C,UACA,4BACA,SACA,6BACA,2BACA,kCACA,0BACA,2BACA,qDACA,sDACA,eACA,gBACA,+BACA,+CACA,+CACA,iDACA,0CACA,uBACD,EACA,uDAAwD,CACvD,yIACA,4EACA,sEACA,wEACA,wEACA,iFACD,EACA,8CAA+C,CAC9C,uCACA,yCACA,8CACD,EACA,0CAA2C,CAC1C,8DACA,iEACA,0EACA,4EACD,EACA,iDAAkD,CACjD,qBACA,oBACD,EACA,gDAAiD,CAChD,4DACA,2CACA,4DACA,2CACA,qEACA,wBACA,qEACA,uBACD,EACA,sDAAuD,CACtD,SACA,SACA,OACA,eACA,cACA,sBACA,sBACA,6EACA,yFACA,6EACA,yFACA,6EACA,8FACA,6DACD,EACA,oDAAqD,CACpD,YACA,cACA,sDACA,4CACA,uBACA,yBACA,gCACA,yCACA,gBACA,wDACA,4BACA,sBACA,wBACA,wDACA,4BACA,0BACA,sBACA,uDACA,6CACA,+BACA,iCACA,gCACA,kBACA,mDACA,yCACA,wBACA,0BACA,6BACA,+CACA,qCACA,oBACA,sBACA,cACA,wBACA,cACA,0BACA,YACA,6BACA,aACD,EACA,qEAAsE,CACrE,6CACD,EACA,iEAAkE,CACjE,yIACA,iBACA,WACD,EACA,2DAA4D,CAC3D,kBACA,kBACA,aACD,EACA,6DAA8D,CAC7D,4BACA,mBACA,aACD,EACA,uDAAwD,CACvD,yCACA,6CACA,0CACA,4CACA,mCACA,2BACA,6BACA,iCACD,EACA,wDAAyD,CACxD,uGACA,4CACA,oBACD,EACA,wCAAyC,CACxC,yDACA,yDACA,2KACA,sDACA,6CACA,+CACA,2CACA,yCACA,oDACA,oDACA,qCACA,qCACD,EACA,2DAA4D,CAC3D,iBACA,sIACA,6KACD,EACA,yDAA0D,CACzD,sBACA,yCACA,iDACA,yCACA,sBACD,EACA,0DAA2D,CAC1D,uCACA,sCACD,EACA,oDAAqD,CACpD,4BACA,kCACA,qCACA,sCACA,mCACA,wDACA,kCACA,4BACA,6CACA,+BACA,kCACA,iCACD,EACA,uDAAwD,CACvD,gCACA,WACA,YACA,eACA,WACA,+FACA,6BACA,mBACD,EACA,uDAAwD,CACvD,0CACA,0CACA,kCACA,yDACA,iBACA,uDACA,eACA,mCACA,UACA,+BACA,wCACD,EACA,+DAAgE,CAC/D,eACD,EACA,0EAA2E,CAC1E,0CACA,qDACA,wHACA,+EACD,EACA,2EAA4E,CAC3E,mEACA,mEACA,YACA,WACA,QACD,EACA,wDAAyD,CACxD,0BACD,EACA,4DAA6D,CAC5D,wBACA,0BACA,yBACA,2BACA,oBACA,sBACA,yBACA,2BACA,0BACA,4BACA,8BACA,gCACA,0BACA,6BACA,eACA,iCACA,gCACA,0BACA,0BACA,2BACA,yBACA,eACA,wCACA,wBACA,wBACA,wCACA,0BACA,0BACA,2BACD,EACA,wDAAyD,CACxD,2BACA,oFACD,EACA,wCAAyC,CACxC,gGACA,uFACA,oBACA,yBACA,WACA,YACA,eACA,WACA,wBACA,mBACD,EACA,sDAAuD,CACtD,+DACD,EACA,oDAAqD,CACpD,wBACA,wBACA,yBACA,2BACA,yBACA,2BACA,+BACA,iCACA,6BACA,8BACA,oDACA,mCACA,oDACA,mCACA,yDACA,yDACA,sDACA,kCACA,6BACA,6BACA,+BACA,kCACA,gDACD,EACA,0DAA2D,CAC1D,uCACD,EACA,gEAAiE,CAChE,mEACA,mEACA,oBACA,gFACD,EACA,8CAA+C,CAC9C,wEACA,SACA,sEACA,kDACA,qEACA,iEACA,uEACA,uFACA,uFACA,8FACA,gGACA,0DACA,kFACA,6FACA,sGACA,yFACA,uFACD,EACA,4DAA6D,CAC5D,0DACA,kCACA,qBACA,qGACA,+EACD,EACA,8DAA+D,CAC9D,0FACA,kEACA,iDACA,kCACA,oBACA,gBACA,gBACA,qBACA,eACA,iBACA,oBACA,kBACA,eACA,eACA,eACA,kBACA,mBACA,uBACA,kBACA,0BACA,eACA,qBACA,qBACA,+BACA,iBACA,aACA,gBACA,eACA,wBACA,gBACA,eACA,cACA,gBACD,EACA,yDAA0D,CACzD,2DACA,2DACD,EACA,0CAA2C,CAC1C,oBACA,kGACA,oCACA,gCACA,yDACA,iEACA,gEACA,mBACA,qGACD,EACA,oDAAqD,CACpD,6DACA,iGACA,uDACD,EACA,oDAAqD,CACpD,oBACA,sBACA,mBACA,oBACD,EACA,uDAAwD,CACvD,sEACA,8EACA,8EACA,iCACD,EACA,qDAAsD,CACrD,WACA,YACA,aACA,eACA,aACA,aACA,SACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,UACA,WACA,QACA,SACA,MACA,SACA,SACA,SACA,YACA,UACA,WACA,WACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACD,EACA,6DAA8D,CAC7D,oCACA,sCACA,yBACA,2BACA,gDACA,kDACA,yDACA,sDACA,kDACA,sBACD,EACA,4CAA6C,CAC5C,4CACA,sDACA,+DACA,4GACA,kEACA,4FACA,mFACA,kFACD,EACA,sDAAuD,CACtD,gFACA,sBACA,YACA,YACA,cACA,cACA,YACA,oBACA,kBACA,qDACD,EACA,kDAAmD,CAClD,gDACA,gDACA,kDACA,yEACA,yFACA,uEACA,0EACA,yHACA,8DACA,iBACA,4BACA,aACA,eACA,UACA,WACA,qBACD,EACA,yDAA0D,CACzD,SACA,gBACD,EACA,0DAA2D,CAC1D,yEACA,oBACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,gKACA,8JACA,+JACA,+JACA,iKACA,oKACA,oKACA,8KACA,+JACA,8JACA,6JACA,iKACA,iKACA,oKACA,+JACA,sKACA,+JACA,+JACA,wKACA,0JACA,8JACA,gKACA,kKACA,kKACA,qKACA,oKACA,kKACA,gKACA,+JACA,8JACA,0KACA,iKACA,iKACD,EACA,kEAAmE,CAClE,6DACA,oGACA,gEACD,EACA,sDAAuD,CACtD,sCACD,EACA,kEAAmE,CAClE,8EACA,sEACA,wDACA,+DACA,mHACA,6GACA,oCACA,wDACA,0BACA,yCACA,yDACA,2CACA,2DACA,sCACA,sDACA,yCACA,yDACA,wCACA,+DACA,iCACA,2DACA,6CACA,qEACA,4CACD,EACA,0EAA2E,CAC1E,6CACA,iEACA,uRACA,yDACA,QACD,EACA,iEAAkE,CACjE,qMACA,yMACA,mJACA,+GACA,mHACA,6DACA,+KACA,uMACA,qLACD,EACA,4DAA6D,CAC5D,sCACA,wCACA,yCACD,EACA,0DAA2D,CAC1D,gBACD,EACA,mDAAoD,CACnD,aACA,QACA,OACA,OACA,aACA,cACD,EACA,8CAA+C,CAC9C,qCACA,mBACA,gCACA,eACD,EACA,gDAAiD,CAChD,oDACA,yBACA,8BACA,8BACA,+BACA,8BACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,WACA,gBACD,EACA,yCAA0C,CACzC,gBACD,EACA,gDAAiD,CAChD,oBACA,oBACA,2BACA,mCACA,iCACA,8BACA,+CACA,6CACA,yBACA,2BACA,iCACA,uBACA,gCACA,gDACA,qBACA,sBACA,uBACA,yBACA,yBACA,0BACD,EACA,yDAA0D,CACzD,qDACA,4FACA,oGACA,iEACA,oGACA,iEACA,uDACA,yNACA,mFACA,4FACD,EACA,mDAAoD,CACnD,+DACD,EACA,2CAA4C,CAC3C,uCACA,qJACA,mBACA,uBACA,mBACA,2DACA,mCACA,0DACA;AAAA,iBACD,EACA,4CAA6C,CAC5C,4CACA,4CACA,8CACA,+CACA,qFACA,0CACA,sEACA,iCACA,uFACD,EACA,wCAAyC,CACxC,oBACA,0BACA,+BACA,yDACA,sJACD,EACA,yDAA0D,CACzD,wCACD,EACA,0DAA2D,CAC1D,2DACA,4DACA,2DACA,0DACD,EACA,uCAAwC,CACvC,YACA,mFACA,+EACA,oUACA,wNACA,2KACA,6EACA,6CACA,8EACA,mEACA,gHACA,yEACA,oJACA,0CACA,sFACA,2LACA,sOACA,8IACA,iIACA,gGACA,kDACA,kDACA,8FACA,wNACA,kNACD,EACA,qCAAsC,CACrC,SACA,SACA,MACD,EACA,qDAAsD,CACrD,mBACA,eACA,gBACA,WACA,0CACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,WACA,oEACA,UACA,+CACA,qDACA,gBACA,kBACA,KACA,iBACA,iBACA,UACD,EACA,gDAAiD,CAChD,cACD,EACA,iDAAkD,CACjD,yCACD,EACA,yCAA0C,CACzC,iHACA,uIACA,yIACA,2GACA,iDACA,2IACA,kGACA,yGACA,yLACA,kCACA,yCACA,uGACA,0DACA,+CACA,+CACA,sDACA,qFACA,iFACA,iCACA,sCACA,gCACA,2DACA,2DACA,4EACA,6DACA,kEACA,iEACA,mEACA,oEACA,2DACA,6DACA,8DACA,2DACA,6DACA,8DACA,gCACA,gCACA,qCACA,+BACA,mCACA,kCACA,iCACA,wDACA,iCACA,8CACA,4CACA,mEACA,4IACA,+IACA,sFACA,2DACA,kFACA,0FACA,+GACA,4IACA,6DACA,+EACA,4IACA,6DACA,+EACA,mJACA,mEACA,2FACA,mEACA,mFACA,gDACA,gCACA,8CACA,6DACA,oFACA,yEACA,2EACA,yKACA,8NACA,+HACA,iIACA,0IACA,oEACA,8DACA,kKACA,oKACA,kKACA,4KACA,sCACA,yDACA,sIACA,mJACA,0EACA,mDACA,oIACA,wIACA,gEACA,6DACA,gJACA,+DACA,yEACA,0EACA,2LACA,2EACA,6EACA,2EACA,sFACA,kCACA,6CACA,2CACA,wDACA,sDACA,6DACA,2DACA,uDACA,gFACA,+IACA,8IACA,+IACA,8IACA,uEACA,sEACA,yFACA,wFACA,yDACA,wDACA,4CACA,iIACA,gEACA,qEACA,+DACA,sMACA,wMACA,yMACA,uNACA,8LACA,gMACA,gNACA,gMACA,kMACA,kNACA,2LACA,2MACA,mEACA,wEACA,iFACA,6HACA,6JACA,wGACA,6DACA,6DACA,uEACA,0EACA,kHACA,8EACA,kDACA,kDACA,4DACA,oEACA,iDACA,oDACA,0EACA,0DACA,6GACA,4DACA,0DACA,6GACA,uDACA,6EACA,6FACA,2EACA,6BACA,yCACA,uCACA,8DACA,4DACA,4DACA,uDACA,yEACA,2EACA,mFACA,0EACA,0EACA,iFACA,iFACA,oDACA,kDACA,oDACA,oDACA,uEACA,yKACA,qKACA,4KACA,wKACA,wKACA,oKACA,iGACA,8GACA,iHACA,6GACA,8KACA,mLACA,4EACA,8EACA,qEACA,uDACA,uDACA,oCACA,wMACA,mEACA,0FACA,kGACA,mDACA,mDACA,sDACA,wCACA,qDACA,oDACA,kDACA,qDACA,wDACA,oDACA,mDACD,EACA,wCAAyC,CACxC,iHACA,yDACA,4CACA,qDACA,oDACD,EACA,8CAA+C,CAC9C,iEACA,oEACA,gEACA,gEACA,0GACA,kJACA,mJACA,mCACA,wBACA,wBACA,4HACA,mBACA,UACA,KACA,+DACA,+DACA,yGACA,+KACA,kJACA,0HACD,EACA,yCAA0C,CACzC,2BACD,CACD,CAAC", "names": [], "file": "editor.main.nls.it.js"}