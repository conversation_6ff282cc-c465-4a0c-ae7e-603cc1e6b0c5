!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("Diff2Html",[],t):"object"==typeof exports?exports.Diff2Html=t():e.Diff2Html=t()}(this,(()=>{return e={696:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.convertChangesToDMP=function(e){for(var t,n,i=[],r=0;r<e.length;r++)n=(t=e[r]).added?1:t.removed?-1:0,i.push([n,t.value]);return i}},826:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.convertChangesToXML=function(e){for(var t=[],n=0;n<e.length;n++){var i=e[n];i.added?t.push("<ins>"):i.removed&&t.push("<del>"),t.push(i.value.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")),i.added?t.push("</ins>"):i.removed&&t.push("</del>")}return t.join("")}},976:(e,t,n)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.diffArrays=function(e,t,n){return r.diff(e,t,n)},t.arrayDiff=void 0;var r=new(((i=n(913))&&i.__esModule?i:{default:i}).default);t.arrayDiff=r,r.tokenize=function(e){return e.slice()},r.join=r.removeEmpty=function(e){return e}},913:(e,t)=>{"use strict";function n(){}function i(e,t,n,i,r){for(var a=0,o=t.length,s=0,l=0;a<o;a++){var f=t[a];if(f.removed){if(f.value=e.join(i.slice(l,l+f.count)),l+=f.count,a&&t[a-1].added){var u=t[a-1];t[a-1]=t[a],t[a]=u}}else{if(!f.added&&r){var d=n.slice(s,s+f.count);d=d.map((function(e,t){var n=i[l+t];return n.length>e.length?n:e})),f.value=e.join(d)}else f.value=e.join(n.slice(s,s+f.count));s+=f.count,f.added||(l+=f.count)}}var c=t[o-1];return o>1&&"string"==typeof c.value&&(c.added||c.removed)&&e.equals("",c.value)&&(t[o-2].value+=c.value,t.pop()),t}function r(e){return{newPos:e.newPos,components:e.components.slice(0)}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,n.prototype={diff:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=n.callback;"function"==typeof n&&(a=n,n={}),this.options=n;var o=this;function s(e){return a?(setTimeout((function(){a(void 0,e)}),0),!0):e}e=this.castInput(e),t=this.castInput(t),e=this.removeEmpty(this.tokenize(e));var l=(t=this.removeEmpty(this.tokenize(t))).length,f=e.length,u=1,d=l+f;n.maxEditLength&&(d=Math.min(d,n.maxEditLength));var c=[{newPos:-1,components:[]}],p=this.extractCommon(c[0],t,e,0);if(c[0].newPos+1>=l&&p+1>=f)return s([{value:this.join(t),count:t.length}]);function h(){for(var n=-1*u;n<=u;n+=2){var a=void 0,d=c[n-1],p=c[n+1],h=(p?p.newPos:0)-n;d&&(c[n-1]=void 0);var b=d&&d.newPos+1<l,g=p&&0<=h&&h<f;if(b||g){if(!b||g&&d.newPos<p.newPos?(a=r(p),o.pushComponent(a.components,void 0,!0)):((a=d).newPos++,o.pushComponent(a.components,!0,void 0)),h=o.extractCommon(a,t,e,n),a.newPos+1>=l&&h+1>=f)return s(i(o,a.components,t,e,o.useLongestToken));c[n]=a}else c[n]=void 0}u++}if(a)!function e(){setTimeout((function(){if(u>d)return a();h()||e()}),0)}();else for(;u<=d;){var b=h();if(b)return b}},pushComponent:function(e,t,n){var i=e[e.length-1];i&&i.added===t&&i.removed===n?e[e.length-1]={count:i.count+1,added:t,removed:n}:e.push({count:1,added:t,removed:n})},extractCommon:function(e,t,n,i){for(var r=t.length,a=n.length,o=e.newPos,s=o-i,l=0;o+1<r&&s+1<a&&this.equals(t[o+1],n[s+1]);)o++,s++,l++;return l&&e.components.push({count:l}),e.newPos=o,s},equals:function(e,t){return this.options.comparator?this.options.comparator(e,t):e===t||this.options.ignoreCase&&e.toLowerCase()===t.toLowerCase()},removeEmpty:function(e){for(var t=[],n=0;n<e.length;n++)e[n]&&t.push(e[n]);return t},castInput:function(e){return e},tokenize:function(e){return e.split("")},join:function(e){return e.join("")}}},630:(e,t,n)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.diffChars=function(e,t,n){return r.diff(e,t,n)},t.characterDiff=void 0;var r=new(((i=n(913))&&i.__esModule?i:{default:i}).default);t.characterDiff=r},852:(e,t,n)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.diffCss=function(e,t,n){return r.diff(e,t,n)},t.cssDiff=void 0;var r=new(((i=n(913))&&i.__esModule?i:{default:i}).default);t.cssDiff=r,r.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)}},276:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.diffJson=function(e,t,n){return l.diff(e,t,n)},t.canonicalize=f,t.jsonDiff=void 0;var i,r=(i=n(913))&&i.__esModule?i:{default:i},a=n(187);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}var s=Object.prototype.toString,l=new r.default;function f(e,t,n,i,r){var a,l;for(t=t||[],n=n||[],i&&(e=i(r,e)),a=0;a<t.length;a+=1)if(t[a]===e)return n[a];if("[object Array]"===s.call(e)){for(t.push(e),l=new Array(e.length),n.push(l),a=0;a<e.length;a+=1)l[a]=f(e[a],t,n,i,r);return t.pop(),n.pop(),l}if(e&&e.toJSON&&(e=e.toJSON()),"object"===o(e)&&null!==e){t.push(e),l={},n.push(l);var u,d=[];for(u in e)e.hasOwnProperty(u)&&d.push(u);for(d.sort(),a=0;a<d.length;a+=1)l[u=d[a]]=f(e[u],t,n,i,u);t.pop(),n.pop()}else l=e;return l}t.jsonDiff=l,l.useLongestToken=!0,l.tokenize=a.lineDiff.tokenize,l.castInput=function(e){var t=this.options,n=t.undefinedReplacement,i=t.stringifyReplacer,r=void 0===i?function(e,t){return void 0===t?n:t}:i;return"string"==typeof e?e:JSON.stringify(f(e,null,null,r),r,"  ")},l.equals=function(e,t){return r.default.prototype.equals.call(l,e.replace(/,([\r\n])/g,"$1"),t.replace(/,([\r\n])/g,"$1"))}},187:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.diffLines=function(e,t,n){return o.diff(e,t,n)},t.diffTrimmedLines=function(e,t,n){var i=(0,a.generateOptions)(n,{ignoreWhitespace:!0});return o.diff(e,t,i)},t.lineDiff=void 0;var i,r=(i=n(913))&&i.__esModule?i:{default:i},a=n(9),o=new r.default;t.lineDiff=o,o.tokenize=function(e){var t=[],n=e.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var i=0;i<n.length;i++){var r=n[i];i%2&&!this.options.newlineIsToken?t[t.length-1]+=r:(this.options.ignoreWhitespace&&(r=r.trim()),t.push(r))}return t}},146:(e,t,n)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.diffSentences=function(e,t,n){return r.diff(e,t,n)},t.sentenceDiff=void 0;var r=new(((i=n(913))&&i.__esModule?i:{default:i}).default);t.sentenceDiff=r,r.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)}},303:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.diffWords=function(e,t,n){return n=(0,a.generateOptions)(n,{ignoreWhitespace:!0}),l.diff(e,t,n)},t.diffWordsWithSpace=function(e,t,n){return l.diff(e,t,n)},t.wordDiff=void 0;var i,r=(i=n(913))&&i.__esModule?i:{default:i},a=n(9),o=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,s=/\S/,l=new r.default;t.wordDiff=l,l.equals=function(e,t){return this.options.ignoreCase&&(e=e.toLowerCase(),t=t.toLowerCase()),e===t||this.options.ignoreWhitespace&&!s.test(e)&&!s.test(t)},l.tokenize=function(e){for(var t=e.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),n=0;n<t.length-1;n++)!t[n+1]&&t[n+2]&&o.test(t[n])&&o.test(t[n+2])&&(t[n]+=t[n+2],t.splice(n+1,2),n--);return t}},785:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Diff",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(t,"diffChars",{enumerable:!0,get:function(){return a.diffChars}}),Object.defineProperty(t,"diffWords",{enumerable:!0,get:function(){return o.diffWords}}),Object.defineProperty(t,"diffWordsWithSpace",{enumerable:!0,get:function(){return o.diffWordsWithSpace}}),Object.defineProperty(t,"diffLines",{enumerable:!0,get:function(){return s.diffLines}}),Object.defineProperty(t,"diffTrimmedLines",{enumerable:!0,get:function(){return s.diffTrimmedLines}}),Object.defineProperty(t,"diffSentences",{enumerable:!0,get:function(){return l.diffSentences}}),Object.defineProperty(t,"diffCss",{enumerable:!0,get:function(){return f.diffCss}}),Object.defineProperty(t,"diffJson",{enumerable:!0,get:function(){return u.diffJson}}),Object.defineProperty(t,"canonicalize",{enumerable:!0,get:function(){return u.canonicalize}}),Object.defineProperty(t,"diffArrays",{enumerable:!0,get:function(){return d.diffArrays}}),Object.defineProperty(t,"applyPatch",{enumerable:!0,get:function(){return c.applyPatch}}),Object.defineProperty(t,"applyPatches",{enumerable:!0,get:function(){return c.applyPatches}}),Object.defineProperty(t,"parsePatch",{enumerable:!0,get:function(){return p.parsePatch}}),Object.defineProperty(t,"merge",{enumerable:!0,get:function(){return h.merge}}),Object.defineProperty(t,"structuredPatch",{enumerable:!0,get:function(){return b.structuredPatch}}),Object.defineProperty(t,"createTwoFilesPatch",{enumerable:!0,get:function(){return b.createTwoFilesPatch}}),Object.defineProperty(t,"createPatch",{enumerable:!0,get:function(){return b.createPatch}}),Object.defineProperty(t,"convertChangesToDMP",{enumerable:!0,get:function(){return g.convertChangesToDMP}}),Object.defineProperty(t,"convertChangesToXML",{enumerable:!0,get:function(){return v.convertChangesToXML}});var i,r=(i=n(913))&&i.__esModule?i:{default:i},a=n(630),o=n(303),s=n(187),l=n(146),f=n(852),u=n(276),d=n(976),c=n(690),p=n(719),h=n(51),b=n(286),g=n(696),v=n(826)},690:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.applyPatch=o,t.applyPatches=function(e,t){"string"==typeof e&&(e=(0,r.parsePatch)(e));var n=0;!function i(){var r=e[n++];if(!r)return t.complete();t.loadFile(r,(function(e,n){if(e)return t.complete(e);var a=o(n,r,t);t.patched(r,a,(function(e){if(e)return t.complete(e);i()}))}))}()};var i,r=n(719),a=(i=n(169))&&i.__esModule?i:{default:i};function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof t&&(t=(0,r.parsePatch)(t)),Array.isArray(t)){if(t.length>1)throw new Error("applyPatch only works with a single input.");t=t[0]}var i,o,s=e.split(/\r\n|[\n\v\f\r\x85]/),l=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],f=t.hunks,u=n.compareLine||function(e,t,n,i){return t===i},d=0,c=n.fuzzFactor||0,p=0,h=0;function b(e,t){for(var n=0;n<e.lines.length;n++){var i=e.lines[n],r=i.length>0?i[0]:" ",a=i.length>0?i.substr(1):i;if(" "===r||"-"===r){if(!u(t+1,s[t],r,a)&&++d>c)return!1;t++}}return!0}for(var g=0;g<f.length;g++){for(var v=f[g],m=s.length-v.oldLines,y=0,w=h+v.oldStart-1,L=(0,a.default)(w,p,m);void 0!==y;y=L())if(b(v,w+y)){v.offset=h+=y;break}if(void 0===y)return!1;p=v.offset+v.oldStart+v.oldLines}for(var S=0,x=0;x<f.length;x++){var C=f[x],_=C.oldStart+C.offset+S-1;S+=C.newLines-C.oldLines;for(var T=0;T<C.lines.length;T++){var O=C.lines[T],N=O.length>0?O[0]:" ",P=O.length>0?O.substr(1):O,j=C.linedelimiters[T];if(" "===N)_++;else if("-"===N)s.splice(_,1),l.splice(_,1);else if("+"===N)s.splice(_,0,P),l.splice(_,0,j),_++;else if("\\"===N){var E=C.lines[T-1]?C.lines[T-1][0]:null;"+"===E?i=!0:"-"===E&&(o=!0)}}}if(i)for(;!s[s.length-1];)s.pop(),l.pop();else o&&(s.push(""),l.push("\n"));for(var M=0;M<s.length-1;M++)s[M]=s[M]+l[M];return s.join("")}},286:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.structuredPatch=o,t.formatPatch=s,t.createTwoFilesPatch=l,t.createPatch=function(e,t,n,i,r,a){return l(e,e,t,n,i,r,a)};var i=n(187);function r(e){return function(e){if(Array.isArray(e))return a(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function o(e,t,n,a,o,s,l){l||(l={}),void 0===l.context&&(l.context=4);var f=(0,i.diffLines)(n,a,l);if(f){f.push({value:"",lines:[]});for(var u=[],d=0,c=0,p=[],h=1,b=1,g=function(e){var t=f[e],i=t.lines||t.value.replace(/\n$/,"").split("\n");if(t.lines=i,t.added||t.removed){var o;if(!d){var s=f[e-1];d=h,c=b,s&&(p=l.context>0?m(s.lines.slice(-l.context)):[],d-=p.length,c-=p.length)}(o=p).push.apply(o,r(i.map((function(e){return(t.added?"+":"-")+e})))),t.added?b+=i.length:h+=i.length}else{if(d)if(i.length<=2*l.context&&e<f.length-2){var g;(g=p).push.apply(g,r(m(i)))}else{var v,y=Math.min(i.length,l.context);(v=p).push.apply(v,r(m(i.slice(0,y))));var w={oldStart:d,oldLines:h-d+y,newStart:c,newLines:b-c+y,lines:p};if(e>=f.length-2&&i.length<=l.context){var L=/\n$/.test(n),S=/\n$/.test(a),x=0==i.length&&p.length>w.oldLines;!L&&x&&n.length>0&&p.splice(w.oldLines,0,"\\ No newline at end of file"),(L||x)&&S||p.push("\\ No newline at end of file")}u.push(w),d=0,c=0,p=[]}h+=i.length,b+=i.length}},v=0;v<f.length;v++)g(v);return{oldFileName:e,newFileName:t,oldHeader:o,newHeader:s,hunks:u}}function m(e){return e.map((function(e){return" "+e}))}}function s(e){var t=[];e.oldFileName==e.newFileName&&t.push("Index: "+e.oldFileName),t.push("==================================================================="),t.push("--- "+e.oldFileName+(void 0===e.oldHeader?"":"\t"+e.oldHeader)),t.push("+++ "+e.newFileName+(void 0===e.newHeader?"":"\t"+e.newHeader));for(var n=0;n<e.hunks.length;n++){var i=e.hunks[n];0===i.oldLines&&(i.oldStart-=1),0===i.newLines&&(i.newStart-=1),t.push("@@ -"+i.oldStart+","+i.oldLines+" +"+i.newStart+","+i.newLines+" @@"),t.push.apply(t,i.lines)}return t.join("\n")+"\n"}function l(e,t,n,i,r,a,l){return s(o(e,t,n,i,r,a,l))}},51:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.calcLineCount=l,t.merge=function(e,t,n){e=f(e,n),t=f(t,n);var i={};(e.index||t.index)&&(i.index=e.index||t.index),(e.newFileName||t.newFileName)&&(u(e)?u(t)?(i.oldFileName=d(i,e.oldFileName,t.oldFileName),i.newFileName=d(i,e.newFileName,t.newFileName),i.oldHeader=d(i,e.oldHeader,t.oldHeader),i.newHeader=d(i,e.newHeader,t.newHeader)):(i.oldFileName=e.oldFileName,i.newFileName=e.newFileName,i.oldHeader=e.oldHeader,i.newHeader=e.newHeader):(i.oldFileName=t.oldFileName||e.oldFileName,i.newFileName=t.newFileName||e.newFileName,i.oldHeader=t.oldHeader||e.oldHeader,i.newHeader=t.newHeader||e.newHeader)),i.hunks=[];for(var r=0,a=0,o=0,s=0;r<e.hunks.length||a<t.hunks.length;){var l=e.hunks[r]||{oldStart:1/0},b=t.hunks[a]||{oldStart:1/0};if(c(l,b))i.hunks.push(p(l,o)),r++,s+=l.newLines-l.oldLines;else if(c(b,l))i.hunks.push(p(b,s)),a++,o+=b.newLines-b.oldLines;else{var g={oldStart:Math.min(l.oldStart,b.oldStart),oldLines:0,newStart:Math.min(l.newStart+o,b.oldStart+s),newLines:0,lines:[]};h(g,l.oldStart,l.lines,b.oldStart,b.lines),a++,r++,i.hunks.push(g)}}return i};var i=n(286),r=n(719),a=n(780);function o(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function l(e){var t=x(e.lines),n=t.oldLines,i=t.newLines;void 0!==n?e.oldLines=n:delete e.oldLines,void 0!==i?e.newLines=i:delete e.newLines}function f(e,t){if("string"==typeof e){if(/^@@/m.test(e)||/^Index:/m.test(e))return(0,r.parsePatch)(e)[0];if(!t)throw new Error("Must provide a base reference or pass in a patch");return(0,i.structuredPatch)(void 0,void 0,t,e)}return e}function u(e){return e.newFileName&&e.newFileName!==e.oldFileName}function d(e,t,n){return t===n?t:(e.conflict=!0,{mine:t,theirs:n})}function c(e,t){return e.oldStart<t.oldStart&&e.oldStart+e.oldLines<t.oldStart}function p(e,t){return{oldStart:e.oldStart,oldLines:e.oldLines,newStart:e.newStart+t,newLines:e.newLines,lines:e.lines}}function h(e,t,n,i,r){var a={offset:t,lines:n,index:0},s={offset:i,lines:r,index:0};for(m(e,a,s),m(e,s,a);a.index<a.lines.length&&s.index<s.lines.length;){var f=a.lines[a.index],u=s.lines[s.index];if("-"!==f[0]&&"+"!==f[0]||"-"!==u[0]&&"+"!==u[0])if("+"===f[0]&&" "===u[0]){var d;(d=e.lines).push.apply(d,o(w(a)))}else if("+"===u[0]&&" "===f[0]){var c;(c=e.lines).push.apply(c,o(w(s)))}else"-"===f[0]&&" "===u[0]?g(e,a,s):"-"===u[0]&&" "===f[0]?g(e,s,a,!0):f===u?(e.lines.push(f),a.index++,s.index++):v(e,w(a),w(s));else b(e,a,s)}y(e,a),y(e,s),l(e)}function b(e,t,n){var i=w(t),r=w(n);if(L(i)&&L(r)){var s,l;if((0,a.arrayStartsWith)(i,r)&&S(n,i,i.length-r.length))return void(s=e.lines).push.apply(s,o(i));if((0,a.arrayStartsWith)(r,i)&&S(t,r,r.length-i.length))return void(l=e.lines).push.apply(l,o(r))}else if((0,a.arrayEqual)(i,r)){var f;return void(f=e.lines).push.apply(f,o(i))}v(e,i,r)}function g(e,t,n,i){var r,a=w(t),s=function(e,t){for(var n=[],i=[],r=0,a=!1,o=!1;r<t.length&&e.index<e.lines.length;){var s=e.lines[e.index],l=t[r];if("+"===l[0])break;if(a=a||" "!==s[0],i.push(l),r++,"+"===s[0])for(o=!0;"+"===s[0];)n.push(s),s=e.lines[++e.index];l.substr(1)===s.substr(1)?(n.push(s),e.index++):o=!0}if("+"===(t[r]||"")[0]&&a&&(o=!0),o)return n;for(;r<t.length;)i.push(t[r++]);return{merged:i,changes:n}}(n,a);s.merged?(r=e.lines).push.apply(r,o(s.merged)):v(e,i?s:a,i?a:s)}function v(e,t,n){e.conflict=!0,e.lines.push({conflict:!0,mine:t,theirs:n})}function m(e,t,n){for(;t.offset<n.offset&&t.index<t.lines.length;){var i=t.lines[t.index++];e.lines.push(i),t.offset++}}function y(e,t){for(;t.index<t.lines.length;){var n=t.lines[t.index++];e.lines.push(n)}}function w(e){for(var t=[],n=e.lines[e.index][0];e.index<e.lines.length;){var i=e.lines[e.index];if("-"===n&&"+"===i[0]&&(n="+"),n!==i[0])break;t.push(i),e.index++}return t}function L(e){return e.reduce((function(e,t){return e&&"-"===t[0]}),!0)}function S(e,t,n){for(var i=0;i<n;i++){var r=t[t.length-n+i].substr(1);if(e.lines[e.index+i]!==" "+r)return!1}return e.index+=n,!0}function x(e){var t=0,n=0;return e.forEach((function(e){if("string"!=typeof e){var i=x(e.mine),r=x(e.theirs);void 0!==t&&(i.oldLines===r.oldLines?t+=i.oldLines:t=void 0),void 0!==n&&(i.newLines===r.newLines?n+=i.newLines:n=void 0)}else void 0===n||"+"!==e[0]&&" "!==e[0]||n++,void 0===t||"-"!==e[0]&&" "!==e[0]||t++})),{oldLines:t,newLines:n}}},719:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parsePatch=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.split(/\r\n|[\n\v\f\r\x85]/),i=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],r=[],a=0;function o(){var e={};for(r.push(e);a<n.length;){var i=n[a];if(/^(\-\-\-|\+\+\+|@@)\s/.test(i))break;var o=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(i);o&&(e.index=o[1]),a++}for(s(e),s(e),e.hunks=[];a<n.length;){var f=n[a];if(/^(Index:|diff|\-\-\-|\+\+\+)\s/.test(f))break;if(/^@@/.test(f))e.hunks.push(l());else{if(f&&t.strict)throw new Error("Unknown line "+(a+1)+" "+JSON.stringify(f));a++}}}function s(e){var t=/^(---|\+\+\+)\s+(.*)$/.exec(n[a]);if(t){var i="---"===t[1]?"old":"new",r=t[2].split("\t",2),o=r[0].replace(/\\\\/g,"\\");/^".*"$/.test(o)&&(o=o.substr(1,o.length-2)),e[i+"FileName"]=o,e[i+"Header"]=(r[1]||"").trim(),a++}}function l(){var e=a,r=n[a++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),o={oldStart:+r[1],oldLines:void 0===r[2]?1:+r[2],newStart:+r[3],newLines:void 0===r[4]?1:+r[4],lines:[],linedelimiters:[]};0===o.oldLines&&(o.oldStart+=1),0===o.newLines&&(o.newStart+=1);for(var s=0,l=0;a<n.length&&!(0===n[a].indexOf("--- ")&&a+2<n.length&&0===n[a+1].indexOf("+++ ")&&0===n[a+2].indexOf("@@"));a++){var f=0==n[a].length&&a!=n.length-1?" ":n[a][0];if("+"!==f&&"-"!==f&&" "!==f&&"\\"!==f)break;o.lines.push(n[a]),o.linedelimiters.push(i[a]||"\n"),"+"===f?s++:"-"===f?l++:" "===f&&(s++,l++)}if(s||1!==o.newLines||(o.newLines=0),l||1!==o.oldLines||(o.oldLines=0),t.strict){if(s!==o.newLines)throw new Error("Added line count did not match for hunk at line "+(e+1));if(l!==o.oldLines)throw new Error("Removed line count did not match for hunk at line "+(e+1))}return o}for(;a<n.length;)o();return r}},780:(e,t)=>{"use strict";function n(e,t){if(t.length>e.length)return!1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}Object.defineProperty(t,"__esModule",{value:!0}),t.arrayEqual=function(e,t){return e.length===t.length&&n(e,t)},t.arrayStartsWith=n},169:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var i=!0,r=!1,a=!1,o=1;return function s(){if(i&&!a){if(r?o++:i=!1,e+o<=n)return o;a=!0}if(!r)return a||(i=!0),t<=e-o?-o++:(r=!0,s())}}},9:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateOptions=function(e,t){if("function"==typeof e)t.callback=e;else if(e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},397:(e,t)=>{!function(e){var t=/\S/,n=/\"/g,i=/\n/g,r=/\r/g,a=/\\/g,o=/\u2028/,s=/\u2029/;function l(e){return e.trim?e.trim():e.replace(/^\s*|\s*$/g,"")}function f(e,t,n){if(t.charAt(n)!=e.charAt(0))return!1;for(var i=1,r=e.length;i<r;i++)if(t.charAt(n+i)!=e.charAt(i))return!1;return!0}e.tags={"#":1,"^":2,"<":3,$:4,"/":5,"!":6,">":7,"=":8,_v:9,"{":10,"&":11,_t:12},e.scan=function(n,i){var r,a=n.length,o=0,s=null,u=null,d="",c=[],p=!1,h=0,b=0,g="{{",v="}}";function m(){d.length>0&&(c.push({tag:"_t",text:new String(d)}),d="")}function y(n,i){if(m(),n&&function(){for(var n=!0,i=b;i<c.length;i++)if(!(n=e.tags[c[i].tag]<e.tags._v||"_t"==c[i].tag&&null===c[i].text.match(t)))return!1;return n}())for(var r,a=b;a<c.length;a++)c[a].text&&((r=c[a+1])&&">"==r.tag&&(r.indent=c[a].text.toString()),c.splice(a,1));else i||c.push({tag:"\n"});p=!1,b=c.length}function w(e,t){var n="="+v,i=e.indexOf(n,t),r=l(e.substring(e.indexOf("=",t)+1,i)).split(" ");return g=r[0],v=r[r.length-1],i+n.length-1}for(i&&(i=i.split(" "),g=i[0],v=i[1]),h=0;h<a;h++)0==o?f(g,n,h)?(--h,m(),o=1):"\n"==n.charAt(h)?y(p):d+=n.charAt(h):1==o?(h+=g.length-1,"="==(s=(u=e.tags[n.charAt(h+1)])?n.charAt(h+1):"_v")?(h=w(n,h),o=0):(u&&h++,o=2),p=h):f(v,n,h)?(c.push({tag:s,n:l(d),otag:g,ctag:v,i:"/"==s?p-g.length:h+v.length}),d="",h+=v.length-1,o=0,"{"==s&&("}}"==v?h++:"}"===(r=c[c.length-1]).n.substr(r.n.length-1)&&(r.n=r.n.substring(0,r.n.length-1)))):d+=n.charAt(h);return y(p,!0),c};var u={_t:!0,"\n":!0,$:!0,"/":!0};function d(t,n,i,r){var a,o=[],s=null,l=null;for(a=i[i.length-1];t.length>0;){if(l=t.shift(),a&&"<"==a.tag&&!(l.tag in u))throw new Error("Illegal content in < super tag.");if(e.tags[l.tag]<=e.tags.$||c(l,r))i.push(l),l.nodes=d(t,l.tag,i,r);else{if("/"==l.tag){if(0===i.length)throw new Error("Closing tag without opener: /"+l.n);if(s=i.pop(),l.n!=s.n&&!p(l.n,s.n,r))throw new Error("Nesting error: "+s.n+" vs. "+l.n);return s.end=l.i,o}"\n"==l.tag&&(l.last=0==t.length||"\n"==t[0].tag)}o.push(l)}if(i.length>0)throw new Error("missing closing tag: "+i.pop().n);return o}function c(e,t){for(var n=0,i=t.length;n<i;n++)if(t[n].o==e.n)return e.tag="#",!0}function p(e,t,n){for(var i=0,r=n.length;i<r;i++)if(n[i].c==e&&n[i].o==t)return!0}function h(e){var t=[];for(var n in e.partials)t.push('"'+g(n)+'":{name:"'+g(e.partials[n].name)+'", '+h(e.partials[n])+"}");return"partials: {"+t.join(",")+"}, subs: "+function(e){var t=[];for(var n in e)t.push('"'+g(n)+'": function(c,p,t,i) {'+e[n]+"}");return"{ "+t.join(",")+" }"}(e.subs)}e.stringify=function(t,n,i){return"{code: function (c,p,i) { "+e.wrapMain(t.code)+" },"+h(t)+"}"};var b=0;function g(e){return e.replace(a,"\\\\").replace(n,'\\"').replace(i,"\\n").replace(r,"\\r").replace(o,"\\u2028").replace(s,"\\u2029")}function v(e){return~e.indexOf(".")?"d":"f"}function m(e,t){var n="<"+(t.prefix||"")+e.n+b++;return t.partials[n]={name:e.n,partials:{}},t.code+='t.b(t.rp("'+g(n)+'",c,p,"'+(e.indent||"")+'"));',n}function y(e,t){t.code+="t.b(t.t(t."+v(e.n)+'("'+g(e.n)+'",c,p,0)));'}function w(e){return"t.b("+e+");"}e.generate=function(t,n,i){b=0;var r={code:"",subs:{},partials:{}};return e.walk(t,r),i.asString?this.stringify(r,n,i):this.makeTemplate(r,n,i)},e.wrapMain=function(e){return'var t=this;t.b(i=i||"");'+e+"return t.fl();"},e.template=e.Template,e.makeTemplate=function(e,t,n){var i=this.makePartials(e);return i.code=new Function("c","p","i",this.wrapMain(e.code)),new this.template(i,t,this,n)},e.makePartials=function(e){var t,n={subs:{},partials:e.partials,name:e.name};for(t in n.partials)n.partials[t]=this.makePartials(n.partials[t]);for(t in e.subs)n.subs[t]=new Function("c","p","t","i",e.subs[t]);return n},e.codegen={"#":function(t,n){n.code+="if(t.s(t."+v(t.n)+'("'+g(t.n)+'",c,p,1),c,p,0,'+t.i+","+t.end+',"'+t.otag+" "+t.ctag+'")){t.rs(c,p,function(c,p,t){',e.walk(t.nodes,n),n.code+="});c.pop();}"},"^":function(t,n){n.code+="if(!t.s(t."+v(t.n)+'("'+g(t.n)+'",c,p,1),c,p,1,0,0,"")){',e.walk(t.nodes,n),n.code+="};"},">":m,"<":function(t,n){var i={partials:{},code:"",subs:{},inPartial:!0};e.walk(t.nodes,i);var r=n.partials[m(t,n)];r.subs=i.subs,r.partials=i.partials},$:function(t,n){var i={subs:{},code:"",partials:n.partials,prefix:t.n};e.walk(t.nodes,i),n.subs[t.n]=i.code,n.inPartial||(n.code+='t.sub("'+g(t.n)+'",c,p,i);')},"\n":function(e,t){t.code+=w('"\\n"'+(e.last?"":" + i"))},_v:function(e,t){t.code+="t.b(t.v(t."+v(e.n)+'("'+g(e.n)+'",c,p,0)));'},_t:function(e,t){t.code+=w('"'+g(e.text)+'"')},"{":y,"&":y},e.walk=function(t,n){for(var i,r=0,a=t.length;r<a;r++)(i=e.codegen[t[r].tag])&&i(t[r],n);return n},e.parse=function(e,t,n){return d(e,0,[],(n=n||{}).sectionTags||[])},e.cache={},e.cacheKey=function(e,t){return[e,!!t.asString,!!t.disableLambda,t.delimiters,!!t.modelGet].join("||")},e.compile=function(t,n){n=n||{};var i=e.cacheKey(t,n),r=this.cache[i];if(r){var a=r.partials;for(var o in a)delete a[o].instance;return r}return r=this.generate(this.parse(this.scan(t,n.delimiters),t,n),t,n),this.cache[i]=r}}(t)},485:(e,t,n)=>{var i=n(397);i.Template=n(882).Template,i.template=i.Template,e.exports=i},882:(e,t)=>{!function(e){function t(e,t,n){var i;return t&&"object"==typeof t&&(void 0!==t[e]?i=t[e]:n&&t.get&&"function"==typeof t.get&&(i=t.get(e))),i}e.Template=function(e,t,n,i){e=e||{},this.r=e.code||this.r,this.c=n,this.options=i||{},this.text=t||"",this.partials=e.partials||{},this.subs=e.subs||{},this.buf=""},e.Template.prototype={r:function(e,t,n){return""},v:function(e){return e=l(e),s.test(e)?e.replace(n,"&amp;").replace(i,"&lt;").replace(r,"&gt;").replace(a,"&#39;").replace(o,"&quot;"):e},t:l,render:function(e,t,n){return this.ri([e],t||{},n)},ri:function(e,t,n){return this.r(e,t,n)},ep:function(e,t){var n=this.partials[e],i=t[n.name];if(n.instance&&n.base==i)return n.instance;if("string"==typeof i){if(!this.c)throw new Error("No compiler available.");i=this.c.compile(i,this.options)}if(!i)return null;if(this.partials[e].base=i,n.subs){for(key in t.stackText||(t.stackText={}),n.subs)t.stackText[key]||(t.stackText[key]=void 0!==this.activeSub&&t.stackText[this.activeSub]?t.stackText[this.activeSub]:this.text);i=function(e,t,n,i,r,a){function o(){}function s(){}var l;o.prototype=e,s.prototype=e.subs;var f=new o;for(l in f.subs=new s,f.subsText={},f.buf="",i=i||{},f.stackSubs=i,f.subsText=a,t)i[l]||(i[l]=t[l]);for(l in i)f.subs[l]=i[l];for(l in r=r||{},f.stackPartials=r,n)r[l]||(r[l]=n[l]);for(l in r)f.partials[l]=r[l];return f}(i,n.subs,n.partials,this.stackSubs,this.stackPartials,t.stackText)}return this.partials[e].instance=i,i},rp:function(e,t,n,i){var r=this.ep(e,n);return r?r.ri(t,n,i):""},rs:function(e,t,n){var i=e[e.length-1];if(f(i))for(var r=0;r<i.length;r++)e.push(i[r]),n(e,t,this),e.pop();else n(e,t,this)},s:function(e,t,n,i,r,a,o){var s;return(!f(e)||0!==e.length)&&("function"==typeof e&&(e=this.ms(e,t,n,i,r,a,o)),s=!!e,!i&&s&&t&&t.push("object"==typeof e?e:t[t.length-1]),s)},d:function(e,n,i,r){var a,o=e.split("."),s=this.f(o[0],n,i,r),l=this.options.modelGet,u=null;if("."===e&&f(n[n.length-2]))s=n[n.length-1];else for(var d=1;d<o.length;d++)void 0!==(a=t(o[d],s,l))?(u=s,s=a):s="";return!(r&&!s)&&(r||"function"!=typeof s||(n.push(u),s=this.mv(s,n,i),n.pop()),s)},f:function(e,n,i,r){for(var a=!1,o=!1,s=this.options.modelGet,l=n.length-1;l>=0;l--)if(void 0!==(a=t(e,n[l],s))){o=!0;break}return o?(r||"function"!=typeof a||(a=this.mv(a,n,i)),a):!r&&""},ls:function(e,t,n,i,r){var a=this.options.delimiters;return this.options.delimiters=r,this.b(this.ct(l(e.call(t,i)),t,n)),this.options.delimiters=a,!1},ct:function(e,t,n){if(this.options.disableLambda)throw new Error("Lambda features disabled.");return this.c.compile(e,this.options).render(t,n)},b:function(e){this.buf+=e},fl:function(){var e=this.buf;return this.buf="",e},ms:function(e,t,n,i,r,a,o){var s,l=t[t.length-1],f=e.call(l);return"function"==typeof f?!!i||(s=this.activeSub&&this.subsText&&this.subsText[this.activeSub]?this.subsText[this.activeSub]:this.text,this.ls(f,l,n,s.substring(r,a),o)):f},mv:function(e,t,n){var i=t[t.length-1],r=e.call(i);return"function"==typeof r?this.ct(l(r.call(i)),i,n):r},sub:function(e,t,n,i){var r=this.subs[e];r&&(this.activeSub=e,r(t,n,this,i),this.activeSub=!1)}};var n=/&/g,i=/</g,r=/>/g,a=/\'/g,o=/\"/g,s=/[&<>\"\']/;function l(e){return String(null==e?"":e)}var f=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}}(t)},468:function(e,t,n){"use strict";var i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.parse=void 0;var r=n(699),a=n(593);function o(e,t){var n=e.split(".");return n.length>1?n[n.length-1]:t}function s(e,t){return t.reduce((function(t,n){return t||e.startsWith(n)}),!1)}var l=["a/","b/","i/","w/","c/","o/"];function f(e,t,n){var r=void 0!==n?i(i([],l,!0),[n],!1):l,o=((t?new RegExp("^".concat((0,a.escapeForRegExp)(t),' "?(.+?)"?$')):new RegExp('^"?(.+?)"?$')).exec(e)||[])[1],s=void 0===o?"":o,f=r.find((function(e){return 0===s.indexOf(e)}));return(f?s.slice(f.length):s).replace(/\s+\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)? [+-]\d{4}.*$/,"")}t.parse=function(e,t){void 0===t&&(t={});var n=[],i=null,a=null,l=null,u=null,d=null,c=null,p=null,h="--- ",b="+++ ",g="@@",v=/^old mode (\d{6})/,m=/^new mode (\d{6})/,y=/^deleted file mode (\d{6})/,w=/^new file mode (\d{6})/,L=/^copy from "?(.+)"?/,S=/^copy to "?(.+)"?/,x=/^rename from "?(.+)"?/,C=/^rename to "?(.+)"?/,_=/^similarity index (\d+)%/,T=/^dissimilarity index (\d+)%/,O=/^index ([\da-z]+)\.\.([\da-z]+)\s*(\d{6})?/,N=/^Binary files (.*) and (.*) differ/,P=/^GIT binary patch/,j=/^index ([\da-z]+),([\da-z]+)\.\.([\da-z]+)/,E=/^mode (\d{6}),(\d{6})\.\.(\d{6})/,M=/^new file mode (\d{6})/,H=/^deleted file mode (\d{6}),(\d{6})/,k=e.replace(/\\ No newline at end of file/g,"").replace(/\r\n?/g,"\n").split("\n");function D(){null!==a&&null!==i&&(i.blocks.push(a),a=null)}function F(){null!==i&&(i.oldName||null===c||(i.oldName=c),i.newName||null===p||(i.newName=p),i.newName&&(n.push(i),i=null)),c=null,p=null}function I(){D(),F(),i={blocks:[],deletedLines:0,addedLines:0}}function A(e){var t;D(),null!==i&&((t=/^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@.*/.exec(e))?(i.isCombined=!1,l=parseInt(t[1],10),d=parseInt(t[2],10)):(t=/^@@@ -(\d+)(?:,\d+)? -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@@.*/.exec(e))?(i.isCombined=!0,l=parseInt(t[1],10),u=parseInt(t[2],10),d=parseInt(t[3],10)):(e.startsWith(g)&&console.error("Failed to parse lines, starting in 0!"),l=0,d=0,i.isCombined=!1)),a={lines:[],oldStartLine:l,oldStartLine2:u,newStartLine:d,header:e}}return k.forEach((function(e,u){if(e&&!e.startsWith("*")){var D,F=k[u-1],W=k[u+1],R=k[u+2];if(e.startsWith("diff --git")||e.startsWith("diff --combined")){if(I(),(D=/^diff --git "?([a-ciow]\/.+)"? "?([a-ciow]\/.+)"?/.exec(e))&&(c=f(D[1],void 0,t.dstPrefix),p=f(D[2],void 0,t.srcPrefix)),null===i)throw new Error("Where is my file !!!");i.isGitDiff=!0}else if(!e.startsWith("Binary files")||(null==i?void 0:i.isGitDiff)){if((!i||!i.isGitDiff&&i&&e.startsWith(h)&&W.startsWith(b)&&R.startsWith(g))&&I(),!(null==i?void 0:i.isTooBig)){if(i&&("number"==typeof t.diffMaxChanges&&i.addedLines+i.deletedLines>t.diffMaxChanges||"number"==typeof t.diffMaxLineLength&&e.length>t.diffMaxLineLength))return i.isTooBig=!0,i.addedLines=0,i.deletedLines=0,i.blocks=[],a=null,void A("function"==typeof t.diffTooBigMessage?t.diffTooBigMessage(n.length):"Diff too big to be displayed");if(e.startsWith(h)&&W.startsWith(b)||e.startsWith(b)&&F.startsWith(h)){if(i&&!i.oldName&&e.startsWith("--- ")&&(D=function(e,t){return f(e,"---",t)}(e,t.srcPrefix)))return i.oldName=D,void(i.language=o(i.oldName,i.language));if(i&&!i.newName&&e.startsWith("+++ ")&&(D=function(e,t){return f(e,"+++",t)}(e,t.dstPrefix)))return i.newName=D,void(i.language=o(i.newName,i.language))}if(i&&(e.startsWith(g)||i.isGitDiff&&i.oldName&&i.newName&&!a))A(e);else if(a&&(e.startsWith("+")||e.startsWith("-")||e.startsWith(" ")))!function(e){if(null!==i&&null!==a&&null!==l&&null!==d){var t={content:e},n=i.isCombined?["+ "," +","++"]:["+"],o=i.isCombined?["- "," -","--"]:["-"];s(e,n)?(i.addedLines++,t.type=r.LineType.INSERT,t.oldNumber=void 0,t.newNumber=d++):s(e,o)?(i.deletedLines++,t.type=r.LineType.DELETE,t.oldNumber=l++,t.newNumber=void 0):(t.type=r.LineType.CONTEXT,t.oldNumber=l++,t.newNumber=d++),a.lines.push(t)}}(e);else{var B=!function(e,t){for(var n=t;n<k.length-3;){if(e.startsWith("diff"))return!1;if(k[n].startsWith(h)&&k[n+1].startsWith(b)&&k[n+2].startsWith(g))return!0;n++}return!1}(e,u);if(null===i)throw new Error("Where is my file !!!");(D=v.exec(e))?i.oldMode=D[1]:(D=m.exec(e))?i.newMode=D[1]:(D=y.exec(e))?(i.deletedFileMode=D[1],i.isDeleted=!0):(D=w.exec(e))?(i.newFileMode=D[1],i.isNew=!0):(D=L.exec(e))?(B&&(i.oldName=D[1]),i.isCopy=!0):(D=S.exec(e))?(B&&(i.newName=D[1]),i.isCopy=!0):(D=x.exec(e))?(B&&(i.oldName=D[1]),i.isRename=!0):(D=C.exec(e))?(B&&(i.newName=D[1]),i.isRename=!0):(D=N.exec(e))?(i.isBinary=!0,i.oldName=f(D[1],void 0,t.srcPrefix),i.newName=f(D[2],void 0,t.dstPrefix),A("Binary file")):P.test(e)?(i.isBinary=!0,A(e)):(D=_.exec(e))?i.unchangedPercentage=parseInt(D[1],10):(D=T.exec(e))?i.changedPercentage=parseInt(D[1],10):(D=O.exec(e))?(i.checksumBefore=D[1],i.checksumAfter=D[2],D[3]&&(i.mode=D[3])):(D=j.exec(e))?(i.checksumBefore=[D[2],D[3]],i.checksumAfter=D[1]):(D=E.exec(e))?(i.oldMode=[D[2],D[3]],i.newMode=D[1]):(D=M.exec(e))?(i.newFileMode=D[1],i.isNew=!0):(D=H.exec(e))&&(i.deletedFileMode=D[1],i.isDeleted=!0)}}}else{if(I(),(D=/^Binary files "?([a-ciow]\/.+)"? and "?([a-ciow]\/.+)"? differ/.exec(e))&&(c=f(D[1],void 0,t.dstPrefix),p=f(D[2],void 0,t.srcPrefix)),null===i)throw new Error("Where is my file !!!");i.isBinary=!0}}})),D(),F(),n}},979:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTemplates=void 0;var o=a(n(485));t.defaultTemplates={},t.defaultTemplates["file-summary-line"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<li class="d2h-file-list-line">'),i.b("\n"+n),i.b('    <span class="d2h-file-name-wrapper">'),i.b("\n"+n),i.b(i.rp("<fileIcon0",e,t,"      ")),i.b('      <a href="#'),i.b(i.v(i.f("fileHtmlId",e,t,0))),i.b('" class="d2h-file-name">'),i.b(i.v(i.f("fileName",e,t,0))),i.b("</a>"),i.b("\n"+n),i.b('      <span class="d2h-file-stats">'),i.b("\n"+n),i.b('          <span class="d2h-lines-added">'),i.b(i.v(i.f("addedLines",e,t,0))),i.b("</span>"),i.b("\n"+n),i.b('          <span class="d2h-lines-deleted">'),i.b(i.v(i.f("deletedLines",e,t,0))),i.b("</span>"),i.b("\n"+n),i.b("      </span>"),i.b("\n"+n),i.b("    </span>"),i.b("\n"+n),i.b("</li>"),i.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}}},subs:{}}),t.defaultTemplates["file-summary-wrapper"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<div class="d2h-file-list-wrapper">'),i.b("\n"+n),i.b('    <div class="d2h-file-list-header">'),i.b("\n"+n),i.b('        <span class="d2h-file-list-title">Files changed ('),i.b(i.v(i.f("filesNumber",e,t,0))),i.b(")</span>"),i.b("\n"+n),i.b('        <a class="d2h-file-switch d2h-hide">hide</a>'),i.b("\n"+n),i.b('        <a class="d2h-file-switch d2h-show">show</a>'),i.b("\n"+n),i.b("    </div>"),i.b("\n"+n),i.b('    <ol class="d2h-file-list">'),i.b("\n"+n),i.b("    "),i.b(i.t(i.f("files",e,t,0))),i.b("\n"+n),i.b("    </ol>"),i.b("\n"+n),i.b("</div>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-block-header"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b("<tr>"),i.b("\n"+n),i.b('    <td class="'),i.b(i.v(i.f("lineClass",e,t,0))),i.b(" "),i.b(i.v(i.d("CSSLineClass.INFO",e,t,0))),i.b('"></td>'),i.b("\n"+n),i.b('    <td class="'),i.b(i.v(i.d("CSSLineClass.INFO",e,t,0))),i.b('">'),i.b("\n"+n),i.b('        <div class="'),i.b(i.v(i.f("contentClass",e,t,0))),i.b('">'),i.s(i.f("blockHeader",e,t,1),e,t,0,156,173,"{{ }}")&&(i.rs(e,t,(function(e,t,n){n.b(n.t(n.f("blockHeader",e,t,0)))})),e.pop()),i.s(i.f("blockHeader",e,t,1),e,t,1,0,0,"")||i.b("&nbsp;"),i.b("</div>"),i.b("\n"+n),i.b("    </td>"),i.b("\n"+n),i.b("</tr>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-empty-diff"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b("<tr>"),i.b("\n"+n),i.b('    <td class="'),i.b(i.v(i.d("CSSLineClass.INFO",e,t,0))),i.b('">'),i.b("\n"+n),i.b('        <div class="'),i.b(i.v(i.f("contentClass",e,t,0))),i.b('">'),i.b("\n"+n),i.b("            File without changes"),i.b("\n"+n),i.b("        </div>"),i.b("\n"+n),i.b("    </td>"),i.b("\n"+n),i.b("</tr>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-file-path"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<span class="d2h-file-name-wrapper">'),i.b("\n"+n),i.b(i.rp("<fileIcon0",e,t,"    ")),i.b('    <span class="d2h-file-name">'),i.b(i.v(i.f("fileDiffName",e,t,0))),i.b("</span>"),i.b("\n"+n),i.b(i.rp("<fileTag1",e,t,"    ")),i.b("</span>"),i.b("\n"+n),i.b('<label class="d2h-file-collapse">'),i.b("\n"+n),i.b('    <input class="d2h-file-collapse-input" type="checkbox" name="viewed" value="viewed">'),i.b("\n"+n),i.b("    Viewed"),i.b("\n"+n),i.b("</label>"),i.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}},"<fileTag1":{name:"fileTag",partials:{},subs:{}}},subs:{}}),t.defaultTemplates["generic-line"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b("<tr>"),i.b("\n"+n),i.b('    <td class="'),i.b(i.v(i.f("lineClass",e,t,0))),i.b(" "),i.b(i.v(i.f("type",e,t,0))),i.b('">'),i.b("\n"+n),i.b("      "),i.b(i.t(i.f("lineNumber",e,t,0))),i.b("\n"+n),i.b("    </td>"),i.b("\n"+n),i.b('    <td class="'),i.b(i.v(i.f("type",e,t,0))),i.b('">'),i.b("\n"+n),i.b('        <div class="'),i.b(i.v(i.f("contentClass",e,t,0))),i.b('">'),i.b("\n"+n),i.s(i.f("prefix",e,t,1),e,t,0,162,238,"{{ }}")&&(i.rs(e,t,(function(e,t,i){i.b('            <span class="d2h-code-line-prefix">'),i.b(i.t(i.f("prefix",e,t,0))),i.b("</span>"),i.b("\n"+n)})),e.pop()),i.s(i.f("prefix",e,t,1),e,t,1,0,0,"")||(i.b('            <span class="d2h-code-line-prefix">&nbsp;</span>'),i.b("\n"+n)),i.s(i.f("content",e,t,1),e,t,0,371,445,"{{ }}")&&(i.rs(e,t,(function(e,t,i){i.b('            <span class="d2h-code-line-ctn">'),i.b(i.t(i.f("content",e,t,0))),i.b("</span>"),i.b("\n"+n)})),e.pop()),i.s(i.f("content",e,t,1),e,t,1,0,0,"")||(i.b('            <span class="d2h-code-line-ctn"><br></span>'),i.b("\n"+n)),i.b("        </div>"),i.b("\n"+n),i.b("    </td>"),i.b("\n"+n),i.b("</tr>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-wrapper"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<div class="d2h-wrapper">'),i.b("\n"+n),i.b("    "),i.b(i.t(i.f("content",e,t,0))),i.b("\n"+n),i.b("</div>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-added"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<svg aria-hidden="true" class="d2h-icon d2h-added" height="16" title="added" version="1.1" viewBox="0 0 14 16"'),i.b("\n"+n),i.b('     width="14">'),i.b("\n"+n),i.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM6 9H3V7h3V4h2v3h3v2H8v3H6V9z"></path>'),i.b("\n"+n),i.b("</svg>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-changed"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<svg aria-hidden="true" class="d2h-icon d2h-changed" height="16" title="modified" version="1.1"'),i.b("\n"+n),i.b('     viewBox="0 0 14 16" width="14">'),i.b("\n"+n),i.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM4 8c0-1.66 1.34-3 3-3s3 1.34 3 3-1.34 3-3 3-3-1.34-3-3z"></path>'),i.b("\n"+n),i.b("</svg>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-deleted"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<svg aria-hidden="true" class="d2h-icon d2h-deleted" height="16" title="removed" version="1.1"'),i.b("\n"+n),i.b('     viewBox="0 0 14 16" width="14">'),i.b("\n"+n),i.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM11 9H3V7h8v2z"></path>'),i.b("\n"+n),i.b("</svg>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-renamed"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<svg aria-hidden="true" class="d2h-icon d2h-moved" height="16" title="renamed" version="1.1"'),i.b("\n"+n),i.b('     viewBox="0 0 14 16" width="14">'),i.b("\n"+n),i.b('    <path d="M6 9H3V7h3V4l5 4-5 4V9z m8-7v12c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h12c0.55 0 1 0.45 1 1z m-1 0H1v12h12V2z"></path>'),i.b("\n"+n),i.b("</svg>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<svg aria-hidden="true" class="d2h-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12">'),i.b("\n"+n),i.b('    <path d="M6 5H2v-1h4v1zM2 8h7v-1H2v1z m0 2h7v-1H2v1z m0 2h7v-1H2v1z m10-7.5v9.5c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h7.5l3.5 3.5z m-1 0.5L8 2H1v12h10V5z"></path>'),i.b("\n"+n),i.b("</svg>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["line-by-line-file-diff"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<div id="'),i.b(i.v(i.f("fileHtmlId",e,t,0))),i.b('" class="d2h-file-wrapper" data-lang="'),i.b(i.v(i.d("file.language",e,t,0))),i.b('">'),i.b("\n"+n),i.b('    <div class="d2h-file-header">'),i.b("\n"+n),i.b("    "),i.b(i.t(i.f("filePath",e,t,0))),i.b("\n"+n),i.b("    </div>"),i.b("\n"+n),i.b('    <div class="d2h-file-diff">'),i.b("\n"+n),i.b('        <div class="d2h-code-wrapper">'),i.b("\n"+n),i.b('            <table class="d2h-diff-table">'),i.b("\n"+n),i.b('                <tbody class="d2h-diff-tbody">'),i.b("\n"+n),i.b("                "),i.b(i.t(i.f("diffs",e,t,0))),i.b("\n"+n),i.b("                </tbody>"),i.b("\n"+n),i.b("            </table>"),i.b("\n"+n),i.b("        </div>"),i.b("\n"+n),i.b("    </div>"),i.b("\n"+n),i.b("</div>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["line-by-line-numbers"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<div class="line-num1">'),i.b(i.v(i.f("oldNumber",e,t,0))),i.b("</div>"),i.b("\n"+n),i.b('<div class="line-num2">'),i.b(i.v(i.f("newNumber",e,t,0))),i.b("</div>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["side-by-side-file-diff"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<div id="'),i.b(i.v(i.f("fileHtmlId",e,t,0))),i.b('" class="d2h-file-wrapper" data-lang="'),i.b(i.v(i.d("file.language",e,t,0))),i.b('">'),i.b("\n"+n),i.b('    <div class="d2h-file-header">'),i.b("\n"+n),i.b("      "),i.b(i.t(i.f("filePath",e,t,0))),i.b("\n"+n),i.b("    </div>"),i.b("\n"+n),i.b('    <div class="d2h-files-diff">'),i.b("\n"+n),i.b('        <div class="d2h-file-side-diff">'),i.b("\n"+n),i.b('            <div class="d2h-code-wrapper">'),i.b("\n"+n),i.b('                <table class="d2h-diff-table">'),i.b("\n"+n),i.b('                    <tbody class="d2h-diff-tbody">'),i.b("\n"+n),i.b("                    "),i.b(i.t(i.d("diffs.left",e,t,0))),i.b("\n"+n),i.b("                    </tbody>"),i.b("\n"+n),i.b("                </table>"),i.b("\n"+n),i.b("            </div>"),i.b("\n"+n),i.b("        </div>"),i.b("\n"+n),i.b('        <div class="d2h-file-side-diff">'),i.b("\n"+n),i.b('            <div class="d2h-code-wrapper">'),i.b("\n"+n),i.b('                <table class="d2h-diff-table">'),i.b("\n"+n),i.b('                    <tbody class="d2h-diff-tbody">'),i.b("\n"+n),i.b("                    "),i.b(i.t(i.d("diffs.right",e,t,0))),i.b("\n"+n),i.b("                    </tbody>"),i.b("\n"+n),i.b("                </table>"),i.b("\n"+n),i.b("            </div>"),i.b("\n"+n),i.b("        </div>"),i.b("\n"+n),i.b("    </div>"),i.b("\n"+n),i.b("</div>"),i.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-added"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<span class="d2h-tag d2h-added d2h-added-tag">ADDED</span>'),i.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-changed"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<span class="d2h-tag d2h-changed d2h-changed-tag">CHANGED</span>'),i.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-deleted"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<span class="d2h-tag d2h-deleted d2h-deleted-tag">DELETED</span>'),i.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-renamed"]=new o.Template({code:function(e,t,n){var i=this;return i.b(n=n||""),i.b('<span class="d2h-tag d2h-moved d2h-moved-tag">RENAMED</span>'),i.fl()},partials:{},subs:{}})},834:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.html=t.parse=t.defaultDiff2HtmlConfig=void 0;var l=o(n(468)),f=o(n(479)),u=o(n(378)),d=o(n(170)),c=n(699),p=s(n(63));t.defaultDiff2HtmlConfig=i(i(i({},u.defaultLineByLineRendererConfig),d.defaultSideBySideRendererConfig),{outputFormat:c.OutputFormatType.LINE_BY_LINE,drawFileList:!0}),t.parse=function(e,n){return void 0===n&&(n={}),l.parse(e,i(i({},t.defaultDiff2HtmlConfig),n))},t.html=function(e,n){void 0===n&&(n={});var r=i(i({},t.defaultDiff2HtmlConfig),n),a="string"==typeof e?l.parse(e,r):e,o=new p.default(r);return(r.drawFileList?f.render(a,o):"")+("side-by-side"===r.outputFormat?new d.default(o,r).render(a):new u.default(o,r).render(a))}},479:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.render=void 0;var o=a(n(741)),s="file-summary";t.render=function(e,t){var n=e.map((function(e){return t.render(s,"line",{fileHtmlId:o.getHtmlId(e),oldName:e.oldName,newName:e.newName,fileName:o.filenameDiff(e),deletedLines:"-"+e.deletedLines,addedLines:"+"+e.addedLines},{fileIcon:t.template("icon",o.getFileIcon(e))})})).join("\n");return t.render(s,"wrapper",{filesNumber:e.length,files:n})}},63:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var s=o(n(485)),l=n(979),f=function(){function e(e){var t=e.compiledTemplates,n=void 0===t?{}:t,r=e.rawTemplates,a=void 0===r?{}:r,o=Object.entries(a).reduce((function(e,t){var n,r=t[0],a=t[1],o=s.compile(a,{asString:!1});return i(i({},e),((n={})[r]=o,n))}),{});this.preCompiledTemplates=i(i(i({},l.defaultTemplates),n),o)}return e.compile=function(e){return s.compile(e,{asString:!1})},e.prototype.render=function(e,t,n,i,r){var a=this.templateKey(e,t);try{return this.preCompiledTemplates[a].render(n,i,r)}catch(e){throw new Error("Could not find template to render '".concat(a,"'"))}},e.prototype.template=function(e,t){return this.preCompiledTemplates[this.templateKey(e,t)]},e.prototype.templateKey=function(e,t){return"".concat(e,"-").concat(t)},e}();t.default=f},378:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultLineByLineRendererConfig=void 0;var s=o(n(483)),l=o(n(741)),f=n(699);t.defaultLineByLineRendererConfig=i(i({},l.defaultRenderConfig),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200});var u="generic",d="line-by-line",c=function(){function e(e,n){void 0===n&&(n={}),this.hoganUtils=e,this.config=i(i({},t.defaultLineByLineRendererConfig),n)}return e.prototype.render=function(e){var t=this,n=e.map((function(e){var n;return n=e.blocks.length?t.generateFileHtml(e):t.generateEmptyDiff(),t.makeFileDiffHtml(e,n)})).join("\n");return this.hoganUtils.render(u,"wrapper",{content:n})},e.prototype.makeFileDiffHtml=function(e,t){if(this.config.renderNothingWhenEmpty&&Array.isArray(e.blocks)&&0===e.blocks.length)return"";var n=this.hoganUtils.template(d,"file-diff"),i=this.hoganUtils.template(u,"file-path"),r=this.hoganUtils.template("icon","file"),a=this.hoganUtils.template("tag",l.getFileIcon(e));return n.render({file:e,fileHtmlId:l.getHtmlId(e),diffs:t,filePath:i.render({fileDiffName:l.filenameDiff(e)},{fileIcon:r,fileTag:a})})},e.prototype.generateEmptyDiff=function(){return this.hoganUtils.render(u,"empty-diff",{contentClass:"d2h-code-line",CSSLineClass:l.CSSLineClass})},e.prototype.generateFileHtml=function(e){var t=this,n=s.newMatcherFn(s.newDistanceFn((function(t){return l.deconstructLine(t.content,e.isCombined).content})));return e.blocks.map((function(i){var r=t.hoganUtils.render(u,"block-header",{CSSLineClass:l.CSSLineClass,blockHeader:e.isTooBig?i.header:l.escapeForHtml(i.header),lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line"});return t.applyLineGroupping(i).forEach((function(i){var a=i[0],o=i[1],s=i[2];if(o.length&&s.length&&!a.length)t.applyRematchMatching(o,s,n).map((function(n){var i=n[0],a=n[1],o=t.processChangedLines(e.isCombined,i,a),s=o.left,l=o.right;r+=s,r+=l}));else if(a.length)a.forEach((function(n){var i=l.deconstructLine(n.content,e.isCombined),a=i.prefix,o=i.content;r+=t.generateSingleLineHtml({type:l.CSSLineClass.CONTEXT,prefix:a,content:o,oldNumber:n.oldNumber,newNumber:n.newNumber})}));else if(o.length||s.length){var f=t.processChangedLines(e.isCombined,o,s),u=f.left,d=f.right;r+=u,r+=d}else console.error("Unknown state reached while processing groups of lines",a,o,s)})),r})).join("\n")},e.prototype.applyLineGroupping=function(e){for(var t=[],n=[],i=[],r=0;r<e.lines.length;r++){var a=e.lines[r];(a.type!==f.LineType.INSERT&&i.length||a.type===f.LineType.CONTEXT&&n.length>0)&&(t.push([[],n,i]),n=[],i=[]),a.type===f.LineType.CONTEXT?t.push([[a],[],[]]):a.type===f.LineType.INSERT&&0===n.length?t.push([[],[],[a]]):a.type===f.LineType.INSERT&&n.length>0?i.push(a):a.type===f.LineType.DELETE&&n.push(a)}return(n.length||i.length)&&(t.push([[],n,i]),n=[],i=[]),t},e.prototype.applyRematchMatching=function(e,t,n){var i=e.length*t.length,r=Math.max.apply(null,[0].concat(e.concat(t).map((function(e){return e.content.length}))));return i<this.config.matchingMaxComparisons&&r<this.config.maxLineSizeInBlockForComparison&&("lines"===this.config.matching||"words"===this.config.matching)?n(e,t):[[e,t]]},e.prototype.processChangedLines=function(e,t,n){for(var r={right:"",left:""},a=Math.max(t.length,n.length),o=0;o<a;o++){var s=t[o],f=n[o],u=void 0!==s&&void 0!==f?l.diffHighlight(s.content,f.content,e,this.config):void 0,d=void 0!==s&&void 0!==s.oldNumber?i(i({},void 0!==u?{prefix:u.oldLine.prefix,content:u.oldLine.content,type:l.CSSLineClass.DELETE_CHANGES}:i(i({},l.deconstructLine(s.content,e)),{type:l.toCSSClass(s.type)})),{oldNumber:s.oldNumber,newNumber:s.newNumber}):void 0,c=void 0!==f&&void 0!==f.newNumber?i(i({},void 0!==u?{prefix:u.newLine.prefix,content:u.newLine.content,type:l.CSSLineClass.INSERT_CHANGES}:i(i({},l.deconstructLine(f.content,e)),{type:l.toCSSClass(f.type)})),{oldNumber:f.oldNumber,newNumber:f.newNumber}):void 0,p=this.generateLineHtml(d,c),h=p.left,b=p.right;r.left+=h,r.right+=b}return r},e.prototype.generateLineHtml=function(e,t){return{left:this.generateSingleLineHtml(e),right:this.generateSingleLineHtml(t)}},e.prototype.generateSingleLineHtml=function(e){if(void 0===e)return"";var t=this.hoganUtils.render(d,"numbers",{oldNumber:e.oldNumber||"",newNumber:e.newNumber||""});return this.hoganUtils.render(u,"line",{type:e.type,lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line",prefix:" "===e.prefix?"&nbsp;":e.prefix,content:e.content,lineNumber:t})},e}();t.default=c},483:(e,t)=>{"use strict";function n(e,t){if(0===e.length)return t.length;if(0===t.length)return e.length;var n,i,r=[];for(n=0;n<=t.length;n++)r[n]=[n];for(i=0;i<=e.length;i++)r[0][i]=i;for(n=1;n<=t.length;n++)for(i=1;i<=e.length;i++)t.charAt(n-1)===e.charAt(i-1)?r[n][i]=r[n-1][i-1]:r[n][i]=Math.min(r[n-1][i-1]+1,Math.min(r[n][i-1]+1,r[n-1][i]+1));return r[t.length][e.length]}Object.defineProperty(t,"__esModule",{value:!0}),t.newMatcherFn=t.newDistanceFn=t.levenshtein=void 0,t.levenshtein=n,t.newDistanceFn=function(e){return function(t,i){var r=e(t).trim(),a=e(i).trim();return n(r,a)/(r.length+a.length)}},t.newMatcherFn=function(e){return function t(n,i,r,a){void 0===r&&(r=0),void 0===a&&(a=new Map);var o=function(t,n,i){void 0===i&&(i=new Map);for(var r,a=1/0,o=0;o<t.length;++o)for(var s=0;s<n.length;++s){var l=JSON.stringify([t[o],n[s]]),f=void 0;i.has(l)&&(f=i.get(l))||(f=e(t[o],n[s]),i.set(l,f)),f<a&&(r={indexA:o,indexB:s,score:a=f})}return r}(n,i,a);if(!o||n.length+i.length<3)return[[n,i]];var s=n.slice(0,o.indexA),l=i.slice(0,o.indexB),f=[n[o.indexA]],u=[i[o.indexB]],d=o.indexA+1,c=o.indexB+1,p=n.slice(d),h=i.slice(c),b=t(s,l,r+1,a),g=t(f,u,r+1,a),v=t(p,h,r+1,a),m=g;return(o.indexA>0||o.indexB>0)&&(m=b.concat(m)),(n.length>d||i.length>c)&&(m=m.concat(v)),m}}},741:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.diffHighlight=t.getFileIcon=t.getHtmlId=t.filenameDiff=t.deconstructLine=t.escapeForHtml=t.toCSSClass=t.defaultRenderConfig=t.CSSLineClass=void 0;var s=o(n(785)),l=n(593),f=o(n(483)),u=n(699);t.CSSLineClass={INSERTS:"d2h-ins",DELETES:"d2h-del",CONTEXT:"d2h-cntx",INFO:"d2h-info",INSERT_CHANGES:"d2h-ins d2h-change",DELETE_CHANGES:"d2h-del d2h-change"},t.defaultRenderConfig={matching:u.LineMatchingType.NONE,matchWordsThreshold:.25,maxLineLengthHighlight:1e4,diffStyle:u.DiffStyleType.WORD};var d="/",c=f.newDistanceFn((function(e){return e.value})),p=f.newMatcherFn(c);function h(e){return-1!==e.indexOf("dev/null")}function b(e){return e.replace(/(<del[^>]*>((.|\n)*?)<\/del>)/g,"")}function g(e){return e.slice(0).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}function v(e,t,n){void 0===n&&(n=!0);var i=function(e){return e?2:1}(t);return{prefix:e.substring(0,i),content:n?g(e.substring(i)):e.substring(i)}}function m(e){var t=(0,l.unifyPath)(e.oldName),n=(0,l.unifyPath)(e.newName);if(t===n||h(t)||h(n))return h(n)?t:n;for(var i=[],r=[],a=t.split(d),o=n.split(d),s=0,f=a.length-1,u=o.length-1;s<f&&s<u&&a[s]===o[s];)i.push(o[s]),s+=1;for(;f>s&&u>s&&a[f]===o[u];)r.unshift(o[u]),f-=1,u-=1;var c=i.join(d),p=r.join(d),b=a.slice(s,f+1).join(d),g=o.slice(s,u+1).join(d);return c.length&&p.length?c+d+"{"+b+" → "+g+"}"+d+p:c.length?c+d+"{"+b+" → "+g+"}":p.length?"{"+b+" → "+g+"}"+d+p:t+" → "+n}t.toCSSClass=function(e){switch(e){case u.LineType.CONTEXT:return t.CSSLineClass.CONTEXT;case u.LineType.INSERT:return t.CSSLineClass.INSERTS;case u.LineType.DELETE:return t.CSSLineClass.DELETES}},t.escapeForHtml=g,t.deconstructLine=v,t.filenameDiff=m,t.getHtmlId=function(e){return"d2h-".concat((0,l.hashCode)(m(e)).toString().slice(-6))},t.getFileIcon=function(e){var t="file-changed";return e.isRename||e.isCopy?t="file-renamed":e.isNew?t="file-added":e.isDeleted?t="file-deleted":e.newName!==e.oldName&&(t="file-renamed"),t},t.diffHighlight=function(e,n,r,a){void 0===a&&(a={});var o=i(i({},t.defaultRenderConfig),a),l=o.matching,f=o.maxLineLengthHighlight,u=o.matchWordsThreshold,d=o.diffStyle,h=v(e,r,!1),m=v(n,r,!1);if(h.content.length>f||m.content.length>f)return{oldLine:{prefix:h.prefix,content:g(h.content)},newLine:{prefix:m.prefix,content:g(m.content)}};var y="char"===d?s.diffChars(h.content,m.content):s.diffWordsWithSpace(h.content,m.content),w=[];if("word"===d&&"words"===l){var L=y.filter((function(e){return e.removed})),S=y.filter((function(e){return e.added}));p(S,L).forEach((function(e){1===e[0].length&&1===e[1].length&&c(e[0][0],e[1][0])<u&&(w.push(e[0][0]),w.push(e[1][0]))}))}var x,C=y.reduce((function(e,t){var n=t.added?"ins":t.removed?"del":null,i=w.indexOf(t)>-1?' class="d2h-change"':"",r=g(t.value);return null!==n?"".concat(e,"<").concat(n).concat(i,">").concat(r,"</").concat(n,">"):"".concat(e).concat(r)}),"");return{oldLine:{prefix:h.prefix,content:(x=C,x.replace(/(<ins[^>]*>((.|\n)*?)<\/ins>)/g,""))},newLine:{prefix:m.prefix,content:b(C)}}}},170:function(e,t,n){"use strict";var i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSideBySideRendererConfig=void 0;var s=o(n(483)),l=o(n(741)),f=n(699);t.defaultSideBySideRendererConfig=i(i({},l.defaultRenderConfig),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200});var u="generic",d=function(){function e(e,n){void 0===n&&(n={}),this.hoganUtils=e,this.config=i(i({},t.defaultSideBySideRendererConfig),n)}return e.prototype.render=function(e){var t=this,n=e.map((function(e){var n;return n=e.blocks.length?t.generateFileHtml(e):t.generateEmptyDiff(),t.makeFileDiffHtml(e,n)})).join("\n");return this.hoganUtils.render(u,"wrapper",{content:n})},e.prototype.makeFileDiffHtml=function(e,t){if(this.config.renderNothingWhenEmpty&&Array.isArray(e.blocks)&&0===e.blocks.length)return"";var n=this.hoganUtils.template("side-by-side","file-diff"),i=this.hoganUtils.template(u,"file-path"),r=this.hoganUtils.template("icon","file"),a=this.hoganUtils.template("tag",l.getFileIcon(e));return n.render({file:e,fileHtmlId:l.getHtmlId(e),diffs:t,filePath:i.render({fileDiffName:l.filenameDiff(e)},{fileIcon:r,fileTag:a})})},e.prototype.generateEmptyDiff=function(){return{right:"",left:this.hoganUtils.render(u,"empty-diff",{contentClass:"d2h-code-side-line",CSSLineClass:l.CSSLineClass})}},e.prototype.generateFileHtml=function(e){var t=this,n=s.newMatcherFn(s.newDistanceFn((function(t){return l.deconstructLine(t.content,e.isCombined).content})));return e.blocks.map((function(i){var r={left:t.makeHeaderHtml(i.header,e),right:t.makeHeaderHtml("")};return t.applyLineGroupping(i).forEach((function(i){var a=i[0],o=i[1],s=i[2];if(o.length&&s.length&&!a.length)t.applyRematchMatching(o,s,n).map((function(n){var i=n[0],a=n[1],o=t.processChangedLines(e.isCombined,i,a),s=o.left,l=o.right;r.left+=s,r.right+=l}));else if(a.length)a.forEach((function(n){var i=l.deconstructLine(n.content,e.isCombined),a=i.prefix,o=i.content,s=t.generateLineHtml({type:l.CSSLineClass.CONTEXT,prefix:a,content:o,number:n.oldNumber},{type:l.CSSLineClass.CONTEXT,prefix:a,content:o,number:n.newNumber}),f=s.left,u=s.right;r.left+=f,r.right+=u}));else if(o.length||s.length){var f=t.processChangedLines(e.isCombined,o,s),u=f.left,d=f.right;r.left+=u,r.right+=d}else console.error("Unknown state reached while processing groups of lines",a,o,s)})),r})).reduce((function(e,t){return{left:e.left+t.left,right:e.right+t.right}}),{left:"",right:""})},e.prototype.applyLineGroupping=function(e){for(var t=[],n=[],i=[],r=0;r<e.lines.length;r++){var a=e.lines[r];(a.type!==f.LineType.INSERT&&i.length||a.type===f.LineType.CONTEXT&&n.length>0)&&(t.push([[],n,i]),n=[],i=[]),a.type===f.LineType.CONTEXT?t.push([[a],[],[]]):a.type===f.LineType.INSERT&&0===n.length?t.push([[],[],[a]]):a.type===f.LineType.INSERT&&n.length>0?i.push(a):a.type===f.LineType.DELETE&&n.push(a)}return(n.length||i.length)&&(t.push([[],n,i]),n=[],i=[]),t},e.prototype.applyRematchMatching=function(e,t,n){var i=e.length*t.length,r=Math.max.apply(null,[0].concat(e.concat(t).map((function(e){return e.content.length}))));return i<this.config.matchingMaxComparisons&&r<this.config.maxLineSizeInBlockForComparison&&("lines"===this.config.matching||"words"===this.config.matching)?n(e,t):[[e,t]]},e.prototype.makeHeaderHtml=function(e,t){return this.hoganUtils.render(u,"block-header",{CSSLineClass:l.CSSLineClass,blockHeader:(null==t?void 0:t.isTooBig)?e:l.escapeForHtml(e),lineClass:"d2h-code-side-linenumber",contentClass:"d2h-code-side-line"})},e.prototype.processChangedLines=function(e,t,n){for(var r={right:"",left:""},a=Math.max(t.length,n.length),o=0;o<a;o++){var s=t[o],f=n[o],u=void 0!==s&&void 0!==f?l.diffHighlight(s.content,f.content,e,this.config):void 0,d=void 0!==s&&void 0!==s.oldNumber?i(i({},void 0!==u?{prefix:u.oldLine.prefix,content:u.oldLine.content,type:l.CSSLineClass.DELETE_CHANGES}:i(i({},l.deconstructLine(s.content,e)),{type:l.toCSSClass(s.type)})),{number:s.oldNumber}):void 0,c=void 0!==f&&void 0!==f.newNumber?i(i({},void 0!==u?{prefix:u.newLine.prefix,content:u.newLine.content,type:l.CSSLineClass.INSERT_CHANGES}:i(i({},l.deconstructLine(f.content,e)),{type:l.toCSSClass(f.type)})),{number:f.newNumber}):void 0,p=this.generateLineHtml(d,c),h=p.left,b=p.right;r.left+=h,r.right+=b}return r},e.prototype.generateLineHtml=function(e,t){return{left:this.generateSingleHtml(e),right:this.generateSingleHtml(t)}},e.prototype.generateSingleHtml=function(e){var t="d2h-code-side-linenumber",n="d2h-code-side-line";return this.hoganUtils.render(u,"line",{type:(null==e?void 0:e.type)||"".concat(l.CSSLineClass.CONTEXT," d2h-emptyplaceholder"),lineClass:void 0!==e?t:"".concat(t," d2h-code-side-emptyplaceholder"),contentClass:void 0!==e?n:"".concat(n," d2h-code-side-emptyplaceholder"),prefix:" "===(null==e?void 0:e.prefix)?"&nbsp;":null==e?void 0:e.prefix,content:null==e?void 0:e.content,lineNumber:null==e?void 0:e.number})},e}();t.default=d},699:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.DiffStyleType=t.LineMatchingType=t.OutputFormatType=t.LineType=void 0,(n=t.LineType||(t.LineType={})).INSERT="insert",n.DELETE="delete",n.CONTEXT="context",t.OutputFormatType={LINE_BY_LINE:"line-by-line",SIDE_BY_SIDE:"side-by-side"},t.LineMatchingType={LINES:"lines",WORDS:"words",NONE:"none"},t.DiffStyleType={WORD:"word",CHAR:"char"}},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hashCode=t.unifyPath=t.escapeForRegExp=void 0;var n=RegExp("["+["-","[","]","/","{","}","(",")","*","+","?",".","\\","^","$","|"].join("\\")+"]","g");t.escapeForRegExp=function(e){return e.replace(n,"\\$&")},t.unifyPath=function(e){return e?e.replace(/\\/g,"/"):e},t.hashCode=function(e){var t,n,i=0;for(t=0,n=e.length;t<n;t++)i=(i<<5)-i+e.charCodeAt(t),i|=0;return i}}},t={},function n(i){var r=t[i];if(void 0!==r)return r.exports;var a=t[i]={exports:{}};return e[i].call(a.exports,a,a.exports,n),a.exports}(834);var e,t}));