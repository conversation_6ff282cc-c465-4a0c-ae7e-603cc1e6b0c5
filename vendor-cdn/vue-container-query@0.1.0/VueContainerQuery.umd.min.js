(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t():"function"===typeof define&&define.amd?define([],t):"object"===typeof exports?exports["VueContainerQuery"]=t():e["VueContainerQuery"]=t()})("undefined"!==typeof self?self:this,function(){return function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="fae3")}({"01f9":function(e,t,n){"use strict";var i=n("2d00"),r=n("5ca1"),o=n("2aba"),a=n("32e9"),c=n("84f2"),s=n("41a0"),u=n("7f20"),l=n("38fd"),f=n("2b4c")("iterator"),d=!([].keys&&"next"in[].keys()),h="@@iterator",p="keys",v="values",b=function(){return this};e.exports=function(e,t,n,g,y,m,x){s(n,t,g);var w,S,E,O=function(e){if(!d&&e in L)return L[e];switch(e){case p:return function(){return new n(this,e)};case v:return function(){return new n(this,e)}}return function(){return new n(this,e)}},j=t+" Iterator",k=y==v,C=!1,L=e.prototype,T=L[f]||L[h]||y&&L[y],z=T||O(y),A=y?k?O("entries"):z:void 0,_="Array"==t&&L.entries||T;if(_&&(E=l(_.call(new e)),E!==Object.prototype&&E.next&&(u(E,j,!0),i||"function"==typeof E[f]||a(E,f,b))),k&&T&&T.name!==v&&(C=!0,z=function(){return T.call(this)}),i&&!x||!d&&!C&&L[f]||a(L,f,z),c[t]=z,c[j]=b,y)if(w={values:k?z:O(v),keys:m?z:O(p),entries:A},x)for(S in w)S in L||o(L,S,w[S]);else r(r.P+r.F*(d||C),t,w);return w}},"0d58":function(e,t,n){var i=n("ce10"),r=n("e11e");e.exports=Object.keys||function(e){return i(e,r)}},1495:function(e,t,n){var i=n("86cc"),r=n("cb7c"),o=n("0d58");e.exports=n("9e1e")?Object.defineProperties:function(e,t){r(e);var n,a=o(t),c=a.length,s=0;while(c>s)i.f(e,n=a[s++],t[n]);return e}},"18d2":function(e,t,n){"use strict";var i=n("18e9");e.exports=function(e){e=e||{};var t=e.reporter,n=e.batchProcessor,r=e.stateHandler.getState;if(!t)throw new Error("Missing required dependency: reporter.");function o(e,t){if(!c(e))throw new Error("Element is not detectable by this strategy.");function n(){t(e)}if(i.isIE(8))r(e).object={proxy:n},e.attachEvent("onresize",n);else{var o=c(e);o.contentDocument.defaultView.addEventListener("resize",n)}}function a(e,o,a){a||(a=o,o=e,e=null),e=e||{};e.debug;function c(e,o){var a="display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; padding: 0; margin: 0; opacity: 0; z-index: -1000; pointer-events: none;",c=!1,s=window.getComputedStyle(e),u=e.offsetWidth,l=e.offsetHeight;function f(){function n(){if("static"===s.position){e.style.position="relative";var n=function(e,t,n,i){function r(e){return e.replace(/[^-\d\.]/g,"")}var o=n[i];"auto"!==o&&"0"!==r(o)&&(e.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",t),t.style[i]=0)};n(t,e,s,"top"),n(t,e,s,"right"),n(t,e,s,"bottom"),n(t,e,s,"left")}}function u(){function t(e,n){e.contentDocument?n(e.contentDocument):setTimeout(function(){t(e,n)},100)}c||n();var i=this;t(i,function(t){o(e)})}""!==s.position&&(n(s),c=!0);var l=document.createElement("object");l.style.cssText=a,l.tabIndex=-1,l.type="text/html",l.onload=u,i.isIE()||(l.data="about:blank"),e.appendChild(l),r(e).object=l,i.isIE()&&(l.data="about:blank")}r(e).startSize={width:u,height:l},n?n.add(f):f()}i.isIE(8)?a(o):c(o,a)}function c(e){return r(e).object}function s(e){i.isIE(8)?e.detachEvent("onresize",r(e).object.proxy):e.removeChild(c(e)),delete r(e).object}return{makeDetectable:a,addListener:o,uninstall:s}}},"18e9":function(e,t,n){"use strict";var i=e.exports={};i.isIE=function(e){function t(){var e=navigator.userAgent.toLowerCase();return-1!==e.indexOf("msie")||-1!==e.indexOf("trident")||-1!==e.indexOf(" edge/")}if(!t())return!1;if(!e)return!0;var n=function(){var e,t=3,n=document.createElement("div"),i=n.getElementsByTagName("i");do{n.innerHTML="\x3c!--[if gt IE "+ ++t+"]><i></i><![endif]--\x3e"}while(i[0]);return t>4?t:e}();return e===n},i.isLegacyOpera=function(){return!!window.opera}},"1b47":function(e,t,n){"use strict";function i(e){for(var t=[],n=0,i=Object.keys(e);n<i.length;n++){var r=i[n],o=e[r];t.push({minWidth:null!=o.minWidth?o.minWidth:0,maxWidth:null!=o.maxWidth?o.maxWidth:1/0,minHeight:null!=o.minHeight?o.minHeight:0,maxHeight:null!=o.maxHeight?o.maxHeight:1/0,className:r})}return function(e){for(var n=e.height,i=e.width,r={},o=0,a=t;o<a.length;o++){var c=a[o],s=c.className,u=c.minWidth,l=c.maxWidth,f=c.minHeight,d=c.maxHeight;r[s]=null!=n&&null!=i?u<=i&&i<=l&&f<=n&&n<=d:null==n&&null!=i?u<=i&&i<=l:null==n||null!=i||f<=n&&n<=d}return r}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=i},"1eb2":function(e,t,n){var i;"undefined"!==typeof window&&((i=window.document.currentScript)&&(i=i.src.match(/(.+\/)[^/]+\.js$/))&&(n.p=i[1]))},"230e":function(e,t,n){var i=n("d3f4"),r=n("7726").document,o=i(r)&&i(r.createElement);e.exports=function(e){return o?r.createElement(e):{}}},"2aba":function(e,t,n){var i=n("7726"),r=n("32e9"),o=n("69a8"),a=n("ca5a")("src"),c="toString",s=Function[c],u=(""+s).split(c);n("8378").inspectSource=function(e){return s.call(e)},(e.exports=function(e,t,n,c){var s="function"==typeof n;s&&(o(n,"name")||r(n,"name",t)),e[t]!==n&&(s&&(o(n,a)||r(n,a,e[t]?""+e[t]:u.join(String(t)))),e===i?e[t]=n:c?e[t]?e[t]=n:r(e,t,n):(delete e[t],r(e,t,n)))})(Function.prototype,c,function(){return"function"==typeof this&&this[a]||s.call(this)})},"2aeb":function(e,t,n){var i=n("cb7c"),r=n("1495"),o=n("e11e"),a=n("613b")("IE_PROTO"),c=function(){},s="prototype",u=function(){var e,t=n("230e")("iframe"),i=o.length,r="<",a=">";t.style.display="none",n("fab2").appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write(r+"script"+a+"document.F=Object"+r+"/script"+a),e.close(),u=e.F;while(i--)delete u[s][o[i]];return u()};e.exports=Object.create||function(e,t){var n;return null!==e?(c[s]=i(e),n=new c,c[s]=null,n[a]=e):n=u(),void 0===t?n:r(n,t)}},"2b4c":function(e,t,n){var i=n("5537")("wks"),r=n("ca5a"),o=n("7726").Symbol,a="function"==typeof o,c=e.exports=function(e){return i[e]||(i[e]=a&&o[e]||(a?o:r)("Symbol."+e))};c.store=i},"2cef":function(e,t,n){"use strict";e.exports=function(){var e=1;function t(){return e++}return{generate:t}}},"2d00":function(e,t){e.exports=!1},"2d95":function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},"32e9":function(e,t,n){var i=n("86cc"),r=n("4630");e.exports=n("9e1e")?function(e,t,n){return i.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},"38fd":function(e,t,n){var i=n("69a8"),r=n("4bf8"),o=n("613b")("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=r(e),i(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},"41a0":function(e,t,n){"use strict";var i=n("2aeb"),r=n("4630"),o=n("7f20"),a={};n("32e9")(a,n("2b4c")("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=i(a,{next:r(1,n)}),o(e,t+" Iterator")}},"456d":function(e,t,n){var i=n("4bf8"),r=n("0d58");n("5eda")("keys",function(){return function(e){return r(i(e))}})},4588:function(e,t){var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},4630:function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"49ad":function(e,t,n){"use strict";e.exports=function(e){var t={};function n(n){var i=e.get(n);return void 0===i?[]:t[i]||[]}function i(n,i){var r=e.get(n);t[r]||(t[r]=[]),t[r].push(i)}function r(e,t){for(var i=n(e),r=0,o=i.length;r<o;++r)if(i[r]===t){i.splice(r,1);break}}function o(e){var t=n(e);t&&(t.length=0)}return{get:n,add:i,removeListener:r,removeAllListeners:o}}},"4bf8":function(e,t,n){var i=n("be13");e.exports=function(e){return Object(i(e))}},5058:function(e,t,n){"use strict";e.exports=function(e){var t=e.idGenerator,n=e.stateHandler.getState;function i(e){var t=n(e);return t&&void 0!==t.id?t.id:null}function r(e){var i=n(e);if(!i)throw new Error("setId required the element to have a resize detection state.");var r=t.generate();return i.id=r,r}return{get:i,set:r}}},"50bf":function(e,t,n){"use strict";var i=e.exports={};function r(e,t,n){var i=e[t];return void 0!==i&&null!==i||void 0===n?i:n}i.getOption=r},5537:function(e,t,n){var i=n("8378"),r=n("7726"),o="__core-js_shared__",a=r[o]||(r[o]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:i.version,mode:n("2d00")?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},"5be5":function(e,t,n){"use strict";e.exports=function(e){var t=e.stateHandler.getState;function n(e){var n=t(e);return n&&!!n.isDetectable}function i(e){t(e).isDetectable=!0}function r(e){return!!t(e).busy}function o(e,n){t(e).busy=!!n}return{isDetectable:n,markAsDetectable:i,isBusy:r,markBusy:o}}},"5ca1":function(e,t,n){var i=n("7726"),r=n("8378"),o=n("32e9"),a=n("2aba"),c=n("9b43"),s="prototype",u=function(e,t,n){var l,f,d,h,p=e&u.F,v=e&u.G,b=e&u.S,g=e&u.P,y=e&u.B,m=v?i:b?i[t]||(i[t]={}):(i[t]||{})[s],x=v?r:r[t]||(r[t]={}),w=x[s]||(x[s]={});for(l in v&&(n=t),n)f=!p&&m&&void 0!==m[l],d=(f?m:n)[l],h=y&&f?c(d,i):g&&"function"==typeof d?c(Function.call,d):d,m&&a(m,l,d,e&u.U),x[l]!=d&&o(x,l,h),g&&w[l]!=d&&(w[l]=d)};i.core=r,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},"5eda":function(e,t,n){var i=n("5ca1"),r=n("8378"),o=n("79e5");e.exports=function(e,t){var n=(r.Object||{})[e]||Object[e],a={};a[e]=t(n),i(i.S+i.F*o(function(){n(1)}),"Object",a)}},"613b":function(e,t,n){var i=n("5537")("keys"),r=n("ca5a");e.exports=function(e){return i[e]||(i[e]=r(e))}},"626a":function(e,t,n){var i=n("2d95");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==i(e)?e.split(""):Object(e)}},6821:function(e,t,n){var i=n("626a"),r=n("be13");e.exports=function(e){return i(r(e))}},"69a8":function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},"6a99":function(e,t,n){var i=n("d3f4");e.exports=function(e,t){if(!i(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!i(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},7726:function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},"77f1":function(e,t,n){var i=n("4588"),r=Math.max,o=Math.min;e.exports=function(e,t){return e=i(e),e<0?r(e+t,0):o(e,t)}},"79e5":function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},"7f20":function(e,t,n){var i=n("86cc").f,r=n("69a8"),o=n("2b4c")("toStringTag");e.exports=function(e,t,n){e&&!r(e=n?e:e.prototype,o)&&i(e,o,{configurable:!0,value:t})}},8378:function(e,t){var n=e.exports={version:"2.5.7"};"number"==typeof __e&&(__e=n)},"84f2":function(e,t){e.exports={}},"86cc":function(e,t,n){var i=n("cb7c"),r=n("c69a"),o=n("6a99"),a=Object.defineProperty;t.f=n("9e1e")?Object.defineProperty:function(e,t,n){if(i(e),t=o(t,!0),i(n),r)try{return a(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},"9b43":function(e,t,n){var i=n("d8e8");e.exports=function(e,t,n){if(i(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,i){return e.call(t,n,i)};case 3:return function(n,i,r){return e.call(t,n,i,r)}}return function(){return e.apply(t,arguments)}}},"9c6c":function(e,t,n){var i=n("2b4c")("unscopables"),r=Array.prototype;void 0==r[i]&&n("32e9")(r,i,{}),e.exports=function(e){r[i][e]=!0}},"9def":function(e,t,n){var i=n("4588"),r=Math.min;e.exports=function(e){return e>0?r(i(e),9007199254740991):0}},"9e1e":function(e,t,n){e.exports=!n("79e5")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},a307:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=n("eec4"),r=function(){function e(e){var t=this;this.handler=e,this.listenedElement=null,this.hasResizeObserver="undefined"!==typeof window.ResizeObserver,this.hasResizeObserver?this.rz=new ResizeObserver(function(e){t.handler(o(e[0].target))}):this.erd=i({strategy:"scroll"})}return e.prototype.observe=function(e){var t=this;this.listenedElement!==e&&(this.listenedElement&&this.disconnect(),e&&(this.hasResizeObserver?this.rz.observe(e):this.erd.listenTo(e,function(e){t.handler(o(e))})),this.listenedElement=e)},e.prototype.disconnect=function(){this.listenedElement&&(this.hasResizeObserver?this.rz.disconnect():this.erd.uninstall(this.listenedElement),this.listenedElement=null)},e}();function o(e){return{width:a(window.getComputedStyle(e)["width"]),height:a(window.getComputedStyle(e)["height"])}}function a(e){var t=/^([0-9\.]+)px$/.exec(e);return t?parseFloat(t[1]):0}t.default=r},abb4:function(e,t,n){"use strict";e.exports=function(e){function t(){}var n={log:t,warn:t,error:t};if(!e&&window.console){var i=function(e,t){e[t]=function(){var e=console[t];if(e.apply)e.apply(console,arguments);else for(var n=0;n<arguments.length;n++)e(arguments[n])}};i(n,"log"),i(n,"warn"),i(n,"error")}return n}},ac6a:function(e,t,n){for(var i=n("cadf"),r=n("0d58"),o=n("2aba"),a=n("7726"),c=n("32e9"),s=n("84f2"),u=n("2b4c"),l=u("iterator"),f=u("toStringTag"),d=s.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=r(h),v=0;v<p.length;v++){var b,g=p[v],y=h[g],m=a[g],x=m&&m.prototype;if(x&&(x[l]||c(x,l,d),x[f]||c(x,f,g),s[g]=d,y))for(b in i)x[b]||o(x,b,i[b],!0)}},b770:function(e,t,n){"use strict";var i=e.exports={};i.forEach=function(e,t){for(var n=0;n<e.length;n++){var i=t(e[n]);if(i)return i}}},be13:function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},c274:function(e,t,n){"use strict";var i=n("50bf");function r(){var e={},t=0,n=0,i=0;function r(r,o){o||(o=r,r=0),r>n?n=r:r<i&&(i=r),e[r]||(e[r]=[]),e[r].push(o),t++}function o(){for(var t=i;t<=n;t++)for(var r=e[t],o=0;o<r.length;o++){var a=r[o];a()}}function a(){return t}return{add:r,process:o,size:a}}e.exports=function(e){e=e||{};var t=e.reporter,n=i.getOption(e,"async",!0),o=i.getOption(e,"auto",!0);o&&!n&&(t&&t.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true."),n=!0);var a,c=r(),s=!1;function u(e,t){!s&&o&&n&&0===c.size()&&d(),c.add(e,t)}function l(){s=!0;while(c.size()){var e=c;c=r(),e.process()}s=!1}function f(e){s||(void 0===e&&(e=n),a&&(h(a),a=null),e?d():l())}function d(){a=p(l)}function h(e){var t=clearTimeout;return t(e)}function p(e){var t=function(e){return setTimeout(e,0)};return t(e)}return{add:u,force:f}}},c366:function(e,t,n){var i=n("6821"),r=n("9def"),o=n("77f1");e.exports=function(e){return function(t,n,a){var c,s=i(t),u=r(s.length),l=o(a,u);if(e&&n!=n){while(u>l)if(c=s[l++],c!=c)return!0}else for(;u>l;l++)if((e||l in s)&&s[l]===n)return e||l||0;return!e&&-1}}},c69a:function(e,t,n){e.exports=!n("9e1e")&&!n("79e5")(function(){return 7!=Object.defineProperty(n("230e")("div"),"a",{get:function(){return 7}}).a})},c946:function(e,t,n){"use strict";var i=n("b770").forEach;e.exports=function(e){e=e||{};var t=e.reporter,n=e.batchProcessor,r=e.stateHandler.getState,o=(e.stateHandler.hasState,e.idHandler);if(!n)throw new Error("Missing required dependency: batchProcessor");if(!t)throw new Error("Missing required dependency: reporter.");var a=u(),c="erd_scroll_detection_scrollbar_style",s="erd_scroll_detection_container";function u(){var e=500,t=500,n=document.createElement("div");n.style.cssText="position: absolute; width: "+2*e+"px; height: "+2*t+"px; visibility: hidden; margin: 0; padding: 0;";var i=document.createElement("div");i.style.cssText="position: absolute; width: "+e+"px; height: "+t+"px; overflow: scroll; visibility: none; top: "+3*-e+"px; left: "+3*-t+"px; visibility: hidden; margin: 0; padding: 0;",i.appendChild(n),document.body.insertBefore(i,document.body.firstChild);var r=e-i.clientWidth,o=t-i.clientHeight;return document.body.removeChild(i),{width:r,height:o}}function l(e,t){function n(t,n){n=n||function(e){document.head.appendChild(e)};var i=document.createElement("style");return i.innerHTML=t,i.id=e,n(i),i}if(!document.getElementById(e)){var i=t+"_animation",r=t+"_animation_active",o="/* Created by the element-resize-detector library. */\n";o+="."+t+" > div::-webkit-scrollbar { display: none; }\n\n",o+="."+r+" { -webkit-animation-duration: 0.1s; animation-duration: 0.1s; -webkit-animation-name: "+i+"; animation-name: "+i+"; }\n",o+="@-webkit-keyframes "+i+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\n",o+="@keyframes "+i+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }",n(o)}}function f(e){e.className+=" "+s+"_animation_active"}function d(e,n,i){if(e.addEventListener)e.addEventListener(n,i);else{if(!e.attachEvent)return t.error("[scroll] Don't know how to add event listeners.");e.attachEvent("on"+n,i)}}function h(e,n,i){if(e.removeEventListener)e.removeEventListener(n,i);else{if(!e.detachEvent)return t.error("[scroll] Don't know how to remove event listeners.");e.detachEvent("on"+n,i)}}function p(e){return r(e).container.childNodes[0].childNodes[0].childNodes[0]}function v(e){return r(e).container.childNodes[0].childNodes[0].childNodes[1]}function b(e,t){var n=r(e).listeners;if(!n.push)throw new Error("Cannot add listener to an element that is not detectable.");r(e).listeners.push(t)}function g(e,c,u){function l(){if(e.debug){var n=Array.prototype.slice.call(arguments);if(n.unshift(o.get(c),"Scroll: "),t.log.apply)t.log.apply(null,n);else for(var i=0;i<n.length;i++)t.log(n[i])}}function h(e){function t(e){return e===e.ownerDocument.body||e.ownerDocument.body.contains(e)}return!t(e)||null===getComputedStyle(e)}function b(e){var t=r(e).container.childNodes[0],n=getComputedStyle(t);return!n.width||-1===n.width.indexOf("px")}function g(){var e=getComputedStyle(c),t={};return t.position=e.position,t.width=c.offsetWidth,t.height=c.offsetHeight,t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left,t.widthCSS=e.width,t.heightCSS=e.height,t}function y(){var e=g();r(c).startSize={width:e.width,height:e.height},l("Element start size",r(c).startSize)}function m(){r(c).listeners=[]}function x(){if(l("storeStyle invoked."),r(c)){var e=g();r(c).style=e}else l("Aborting because element has been uninstalled")}function w(e,t,n){r(e).lastWidth=t,r(e).lastHeight=n}function S(e){return p(e).childNodes[0]}function E(){return 2*a.width+1}function O(){return 2*a.height+1}function j(e){return e+10+E()}function k(e){return e+10+O()}function C(e){return 2*e+E()}function L(e){return 2*e+O()}function T(e,t,n){var i=p(e),r=v(e),o=j(t),a=k(n),c=C(t),s=L(n);i.scrollLeft=o,i.scrollTop=a,r.scrollLeft=c,r.scrollTop=s}function z(){var e=r(c).container;if(!e){e=document.createElement("div"),e.className=s,e.style.cssText="visibility: hidden; display: inline; width: 0px; height: 0px; z-index: -1; overflow: hidden; margin: 0; padding: 0;",r(c).container=e,f(e),c.appendChild(e);var t=function(){r(c).onRendered&&r(c).onRendered()};d(e,"animationstart",t),r(c).onAnimationStart=t}return e}function A(){function e(){var e=r(c).style;if("static"===e.position){c.style.position="relative";var n=function(e,t,n,i){function r(e){return e.replace(/[^-\d\.]/g,"")}var o=n[i];"auto"!==o&&"0"!==r(o)&&(e.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",t),t.style[i]=0)};n(t,c,e,"top"),n(t,c,e,"right"),n(t,c,e,"bottom"),n(t,c,e,"left")}}function n(e,t,n,i){return e=e?e+"px":"0",t=t?t+"px":"0",n=n?n+"px":"0",i=i?i+"px":"0","left: "+e+"; top: "+t+"; right: "+i+"; bottom: "+n+";"}if(l("Injecting elements"),r(c)){e();var i=r(c).container;i||(i=z());var o=a.width,u=a.height,f="position: absolute; flex: none; overflow: hidden; z-index: -1; visibility: hidden; width: 100%; height: 100%; left: 0px; top: 0px;",h="position: absolute; flex: none; overflow: hidden; z-index: -1; visibility: hidden; "+n(-(1+o),-(1+u),-u,-o),p="position: absolute; flex: none; overflow: scroll; z-index: -1; visibility: hidden; width: 100%; height: 100%;",v="position: absolute; flex: none; overflow: scroll; z-index: -1; visibility: hidden; width: 100%; height: 100%;",b="position: absolute; left: 0; top: 0;",g="position: absolute; width: 200%; height: 200%;",y=document.createElement("div"),m=document.createElement("div"),x=document.createElement("div"),w=document.createElement("div"),S=document.createElement("div"),E=document.createElement("div");y.dir="ltr",y.style.cssText=f,y.className=s,m.className=s,m.style.cssText=h,x.style.cssText=p,w.style.cssText=b,S.style.cssText=v,E.style.cssText=g,x.appendChild(w),S.appendChild(E),m.appendChild(x),m.appendChild(S),y.appendChild(m),i.appendChild(y),d(x,"scroll",O),d(S,"scroll",j),r(c).onExpandScroll=O,r(c).onShrinkScroll=j}else l("Aborting because element has been uninstalled");function O(){r(c).onExpand&&r(c).onExpand()}function j(){r(c).onShrink&&r(c).onShrink()}}function _(){function a(e,t,n){var i=S(e),r=j(t),o=k(n);i.style.width=r+"px",i.style.height=o+"px"}function s(i){var s=c.offsetWidth,f=c.offsetHeight;l("Storing current size",s,f),w(c,s,f),n.add(0,function(){if(r(c))if(u()){if(e.debug){var n=c.offsetWidth,i=c.offsetHeight;n===s&&i===f||t.warn(o.get(c),"Scroll: Size changed before updating detector elements.")}a(c,s,f)}else l("Aborting because element container has not been initialized");else l("Aborting because element has been uninstalled")}),n.add(1,function(){r(c)?u()?T(c,s,f):l("Aborting because element container has not been initialized"):l("Aborting because element has been uninstalled")}),i&&n.add(2,function(){r(c)?u()?i():l("Aborting because element container has not been initialized"):l("Aborting because element has been uninstalled")})}function u(){return!!r(c).container}function f(){function e(){return void 0===r(c).lastNotifiedWidth}l("notifyListenersIfNeeded invoked");var t=r(c);return e()&&t.lastWidth===t.startSize.width&&t.lastHeight===t.startSize.height?l("Not notifying: Size is the same as the start size, and there has been no notification yet."):t.lastWidth===t.lastNotifiedWidth&&t.lastHeight===t.lastNotifiedHeight?l("Not notifying: Size already notified"):(l("Current size not notified, notifying..."),t.lastNotifiedWidth=t.lastWidth,t.lastNotifiedHeight=t.lastHeight,void i(r(c).listeners,function(e){e(c)}))}function d(){if(l("startanimation triggered."),b(c))l("Ignoring since element is still unrendered...");else{l("Element rendered.");var e=p(c),t=v(c);0!==e.scrollLeft&&0!==e.scrollTop&&0!==t.scrollLeft&&0!==t.scrollTop||(l("Scrollbars out of sync. Updating detector elements..."),s(f))}}function h(){if(l("Scroll detected."),b(c))l("Scroll event fired while unrendered. Ignoring...");else{var e=c.offsetWidth,t=c.offsetHeight;e!==r(c).lastWidth||t!==r(c).lastHeight?(l("Element size changed."),s(f)):l("Element size has not changed ("+e+"x"+t+").")}}if(l("registerListenersAndPositionElements invoked."),r(c)){r(c).onRendered=d,r(c).onExpand=h,r(c).onShrink=h;var g=r(c).style;a(c,g.width,g.height)}else l("Aborting because element has been uninstalled")}function M(){if(l("finalizeDomMutation invoked."),r(c)){var e=r(c).style;w(c,e.width,e.height),T(c,e.width,e.height)}else l("Aborting because element has been uninstalled")}function H(){u(c)}function P(){l("Installing..."),m(),y(),n.add(0,x),n.add(1,A),n.add(2,_),n.add(3,M),n.add(4,H)}u||(u=c,c=e,e=null),e=e||{},l("Making detectable..."),h(c)?(l("Element is detached"),z(),l("Waiting until element is attached..."),r(c).onRendered=function(){l("Element is now attached"),P()}):P()}function y(e){var t=r(e);t&&(t.onExpandScroll&&h(p(e),"scroll",t.onExpandScroll),t.onShrinkScroll&&h(v(e),"scroll",t.onShrinkScroll),t.onAnimationStart&&h(t.container,"animationstart",t.onAnimationStart),t.container&&e.removeChild(t.container))}return l(c,s),{makeDetectable:g,addListener:b,uninstall:y}}},ca5a:function(e,t){var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},cadf:function(e,t,n){"use strict";var i=n("9c6c"),r=n("d53b"),o=n("84f2"),a=n("6821");e.exports=n("01f9")(Array,"Array",function(e,t){this._t=a(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,r(1)):r(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])},"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},cb7c:function(e,t,n){var i=n("d3f4");e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},ce10:function(e,t,n){var i=n("69a8"),r=n("6821"),o=n("c366")(!1),a=n("613b")("IE_PROTO");e.exports=function(e,t){var n,c=r(e),s=0,u=[];for(n in c)n!=a&&i(c,n)&&u.push(n);while(t.length>s)i(c,n=t[s++])&&(~o(u,n)||u.push(n));return u}},d3f4:function(e,t){e.exports=function(e){return"object"===typeof e?null!==e:"function"===typeof e}},d53b:function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},d6eb:function(e,t,n){"use strict";var i="_erd";function r(e){return e[i]={},o(e)}function o(e){return e[i]}function a(e){delete e[i]}e.exports={initState:r,getState:o,cleanState:a}},d8e8:function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},e11e:function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},eec4:function(e,t,n){"use strict";var i=n("b770").forEach,r=n("5be5"),o=n("49ad"),a=n("2cef"),c=n("5058"),s=n("abb4"),u=n("18e9"),l=n("c274"),f=n("d6eb"),d=n("18d2"),h=n("c946");function p(e){return Array.isArray(e)||void 0!==e.length}function v(e){if(Array.isArray(e))return e;var t=[];return i(e,function(e){t.push(e)}),t}function b(e){return e&&1===e.nodeType}function g(e,t,n){var i=e[t];return void 0!==i&&null!==i||void 0===n?i:n}e.exports=function(e){var t;if(e=e||{},e.idHandler)t={get:function(t){return e.idHandler.get(t,!0)},set:e.idHandler.set};else{var n=a(),y=c({idGenerator:n,stateHandler:f});t=y}var m=e.reporter;if(!m){var x=!1===m;m=s(x)}var w=g(e,"batchProcessor",l({reporter:m})),S={};S.callOnAdd=!!g(e,"callOnAdd",!0),S.debug=!!g(e,"debug",!1);var E,O=o(t),j=r({stateHandler:f}),k=g(e,"strategy","object"),C={reporter:m,batchProcessor:w,stateHandler:f,idHandler:t};if("scroll"===k&&(u.isLegacyOpera()?(m.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy."),k="object"):u.isIE(9)&&(m.warn("Scroll strategy is not supported on IE9. Changing to object strategy."),k="object")),"scroll"===k)E=h(C);else{if("object"!==k)throw new Error("Invalid strategy name: "+k);E=d(C)}var L={};function T(e,n,r){function o(e){var t=O.get(e);i(t,function(t){t(e)})}function a(e,t,n){O.add(t,n),e&&n(t)}if(r||(r=n,n=e,e={}),!n)throw new Error("At least one element required.");if(!r)throw new Error("Listener required.");if(b(n))n=[n];else{if(!p(n))return m.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");n=v(n)}var c=0,s=g(e,"callOnAdd",S.callOnAdd),u=g(e,"onReady",function(){}),l=g(e,"debug",S.debug);i(n,function(e){f.getState(e)||(f.initState(e),t.set(e));var d=t.get(e);if(l&&m.log("Attaching listener to element",d,e),!j.isDetectable(e))return l&&m.log(d,"Not detectable."),j.isBusy(e)?(l&&m.log(d,"System busy making it detectable"),a(s,e,r),L[d]=L[d]||[],void L[d].push(function(){c++,c===n.length&&u()})):(l&&m.log(d,"Making detectable..."),j.markBusy(e,!0),E.makeDetectable({debug:l},e,function(e){if(l&&m.log(d,"onElementDetectable"),f.getState(e)){j.markAsDetectable(e),j.markBusy(e,!1),E.addListener(e,o),a(s,e,r);var t=f.getState(e);if(t&&t.startSize){var h=e.offsetWidth,p=e.offsetHeight;t.startSize.width===h&&t.startSize.height===p||o(e)}L[d]&&i(L[d],function(e){e()})}else l&&m.log(d,"Element uninstalled before being detectable.");delete L[d],c++,c===n.length&&u()}));l&&m.log(d,"Already detecable, adding listener."),a(s,e,r),c++}),c===n.length&&u()}function z(e){if(!e)return m.error("At least one element is required.");if(b(e))e=[e];else{if(!p(e))return m.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");e=v(e)}i(e,function(e){O.removeAllListeners(e),E.uninstall(e),f.cleanState(e)})}return{listenTo:T,removeListener:O.removeListener,removeAllListeners:O.removeAllListeners,uninstall:z}}},fab2:function(e,t,n){var i=n("7726").document;e.exports=i&&i.documentElement},fae3:function(e,t,n){"use strict";n.r(t);n("1eb2");var i=n("1b47"),r=n.n(i);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function c(e,t,n){return t&&a(e.prototype,t),n&&a(e,n),e}var s=n("a307"),u=n.n(s),l=(n("ac6a"),n("cadf"),n("456d"),Object.prototype.hasOwnProperty);function f(e,t){var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(var r=0;r<n.length;r++)if(!l.call(t,n[r])||e[n[r]]!==t[n[r]])return!1;return!0}var d=function(){function e(t,n){var i=this;o(this,e),this.result={},this.rol=new u.a(function(e){var o=r()(t)(e);f(i.result,o)||(n(o),i.result=o)})}return c(e,[{key:"observe",value:function(e){this.rol.observe(e)}},{key:"disconnect",value:function(){this.rol.disconnect()}}]),e}(),h={methods:{handleChange:function(){throw new Error("Method `handleChange()` not implemented.")},disposeObserver:function(){this.cqCore&&this.cqCore.disconnect(),this.cqCore=null},startObserving:function(e){var t=this;this.cqCore=new d(e,function(e){t.handleChange(e)});var n=this.$el||this.$slots.default[0].elm;this.cqCore.observe(n)}},created:function(){var e=this.query,t=this.initialSize,n=t?r()(e)(t):{};this.handleChange(n)},mounted:function(){this.startObserving(this.query)},destroyed:function(){this.disposeObserver()}},p=Object.prototype.hasOwnProperty;function v(e,t){var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(var r=0;r<n.length;r++)if(!p.call(t,n[r])||!f(e[n[r]],t[n[r]]))return!1;return!0}var b="<vue-Container-query> can only render one, and exactly one child component",g={name:"vue-container-query",mixins:[h],model:{event:"change"},props:{initialSize:{type:Object},query:{required:!0,type:Object}},methods:{handleChange:function(e){this.$emit("change",e)}},watch:{query:{deep:!0,immediate:!0,handler:function(e,t){this.cqCore&&!v(t,e)&&(this.disposeObserver(),this.startObserving(e))}}},render:function(){var e=this.$slots.default;if(e&&1===e.length)return e[0];throw new Error(b)}};function y(e,t){return{mixins:[h],data:function(){return{query:e,initialSize:t,containerQuery:{}}},methods:{handleChange:function(e){this.containerQuery=e}}}}n.d(t,"ContainerQuery",function(){return g}),n.d(t,"createContainerQueryMixin",function(){return y})}})});
