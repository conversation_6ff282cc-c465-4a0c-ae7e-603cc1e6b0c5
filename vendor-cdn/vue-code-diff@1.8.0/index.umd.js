(function(){"use strict";try{if(typeof document!="undefined"){var o=document.createElement("style");o.appendChild(document.createTextNode(".code-diff-view[theme=light]{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));--color-canvas-default-transparent: rgba(255,255,255,0);--color-page-header-bg: #f6f8fa;--color-marketing-icon-primary: #218bff;--color-marketing-icon-secondary: #54aeff;--color-diff-blob-addition-num-text: #1F2328;--color-diff-blob-addition-fg: #1F2328;--color-diff-blob-addition-num-bg: #ccffd8;--color-diff-blob-addition-line-bg: #e6ffec;--color-diff-blob-addition-word-bg: #abf2bc;--color-diff-blob-deletion-num-text: #1F2328;--color-diff-blob-deletion-fg: #1F2328;--color-diff-blob-deletion-num-bg: #ffd7d5;--color-diff-blob-deletion-line-bg: #ffebe9;--color-diff-blob-deletion-word-bg: rgba(255,129,130,.4);--color-diff-blob-hunk-num-bg: rgba(84,174,255,.4);--color-diff-blob-expander-icon: #656d76;--color-diff-blob-selected-line-highlight-mix-blend-mode: multiply;--color-diffstat-deletion-border: rgba(31,35,40,.15);--color-diffstat-addition-border: rgba(31,35,40,.15);--color-diffstat-addition-bg: #1f883d;--color-search-keyword-hl: #fff8c5;--color-prettylights-syntax-comment: #6e7781;--color-prettylights-syntax-constant: #0550ae;--color-prettylights-syntax-entity: #6639ba;--color-prettylights-syntax-storage-modifier-import: #24292f;--color-prettylights-syntax-entity-tag: #116329;--color-prettylights-syntax-keyword: #cf222e;--color-prettylights-syntax-string: #0a3069;--color-prettylights-syntax-variable: #953800;--color-prettylights-syntax-brackethighlighter-unmatched: #82071e;--color-prettylights-syntax-invalid-illegal-text: #f6f8fa;--color-prettylights-syntax-invalid-illegal-bg: #82071e;--color-prettylights-syntax-carriage-return-text: #f6f8fa;--color-prettylights-syntax-carriage-return-bg: #cf222e;--color-prettylights-syntax-string-regexp: #116329;--color-prettylights-syntax-markup-list: #3b2300;--color-prettylights-syntax-markup-heading: #0550ae;--color-prettylights-syntax-markup-italic: #24292f;--color-prettylights-syntax-markup-bold: #24292f;--color-prettylights-syntax-markup-deleted-text: #82071e;--color-prettylights-syntax-markup-deleted-bg: #ffebe9;--color-prettylights-syntax-markup-inserted-text: #116329;--color-prettylights-syntax-markup-inserted-bg: #dafbe1;--color-prettylights-syntax-markup-changed-text: #953800;--color-prettylights-syntax-markup-changed-bg: #ffd8b5;--color-prettylights-syntax-markup-ignored-text: #eaeef2;--color-prettylights-syntax-markup-ignored-bg: #0550ae;--color-prettylights-syntax-meta-diff-range: #8250df;--color-prettylights-syntax-brackethighlighter-angle: #57606a;--color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;--color-prettylights-syntax-constant-other-reference-link: #0a3069;--color-codemirror-text: #1F2328;--color-codemirror-bg: #ffffff;--color-codemirror-gutters-bg: #ffffff;--color-codemirror-guttermarker-text: #ffffff;--color-codemirror-guttermarker-subtle-text: #6e7781;--color-codemirror-linenumber-text: #656d76;--color-codemirror-cursor: #1F2328;--color-codemirror-selection-bg: rgba(84,174,255,.4);--color-codemirror-activeline-bg: rgba(234,238,242,.5);--color-codemirror-matchingbracket-text: #1F2328;--color-codemirror-lines-bg: #ffffff;--color-codemirror-syntax-comment: #24292f;--color-codemirror-syntax-constant: #0550ae;--color-codemirror-syntax-entity: #8250df;--color-codemirror-syntax-keyword: #cf222e;--color-codemirror-syntax-storage: #cf222e;--color-codemirror-syntax-string: #0a3069;--color-codemirror-syntax-support: #0550ae;--color-codemirror-syntax-variable: #953800;--color-checks-bg: #24292f;--color-checks-run-border-width: 0px;--color-checks-container-border-width: 0px;--color-checks-text-primary: #f6f8fa;--color-checks-text-secondary: #8c959f;--color-checks-text-link: #54aeff;--color-checks-btn-icon: #afb8c1;--color-checks-btn-hover-icon: #f6f8fa;--color-checks-btn-hover-bg: rgba(255,255,255,.125);--color-checks-input-text: #eaeef2;--color-checks-input-placeholder-text: #8c959f;--color-checks-input-focus-text: #8c959f;--color-checks-input-bg: #32383f;--color-checks-input-shadow: none;--color-checks-donut-error: #fa4549;--color-checks-donut-pending: #bf8700;--color-checks-donut-success: #1f883d;--color-checks-donut-neutral: #afb8c1;--color-checks-dropdown-text: #afb8c1;--color-checks-dropdown-bg: #32383f;--color-checks-dropdown-border: #424a53;--color-checks-dropdown-shadow: rgba(31,35,40,.3);--color-checks-dropdown-hover-text: #f6f8fa;--color-checks-dropdown-hover-bg: #424a53;--color-checks-dropdown-btn-hover-text: #f6f8fa;--color-checks-dropdown-btn-hover-bg: #32383f;--color-checks-scrollbar-thumb-bg: #57606a;--color-checks-header-label-text: #d0d7de;--color-checks-header-label-open-text: #f6f8fa;--color-checks-header-border: #32383f;--color-checks-header-icon: #8c959f;--color-checks-line-text: #d0d7de;--color-checks-line-num-text: rgba(140,149,159,.75);--color-checks-line-timestamp-text: #8c959f;--color-checks-line-hover-bg: #32383f;--color-checks-line-selected-bg: rgba(33,139,255,.15);--color-checks-line-selected-num-text: #54aeff;--color-checks-line-dt-fm-text: #24292f;--color-checks-line-dt-fm-bg: #9a6700;--color-checks-gate-bg: rgba(125,78,0,.15);--color-checks-gate-text: #d0d7de;--color-checks-gate-waiting-text: #d4a72c;--color-checks-step-header-open-bg: #32383f;--color-checks-step-error-text: #ff8182;--color-checks-step-warning-text: #d4a72c;--color-checks-logline-text: #8c959f;--color-checks-logline-num-text: rgba(140,149,159,.75);--color-checks-logline-debug-text: #c297ff;--color-checks-logline-error-text: #d0d7de;--color-checks-logline-error-num-text: #ff8182;--color-checks-logline-error-bg: rgba(164,14,38,.15);--color-checks-logline-warning-text: #d0d7de;--color-checks-logline-warning-num-text: #d4a72c;--color-checks-logline-warning-bg: rgba(125,78,0,.15);--color-checks-logline-command-text: #54aeff;--color-checks-logline-section-text: #4ac26b;--color-checks-ansi-black: #24292f;--color-checks-ansi-black-bright: #32383f;--color-checks-ansi-white: #d0d7de;--color-checks-ansi-white-bright: #d0d7de;--color-checks-ansi-gray: #8c959f;--color-checks-ansi-red: #ff8182;--color-checks-ansi-red-bright: #ffaba8;--color-checks-ansi-green: #4ac26b;--color-checks-ansi-green-bright: #6fdd8b;--color-checks-ansi-yellow: #d4a72c;--color-checks-ansi-yellow-bright: #eac54f;--color-checks-ansi-blue: #54aeff;--color-checks-ansi-blue-bright: #80ccff;--color-checks-ansi-magenta: #c297ff;--color-checks-ansi-magenta-bright: #d8b9ff;--color-checks-ansi-cyan: #76e3ea;--color-checks-ansi-cyan-bright: #b3f0ff;--color-project-header-bg: #24292f;--color-project-sidebar-bg: #ffffff;--color-project-gradient-in: #ffffff;--color-project-gradient-out: rgba(255,255,255,0);--color-mktg-btn-bg: #1b1f23;--color-mktg-btn-shadow-outline: rgb(0 0 0 / 15%) 0 0 0 1px inset;--color-mktg-btn-shadow-focus: rgb(0 0 0 / 15%) 0 0 0 4px;--color-mktg-btn-shadow-hover: 0 3px 2px rgba(0, 0, 0, .07), 0 7px 5px rgba(0, 0, 0, .04), 0 12px 10px rgba(0, 0, 0, .03), 0 22px 18px rgba(0, 0, 0, .03), 0 42px 33px rgba(0, 0, 0, .02), 0 100px 80px rgba(0, 0, 0, .02);--color-mktg-btn-shadow-hover-muted: rgb(0 0 0 / 70%) 0 0 0 2px inset;--color-control-border-color-emphasis: #858F99;--color-avatar-bg: #ffffff;--color-avatar-border: rgba(31,35,40,.15);--color-avatar-stack-fade: #afb8c1;--color-avatar-stack-fade-more: #d0d7de;--color-avatar-child-shadow: 0 0 0 2px rgba(255,255,255,.8);--color-topic-tag-border: rgba(0,0,0,0);--color-counter-border: rgba(0,0,0,0);--color-select-menu-backdrop-border: rgba(0,0,0,0);--color-select-menu-tap-highlight: rgba(175,184,193,.5);--color-select-menu-tap-focus-bg: #b6e3ff;--color-overlay-shadow: 0 1px 3px rgba(31,35,40,.12), 0 8px 24px rgba(66,74,83,.12);--color-overlay-backdrop: rgba(140,149,159,.2);--color-header-text: rgba(255,255,255,.7);--color-header-bg: #24292f;--color-header-divider: #57606a;--color-header-logo: #ffffff;--color-header-search-bg: #24292f;--color-header-search-border: #57606a;--color-sidenav-selected-bg: #ffffff;--color-menu-bg-active: rgba(0,0,0,0);--color-input-disabled-bg: rgba(175,184,193,.2);--color-timeline-badge-bg: #eaeef2;--color-ansi-black: #24292f;--color-ansi-black-bright: #57606a;--color-ansi-white: #6e7781;--color-ansi-white-bright: #8c959f;--color-ansi-gray: #6e7781;--color-ansi-red: #cf222e;--color-ansi-red-bright: #a40e26;--color-ansi-green: #116329;--color-ansi-green-bright: #1a7f37;--color-ansi-yellow: #4d2d00;--color-ansi-yellow-bright: #633c01;--color-ansi-blue: #0969da;--color-ansi-blue-bright: #218bff;--color-ansi-magenta: #8250df;--color-ansi-magenta-bright: #a475f9;--color-ansi-cyan: #1b7c83;--color-ansi-cyan-bright: #3192aa;--color-btn-text: #24292f;--color-btn-bg: #f6f8fa;--color-btn-border: rgba(31,35,40,.15);--color-btn-shadow: 0 1px 0 rgba(31,35,40,.04);--color-btn-inset-shadow: inset 0 1px 0 rgba(255,255,255,.25);--color-btn-hover-bg: #f3f4f6;--color-btn-hover-border: rgba(31,35,40,.15);--color-btn-active-bg: hsla(220,14%,93%,1);--color-btn-active-border: rgba(31,35,40,.15);--color-btn-selected-bg: hsla(220,14%,94%,1);--color-btn-counter-bg: rgba(31,35,40,.08);--color-btn-primary-text: #ffffff;--color-btn-primary-bg: #1f883d;--color-btn-primary-border: rgba(31,35,40,.15);--color-btn-primary-shadow: 0 1px 0 rgba(31,35,40,.1);--color-btn-primary-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-primary-hover-bg: #1a7f37;--color-btn-primary-hover-border: rgba(31,35,40,.15);--color-btn-primary-selected-bg: hsla(137,66%,28%,1);--color-btn-primary-selected-shadow: inset 0 1px 0 rgba(0,45,17,.2);--color-btn-primary-disabled-text: rgba(255,255,255,.8);--color-btn-primary-disabled-bg: #94d3a2;--color-btn-primary-disabled-border: rgba(31,35,40,.15);--color-btn-primary-icon: rgba(255,255,255,.8);--color-btn-primary-counter-bg: rgba(0,45,17,.2);--color-btn-outline-text: #0969da;--color-btn-outline-hover-text: #ffffff;--color-btn-outline-hover-bg: #0969da;--color-btn-outline-hover-border: rgba(31,35,40,.15);--color-btn-outline-hover-shadow: 0 1px 0 rgba(31,35,40,.1);--color-btn-outline-hover-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-outline-hover-counter-bg: rgba(255,255,255,.2);--color-btn-outline-selected-text: #ffffff;--color-btn-outline-selected-bg: hsla(212,92%,42%,1);--color-btn-outline-selected-border: rgba(31,35,40,.15);--color-btn-outline-selected-shadow: inset 0 1px 0 rgba(0,33,85,.2);--color-btn-outline-disabled-text: rgba(9,105,218,.5);--color-btn-outline-disabled-bg: #f6f8fa;--color-btn-outline-disabled-counter-bg: rgba(9,105,218,.05);--color-btn-outline-counter-bg: #0969da1a;--color-btn-outline-counter-fg: #0550ae;--color-btn-outline-hover-counter-fg: #ffffff;--color-btn-outline-disabled-counter-fg: rgba(9,105,218,.5);--color-btn-danger-text: #cf222e;--color-btn-danger-hover-text: #ffffff;--color-btn-danger-hover-bg: #a40e26;--color-btn-danger-hover-border: rgba(31,35,40,.15);--color-btn-danger-hover-shadow: 0 1px 0 rgba(31,35,40,.1);--color-btn-danger-hover-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-danger-hover-counter-bg: rgba(255,255,255,.2);--color-btn-danger-selected-text: #ffffff;--color-btn-danger-selected-bg: hsla(356,72%,44%,1);--color-btn-danger-selected-border: rgba(31,35,40,.15);--color-btn-danger-selected-shadow: inset 0 1px 0 rgba(76,0,20,.2);--color-btn-danger-disabled-text: rgba(207,34,46,.5);--color-btn-danger-disabled-bg: #f6f8fa;--color-btn-danger-disabled-counter-bg: rgba(207,34,46,.05);--color-btn-danger-counter-bg: rgba(207,34,46,.1);--color-btn-danger-icon: #cf222e;--color-btn-danger-hover-icon: #ffffff;--color-btn-danger-counter-fg: #a40e26;--color-btn-danger-hover-counter-fg: #ffffff;--color-btn-danger-disabled-counter-fg: rgba(207,34,46,.5);--color-underlinenav-icon: #6e7781;--color-underlinenav-border-hover: rgba(175,184,193,.2);--color-action-list-item-inline-divider: rgba(208,215,222,.48);--color-action-list-item-default-hover-bg: rgba(208,215,222,.32);--color-action-list-item-default-hover-border: rgba(0,0,0,0);--color-action-list-item-default-active-bg: rgba(208,215,222,.48);--color-action-list-item-default-active-border: rgba(0,0,0,0);--color-action-list-item-default-selected-bg: rgba(208,215,222,.24);--color-action-list-item-danger-hover-bg: rgba(255,235,233,.64);--color-action-list-item-danger-active-bg: #ffebe9;--color-action-list-item-danger-hover-text: #d1242f;--color-switch-track-bg: #eaeef2;--color-switch-track-hover-bg: hsla(210,24%,90%,1);--color-switch-track-active-bg: hsla(210,24%,88%,1);--color-switch-track-disabled-bg: #8c959f;--color-switch-track-fg: #656d76;--color-switch-track-disabled-fg: #ffffff;--color-switch-track-border: rgba(0,0,0,0);--color-switch-track-checked-bg: #0969da;--color-switch-track-checked-hover-bg: #0860CA;--color-switch-track-checked-active-bg: #0757BA;--color-switch-track-checked-fg: #ffffff;--color-switch-track-checked-disabled-fg: #ffffff;--color-switch-track-checked-border: rgba(0,0,0,0);--color-switch-knob-bg: #ffffff;--color-switch-knob-disabled-bg: #f6f8fa;--color-switch-knob-border: #858F99;--color-switch-knob-checked-bg: #ffffff;--color-switch-knob-checked-disabled-bg: #f6f8fa;--color-switch-knob-checked-border: #0969da;--color-segmented-control-bg: #eaeef2;--color-segmented-control-button-bg: #ffffff;--color-segmented-control-button-hover-bg: rgba(175,184,193,.2);--color-segmented-control-button-active-bg: rgba(175,184,193,.4);--color-segmented-control-button-selected-border: #8c959f;--color-tree-view-item-chevron-hover-bg: rgba(208,215,222,.32);--color-tree-view-item-directory-fill: #54aeff;--color-fg-default: #1F2328;--color-fg-muted: #656d76;--color-fg-subtle: #6e7781;--color-fg-on-emphasis: #ffffff;--color-canvas-default: #ffffff;--color-canvas-overlay: #ffffff;--color-canvas-inset: #f6f8fa;--color-canvas-subtle: #f6f8fa;--color-border-default: #d0d7de;--color-border-muted: hsla(210,18%,87%,1);--color-border-subtle: rgba(31,35,40,.15);--color-shadow-small: 0 1px 0 rgba(31,35,40,.04);--color-shadow-medium: 0 3px 6px rgba(140,149,159,.15);--color-shadow-large: 0 8px 24px rgba(140,149,159,.2);--color-shadow-extra-large: 0 12px 28px rgba(140,149,159,.3);--color-neutral-emphasis-plus: #24292f;--color-neutral-emphasis: #6e7781;--color-neutral-muted: rgba(175,184,193,.2);--color-neutral-subtle: rgba(234,238,242,.5);--color-accent-fg: #0969da;--color-accent-emphasis: #0969da;--color-accent-muted: rgba(84,174,255,.4);--color-accent-subtle: #ddf4ff;--color-success-fg: #1a7f37;--color-success-emphasis: #1f883d;--color-success-muted: rgba(74,194,107,.4);--color-success-subtle: #dafbe1;--color-attention-fg: #9a6700;--color-attention-emphasis: #9a6700;--color-attention-muted: rgba(212,167,44,.4);--color-attention-subtle: #fff8c5;--color-severe-fg: #bc4c00;--color-severe-emphasis: #bc4c00;--color-severe-muted: rgba(251,143,68,.4);--color-severe-subtle: #fff1e5;--color-danger-fg: #d1242f;--color-danger-emphasis: #cf222e;--color-danger-muted: rgba(255,129,130,.4);--color-danger-subtle: #ffebe9;--color-open-fg: #1a7f37;--color-open-emphasis: #1f883d;--color-open-muted: rgba(74,194,107,.4);--color-open-subtle: #dafbe1;--color-closed-fg: #d1242f;--color-closed-emphasis: #cf222e;--color-closed-muted: rgba(255,129,130,.4);--color-closed-subtle: #ffebe9;--color-done-fg: #8250df;--color-done-emphasis: #8250df;--color-done-muted: rgba(194,151,255,.4);--color-done-subtle: #fbefff;--color-sponsors-fg: #bf3989;--color-sponsors-emphasis: #bf3989;--color-sponsors-muted: rgba(255,128,200,.4);--color-sponsors-subtle: #ffeff7;--color-primer-fg-disabled: #8c959f;--color-primer-canvas-backdrop: rgba(31,35,40,.5);--color-primer-canvas-sticky: rgba(255,255,255,.95);--color-primer-border-active: #fd8c73;--color-primer-border-contrast: rgba(31,35,40,.1);--color-primer-shadow-highlight: inset 0 1px 0 rgba(255,255,255,.25);--color-primer-shadow-inset: inset 0 1px 0 rgba(208,215,222,.2);--color-scale-black: #1F2328;--color-scale-white: #ffffff;--color-scale-gray-0: #f6f8fa;--color-scale-gray-1: #eaeef2;--color-scale-gray-2: #d0d7de;--color-scale-gray-3: #afb8c1;--color-scale-gray-4: #8c959f;--color-scale-gray-5: #6e7781;--color-scale-gray-6: #57606a;--color-scale-gray-7: #424a53;--color-scale-gray-8: #32383f;--color-scale-gray-9: #24292f;--color-scale-blue-0: #ddf4ff;--color-scale-blue-1: #b6e3ff;--color-scale-blue-2: #80ccff;--color-scale-blue-3: #54aeff;--color-scale-blue-4: #218bff;--color-scale-blue-5: #0969da;--color-scale-blue-6: #0550ae;--color-scale-blue-7: #033d8b;--color-scale-blue-8: #0a3069;--color-scale-blue-9: #002155;--color-scale-green-0: #dafbe1;--color-scale-green-1: #aceebb;--color-scale-green-2: #6fdd8b;--color-scale-green-3: #4ac26b;--color-scale-green-4: #2da44e;--color-scale-green-5: #1a7f37;--color-scale-green-6: #116329;--color-scale-green-7: #044f1e;--color-scale-green-8: #003d16;--color-scale-green-9: #002d11;--color-scale-yellow-0: #fff8c5;--color-scale-yellow-1: #fae17d;--color-scale-yellow-2: #eac54f;--color-scale-yellow-3: #d4a72c;--color-scale-yellow-4: #bf8700;--color-scale-yellow-5: #9a6700;--color-scale-yellow-6: #7d4e00;--color-scale-yellow-7: #633c01;--color-scale-yellow-8: #4d2d00;--color-scale-yellow-9: #3b2300;--color-scale-orange-0: #fff1e5;--color-scale-orange-1: #ffd8b5;--color-scale-orange-2: #ffb77c;--color-scale-orange-3: #fb8f44;--color-scale-orange-4: #e16f24;--color-scale-orange-5: #bc4c00;--color-scale-orange-6: #953800;--color-scale-orange-7: #762c00;--color-scale-orange-8: #5c2200;--color-scale-orange-9: #471700;--color-scale-red-0: #ffebe9;--color-scale-red-1: #ffcecb;--color-scale-red-2: #ffaba8;--color-scale-red-3: #ff8182;--color-scale-red-4: #fa4549;--color-scale-red-5: #cf222e;--color-scale-red-6: #a40e26;--color-scale-red-7: #82071e;--color-scale-red-8: #660018;--color-scale-red-9: #4c0014;--color-scale-purple-0: #fbefff;--color-scale-purple-1: #ecd8ff;--color-scale-purple-2: #d8b9ff;--color-scale-purple-3: #c297ff;--color-scale-purple-4: #a475f9;--color-scale-purple-5: #8250df;--color-scale-purple-6: #6639ba;--color-scale-purple-7: #512a97;--color-scale-purple-8: #3e1f79;--color-scale-purple-9: #2e1461;--color-scale-pink-0: #ffeff7;--color-scale-pink-1: #ffd3eb;--color-scale-pink-2: #ffadda;--color-scale-pink-3: #ff80c8;--color-scale-pink-4: #e85aad;--color-scale-pink-5: #bf3989;--color-scale-pink-6: #99286e;--color-scale-pink-7: #772057;--color-scale-pink-8: #611347;--color-scale-pink-9: #4d0336;--color-scale-coral-0: #fff0eb;--color-scale-coral-1: #ffd6cc;--color-scale-coral-2: #ffb4a1;--color-scale-coral-3: #fd8c73;--color-scale-coral-4: #ec6547;--color-scale-coral-5: #c4432b;--color-scale-coral-6: #9e2f1c;--color-scale-coral-7: #801f0f;--color-scale-coral-8: #691105;--color-scale-coral-9: #510901}.code-diff-view[theme=light] pre code.hljs{display:block;overflow-x:auto;padding:1em}.code-diff-view[theme=light] code.hljs{padding:3px 5px}.code-diff-view[theme=light] .hljs{color:#24292e;background:#ffffff}.code-diff-view[theme=light] .hljs-doctag,.code-diff-view[theme=light] .hljs-keyword,.code-diff-view[theme=light] .hljs-meta .hljs-keyword,.code-diff-view[theme=light] .hljs-template-tag,.code-diff-view[theme=light] .hljs-template-variable,.code-diff-view[theme=light] .hljs-type,.code-diff-view[theme=light] .hljs-variable.language_{color:#d73a49}.code-diff-view[theme=light] .hljs-title,.code-diff-view[theme=light] .hljs-title.class_,.code-diff-view[theme=light] .hljs-title.class_.inherited__,.code-diff-view[theme=light] .hljs-title.function_{color:#6f42c1}.code-diff-view[theme=light] .hljs-attr,.code-diff-view[theme=light] .hljs-attribute,.code-diff-view[theme=light] .hljs-literal,.code-diff-view[theme=light] .hljs-meta,.code-diff-view[theme=light] .hljs-number,.code-diff-view[theme=light] .hljs-operator,.code-diff-view[theme=light] .hljs-variable,.code-diff-view[theme=light] .hljs-selector-attr,.code-diff-view[theme=light] .hljs-selector-class,.code-diff-view[theme=light] .hljs-selector-id{color:#005cc5}.code-diff-view[theme=light] .hljs-regexp,.code-diff-view[theme=light] .hljs-string,.code-diff-view[theme=light] .hljs-meta .hljs-string{color:#032f62}.code-diff-view[theme=light] .hljs-built_in,.code-diff-view[theme=light] .hljs-symbol{color:#e36209}.code-diff-view[theme=light] .hljs-comment,.code-diff-view[theme=light] .hljs-code,.code-diff-view[theme=light] .hljs-formula{color:#6a737d}.code-diff-view[theme=light] .hljs-name,.code-diff-view[theme=light] .hljs-quote,.code-diff-view[theme=light] .hljs-selector-tag,.code-diff-view[theme=light] .hljs-selector-pseudo{color:#22863a}.code-diff-view[theme=light] .hljs-subst{color:#24292e}.code-diff-view[theme=light] .hljs-section{color:#005cc5;font-weight:700}.code-diff-view[theme=light] .hljs-bullet{color:#735c0f}.code-diff-view[theme=light] .hljs-emphasis{color:#24292e;font-style:italic}.code-diff-view[theme=light] .hljs-strong{color:#24292e;font-weight:700}.code-diff-view[theme=light] .hljs-addition{color:#22863a;background-color:#f0fff4}.code-diff-view[theme=light] .hljs-deletion{color:#b31d28;background-color:#ffeef0}.code-diff-view[theme=dark]{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));--color-canvas-default-transparent: rgba(13,17,23,0);--color-page-header-bg: #0d1117;--color-marketing-icon-primary: #79c0ff;--color-marketing-icon-secondary: #1f6feb;--color-diff-blob-addition-num-text: #e6edf3;--color-diff-blob-addition-fg: #e6edf3;--color-diff-blob-addition-num-bg: rgba(63,185,80,.3);--color-diff-blob-addition-line-bg: rgba(46,160,67,.15);--color-diff-blob-addition-word-bg: rgba(46,160,67,.4);--color-diff-blob-deletion-num-text: #e6edf3;--color-diff-blob-deletion-fg: #e6edf3;--color-diff-blob-deletion-num-bg: rgba(248,81,73,.3);--color-diff-blob-deletion-line-bg: rgba(248,81,73,.1);--color-diff-blob-deletion-word-bg: rgba(248,81,73,.4);--color-diff-blob-hunk-num-bg: rgba(56,139,253,.4);--color-diff-blob-expander-icon: #7d8590;--color-diff-blob-selected-line-highlight-mix-blend-mode: screen;--color-diffstat-deletion-border: rgba(240,246,252,.1);--color-diffstat-addition-border: rgba(240,246,252,.1);--color-diffstat-addition-bg: #3fb950;--color-search-keyword-hl: rgba(210,153,34,.4);--color-prettylights-syntax-comment: #8b949e;--color-prettylights-syntax-constant: #79c0ff;--color-prettylights-syntax-entity: #d2a8ff;--color-prettylights-syntax-storage-modifier-import: #c9d1d9;--color-prettylights-syntax-entity-tag: #7ee787;--color-prettylights-syntax-keyword: #ff7b72;--color-prettylights-syntax-string: #a5d6ff;--color-prettylights-syntax-variable: #ffa657;--color-prettylights-syntax-brackethighlighter-unmatched: #f85149;--color-prettylights-syntax-invalid-illegal-text: #f0f6fc;--color-prettylights-syntax-invalid-illegal-bg: #8e1519;--color-prettylights-syntax-carriage-return-text: #f0f6fc;--color-prettylights-syntax-carriage-return-bg: #b62324;--color-prettylights-syntax-string-regexp: #7ee787;--color-prettylights-syntax-markup-list: #f2cc60;--color-prettylights-syntax-markup-heading: #1f6feb;--color-prettylights-syntax-markup-italic: #c9d1d9;--color-prettylights-syntax-markup-bold: #c9d1d9;--color-prettylights-syntax-markup-deleted-text: #ffdcd7;--color-prettylights-syntax-markup-deleted-bg: #67060c;--color-prettylights-syntax-markup-inserted-text: #aff5b4;--color-prettylights-syntax-markup-inserted-bg: #033a16;--color-prettylights-syntax-markup-changed-text: #ffdfb6;--color-prettylights-syntax-markup-changed-bg: #5a1e02;--color-prettylights-syntax-markup-ignored-text: #c9d1d9;--color-prettylights-syntax-markup-ignored-bg: #1158c7;--color-prettylights-syntax-meta-diff-range: #d2a8ff;--color-prettylights-syntax-brackethighlighter-angle: #8b949e;--color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;--color-prettylights-syntax-constant-other-reference-link: #a5d6ff;--color-codemirror-text: #e6edf3;--color-codemirror-bg: #0d1117;--color-codemirror-gutters-bg: #0d1117;--color-codemirror-guttermarker-text: #0d1117;--color-codemirror-guttermarker-subtle-text: #6e7681;--color-codemirror-linenumber-text: #7d8590;--color-codemirror-cursor: #e6edf3;--color-codemirror-selection-bg: rgba(56,139,253,.4);--color-codemirror-activeline-bg: rgba(110,118,129,.1);--color-codemirror-matchingbracket-text: #e6edf3;--color-codemirror-lines-bg: #0d1117;--color-codemirror-syntax-comment: #8b949e;--color-codemirror-syntax-constant: #79c0ff;--color-codemirror-syntax-entity: #d2a8ff;--color-codemirror-syntax-keyword: #ff7b72;--color-codemirror-syntax-storage: #ff7b72;--color-codemirror-syntax-string: #a5d6ff;--color-codemirror-syntax-support: #79c0ff;--color-codemirror-syntax-variable: #ffa657;--color-checks-bg: #010409;--color-checks-run-border-width: 1px;--color-checks-container-border-width: 1px;--color-checks-text-primary: #e6edf3;--color-checks-text-secondary: #7d8590;--color-checks-text-link: #2f81f7;--color-checks-btn-icon: #7d8590;--color-checks-btn-hover-icon: #e6edf3;--color-checks-btn-hover-bg: rgba(110,118,129,.1);--color-checks-input-text: #7d8590;--color-checks-input-placeholder-text: #6e7681;--color-checks-input-focus-text: #e6edf3;--color-checks-input-bg: #161b22;--color-checks-donut-error: #f85149;--color-checks-donut-pending: #d29922;--color-checks-donut-success: #2ea043;--color-checks-donut-neutral: #8b949e;--color-checks-dropdown-text: #e6edf3;--color-checks-dropdown-bg: #161b22;--color-checks-dropdown-border: #30363d;--color-checks-dropdown-shadow: rgba(1,4,9,.3);--color-checks-dropdown-hover-text: #e6edf3;--color-checks-dropdown-hover-bg: rgba(110,118,129,.1);--color-checks-dropdown-btn-hover-text: #e6edf3;--color-checks-dropdown-btn-hover-bg: rgba(110,118,129,.1);--color-checks-scrollbar-thumb-bg: rgba(110,118,129,.4);--color-checks-header-label-text: #7d8590;--color-checks-header-label-open-text: #e6edf3;--color-checks-header-border: #21262d;--color-checks-header-icon: #7d8590;--color-checks-line-text: #7d8590;--color-checks-line-num-text: #6e7681;--color-checks-line-timestamp-text: #6e7681;--color-checks-line-hover-bg: rgba(110,118,129,.1);--color-checks-line-selected-bg: rgba(56,139,253,.1);--color-checks-line-selected-num-text: #2f81f7;--color-checks-line-dt-fm-text: #ffffff;--color-checks-line-dt-fm-bg: #9e6a03;--color-checks-gate-bg: rgba(187,128,9,.15);--color-checks-gate-text: #7d8590;--color-checks-gate-waiting-text: #d29922;--color-checks-step-header-open-bg: #161b22;--color-checks-step-error-text: #f85149;--color-checks-step-warning-text: #d29922;--color-checks-logline-text: #7d8590;--color-checks-logline-num-text: #6e7681;--color-checks-logline-debug-text: #a371f7;--color-checks-logline-error-text: #7d8590;--color-checks-logline-error-num-text: #6e7681;--color-checks-logline-error-bg: rgba(248,81,73,.1);--color-checks-logline-warning-text: #7d8590;--color-checks-logline-warning-num-text: #d29922;--color-checks-logline-warning-bg: rgba(187,128,9,.15);--color-checks-logline-command-text: #2f81f7;--color-checks-logline-section-text: #3fb950;--color-checks-ansi-black: #0d1117;--color-checks-ansi-black-bright: #161b22;--color-checks-ansi-white: #b1bac4;--color-checks-ansi-white-bright: #b1bac4;--color-checks-ansi-gray: #6e7681;--color-checks-ansi-red: #ff7b72;--color-checks-ansi-red-bright: #ffa198;--color-checks-ansi-green: #3fb950;--color-checks-ansi-green-bright: #56d364;--color-checks-ansi-yellow: #d29922;--color-checks-ansi-yellow-bright: #e3b341;--color-checks-ansi-blue: #58a6ff;--color-checks-ansi-blue-bright: #79c0ff;--color-checks-ansi-magenta: #bc8cff;--color-checks-ansi-magenta-bright: #d2a8ff;--color-checks-ansi-cyan: #76e3ea;--color-checks-ansi-cyan-bright: #b3f0ff;--color-project-header-bg: #0d1117;--color-project-sidebar-bg: #161b22;--color-project-gradient-in: #161b22;--color-project-gradient-out: rgba(22,27,34,0);--color-mktg-btn-bg: #f6f8fa;--color-mktg-btn-shadow-outline: rgb(255 255 255 / 25%) 0 0 0 1px inset;--color-mktg-btn-shadow-focus: rgb(255 255 255 / 25%) 0 0 0 4px;--color-mktg-btn-shadow-hover: 0 4px 7px rgba(0, 0, 0, .15), 0 100px 80px rgba(255, 255, 255, .02), 0 42px 33px rgba(255, 255, 255, .024), 0 22px 18px rgba(255, 255, 255, .028), 0 12px 10px rgba(255, 255, 255, .034), 0 7px 5px rgba(255, 255, 255, .04), 0 3px 2px rgba(255, 255, 255, .07);--color-mktg-btn-shadow-hover-muted: rgb(255 255 255) 0 0 0 2px inset;--color-control-border-color-emphasis: #606771;--color-avatar-bg: rgba(255,255,255,.1);--color-avatar-border: rgba(240,246,252,.1);--color-avatar-stack-fade: #30363d;--color-avatar-stack-fade-more: #21262d;--color-avatar-child-shadow: 0 0 0 2px #0d1117;--color-topic-tag-border: rgba(0,0,0,0);--color-counter-border: rgba(0,0,0,0);--color-select-menu-backdrop-border: #484f58;--color-select-menu-tap-highlight: rgba(48,54,61,.5);--color-select-menu-tap-focus-bg: #0c2d6b;--color-overlay-shadow: 0 0 0 1px #30363d, 0 16px 32px rgba(1,4,9,.85);--color-overlay-backdrop: rgba(22,27,34,.4);--color-header-text: rgba(255,255,255,.7);--color-header-bg: #161b22;--color-header-divider: #8b949e;--color-header-logo: #f0f6fc;--color-header-search-bg: #0d1117;--color-header-search-border: #30363d;--color-sidenav-selected-bg: #21262d;--color-menu-bg-active: #161b22;--color-input-disabled-bg: rgba(110,118,129,0);--color-timeline-badge-bg: #21262d;--color-ansi-black: #484f58;--color-ansi-black-bright: #6e7681;--color-ansi-white: #b1bac4;--color-ansi-white-bright: #ffffff;--color-ansi-gray: #6e7681;--color-ansi-red: #ff7b72;--color-ansi-red-bright: #ffa198;--color-ansi-green: #3fb950;--color-ansi-green-bright: #56d364;--color-ansi-yellow: #d29922;--color-ansi-yellow-bright: #e3b341;--color-ansi-blue: #58a6ff;--color-ansi-blue-bright: #79c0ff;--color-ansi-magenta: #bc8cff;--color-ansi-magenta-bright: #d2a8ff;--color-ansi-cyan: #39c5cf;--color-ansi-cyan-bright: #56d4dd;--color-btn-text: #c9d1d9;--color-btn-bg: #21262d;--color-btn-border: rgba(240,246,252,.1);--color-btn-shadow: 0 0 transparent;--color-btn-inset-shadow: 0 0 transparent;--color-btn-hover-bg: #30363d;--color-btn-hover-border: #8b949e;--color-btn-active-bg: hsla(212,12%,18%,1);--color-btn-active-border: #6e7681;--color-btn-selected-bg: #161b22;--color-btn-counter-bg: #30363d;--color-btn-primary-text: #ffffff;--color-btn-primary-bg: #238636;--color-btn-primary-border: rgba(240,246,252,.1);--color-btn-primary-shadow: 0 0 transparent;--color-btn-primary-inset-shadow: 0 0 transparent;--color-btn-primary-hover-bg: #2ea043;--color-btn-primary-hover-border: rgba(240,246,252,.1);--color-btn-primary-selected-bg: #238636;--color-btn-primary-selected-shadow: 0 0 transparent;--color-btn-primary-disabled-text: rgba(255,255,255,.5);--color-btn-primary-disabled-bg: rgba(35,134,54,.6);--color-btn-primary-disabled-border: rgba(240,246,252,.1);--color-btn-primary-icon: #ffffff;--color-btn-primary-counter-bg: rgba(4,38,15,.2);--color-btn-outline-text: #388bfd;--color-btn-outline-hover-text: #58a6ff;--color-btn-outline-hover-bg: #30363d;--color-btn-outline-hover-border: rgba(240,246,252,.1);--color-btn-outline-hover-shadow: 0 1px 0 rgba(1,4,9,.1);--color-btn-outline-hover-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-outline-hover-counter-bg: rgba(5,29,77,.2);--color-btn-outline-selected-text: #ffffff;--color-btn-outline-selected-bg: #0d419d;--color-btn-outline-selected-border: rgba(240,246,252,.1);--color-btn-outline-selected-shadow: 0 0 transparent;--color-btn-outline-disabled-text: rgba(88,166,255,.5);--color-btn-outline-disabled-bg: #0d1117;--color-btn-outline-disabled-counter-bg: rgba(31,111,235,.05);--color-btn-outline-counter-bg: rgba(5,29,77,.2);--color-btn-outline-hover-counter-fg: #58a6ff;--color-btn-outline-disabled-counter-fg: rgba(47,129,247,.5);--color-btn-outline-counter-fg: #388bfd;--color-btn-danger-text: #f85149;--color-btn-danger-hover-text: #ffffff;--color-btn-danger-hover-bg: #da3633;--color-btn-danger-hover-border: #f85149;--color-btn-danger-hover-shadow: 0 0 transparent;--color-btn-danger-hover-inset-shadow: 0 0 transparent;--color-btn-danger-hover-icon: #ffffff;--color-btn-danger-hover-counter-bg: rgba(255,255,255,.2);--color-btn-danger-selected-text: #ffffff;--color-btn-danger-selected-bg: #b62324;--color-btn-danger-selected-border: #ff7b72;--color-btn-danger-selected-shadow: 0 0 transparent;--color-btn-danger-disabled-text: rgba(248,81,73,.5);--color-btn-danger-disabled-bg: #0d1117;--color-btn-danger-disabled-counter-bg: rgba(218,54,51,.05);--color-btn-danger-counter-bg: rgba(73,2,2,.2);--color-btn-danger-icon: #f85149;--color-btn-danger-counter-fg: #f85149;--color-btn-danger-disabled-counter-fg: rgba(248,81,73,.5);--color-btn-danger-hover-counter-fg: #ffffff;--color-underlinenav-icon: #6e7681;--color-underlinenav-border-hover: rgba(110,118,129,.4);--color-action-list-item-inline-divider: rgba(48,54,61,.48);--color-action-list-item-default-hover-bg: rgba(177,186,196,.12);--color-action-list-item-default-hover-border: rgba(0,0,0,0);--color-action-list-item-default-active-bg: rgba(177,186,196,.2);--color-action-list-item-default-active-border: rgba(0,0,0,0);--color-action-list-item-default-selected-bg: rgba(177,186,196,.08);--color-action-list-item-danger-hover-bg: rgba(248,81,73,.16);--color-action-list-item-danger-active-bg: rgba(248,81,73,.24);--color-action-list-item-danger-hover-text: #ff7b72;--color-switch-track-bg: rgba(110,118,129,.1);--color-switch-track-hover-bg: hsla(215,8%,72%,.1);--color-switch-track-active-bg: rgba(110,118,129,.4);--color-switch-track-disabled-bg: #21262d;--color-switch-track-fg: #7d8590;--color-switch-track-disabled-fg: #010409;--color-switch-track-border: rgba(0,0,0,0);--color-switch-track-checked-bg: rgba(31,111,235,.35);--color-switch-track-checked-hover-bg: rgba(31,111,235,.5);--color-switch-track-checked-active-bg: rgba(31,111,235,.65);--color-switch-track-checked-fg: #ffffff;--color-switch-track-checked-disabled-fg: #010409;--color-switch-track-checked-border: rgba(0,0,0,0);--color-switch-knob-bg: #0d1117;--color-switch-knob-border: #606771;--color-switch-knob-disabled-bg: #161b22;--color-switch-knob-checked-bg: #0d1117;--color-switch-knob-checked-disabled-bg: #161b22;--color-switch-knob-checked-border: rgba(31,111,235,.35);--color-segmented-control-bg: rgba(110,118,129,.1);--color-segmented-control-button-bg: #0d1117;--color-segmented-control-button-hover-bg: #30363d;--color-segmented-control-button-active-bg: #21262d;--color-segmented-control-button-selected-border: #6e7681;--color-tree-view-item-chevron-hover-bg: rgba(177,186,196,.12);--color-tree-view-item-directory-fill: #7d8590;--color-fg-default: #e6edf3;--color-fg-muted: #7d8590;--color-fg-subtle: #6e7681;--color-fg-on-emphasis: #ffffff;--color-canvas-default: #0d1117;--color-canvas-overlay: #161b22;--color-canvas-inset: #010409;--color-canvas-subtle: #161b22;--color-border-default: #30363d;--color-border-muted: #21262d;--color-border-subtle: rgba(240,246,252,.1);--color-shadow-small: 0 0 transparent;--color-shadow-medium: 0 3px 6px #010409;--color-shadow-large: 0 8px 24px #010409;--color-shadow-extra-large: 0 12px 48px #010409;--color-neutral-emphasis-plus: #6e7681;--color-neutral-emphasis: #6e7681;--color-neutral-muted: rgba(110,118,129,.4);--color-neutral-subtle: rgba(110,118,129,.1);--color-accent-fg: #2f81f7;--color-accent-emphasis: #1f6feb;--color-accent-muted: rgba(56,139,253,.4);--color-accent-subtle: rgba(56,139,253,.1);--color-success-fg: #3fb950;--color-success-emphasis: #238636;--color-success-muted: rgba(46,160,67,.4);--color-success-subtle: rgba(46,160,67,.15);--color-attention-fg: #d29922;--color-attention-emphasis: #9e6a03;--color-attention-muted: rgba(187,128,9,.4);--color-attention-subtle: rgba(187,128,9,.15);--color-severe-fg: #db6d28;--color-severe-emphasis: #bd561d;--color-severe-muted: rgba(219,109,40,.4);--color-severe-subtle: rgba(219,109,40,.1);--color-danger-fg: #f85149;--color-danger-emphasis: #da3633;--color-danger-muted: rgba(248,81,73,.4);--color-danger-subtle: rgba(248,81,73,.1);--color-open-fg: #3fb950;--color-open-emphasis: #238636;--color-open-muted: rgba(46,160,67,.4);--color-open-subtle: rgba(46,160,67,.15);--color-closed-fg: #f85149;--color-closed-emphasis: #da3633;--color-closed-muted: rgba(248,81,73,.4);--color-closed-subtle: rgba(248,81,73,.15);--color-done-fg: #a371f7;--color-done-emphasis: #8957e5;--color-done-muted: rgba(163,113,247,.4);--color-done-subtle: rgba(163,113,247,.1);--color-sponsors-fg: #db61a2;--color-sponsors-emphasis: #bf4b8a;--color-sponsors-muted: rgba(219,97,162,.4);--color-sponsors-subtle: rgba(219,97,162,.1);--color-primer-fg-disabled: #484f58;--color-primer-canvas-backdrop: rgba(1,4,9,.8);--color-primer-canvas-sticky: rgba(13,17,23,.95);--color-primer-border-active: #f78166;--color-primer-border-contrast: rgba(255,255,255,.2);--color-primer-shadow-highlight: 0 0 transparent;--color-primer-shadow-inset: 0 0 transparent;--color-scale-black: #010409;--color-scale-white: #ffffff;--color-scale-gray-0: #f0f6fc;--color-scale-gray-1: #c9d1d9;--color-scale-gray-2: #b1bac4;--color-scale-gray-3: #8b949e;--color-scale-gray-4: #6e7681;--color-scale-gray-5: #484f58;--color-scale-gray-6: #30363d;--color-scale-gray-7: #21262d;--color-scale-gray-8: #161b22;--color-scale-gray-9: #0d1117;--color-scale-blue-0: #cae8ff;--color-scale-blue-1: #a5d6ff;--color-scale-blue-2: #79c0ff;--color-scale-blue-3: #58a6ff;--color-scale-blue-4: #388bfd;--color-scale-blue-5: #1f6feb;--color-scale-blue-6: #1158c7;--color-scale-blue-7: #0d419d;--color-scale-blue-8: #0c2d6b;--color-scale-blue-9: #051d4d;--color-scale-green-0: #aff5b4;--color-scale-green-1: #7ee787;--color-scale-green-2: #56d364;--color-scale-green-3: #3fb950;--color-scale-green-4: #2ea043;--color-scale-green-5: #238636;--color-scale-green-6: #196c2e;--color-scale-green-7: #0f5323;--color-scale-green-8: #033a16;--color-scale-green-9: #04260f;--color-scale-yellow-0: #f8e3a1;--color-scale-yellow-1: #f2cc60;--color-scale-yellow-2: #e3b341;--color-scale-yellow-3: #d29922;--color-scale-yellow-4: #bb8009;--color-scale-yellow-5: #9e6a03;--color-scale-yellow-6: #845306;--color-scale-yellow-7: #693e00;--color-scale-yellow-8: #4b2900;--color-scale-yellow-9: #341a00;--color-scale-orange-0: #ffdfb6;--color-scale-orange-1: #ffc680;--color-scale-orange-2: #ffa657;--color-scale-orange-3: #f0883e;--color-scale-orange-4: #db6d28;--color-scale-orange-5: #bd561d;--color-scale-orange-6: #9b4215;--color-scale-orange-7: #762d0a;--color-scale-orange-8: #5a1e02;--color-scale-orange-9: #3d1300;--color-scale-red-0: #ffdcd7;--color-scale-red-1: #ffc1ba;--color-scale-red-2: #ffa198;--color-scale-red-3: #ff7b72;--color-scale-red-4: #f85149;--color-scale-red-5: #da3633;--color-scale-red-6: #b62324;--color-scale-red-7: #8e1519;--color-scale-red-8: #67060c;--color-scale-red-9: #490202;--color-scale-purple-0: #eddeff;--color-scale-purple-1: #e2c5ff;--color-scale-purple-2: #d2a8ff;--color-scale-purple-3: #bc8cff;--color-scale-purple-4: #a371f7;--color-scale-purple-5: #8957e5;--color-scale-purple-6: #6e40c9;--color-scale-purple-7: #553098;--color-scale-purple-8: #3c1e70;--color-scale-purple-9: #271052;--color-scale-pink-0: #ffdaec;--color-scale-pink-1: #ffbedd;--color-scale-pink-2: #ff9bce;--color-scale-pink-3: #f778ba;--color-scale-pink-4: #db61a2;--color-scale-pink-5: #bf4b8a;--color-scale-pink-6: #9e3670;--color-scale-pink-7: #7d2457;--color-scale-pink-8: #5e103e;--color-scale-pink-9: #42062a;--color-scale-coral-0: #ffddd2;--color-scale-coral-1: #ffc2b2;--color-scale-coral-2: #ffa28b;--color-scale-coral-3: #f78166;--color-scale-coral-4: #ea6045;--color-scale-coral-5: #cf462d;--color-scale-coral-6: #ac3220;--color-scale-coral-7: #872012;--color-scale-coral-8: #640d04;--color-scale-coral-9: #460701}.code-diff-view[theme=dark] pre code.hljs{display:block;overflow-x:auto;padding:1em}.code-diff-view[theme=dark] code.hljs{padding:3px 5px}.code-diff-view[theme=dark] .hljs{color:#c9d1d9;background:#0d1117}.code-diff-view[theme=dark] .hljs-doctag,.code-diff-view[theme=dark] .hljs-keyword,.code-diff-view[theme=dark] .hljs-meta .hljs-keyword,.code-diff-view[theme=dark] .hljs-template-tag,.code-diff-view[theme=dark] .hljs-template-variable,.code-diff-view[theme=dark] .hljs-type,.code-diff-view[theme=dark] .hljs-variable.language_{color:#ff7b72}.code-diff-view[theme=dark] .hljs-title,.code-diff-view[theme=dark] .hljs-title.class_,.code-diff-view[theme=dark] .hljs-title.class_.inherited__,.code-diff-view[theme=dark] .hljs-title.function_{color:#d2a8ff}.code-diff-view[theme=dark] .hljs-attr,.code-diff-view[theme=dark] .hljs-attribute,.code-diff-view[theme=dark] .hljs-literal,.code-diff-view[theme=dark] .hljs-meta,.code-diff-view[theme=dark] .hljs-number,.code-diff-view[theme=dark] .hljs-operator,.code-diff-view[theme=dark] .hljs-variable,.code-diff-view[theme=dark] .hljs-selector-attr,.code-diff-view[theme=dark] .hljs-selector-class,.code-diff-view[theme=dark] .hljs-selector-id{color:#79c0ff}.code-diff-view[theme=dark] .hljs-regexp,.code-diff-view[theme=dark] .hljs-string,.code-diff-view[theme=dark] .hljs-meta .hljs-string{color:#a5d6ff}.code-diff-view[theme=dark] .hljs-built_in,.code-diff-view[theme=dark] .hljs-symbol{color:#ffa657}.code-diff-view[theme=dark] .hljs-comment,.code-diff-view[theme=dark] .hljs-code,.code-diff-view[theme=dark] .hljs-formula{color:#8b949e}.code-diff-view[theme=dark] .hljs-name,.code-diff-view[theme=dark] .hljs-quote,.code-diff-view[theme=dark] .hljs-selector-tag,.code-diff-view[theme=dark] .hljs-selector-pseudo{color:#7ee787}.code-diff-view[theme=dark] .hljs-subst{color:#c9d1d9}.code-diff-view[theme=dark] .hljs-section{color:#1f6feb;font-weight:700}.code-diff-view[theme=dark] .hljs-bullet{color:#f2cc60}.code-diff-view[theme=dark] .hljs-emphasis{color:#c9d1d9;font-style:italic}.code-diff-view[theme=dark] .hljs-strong{color:#c9d1d9;font-weight:700}.code-diff-view[theme=dark] .hljs-addition{color:#aff5b4;background-color:#033a16}.code-diff-view[theme=dark] .hljs-deletion{color:#ffdcd7;background-color:#67060c}.code-diff-view{position:relative;margin-top:16px;margin-bottom:16px;border:1px solid var(--color-border-default, #ddd);border-radius:6px;overflow-y:auto}.code-diff-view *{position:static;box-sizing:border-box}.code-diff-view .file-header{background-color:var(--color-canvas-subtle);border-bottom:1px solid var(--color-border-default);padding:8px 16px;font-size:12px;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace}.code-diff-view .file-header .file-info{display:flex;justify-content:space-between;align-items:center;margin-left:8px;height:24px}.code-diff-view .file-header .file-info .info-left{font-size:13px;color:var(--color-fg-default)}.code-diff-view .file-header .file-info .info-right{display:flex;justify-content:space-between;width:50%}.code-diff-view .file-header .file-info .diff-stat .diff-stat-added{color:var(--color-diffstat-addition-bg)}.code-diff-view .file-header .file-info .diff-stat .diff-stat-deleted{color:var(--color-danger-emphasis)}.code-diff-view table{border-spacing:0}.code-diff-view .diff-table{width:100%}.code-diff-view .diff-table .blob-num{position:relative;width:1%;min-width:50px;padding-right:10px;padding-left:10px;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;line-height:20px;color:var(--color-fg-subtle);text-align:right;white-space:nowrap;vertical-align:top;cursor:pointer;-webkit-user-select:none;user-select:none}.code-diff-view .diff-table .blob-num-deletion{color:var(--color-diff-blob-deletion-num-text);background-color:var(--color-diff-blob-deletion-num-bg);border-color:var(--color-danger-emphasis)}.code-diff-view .diff-table .blob-num-addition{color:var(--color-diff-blob-addition-num-text);background-color:var(--color-diff-blob-addition-num-bg);border-color:var(--color-success-emphasis)}.code-diff-view .diff-table .blob-code{position:relative;padding-right:10px;padding-left:10px;line-height:20px;vertical-align:top}.code-diff-view .diff-table .blob-code .blob-code-inner{display:table-cell;overflow:visible;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;color:var(--color-fg-default);word-wrap:anywhere;white-space:pre-wrap}.code-diff-view .diff-table .blob-code-deletion{background-color:var(--color-diff-blob-deletion-line-bg);outline:1px dashed transparent}.code-diff-view .diff-table .blob-code-deletion .x{color:var(--color-diff-blob-deletion-fg);background-color:var(--color-diff-blob-deletion-word-bg)}.code-diff-view .diff-table .blob-code-addition{background-color:var(--color-diff-blob-addition-line-bg);outline:1px dotted transparent}.code-diff-view .diff-table .blob-code-addition .x{color:var(--color-diff-blob-addition-fg);background-color:var(--color-diff-blob-addition-word-bg)}.code-diff-view .diff-table .blob-code-context,.code-diff-view .diff-table .blob-code-addition,.code-diff-view .diff-table .blob-code-deletion{padding-left:22px!important}.code-diff-view .diff-table .blob-code-marker:before{position:absolute;top:1px;left:8px;padding-right:8px;content:attr(data-code-marker)}.code-diff-view .diff-table .blob-num-hunk{background-color:var(--color-diff-blob-hunk-num-bg)}.code-diff-view .diff-table .blob-code-hunk{background-color:var(--color-accent-subtle)}.code-diff-view .file-diff-split{table-layout:fixed}.code-diff-view .file-diff-split .blob-code+.blob-num{border-left:1px solid var(--color-border-muted)}.code-diff-view .file-diff-split .no-select{user-select:none}.code-diff-view .empty-cell{cursor:default;background-color:var(--color-neutral-subtle);border-right-color:var(--color-border-muted)}")),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();
(function(d,W){
  d.CodeDiff=W(d.Vue)
})(this,function(d){"use strict";function W(){}W.prototype={diff:function(r,l){var u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},h=u.callback;typeof u=="function"&&(h=u,u={}),this.options=u;var n=this;function t(b){return h?(setTimeout(function(){h(void 0,b)},0),!0):b}r=this.castInput(r),l=this.castInput(l),r=this.removeEmpty(this.tokenize(r)),l=this.removeEmpty(this.tokenize(l));var i=l.length,a=r.length,s=1,o=i+a;u.maxEditLength&&(o=Math.min(o,u.maxEditLength));var c=[{newPos:-1,components:[]}],f=this.extractCommon(c[0],l,r,0);if(c[0].newPos+1>=i&&f+1>=a)return t([{value:this.join(l),count:l.length}]);function g(){for(var b=-1*s;b<=s;b+=2){var _=void 0,m=c[b-1],v=c[b+1],N=(v?v.newPos:0)-b;m&&(c[b-1]=void 0);var w=m&&m.newPos+1<i,R=v&&0<=N&&N<a;if(!w&&!R){c[b]=void 0;continue}if(!w||R&&m.newPos<v.newPos?(_=wn(v),n.pushComponent(_.components,void 0,!0)):(_=m,_.newPos++,n.pushComponent(_.components,!0,void 0)),N=n.extractCommon(_,l,r,b),_.newPos+1>=i&&N+1>=a)return t(yn(n,_.components,l,r,n.useLongestToken));c[b]=_}s++}if(h)(function b(){setTimeout(function(){if(s>o)return h();g()||b()},0)})();else for(;s<=o;){var p=g();if(p)return p}},pushComponent:function(r,l,u){var h=r[r.length-1];h&&h.added===l&&h.removed===u?r[r.length-1]={count:h.count+1,added:l,removed:u}:r.push({count:1,added:l,removed:u})},extractCommon:function(r,l,u,h){for(var n=l.length,t=u.length,i=r.newPos,a=i-h,s=0;i+1<n&&a+1<t&&this.equals(l[i+1],u[a+1]);)i++,a++,s++;return s&&r.components.push({count:s}),r.newPos=i,a},equals:function(r,l){return this.options.comparator?this.options.comparator(r,l):r===l||this.options.ignoreCase&&r.toLowerCase()===l.toLowerCase()},removeEmpty:function(r){for(var l=[],u=0;u<r.length;u++)r[u]&&l.push(r[u]);return l},castInput:function(r){return r},tokenize:function(r){return r.split("")},join:function(r){return r.join("")}};function yn(e,r,l,u,h){for(var n=0,t=r.length,i=0,a=0;n<t;n++){var s=r[n];if(s.removed){if(s.value=e.join(u.slice(a,a+s.count)),a+=s.count,n&&r[n-1].added){var c=r[n-1];r[n-1]=r[n],r[n]=c}}else{if(!s.added&&h){var o=l.slice(i,i+s.count);o=o.map(function(g,p){var b=u[a+p];return b.length>g.length?b:g}),s.value=e.join(o)}else s.value=e.join(l.slice(i,i+s.count));i+=s.count,s.added||(a+=s.count)}}var f=r[t-1];return t>1&&typeof f.value=="string"&&(f.added||f.removed)&&e.equals("",f.value)&&(r[t-2].value+=f.value,r.pop()),r}function wn(e){return{newPos:e.newPos,components:e.components.slice(0)}}var Nn=new W;function An(e,r,l){return Nn.diff(e,r,l)}function Mn(e,r){if(typeof e=="function")r.callback=e;else if(e)for(var l in e)e.hasOwnProperty(l)&&(r[l]=e[l]);return r}var Ue=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,xe=/\S/,Se=new W;Se.equals=function(e,r){return this.options.ignoreCase&&(e=e.toLowerCase(),r=r.toLowerCase()),e===r||this.options.ignoreWhitespace&&!xe.test(e)&&!xe.test(r)},Se.tokenize=function(e){for(var r=e.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),l=0;l<r.length-1;l++)!r[l+1]&&r[l+2]&&Ue.test(r[l])&&Ue.test(r[l+2])&&(r[l]+=r[l+2],r.splice(l+1,2),l--);return r};function Sn(e,r,l){return l=Mn(l,{ignoreWhitespace:!0}),Se.diff(e,r,l)}var He=new W;He.tokenize=function(e){var r=[],l=e.split(/(\n|\r\n)/);l[l.length-1]||l.pop();for(var u=0;u<l.length;u++){var h=l[u];u%2&&!this.options.newlineIsToken?r[r.length-1]+=h:(this.options.ignoreWhitespace&&(h=h.trim()),r.push(h))}return r};var Tn=new W;Tn.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)};var Dn=new W;Dn.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)};function pe(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?pe=function(r){return typeof r}:pe=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},pe(e)}var Rn=Object.prototype.toString,ge=new W;ge.useLongestToken=!0,ge.tokenize=He.tokenize,ge.castInput=function(e){var r=this.options,l=r.undefinedReplacement,u=r.stringifyReplacer,h=u===void 0?function(n,t){return typeof t=="undefined"?l:t}:u;return typeof e=="string"?e:JSON.stringify(Te(e,null,null,h),h,"  ")},ge.equals=function(e,r){return W.prototype.equals.call(ge,e.replace(/,([\r\n])/g,"$1"),r.replace(/,([\r\n])/g,"$1"))};function Te(e,r,l,u,h){r=r||[],l=l||[],u&&(e=u(h,e));var n;for(n=0;n<r.length;n+=1)if(r[n]===e)return l[n];var t;if(Rn.call(e)==="[object Array]"){for(r.push(e),t=new Array(e.length),l.push(t),n=0;n<e.length;n+=1)t[n]=Te(e[n],r,l,u,h);return r.pop(),l.pop(),t}if(e&&e.toJSON&&(e=e.toJSON()),pe(e)==="object"&&e!==null){r.push(e),t={},l.push(t);var i=[],a;for(a in e)e.hasOwnProperty(a)&&i.push(a);for(i.sort(),n=0;n<i.length;n+=1)a=i[n],t[a]=Te(e[a],r,l,u,a);r.pop(),l.pop()}else t=e;return t}var De=new W;De.tokenize=function(e){return e.slice()},De.join=De.removeEmpty=function(e){return e};function Ln(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Fe={exports:{}};(function(e){var r=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32},l=-1,u=1,h=0;r.Diff=function(n,t){return[n,t]},r.prototype.diff_main=function(n,t,i,a){typeof a=="undefined"&&(this.Diff_Timeout<=0?a=Number.MAX_VALUE:a=new Date().getTime()+this.Diff_Timeout*1e3);var s=a;if(n==null||t==null)throw new Error("Null input. (diff_main)");if(n==t)return n?[new r.Diff(h,n)]:[];typeof i=="undefined"&&(i=!0);var o=i,c=this.diff_commonPrefix(n,t),f=n.substring(0,c);n=n.substring(c),t=t.substring(c),c=this.diff_commonSuffix(n,t);var g=n.substring(n.length-c);n=n.substring(0,n.length-c),t=t.substring(0,t.length-c);var p=this.diff_compute_(n,t,o,s);return f&&p.unshift(new r.Diff(h,f)),g&&p.push(new r.Diff(h,g)),this.diff_cleanupMerge(p),p},r.prototype.diff_compute_=function(n,t,i,a){var s;if(!n)return[new r.Diff(u,t)];if(!t)return[new r.Diff(l,n)];var o=n.length>t.length?n:t,c=n.length>t.length?t:n,f=o.indexOf(c);if(f!=-1)return s=[new r.Diff(u,o.substring(0,f)),new r.Diff(h,c),new r.Diff(u,o.substring(f+c.length))],n.length>t.length&&(s[0][0]=s[2][0]=l),s;if(c.length==1)return[new r.Diff(l,n),new r.Diff(u,t)];var g=this.diff_halfMatch_(n,t);if(g){var p=g[0],b=g[1],_=g[2],m=g[3],v=g[4],N=this.diff_main(p,_,i,a),w=this.diff_main(b,m,i,a);return N.concat([new r.Diff(h,v)],w)}return i&&n.length>100&&t.length>100?this.diff_lineMode_(n,t,a):this.diff_bisect_(n,t,a)},r.prototype.diff_lineMode_=function(n,t,i){var a=this.diff_linesToChars_(n,t);n=a.chars1,t=a.chars2;var s=a.lineArray,o=this.diff_main(n,t,!1,i);this.diff_charsToLines_(o,s),this.diff_cleanupSemantic(o),o.push(new r.Diff(h,""));for(var c=0,f=0,g=0,p="",b="";c<o.length;){switch(o[c][0]){case u:g++,b+=o[c][1];break;case l:f++,p+=o[c][1];break;case h:if(f>=1&&g>=1){o.splice(c-f-g,f+g),c=c-f-g;for(var _=this.diff_main(p,b,!1,i),m=_.length-1;m>=0;m--)o.splice(c,0,_[m]);c=c+_.length}g=0,f=0,p="",b="";break}c++}return o.pop(),o},r.prototype.diff_bisect_=function(n,t,i){for(var a=n.length,s=t.length,o=Math.ceil((a+s)/2),c=o,f=2*o,g=new Array(f),p=new Array(f),b=0;b<f;b++)g[b]=-1,p[b]=-1;g[c+1]=0,p[c+1]=0;for(var _=a-s,m=_%2!=0,v=0,N=0,w=0,R=0,D=0;D<o&&!(new Date().getTime()>i);D++){for(var T=-D+v;T<=D-N;T+=2){var I=c+T,L;T==-D||T!=D&&g[I-1]<g[I+1]?L=g[I+1]:L=g[I-1]+1;for(var F=L-T;L<a&&F<s&&n.charAt(L)==t.charAt(F);)L++,F++;if(g[I]=L,L>a)N+=2;else if(F>s)v+=2;else if(m){var U=c+_-T;if(U>=0&&U<f&&p[U]!=-1){var P=a-p[U];if(L>=P)return this.diff_bisectSplit_(n,t,L,F,i)}}}for(var z=-D+w;z<=D-R;z+=2){var U=c+z,P;z==-D||z!=D&&p[U-1]<p[U+1]?P=p[U+1]:P=p[U-1]+1;for(var Q=P-z;P<a&&Q<s&&n.charAt(a-P-1)==t.charAt(s-Q-1);)P++,Q++;if(p[U]=P,P>a)R+=2;else if(Q>s)w+=2;else if(!m){var I=c+_-z;if(I>=0&&I<f&&g[I]!=-1){var L=g[I],F=c+L-I;if(P=a-P,L>=P)return this.diff_bisectSplit_(n,t,L,F,i)}}}}return[new r.Diff(l,n),new r.Diff(u,t)]},r.prototype.diff_bisectSplit_=function(n,t,i,a,s){var o=n.substring(0,i),c=t.substring(0,a),f=n.substring(i),g=t.substring(a),p=this.diff_main(o,c,!1,s),b=this.diff_main(f,g,!1,s);return p.concat(b)},r.prototype.diff_linesToChars_=function(n,t){var i=[],a={};i[0]="";function s(g){for(var p="",b=0,_=-1,m=i.length;_<g.length-1;){_=g.indexOf(`
`,b),_==-1&&(_=g.length-1);var v=g.substring(b,_+1);(a.hasOwnProperty?a.hasOwnProperty(v):a[v]!==void 0)?p+=String.fromCharCode(a[v]):(m==o&&(v=g.substring(b),_=g.length),p+=String.fromCharCode(m),a[v]=m,i[m++]=v),b=_+1}return p}var o=4e4,c=s(n);o=65535;var f=s(t);return{chars1:c,chars2:f,lineArray:i}},r.prototype.diff_charsToLines_=function(n,t){for(var i=0;i<n.length;i++){for(var a=n[i][1],s=[],o=0;o<a.length;o++)s[o]=t[a.charCodeAt(o)];n[i][1]=s.join("")}},r.prototype.diff_commonPrefix=function(n,t){if(!n||!t||n.charAt(0)!=t.charAt(0))return 0;for(var i=0,a=Math.min(n.length,t.length),s=a,o=0;i<s;)n.substring(o,s)==t.substring(o,s)?(i=s,o=i):a=s,s=Math.floor((a-i)/2+i);return s},r.prototype.diff_commonSuffix=function(n,t){if(!n||!t||n.charAt(n.length-1)!=t.charAt(t.length-1))return 0;for(var i=0,a=Math.min(n.length,t.length),s=a,o=0;i<s;)n.substring(n.length-s,n.length-o)==t.substring(t.length-s,t.length-o)?(i=s,o=i):a=s,s=Math.floor((a-i)/2+i);return s},r.prototype.diff_commonOverlap_=function(n,t){var i=n.length,a=t.length;if(i==0||a==0)return 0;i>a?n=n.substring(i-a):i<a&&(t=t.substring(0,i));var s=Math.min(i,a);if(n==t)return s;for(var o=0,c=1;;){var f=n.substring(s-c),g=t.indexOf(f);if(g==-1)return o;c+=g,(g==0||n.substring(s-c)==t.substring(0,c))&&(o=c,c++)}},r.prototype.diff_halfMatch_=function(n,t){if(this.Diff_Timeout<=0)return null;var i=n.length>t.length?n:t,a=n.length>t.length?t:n;if(i.length<4||a.length*2<i.length)return null;var s=this;function o(N,w,R){for(var D=N.substring(R,R+Math.floor(N.length/4)),T=-1,I="",L,F,U,P;(T=w.indexOf(D,T+1))!=-1;){var z=s.diff_commonPrefix(N.substring(R),w.substring(T)),Q=s.diff_commonSuffix(N.substring(0,R),w.substring(0,T));I.length<Q+z&&(I=w.substring(T-Q,T)+w.substring(T,T+z),L=N.substring(0,R-Q),F=N.substring(R+z),U=w.substring(0,T-Q),P=w.substring(T+z))}return I.length*2>=N.length?[L,F,U,P,I]:null}var c=o(i,a,Math.ceil(i.length/4)),f=o(i,a,Math.ceil(i.length/2)),g;if(!c&&!f)return null;f?c?g=c[4].length>f[4].length?c:f:g=f:g=c;var p,b,_,m;n.length>t.length?(p=g[0],b=g[1],_=g[2],m=g[3]):(_=g[0],m=g[1],p=g[2],b=g[3]);var v=g[4];return[p,b,_,m,v]},r.prototype.diff_cleanupSemantic=function(n){for(var t=!1,i=[],a=0,s=null,o=0,c=0,f=0,g=0,p=0;o<n.length;)n[o][0]==h?(i[a++]=o,c=g,f=p,g=0,p=0,s=n[o][1]):(n[o][0]==u?g+=n[o][1].length:p+=n[o][1].length,s&&s.length<=Math.max(c,f)&&s.length<=Math.max(g,p)&&(n.splice(i[a-1],0,new r.Diff(l,s)),n[i[a-1]+1][0]=u,a--,a--,o=a>0?i[a-1]:-1,c=0,f=0,g=0,p=0,s=null,t=!0)),o++;for(t&&this.diff_cleanupMerge(n),this.diff_cleanupSemanticLossless(n),o=1;o<n.length;){if(n[o-1][0]==l&&n[o][0]==u){var b=n[o-1][1],_=n[o][1],m=this.diff_commonOverlap_(b,_),v=this.diff_commonOverlap_(_,b);m>=v?(m>=b.length/2||m>=_.length/2)&&(n.splice(o,0,new r.Diff(h,_.substring(0,m))),n[o-1][1]=b.substring(0,b.length-m),n[o+1][1]=_.substring(m),o++):(v>=b.length/2||v>=_.length/2)&&(n.splice(o,0,new r.Diff(h,b.substring(0,v))),n[o-1][0]=u,n[o-1][1]=_.substring(0,_.length-v),n[o+1][0]=l,n[o+1][1]=b.substring(v),o++),o++}o++}},r.prototype.diff_cleanupSemanticLossless=function(n){function t(v,N){if(!v||!N)return 6;var w=v.charAt(v.length-1),R=N.charAt(0),D=w.match(r.nonAlphaNumericRegex_),T=R.match(r.nonAlphaNumericRegex_),I=D&&w.match(r.whitespaceRegex_),L=T&&R.match(r.whitespaceRegex_),F=I&&w.match(r.linebreakRegex_),U=L&&R.match(r.linebreakRegex_),P=F&&v.match(r.blanklineEndRegex_),z=U&&N.match(r.blanklineStartRegex_);return P||z?5:F||U?4:D&&!I&&L?3:I||L?2:D||T?1:0}for(var i=1;i<n.length-1;){if(n[i-1][0]==h&&n[i+1][0]==h){var a=n[i-1][1],s=n[i][1],o=n[i+1][1],c=this.diff_commonSuffix(a,s);if(c){var f=s.substring(s.length-c);a=a.substring(0,a.length-c),s=f+s.substring(0,s.length-c),o=f+o}for(var g=a,p=s,b=o,_=t(a,s)+t(s,o);s.charAt(0)===o.charAt(0);){a+=s.charAt(0),s=s.substring(1)+o.charAt(0),o=o.substring(1);var m=t(a,s)+t(s,o);m>=_&&(_=m,g=a,p=s,b=o)}n[i-1][1]!=g&&(g?n[i-1][1]=g:(n.splice(i-1,1),i--),n[i][1]=p,b?n[i+1][1]=b:(n.splice(i+1,1),i--))}i++}},r.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,r.whitespaceRegex_=/\s/,r.linebreakRegex_=/[\r\n]/,r.blanklineEndRegex_=/\n\r?\n$/,r.blanklineStartRegex_=/^\r?\n\r?\n/,r.prototype.diff_cleanupEfficiency=function(n){for(var t=!1,i=[],a=0,s=null,o=0,c=!1,f=!1,g=!1,p=!1;o<n.length;)n[o][0]==h?(n[o][1].length<this.Diff_EditCost&&(g||p)?(i[a++]=o,c=g,f=p,s=n[o][1]):(a=0,s=null),g=p=!1):(n[o][0]==l?p=!0:g=!0,s&&(c&&f&&g&&p||s.length<this.Diff_EditCost/2&&c+f+g+p==3)&&(n.splice(i[a-1],0,new r.Diff(l,s)),n[i[a-1]+1][0]=u,a--,s=null,c&&f?(g=p=!0,a=0):(a--,o=a>0?i[a-1]:-1,g=p=!1),t=!0)),o++;t&&this.diff_cleanupMerge(n)},r.prototype.diff_cleanupMerge=function(n){n.push(new r.Diff(h,""));for(var t=0,i=0,a=0,s="",o="",c;t<n.length;)switch(n[t][0]){case u:a++,o+=n[t][1],t++;break;case l:i++,s+=n[t][1],t++;break;case h:i+a>1?(i!==0&&a!==0&&(c=this.diff_commonPrefix(o,s),c!==0&&(t-i-a>0&&n[t-i-a-1][0]==h?n[t-i-a-1][1]+=o.substring(0,c):(n.splice(0,0,new r.Diff(h,o.substring(0,c))),t++),o=o.substring(c),s=s.substring(c)),c=this.diff_commonSuffix(o,s),c!==0&&(n[t][1]=o.substring(o.length-c)+n[t][1],o=o.substring(0,o.length-c),s=s.substring(0,s.length-c))),t-=i+a,n.splice(t,i+a),s.length&&(n.splice(t,0,new r.Diff(l,s)),t++),o.length&&(n.splice(t,0,new r.Diff(u,o)),t++),t++):t!==0&&n[t-1][0]==h?(n[t-1][1]+=n[t][1],n.splice(t,1)):t++,a=0,i=0,s="",o="";break}n[n.length-1][1]===""&&n.pop();var f=!1;for(t=1;t<n.length-1;)n[t-1][0]==h&&n[t+1][0]==h&&(n[t][1].substring(n[t][1].length-n[t-1][1].length)==n[t-1][1]?(n[t][1]=n[t-1][1]+n[t][1].substring(0,n[t][1].length-n[t-1][1].length),n[t+1][1]=n[t-1][1]+n[t+1][1],n.splice(t-1,1),f=!0):n[t][1].substring(0,n[t+1][1].length)==n[t+1][1]&&(n[t-1][1]+=n[t+1][1],n[t][1]=n[t][1].substring(n[t+1][1].length)+n[t+1][1],n.splice(t+1,1),f=!0)),t++;f&&this.diff_cleanupMerge(n)},r.prototype.diff_xIndex=function(n,t){var i=0,a=0,s=0,o=0,c;for(c=0;c<n.length&&(n[c][0]!==u&&(i+=n[c][1].length),n[c][0]!==l&&(a+=n[c][1].length),!(i>t));c++)s=i,o=a;return n.length!=c&&n[c][0]===l?o:o+(t-s)},r.prototype.diff_prettyHtml=function(n){for(var t=[],i=/&/g,a=/</g,s=/>/g,o=/\n/g,c=0;c<n.length;c++){var f=n[c][0],g=n[c][1],p=g.replace(i,"&amp;").replace(a,"&lt;").replace(s,"&gt;").replace(o,"&para;<br>");switch(f){case u:t[c]='<ins style="background:#e6ffe6;">'+p+"</ins>";break;case l:t[c]='<del style="background:#ffe6e6;">'+p+"</del>";break;case h:t[c]="<span>"+p+"</span>";break}}return t.join("")},r.prototype.diff_text1=function(n){for(var t=[],i=0;i<n.length;i++)n[i][0]!==u&&(t[i]=n[i][1]);return t.join("")},r.prototype.diff_text2=function(n){for(var t=[],i=0;i<n.length;i++)n[i][0]!==l&&(t[i]=n[i][1]);return t.join("")},r.prototype.diff_levenshtein=function(n){for(var t=0,i=0,a=0,s=0;s<n.length;s++){var o=n[s][0],c=n[s][1];switch(o){case u:i+=c.length;break;case l:a+=c.length;break;case h:t+=Math.max(i,a),i=0,a=0;break}}return t+=Math.max(i,a),t},r.prototype.diff_toDelta=function(n){for(var t=[],i=0;i<n.length;i++)switch(n[i][0]){case u:t[i]="+"+encodeURI(n[i][1]);break;case l:t[i]="-"+n[i][1].length;break;case h:t[i]="="+n[i][1].length;break}return t.join("	").replace(/%20/g," ")},r.prototype.diff_fromDelta=function(n,t){for(var i=[],a=0,s=0,o=t.split(/\t/g),c=0;c<o.length;c++){var f=o[c].substring(1);switch(o[c].charAt(0)){case"+":try{i[a++]=new r.Diff(u,decodeURI(f))}catch(b){throw new Error("Illegal escape in diff_fromDelta: "+f)}break;case"-":case"=":var g=parseInt(f,10);if(isNaN(g)||g<0)throw new Error("Invalid number in diff_fromDelta: "+f);var p=n.substring(s,s+=g);o[c].charAt(0)=="="?i[a++]=new r.Diff(h,p):i[a++]=new r.Diff(l,p);break;default:if(o[c])throw new Error("Invalid diff operation in diff_fromDelta: "+o[c])}}if(s!=n.length)throw new Error("Delta length ("+s+") does not equal source text length ("+n.length+").");return i},r.prototype.match_main=function(n,t,i){if(n==null||t==null||i==null)throw new Error("Null input. (match_main)");return i=Math.max(0,Math.min(i,n.length)),n==t?0:n.length?n.substring(i,i+t.length)==t?i:this.match_bitap_(n,t,i):-1},r.prototype.match_bitap_=function(n,t,i){if(t.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var a=this.match_alphabet_(t),s=this;function o(L,F){var U=L/t.length,P=Math.abs(i-F);return s.Match_Distance?U+P/s.Match_Distance:P?1:U}var c=this.Match_Threshold,f=n.indexOf(t,i);f!=-1&&(c=Math.min(o(0,f),c),f=n.lastIndexOf(t,i+t.length),f!=-1&&(c=Math.min(o(0,f),c)));var g=1<<t.length-1;f=-1;for(var p,b,_=t.length+n.length,m,v=0;v<t.length;v++){for(p=0,b=_;p<b;)o(v,i+b)<=c?p=b:_=b,b=Math.floor((_-p)/2+p);_=b;var N=Math.max(1,i-b+1),w=Math.min(i+b,n.length)+t.length,R=Array(w+2);R[w+1]=(1<<v)-1;for(var D=w;D>=N;D--){var T=a[n.charAt(D-1)];if(v===0?R[D]=(R[D+1]<<1|1)&T:R[D]=(R[D+1]<<1|1)&T|((m[D+1]|m[D])<<1|1)|m[D+1],R[D]&g){var I=o(v,D-1);if(I<=c)if(c=I,f=D-1,f>i)N=Math.max(1,2*i-f);else break}}if(o(v+1,i)>c)break;m=R}return f},r.prototype.match_alphabet_=function(n){for(var t={},i=0;i<n.length;i++)t[n.charAt(i)]=0;for(var i=0;i<n.length;i++)t[n.charAt(i)]|=1<<n.length-i-1;return t},r.prototype.patch_addContext_=function(n,t){if(t.length!=0){if(n.start2===null)throw Error("patch not initialized");for(var i=t.substring(n.start2,n.start2+n.length1),a=0;t.indexOf(i)!=t.lastIndexOf(i)&&i.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)a+=this.Patch_Margin,i=t.substring(n.start2-a,n.start2+n.length1+a);a+=this.Patch_Margin;var s=t.substring(n.start2-a,n.start2);s&&n.diffs.unshift(new r.Diff(h,s));var o=t.substring(n.start2+n.length1,n.start2+n.length1+a);o&&n.diffs.push(new r.Diff(h,o)),n.start1-=s.length,n.start2-=s.length,n.length1+=s.length+o.length,n.length2+=s.length+o.length}},r.prototype.patch_make=function(n,t,i){var a,s;if(typeof n=="string"&&typeof t=="string"&&typeof i=="undefined")a=n,s=this.diff_main(a,t,!0),s.length>2&&(this.diff_cleanupSemantic(s),this.diff_cleanupEfficiency(s));else if(n&&typeof n=="object"&&typeof t=="undefined"&&typeof i=="undefined")s=n,a=this.diff_text1(s);else if(typeof n=="string"&&t&&typeof t=="object"&&typeof i=="undefined")a=n,s=t;else if(typeof n=="string"&&typeof t=="string"&&i&&typeof i=="object")a=n,s=i;else throw new Error("Unknown call format to patch_make.");if(s.length===0)return[];for(var o=[],c=new r.patch_obj,f=0,g=0,p=0,b=a,_=a,m=0;m<s.length;m++){var v=s[m][0],N=s[m][1];switch(!f&&v!==h&&(c.start1=g,c.start2=p),v){case u:c.diffs[f++]=s[m],c.length2+=N.length,_=_.substring(0,p)+N+_.substring(p);break;case l:c.length1+=N.length,c.diffs[f++]=s[m],_=_.substring(0,p)+_.substring(p+N.length);break;case h:N.length<=2*this.Patch_Margin&&f&&s.length!=m+1?(c.diffs[f++]=s[m],c.length1+=N.length,c.length2+=N.length):N.length>=2*this.Patch_Margin&&f&&(this.patch_addContext_(c,b),o.push(c),c=new r.patch_obj,f=0,b=_,g=p);break}v!==u&&(g+=N.length),v!==l&&(p+=N.length)}return f&&(this.patch_addContext_(c,b),o.push(c)),o},r.prototype.patch_deepCopy=function(n){for(var t=[],i=0;i<n.length;i++){var a=n[i],s=new r.patch_obj;s.diffs=[];for(var o=0;o<a.diffs.length;o++)s.diffs[o]=new r.Diff(a.diffs[o][0],a.diffs[o][1]);s.start1=a.start1,s.start2=a.start2,s.length1=a.length1,s.length2=a.length2,t[i]=s}return t},r.prototype.patch_apply=function(n,t){if(n.length==0)return[t,[]];n=this.patch_deepCopy(n);var i=this.patch_addPadding(n);t=i+t+i,this.patch_splitMax(n);for(var a=0,s=[],o=0;o<n.length;o++){var c=n[o].start2+a,f=this.diff_text1(n[o].diffs),g,p=-1;if(f.length>this.Match_MaxBits?(g=this.match_main(t,f.substring(0,this.Match_MaxBits),c),g!=-1&&(p=this.match_main(t,f.substring(f.length-this.Match_MaxBits),c+f.length-this.Match_MaxBits),(p==-1||g>=p)&&(g=-1))):g=this.match_main(t,f,c),g==-1)s[o]=!1,a-=n[o].length2-n[o].length1;else{s[o]=!0,a=g-c;var b;if(p==-1?b=t.substring(g,g+f.length):b=t.substring(g,p+this.Match_MaxBits),f==b)t=t.substring(0,g)+this.diff_text2(n[o].diffs)+t.substring(g+f.length);else{var _=this.diff_main(f,b,!1);if(f.length>this.Match_MaxBits&&this.diff_levenshtein(_)/f.length>this.Patch_DeleteThreshold)s[o]=!1;else{this.diff_cleanupSemanticLossless(_);for(var m=0,v,N=0;N<n[o].diffs.length;N++){var w=n[o].diffs[N];w[0]!==h&&(v=this.diff_xIndex(_,m)),w[0]===u?t=t.substring(0,g+v)+w[1]+t.substring(g+v):w[0]===l&&(t=t.substring(0,g+v)+t.substring(g+this.diff_xIndex(_,m+w[1].length))),w[0]!==l&&(m+=w[1].length)}}}}}return t=t.substring(i.length,t.length-i.length),[t,s]},r.prototype.patch_addPadding=function(n){for(var t=this.Patch_Margin,i="",a=1;a<=t;a++)i+=String.fromCharCode(a);for(var a=0;a<n.length;a++)n[a].start1+=t,n[a].start2+=t;var s=n[0],o=s.diffs;if(o.length==0||o[0][0]!=h)o.unshift(new r.Diff(h,i)),s.start1-=t,s.start2-=t,s.length1+=t,s.length2+=t;else if(t>o[0][1].length){var c=t-o[0][1].length;o[0][1]=i.substring(o[0][1].length)+o[0][1],s.start1-=c,s.start2-=c,s.length1+=c,s.length2+=c}if(s=n[n.length-1],o=s.diffs,o.length==0||o[o.length-1][0]!=h)o.push(new r.Diff(h,i)),s.length1+=t,s.length2+=t;else if(t>o[o.length-1][1].length){var c=t-o[o.length-1][1].length;o[o.length-1][1]+=i.substring(0,c),s.length1+=c,s.length2+=c}return i},r.prototype.patch_splitMax=function(n){for(var t=this.Match_MaxBits,i=0;i<n.length;i++)if(!(n[i].length1<=t)){var a=n[i];n.splice(i--,1);for(var s=a.start1,o=a.start2,c="";a.diffs.length!==0;){var f=new r.patch_obj,g=!0;for(f.start1=s-c.length,f.start2=o-c.length,c!==""&&(f.length1=f.length2=c.length,f.diffs.push(new r.Diff(h,c)));a.diffs.length!==0&&f.length1<t-this.Patch_Margin;){var p=a.diffs[0][0],b=a.diffs[0][1];p===u?(f.length2+=b.length,o+=b.length,f.diffs.push(a.diffs.shift()),g=!1):p===l&&f.diffs.length==1&&f.diffs[0][0]==h&&b.length>2*t?(f.length1+=b.length,s+=b.length,g=!1,f.diffs.push(new r.Diff(p,b)),a.diffs.shift()):(b=b.substring(0,t-f.length1-this.Patch_Margin),f.length1+=b.length,s+=b.length,p===h?(f.length2+=b.length,o+=b.length):g=!1,f.diffs.push(new r.Diff(p,b)),b==a.diffs[0][1]?a.diffs.shift():a.diffs[0][1]=a.diffs[0][1].substring(b.length))}c=this.diff_text2(f.diffs),c=c.substring(c.length-this.Patch_Margin);var _=this.diff_text1(a.diffs).substring(0,this.Patch_Margin);_!==""&&(f.length1+=_.length,f.length2+=_.length,f.diffs.length!==0&&f.diffs[f.diffs.length-1][0]===h?f.diffs[f.diffs.length-1][1]+=_:f.diffs.push(new r.Diff(h,_))),g||n.splice(++i,0,f)}}},r.prototype.patch_toText=function(n){for(var t=[],i=0;i<n.length;i++)t[i]=n[i];return t.join("")},r.prototype.patch_fromText=function(n){var t=[];if(!n)return t;for(var i=n.split(`
`),a=0,s=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;a<i.length;){var o=i[a].match(s);if(!o)throw new Error("Invalid patch string: "+i[a]);var c=new r.patch_obj;for(t.push(c),c.start1=parseInt(o[1],10),o[2]===""?(c.start1--,c.length1=1):o[2]=="0"?c.length1=0:(c.start1--,c.length1=parseInt(o[2],10)),c.start2=parseInt(o[3],10),o[4]===""?(c.start2--,c.length2=1):o[4]=="0"?c.length2=0:(c.start2--,c.length2=parseInt(o[4],10)),a++;a<i.length;){var f=i[a].charAt(0);try{var g=decodeURI(i[a].substring(1))}catch(p){throw new Error("Illegal escape in patch_fromText: "+g)}if(f=="-")c.diffs.push(new r.Diff(l,g));else if(f=="+")c.diffs.push(new r.Diff(u,g));else if(f==" ")c.diffs.push(new r.Diff(h,g));else{if(f=="@")break;if(f!=="")throw new Error('Invalid patch mode "'+f+'" in: '+g)}a++}}return t},r.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},r.patch_obj.prototype.toString=function(){var n,t;this.length1===0?n=this.start1+",0":this.length1==1?n=this.start1+1:n=this.start1+1+","+this.length1,this.length2===0?t=this.start2+",0":this.length2==1?t=this.start2+1:t=this.start2+1+","+this.length2;for(var i=["@@ -"+n+" +"+t+` @@
`],a,s=0;s<this.diffs.length;s++){switch(this.diffs[s][0]){case u:a="+";break;case l:a="-";break;case h:a=" ";break}i[s+1]=a+encodeURI(this.diffs[s][1])+`
`}return i.join("").replace(/%20/g," ")},e.exports=r,e.exports.diff_match_patch=r,e.exports.DIFF_DELETE=l,e.exports.DIFF_INSERT=u,e.exports.DIFF_EQUAL=h})(Fe);var Re=Fe.exports;function ze(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(r=>{const l=e[r],u=typeof l;(u==="object"||u==="function")&&!Object.isFrozen(l)&&ze(l)}),e}class Ge{constructor(r){r.data===void 0&&(r.data={}),this.data=r.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function Ve(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function re(e,...r){const l=Object.create(null);for(const u in e)l[u]=e[u];return r.forEach(function(u){for(const h in u)l[h]=u[h]}),l}const On="</span>",Ke=e=>!!e.scope,kn=(e,{prefix:r})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const l=e.split(".");return[`${r}${l.shift()}`,...l.map((u,h)=>`${u}${"_".repeat(h+1)}`)].join(" ")}return`${r}${e}`};class Cn{constructor(r,l){this.buffer="",this.classPrefix=l.classPrefix,r.walk(this)}addText(r){this.buffer+=Ve(r)}openNode(r){if(!Ke(r))return;const l=kn(r.scope,{prefix:this.classPrefix});this.span(l)}closeNode(r){Ke(r)&&(this.buffer+=On)}value(){return this.buffer}span(r){this.buffer+=`<span class="${r}">`}}const We=(e={})=>{const r={children:[]};return Object.assign(r,e),r};class Le{constructor(){this.rootNode=We(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(r){this.top.children.push(r)}openNode(r){const l=We({scope:r});this.add(l),this.stack.push(l)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(r){return this.constructor._walk(r,this.rootNode)}static _walk(r,l){return typeof l=="string"?r.addText(l):l.children&&(r.openNode(l),l.children.forEach(u=>this._walk(r,u)),r.closeNode(l)),r}static _collapse(r){typeof r!="string"&&r.children&&(r.children.every(l=>typeof l=="string")?r.children=[r.children.join("")]:r.children.forEach(l=>{Le._collapse(l)}))}}class In extends Le{constructor(r){super(),this.options=r}addText(r){r!==""&&this.add(r)}startScope(r){this.openNode(r)}endScope(){this.closeNode()}__addSublanguage(r,l){const u=r.root;l&&(u.scope=`language:${l}`),this.add(u)}toHTML(){return new Cn(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function he(e){return e?typeof e=="string"?e:e.source:null}function Qe(e){return ie("(?=",e,")")}function Bn(e){return ie("(?:",e,")*")}function $n(e){return ie("(?:",e,")?")}function ie(...e){return e.map(l=>he(l)).join("")}function Pn(e){const r=e[e.length-1];return typeof r=="object"&&r.constructor===Object?(e.splice(e.length-1,1),r):{}}function Oe(...e){return"("+(Pn(e).capture?"":"?:")+e.map(u=>he(u)).join("|")+")"}function Ze(e){return new RegExp(e.toString()+"|").exec("").length-1}function Un(e,r){const l=e&&e.exec(r);return l&&l.index===0}const xn=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function ke(e,{joinWith:r}){let l=0;return e.map(u=>{l+=1;const h=l;let n=he(u),t="";for(;n.length>0;){const i=xn.exec(n);if(!i){t+=n;break}t+=n.substring(0,i.index),n=n.substring(i.index+i[0].length),i[0][0]==="\\"&&i[1]?t+="\\"+String(Number(i[1])+h):(t+=i[0],i[0]==="("&&l++)}return t}).map(u=>`(${u})`).join(r)}const Hn=/\b\B/,Ye="[a-zA-Z]\\w*",Ce="[a-zA-Z_]\\w*",Xe="\\b\\d+(\\.\\d+)?",je="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Je="\\b(0b[01]+)",Fn="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",zn=(e={})=>{const r=/^#![ ]*\//;return e.binary&&(e.begin=ie(r,/.*\b/,e.binary,/\b.*/)),re({scope:"meta",begin:r,end:/$/,relevance:0,"on:begin":(l,u)=>{l.index!==0&&u.ignoreMatch()}},e)},de={begin:"\\\\[\\s\\S]",relevance:0},Gn={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[de]},Vn={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[de]},Kn={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},be=function(e,r,l={}){const u=re({scope:"comment",begin:e,end:r,contains:[]},l);u.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const h=Oe("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return u.contains.push({begin:ie(/[ ]+/,"(",h,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),u},Wn=be("//","$"),Qn=be("/\\*","\\*/"),Zn=be("#","$"),Yn={scope:"number",begin:Xe,relevance:0},Xn={scope:"number",begin:je,relevance:0},jn={scope:"number",begin:Je,relevance:0},Jn={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[de,{begin:/\[/,end:/\]/,relevance:0,contains:[de]}]},qn={scope:"title",begin:Ye,relevance:0},et={scope:"title",begin:Ce,relevance:0},nt={begin:"\\.\\s*"+Ce,relevance:0};var _e=Object.freeze({__proto__:null,APOS_STRING_MODE:Gn,BACKSLASH_ESCAPE:de,BINARY_NUMBER_MODE:jn,BINARY_NUMBER_RE:Je,COMMENT:be,C_BLOCK_COMMENT_MODE:Qn,C_LINE_COMMENT_MODE:Wn,C_NUMBER_MODE:Xn,C_NUMBER_RE:je,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(r,l)=>{l.data._beginMatch=r[1]},"on:end":(r,l)=>{l.data._beginMatch!==r[1]&&l.ignoreMatch()}})},HASH_COMMENT_MODE:Zn,IDENT_RE:Ye,MATCH_NOTHING_RE:Hn,METHOD_GUARD:nt,NUMBER_MODE:Yn,NUMBER_RE:Xe,PHRASAL_WORDS_MODE:Kn,QUOTE_STRING_MODE:Vn,REGEXP_MODE:Jn,RE_STARTERS_RE:Fn,SHEBANG:zn,TITLE_MODE:qn,UNDERSCORE_IDENT_RE:Ce,UNDERSCORE_TITLE_MODE:et});function tt(e,r){e.input[e.index-1]==="."&&r.ignoreMatch()}function rt(e,r){e.className!==void 0&&(e.scope=e.className,delete e.className)}function it(e,r){r&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=tt,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function st(e,r){Array.isArray(e.illegal)&&(e.illegal=Oe(...e.illegal))}function at(e,r){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function ot(e,r){e.relevance===void 0&&(e.relevance=1)}const lt=(e,r)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const l=Object.assign({},e);Object.keys(e).forEach(u=>{delete e[u]}),e.keywords=l.keywords,e.begin=ie(l.beforeMatch,Qe(l.begin)),e.starts={relevance:0,contains:[Object.assign(l,{endsParent:!0})]},e.relevance=0,delete l.beforeMatch},ct=["of","and","for","in","not","or","if","then","parent","list","value"],ut="keyword";function qe(e,r,l=ut){const u=Object.create(null);return typeof e=="string"?h(l,e.split(" ")):Array.isArray(e)?h(l,e):Object.keys(e).forEach(function(n){Object.assign(u,qe(e[n],r,n))}),u;function h(n,t){r&&(t=t.map(i=>i.toLowerCase())),t.forEach(function(i){const a=i.split("|");u[a[0]]=[n,ft(a[0],a[1])]})}}function ft(e,r){return r?Number(r):gt(e)?0:1}function gt(e){return ct.includes(e.toLowerCase())}const en={},se=e=>{console.error(e)},nn=(e,...r)=>{console.log(`WARN: ${e}`,...r)},oe=(e,r)=>{en[`${e}/${r}`]||(console.log(`Deprecated as of ${e}. ${r}`),en[`${e}/${r}`]=!0)},Ee=new Error;function tn(e,r,{key:l}){let u=0;const h=e[l],n={},t={};for(let i=1;i<=r.length;i++)t[i+u]=h[i],n[i+u]=!0,u+=Ze(r[i-1]);e[l]=t,e[l]._emit=n,e[l]._multi=!0}function ht(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw se("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),Ee;if(typeof e.beginScope!="object"||e.beginScope===null)throw se("beginScope must be object"),Ee;tn(e,e.begin,{key:"beginScope"}),e.begin=ke(e.begin,{joinWith:""})}}function dt(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw se("skip, excludeEnd, returnEnd not compatible with endScope: {}"),Ee;if(typeof e.endScope!="object"||e.endScope===null)throw se("endScope must be object"),Ee;tn(e,e.end,{key:"endScope"}),e.end=ke(e.end,{joinWith:""})}}function pt(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function bt(e){pt(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),ht(e),dt(e)}function _t(e){function r(t,i){return new RegExp(he(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(i?"g":""))}class l{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(i,a){a.position=this.position++,this.matchIndexes[this.matchAt]=a,this.regexes.push([a,i]),this.matchAt+=Ze(i)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const i=this.regexes.map(a=>a[1]);this.matcherRe=r(ke(i,{joinWith:"|"}),!0),this.lastIndex=0}exec(i){this.matcherRe.lastIndex=this.lastIndex;const a=this.matcherRe.exec(i);if(!a)return null;const s=a.findIndex((c,f)=>f>0&&c!==void 0),o=this.matchIndexes[s];return a.splice(0,s),Object.assign(a,o)}}class u{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(i){if(this.multiRegexes[i])return this.multiRegexes[i];const a=new l;return this.rules.slice(i).forEach(([s,o])=>a.addRule(s,o)),a.compile(),this.multiRegexes[i]=a,a}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(i,a){this.rules.push([i,a]),a.type==="begin"&&this.count++}exec(i){const a=this.getMatcher(this.regexIndex);a.lastIndex=this.lastIndex;let s=a.exec(i);if(this.resumingScanAtSamePosition()&&!(s&&s.index===this.lastIndex)){const o=this.getMatcher(0);o.lastIndex=this.lastIndex+1,s=o.exec(i)}return s&&(this.regexIndex+=s.position+1,this.regexIndex===this.count&&this.considerAll()),s}}function h(t){const i=new u;return t.contains.forEach(a=>i.addRule(a.begin,{rule:a,type:"begin"})),t.terminatorEnd&&i.addRule(t.terminatorEnd,{type:"end"}),t.illegal&&i.addRule(t.illegal,{type:"illegal"}),i}function n(t,i){const a=t;if(t.isCompiled)return a;[rt,at,bt,lt].forEach(o=>o(t,i)),e.compilerExtensions.forEach(o=>o(t,i)),t.__beforeBegin=null,[it,st,ot].forEach(o=>o(t,i)),t.isCompiled=!0;let s=null;return typeof t.keywords=="object"&&t.keywords.$pattern&&(t.keywords=Object.assign({},t.keywords),s=t.keywords.$pattern,delete t.keywords.$pattern),s=s||/\w+/,t.keywords&&(t.keywords=qe(t.keywords,e.case_insensitive)),a.keywordPatternRe=r(s,!0),i&&(t.begin||(t.begin=/\B|\b/),a.beginRe=r(a.begin),!t.end&&!t.endsWithParent&&(t.end=/\B|\b/),t.end&&(a.endRe=r(a.end)),a.terminatorEnd=he(a.end)||"",t.endsWithParent&&i.terminatorEnd&&(a.terminatorEnd+=(t.end?"|":"")+i.terminatorEnd)),t.illegal&&(a.illegalRe=r(t.illegal)),t.contains||(t.contains=[]),t.contains=[].concat(...t.contains.map(function(o){return Et(o==="self"?t:o)})),t.contains.forEach(function(o){n(o,a)}),t.starts&&n(t.starts,i),a.matcher=h(a),a}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=re(e.classNameAliases||{}),n(e)}function rn(e){return e?e.endsWithParent||rn(e.starts):!1}function Et(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(r){return re(e,{variants:null},r)})),e.cachedVariants?e.cachedVariants:rn(e)?re(e,{starts:e.starts?re(e.starts):null}):Object.isFrozen(e)?re(e):e}var mt="11.9.0";class vt extends Error{constructor(r,l){super(r),this.name="HTMLInjectionError",this.html=l}}const Ie=Ve,sn=re,an=Symbol("nomatch"),yt=7,on=function(e){const r=Object.create(null),l=Object.create(null),u=[];let h=!0;const n="Could not find the language '{}', did you forget to load/include a language module?",t={disableAutodetect:!0,name:"Plain text",contains:[]};let i={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:In};function a(E){return i.noHighlightRe.test(E)}function s(E){let M=E.className+" ";M+=E.parentNode?E.parentNode.className:"";const k=i.languageDetectRe.exec(M);if(k){const B=L(k[1]);return B||(nn(n.replace("{}",k[1])),nn("Falling back to no-highlight mode for this block.",E)),B?k[1]:"no-highlight"}return M.split(/\s+/).find(B=>a(B)||L(B))}function o(E,M,k){let B="",H="";typeof M=="object"?(B=E,k=M.ignoreIllegals,H=M.language):(oe("10.7.0","highlight(lang, code, ...args) has been deprecated."),oe("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),H=E,B=M),k===void 0&&(k=!0);const K={code:B,language:H};fe("before:highlight",K);const j=K.result?K.result:c(K.language,K.code,k);return j.code=K.code,fe("after:highlight",j),j}function c(E,M,k,B){const H=Object.create(null);function K(y,S){return y.keywords[S]}function j(){if(!O.keywords){G.addText(x);return}let y=0;O.keywordPatternRe.lastIndex=0;let S=O.keywordPatternRe.exec(x),C="";for(;S;){C+=x.substring(y,S.index);const $=q.case_insensitive?S[0].toLowerCase():S[0],V=K(O,$);if(V){const[te,Sr]=V;if(G.addText(C),C="",H[$]=(H[$]||0)+1,H[$]<=yt&&(Me+=Sr),te.startsWith("_"))C+=S[0];else{const Tr=q.classNameAliases[te]||te;J(S[0],Tr)}}else C+=S[0];y=O.keywordPatternRe.lastIndex,S=O.keywordPatternRe.exec(x)}C+=x.substring(y),G.addText(C)}function Ne(){if(x==="")return;let y=null;if(typeof O.subLanguage=="string"){if(!r[O.subLanguage]){G.addText(x);return}y=c(O.subLanguage,x,!0,vn[O.subLanguage]),vn[O.subLanguage]=y._top}else y=g(x,O.subLanguage.length?O.subLanguage:null);O.relevance>0&&(Me+=y.relevance),G.__addSublanguage(y._emitter,y.language)}function Z(){O.subLanguage!=null?Ne():j(),x=""}function J(y,S){y!==""&&(G.startScope(S),G.addText(y),G.endScope())}function bn(y,S){let C=1;const $=S.length-1;for(;C<=$;){if(!y._emit[C]){C++;continue}const V=q.classNameAliases[y[C]]||y[C],te=S[C];V?J(te,V):(x=te,j(),x=""),C++}}function _n(y,S){return y.scope&&typeof y.scope=="string"&&G.openNode(q.classNameAliases[y.scope]||y.scope),y.beginScope&&(y.beginScope._wrap?(J(x,q.classNameAliases[y.beginScope._wrap]||y.beginScope._wrap),x=""):y.beginScope._multi&&(bn(y.beginScope,S),x="")),O=Object.create(y,{parent:{value:O}}),O}function En(y,S,C){let $=Un(y.endRe,C);if($){if(y["on:end"]){const V=new Ge(y);y["on:end"](S,V),V.isMatchIgnored&&($=!1)}if($){for(;y.endsParent&&y.parent;)y=y.parent;return y}}if(y.endsWithParent)return En(y.parent,S,C)}function yr(y){return O.matcher.regexIndex===0?(x+=y[0],1):(Pe=!0,0)}function wr(y){const S=y[0],C=y.rule,$=new Ge(C),V=[C.__beforeBegin,C["on:begin"]];for(const te of V)if(te&&(te(y,$),$.isMatchIgnored))return yr(S);return C.skip?x+=S:(C.excludeBegin&&(x+=S),Z(),!C.returnBegin&&!C.excludeBegin&&(x=S)),_n(C,y),C.returnBegin?0:S.length}function Nr(y){const S=y[0],C=M.substring(y.index),$=En(O,y,C);if(!$)return an;const V=O;O.endScope&&O.endScope._wrap?(Z(),J(S,O.endScope._wrap)):O.endScope&&O.endScope._multi?(Z(),bn(O.endScope,y)):V.skip?x+=S:(V.returnEnd||V.excludeEnd||(x+=S),Z(),V.excludeEnd&&(x=S));do O.scope&&G.closeNode(),!O.skip&&!O.subLanguage&&(Me+=O.relevance),O=O.parent;while(O!==$.parent);return $.starts&&_n($.starts,y),V.returnEnd?0:S.length}function Ar(){const y=[];for(let S=O;S!==q;S=S.parent)S.scope&&y.unshift(S.scope);y.forEach(S=>G.openNode(S))}let Ae={};function mn(y,S){const C=S&&S[0];if(x+=y,C==null)return Z(),0;if(Ae.type==="begin"&&S.type==="end"&&Ae.index===S.index&&C===""){if(x+=M.slice(S.index,S.index+1),!h){const $=new Error(`0 width match regex (${E})`);throw $.languageName=E,$.badRule=Ae.rule,$}return 1}if(Ae=S,S.type==="begin")return wr(S);if(S.type==="illegal"&&!k){const $=new Error('Illegal lexeme "'+C+'" for mode "'+(O.scope||"<unnamed>")+'"');throw $.mode=O,$}else if(S.type==="end"){const $=Nr(S);if($!==an)return $}if(S.type==="illegal"&&C==="")return 1;if($e>1e5&&$e>S.index*3)throw new Error("potential infinite loop, way more iterations than matches");return x+=C,C.length}const q=L(E);if(!q)throw se(n.replace("{}",E)),new Error('Unknown language: "'+E+'"');const Mr=_t(q);let Be="",O=B||Mr;const vn={},G=new i.__emitter(i);Ar();let x="",Me=0,ae=0,$e=0,Pe=!1;try{if(q.__emitTokens)q.__emitTokens(M,G);else{for(O.matcher.considerAll();;){$e++,Pe?Pe=!1:O.matcher.considerAll(),O.matcher.lastIndex=ae;const y=O.matcher.exec(M);if(!y)break;const S=M.substring(ae,y.index),C=mn(S,y);ae=y.index+C}mn(M.substring(ae))}return G.finalize(),Be=G.toHTML(),{language:E,value:Be,relevance:Me,illegal:!1,_emitter:G,_top:O}}catch(y){if(y.message&&y.message.includes("Illegal"))return{language:E,value:Ie(M),illegal:!0,relevance:0,_illegalBy:{message:y.message,index:ae,context:M.slice(ae-100,ae+100),mode:y.mode,resultSoFar:Be},_emitter:G};if(h)return{language:E,value:Ie(M),illegal:!1,relevance:0,errorRaised:y,_emitter:G,_top:O};throw y}}function f(E){const M={value:Ie(E),illegal:!1,relevance:0,_top:t,_emitter:new i.__emitter(i)};return M._emitter.addText(E),M}function g(E,M){M=M||i.languages||Object.keys(r);const k=f(E),B=M.filter(L).filter(U).map(Z=>c(Z,E,!1));B.unshift(k);const H=B.sort((Z,J)=>{if(Z.relevance!==J.relevance)return J.relevance-Z.relevance;if(Z.language&&J.language){if(L(Z.language).supersetOf===J.language)return 1;if(L(J.language).supersetOf===Z.language)return-1}return 0}),[K,j]=H,Ne=K;return Ne.secondBest=j,Ne}function p(E,M,k){const B=M&&l[M]||k;E.classList.add("hljs"),E.classList.add(`language-${B}`)}function b(E){let M=null;const k=s(E);if(a(k))return;if(fe("before:highlightElement",{el:E,language:k}),E.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",E);return}if(E.children.length>0&&(i.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(E)),i.throwUnescapedHTML))throw new vt("One of your code blocks includes unescaped HTML.",E.innerHTML);M=E;const B=M.textContent,H=k?o(B,{language:k,ignoreIllegals:!0}):g(B);E.innerHTML=H.value,E.dataset.highlighted="yes",p(E,k,H.language),E.result={language:H.language,re:H.relevance,relevance:H.relevance},H.secondBest&&(E.secondBest={language:H.secondBest.language,relevance:H.secondBest.relevance}),fe("after:highlightElement",{el:E,result:H,text:B})}function _(E){i=sn(i,E)}const m=()=>{w(),oe("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function v(){w(),oe("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let N=!1;function w(){if(document.readyState==="loading"){N=!0;return}document.querySelectorAll(i.cssSelector).forEach(b)}function R(){N&&w()}typeof window!="undefined"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",R,!1);function D(E,M){let k=null;try{k=M(e)}catch(B){if(se("Language definition for '{}' could not be registered.".replace("{}",E)),h)se(B);else throw B;k=t}k.name||(k.name=E),r[E]=k,k.rawDefinition=M.bind(null,e),k.aliases&&F(k.aliases,{languageName:E})}function T(E){delete r[E];for(const M of Object.keys(l))l[M]===E&&delete l[M]}function I(){return Object.keys(r)}function L(E){return E=(E||"").toLowerCase(),r[E]||r[l[E]]}function F(E,{languageName:M}){typeof E=="string"&&(E=[E]),E.forEach(k=>{l[k.toLowerCase()]=M})}function U(E){const M=L(E);return M&&!M.disableAutodetect}function P(E){E["before:highlightBlock"]&&!E["before:highlightElement"]&&(E["before:highlightElement"]=M=>{E["before:highlightBlock"](Object.assign({block:M.el},M))}),E["after:highlightBlock"]&&!E["after:highlightElement"]&&(E["after:highlightElement"]=M=>{E["after:highlightBlock"](Object.assign({block:M.el},M))})}function z(E){P(E),u.push(E)}function Q(E){const M=u.indexOf(E);M!==-1&&u.splice(M,1)}function fe(E,M){const k=E;u.forEach(function(B){B[k]&&B[k](M)})}function we(E){return oe("10.7.0","highlightBlock will be removed entirely in v12.0"),oe("10.7.0","Please use highlightElement now."),b(E)}Object.assign(e,{highlight:o,highlightAuto:g,highlightAll:w,highlightElement:b,highlightBlock:we,configure:_,initHighlighting:m,initHighlightingOnLoad:v,registerLanguage:D,unregisterLanguage:T,listLanguages:I,getLanguage:L,registerAliases:F,autoDetection:U,inherit:sn,addPlugin:z,removePlugin:Q}),e.debugMode=function(){h=!1},e.safeMode=function(){h=!0},e.versionString=mt,e.regex={concat:ie,lookahead:Qe,either:Oe,optional:$n,anyNumberOfTimes:Bn};for(const E in _e)typeof _e[E]=="object"&&ze(_e[E]);return Object.assign(e,_e),e},le=on({});le.newInstance=()=>on({});var wt=le;le.HighlightJS=le,le.default=le;const Y=Ln(wt);function Nt(e){const r=e.regex,l=r.concat(/[\p{L}_]/u,r.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),u=/[\p{L}0-9._:-]+/u,h={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},n={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},t=e.inherit(n,{begin:/\(/,end:/\)/}),i=e.inherit(e.APOS_STRING_MODE,{className:"string"}),a=e.inherit(e.QUOTE_STRING_MODE,{className:"string"}),s={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:u,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[h]},{begin:/'/,end:/'/,contains:[h]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[n,a,i,t,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[n,t,a,i]}]}]},e.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},h,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[a]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[s],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[s],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:r.concat(/</,r.lookahead(r.concat(l,r.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:l,relevance:0,starts:s}]},{className:"tag",begin:r.concat(/<\//,r.lookahead(r.concat(l,/>/))),contains:[{className:"name",begin:l,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}}const ln="[A-Za-z$_][0-9A-Za-z$_]*",At=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends"],Mt=["true","false","null","undefined","NaN","Infinity"],cn=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],un=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],fn=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],St=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],Tt=[].concat(fn,cn,un);function Dt(e){const r=e.regex,l=(M,{after:k})=>{const B="</"+M[0].slice(1);return M.input.indexOf(B,k)!==-1},u=ln,h={begin:"<>",end:"</>"},n=/<[A-Za-z0-9\\._:-]+\s*\/>/,t={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(M,k)=>{const B=M[0].length+M.index,H=M.input[B];if(H==="<"||H===","){k.ignoreMatch();return}H===">"&&(l(M,{after:B})||k.ignoreMatch());let K;const j=M.input.substring(B);if(K=j.match(/^\s*=/)){k.ignoreMatch();return}if((K=j.match(/^\s+extends\s+/))&&K.index===0){k.ignoreMatch();return}}},i={$pattern:ln,keyword:At,literal:Mt,built_in:Tt,"variable.language":St},a="[0-9](_?[0-9])*",s=`\\.(${a})`,o="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",c={className:"number",variants:[{begin:`(\\b(${o})((${s})|\\.)?|(${s}))[eE][+-]?(${a})\\b`},{begin:`\\b(${o})\\b((${s})\\b|\\.)?|(${s})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},f={className:"subst",begin:"\\$\\{",end:"\\}",keywords:i,contains:[]},g={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,f],subLanguage:"xml"}},p={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,f],subLanguage:"css"}},b={begin:"gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,f],subLanguage:"graphql"}},_={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,f]},v={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:u+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},N=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,g,p,b,_,{match:/\$\d+/},c];f.contains=N.concat({begin:/\{/,end:/\}/,keywords:i,contains:["self"].concat(N)});const w=[].concat(v,f.contains),R=w.concat([{begin:/\(/,end:/\)/,keywords:i,contains:["self"].concat(w)}]),D={className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:R},T={variants:[{match:[/class/,/\s+/,u,/\s+/,/extends/,/\s+/,r.concat(u,"(",r.concat(/\./,u),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,u],scope:{1:"keyword",3:"title.class"}}]},I={relevance:0,match:r.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...cn,...un]}},L={label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},F={variants:[{match:[/function/,/\s+/,u,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[D],illegal:/%/},U={relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"};function P(M){return r.concat("(?!",M.join("|"),")")}const z={match:r.concat(/\b/,P([...fn,"super","import"]),u,r.lookahead(/\(/)),className:"title.function",relevance:0},Q={begin:r.concat(/\./,r.lookahead(r.concat(u,/(?![0-9A-Za-z$_(])/))),end:u,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},fe={match:[/get|set/,/\s+/,u,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},D]},we="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",E={match:[/const|var|let/,/\s+/,u,/\s*/,/=\s*/,/(async\s*)?/,r.lookahead(we)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[D]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:i,exports:{PARAMS_CONTAINS:R,CLASS_REFERENCE:I},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),L,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,g,p,b,_,v,{match:/\$\d+/},c,I,{className:"attr",begin:u+r.lookahead(":"),relevance:0},E,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[v,e.REGEXP_MODE,{className:"function",begin:we,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:R}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:h.begin,end:h.end},{match:n},{begin:t.begin,"on:begin":t.isTrulyOpeningTag,end:t.end}],subLanguage:"xml",contains:[{begin:t.begin,end:t.end,skip:!0,contains:["self"]}]}]},F,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[D,e.inherit(e.TITLE_MODE,{begin:u,className:"title.function"})]},{match:/\.\.\./,relevance:0},Q,{match:"\\$"+u,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[D]},z,U,T,fe,{match:/\$[(.]/}]}}function Rt(e){const r={className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},l={match:/[{}[\],:]/,className:"punctuation",relevance:0},u=["true","false","null"],h={scope:"literal",beginKeywords:u.join(" ")};return{name:"JSON",keywords:{literal:u},contains:[r,l,e.QUOTE_STRING_MODE,h,e.C_NUMBER_MODE,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}function Lt(e){const r="true false yes no null",l="[\\w#;/?:@&=+$,.~*'()[\\]]+",u={className:"attr",variants:[{begin:"\\w[\\w :\\/.-]*:(?=[ 	]|$)"},{begin:'"\\w[\\w :\\/.-]*":(?=[ 	]|$)'},{begin:"'\\w[\\w :\\/.-]*':(?=[ 	]|$)"}]},h={className:"template-variable",variants:[{begin:/\{\{/,end:/\}\}/},{begin:/%\{/,end:/\}/}]},n={className:"string",relevance:0,variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/\S+/}],contains:[e.BACKSLASH_ESCAPE,h]},t=e.inherit(n,{variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/[^\s,{}[\]]+/}]}),i="[0-9]{4}(-[0-9][0-9]){0,2}",a="([Tt \\t][0-9][0-9]?(:[0-9][0-9]){2})?",s="(\\.[0-9]*)?",o="([ \\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?",c={className:"number",begin:"\\b"+i+a+s+o+"\\b"},f={end:",",endsWithParent:!0,excludeEnd:!0,keywords:r,relevance:0},g={begin:/\{/,end:/\}/,contains:[f],illegal:"\\n",relevance:0},p={begin:"\\[",end:"\\]",contains:[f],illegal:"\\n",relevance:0},b=[u,{className:"meta",begin:"^---\\s*$",relevance:10},{className:"string",begin:"[\\|>]([1-9]?[+-])?[ ]*\\n( +)[^ ][^\\n]*\\n(\\2[^\\n]+\\n?)*"},{begin:"<%[%=-]?",end:"[%-]?%>",subLanguage:"ruby",excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:"!\\w+!"+l},{className:"type",begin:"!<"+l+">"},{className:"type",begin:"!"+l},{className:"type",begin:"!!"+l},{className:"meta",begin:"&"+e.UNDERSCORE_IDENT_RE+"$"},{className:"meta",begin:"\\*"+e.UNDERSCORE_IDENT_RE+"$"},{className:"bullet",begin:"-(?=[ ]|$)",relevance:0},e.HASH_COMMENT_MODE,{beginKeywords:r,keywords:{literal:r}},c,{className:"number",begin:e.C_NUMBER_RE+"\\b",relevance:0},g,p,n],_=[...b];return _.pop(),_.push(t),f.contains=_,{name:"YAML",case_insensitive:!0,aliases:["yml"],contains:b}}function Ot(e){return{name:"Plain text",aliases:["text","txt"],disableAutodetect:!0}}function kt(e){const r=e.regex,l=/[\p{XID_Start}_]\p{XID_Continue}*/u,u=["and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","in","is","lambda","match","nonlocal|10","not","or","pass","raise","return","try","while","with","yield"],i={$pattern:/[A-Za-z]\w+|__\w+__/,keyword:u,built_in:["__import__","abs","all","any","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip"],literal:["__debug__","Ellipsis","False","None","NotImplemented","True"],type:["Any","Callable","Coroutine","Dict","List","Literal","Generic","Optional","Sequence","Set","Tuple","Type","Union"]},a={className:"meta",begin:/^(>>>|\.\.\.) /},s={className:"subst",begin:/\{/,end:/\}/,keywords:i,illegal:/#/},o={begin:/\{\{/,relevance:0},c={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,a],relevance:10},{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,a],relevance:10},{begin:/([fF][rR]|[rR][fF]|[fF])'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,a,o,s]},{begin:/([fF][rR]|[rR][fF]|[fF])"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,a,o,s]},{begin:/([uU]|[rR])'/,end:/'/,relevance:10},{begin:/([uU]|[rR])"/,end:/"/,relevance:10},{begin:/([bB]|[bB][rR]|[rR][bB])'/,end:/'/},{begin:/([bB]|[bB][rR]|[rR][bB])"/,end:/"/},{begin:/([fF][rR]|[rR][fF]|[fF])'/,end:/'/,contains:[e.BACKSLASH_ESCAPE,o,s]},{begin:/([fF][rR]|[rR][fF]|[fF])"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,o,s]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},f="[0-9](_?[0-9])*",g=`(\\b(${f}))?\\.(${f})|\\b(${f})\\.`,p=`\\b|${u.join("|")}`,b={className:"number",relevance:0,variants:[{begin:`(\\b(${f})|(${g}))[eE][+-]?(${f})[jJ]?(?=${p})`},{begin:`(${g})[jJ]?`},{begin:`\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${p})`},{begin:`\\b0[bB](_?[01])+[lL]?(?=${p})`},{begin:`\\b0[oO](_?[0-7])+[lL]?(?=${p})`},{begin:`\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${p})`},{begin:`\\b(${f})[jJ](?=${p})`}]},_={className:"comment",begin:r.lookahead(/# type:/),end:/$/,keywords:i,contains:[{begin:/# type:/},{begin:/#/,end:/\b\B/,endsWithParent:!0}]},m={className:"params",variants:[{className:"",begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:i,contains:["self",a,b,c,e.HASH_COMMENT_MODE]}]};return s.contains=[c,b,a],{name:"Python",aliases:["py","gyp","ipython"],unicodeRegex:!0,keywords:i,illegal:/(<\/|\?)|=>/,contains:[a,b,{begin:/\bself\b/},{beginKeywords:"if",relevance:0},c,_,e.HASH_COMMENT_MODE,{match:[/\bdef/,/\s+/,l],scope:{1:"keyword",3:"title.function"},contains:[m]},{variants:[{match:[/\bclass/,/\s+/,l,/\s*/,/\(\s*/,l,/\s*\)/]},{match:[/\bclass/,/\s+/,l]}],scope:{1:"keyword",3:"title.class",6:"title.class.inherited"}},{className:"meta",begin:/^[\t ]*@/,end:/(?=#)|$/,contains:[b,m,c]}]}}var ce="[0-9](_*[0-9])*",me=`\\.(${ce})`,ve="[0-9a-fA-F](_*[0-9a-fA-F])*",gn={className:"number",variants:[{begin:`(\\b(${ce})((${me})|\\.)?|(${me}))[eE][+-]?(${ce})[fFdD]?\\b`},{begin:`\\b(${ce})((${me})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${me})[fFdD]?\\b`},{begin:`\\b(${ce})[fFdD]\\b`},{begin:`\\b0[xX]((${ve})\\.?|(${ve})?\\.(${ve}))[pP][+-]?(${ce})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${ve})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};function hn(e,r,l){return l===-1?"":e.replace(r,u=>hn(e,r,l-1))}function Ct(e){const r=e.regex,l="[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*",u=l+hn("(?:<"+l+"~~~(?:\\s*,\\s*"+l+"~~~)*>)?",/~~~/g,2),a={keyword:["synchronized","abstract","private","var","static","if","const ","for","while","strictfp","finally","protected","import","native","final","void","enum","else","break","transient","catch","instanceof","volatile","case","assert","package","default","public","try","switch","continue","throws","protected","public","private","module","requires","exports","do","sealed","yield","permits"],literal:["false","true","null"],type:["char","boolean","long","float","int","byte","short","double"],built_in:["super","this"]},s={className:"meta",begin:"@"+l,contains:[{begin:/\(/,end:/\)/,contains:["self"]}]},o={className:"params",begin:/\(/,end:/\)/,keywords:a,relevance:0,contains:[e.C_BLOCK_COMMENT_MODE],endsParent:!0};return{name:"Java",aliases:["jsp"],keywords:a,illegal:/<\/|#/,contains:[e.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",begin:"@[A-Za-z]+"}]}),{begin:/import java\.[a-z]+\./,keywords:"import",relevance:2},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{begin:/"""/,end:/"""/,className:"string",contains:[e.BACKSLASH_ESCAPE]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,{match:[/\b(?:class|interface|enum|extends|implements|new)/,/\s+/,l],className:{1:"keyword",3:"title.class"}},{match:/non-sealed/,scope:"keyword"},{begin:[r.concat(/(?!else)/,l),/\s+/,l,/\s+/,/=(?!=)/],className:{1:"type",3:"variable",5:"operator"}},{begin:[/record/,/\s+/,l],className:{1:"keyword",3:"title.class"},contains:[o,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{beginKeywords:"new throw return else",relevance:0},{begin:["(?:"+u+"\\s+)",e.UNDERSCORE_IDENT_RE,/\s*(?=\()/],className:{2:"title.function"},keywords:a,contains:[{className:"params",begin:/\(/,end:/\)/,keywords:a,relevance:0,contains:[s,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,gn,e.C_BLOCK_COMMENT_MODE]},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},gn,s]}}function It(e){const r=e.regex,l={},u={begin:/\$\{/,end:/\}/,contains:["self",{begin:/:-/,contains:[l]}]};Object.assign(l,{className:"variable",variants:[{begin:r.concat(/\$[\w\d#@][\w\d_]*/,"(?![\\w\\d])(?![$])")},u]});const h={className:"subst",begin:/\$\(/,end:/\)/,contains:[e.BACKSLASH_ESCAPE]},n={begin:/<<-?\s*(?=\w+)/,starts:{contains:[e.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,className:"string"})]}},t={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,l,h]};h.contains.push(t);const i={match:/\\"/},a={className:"string",begin:/'/,end:/'/},s={match:/\\'/},o={begin:/\$?\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},e.NUMBER_MODE,l]},c=["fish","bash","zsh","sh","csh","ksh","tcsh","dash","scsh"],f=e.SHEBANG({binary:`(${c.join("|")})`,relevance:10}),g={className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0},p=["if","then","else","elif","fi","for","while","until","in","do","done","case","esac","function","select"],b=["true","false"],_={match:/(\/[a-z._-]+)+/},m=["break","cd","continue","eval","exec","exit","export","getopts","hash","pwd","readonly","return","shift","test","times","trap","umask","unset"],v=["alias","bind","builtin","caller","command","declare","echo","enable","help","let","local","logout","mapfile","printf","read","readarray","source","type","typeset","ulimit","unalias"],N=["autoload","bg","bindkey","bye","cap","chdir","clone","comparguments","compcall","compctl","compdescribe","compfiles","compgroups","compquote","comptags","comptry","compvalues","dirs","disable","disown","echotc","echoti","emulate","fc","fg","float","functions","getcap","getln","history","integer","jobs","kill","limit","log","noglob","popd","print","pushd","pushln","rehash","sched","setcap","setopt","stat","suspend","ttyctl","unfunction","unhash","unlimit","unsetopt","vared","wait","whence","where","which","zcompile","zformat","zftp","zle","zmodload","zparseopts","zprof","zpty","zregexparse","zsocket","zstyle","ztcp"],w=["chcon","chgrp","chown","chmod","cp","dd","df","dir","dircolors","ln","ls","mkdir","mkfifo","mknod","mktemp","mv","realpath","rm","rmdir","shred","sync","touch","truncate","vdir","b2sum","base32","base64","cat","cksum","comm","csplit","cut","expand","fmt","fold","head","join","md5sum","nl","numfmt","od","paste","ptx","pr","sha1sum","sha224sum","sha256sum","sha384sum","sha512sum","shuf","sort","split","sum","tac","tail","tr","tsort","unexpand","uniq","wc","arch","basename","chroot","date","dirname","du","echo","env","expr","factor","groups","hostid","id","link","logname","nice","nohup","nproc","pathchk","pinky","printenv","printf","pwd","readlink","runcon","seq","sleep","stat","stdbuf","stty","tee","test","timeout","tty","uname","unlink","uptime","users","who","whoami","yes"];return{name:"Bash",aliases:["sh"],keywords:{$pattern:/\b[a-z][a-z0-9._-]+\b/,keyword:p,literal:b,built_in:[...m,...v,"set","shopt",...N,...w]},contains:[f,e.SHEBANG(),g,o,e.HASH_COMMENT_MODE,n,_,t,i,a,s,l]}}function Bt(e){const r=e.regex,l=e.COMMENT("--","$"),u={className:"string",variants:[{begin:/'/,end:/'/,contains:[{begin:/''/}]}]},h={begin:/"/,end:/"/,contains:[{begin:/""/}]},n=["true","false","unknown"],t=["double precision","large object","with timezone","without timezone"],i=["bigint","binary","blob","boolean","char","character","clob","date","dec","decfloat","decimal","float","int","integer","interval","nchar","nclob","national","numeric","real","row","smallint","time","timestamp","varchar","varying","varbinary"],a=["add","asc","collation","desc","final","first","last","view"],s=["abs","acos","all","allocate","alter","and","any","are","array","array_agg","array_max_cardinality","as","asensitive","asin","asymmetric","at","atan","atomic","authorization","avg","begin","begin_frame","begin_partition","between","bigint","binary","blob","boolean","both","by","call","called","cardinality","cascaded","case","cast","ceil","ceiling","char","char_length","character","character_length","check","classifier","clob","close","coalesce","collate","collect","column","commit","condition","connect","constraint","contains","convert","copy","corr","corresponding","cos","cosh","count","covar_pop","covar_samp","create","cross","cube","cume_dist","current","current_catalog","current_date","current_default_transform_group","current_path","current_role","current_row","current_schema","current_time","current_timestamp","current_path","current_role","current_transform_group_for_type","current_user","cursor","cycle","date","day","deallocate","dec","decimal","decfloat","declare","default","define","delete","dense_rank","deref","describe","deterministic","disconnect","distinct","double","drop","dynamic","each","element","else","empty","end","end_frame","end_partition","end-exec","equals","escape","every","except","exec","execute","exists","exp","external","extract","false","fetch","filter","first_value","float","floor","for","foreign","frame_row","free","from","full","function","fusion","get","global","grant","group","grouping","groups","having","hold","hour","identity","in","indicator","initial","inner","inout","insensitive","insert","int","integer","intersect","intersection","interval","into","is","join","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","language","large","last_value","lateral","lead","leading","left","like","like_regex","listagg","ln","local","localtime","localtimestamp","log","log10","lower","match","match_number","match_recognize","matches","max","member","merge","method","min","minute","mod","modifies","module","month","multiset","national","natural","nchar","nclob","new","no","none","normalize","not","nth_value","ntile","null","nullif","numeric","octet_length","occurrences_regex","of","offset","old","omit","on","one","only","open","or","order","out","outer","over","overlaps","overlay","parameter","partition","pattern","per","percent","percent_rank","percentile_cont","percentile_disc","period","portion","position","position_regex","power","precedes","precision","prepare","primary","procedure","ptf","range","rank","reads","real","recursive","ref","references","referencing","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","release","result","return","returns","revoke","right","rollback","rollup","row","row_number","rows","running","savepoint","scope","scroll","search","second","seek","select","sensitive","session_user","set","show","similar","sin","sinh","skip","smallint","some","specific","specifictype","sql","sqlexception","sqlstate","sqlwarning","sqrt","start","static","stddev_pop","stddev_samp","submultiset","subset","substring","substring_regex","succeeds","sum","symmetric","system","system_time","system_user","table","tablesample","tan","tanh","then","time","timestamp","timezone_hour","timezone_minute","to","trailing","translate","translate_regex","translation","treat","trigger","trim","trim_array","true","truncate","uescape","union","unique","unknown","unnest","update","upper","user","using","value","values","value_of","var_pop","var_samp","varbinary","varchar","varying","versioning","when","whenever","where","width_bucket","window","with","within","without","year"],o=["abs","acos","array_agg","asin","atan","avg","cast","ceil","ceiling","coalesce","corr","cos","cosh","count","covar_pop","covar_samp","cume_dist","dense_rank","deref","element","exp","extract","first_value","floor","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","last_value","lead","listagg","ln","log","log10","lower","max","min","mod","nth_value","ntile","nullif","percent_rank","percentile_cont","percentile_disc","position","position_regex","power","rank","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","row_number","sin","sinh","sqrt","stddev_pop","stddev_samp","substring","substring_regex","sum","tan","tanh","translate","translate_regex","treat","trim","trim_array","unnest","upper","value_of","var_pop","var_samp","width_bucket"],c=["current_catalog","current_date","current_default_transform_group","current_path","current_role","current_schema","current_transform_group_for_type","current_user","session_user","system_time","system_user","current_time","localtime","current_timestamp","localtimestamp"],f=["create table","insert into","primary key","foreign key","not null","alter table","add constraint","grouping sets","on overflow","character set","respect nulls","ignore nulls","nulls first","nulls last","depth first","breadth first"],g=o,p=[...s,...a].filter(N=>!o.includes(N)),b={className:"variable",begin:/@[a-z0-9][a-z0-9_]*/},_={className:"operator",begin:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,relevance:0},m={begin:r.concat(/\b/,r.either(...g),/\s*\(/),relevance:0,keywords:{built_in:g}};function v(N,{exceptions:w,when:R}={}){const D=R;return w=w||[],N.map(T=>T.match(/\|\d+$/)||w.includes(T)?T:D(T)?`${T}|0`:T)}return{name:"SQL",case_insensitive:!0,illegal:/[{}]|<\//,keywords:{$pattern:/\b[\w\.]+/,keyword:v(p,{when:N=>N.length<3}),literal:n,type:i,built_in:c},contains:[{begin:r.either(...f),relevance:0,keywords:{$pattern:/[\w\.]+/,keyword:p.concat(f),literal:n,type:i}},{className:"type",begin:r.either(...t)},m,b,u,h,e.C_NUMBER_MODE,e.C_BLOCK_COMMENT_MODE,l,_]}}Y.registerLanguage("xml",Nt),Y.registerLanguage("javascript",Dt),Y.registerLanguage("json",Rt),Y.registerLanguage("yaml",Lt),Y.registerLanguage("plaintext",Ot),Y.registerLanguage("python",kt),Y.registerLanguage("java",Ct),Y.registerLanguage("bash",It),Y.registerLanguage("sql",Bt);var A=(e=>(e.EQUAL="equal",e.DELETE="removed",e.ADD="added",e.EMPTY="empty",e))(A||{});const ee="<code-diff-modified>",ne="</code-diff-modified>",$t=ee.replace("<","&lt;").replace(">","&gt;"),Pt=ne.replace("<","&lt;").replace(">","&gt;");function ue(e){return e===void 0?A.EQUAL:e.added?A.ADD:e.removed?A.DELETE:A.EQUAL}function ye(e,r,l="word"){return typeof e=="undefined"?r:typeof r=="undefined"?e:(l==="char"?An:Sn)(e,r).filter(h=>ue(h)!==A.DELETE).map(h=>ue(h)===A.ADD?`${ee}${h.value}${ne}`:h.value).join("")}function dn(e,r){const l=new Re.diff_match_patch,u=l.diff_linesToChars_(e,r),h=u.chars1,n=u.chars2,t=u.lineArray,i=l.diff_main(h,n,!1);return l.diff_charsToLines_(i,t),i.map(a=>{const[s,o]=a;return{count:o.replace(/\n$/,"").split(`
`).length,value:o,removed:s===Re.DIFF_DELETE,added:s===Re.DIFF_INSERT}})}function X(e,r){if(!r.match(new RegExp(`(${ee}|${ne})`,"g")))return Y.highlight(r,{language:e}).value;let u=r;const h=r.replace(new RegExp(`(${ee}|${ne})`,"g"),""),n=document.createElement("div");n.innerHTML=Y.highlight(h,{language:e}).value;let t=!1;const i=a=>{a.childNodes.forEach(s=>{if(s.nodeType===Node.ELEMENT_NODE&&i(s),s.nodeType===Node.TEXT_NODE){if(!s.textContent)return;let o=s.textContent,c="";for(t&&(c=c+ee);o.length;){if(u.startsWith(ee)){u=u.slice(ee.length),c=c+ee,t=!0;continue}if(u.startsWith(ne)){u=u.slice(ne.length),c=c+ne,t=!1;continue}const f=u.match(new RegExp(`(${ee}|${ne})`)),g=f&&f.index?f.index:u.length,p=Math.min(g,o.length);c=c+u.substring(0,p),u=u.slice(p),o=o.slice(p)}t&&(c=c+ne),s.textContent=c}})};return i(n),n.innerHTML.replace(new RegExp($t,"g"),'<span class="x">').replace(new RegExp(Pt,"g"),"</span>")}function pn(e){const r=(h,n)=>(h.match(new RegExp(n,"g"))||[]).length;let l=0,u=0;for(const h of e)h.added&&(l+=r(h.value.trim(),`
`)+1),h.removed&&(u+=r(h.value.trim(),`
`)+1);return{additionsNum:l,deletionsNum:u}}function Ut(e,r,l="plaintext",u="word",h=10){const n=()=>({type:A.EMPTY}),t=(b,_,m)=>({type:b,num:_,code:m}),i=dn(e,r);let a=0,s=0,o=!1;const c=[],f={changes:c,collector:[],stat:pn(i)};for(let b=0;b<i.length;b++){if(o){o=!1;continue}const[_,m]=[i[b],i[b+1]],[v,N]=[ue(_),ue(m)],w=_.value.replace(/\n$/,"").split(`
`);if(m===void 0){for(const D of w){let T=n(),I=n();const L=X(l,D);v===A.EQUAL&&(a++,s++,T=t(A.EQUAL,a,L),I=t(A.EQUAL,s,L)),v===A.DELETE&&(a++,T=t(A.DELETE,a,L),I=n()),v===A.ADD&&(s++,T=n(),I=t(A.ADD,s,L)),c.push({left:T,right:I})}break}if(v===A.EQUAL)for(const D of w){a++,s++;const T=X(l,D);c.push({left:t(A.EQUAL,a,T),right:t(A.EQUAL,s,T)})}const R=m.value.replace(/\n$/,"").split(`
`);if(v===A.DELETE){if(N===A.EQUAL)for(const D of w)a++,c.push({left:t(A.DELETE,a,X(l,D)),right:n()});if(N===A.ADD){o=!0;const D=Math.max(_.count,m.count);for(let T=0;T<D;T++){T<_.count&&a++,T<m.count&&s++;const[I,L]=[w[T],R[T]],F=w.length===R.length?ye(L,I,u):I,U=w.length===R.length?ye(I,L,u):L,P=T<_.count?t(A.DELETE,a,X(l,F)):n(),z=T<m.count?t(A.ADD,s,X(l,U)):n();c.push({left:P,right:z})}}}if(v===A.ADD)for(const D of w)s++,c.push({left:n(),right:t(A.ADD,s,X(l,D))})}if(e===r){for(let b=0;b<c.length;b++)c[b].fold=!1;return f}for(let b=0;b<c.length;b++){const _=c[b];if(_.left.type===A.DELETE||_.right.type===A.ADD){const[m,v]=[Math.max(b-h,0),Math.min(b+h+1,c.length)];for(let N=m;N<v;N++)c[N].fold=!1}_.fold===void 0&&(_.fold=!0)}const g=[];let p=[];for(let b=0;b<c.length;b++){const _=c[b];if(_.fold===!1){p.length&&(p[0].hideIndex=f.collector.length,f.collector.push({lines:p,fold:!0}),p=[]),g.push(_);continue}_.hide=!0,p.push(_),g.push(_)}return p.length&&(p[0].hideIndex=f.collector.length,f.collector.push({lines:p,fold:!0}),p=[]),f.changes=g,f}function xt(e,r,l="plaintext",u="word",h=10){const n=dn(e,r);let t=0,i=0,a=!1;const s=[],o={changes:s,collector:[],stat:pn(n)};for(let g=0;g<n.length;g++){if(a){a=!1;continue}const[p,b]=[n[g],n[g+1]],[_,m]=[ue(p),ue(b)],v=p.value.replace(/\n$/,"").split(`
`);if(b===void 0){for(const w of v){_===A.EQUAL&&(t++,i++),_===A.DELETE&&t++,_===A.ADD&&i++;const R=X(l,w);s.push({type:_,code:R,addNum:_===A.DELETE?void 0:i,delNum:_===A.ADD?void 0:t})}break}if(_===A.EQUAL)for(const w of v){t++,i++;const R=X(l,w);s.push({type:A.EQUAL,code:R,delNum:t,addNum:i})}const N=b.value.replace(/\n$/,"").split(`
`);if(_===A.DELETE)if(m===A.ADD&&v.length===N.length){for(let w=0;w<v.length;w++){const R=v[w],D=N[w];t++;const T=X(l,ye(D,R,u));s.push({type:A.DELETE,code:T,delNum:t})}for(let w=0;w<N.length;w++){const R=v[w],D=N[w];i++;const T=X(l,ye(R,D,u));s.push({type:A.ADD,code:T,addNum:i})}a=!0}else for(const w of v){t++;const R=X(l,w);s.push({type:A.DELETE,code:R,delNum:t})}if(_===A.ADD)for(const w of v){i++;const R=X(l,w);s.push({type:A.ADD,code:R,addNum:i})}}for(let g=0;g<s.length;g++){const p=s[g];if(p.type===A.DELETE||p.type===A.ADD){const[b,_]=[Math.max(g-h,0),Math.min(g+h+1,s.length)];for(let m=b;m<_;m++)s[m].fold=!1}p.fold===void 0&&(p.fold=!0)}if(e===r){for(let g=0;g<s.length;g++)s[g].fold=!1;return o}const c=[];let f=[];for(let g=0;g<s.length;g++){const p=s[g];if(p.fold===!1){f.length&&(f[0].hideIndex=o.collector.length,o.collector.push({lines:f,fold:!0}),f=[]),c.push(p);continue}p.type==="equal"&&(p.hide=!0,f.push(p)),c.push(p)}return f.length&&(f[0].hideIndex=o.collector.length,o.collector.push({lines:f,fold:!0}),f=[]),o.changes=c,o}const Ht={key:0},Ft=d.createElementVNode("td",{class:"blob-code blob-code-hunk",align:"left"}," ⋯ ",-1),zt={key:1},Gt=["data-code-marker","innerHTML"],Vt=d.defineComponent({__name:"UnifiedLine",props:{line:null},emits:["expand"],setup(e,{emit:r}){function l(u){return u===A.DELETE?"-":u===A.ADD?"+":""}return(u,h)=>e.line.hideIndex!==void 0&&e.line.hide?(d.openBlock(),d.createElementBlock("tr",Ht,[d.createElementVNode("td",{class:"blob-num blob-num-hunk text-center",colspan:"2",onClick:h[0]||(h[0]=n=>r("expand",e.line))}," > "),Ft])):e.line.hide?d.createCommentVNode("",!0):(d.openBlock(),d.createElementBlock("tr",zt,[d.createElementVNode("td",{class:d.normalizeClass(["blob-num",{"blob-num-deletion":e.line.type===d.unref(A).DELETE,"blob-num-addition":e.line.type===d.unref(A).ADD,"blob-num-context":e.line.type===d.unref(A).EQUAL,"blob-num-hunk":e.line.hide!==void 0}])},d.toDisplayString(e.line.delNum),3),d.createElementVNode("td",{class:d.normalizeClass(["blob-num",{"blob-num-deletion":e.line.type===d.unref(A).DELETE,"blob-num-addition":e.line.type===d.unref(A).ADD,"blob-num-context":e.line.type===d.unref(A).EQUAL,"blob-num-hunk":e.line.hide!==void 0}])},d.toDisplayString(e.line.addNum),3),d.createElementVNode("td",{class:d.normalizeClass(["blob-code",{"blob-code-deletion":e.line.type===d.unref(A).DELETE,"blob-code-addition":e.line.type===d.unref(A).ADD,"blob-code-context":e.line.type===d.unref(A).EQUAL,"blob-code-hunk":e.line.hide!==void 0}])},[d.createElementVNode("span",{class:"blob-code-inner blob-code-marker","data-code-marker":l(e.line.type),innerHTML:e.line.code},null,8,Gt)],2)]))}}),Kt={class:"diff-table"},Wt=d.defineComponent({__name:"UnifiedViewer",props:{diffChange:null},setup(e){const r=e;function l({hideIndex:u}){u!==void 0&&r.diffChange.collector[u].lines.forEach(h=>{h.hide=!1,h.fold=!1})}return(u,h)=>{var n;return d.openBlock(),d.createElementBlock("table",Kt,[d.createElementVNode("tbody",null,[(d.openBlock(!0),d.createElementBlock(d.Fragment,null,d.renderList((n=e.diffChange)==null?void 0:n.changes,(t,i)=>(d.openBlock(),d.createBlock(Vt,{key:i,line:t,onExpand:l},null,8,["line"]))),128))])])}}}),Qt={key:0},Zt=d.createElementVNode("td",{class:"blob-code blob-code-inner blob-code-hunk",colspan:"3",align:"left"}," ⋯ ",-1),Yt={key:1},Xt=d.createElementVNode("td",{class:"blob-num blob-num-empty empty-cell"},null,-1),jt=d.createElementVNode("td",{class:"blob-code blob-code-empty empty-cell"},null,-1),Jt=["onMousedown"],qt=["data-code-marker","innerHTML"],er=d.defineComponent({__name:"SplitLine",props:{splitLine:null},emits:["expand"],setup(e,{emit:r}){function l(h){return h===A.DELETE?"-":h===A.ADD?"+":""}function u(h){window.getSelection().removeAllRanges();const n=document.querySelectorAll(".file-diff-split .split-side-left"),t=document.querySelectorAll(".file-diff-split .split-side-right");for(const i of t)i.classList.toggle("no-select",h==="left");for(const i of n)i.classList.toggle("no-select",h==="right")}return(h,n)=>e.splitLine.hideIndex!==void 0&&e.splitLine.hide?(d.openBlock(),d.createElementBlock("tr",Qt,[d.createElementVNode("td",{class:"blob-num blob-num-hunk",colspan:"1",onClick:n[0]||(n[0]=t=>r("expand",e.splitLine))}," > "),Zt])):e.splitLine.hide?d.createCommentVNode("",!0):(d.openBlock(),d.createElementBlock("tr",Yt,[(d.openBlock(!0),d.createElementBlock(d.Fragment,null,d.renderList([e.splitLine.left,e.splitLine.right],(t,i)=>(d.openBlock(),d.createElementBlock(d.Fragment,null,[t.type===d.unref(A).EMPTY?(d.openBlock(),d.createElementBlock(d.Fragment,{key:0},[Xt,jt],64)):(d.openBlock(),d.createElementBlock(d.Fragment,{key:1},[d.createElementVNode("td",{class:d.normalizeClass(["blob-num",{"blob-num-deletion":t.type===d.unref(A).DELETE,"blob-num-addition":t.type===d.unref(A).ADD,"blob-num-context":t.type===d.unref(A).EQUAL,"blob-num-hunk":e.splitLine.hide!==void 0}])},d.toDisplayString(t.num),3),d.createElementVNode("td",{class:d.normalizeClass(["blob-code",{"blob-code-deletion":t.type===d.unref(A).DELETE,"blob-code-addition":t.type===d.unref(A).ADD,"blob-code-context":t.type===d.unref(A).EQUAL,"blob-code-hunk":e.splitLine.hide!==void 0,"split-side-left":i===0,"split-side-right":i===1}]),onMousedown:a=>u(i===0?"left":"right")},[d.createElementVNode("span",{class:"blob-code-inner blob-code-marker","data-code-marker":l(t.type),innerHTML:t.code},null,8,qt)],42,Jt)],64))],64))),256))]))}}),nr={class:"file-diff-split diff-table"},tr=d.createElementVNode("colgroup",null,[d.createElementVNode("col",{width:"44"}),d.createElementVNode("col"),d.createElementVNode("col",{width:"44"}),d.createElementVNode("col")],-1),rr=d.defineComponent({__name:"SplitViewer",props:{diffChange:null},setup(e){const r=e;function l({hideIndex:u}){u!==void 0&&r.diffChange.collector[u].lines.forEach(h=>{h.hide=!1,h.fold=!1})}return(u,h)=>{var n;return d.openBlock(),d.createElementBlock("table",nr,[tr,d.createElementVNode("tbody",null,[(d.openBlock(!0),d.createElementBlock(d.Fragment,null,d.renderList((n=e.diffChange)==null?void 0:n.changes,(t,i)=>(d.openBlock(),d.createBlock(er,{key:i,"split-line":t,onExpand:l},null,8,["split-line"]))),128))])])}}}),Rr="",ir=["theme"],sr={key:0,class:"file-header"},ar={key:0,class:"file-info"},or={class:"info-left"},lr={class:"info-left"},cr={key:0,class:"diff-stat"},ur={class:"diff-stat-added"},fr={class:"diff-stat-deleted",style:{"margin-left":"8px"}},gr={key:1,class:"file-info"},hr={class:"info-left"},dr={class:"info-right"},pr={style:{"margin-left":"20px"}},br={key:0,class:"diff-stat"},_r={class:"diff-stat-added"},Er={class:"diff-stat-deleted",style:{"margin-left":"8px"}},mr=d.defineComponent({__name:"CodeDiff",props:{newString:null,oldString:null,language:{default:"plaintext"},context:{default:10},diffStyle:{default:"word"},outputFormat:{default:"line-by-line"},trim:{type:Boolean,default:!1},noDiffLineFeed:{type:Boolean,default:!1},maxHeight:{default:void 0},filename:{default:void 0},newFilename:{default:void 0},hideHeader:{type:Boolean,default:!1},hideStat:{type:Boolean,default:!1},theme:{default:"light"}},emits:["diff"],setup(e,{emit:r}){const l=e,u=d.computed(()=>l.outputFormat==="line-by-line"),h=d.computed(()=>{let s=l.oldString||"";return s=l.trim?s.trim():s,s=l.noDiffLineFeed?s.replace(/(\r\n)/g,`
`):s,s}),n=d.computed(()=>{let s=l.newString||"";return s=l.trim?s.trim():s,s=l.noDiffLineFeed?s.replace(/(\r\n)/g,`
`):s,s}),t=d.computed(()=>u.value?xt(h.value,n.value,l.language,l.diffStyle,l.context):Ut(h.value,n.value,l.language,l.diffStyle,l.context)),i=d.ref(t.value),a=d.computed(()=>i.value.stat.additionsNum===0&&i.value.stat.deletionsNum===0);return d.watch(()=>l,()=>{i.value=t.value,r("diff",{stat:{isChanged:!a.value,addNum:i.value.stat.additionsNum,delNum:i.value.stat.deletionsNum}})},{deep:!0,immediate:!0}),(s,o)=>(d.openBlock(),d.createElementBlock("div",{class:"code-diff-view",theme:e.theme,style:d.normalizeStyle({maxHeight:e.maxHeight})},[e.hideHeader?d.createCommentVNode("",!0):(d.openBlock(),d.createElementBlock("div",sr,[d.unref(u)?(d.openBlock(),d.createElementBlock("div",ar,[d.createElementVNode("span",null,[d.createElementVNode("div",or,d.toDisplayString(e.filename),1),d.createElementVNode("div",lr,d.toDisplayString(e.newFilename),1)]),e.hideStat?d.createCommentVNode("",!0):(d.openBlock(),d.createElementBlock("span",cr,[d.renderSlot(s.$slots,"stat",{},()=>[d.createElementVNode("span",ur,"+"+d.toDisplayString(d.unref(i).stat.additionsNum)+" additions",1),d.createElementVNode("span",fr,"-"+d.toDisplayString(d.unref(i).stat.deletionsNum)+" deletions",1)])]))])):(d.openBlock(),d.createElementBlock("div",gr,[d.createElementVNode("span",hr,d.toDisplayString(e.filename),1),d.createElementVNode("span",dr,[d.createElementVNode("span",pr,d.toDisplayString(e.newFilename),1),e.hideStat?d.createCommentVNode("",!0):(d.openBlock(),d.createElementBlock("span",br,[d.renderSlot(s.$slots,"stat",{},()=>[d.createElementVNode("span",_r,"+"+d.toDisplayString(d.unref(i).stat.additionsNum)+" additions",1),d.createElementVNode("span",Er,"-"+d.toDisplayString(d.unref(i).stat.deletionsNum)+" deletions",1)])]))])]))])),d.unref(u)?(d.openBlock(),d.createBlock(Wt,{key:1,"diff-change":d.unref(i)},null,8,["diff-change"])):(d.openBlock(),d.createBlock(rr,{key:2,"diff-change":d.unref(i)},null,8,["diff-change"]))],12,ir))}});function vr(e){e.component("CodeDiff",mr)}return{install:vr,hljs:Y}});
