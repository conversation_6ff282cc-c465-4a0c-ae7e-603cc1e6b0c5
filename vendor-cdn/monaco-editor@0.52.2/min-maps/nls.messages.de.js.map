{"version": 3, "sources": ["out-editor/nls.messages.de.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\nglobalThis._VSCODE_NLS_MESSAGES=[\"{0} ({1})\",\"Eingabe\",\"Groß-/Kleinschreibung beachten\",\"Nur ganzes Wort suchen\",\"Regulären Ausdruck verwenden\",\"Eingabe\",\"Groß-/Kleinschreibung beibehalten\",\"<PERSON>ber<PERSON><PERSON><PERSON><PERSON> dies in der barrierefreien Ansicht mit \\\"{0}\\\".\",\"Überprüfen Sie dies in der barrierefreien Ansicht über den Befehl \\\"Barrierefreie Ansicht öffnen\\\", der zurzeit nicht über eine Tastenzuordnung ausgelöst werden kann.\",\"Fehler: {0}\",\"Warnung: {0}\",\"Info: {0}\",\" oder {0} für Verlauf\",\" ({0} für Verlauf)\",\"Gelöschte Eingabe\",\"Ungebunden\",\"Auswahlfeld\",\"Weitere Aktionen...\",\"Filter\",\"Fuzzyübereinstimmung\",\"Zum Filtern Text eingeben\",\"Zum Suchen eingeben\",\"Zum Suchen eingeben\",\"Schließen\",\"Keine Ergebnisse\",\"Keine Ergebnisse gefunden.\",null,\"(leer)\",\"{0}: {1}\",\"Ein Systemfehler ist aufgetreten ({0}).\",\"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.\",\"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.\",\"{0} ({1} Fehler gesamt)\",\"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.\",\"STRG\",\"UMSCHALTTASTE\",\"ALT\",\"Windows\",\"STRG\",\"UMSCHALTTASTE\",\"ALT\",\"Super\",\"Steuern\",\"UMSCHALTTASTE\",\"Option\",\"Befehl\",\"Steuern\",\"UMSCHALTTASTE\",\"ALT\",\"Windows\",\"Steuern\",\"UMSCHALTTASTE\",\"ALT\",\"Super\",null,null,null,null,null,\"Auch bei längeren Zeilen am Ende bleiben\",\"Auch bei längeren Zeilen am Ende bleiben\",\"Sekundäre Cursor entfernt\",\"&&Rückgängig\",\"Rückgängig\",\"&&Wiederholen\",\"Wiederholen\",\"&&Alles auswählen\",\"Alle auswählen\",\"Halten Sie die {0}-Taste gedrückt, um mit der Maus darauf zu zeigen.\",\"Wird geladen...\",\"Die Anzahl der Cursor wurde auf {0} beschränkt. Erwägen Sie die Verwendung von [Suchen und Ersetzen](https://code.visualstudio.com/docs/editor/codebasics#_find-und-ersetzen) für größere Änderungen, oder erhöhen Sie die Multicursorbegrenzungseinstellung des Editors.\",\"Erhöhen des Grenzwerts für mehrere Cursor\",\"\\\"Unveränderte Bereiche reduzieren\\\" umschalten\",\"\\\"Verschobene Codeblöcke anzeigen\\\" umschalten\",\"\\\"Bei eingeschränktem Speicherplatz Inlineansicht verwenden\\\" umschalten\",\"Diff-Editor\",\"Seite wechseln\",\"Vergleichsmodus beenden\",\"Alle unveränderten Regionen reduzieren\",\"Alle unveränderten Regionen anzeigen\",\"Zurücksetzen\",\"Barrierefreier Diff-Viewer\",\"Zum nächsten Unterschied wechseln\",\"Zum vorherigen Unterschied wechseln\",\"Symbol für \\\"Einfügen\\\" im barrierefreien Diff-Viewer.\",\"Symbol für \\\"Entfernen\\\" im barrierefreien Diff-Viewer.\",\"Symbol für \\\"Schließen\\\" im barrierefreien Diff-Viewer.\",\"Schließen\",\"Barrierefreier Diff-Viewer. Verwenden Sie den Pfeil nach oben und unten, um zu navigieren.\",\"keine geänderten Zeilen\",\"1 Zeile geändert\",\"{0} Zeilen geändert\",\"Unterschied {0} von {1}: ursprüngliche Zeile {2}, {3}, geänderte Zeile {4}, {5}\",\"leer\",\"{0}: unveränderte Zeile {1}\",\"{0} ursprüngliche Zeile {1} geänderte Zeile {2}\",\"+ {0} geänderte Zeile(n) {1}\",\"– {0} Originalzeile {1}\",\" verwenden Sie {0}, um die Hilfe zur Barrierefreiheit zu öffnen.\",\"Gelöschte Zeilen kopieren\",\"Gelöschte Zeile kopieren\",\"Geänderte Zeilen kopieren\",\"Geänderte Zeile kopieren\",\"Gelöschte Zeile kopieren ({0})\",\"Geänderte Zeile ({0}) kopieren\",\"Diese Änderung rückgängig machen\",\"Bei eingeschränktem Speicherplatz Inlineansicht verwenden\",\"Verschobene Codeblöcke anzeigen\",\"Block wiederherstellen\",\"Auswahl wiederherstellen\",\"Barrierefreien Diff-Viewer öffnen\",\"Unveränderten Bereich falten\",\"{0} ausgeblendete Linien\",\"Klicken oder ziehen Sie, um oben mehr anzuzeigen.\",\"Unveränderte Regionen anzeigen\",\"Klicken oder ziehen Sie, um unten mehr anzuzeigen.\",\"{0} ausgeblendete Linien\",\"Zum Auffalten doppelklicken\",\"Code mit Änderungen in Zeile {0}-{1} verschoben\",\"Code mit Änderungen aus Zeile {0}-{1} verschoben\",\"Code in Zeile {0}-{1} verschoben\",\"Code aus Zeile {0}-{1} verschoben\",\"Ausgewählte Änderungen zurücksetzen\",\"Änderung zurücksetzen\",\"Die Rahmenfarbe für Text, der im Diff-Editor verschoben wurde.\",\"Die aktive Rahmenfarbe für Text, der im Diff-Editor verschoben wurde.\",\"Die Farbe des Schattens um unveränderte Regionswidgets.\",\"Zeilenformatierung für Einfügungen im Diff-Editor\",\"Zeilenformatierung für Entfernungen im Diff-Editor\",\"Die Hintergrundfarbe des Diff-Editor-Headers\",\"Die Hintergrundfarbe des Diff-Editors für mehrere Dateien\",\"Die Rahmenfarbe des Diff-Editors für mehrere Dateien\",\"Keine geänderten Dateien\",\"Editor\",\"Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn {0} aktiviert ist.\",\"Die Anzahl von Leerzeichen, die für den Einzug oder „tabSize“ verwendet werden, um den Wert aus „#editor.tabSize#“ zu verwenden. Diese Einstellung wird basierend auf dem Dateiinhalt überschrieben, wenn „#editor.detectIndentation#“ aktiviert ist.\",\"Fügt beim Drücken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn {0} aktiviert ist.\",\"Steuert, ob {0} und {1} automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt geöffnet wird.\",\"Nachfolgende automatisch eingefügte Leerzeichen entfernen\",\"Spezielle Behandlung für große Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.\",\"Deaktivieren Sie Word-basierte Vorschläge.\",\"Nur Wörter aus dem aktiven Dokument vorschlagen\",\"Wörter aus allen geöffneten Dokumenten derselben Sprache vorschlagen\",\"Wörter aus allen geöffneten Dokumenten vorschlagen\",\"Steuert, ob Vervollständigungen auf Grundlage der Wörter im Dokument berechnet werden sollen, und aus welchen Dokumenten sie berechnet werden sollen.\",\"Die semantische Hervorhebung ist für alle Farbdesigns aktiviert.\",\"Die semantische Hervorhebung ist für alle Farbdesigns deaktiviert.\",\"Die semantische Hervorhebung wird durch die Einstellung \\\"semanticHighlighting\\\" des aktuellen Farbdesigns konfiguriert.\",\"Steuert, ob die semantische Hervorhebung für die Sprachen angezeigt wird, die sie unterstützen.\",\"Lassen Sie Peek-Editoren geöffnet, auch wenn Sie auf ihren Inhalt doppelklicken oder auf die ESCAPETASTE klicken.\",\"Zeilen, die diese Länge überschreiten, werden aus Leistungsgründen nicht tokenisiert\",\"Steuert, ob die Tokenisierung asynchron auf einem Webworker erfolgen soll.\",\"Steuert, ob die asynchrone Tokenisierung protokolliert werden soll. Nur zum Debuggen.\",\"Steuert, ob die asynchrone Tokenisierung anhand der Legacy-Hintergrundtokenisierung überprüft werden soll. Die Tokenisierung kann verlangsamt werden. Nur zum Debuggen.\",\"Steuert, ob die Struktursitteranalyse aktiviert und Telemetriedaten erfasst werden sollen. Das Festlegen von \\\"editor.experimental.preferTreeSitter\\\" für bestimmte Sprachen hat Vorrang.\",\"Definiert die Klammersymbole, die den Einzug vergrößern oder verkleinern.\",\"Das öffnende Klammerzeichen oder die Zeichenfolgensequenz.\",\"Das schließende Klammerzeichen oder die Zeichenfolgensequenz.\",\"Definiert die Klammerpaare, die durch ihre Schachtelungsebene farbig formatiert werden, wenn die Farbgebung für das Klammerpaar aktiviert ist.\",\"Das öffnende Klammerzeichen oder die Zeichenfolgensequenz.\",\"Das schließende Klammerzeichen oder die Zeichenfolgensequenz.\",\"Timeout in Millisekunden, nach dem die Diff-Berechnung abgebrochen wird. Bei 0 wird kein Timeout verwendet.\",\"Maximale Dateigröße in MB, für die Diffs berechnet werden sollen. Verwenden Sie 0, um keinen Grenzwert zu setzen.\",\"Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.\",\"Wenn die Breite des Diff-Editors unter diesem Wert liegt, wird die Inlineansicht verwendet.\",\"Wenn diese Option aktiviert ist und die Breite des Editors nicht ausreicht, wird die Inlineansicht verwendet.\",\"Wenn diese Option aktiviert ist, zeigt der Diff-Editor Pfeile in seinem Glyphenrand an, um Änderungen rückgängig zu machen.\",\"Wenn diese Option aktiviert ist, zeigt der Diff-Editor einen speziellen Bundsteg für die Aktionen „Wiederherstellen“ und „Stagen“ an.\",\"Wenn aktiviert, ignoriert der Diff-Editor Änderungen an voran- oder nachgestellten Leerzeichen.\",\"Steuert, ob der Diff-Editor die Indikatoren \\\"+\\\" und \\\"-\\\" für hinzugefügte/entfernte Änderungen anzeigt.\",\"Steuert, ob der Editor CodeLens anzeigt.\",\"Zeilenumbrüche erfolgen nie.\",\"Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.\",\"Zeilen werden gemäß der Einstellung „{0}“ umbrochen.\",\"Verwendet den Legacyvergleichsalgorithmus.\",\"Verwendet den erweiterten Vergleichsalgorithmus.\",\"Steuert, ob der Diff-Editor unveränderte Regionen anzeigt.\",\"Steuert, wie viele Zeilen für unveränderte Regionen verwendet werden.\",\"Steuert, wie viele Zeilen als Mindestwert für unveränderte Regionen verwendet werden.\",\"Steuert, wie viele Zeilen beim Vergleich unveränderter Regionen als Kontext verwendet werden.\",\"Steuert, ob der Diff-Editor erkannte Codeverschiebevorgänge anzeigen soll.\",\"Steuert, ob der diff-Editor leere Dekorationen anzeigt, um anzuzeigen, wo Zeichen eingefügt oder gelöscht wurden.\",\"Wenn diese Option aktiviert ist und der Editor die Inlineansicht verwendet, werden Wortänderungen inline gerendert.\",\"Verwenden Sie Plattform-APIs, um zu erkennen, wenn eine Sprachausgabe angefügt ist.\",\"Optimieren Sie diese Option für die Verwendung mit einer Sprachausgabe.\",\"Hiermit wird angenommen, dass keine Sprachausgabe angefügt ist.\",\"Steuert, ob die Benutzeroberfläche in einem Modus ausgeführt werden soll, in dem sie für Sprachausgaben optimiert ist.\",\"Steuert, ob beim Kommentieren ein Leerzeichen eingefügt wird.\",\"Steuert, ob leere Zeilen bei Umschalt-, Hinzufügungs- oder Entfernungsaktionen für Zeilenkommentare ignoriert werden sollen.\",\"Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.\",\"Steuert, ob der Cursor bei der Suche nach Übereinstimmungen während der Eingabe springt.\",\"Suchzeichenfolge niemals aus der Editorauswahl seeden.\",\"Suchzeichenfolge immer aus der Editorauswahl seeden, einschließlich Wort an Cursorposition.\",\"Suchzeichenfolge nur aus der Editorauswahl seeden.\",\"Steuert, ob für die Suchzeichenfolge im Widget \\\"Suche\\\" ein Seeding aus der Auswahl des Editors ausgeführt wird.\",\"\\\"In Auswahl suchen\\\" niemals automatisch aktivieren (Standard).\",\"\\\"In Auswahl suchen\\\" immer automatisch aktivieren.\",\"\\\"In Auswahl suchen\\\" automatisch aktivieren, wenn mehrere Inhaltszeilen ausgewählt sind.\",\"Steuert die Bedingung zum automatischen Aktivieren von \\\"In Auswahl suchen\\\".\",\"Steuert, ob das Widget \\\"Suche\\\" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.\",\"Steuert, ob das Suchwidget zusätzliche Zeilen im oberen Bereich des Editors hinzufügen soll. Wenn die Option auf \\\"true\\\" festgelegt ist, können Sie über die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.\",\"Steuert, ob die Suche automatisch am Anfang (oder am Ende) neu gestartet wird, wenn keine weiteren Übereinstimmungen gefunden werden.\",\"Hiermit werden Schriftligaturen (Schriftartfeatures \\\"calt\\\" und \\\"liga\\\") aktiviert/deaktiviert. Ändern Sie diesen Wert in eine Zeichenfolge, um die CSS-Eigenschaft \\\"font-feature-settings\\\" detailliert zu steuern.\",\"Explizite CSS-Eigenschaft \\\"font-feature-settings\\\". Stattdessen kann ein boolescher Wert übergeben werden, wenn nur Ligaturen aktiviert/deaktiviert werden müssen.\",\"Hiermit werden Schriftligaturen oder Schriftartfeatures konfiguriert. Hierbei kann es sich entweder um einen booleschen Wert zum Aktivieren oder Deaktivieren von Ligaturen oder um eine Zeichenfolge für den Wert der CSS-Eigenschaft \\\"font-feature-settings\\\" handeln.\",\"Aktiviert/deaktiviert die Übersetzung von „font-weight“ in „font-variation-settings“. Ändern Sie dies in eine Zeichenfolge für eine differenzierte Steuerung der CSS-Eigenschaft „font-variation-settings“.\",\"Explizite CSS-Eigenschaft „font-variation-settings“. Stattdessen kann ein boolescher Wert eingeben werden, wenn nur „font-weight“ in „font-variation-settings“ übersetzt werden muss.\",\"Konfiguriert Variationen der Schriftart. Kann entweder ein boolescher Wert zum Aktivieren/Deaktivieren der Übersetzung von „font-weight“ in „font-variation-settings“ oder eine Zeichenfolge für den Wert der CSS-Eigenschaft „font-variation-settings“ sein.\",\"Legt die Schriftgröße in Pixeln fest.\",\"Es sind nur die Schlüsselwörter \\\"normal\\\" und \\\"bold\\\" sowie Zahlen zwischen 1 und 1000 zulässig.\",\"Steuert die Schriftbreite. Akzeptiert die Schlüsselwörter \\\"normal\\\" und \\\"bold\\\" sowie Zahlen zwischen 1 und 1000.\",\"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)\",\"Zum Hauptergebnis gehen und Vorschauansicht anzeigen\",\"Wechseln Sie zum primären Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.\",\"Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie \\\"editor.editor.gotoLocation.multipleDefinitions\\\" oder \\\"editor.editor.gotoLocation.multipleImplementations\\\".\",\"Legt das Verhalten des Befehls \\\"Gehe zu Definition\\\" fest, wenn mehrere Zielpositionen vorhanden sind\",\"Legt das Verhalten des Befehls \\\"Gehe zur Typdefinition\\\" fest, wenn mehrere Zielpositionen vorhanden sind.\",\"Legt das Verhalten des Befehls \\\"Gehe zu Deklaration\\\" fest, wenn mehrere Zielpositionen vorhanden sind.\",\"Legt das Verhalten des Befehls \\\"Gehe zu Implementierungen\\\", wenn mehrere Zielspeicherorte vorhanden sind\",\"Legt das Verhalten des Befehls \\\"Gehe zu Verweisen\\\" fest, wenn mehrere Zielpositionen vorhanden sind\",\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Definition\\\" die aktuelle Position ist.\",\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Typdefinition\\\" die aktuelle Position ist.\",\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Deklaration\\\" der aktuelle Speicherort ist.\",\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Implementatierung\\\" der aktuelle Speicherort ist.\",\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Verweis\\\" die aktuelle Position ist.\",\"Steuert, ob die Hovermarkierung angezeigt wird.\",\"Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.\",\"Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger darüber bewegt wird.\",\"Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung ausgeblendet wird. Erfordert die Aktivierung von \\\"editor.hover.sticky\\\".\",\"Zeigen Sie den Mauszeiger lieber über der Linie an, wenn Platz vorhanden ist.\",\"Es wird angenommen, dass alle Zeichen gleich breit sind. Dies ist ein schneller Algorithmus, der für Festbreitenschriftarten und bestimmte Alphabete (wie dem lateinischen), bei denen die Glyphen gleich breit sind, korrekt funktioniert.\",\"Delegiert die Berechnung von Umbruchpunkten an den Browser. Dies ist ein langsamer Algorithmus, der bei großen Dateien Code Freezes verursachen kann, aber in allen Fällen korrekt funktioniert.\",\"Steuert den Algorithmus, der Umbruchpunkte berechnet. Beachten Sie, dass \\\"advanced\\\" im Barrierefreiheitsmodus für eine optimale Benutzererfahrung verwendet wird.\",\"Codeaktionsmenü deaktivieren.\",\"Zeigt das Codeaktionsmenü an, wenn sich der Cursor in Zeilen mit Code befindet.\",\"Zeigt das Codeaktionsmenü an, wenn sich der Cursor in Zeilen mit Code oder in leeren Zeilen befindet.\",\"Aktiviert das Glühlampensymbol für Codeaktionen im Editor.\",\"Zeigt die geschachtelten aktuellen Bereiche während des Bildlaufs am oberen Rand des Editors an.\",\"Definiert die maximale Anzahl fixierter Zeilen, die angezeigt werden sollen.\",\"Legt das Modell fest, das zur Bestimmung der zu fixierenden Zeilen verwendet wird. Existiert das Gliederungsmodell nicht, wird auf das Modell des Folding Providers zurückgegriffen, der wiederum auf das Einrückungsmodell zurückgreift. Diese Reihenfolge wird in allen drei Fällen beachtet.\",\"Hiermit aktivieren Sie das Scrollen mit fixiertem Bildlauf mit der horizontalen Scrollleiste des Editors.\",\"Aktiviert die Inlay-Hinweise im Editor.\",\"Inlay-Hinweise sind aktiviert\",\"Inlay-Hinweise werden standardmäßig angezeigt und ausgeblendet, wenn Sie {0} gedrückt halten\",\"Inlayhinweise sind standardmäßig ausgeblendet. Sie werden angezeigt, wenn {0} gedrückt gehalten wird.\",\"Inlay-Hinweise sind deaktiviert\",\"Steuert den Schriftgrad von Einlapphinweisen im Editor. Standardmäßig wird die {0} verwendet, wenn der konfigurierte Wert kleiner als {1} oder größer als der Schriftgrad des Editors ist.\",\"Steuert die Schriftartfamilie von Einlapphinweisen im Editor. Bei Festlegung auf \\\"leer\\\" wird die {0} verwendet.\",\"Aktiviert den Abstand um die Inlay-Hinweise im Editor.\",\"Steuert die Zeilenhöhe. \\r\\n – Verwenden Sie 0, um die Zeilenhöhe automatisch anhand des Schriftgrads zu berechnen.\\r\\n – Werte zwischen 0 und 8 werden als Multiplikator mit dem Schriftgrad verwendet.\\r\\n – Werte größer oder gleich 8 werden als effektive Werte verwendet.\",\"Steuert, ob die Minimap angezeigt wird.\",\"Steuert, ob die Minimap automatisch ausgeblendet wird.\",\"Die Minimap hat die gleiche Größe wie der Editor-Inhalt (und kann scrollen).\",\"Die Minimap wird bei Bedarf vergrößert oder verkleinert, um die Höhe des Editors zu füllen (kein Scrollen).\",\"Die Minimap wird bei Bedarf verkleinert, damit sie nicht größer als der Editor ist (kein Scrollen).\",\"Legt die Größe der Minimap fest.\",\"Steuert die Seite, wo die Minimap gerendert wird.\",\"Steuert, wann der Schieberegler für die Minimap angezeigt wird.\",\"Maßstab des in der Minimap gezeichneten Inhalts: 1, 2 oder 3.\",\"Die tatsächlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbblöcken.\",\"Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.\",\"Steuert, ob benannte Bereiche als Abschnittsheader in der Minimap angezeigt werden.\",\"Steuert, ob „MARK: Kommentare“ als Abschnittsheader in der Minimap angezeigt werden.\",\"Steuert den Schriftgrad von Abschnittsüberschriften in der Minimap.\",\"Steuert den Abstand (in Pixeln) zwischen den Zeichen der Abschnittsüberschrift. Dies erleichtert die Lesbarkeit der Kopfzeile bei kleinen Schriftgrößen.\",\"Steuert den Abstand zwischen dem oberen Rand des Editors und der ersten Zeile.\",\"Steuert den Abstand zwischen dem unteren Rand des Editors und der letzten Zeile.\",\"Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt während Sie tippen.\",\"Steuert, ob das Menü mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schließt.\",\"Schnelle Vorschläge werden im Vorschlagswidget angezeigt\",\"Schnelle Vorschläge werden als inaktiver Text angezeigt\",\"Schnelle Vorschläge sind deaktiviert\",\"Schnellvorschläge innerhalb von Zeichenfolgen aktivieren.\",\"Schnellvorschläge innerhalb von Kommentaren aktivieren.\",\"Schnellvorschläge außerhalb von Zeichenfolgen und Kommentaren aktivieren.\",\"Steuert, ob Vorschläge während des Tippens automatisch angezeigt werden sollen. Dies kann bei der Eingabe von Kommentaren, Zeichenketten und anderem Code kontrolliert werden. Schnellvorschläge können so konfiguriert werden, dass sie als Geistertext oder mit dem Vorschlags-Widget angezeigt werden. Beachten Sie auch die {0}-Einstellung, die steuert, ob Vorschläge durch Sonderzeichen ausgelöst werden.\",\"Zeilennummern werden nicht dargestellt.\",\"Zeilennummern werden als absolute Zahl dargestellt.\",\"Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.\",\"Zeilennummern werden alle 10 Zeilen dargestellt.\",\"Steuert die Anzeige von Zeilennummern.\",\"Anzahl der Zeichen aus Festbreitenschriftarten, ab der dieses Editor-Lineal gerendert wird.\",\"Farbe dieses Editor-Lineals.\",\"Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte für mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.\",\"Die vertikale Bildlaufleiste wird nur bei Bedarf angezeigt.\",\"Die vertikale Bildlaufleiste ist immer sichtbar.\",\"Die vertikale Bildlaufleiste wird immer ausgeblendet.\",\"Steuert die Sichtbarkeit der vertikalen Bildlaufleiste.\",\"Die horizontale Bildlaufleiste wird nur bei Bedarf angezeigt.\",\"Die horizontale Bildlaufleiste ist immer sichtbar.\",\"Die horizontale Bildlaufleiste wird immer ausgeblendet.\",\"Steuert die Sichtbarkeit der horizontalen Bildlaufleiste.\",\"Die Breite der vertikalen Bildlaufleiste.\",\"Die Höhe der horizontalen Bildlaufleiste.\",\"Steuert, ob Klicks nach Seite scrollen oder zur Klickposition springen.\",\"Wenn diese Option festgelegt ist, wird die Größe des Editorinhalts nicht durch die horizontale Scrollleiste vergrößert.\",\"Legt fest, ob alle nicht einfachen ASCII-Zeichen hervorgehoben werden. Nur Zeichen zwischen U+0020 und U+007E, Tabulator, Zeilenvorschub und Wagenrücklauf gelten als einfache ASCII-Zeichen.\",\"Legt fest, ob Zeichen, die nur als Platzhalter dienen oder überhaupt keine Breite haben, hervorgehoben werden.\",\"Legt fest, ob Zeichen hervorgehoben werden, die mit einfachen ASCII-Zeichen verwechselt werden können, mit Ausnahme derjenigen, die im aktuellen Gebietsschema des Benutzers üblich sind.\",\"Steuert, ob Zeichen in Kommentaren auch mit Unicode-Hervorhebung versehen werden sollen.\",\"Steuert, ob Zeichen in Zeichenfolgen auch mit Unicode-Hervorhebung versehen werden sollen.\",\"Definiert zulässige Zeichen, die nicht hervorgehoben werden.\",\"Unicodezeichen, die in zulässigen Gebietsschemas üblich sind, werden nicht hervorgehoben.\",\"Steuert, ob Inline-Vorschläge automatisch im Editor angezeigt werden.\",\"Die Symbolleiste „Inline-Vorschlag“ anzeigen, wenn ein Inline-Vorschlag angezeigt wird.\",\"Die Symbolleiste „Inline-Vorschlag“ anzeigen, wenn Sie mit dem Mauszeiger auf einen Inline-Vorschlag zeigen.\",\"Die Inlinevorschlagssymbolleiste nie anzeigen.\",\"Steuert, wann die Inlinevorschlagssymbolleiste angezeigt werden soll.\",\"Steuert, wie Inlinevorschläge mit dem Vorschlagswidget interagieren. Wenn diese Option aktiviert ist, wird das Vorschlagswidget nicht automatisch angezeigt, wenn Inlinevorschläge verfügbar sind.\",\"Steuert die Schriftfamilie der Inlinevorschläge.\",null,null,null,null,null,null,\"Steuert, ob die Klammerpaar-Farbgebung aktiviert ist oder nicht. Verwenden Sie {0}, um die Hervorhebungsfarben der Klammer zu überschreiben.\",\"Steuert, ob jeder Klammertyp über einen eigenen unabhängigen Farbpool verfügt.\",\"Aktiviert Klammernpaarführungslinien.\",\"Aktiviert Klammernpaarführungslinien nur für das aktive Klammerpaar.\",\"Deaktiviert Klammernpaarführungslinien.\",\"Steuert, ob Führungslinien für Klammerpaare aktiviert sind oder nicht.\",\"Aktiviert horizontale Führungslinien als Ergänzung zu vertikalen Klammernpaarführungslinien.\",\"Aktiviert horizontale Führungslinien nur für das aktive Klammerpaar.\",\"Deaktiviert horizontale Führungslinien für Klammernpaare.\",\"Steuert, ob horizontale Führungslinien für Klammernpaare aktiviert sind oder nicht.\",\"Steuert, ob der Editor das aktive Klammerpaar hervorheben soll.\",\"Steuert, ob der Editor Einzugsführungslinien rendern soll.\",\"Hebt die aktive Einzugsführung hervor.\",\"Hebt die aktive Einzugshilfslinie hervor, selbst wenn Klammerhilfslinien hervorgehoben sind.\",\"Heben Sie die aktive Einzugshilfslinie nicht hervor.\",\"Steuert, ob der Editor die aktive Einzugsführungslinie hevorheben soll.\",\"Vorschlag einfügen, ohne den Text auf der rechten Seite des Cursors zu überschreiben\",\"Vorschlag einfügen und Text auf der rechten Seite des Cursors überschreiben\",\"Legt fest, ob Wörter beim Akzeptieren von Vervollständigungen überschrieben werden. Beachten Sie, dass dies von Erweiterungen abhängt, die für dieses Features aktiviert sind.\",\"Steuert, ob Filter- und Suchvorschläge geringfügige Tippfehler berücksichtigen.\",\"Steuert, ob bei der Sortierung Wörter priorisiert werden, die in der Nähe des Cursors stehen.\",\"Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (dafür ist \\\"#editor.suggestSelection#\\\" erforderlich).\",\"Wählen Sie immer einen Vorschlag aus, wenn IntelliSense automatisch ausgelöst wird.\",\"Wählen Sie niemals einen Vorschlag aus, wenn IntelliSense automatisch ausgelöst wird.\",\"Wählen Sie einen Vorschlag nur aus, wenn IntelliSense aus einem Triggerzeichen ausgelöst wird.\",\"Wählen Sie einen Vorschlag nur aus, wenn Sie IntelliSense während der Eingabe auslösen.\",\"Steuert, ob ein Vorschlag ausgewählt wird, wenn das Widget angezeigt wird. Beachten Sie, dass dies nur für automatisch ausgelöste Vorschläge ({0} und {1}) gilt und dass ein Vorschlag immer ausgewählt wird, wenn er explizit aufgerufen wird, z. B. über STRG+LEERTASTE.\",\"Steuert, ob ein aktiver Schnipsel verhindert, dass der Bereich \\\"Schnelle Vorschläge\\\" angezeigt wird.\",\"Steuert, ob Symbole in Vorschlägen ein- oder ausgeblendet werden.\",\"Steuert die Sichtbarkeit der Statusleiste unten im Vorschlagswidget.\",\"Steuert, ob das Ergebnis des Vorschlags im Editor in der Vorschau angezeigt werden soll.\",\"Steuert, ob Vorschlagsdetails inline mit der Bezeichnung oder nur im Detailwidget angezeigt werden.\",\"Diese Einstellung ist veraltet. Die Größe des Vorschlagswidgets kann jetzt geändert werden.\",\"Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie \\\"editor.suggest.showKeywords\\\" oder \\\"editor.suggest.showSnippets\\\".\",\"Wenn aktiviert, zeigt IntelliSense \\\"method\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"funktions\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"constructor\\\"-Vorschläge an.\",\"Wenn IntelliSense aktiviert ist, werden „veraltete“ Vorschläge angezeigt.\",\"Wenn dies aktiviert ist, erfordert die IntelliSense-Filterung, dass das erste Zeichen mit einem Wortanfang übereinstimmt, z. B. „c“ in „Console“ oder „WebContext“, aber _nicht_ bei „description“. Wenn diese Option deaktiviert ist, zeigt IntelliSense mehr Ergebnisse an, sortiert sie aber weiterhin nach der Übereinstimmungsqualität.\",\"Wenn aktiviert, zeigt IntelliSense \\\"field\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"variable\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"class\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"struct\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"interface\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"module\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"property\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"event\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"operator\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"unit\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"value\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"constant\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"enum\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"enumMember\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"keyword\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"text\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"color\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"file\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"reference\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"customcolor\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"folder\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"typeParameter\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense \\\"snippet\\\"-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense user-Vorschläge an.\",\"Wenn aktiviert, zeigt IntelliSense issues-Vorschläge an.\",\"Gibt an, ob führende und nachstehende Leerzeichen immer ausgewählt werden sollen.\",\"Gibt an, ob Unterwörter (z. B. \\\"foo\\\" in \\\"fooBar\\\" oder \\\"foo_bar\\\") ausgewählt werden sollen.\",\"Gebietsschemas, die für die Wortsegmentierung verwendet werden sollen, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden. Geben Sie das BCP 47-Sprachtag des Worts an, das Sie erkennen möchten (z. B. ja, zh-CN, zh-Hant-TW usw.).\",\"Gebietsschemas, die für die Wortsegmentierung verwendet werden sollen, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden. Geben Sie das BCP 47-Sprachtag des Worts an, das Sie erkennen möchten (z. B. ja, zh-CN, zh-Hant-TW usw.).\",\"Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.\",\"Umbrochene Zeilen erhalten den gleichen Einzug wie das übergeordnete Element.\",\"Umbrochene Zeilen erhalten + 1 Einzug auf das übergeordnete Element.\",\"Umgebrochene Zeilen werden im Vergleich zum übergeordneten Element +2 eingerückt.\",\"Steuert die Einrückung der umbrochenen Zeilen.\",\"Steuert, ob Sie eine Datei per Drag & Drop in einen Text-Editor ziehen können, indem Sie die UMSCHALTTASTE gedrückt halten (anstatt die Datei in einem Editor zu öffnen).\",\"Steuert, ob beim Ablegen von Dateien im Editor ein Widget angezeigt wird. Mit diesem Widget können Sie steuern, wie die Datei ablegt wird.\",\"Zeigt das Widget für die Dropdownauswahl an, nachdem eine Datei im Editor abgelegt wurde.\",\"Das Widget für die Ablageauswahl wird nie angezeigt. Stattdessen wird immer der Standardablageanbieter verwendet.\",\"Steuert, ob Sie Inhalte auf unterschiedliche Weise einfügen können.\",\"Steuert, ob beim Einfügen von Inhalt im Editor ein Widget angezeigt wird. Mit diesem Widget können Sie steuern, wie die Datei eingefügt wird.\",\"Das Widget für die Einfügeauswahl anzeigen, nachdem der Inhalt in den Editor eingefügt wurde.\",\"Das Widget für die Einfügeauswahl wird nie angezeigt. Stattdessen wird immer das Standardeinfügeverhalten verwendet.\",\"Steuert, ob Vorschläge über Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (\\\";\\\") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.\",\"Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine Änderung am Text vornimmt.\",\"Steuert, ob Vorschläge mit der EINGABETASTE (zusätzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einfügen neuer Zeilen oder dem Annehmen von Vorschlägen.\",\"Steuert die Anzahl von Zeilen im Editor, die von einer Sprachausgabe in einem Arbeitsschritt gelesen werden können. Wenn eine Sprachausgabe erkannt wird, wird der Standardwert automatisch auf 500 festgelegt. Warnung: Ein Wert höher als der Standardwert, kann sich auf die Leistung auswirken.\",\"Editor-Inhalt\",\"Steuern Sie, ob Inlinevorschläge von einer Sprachausgabe angekündigt werden.\",\"Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.\",\"Schließe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.\",\"Steuert, ob der Editor automatisch Klammern schließen soll, nachdem der Benutzer eine öffnende Klammer hinzugefügt hat.\",\"Verwenden Sie Sprachkonfigurationen, um festzulegen, wann Kommentare automatisch geschlossen werden sollen.\",\"Kommentare werden nur dann automatisch geschlossen, wenn sich der Cursor links von einem Leerraum befindet.\",\"Steuert, ob der Editor Kommentare automatisch schließen soll, nachdem die Benutzer*innen einen ersten Kommentar hinzugefügt haben.\",\"Angrenzende schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.\",\"Steuert, ob der Editor angrenzende schließende Anführungszeichen oder Klammern beim Löschen entfernen soll.\",\"Schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.\",\"Steuert, ob der Editor schließende Anführungszeichen oder Klammern überschreiben soll.\",\"Verwende die Sprachkonfiguration, um zu ermitteln, wann Anführungsstriche automatisch geschlossen werden.\",\"Schließende Anführungszeichen nur dann automatisch ergänzen, wenn der Cursor sich links von einem Leerzeichen befindet.\",\"Steuert, ob der Editor Anführungszeichen automatisch schließen soll, nachdem der Benutzer ein öffnendes Anführungszeichen hinzugefügt hat.\",\"Der Editor fügt den Einzug nicht automatisch ein.\",\"Der Editor behält den Einzug der aktuellen Zeile bei.\",\"Der Editor behält den in der aktuellen Zeile definierten Einzug bei und beachtet für Sprachen definierte Klammern.\",\"Der Editor behält den Einzug der aktuellen Zeile bei, beachtet von Sprachen definierte Klammern und ruft spezielle onEnterRules-Regeln auf, die von Sprachen definiert wurden.\",\"Der Editor behält den Einzug der aktuellen Zeile bei, beachtet die von Sprachen definierten Klammern, ruft von Sprachen definierte spezielle onEnterRules-Regeln auf und beachtet von Sprachen definierte indentationRules-Regeln.\",\"Legt fest, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einfügen, verschieben oder einrücken\",\"Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.\",\"Mit Anführungszeichen, nicht mit Klammern umschließen.\",\"Mit Klammern, nicht mit Anführungszeichen umschließen.\",\"Steuert, ob der Editor die Auswahl beim Eingeben von Anführungszeichen oder Klammern automatisch umschließt.\",\"Emuliert das Auswahlverhalten von Tabstoppzeichen, wenn Leerzeichen für den Einzug verwendet werden. Die Auswahl wird an Tabstopps ausgerichtet.\",\"Steuert, ob der Editor CodeLens anzeigt.\",\"Steuert die Schriftfamilie für CodeLens.\",\"Steuert den Schriftgrad in Pixeln für CodeLens. Bei Festlegung auf „0, 90 % von „#editor.fontSize#“ verwendet.\",\"Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.\",\"Farbauswahl sowohl beim Klicken als auch beim Daraufzeigen des Farbdekorators anzeigen\",\"Farbauswahl beim Draufzeigen auf den Farbdekorator anzeigen\",\"Farbauswahl beim Klicken auf den Farbdekorator anzeigen\",\"Steuert die Bedingung, damit eine Farbauswahl aus einem Farbdekorator angezeigt wird.\",\"Steuert die maximale Anzahl von Farb-Decorators, die in einem Editor gleichzeitig gerendert werden können.\",\"Zulassen, dass die Auswahl per Maus und Tasten die Spaltenauswahl durchführt.\",\"Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.\",\"Steuert den Cursoranimationsstil.\",\"Die Smooth Caret-Animation ist deaktiviert.\",\"Die Smooth Caret-Animation ist nur aktiviert, wenn der Benutzer den Cursor mit einer expliziten Geste bewegt.\",\"Die Smooth Caret-Animation ist immer aktiviert.\",\"Steuert, ob die weiche Cursoranimation aktiviert werden soll.\",\"Steuert den Cursorstil im Einfügeeingabemodus.\",\"Steuert die Mindestanzahl sichtbarer führender Zeilen (mindestens 0) und nachfolgender Zeilen (mindestens 1) um den Cursor. Dies wird in einigen anderen Editoren als „scrollOff“ oder „scrollOffset“ bezeichnet.\",\"\\\"cursorSurroundingLines\\\" wird nur erzwungen, wenn die Auslösung über die Tastatur oder API erfolgt.\",\"\\\"cursorSurroundingLines\\\" wird immer erzwungen.\",\"Steuert, wann „#editor.cursorSurroundingLines#“ erzwungen werden soll.\",\"Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.\",\"Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zulässt.\",\"Verwenden Sie eine neue Rendering-Methode mit SVGs.\",\"Verwenden Sie eine neue Rendering-Methode mit Schriftartzeichen.\",\"Verwenden Sie die stabile Rendering-Methode.\",\"Steuert, ob Leerzeichen mit einer neuen experimentellen Methode gerendert werden.\",\"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.\",\"Steuert, ob Codefaltung im Editor aktiviert ist.\",\"Verwenden Sie eine sprachspezifische Faltstrategie, falls verfügbar. Andernfalls wird eine einzugsbasierte verwendet.\",\"Einzugsbasierte Faltstrategie verwenden.\",\"Steuert die Strategie für die Berechnung von Faltbereichen.\",\"Steuert, ob der Editor eingefaltete Bereiche hervorheben soll.\",\"Steuert, ob der Editor Importbereiche automatisch reduziert.\",\"Die maximale Anzahl von faltbaren Regionen. Eine Erhöhung dieses Werts kann dazu führen, dass der Editor weniger reaktionsfähig wird, wenn die aktuelle Quelle eine große Anzahl von faltbaren Regionen aufweist.\",\"Steuert, ob eine Zeile aufgefaltet wird, wenn nach einer gefalteten Zeile auf den leeren Inhalt geklickt wird.\",\"Steuert die Schriftfamilie.\",\"Steuert, ob der Editor den eingefügten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.\",\"Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.\",\"Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird hauptsächlich zum Debuggen verwendet.\",\"Steuert, ob der Cursor im Übersichtslineal ausgeblendet werden soll.\",\"Legt den Abstand der Buchstaben in Pixeln fest.\",\"Steuert, ob die verknüpfte Bearbeitung im Editor aktiviert ist. Abhängig von der Sprache werden zugehörige Symbole, z. B. HTML-Tags, während der Bearbeitung aktualisiert.\",\"Steuert, ob der Editor Links erkennen und anklickbar machen soll.\",\"Passende Klammern hervorheben\",\"Ein Multiplikator, der für die Mausrad-Bildlaufereignisse \\\"deltaX\\\" und \\\"deltaY\\\" verwendet werden soll.\",\"Schriftart des Editors vergrößern, wenn das Mausrad verwendet und „Cmd“ gedrückt wird.\",\"Schriftart des Editors vergrößern, wenn das Mausrad verwendet und die STRG-TASTE gedrückt wird.\",\"Mehrere Cursor zusammenführen, wenn sie sich überlappen.\",\"Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.\",\"Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.\",\"Der Modifizierer, der zum Hinzufügen mehrerer Cursor mit der Maus verwendet werden soll. Die Mausgesten \\\"Gehe zu Definition\\\" und \\\"Link öffnen\\\" werden so angepasst, dass sie nicht mit dem [Multicursormodifizierer](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-Modifizierer) in Konflikt stehen.\",\"Jeder Cursor fügt eine Textzeile ein.\",\"Jeder Cursor fügt den vollständigen Text ein.\",\"Steuert das Einfügen, wenn die Zeilenanzahl des Einfügetexts der Cursor-Anzahl entspricht.\",\"Steuert die maximale Anzahl von Cursorn, die sich gleichzeitig in einem aktiven Editor befindet.\",\"Hebt keine Vorkommen hervor.\",\"Hebt Vorkommen nur in der aktuellen Datei hervor.\",\"Experimentell: Hebt Vorkommen in allen gültigen geöffneten Dateien hervor.\",\"Steuert, ob Vorkommen in geöffneten Dateien hervorgehoben werden sollen.\",\"Steuert, ob um das Übersichtslineal ein Rahmen gezeichnet werden soll.\",\"Struktur beim Öffnen des Peek-Editors fokussieren\",\"Editor fokussieren, wenn Sie den Peek-Editor öffnen\",\"Steuert, ob der Inline-Editor oder die Struktur im Peek-Widget fokussiert werden soll.\",\"Steuert, ob die Mausgeste \\\"Gehe zu Definition\\\" immer das Vorschauwidget öffnet.\",\"Steuert die Verzögerung in Millisekunden nach der Schnellvorschläge angezeigt werden.\",\"Steuert, ob der Editor bei Eingabe automatisch eine Umbenennung vornimmt.\",\"Veraltet. Verwenden Sie stattdessen \\\"editor.linkedEditing\\\".\",\"Steuert, ob der Editor Steuerzeichen rendern soll.\",\"Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.\",\"Hebt den Bundsteg und die aktuelle Zeile hervor.\",\"Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.\",\"Steuert, ob der Editor die aktuelle Zeilenhervorhebung nur dann rendern soll, wenn der Fokus auf dem Editor liegt.\",\"Leerraumzeichen werden gerendert mit Ausnahme der einzelnen Leerzeichen zwischen Wörtern.\",\"Hiermit werden Leerraumzeichen nur für ausgewählten Text gerendert.\",\"Nur nachstehende Leerzeichen rendern\",\"Steuert, wie der Editor Leerzeichen rendern soll.\",\"Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.\",\"Steuert die Anzahl der zusätzlichen Zeichen, nach denen der Editor horizontal scrollt.\",\"Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.\",\"Nur entlang der vorherrschenden Achse scrollen, wenn gleichzeitig vertikal und horizontal gescrollt wird. Dadurch wird ein horizontaler Versatz beim vertikalen Scrollen auf einem Trackpad verhindert.\",\"Steuert, ob die primäre Linux-Zwischenablage unterstützt werden soll.\",\"Steuert, ob der Editor Übereinstimmungen hervorheben soll, die der Auswahl ähneln.\",\"Steuerelemente für die Codefaltung immer anzeigen.\",\"Zeigen Sie niemals die Faltungssteuerelemente an, und verringern Sie die Größe des Bundstegs.\",\"Steuerelemente für die Codefaltung nur anzeigen, wenn sich die Maus über dem Bundsteg befindet.\",\"Steuert, wann die Steuerungselemente für die Codefaltung am Bundsteg angezeigt werden.\",\"Steuert das Ausblenden von nicht verwendetem Code.\",\"Steuert durchgestrichene veraltete Variablen.\",\"Zeige Schnipselvorschläge über den anderen Vorschlägen.\",\"Schnipselvorschläge unter anderen Vorschlägen anzeigen.\",\"Zeige Schnipselvorschläge mit anderen Vorschlägen.\",\"Keine Schnipselvorschläge anzeigen.\",\"Steuert, ob Codeschnipsel mit anderen Vorschlägen angezeigt und wie diese sortiert werden.\",\"Legt fest, ob der Editor Bildläufe animiert ausführt.\",\"Steuert, ob für Benutzer*innen, die eine Sprachausgabe nutzen, bei Anzeige einer Inlinevervollständigung ein Hinweis zur Barrierefreiheit angezeigt werden soll.\",\"Schriftgrad für das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet.\",\"Zeilenhöhe für das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet. Der Mindestwert ist 8.\",\"Steuert, ob Vorschläge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.\",\"Immer den ersten Vorschlag auswählen.\",\"Wählen Sie die aktuellsten Vorschläge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgewählt, z.B. \\\"console.| -> console.log\\\", weil \\\"log\\\" vor Kurzem abgeschlossen wurde.\",\"Wählen Sie Vorschläge basierend auf früheren Präfixen aus, die diese Vorschläge abgeschlossen haben, z.B. \\\"co -> console\\\" und \\\"con ->\\\" const\\\".\",\"Steuert, wie Vorschläge bei Anzeige der Vorschlagsliste vorab ausgewählt werden.\",\"Die Tab-Vervollständigung fügt den passendsten Vorschlag ein, wenn auf Tab gedrückt wird.\",\"Tab-Vervollständigungen deaktivieren.\",\"Codeschnipsel per Tab vervollständigen, wenn die Präfixe übereinstimmen. Funktioniert am besten, wenn \\\"quickSuggestions\\\" deaktiviert sind.\",\"Tab-Vervollständigungen aktivieren.\",\"Ungewöhnliche Zeilenabschlusszeichen werden automatisch entfernt.\",\"Ungewöhnliche Zeilenabschlusszeichen werden ignoriert.\",\"Zum Entfernen ungewöhnlicher Zeilenabschlusszeichen wird eine Eingabeaufforderung angezeigt.\",\"Entfernen Sie unübliche Zeilenabschlusszeichen, die Probleme verursachen können.\",\"Leerzeichen und Registerkarten werden in Übereinstimmung mit Tabstopps eingefügt und gelöscht.\",\"Verwenden Sie die Standardregel für Zeilenumbrüche.\",\"Trennstellen dürfen nicht für Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden. Das Verhalten von Nicht-CJK-Texten ist mit dem für normales Verhalten identisch.\",\"Steuert die Regeln für Trennstellen, die für Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden.\",\"Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden.\",\"Zeilenumbrüche erfolgen nie.\",\"Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.\",\"Der Zeilenumbruch erfolgt bei \\\"#editor.wordWrapColumn#\\\".\",\"Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und \\\"#editor.wordWrapColumn\\\".\",\"Steuert, wie der Zeilenumbruch durchgeführt werden soll.\",\"Steuert die umschließende Spalte des Editors, wenn \\\"#editor.wordWrap#\\\" den Wert \\\"wordWrapColumn\\\" oder \\\"bounded\\\" aufweist.\",\"Steuert, ob Inlinefarbdekorationen mithilfe des Standard-Dokumentfarbanbieters angezeigt werden sollen.\",\"Steuert, ob der Editor Registerkarten empfängt oder zur Navigation zur Workbench zurückgibt.\",\"Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.\",\"Hintergrundfarbe für den Rahmen um die Zeile an der Cursorposition.\",\"Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrundfarbe für den Rahmen um hervorgehobene Bereiche.\",\"Hintergrundfarbe des hervorgehobenen Symbols, z. B. \\\"Gehe zu Definition\\\" oder \\\"Gehe zu nächster/vorheriger\\\". Die Farbe darf nicht undurchsichtig sein, um zugrunde liegende Dekorationen nicht zu verbergen.\",\"Hintergrundfarbe des Rahmens um hervorgehobene Symbole\",\"Farbe des Cursors im Editor.\",\"Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.\",\"Farbe des primären Editorcursors, wenn mehrere Cursor vorhanden sind.\",\"Die Hintergrundfarbe des primären Editorcursors, wenn mehrere Cursor vorhanden sind. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.\",\"Farbe sekundärer Editorcursor, wenn mehrere Cursor vorhanden sind.\",\"Die Hintergrundfarbe sekundärer Editorcursor, wenn mehrere Cursor vorhanden sind. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.\",\"Farbe der Leerzeichen im Editor.\",\"Zeilennummernfarbe im Editor.\",\"Farbe der Führungslinien für Einzüge im Editor.\",\"\\\"editorIndentGuide.background\\\" ist veraltet. Verwenden Sie stattdessen \\\"editorIndentGuide.background1\\\".\",\"Farbe der Führungslinien für Einzüge im aktiven Editor.\",\"\\\"editorIndentGuide.activeBackground\\\" ist veraltet. Verwenden Sie stattdessen \\\"editorIndentGuide.activeBackground1\\\".\",\"Farbe der Führungslinien für Einzüge im Editor (1).\",\"Farbe der Führungslinien für Einzüge im Editor (2).\",\"Farbe der Führungslinien für Einzüge im Editor (3).\",\"Farbe der Führungslinien für Einzüge im Editor (4).\",\"Farbe der Führungslinien für Einzüge im Editor (5).\",\"Farbe der Führungslinien für Einzüge im Editor (6).\",\"Farbe der Führungslinien für Einzüge im aktiven Editor (1).\",\"Farbe der Führungslinien für Einzüge im aktiven Editor (2).\",\"Farbe der Führungslinien für Einzüge im aktiven Editor (3).\",\"Farbe der Führungslinien für Einzüge im aktiven Editor (4).\",\"Farbe der Führungslinien für Einzüge im aktiven Editor (5).\",\"Farbe der Führungslinien für Einzüge im aktiven Editor (6).\",\"Zeilennummernfarbe der aktiven Editorzeile.\",\"Die ID ist veraltet. Verwenden Sie stattdessen \\\"editorLineNumber.activeForeground\\\".\",\"Zeilennummernfarbe der aktiven Editorzeile.\",\"Die Farbe der letzten Editor-Zeile, wenn „editor.renderFinalNewline“ auf „abgeblendet“ festgelegt ist.\",\"Farbe des Editor-Lineals.\",\"Vordergrundfarbe der CodeLens-Links im Editor\",\"Hintergrundfarbe für zusammengehörige Klammern\",\"Farbe für zusammengehörige Klammern\",\"Farbe des Rahmens für das Übersicht-Lineal.\",\"Hintergrundfarbe des Editor-Übersichtslineals.\",\"Hintergrundfarbe der Editorleiste. Die Leiste enthält die Glyphenränder und die Zeilennummern.\",\"Rahmenfarbe unnötigen (nicht genutzten) Quellcodes im Editor.\",\"Deckkraft des unnötigen (nicht genutzten) Quellcodes im Editor. \\\"#000000c0\\\" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie für Designs mit hohem Kontrast das Farbdesign \\\"editorUnnecessaryCode.border\\\", um unnötigen Code zu unterstreichen statt ihn abzublenden.\",\"Rahmenfarbe des Ghost-Texts im Editor.\",\"Vordergrundfarbe des Ghost-Texts im Editor.\",\"Hintergrundfarbe des Ghost-Texts im Editor.\",\"Übersichtslinealmarkerfarbe für das Hervorheben von Bereichen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Übersichtslineal-Markierungsfarbe für Fehler.\",\"Übersichtslineal-Markierungsfarbe für Warnungen.\",\"Übersichtslineal-Markierungsfarbe für Informationen.\",\"Vordergrundfarbe der Klammern (1). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\"Vordergrundfarbe der Klammern (2). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\"Vordergrundfarbe der Klammern (3). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\"Vordergrundfarbe der Klammern (4). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\"Vordergrundfarbe der Klammern (5). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\"Vordergrundfarbe der Klammern (6). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\"Vordergrundfarbe der unerwarteten Klammern.\",\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\"Rahmenfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.\",\"Hintergrundfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.\",\"Gibt an, ob der Editor-Text den Fokus besitzt (Cursor blinkt).\",\"Gibt an, ob der Editor oder ein Editor-Widget den Fokus besitzt (z. B. ob der Fokus sich im Suchwidget befindet).\",\"Gibt an, ob ein Editor oder eine Rich-Text-Eingabe den Fokus besitzt (Cursor blinkt).\",\"Gibt an, ob der Editor schreibgeschützt ist\",\"Gibt an, ob der Kontext ein Diff-Editor ist.\",\"Gibt an, ob der Kontext ein eingebetteter Diff-Editor ist.\",null,\"Gibt an, ob alle Dateien im Multi-Diff-Editor reduziert sind\",\"Gibt an, ob der Diff-Editor Änderungen aufweist.\",\"Gibt an, ob ein verschobener Codeblock für den Vergleich ausgewählt wird.\",\"Gibt an, ob der barrierefreie Diff-Viewer sichtbar ist.\",\"Gibt an, ob für den Diff-Editor der Breakpoint für das Rendern im Modus \\\"Parallel\\\" oder \\\"Inline\\\" erreicht wurde.\",\"Gibt an, ob der Inlinemodus aktiv ist.\",\"Gibt an, ob „geändert“ im Diff-Editor beschreibbar ist.\",\"Gibt an, ob „geändert“ im Diff-Editor beschreibbar ist.\",\"Der URI des ursprünglichen Dokuments\",\"Der URI des geänderten Dokuments\",\"Gibt an, ob \\\"editor.columnSelection\\\" aktiviert ist.\",\"Gibt an, ob im Editor Text ausgewählt ist.\",\"Gibt an, ob der Editor über Mehrfachauswahl verfügt.\",\"Gibt an, ob die TAB-TASTE den Fokus aus dem Editor verschiebt.\",\"Gibt an, ob Hover im Editor sichtbar ist.\",\"Gibt an, ob Daraufzeigen im Editor fokussiert ist.\",\"Gibt an, ob der Fokus auf dem Fixierten Bildlauf liegt.\",\"Gibt an, ob der Fixierte Bildlauf sichtbar ist.\",\"Gibt an, ob der eigenständige Farbwähler sichtbar ist.\",\"Gibt an, ob der eigenständige Farbwähler fokussiert ist.\",\"Gibt an, ob der Editor Bestandteil eines größeren Editors ist (z. B. Notebooks).\",\"Der Sprachbezeichner des Editors.\",\"Gibt an, ob der Editor über einen Vervollständigungselementanbieter verfügt.\",\"Gibt an, ob der Editor über einen Codeaktionsanbieter verfügt.\",\"Gibt an, ob der Editor über einen CodeLens-Anbieter verfügt.\",\"Gibt an, ob der Editor über einen Definitionsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Deklarationsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Implementierungsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Typdefinitionsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Hoveranbieter verfügt.\",\"Gibt an, ob der Editor über einen Dokumenthervorhebungsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Dokumentsymbolanbieter verfügt.\",\"Gibt an, ob der Editor über einen Verweisanbieter verfügt.\",\"Gibt an, ob der Editor über einen Umbenennungsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Signaturhilfeanbieter verfügt.\",\"Gibt an, ob der Editor über einen Inlinehinweisanbieter verfügt.\",\"Gibt an, ob der Editor über einen Dokumentformatierungsanbieter verfügt.\",\"Gibt an, ob der Editor über einen Anbieter für Dokumentauswahlformatierung verfügt.\",\"Gibt an, ob der Editor über mehrere Dokumentformatierungsanbieter verfügt.\",\"Gibt an, ob der Editor über mehrere Anbieter für Dokumentauswahlformatierung verfügt.\",\"Array\",\"Boolescher Wert\",\"Klasse\",\"Konstante\",\"Konstruktor\",\"Enumeration\",\"Enumerationsmember\",\"Ereignis\",\"Feld\",\"Datei\",\"Funktion\",\"Schnittstelle\",\"Schlüssel\",\"Methode\",\"Modul\",\"Namespace\",\"NULL\",\"Zahl\",\"Objekt\",\"Operator\",\"Paket\",\"Eigenschaft\",\"Zeichenfolge\",\"Struktur\",\"Typparameter\",\"Variable\",\"{0} ({1})\",\"Nur-Text\",\"Eingabe\",\"Entwickler: Token überprüfen\",\"Gehe zu Zeile/Spalte...\",\"Alle Anbieter für den Schnellzugriff anzeigen\",\"Befehlspalette\",\"Befehle anzeigen und ausführen\",\"Gehe zu Symbol...\",\"Gehe zu Symbol nach Kategorie...\",\"Editor-Inhalt\",\"Zu Design mit hohem Kontrast umschalten\",\"{0} Bearbeitungen in {1} Dateien durchgeführt\",\"Mehr anzeigen ({0})\",\"{0} Zeichen\",\"Auswahlanker\",\"Anker festgelegt bei \\\"{0}:{1}\\\"\",\"Auswahlanker festlegen\",\"Zu Auswahlanker wechseln\",\"Auswahl von Anker zu Cursor\",\"Auswahlanker abbrechen\",\"Übersichtslineal-Markierungsfarbe für zusammengehörige Klammern.\",\"Gehe zu Klammer\",\"Auswählen bis Klammer\",\"Klammern entfernen\",\"Gehe zu &&Klammer\",\"Text auswählen und Klammern oder geschweifte Klammern einschließen\",\"Ausgewählten Text nach links verschieben\",\"Ausgewählten Text nach rechts verschieben\",\"Buchstaben austauschen\",\"&&Ausschneiden\",\"Ausschneiden\",\"Ausschneiden\",\"Ausschneiden\",\"&&Kopieren\",\"Kopieren\",\"Kopieren\",\"Kopieren\",\"&&Einfügen\",\"Einfügen\",\"Einfügen\",\"Einfügen\",\"Mit Syntaxhervorhebung kopieren\",\"Kopieren als\",\"Kopieren als\",\"Freigeben\",\"Freigeben\",\"Beim Anwenden der Code-Aktion ist ein unbekannter Fehler aufgetreten\",\"Art der auszuführenden Codeaktion\",\"Legt fest, wann die zurückgegebenen Aktionen angewendet werden\",\"Die erste zurückgegebene Codeaktion immer anwenden\",\"Die erste zurückgegebene Codeaktion anwenden, wenn nur eine vorhanden ist\",\"Zurückgegebene Codeaktionen nicht anwenden\",\"Legt fest, ob nur bevorzugte Codeaktionen zurückgegeben werden sollen\",\"Schnelle Problembehebung ...\",\"Keine Codeaktionen verfügbar\",\"Keine bevorzugten Codeaktionen für \\\"{0}\\\" verfügbar\",\"Keine Codeaktionen für \\\"{0}\\\" verfügbar\",\"Keine bevorzugten Codeaktionen verfügbar\",\"Keine Codeaktionen verfügbar\",\"Refactoring durchführen...\",\"Keine bevorzugten Refactorings für \\\"{0}\\\" verfügbar\",\"Keine Refactorings für \\\"{0}\\\" verfügbar\",\"Keine bevorzugten Refactorings verfügbar\",\"Keine Refactorings verfügbar\",\"Quellaktion...\",\"Keine bevorzugten Quellaktionen für \\\"{0}\\\" verfügbar\",\"Keine Quellaktionen für \\\"{0}\\\" verfügbar\",\"Keine bevorzugten Quellaktionen verfügbar\",\"Keine Quellaktionen verfügbar\",\"Importe organisieren\",\"Keine Aktion zum Organisieren von Importen verfügbar\",\"Alle korrigieren\",\"Aktion \\\"Alle korrigieren\\\" nicht verfügbar\",\"Automatisch korrigieren...\",\"Keine automatischen Korrekturen verfügbar\",\"Aktivieren/Deaktivieren Sie die Anzeige von Gruppenheadern im Codeaktionsmenü.\",\"Hiermit aktivieren/deaktivieren Sie die Anzeige der nächstgelegenen schnellen Problembehebung innerhalb einer Zeile, wenn derzeit keine Diagnose durchgeführt wird.\",\"Aktivieren Sie das Auslösen von {0}, wenn {1} auf {2} festgelegt ist. Codeaktionen müssen auf {3} festgelegt werden, um für Fenster- und Fokusänderungen ausgelöst zu werden.\",\"Kontext: {0} in Zeile {1} und Spalte {2}.\",\"Deaktivierte Elemente ausblenden\",\"Deaktivierte Elemente anzeigen\",\"Weitere Aktionen...\",\"Schnelle Problembehebung\",\"Extrahieren\",\"Inline\",\"Erneut generieren\",\"Verschieben\",\"Umgeben mit\",\"Quellaktion\",\"Symbol, das das Menü „Codeaktionen“ aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist.\",\"Symbol, das das Menü „Codeaktionen“ aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und eine schnelle Korrektur verfügbar ist.\",\"Symbol, das das Menü „Codeaktionen“ aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und ein KI-Fix verfügbar ist.\",\"Symbol, das das Menü „Codeaktionen“ aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und ein KI-Fix und eine schnelle Korrektur verfügbar sind.\",\"Symbol, das das Menü „Codeaktionen“ aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und ein KI-Fix und eine schnelle Korrektur verfügbar sind.\",\"Ausführen: {0}\",\"Zeigt Codeaktionen an. Bevorzugte Schnellkorrektur verfügbar ({0})\",\"Codeaktionen anzeigen ({0})\",\"Codeaktionen anzeigen\",\"CodeLens-Befehle für aktuelle Zeile anzeigen\",\"Befehl auswählen\",null,null,null,null,null,null,null,null,null,\"Zeilenkommentar umschalten\",\"Zeilenkommen&&tar umschalten\",\"Zeilenkommentar hinzufügen\",\"Zeilenkommentar entfernen\",\"Blockkommentar umschalten\",\"&&Blockkommentar umschalten\",\"Minimap\",\"Zeichen rendern\",\"Vertikale Größe\",\"Proportional\",\"Ausfüllen\",\"Anpassen\",\"Schieberegler\",\"Maus über\",\"Immer\",\"Editor-Kontextmenü anzeigen\",\"Mit Cursor rückgängig machen\",\"Wiederholen mit Cursor\",\"Die Art der Einfügebearbeitung, für die Sie das Einfügen versuchen.\\r\\nWenn mehrere Bearbeitungen für diese Art vorhanden sind, zeigt der Editor eine Auswahl an. Wenn keine Bearbeitungen dieser Art vorhanden sind, zeigt der Editor eine Fehlermeldung an.\",\"Einfügen als...\",\"Als Text einfügen\",\"Gibt an, ob das Einfügewidget angezeigt wird.\",\"Einfügeoptionen anzeigen...\",\"Es wurden keine Einfügebearbeitungen für „{0}“ gefunden.\",\"Die Einfügebearbeitung wird aufgelöst. Zum Abbrechen klicken\",\"Einfügehandler werden ausgeführt. Klicken Sie hier, um den Vorgang abzubrechen und ein einfaches Einfügen auszuführen.\",\"Einfügeaktion auswählen\",\"Einfügehandler werden ausgeführt\",\"Nur-Text einfügen\",\"URI einfügen\",\"URI einfügen\",\"Pfade einfügen\",\"Pfad einfügen\",\"Relative Pfade einfügen\",\"Relativen Pfad einfügen\",\"HTML einfügen\",null,\"Gibt an, ob das Ablagewidget angezeigt wird.\",\"Ablageoptionen anzeigen...\",\"Drophandler werden ausgeführt. Klicken Sie hier, um den Vorgang abzubrechen.\",\"Fehler beim Auflösen der Bearbeitung \\\"{0}\\\":\\r\\n{1}\",\"Fehler beim Anwenden der Bearbeitung \\\"{0}\\\":\\r\\n{1}\",\"Gibt an, ob der Editor einen abbrechbaren Vorgang ausführt, z. B. \\\"Verweisvorschau\\\".\",\"Die Datei ist zu groß, um einen Vorgang zum Ersetzen aller Elemente auszuführen.\",\"Suchen\",\"&&Suchen\",\"Mit Argumenten suchen\",\"Mit Auswahl suchen\",\"Weitersuchen\",\"Vorheriges Element suchen\",\"Zu Übereinstimmung wechseln ...\",\"Keine Übereinstimmungen. Versuchen Sie, nach etwas anderem zu suchen.\",\"Geben Sie eine Zahl ein, um zu einer bestimmten Übereinstimmung zu wechseln (zwischen 1 und {0}).\",\"Zahl zwischen 1 und {0} eingeben\",\"Zahl zwischen 1 und {0} eingeben\",\"Nächste Auswahl suchen\",\"Vorherige Auswahl suchen\",\"Ersetzen\",\"&&Ersetzen\",\"Symbol für die Anzeige, dass das Editor-Such-Widget zugeklappt wurde.\",\"Symbol für die Anzeige, dass das Editor-Such-Widget aufgeklappt wurde.\",\"Symbol für \\\"In Auswahl suchen\\\" im Editor-Such-Widget.\",\"Symbol für \\\"Ersetzen\\\" im Editor-Such-Widget.\",\"Symbol für \\\"Alle ersetzen\\\" im Editor-Such-Widget.\",\"Symbol für \\\"Vorheriges Element suchen\\\" im Editor-Such-Widget.\",\"Symbol für \\\"Nächstes Element suchen\\\" im Editor-Such-Widget.\",\"Suchen/Ersetzen\",\"Suchen\",\"Suchen\",\"Vorherige Übereinstimmung\",\"Nächste Übereinstimmung\",\"In Auswahl suchen\",\"Schließen\",\"Ersetzen\",\"Ersetzen\",\"Ersetzen\",\"Alle ersetzen\",\"Ersetzen umschalten\",\"Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgeführt.\",\"{0} von {1}\",\"Keine Ergebnisse\",\"{0} gefunden\",\"{0} für \\\"{1}\\\" gefunden\",\"{0} für \\\"{1}\\\" gefunden, bei {2}\",\"{0} für \\\"{1}\\\" gefunden\",\"STRG+EINGABE fügt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie können die Tastenzuordnung für \\\"editor.action.replaceAll\\\" ändern, um dieses Verhalten außer Kraft zu setzen.\",\"Auffalten\",\"Faltung rekursiv aufheben\",\"Falten\",\"Einklappung umschalten\",\"Rekursiv falten\",\"Rekursiv falten umschalten\",\"Alle Blockkommentare falten\",\"Alle Regionen falten\",\"Alle Regionen auffalten\",\"Alle bis auf ausgewählte falten\",\"Alle bis auf ausgewählte auffalten\",\"Alle falten\",\"Alle auffalten\",\"Zur übergeordneten Reduzierung wechseln\",\"Zum vorherigen Faltbereich wechseln\",\"Zum nächsten Faltbereich wechseln\",\"Faltungsbereich aus Auswahl erstellen\",\"Manuelle Faltbereiche entfernen\",\"Faltebene {0}\",\"Hintergrundfarbe hinter gefalteten Bereichen. Die Farbe darf nicht deckend sein, sodass zugrunde liegende Dekorationen nicht ausgeblendet werden.\",\"Farbe des reduzierten Texts nach der ersten Zeile eines gefalteten Bereichs.\",\"Farbe des Faltsteuerelements im Editor-Bundsteg.\",\"Symbol für aufgeklappte Bereiche im Editor-Glyphenrand.\",\"Symbol für zugeklappte Bereiche im Editor-Glyphenrand.\",\"Symbol für manuell reduzierte Bereiche im Glyphenrand des Editors.\",\"Symbol für manuell erweiterte Bereiche im Glyphenrand des Editors.\",\"Klicken Sie hier, um den Bereich zu erweitern.\",\"Klicken Sie hier, um den Bereich zu reduzieren.\",\"Schriftgrad des Editors erhöhen\",\"Schriftgrad des Editors verringern\",\"Editor-Schriftgrad zurücksetzen\",\"Dokument formatieren\",\"Auswahl formatieren\",\"Gehe zu nächstem Problem (Fehler, Warnung, Information)\",\"Symbol für den Marker zum Wechseln zum nächsten Element.\",\"Gehe zu vorigem Problem (Fehler, Warnung, Information)\",\"Symbol für den Marker zum Wechseln zum vorherigen Element.\",\"Gehe zu dem nächsten Problem in den Dateien (Fehler, Warnung, Info)\",\"Nächstes &&Problem\",\"Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)\",\"Vorheriges &&Problem\",\"Fehler\",\"Warnung\",\"Info\",\"Hinweis\",\"{0} bei {1}. \",\"{0} von {1} Problemen\",\"{0} von {1} Problemen\",\"Editormarkierung: Farbe bei Fehler des Navigationswidgets.\",\"Hintergrund der Fehlerüberschrift des Markernavigationswidgets im Editor.\",\"Editormarkierung: Farbe bei Warnung des Navigationswidgets.\",\"Hintergrund der Warnungsüberschrift des Markernavigationswidgets im Editor.\",\"Editormarkierung: Farbe bei Information des Navigationswidgets.\",\"Hintergrund der Informationsüberschrift des Markernavigationswidgets im Editor.\",\"Editormarkierung: Hintergrund des Navigationswidgets.\",\"Vorschau\",\"Definitionen\",\"Keine Definition gefunden für \\\"{0}\\\".\",\"Keine Definition gefunden\",\"Gehe &&zu Definition\",\"Deklarationen\",\"Keine Deklaration für \\\"{0}\\\" gefunden.\",\"Keine Deklaration gefunden.\",\"Gehe zu &&Deklaration\",\"Keine Deklaration für \\\"{0}\\\" gefunden.\",\"Keine Deklaration gefunden.\",\"Typdefinitionen\",\"Keine Typendefinition gefunden für \\\"{0}\\\"\",\"Keine Typendefinition gefunden\",\"Zur &&Typdefinition wechseln\",\"Implementierungen\",\"Keine Implementierung gefunden für \\\"{0}\\\"\",\"Keine Implementierung gefunden\",\"Gehe zu &&Implementierungen\",\"Für \\\"{0}\\\" wurden keine Verweise gefunden.\",\"Keine Referenzen gefunden\",\"Gehe zu &&Verweisen\",\"Verweise\",\"Verweise\",\"Speicherorte\",\"Keine Ergebnisse für \\\"{0}\\\"\",\"Verweise\",\"Gehe zu Definition\",\"Definition an der Seite öffnen\",\"Definition einsehen\",\"Zur Deklaration wechseln\",\"Vorschau für Deklaration anzeigen\",\"Zur Typdefinition wechseln\",\"Vorschau der Typdefinition anzeigen\",\"Gehe zu Implementierungen\",\"Vorschau für Implementierungen anzeigen\",\"Gehe zu Verweisen\",\"Vorschau für Verweise anzeigen\",\"Zum beliebigem Symbol wechseln\",\"Klicken Sie, um {0} Definitionen anzuzeigen.\",\"Gibt an, ob die Verweisvorschau sichtbar ist, z. B. \\\"Verweisvorschau\\\" oder \\\"Definition einsehen\\\".\",\"Wird geladen...\",\"{0} ({1})\",\"{0} Verweise\",\"{0} Verweis\",\"Verweise\",\"Keine Vorschau verfügbar.\",\"Keine Ergebnisse\",\"Verweise\",\"in {0} in Zeile {1} in Spalte {2}\",\"{0} in {1} in Zeile {2} in Spalte {3}\",\"1 Symbol in {0}, vollständiger Pfad {1}\",\"{0} Symbole in {1}, vollständiger Pfad {2}\",\"Es wurden keine Ergebnisse gefunden.\",\"1 Symbol in {0} gefunden\",\"{0} Symbole in {1} gefunden\",\"{0} Symbole in {1} Dateien gefunden\",\"Gibt an, ob Symbolpositionen vorliegen, bei denen die Navigation nur über die Tastatur möglich ist.\",\"Symbol {0} von {1}, {2} für nächstes\",\"Symbol {0} von {1}\",\"Ausführlichkeitsgrad beim Daraufzeigen erhöhen\",\"Ausführlichkeitsgrad beim Daraufzeigen verringern\",\"Anzeigen oder Fokus beim Daraufzeigen\",\"Beim Daraufzeigen wird der Fokus nicht automatisch verwendet.\",\"Beim Daraufzeigen wird nur dann den Fokus erhalten, wenn er bereits sichtbar ist.\",\"Beim Daraufzeigen wird automatisch der Fokus erhalten, wenn er angezeigt wird.\",\"Definitionsvorschauhover anzeigen\",\"Bildlauf nach oben beim Daraufzeigen\",\"Bildlauf nach unten beim Daraufzeigen\",\"Bildlauf nach links beim Daraufzeigen\",\"Bildlauf nach rechts beim Daraufzeigen\",\"Eine Seite nach oben beim Daraufzeigen\",\"Eine Seite nach unten beim Daraufzeigen\",\"Gehe nach oben beim Daraufzeigen\",\"Gehe nach unten beim Daraufzeigen\",\"Editor-Mauszeiger anzeigen oder fokussieren, um Dokumentation, Verweise und anderen Inhalt für ein Symbol an der aktuellen Cursorposition anzuzeigen oder zu fokussieren.\",\"Definitionsvorschau-Mauszeiger im Editor anzeigen\",\"Beim Daraufzeigen auf den Editor nach oben scrollen.\",\"Beim Daraufzeigen auf den Editor nach unten. scrollen.\",\"Bildlauf nach links beim Daraufzeigen auf den Editor.\",\"Bildlauf nach rechts beim Daraufzeigen auf den Editor.\",\"Eine Seite nach oben beim Daraufzeigen auf den Editor.\",\"Eine Seite nach unten beim Daraufzeigen auf den Editor.\",\"Beim Daraufzeigen auf den Editor zum oberen Rand navigieren.\",\"Beim Daraufzeigen auf den Editor zum unteren Rand navigieren.\",\"Symbol zum Erhöhen der Ausführlichkeit beim Daraufzeigen.\",\"Symbol zum Verringern der Ausführlichkeit beim Daraufzeigen.\",\"Wird geladen...\",\"Das Rendering langer Zeilen wurde aus Leistungsgründen angehalten. Dies kann über „editor.stopRenderingLineAfter“ konfiguriert werden.\",\"Die Tokenisierung wird bei langen Zeilen aus Leistungsgründen übersprungen. Dies kann über „editor.maxTokenizationLineLength“ konfiguriert werden.\",\"Ausführlichkeit beim Daraufzeigen erhöhen ({0})\",\"Ausführlichkeit beim Daraufzeigen erhöhen\",\"Ausführlichkeit beim Daraufzeigen verringern ({0})\",\"Ausführlichkeit beim Daraufzeigen verringern\",\"Problem anzeigen\",\"Keine Schnellkorrekturen verfügbar\",\"Es wird nach Schnellkorrekturen gesucht...\",\"Keine Schnellkorrekturen verfügbar\",\"Schnelle Problembehebung ...\",\"Einzug in Leerzeichen konvertieren\",\"Einzug in Tabstopps konvertieren\",\"Konfigurierte Tabulatorgröße\",\"Standardregisterkartengröße\",\"Aktuelle Registerkartengröße\",\"Tabulatorgröße für aktuelle Datei auswählen\",\"Einzug mithilfe von Tabstopps\",\"Einzug mithilfe von Leerzeichen\",\"Anzeigegröße der Registerkarte ändern\",\"Einzug aus Inhalt erkennen\",\"Neuen Einzug für Zeilen festlegen\",\"Gewählte Zeilen zurückziehen\",\"Tabulatoreinzug in Leerzeichen umwandeln.\",\"Leerraumeinzug in Tabulator konvertieren.\",\"Einzug mit Tabulator verwenden.\",\"Einzug mit Leerzeichen verwenden.\",\"Leerzeichengröße entsprechend der Tabulatorgröße ändern.\",\"Einzug aus dem Inhalt erkennen.\",\"Die Zeilen des Editors erneut einrücken.\",\"Die ausgewählten Zeilen des Editors erneut einrücken.\",\"Zum Einfügen doppelklicken\",\"BEFEHL + Klicken\",\"STRG + Klicken\",\"OPTION + Klicken\",\"ALT + Klicken\",\"Wechseln Sie zu Definition ({0}), klicken Sie mit der rechten Maustaste, um weitere Informationen zu finden.\",\"Gehe zu Definition ({0})\",\"Befehl ausführen\",\"Nächsten Inline-Vorschlag anzeigen\",\"Vorherigen Inline-Vorschlag anzeigen\",\"Inline-Vorschlag auslösen\",\"Nächstes Wort des Inline-Vorschlags annehmen\",\"Wort annehmen\",\"Nächste Zeile des Inlinevorschlags akzeptieren\",\"Zeile annehmen\",\"Inline-Vorschlag annehmen\",\"Akzeptieren\",\"Inlinevorschlag ausblenden\",\"Symbolleiste immer anzeigen\",\"Gibt an, ob ein Inline-Vorschlag sichtbar ist.\",\"Gibt an, ob der Inline-Vorschlag mit Leerzeichen beginnt.\",\"Ob der Inline-Vorschlag mit Leerzeichen beginnt, das kleiner ist als das, was durch die Tabulatortaste eingefügt werden würde\",\"Gibt an, ob Vorschläge für den aktuellen Vorschlag unterdrückt werden sollen\",\"Überprüfen Sie dies in der barrierefreien Ansicht ({0}).\",\"Vorschlag:\",\"Symbol für die Anzeige des nächsten Parameterhinweises.\",\"Symbol für die Anzeige des vorherigen Parameterhinweises.\",\"{0} ({1})\",\"Zurück\",\"Weiter\",null,null,null,null,null,null,null,null,\"Durch vorherigen Wert ersetzen\",\"Durch nächsten Wert ersetzen\",\"Zeilenauswahl erweitern\",\"Zeile nach oben kopieren\",\"Zeile nach oben &&kopieren\",\"Zeile nach unten kopieren\",\"Zeile nach unten ko&&pieren\",\"Auswahl duplizieren\",\"&&Auswahl duplizieren\",\"Zeile nach oben verschieben\",\"Zeile nach oben &&verschieben\",\"Zeile nach unten verschieben\",\"Zeile nach &&unten verschieben\",\"Zeilen aufsteigend sortieren\",\"Zeilen absteigend sortieren\",\"Doppelte Zeilen löschen\",\"Nachgestelltes Leerzeichen kürzen\",\"Zeile löschen\",\"Zeileneinzug\",\"Zeile ausrücken\",\"Zeile oben einfügen\",\"Zeile unten einfügen\",\"Alle übrigen löschen\",\"Alle rechts löschen\",\"Zeilen verknüpfen\",\"Zeichen um den Cursor herum transponieren\",\"In Großbuchstaben umwandeln\",\"In Kleinbuchstaben umwandeln\",\"In große Anfangsbuchstaben umwandeln\",\"In Snake Case umwandeln\",\"In CamelCase umwandeln\",\"In Pascal-Pascal-Schreibweise transformieren\",\"In kebab-case umwandeln\",\"Verknüpfte Bearbeitung starten\",\"Hintergrundfarbe, wenn der Editor automatisch nach Typ umbenennt.\",\"Fehler beim Öffnen dieses Links, weil er nicht wohlgeformt ist: {0}\",\"Fehler beim Öffnen dieses Links, weil das Ziel fehlt.\",\"Befehl ausführen\",\"Link folgen\",\"BEFEHL + Klicken\",\"STRG + Klicken\",\"OPTION + Klicken\",\"alt + klicken\",\"Führen Sie den Befehl \\\"{0}\\\" aus.\",\"Link öffnen\",\"Gibt an, ob der Editor zurzeit eine Inlinenachricht anzeigt.\",\"Hinzugefügter Cursor: {0}\",\"Hinzugefügte Cursor: {0}\",\"Cursor oberhalb hinzufügen\",\"Cursor oberh&&alb hinzufügen\",\"Cursor unterhalb hinzufügen\",\"Cursor unterhal&&b hinzufügen\",\"Cursor an Zeilenenden hinzufügen\",\"C&&ursor an Zeilenenden hinzufügen\",\"Cursor am Ende hinzufügen\",\"Cursor am Anfang hinzufügen\",\"Auswahl zur nächsten Übereinstimmungssuche hinzufügen\",\"&&Nächstes Vorkommen hinzufügen\",\"Letzte Auswahl zu vorheriger Übereinstimmungssuche hinzufügen\",\"Vo&&rheriges Vorkommen hinzufügen\",\"Letzte Auswahl in nächste Übereinstimmungssuche verschieben\",\"Letzte Auswahl in vorherige Übereinstimmungssuche verschieben\",\"Alle Vorkommen auswählen und Übereinstimmung suchen\",\"Alle V&&orkommen auswählen\",\"Alle Vorkommen ändern\",\"Fokus auf nächsten Cursor\",\"Fokussiert den nächsten Cursor\",\"Fokus auf vorherigen Cursor\",\"Fokussiert den vorherigen Cursor\",\"Parameterhinweise auslösen\",\"Symbol für die Anzeige des nächsten Parameterhinweises.\",\"Symbol für die Anzeige des vorherigen Parameterhinweises.\",\"{0}, Hinweis\",\"Vordergrundfarbe des aktiven Elements im Parameterhinweis.\",\"Gibt an, ob der aktuelle Code-Editor in der Vorschau eingebettet ist.\",\"Schließen\",\"Hintergrundfarbe des Titelbereichs der Peek-Ansicht.\",\"Farbe des Titels in der Peek-Ansicht.\",\"Farbe der Titelinformationen in der Peek-Ansicht.\",\"Farbe der Peek-Ansichtsränder und des Pfeils.\",\"Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.\",\"Vordergrundfarbe für Zeilenknoten in der Ergebnisliste der Peek-Ansicht.\",\"Vordergrundfarbe für Dateiknoten in der Ergebnisliste der Peek-Ansicht.\",\"Hintergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.\",\"Vordergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.\",\"Hintergrundfarbe des Peek-Editors.\",\"Hintergrundfarbe der Leiste im Peek-Editor.\",\"Die Hintergrundfarbe für den „Sticky“-Bildlaufeffekt im Editor für die „Peek“-Ansicht.\",\"Farbe für Übereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.\",\"Farbe für Übereinstimmungsmarkierungen im Peek-Editor.\",\"Rahmen für Übereinstimmungsmarkierungen im Peek-Editor.\",\"Vordergrundfarbe des Platzhaltertexts im Editor.\",\"Öffnen Sie zuerst einen Text-Editor, um zu einer Zeile zu wechseln.\",\"Wechseln Sie zu Zeile {0} und Zeichen {1}.\",\"Zu Zeile {0} wechseln.\",\"Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer zwischen 1 und {2} ein, zu der Sie navigieren möchten.\",\"Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer ein, zu der Sie navigieren möchten.\",\"Öffnen Sie zunächst einen Text-Editor mit Symbolinformationen, um zu einem Symbol zu navigieren.\",\"Der aktive Text-Editor stellt keine Symbolinformationen bereit.\",\"Keine übereinstimmenden Editorsymbole.\",\"Keine Editorsymbole.\",\"An der Seite öffnen\",\"Unten öffnen\",\"Symbole ({0})\",\"Eigenschaften ({0})\",\"Methoden ({0})\",\"Funktionen ({0})\",\"Konstruktoren ({0})\",\"Variablen ({0})\",\"Klassen ({0})\",\"Strukturen ({0})\",\"Ereignisse ({0})\",\"Operatoren ({0})\",\"Schnittstellen ({0})\",\"Namespaces ({0})\",\"Pakete ({0})\",\"Typparameter ({0})\",\"Module ({0})\",\"Eigenschaften ({0})\",\"Enumerationen ({0})\",\"Enumerationsmember ({0})\",\"Zeichenfolgen ({0})\",\"Dateien ({0})\",\"Arrays ({0})\",\"Zahlen ({0})\",\"Boolesche Werte ({0})\",\"Objekte ({0})\",\"Schlüssel ({0})\",\"Felder ({0})\",\"Konstanten ({0})\",\"Bearbeitung von schreibgeschützter Eingabe nicht möglich\",\"Ein Bearbeiten ist im schreibgeschützten Editor nicht möglich\",\"Kein Ergebnis.\",\"Ein unbekannter Fehler ist beim Auflösen der Umbenennung eines Ortes aufgetreten.\",\"'{0}' wird in '{1}' umbenannt\",\"{0} wird in {1} umbenannt.\",\"\\\"{0}\\\" erfolgreich in \\\"{1}\\\" umbenannt. Zusammenfassung: {2}\",\"Die rename-Funktion konnte die Änderungen nicht anwenden.\",\"Die rename-Funktion konnte die Änderungen nicht berechnen.\",\"Symbol umbenennen\",\"Möglichkeit aktivieren/deaktivieren, Änderungen vor dem Umbenennen als Vorschau anzeigen zu lassen\",\"Nächsten Umbenennungsvorschlag fokussieren\",\"Vorherigen Umbenennungsvorschlag fokussieren\",\"Gibt an, ob das Widget zum Umbenennen der Eingabe sichtbar ist.\",\"Gibt an, ob das Widget zum Umbenennen der Eingabe fokussiert ist.\",\"{0} zur Umbenennung, {1} zur Vorschau\",\"{0} Vorschläge zum Umbenennen erhalten\",\"Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und drücken Sie die EINGABETASTE, um den Commit auszuführen.\",\"Vorschläge für neuen Namen generieren\",\"Abbrechen\",\"Auswahl aufklappen\",\"Auswahl &&erweitern\",\"Markierung verkleinern\",\"Au&&swahl verkleinern\",\"Gibt an, ob der Editor sich zurzeit im Schnipselmodus befindet.\",\"Gibt an, ob ein nächster Tabstopp im Schnipselmodus vorhanden ist.\",\"Gibt an, ob ein vorheriger Tabstopp im Schnipselmodus vorhanden ist.\",\"Zum nächsten Platzhalter wechseln...\",\"Sonntag\",\"Montag\",\"Dienstag\",\"Mittwoch\",\"Donnerstag\",\"Freitag\",\"Samstag\",\"So\",\"Mo\",\"Di\",\"Mi\",\"Do\",\"Fr\",\"Sa\",\"Januar\",\"Februar\",\"März\",\"April\",\"Mai\",\"Juni\",\"Juli\",\"August\",\"September\",\"Oktober\",\"November\",\"Dezember\",\"Jan\",\"Feb\",\"Mär\",\"Apr\",\"Mai\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Okt\",\"Nov\",\"Dez\",\"Fixierten Bildlauf des Editors &&umschalten\",\"Fixierter Bildlauf\",\"&&Fixierter Bildlauf\",\"&&Fokus fixierter Bildlauf\",\"Fixierten Bildlauf für Editor umschalten\",\"Fixierten Bildlauf des Editors umschalten/aktivieren, der die geschachtelten Bereiche am oberen Rand des Viewports anzeigt\",\"Auf fixierten Bildlauf des Editors fokussieren\",\"Nächste fixierte Bildlaufzeile des Editors auswählen\",\"Vorherige fixierte Bildlaufzeile auswählen\",\"Zur fokussierten fixierten Bildlaufzeile wechseln\",\"Editor auswählen\",\"Gibt an, ob ein Vorschlag fokussiert ist\",\"Gibt an, ob Vorschlagsdetails sichtbar sind.\",\"Gibt an, ob mehrere Vorschläge zur Auswahl stehen.\",\"Gibt an, ob das Einfügen des aktuellen Vorschlags zu einer Änderung führt oder ob bereits alles eingegeben wurde.\",\"Gibt an, ob Vorschläge durch Drücken der EINGABETASTE eingefügt werden.\",\"Gibt an, ob der aktuelle Vorschlag Verhalten zum Einfügen und Ersetzen aufweist.\",\"Gibt an, ob Einfügen oder Ersetzen als Standardverhalten verwendet wird.\",\"Gibt an, ob der aktuelle Vorschlag die Auflösung weiterer Details unterstützt.\",\"Das Akzeptieren von \\\"{0}\\\" ergab {1} zusätzliche Bearbeitungen.\",\"Vorschlag auslösen\",\"Einfügen\",\"Einfügen\",\"Ersetzen\",\"Ersetzen\",\"Einfügen\",\"Weniger anzeigen\",\"Mehr anzeigen\",\"Größe des Vorschlagswidgets zurücksetzen\",\"Hintergrundfarbe des Vorschlagswidgets.\",\"Rahmenfarbe des Vorschlagswidgets.\",\"Vordergrundfarbe des Vorschlagswidgets.\",\"Die Vordergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.\",\"Die Vordergrundfarbe des Symbols des ausgewählten Eintrags im Vorschlagswidget.\",\"Hintergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.\",\"Farbe der Trefferhervorhebung im Vorschlagswidget.\",\"Die Farbe des Treffers wird im Vorschlagswidget hervorgehoben, wenn ein Element fokussiert wird.\",\"Vordergrundfarbe des Status des Vorschlagswidgets.\",\"Wird geladen...\",\"Keine Vorschläge.\",\"Vorschlagen\",\"{0} {1}, {2}\",\"{0} {1}\",\"{0}, {1}\",\"{0}, Dokumente: {1}\",\"Schließen\",\"Wird geladen...\",\"Symbol für weitere Informationen im Vorschlags-Widget.\",\"Weitere Informationen\",\"Die Vordergrundfarbe für Arraysymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für boolesche Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Klassensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Farbsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für konstante Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Konstruktorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Enumeratorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Enumeratormembersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Ereignissymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Feldsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Dateisymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Ordnersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Funktionssymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Schnittstellensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Schlüsselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Schlüsselwortsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Methodensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Modulsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Namespacesymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für NULL-Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Zahlensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Objektsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Operatorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Paketsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Eigenschaftensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Referenzsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Codeschnipselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Zeichenfolgensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Struktursymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Textsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Typparametersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für Einheitensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Die Vordergrundfarbe für variable Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\"Beim Drücken auf Tab wird der Fokus jetzt auf das nächste fokussierbare Element verschoben\",\"Beim Drücken von Tab wird jetzt das Tabulator-Zeichen eingefügt\",\"TAB-Umschalttaste verschiebt Fokus\",\"Bestimmt, ob die Tabulatortaste den Fokus um die Workbench verschiebt oder das Tabulatorzeichen im aktuellen Editor einfügt. Dies wird auch als Tabstopp, Tabulatornavigation oder Tabulatorfokusmodus bezeichnet.\",\"Entwickler: Force Retokenize\",\"Symbol, das mit einer Warnmeldung im Erweiterungs-Editor angezeigt wird.\",\"Dieses Dokument enthält viele nicht einfache ASCII-Unicode-Zeichen.\",\"Dieses Dokument enthält viele mehrdeutige Unicode-Zeichen.\",\"Dieses Dokument enthält viele unsichtbare Unicode-Zeichen.\",\"Konfigurieren der Optionen für die Unicode-Hervorhebung\",\"Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode häufiger vorkommt.\",\"Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode häufiger vorkommt.\",\"Das Zeichen {0} ist nicht sichtbar.\",\"Das Zeichen {0} ist kein einfaches ASCII-Zeichen.\",\"Einstellungen anpassen\",\"Hervorhebung in Kommentaren deaktivieren\",\"Deaktivieren der Hervorhebung von Zeichen in Kommentaren\",\"Hervorhebung in Zeichenfolgen deaktivieren\",\"Deaktivieren der Hervorhebung von Zeichen in Zeichenfolgen\",\"Mehrdeutige Hervorhebung deaktivieren\",\"Deaktivieren der Hervorhebung von mehrdeutigen Zeichen\",\"Unsichtbare Hervorhebung deaktivieren\",\"Deaktivieren der Hervorhebung unsichtbarer Zeichen\",\"Nicht-ASCII-Hervorhebung deaktivieren\",\"Deaktivieren der Hervorhebung von nicht einfachen ASCII-Zeichen\",\"Ausschlussoptionen anzeigen\",\"{0} (unsichtbares Zeichen) von der Hervorhebung ausschließen\",\"{0} nicht hervorheben\",\"Unicodezeichen zulassen, die in der Sprache „{0}“ häufiger vorkommen.\",\"Ungewöhnliche Zeilentrennzeichen\",\"Ungewöhnliche Zeilentrennzeichen erkannt\",\"Die Datei \\\"{0}\\\" enthält mindestens ein ungewöhnliches Zeilenabschlusszeichen, z. B. Zeilentrennzeichen (LS) oder Absatztrennzeichen (PS).\\r\\n\\r\\nEs wird empfohlen, sie aus der Datei zu entfernen. Dies kann über \\\"editor.unusualLineTerminators\\\" konfiguriert werden.\",\"&&Ungewöhnliche Zeilenabschlusszeichen entfernen\",\"Ignorieren\",\"Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Die Hintergrundfarbe eines Textteils für ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\"Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.\",\"Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.\",\"Die Rahmenfarbe eines Textteils für ein Symbol.\",\"Übersichtslinealmarkerfarbd für das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Übersichtslinealmarkerfarbe für Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Die Markierungsfarbe des Übersichtslineals eines Textteils für ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\"Gehe zur nächsten Symbolhervorhebungen\",\"Gehe zur vorherigen Symbolhervorhebungen\",\"Symbol-Hervorhebung ein-/ausschalten\",\"Wort löschen\",\"Fehler an Position\",\"Fehler\",\"Warnung an Position\",\"Warnung\",\"Fehler in der Zeile\",\"Fehler in Zeile\",\"Warnung in der Zeile\",\"Warnung in Zeile\",\"Gefalteter Bereich in der Zeile\",\"Gefaltet\",\"Haltepunkt in der Zeile\",\"Haltepunkt\",\"Inlinevorschlag in der Zeile\",\"Terminale schnelle Problembehebung\",\"Schnelle Problembehebung\",\"Debugger auf Haltepunkt beendet\",\"Haltepunkt\",\"Keine Inlay-Hinweise in der Zeile\",\"Keine Inlay-Hinweise\",\"Aufgabe abgeschlossen\",\"Aufgabe abgeschlossen\",\"Aufgabe fehlgeschlagen\",\"Fehler bei Aufgabe\",\"Terminalbefehl fehlgeschlagen\",\"Befehl fehlgeschlagen\",\"Terminalbefehl erfolgreich\",\"Befehl erfolgreich\",\"Terminalglocke\",\"Terminalglocke\",\"Notebookzelle abgeschlossen\",\"Notebookzelle abgeschlossen\",\"Notebookzelle fehlgeschlagen\",\"Notebookzelle fehlgeschlagen\",\"Vergleichslinie eingefügt\",\"Vergleichslinie gelöscht\",\"Vergleichslinie geändert\",\"Chatanfrage gesendet\",\"Chatanfrage gesendet\",\"Chatantwort empfangen\",\"Status\",\"Status\",\"Löschen\",\"Löschen\",\"Speichern\",\"Speichern\",\"Format\",\"Formatieren\",\"Sprachaufzeichnung gestartet\",\"Sprachaufzeichnung beendet\",\"Ansehen\",\"Hilfe\",\"Test\",\"Datei\",\"Einstellungen\",\"Entwickler\",\"{0} ({1})\",\"{0} ({1})\",\"{0}\\r\\n[{1}] {2}\",\"{1} bis {0}\",\"{0} ({1})\",\"Ausblenden\",\"Menü zurücksetzen\",\"\\\"{0}\\\" ausblenden\",\"Tastenzuordnung konfigurieren\",\"{0} zum Anwenden, {1} für Vorschau\",\"{0} zum Anwenden\",\"{0} deaktiviert, Grund: {1}\",\"Aktionswidget\",\"Hintergrundfarbe für umgeschaltete Aktionselemente in der Aktionsleiste.\",\"Gibt an, ob die Aktionswidgetliste sichtbar ist.\",\"Codeaktionswidget ausblenden\",\"Vorherige Aktion auswählen\",\"Nächste Aktion auswählen\",\"Ausgewählte Aktion akzeptieren\",\"Vorschau für ausgewählte Elemente anzeigen\",\"Außerkraftsetzungen für die Standardsprachkonfiguration\",\"Konfigurieren Sie Einstellungen, die für die Sprache {0} überschrieben werden sollen.\",\"Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.\",\"Diese Einstellung unterstützt keine sprachspezifische Konfiguration.\",\"Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.\",\"Diese Einstellung unterstützt keine sprachspezifische Konfiguration.\",\"Eine leere Eigenschaft kann nicht registriert werden.\",\"\\\"{0}\\\" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster \\\"\\\\\\\\[.*\\\\\\\\]$\\\" zum Beschreiben sprachspezifischer Editor-Einstellungen überein. Verwenden Sie den Beitrag \\\"configurationDefaults\\\".\",\"{0}\\\" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.\",\"\\\"{0}\\\" kann nicht registriert werden. Die zugeordnete Richtlinie {1} ist bereits bei {2} registriert.\",\"Ein Befehl, der Informationen zu Kontextschlüsseln zurückgibt\",\"Leerer Kontextschlüsselausdruck\",\"Haben Sie vergessen, einen Ausdruck zu schreiben? Sie können auch „false“ oder „true“ festlegen, um immer auf „false“ oder „true“ auszuwerten.\",\"„in“ nach „not“.\",\"schließende Klammer „)“\",\"Unerwartetes Token\",\"Haben Sie vergessen, && oder || vor dem Token einzufügen?\",\"Unerwartetes Ende des Ausdrucks.\",\"Haben Sie vergessen, einen Kontextschlüssel zu setzen?\",\"Erwartet: {0}\\r\\nEmpfangen: „{1}“.\",\"Gibt an, ob macOS als Betriebssystem verwendet wird.\",\"Gibt an, ob Linux als Betriebssystem verwendet wird.\",\"Gibt an, ob Windows als Betriebssystem verwendet wird.\",\"Gibt an, ob es sich bei der Plattform um einen Webbrowser handelt.\",\"Gibt an, ob macOS auf einer Nicht-Browser-Plattform als Betriebssystem verwendet wird.\",\"Gibt an, ob iOS als Betriebssystem verwendet wird.\",\"Gibt an, ob es sich bei der Plattform um einen mobilen Webbrowser handelt.\",\"Qualitätstyp des VS Codes\",\"Gibt an, ob sich der Tastaturfokus in einem Eingabefeld befindet.\",\"Meinten Sie {0}?\",\"Meinten Sie {0} oder {1}?\",\"Meinten Sie {0}, {1} oder {2}?\",\"Haben Sie vergessen, das Anführungszeichen zu öffnen oder zu schließen?\",\"Haben Sie vergessen, das Zeichen „/“ (Schrägstrich) zu escapen? Setzen Sie zwei Backslashes davor, um es zu escapen, z. B. „\\\\\\\\/“.\",\"Gibt an, ob Vorschläge sichtbar sind.\",\"({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...\",\"({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...\",\"Die Tastenkombination ({0}, {1}) ist kein Befehl.\",\"Die Tastenkombination ({0}, {1}) ist kein Befehl.\",\"Workbench\",\"Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.\",\"Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.\",\"Der Modifizierer zum Hinzufügen eines Elements in Bäumen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in geöffneten Editoren und in der SCM-Ansicht). Die Mausbewegung \\\"Seitlich öffnen\\\" wird – sofern unterstützt – so angepasst, dass kein Konflikt mit dem Modifizierer für Mehrfachauswahl entsteht.\",\"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus geöffnet werden (sofern unterstützt). Bei übergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das übergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.\",\"Steuert, ob Listen und Strukturen ein horizontales Scrollen in der Workbench unterstützen. Warnung: Das Aktivieren dieser Einstellung kann sich auf die Leistung auswirken.\",\"Steuert, ob Klicks in der Bildlaufleiste Seite für Seite scrollen.\",\"Steuert den Struktureinzug in Pixeln.\",\"Steuert, ob die Struktur Einzugsführungslinien rendern soll.\",\"Steuert, ob Listen und Strukturen einen optimierten Bildlauf verwenden.\",\"Ein Multiplikator, der für die Mausrad-Bildlaufereignisse \\\"deltaX\\\" und \\\"deltaY\\\" verwendet werden soll.\",\"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.\",\"Elemente beim Suchen hervorheben. Die Navigation nach oben und unten durchläuft dann nur die markierten Elemente.\",\"Filterelemente bei der Suche.\",\"Steuert den Standardsuchmodus für Listen und Strukturen in der Workbench.\",\"Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe übereinstimmen. Die Übereinstimmungen gelten nur für Präfixe.\",\"Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe übereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.\",\"Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe übereinstimmen.\",\"Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann \\\"simple\\\" (einfach), \\\"highlight\\\" (hervorheben) und \\\"filter\\\" (filtern) sein.\",\"Bitte verwenden Sie stattdessen „workbench.list.defaultFindMode“ und „workbench.list.typeNavigationMode“.\",\"Verwenden Sie bei der Suche eine Fuzzyübereinstimmung.\",\"Verwenden Sie bei der Suche eine zusammenhängende Übereinstimmung.\",\"Steuert den Typ der Übereinstimmung, der beim Durchsuchen von Listen und Strukturen in der Workbench verwendet wird.\",\"Steuert, wie Strukturordner beim Klicken auf die Ordnernamen erweitert werden. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.\",\"Steuert, ob fester Bildlauf in Strukturen aktiviert ist.\",\"Steuert die Anzahl der festen Elemente, die in der Struktur angezeigt werden, wenn {0} aktiviert ist.\",\"Steuert die Funktionsweise der Typnavigation in Listen und Strukturen in der Workbench. Bei einer Festlegung auf \\\"trigger\\\" beginnt die Typnavigation, sobald der Befehl \\\"list.triggerTypeNavigation\\\" ausgeführt wird.\",\"Fehler\",\"Warnung\",\"Info\",\"zuletzt verwendet\",\"ähnliche Befehle\",\"häufig verwendet\",\"andere Befehle\",\"ähnliche Befehle\",\"{0}, {1}\",\"Der Befehl \\\"{0}\\\" hat zu einem Fehler geführt.\",\"{0}, {1}\",\"Gibt an, ob sich der Tastaturfokus innerhalb des Steuerelements für die Schnelleingabe befindet.\",\"Der Typ der aktuell sichtbaren Schnelleingabe\",\"Gibt an, ob sich der Cursor in der Schnelleingabe am Ende des Eingabefelds befindet.\",\"Zurück\",\"Drücken Sie die EINGABETASTE, um Ihre Eingabe zu bestätigen, oder ESC, um den Vorgang abzubrechen.\",\"{0}/{1}\",\"Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.\",\"Wird im Kontext der Schnellauswahl verwendet. Wenn Sie eine Tastenzuordnung für diesen Befehl ändern, sollten Sie auch alle anderen Tastenzuordnungen (Modifizierervarianten) dieses Befehls ändern.\",\"Wenn wir uns im Schnellzugriffsmodus befinden, wird zum nächsten Element navigiert. Wenn wir uns nicht im Schnellzugriffsmodus befinden, wird zum nächsten Trennzeichen navigiert.\",\"Wenn wir uns im Schnellzugriffsmodus befinden, wird zum vorherigen Element navigiert. Wenn wir uns nicht im Schnellzugriffsmodus befinden, wird zum vorherigen Trennzeichen navigiert.\",\"Aktivieren Sie alle Kontrollkästchen\",\"{0} Ergebnisse\",\"{0} ausgewählt\",\"OK\",\"Benutzerdefiniert\",\"Zurück ({0})\",\"Zurück\",\"Schnelleingabe\",\"Klicken, um den Befehl \\\"{0}\\\" auszuführen\",\"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\"Allgemeine Vordergrundfarbe für Fehlermeldungen. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\"Vordergrundfarbe für Beschreibungstexte, die weitere Informationen anzeigen, z.B. für eine Beschriftung.\",\"Die für Symbole in der Workbench verwendete Standardfarbe.\",\"Allgemeine Rahmenfarbe für fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\"Ein zusätzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.\",\"Ein zusätzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.\",\"Hintergrundfarbe der Textauswahl in der Workbench (z.B. für Eingabefelder oder Textbereiche). Diese Farbe gilt nicht für die Auswahl im Editor.\",\"Vordergrundfarbe für Links im Text.\",\"Vordergrundfarbe für angeklickte Links im Text und beim Zeigen darauf mit der Maus.\",\"Farbe für Text-Trennzeichen.\",\"Vordergrundfarbe für vorformatierte Textsegmente.\",\"Hintergrundfarbe für vorformatierte Textsegmente.\",\"Hintergrundfarbe für Blockzitate im Text.\",\"Rahmenfarbe für blockquote-Elemente im Text.\",\"Hintergrundfarbe für Codeblöcke im Text.\",\"Die in Diagrammen verwendete Vordergrundfarbe.\",\"Die für horizontale Linien in Diagrammen verwendete Farbe.\",\"Die in Diagrammvisualisierungen verwendete Farbe Rot.\",\"Die in Diagrammvisualisierungen verwendete Farbe Blau.\",\"Die in Diagrammvisualisierungen verwendete Farbe Gelb.\",\"Die in Diagrammvisualisierungen verwendete Farbe Orange.\",\"Die in Diagrammvisualisierungen verwendete Farbe Grün.\",\"Die in Diagrammvisualisierungen verwendete Farbe Violett.\",\"Hintergrundfarbe des Editors.\",\"Standardvordergrundfarbe des Editors.\",\"Hintergrundfarbe des fixierten Bildlaufs im Editor\",\"Hintergrundfarbe des fixierten Bildlaufs beim Daraufzeigen im Editor\",\"Rahmenfarbe des fixierten Bildlaufs im Editor\",\" Schattenfarbe des fixierten Bildlaufs im Editor\",\"Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.\",\"Vordergrundfarbe für Editorwidgets wie Suchen/Ersetzen.\",\"Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget überschrieben wird.\",\"Rahmenfarbe der Größenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Größenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget außer Kraft gesetzt wird.\",\"Hintergrundfarbe für Fehlertext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Vordergrundfarbe von Fehlerunterstreichungen im Editor.\",\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Fehler im Editor angezeigt.\",\"Hintergrundfarbe für Warnungstext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Vordergrundfarbe von Warnungsunterstreichungen im Editor.\",\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Warnungen im Editor angezeigt.\",\"Hintergrundfarbe für Infotext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Vordergrundfarbe von Informationsunterstreichungen im Editor.\",\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Infos im Editor angezeigt.\",\"Vordergrundfarbe der Hinweisunterstreichungen im Editor.\",\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Hinweise im Editor angezeigt.\",\"Farbe der aktiven Links.\",\"Farbe der Editor-Auswahl.\",\"Farbe des gewählten Text für einen hohen Kontrast\",\"Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.\",\"Farbe für Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Randfarbe für Bereiche, deren Inhalt der Auswahl entspricht.\",\"Farbe des aktuellen Suchergebnisses.\",\"Textfarbe der aktuellen Suchübereinstimmung.\",\"Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Vordergrundfarbe der anderen Suchübereinstimmungen.\",\"Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.\",\"Randfarbe des aktuellen Suchergebnisses.\",\"Randfarbe der anderen Suchtreffer.\",\"Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hervorhebung unterhalb des Worts, für das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrundfarbe des Editor-Mauszeigers.\",\"Vordergrundfarbe des Editor-Mauszeigers\",\"Rahmenfarbe des Editor-Mauszeigers.\",\"Hintergrundfarbe der Hoverstatusleiste des Editors.\",\"Vordergrundfarbe für Inlinehinweise\",\"Hintergrundfarbe für Inlinehinweise\",\"Vordergrundfarbe von Inlinehinweisen für Typen\",\"Hintergrundfarbe von Inlinehinweisen für Typen\",\"Vordergrundfarbe von Inlinehinweisen für Parameter\",\"Hintergrundfarbe von Inlinehinweisen für Parameter\",\"Die für das Aktionssymbol \\\"Glühbirne\\\" verwendete Farbe.\",\"Die für das Aktionssymbol \\\"Automatische Glühbirnenkorrektur\\\" verwendete Farbe.\",\"Die Farbe, die für das KI-Symbol der Glühbirne verwendet wird.\",\"Hervorhebungs-Hintergrundfarbe eines Codeschnipsel-Tabstopps.\",\"Hervorhebungs-Rahmenfarbe eines Codeschnipsel-Tabstopps.\",\"Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeschnipsels.\",\"Rahmenfarbe zur Hervorhebung des letzten Tabstopps eines Codeschnipsels.\",\"Hintergrundfarbe für eingefügten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrundfarbe für Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrundfarbe für eingefügte Zeilen. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\"Hintergrundfarbe für Zeilen, die entfernt wurden. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\"Hintergrundfarbe für den Rand, an dem Zeilen eingefügt wurden.\",\"Hintergrundfarbe für den Rand, an dem die Zeilen entfernt wurden.\",\"Vordergrund des Diff-Übersichtslineals für eingefügten Inhalt.\",\"Vordergrund des Diff-Übersichtslineals für entfernten Inhalt.\",\"Konturfarbe für eingefügten Text.\",\"Konturfarbe für entfernten Text.\",\"Die Rahmenfarbe zwischen zwei Text-Editoren.\",\"Farbe der diagonalen Füllung des Vergleichs-Editors. Die diagonale Füllung wird in Ansichten mit parallelem Vergleich verwendet.\",\"Die Hintergrundfarbe von unveränderten Blöcken im Diff-Editor.\",\"Die Vordergrundfarbe von unveränderten Blöcken im Diff-Editor.\",\"Die Hintergrundfarbe des unveränderten Codes im Diff-Editor.\",\"Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.\",\"Die Rahmenfarbe von Widgets, z. B. Suchen/Ersetzen im Editor.\",\"Symbolleistenhintergrund beim Bewegen der Maus über Aktionen\",\"Symbolleistengliederung beim Bewegen der Maus über Aktionen\",\"Symbolleistenhintergrund beim Halten der Maus über Aktionen\",\"Farbe der Breadcrumb-Elemente, die den Fokus haben.\",\"Hintergrundfarbe der Breadcrumb-Elemente.\",\"Farbe der Breadcrumb-Elemente, die den Fokus haben.\",\"Die Farbe der ausgewählten Breadcrumb-Elemente.\",\"Hintergrundfarbe des Breadcrumb-Auswahltools.\",\"Hintergrund des aktuellen Headers in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrund für den aktuellen Inhalt in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrund für eingehende Header in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrund für eingehenden Inhalt in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Headerhintergrund für gemeinsame Vorgängerelemente in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Hintergrund des Inhalts gemeinsamer Vorgängerelemente in Inlinezusammenführungskonflikt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Rahmenfarbe für Kopfzeilen und die Aufteilung in Inline-Mergingkonflikten.\",\"Aktueller Übersichtslineal-Vordergrund für Inline-Mergingkonflikte.\",\"Eingehender Übersichtslineal-Vordergrund für Inline-Mergingkonflikte.\",\"Hintergrund des Übersichtslineals des gemeinsamen übergeordneten Elements bei Inlinezusammenführungskonflikten.\",\"Übersichtslinealmarkerfarbe für das Suchen von Übereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Übersichtslinealmarkerfarbe für das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\"Die Farbe, die für das Problemfehlersymbol verwendet wird.\",\"Die Farbe, die für das Problemwarnsymbol verwendet wird.\",\"Die Farbe, die für das Probleminfosymbol verwendet wird.\",\"Hintergrund für Eingabefeld.\",\"Vordergrund für Eingabefeld.\",\"Rahmen für Eingabefeld.\",\"Rahmenfarbe für aktivierte Optionen in Eingabefeldern.\",\"Hintergrundfarbe für aktivierte Optionen in Eingabefeldern.\",\"Hintergrundfarbe beim Daraufzeigen für Optionen in Eingabefeldern.\",\"Vordergrundfarbe für aktivierte Optionen in Eingabefeldern.\",\"Eingabefeld-Vordergrundfarbe für Platzhaltertext.\",\"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.\",\"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.\",\"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Information.\",\"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.\",\"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.\",\"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.\",\"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.\",\"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.\",\"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.\",\"Hintergrund für Dropdown.\",\"Hintergrund für Dropdownliste.\",\"Vordergrund für Dropdown.\",\"Rahmen für Dropdown.\",\"Vordergrundfarbe der Schaltfläche.\",\"Farbe des Schaltflächentrennzeichens.\",\"Hintergrundfarbe der Schaltfläche.\",\"Hintergrundfarbe der Schaltfläche, wenn darauf gezeigt wird.\",\"Rahmenfarbe der Schaltfläche.\",\"Sekundäre Vordergrundfarbe der Schaltfläche.\",\"Hintergrundfarbe der sekundären Schaltfläche.\",\"Hintergrundfarbe der sekundären Schaltfläche beim Daraufzeigen.\",\"Vordergrundfarbe für \\\"aktive Radiooption\\\"\",\"Hintergrundfarbe für \\\"aktive Radiooption\\\"\",\"Rahmenfarbe der aktiven Radiooption\",\"Vordergrundfarbe für „inaktive Radiooption“\",\"Hintergrundfarbe für „inaktive Radiooption“\",\"Rahmenfarbe der inaktiven Radiooption\",\"Hintergrundfarbe der inaktiven aktiven Radiooption, wenn darauf gezeigt wird.\",\"Hintergrundfarbe von Kontrollkästchenwidget.\",\"Hintergrundfarbe des Kontrollkästchenwidgets, wenn das Element ausgewählt ist, in dem es sich befindet.\",\"Vordergrundfarbe von Kontrollkästchenwidget.\",\"Rahmenfarbe von Kontrollkästchenwidget.\",\"Rahmenfarbe des Kontrollkästchenwidgets, wenn das Element ausgewählt ist, in dem es sich befindet.\",\"Die Hintergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\"Die Vordergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\"Die Rahmenfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\"Die Rahmenfarbe der Schaltfläche der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\"Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Vordergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Konturfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Umrissfarbe der Liste/des Baums für das fokussierte Element, wenn die Liste/der Baum aktiv und ausgewählt ist. Eine aktive Liste/Baum hat Tastaturfokus, eine inaktive nicht.\",\"Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Vordergrundfarbe des Symbols der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Vordergrundfarbe des Symbols der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Konturfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\"Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.\",\"Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.\",\"Hintergrund für Drag & Drop auflisten/strukturieren, wenn Elemente bei Verwendung der Maus über Elemente verschoben werden.\",\"Rahmenfarbe für Drag & Drop auflisten/strukturieren, wenn Elemente bei Verwendung der Maus zwischen Elementen verschoben werden.\",\"Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.\",\"Die Vordergrundfarbe der Liste/Struktur des Treffers hebt aktiv fokussierte Elemente hervor, wenn innerhalb der Liste / der Struktur gesucht wird.\",\"Vordergrundfarbe einer Liste/Struktur für ungültige Elemente, z.B. ein nicht ausgelöster Stamm im Explorer.\",\"Vordergrundfarbe für Listenelemente, die Fehler enthalten.\",\"Vordergrundfarbe für Listenelemente, die Warnungen enthalten.\",\"Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.\",\"Konturfarbe des Typfilterwidgets in Listen und Strukturen.\",\"Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine Übereinstimmungen gibt.\",\"Schattenfarbe des Typfilterwidgets in Listen und Strukturen.\",\"Hintergrundfarbe der gefilterten Übereinstimmung\",\"Rahmenfarbe der gefilterten Übereinstimmung\",\"Hintergrundfarbe für nicht hervorgehobene Listen-/Strukturelemente.\",\"Strukturstrichfarbe für die Einzugsführungslinien.\",\"Strukturstrichfarbe für die Einzugslinien, die nicht aktiv sind.\",\"Tabellenrahmenfarbe zwischen Spalten.\",\"Hintergrundfarbe für ungerade Tabellenzeilen.\",\"Hintergrundfarbe der Aktionenliste.\",\"Vordergrundfarbe der Aktionenliste.\",\"Die Hintergrundfarbe der Aktionenliste für das fokussierte Element.\",\"Die Hintergrundfarbe der Aktionenliste für das fokussierte Element.\",\"Rahmenfarbe von Menüs.\",\"Vordergrundfarbe von Menüelementen.\",\"Hintergrundfarbe von Menüelementen.\",\"Vordergrundfarbe des ausgewählten Menüelements im Menü.\",\"Hintergrundfarbe des ausgewählten Menüelements im Menü.\",\"Rahmenfarbe des ausgewählten Menüelements im Menü.\",\"Farbe eines Trenner-Menüelements in Menüs.\",\"Minimap-Markerfarbe für gefundene Übereinstimmungen.\",\"Minimap-Markerfarbe für wiederholte Editorauswahlen.\",\"Minimap-Markerfarbe für die Editorauswahl.\",\"Minimapmarkerfarbe für Informationen.\",\"Minimapmarkerfarbe für Warnungen\",\"Minimapmarkerfarbe für Fehler\",\"Hintergrundfarbe der Minimap.\",\"Deckkraft von Vordergrundelementen, die in der Minimap gerendert werden. Beispiel: „#000000c0“ wird die Elemente mit einer Deckkraft von 75 % rendern.\",\"Hintergrundfarbe des Minimap-Schiebereglers.\",\"Hintergrundfarbe des Minimap-Schiebereglers beim Daraufzeigen.\",\"Hintergrundfarbe des Minimap-Schiebereglers, wenn darauf geklickt wird.\",\"Rahmenfarbe aktiver Trennleisten.\",\"Hintergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.\",\"Vordergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.\",\"Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.\",\"Hintergrundfarbe vom Scrollbar-Schieber\",\"Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.\",\"Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.\",\"Hintergrundfarbe des Fortschrittbalkens, der für zeitintensive Vorgänge angezeigt werden kann.\",\"Schnellauswahl der Hintergrundfarbe. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.\",\"Vordergrundfarbe der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.\",\"Hintergrundfarbe für den Titel der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.\",\"Schnellauswahlfarbe für das Gruppieren von Bezeichnungen.\",\"Schnellauswahlfarbe für das Gruppieren von Rahmen.\",\"Verwenden Sie stattdessen \\\"quickInputList.focusBackground\\\".\",\"Die Hintergrundfarbe der Schnellauswahl für das fokussierte Element.\",\"Die Vordergrundfarbe des Symbols der Schnellauswahl für das fokussierte Element.\",\"Die Hintergrundfarbe der Schnellauswahl für das fokussierte Element.\",\"Farbe des Texts in der Abschlussmeldung des Such-Viewlets.\",\"Farbe der Abfrageübereinstimmungen des Such-Editors\",\"Rahmenfarbe der Abfrageübereinstimmungen des Such-Editors\",\"Diese Farbe muss transparent sein, oder der Inhalt wird verdeckt.\",\"Standardfarbe verwenden.\",\"Die ID der zu verwendenden Schriftart. Sofern nicht festgelegt, wird die zuerst definierte Schriftart verwendet.\",\"Das der Symboldefinition zugeordnete Schriftzeichen.\",\"Symbol für Aktion zum Schließen in Widgets\",\"Symbol für den Wechsel zur vorherigen Editor-Position.\",\"Symbol für den Wechsel zur nächsten Editor-Position.\",\"Die folgenden Dateien wurden geschlossen und auf dem Datenträger geändert: {0}.\",\"Die folgenden Dateien wurden auf inkompatible Weise geändert: {0}.\",\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden. {1}\",\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden. {1}\",\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden, da Änderungen an {1} vorgenommen wurden.\",\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für \\\"{1}\\\" durchgeführt wird.\",\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.\",\"Möchten Sie \\\"{0}\\\" für alle Dateien rückgängig machen?\",\"&&In {0} Dateien rückgängig machen\",\"&&Datei rückgängig machen\",\"\\\"{0}\\\" konnte nicht rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.\",\"Möchten Sie \\\"{0}\\\" rückgängig machen?\",\"&&Ja\",\"Nein\",\"\\\"{0}\\\" konnte nicht in allen Dateien wiederholt werden. {1}\",\"\\\"{0}\\\" konnte nicht in allen Dateien wiederholt werden. {1}\",\"\\\"{0}\\\" konnte nicht in allen Dateien wiederholt werden, da Änderungen an {1} vorgenommen wurden.\",\"\\\"{0}\\\" konnte nicht für alle Dateien wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für \\\"{1}\\\" durchgeführt wird.\",\"\\\"{0}\\\" konnte nicht für alle Dateien wiederholt werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.\",\"\\\"{0}\\\" konnte nicht wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.\",\"Codearbeitsbereich\"];\nglobalThis._VSCODE_NLS_LANGUAGE=\"de\";"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,WAAW,qBAAqB,CAAC,YAAY,UAAU,oCAAiC,yBAAyB,kCAA+B,UAAU,uCAAoC,qEAAiE,yLAAyK,cAAc,eAAe,YAAY,2BAAwB,wBAAqB,uBAAoB,aAAa,cAAc,sBAAsB,SAAS,0BAAuB,4BAA4B,sBAAsB,sBAAsB,eAAY,mBAAmB,6BAA6B,KAAK,SAAS,WAAW,0CAA0C,wFAAwF,wFAAwF,0BAA0B,wFAAwF,OAAO,gBAAgB,MAAM,UAAU,OAAO,gBAAgB,MAAM,QAAQ,UAAU,gBAAgB,SAAS,SAAS,UAAU,gBAAgB,MAAM,UAAU,UAAU,gBAAgB,MAAM,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,8CAA2C,8CAA2C,+BAA4B,qBAAe,mBAAa,gBAAgB,cAAc,uBAAoB,oBAAiB,0EAAuE,kBAAkB,iSAA4Q,kDAA4C,mDAAkD,kDAAiD,4EAA2E,cAAc,iBAAiB,0BAA0B,4CAAyC,0CAAuC,kBAAe,6BAA6B,uCAAoC,sCAAsC,6DAAyD,2DAA0D,8DAA0D,eAAY,6FAA6F,6BAA0B,sBAAmB,yBAAsB,wFAAkF,OAAO,iCAA8B,wDAAkD,kCAA+B,+BAA0B,sEAAmE,+BAA4B,8BAA2B,+BAA4B,8BAA2B,oCAAiC,oCAAiC,4CAAmC,+DAA4D,qCAAkC,yBAAyB,2BAA2B,uCAAoC,kCAA+B,2BAA2B,oDAAoD,oCAAiC,qDAAqD,2BAA2B,8BAA8B,qDAAkD,sDAAmD,mCAAmC,oCAAoC,+CAAsC,8BAAwB,oEAAiE,2EAAwE,6DAA0D,0DAAoD,wDAAqD,+CAA+C,+DAA4D,0DAAuD,8BAA2B,SAAS,iKAA8J,4RAAwP,6JAAoJ,sHAAmH,+DAA4D,0GAAoG,gDAA6C,qDAAkD,6EAAuE,2DAAqD,8JAAwJ,sEAAmE,wEAAqE,yHAA2H,wGAAkG,uHAAoH,gGAAuF,6EAA6E,wFAAwF,gLAA0K,6LAA4L,kFAA4E,gEAA6D,mEAAgE,oJAAiJ,gEAA6D,mEAAgE,8GAA8G,6HAAoH,mFAAmF,8FAA8F,gHAAgH,uIAA8H,+JAAwI,qGAAkG,kHAA6G,2CAA2C,kCAA+B,+DAA+D,uEAAuD,6CAA6C,mDAAmD,gEAA6D,8EAAwE,8FAAwF,mGAAgG,gFAA6E,0HAAoH,yHAAsH,yFAAsF,6EAA0E,qEAAkE,kIAAyH,mEAAgE,qIAA+H,yEAAyE,iGAA2F,yDAAyD,iGAA8F,qDAAqD,wHAAoH,iEAAmE,oDAAsD,6FAA4F,8EAAgF,6GAA+G,2OAAiO,2IAAwI,uNAA0N,0KAAsK,6QAA4Q,qPAA8M,yNAAwL,oSAAgQ,8CAAwC,0GAAqG,wHAAsH,gEAAgE,uDAAuD,iHAA8G,mMAAuM,uGAAyG,4GAA8G,yGAA2G,2GAA6G,sGAAwG,4HAA2H,+HAA8H,gIAA+H,sIAAqI,yHAAwH,kDAAkD,4FAAyF,qGAAkG,oJAAmJ,mFAAgF,iPAA8O,yMAAmM,uKAAsK,mCAAgC,qFAAkF,2GAAwG,mEAA6D,sGAAmG,+EAA+E,8SAAkS,4GAA4G,0CAA0C,gCAAgC,wGAA+F,iHAAwG,kCAAkC,yMAA6L,kHAAoH,yDAAyD;AAAA;AAAA;AAAA,gFAAkR,0CAA0C,yDAAyD,qFAA+E,0HAA8G,4GAAsG,yCAAmC,oDAAoD,qEAAkE,mEAAgE,sFAAgF,6FAA6F,sFAAsF,iGAAuF,yEAAsE,oKAA2J,iFAAiF,mFAAmF,kGAA+F,uGAAiG,8DAA2D,6DAA0D,0CAAuC,+DAA4D,6DAA0D,kFAA4E,saAAoZ,0CAA0C,sDAAsD,4EAA4E,mDAAmD,yCAAyC,8FAA8F,+BAA+B,yLAAsL,8DAA8D,mDAAmD,wDAAwD,0DAA0D,gEAAgE,qDAAqD,0DAA0D,4DAA4D,4CAA4C,+CAA4C,0EAA0E,sIAA0H,mMAAgM,oHAAiH,kMAA4L,2FAA2F,6FAA6F,kEAA+D,kGAA4F,2EAAwE,oGAA0F,yHAA+G,iDAAiD,wEAAwE,8MAAqM,sDAAmD,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,kJAA+I,0FAAiF,2CAAwC,6EAAuE,6CAA0C,+EAAyE,wGAA+F,6EAAuE,kEAA4D,4FAAsF,kEAAkE,gEAA6D,4CAAyC,+FAA+F,uDAAuD,6EAA0E,6FAAuF,oFAA8E,gMAAiL,2FAAkF,sGAAgG,iLAAgL,4FAAsF,8FAAwF,uGAAiG,mGAA0F,+RAA6Q,0GAAyG,uEAAoE,uEAAuE,2FAA2F,sGAAsG,uGAA8F,yJAA6J,gEAA+D,mEAAkE,qEAAoE,yFAA4E,mYAA+U,+DAA8D,kEAAiE,+DAA8D,gEAA+D,mEAAkE,gEAA+D,kEAAiE,+DAA8D,kEAAiE,8DAA6D,+DAA8D,kEAAiE,8DAA6D,oEAAmE,iEAAgE,8DAA6D,+DAA8D,8DAA6D,mEAAkE,qEAAoE,gEAA+D,uEAAsE,iEAAgE,4DAAyD,8DAA2D,0FAAoF,sGAAmG,+PAAmP,+PAAmP,wDAAwD,mFAAgF,0EAAuE,0FAAoF,oDAAiD,qLAA4K,gJAA6I,+FAA4F,uHAAoH,4EAAsE,yJAAgJ,yGAAgG,gIAAuH,kNAA8M,uGAAoG,8MAAkM,4SAAsS,gBAAgB,qFAA+E,6GAA6G,mGAAgG,mIAA0H,8GAA8G,8GAA8G,2IAAqI,uIAA2H,uHAA8G,2HAA+G,kGAAyF,+GAA4G,mIAA0H,4JAA6I,uDAAoD,2DAAwD,2HAAqH,oLAAiL,wOAAqO,2IAAqI,2GAA2G,+DAAyD,+DAAyD,qHAA+G,sJAAmJ,2CAA2C,8CAA2C,sIAAiH,qFAAqF,yFAAyF,8DAA8D,0DAA0D,wFAAwF,gHAA6G,mFAAgF,sEAAsE,oCAAoC,8CAA8C,gHAAgH,kDAAkD,gEAAgE,oDAAiD,uPAAoN,4GAAwG,iDAAmD,mFAAyE,yFAAyF,qFAAkF,sDAAsD,mEAAmE,+CAA+C,oFAAoF,qEAA+D,mDAAmD,2HAAwH,2CAA2C,iEAA8D,iEAAiE,+DAA+D,gOAAoN,iHAAiH,8BAA8B,oLAAiL,kFAAkF,2HAAwH,0EAAuE,kDAAkD,4LAA6K,oEAAoE,gCAAgC,4GAA6G,4GAAyF,2GAAkG,iEAA2D,0FAA0F,sFAAsF,iUAA+T,2CAAwC,sDAAgD,mGAA6F,mGAAmG,+BAA+B,oDAAoD,mFAA6E,8EAA2E,4EAAyE,uDAAoD,yDAAsD,yFAAyF,qFAAoF,8FAAwF,4EAA4E,8DAAgE,qDAAqD,6EAA6E,mDAAmD,wEAAwE,qHAAqH,+FAA4F,4EAAsE,uCAAuC,oDAAoD,6DAA6D,4FAAyF,mEAAmE,0MAA0M,8EAAwE,2FAAqF,wDAAqD,sGAAgG,wGAAkG,4FAAyF,qDAAqD,gDAAgD,mEAA0D,gEAA0D,2DAAqD,yCAAsC,gGAA6F,8DAAwD,yKAAmK,mGAAgG,4HAAsH,wGAAqG,2CAAwC,4MAAuM,gKAAsJ,yFAAmF,qGAA4F,2CAAwC,sJAA+I,yCAAsC,uEAAoE,4DAAyD,kGAA+F,yFAAmF,0GAAiG,4DAAsD,2LAAkL,sHAAgH,4HAAsH,kCAA+B,+DAA+D,2DAA6D,qFAAuF,8DAA2D,+HAAkI,0GAA0G,qGAA+F,qEAAqE,yEAAsE,gLAAgL,iEAA8D,kNAAmN,yDAAyD,+BAA+B,2IAAwI,2EAAwE,+LAAyL,wEAAqE,4LAAsL,mCAAmC,gCAAgC,2DAAkD,0GAA8G,mEAA0D,sHAA0H,+DAAsD,+DAAsD,+DAAsD,+DAAsD,+DAAsD,+DAAsD,uEAA8D,uEAA8D,uEAA8D,uEAA8D,uEAA8D,uEAA8D,8CAA8C,sFAAwF,8CAA8C,6HAAyG,4BAA4B,gDAAgD,uDAAiD,4CAAsC,oDAA8C,oDAAiD,uGAAiG,mEAAgE,iSAA4R,yCAAyC,8CAA8C,8CAA8C,uKAAiK,sDAAgD,yDAAmD,6DAAuD,gGAAgG,gGAAgG,gGAAgG,gGAAgG,gGAAgG,gGAAgG,8CAA8C,oHAAoH,oHAAoH,oHAAoH,oHAAoH,oHAAoH,oHAAoH,kHAAkH,kHAAkH,kHAAkH,kHAAkH,kHAAkH,kHAAkH,uEAAuE,4EAA4E,iEAAiE,uHAAoH,wFAAwF,iDAA8C,+CAA+C,6DAA6D,KAAK,+DAA+D,sDAAmD,kFAA4E,0DAA0D,yHAAuH,yCAAyC,uEAA0D,uEAA0D,0CAAuC,sCAAmC,sDAAwD,gDAA6C,6DAAuD,iEAAiE,4CAA4C,qDAAqD,0DAA0D,kDAAkD,+DAAyD,iEAA2D,4FAAmF,oCAAoC,wFAA+E,uEAAiE,qEAA+D,uEAAiE,wEAAkE,4EAAsE,0EAAoE,iEAA2D,iFAA2E,0EAAoE,mEAA6D,wEAAkE,yEAAmE,yEAAmE,iFAA2E,+FAAsF,mFAA6E,iGAAwF,QAAQ,kBAAkB,SAAS,YAAY,cAAc,cAAc,qBAAqB,WAAW,OAAO,QAAQ,WAAW,gBAAgB,eAAY,UAAU,QAAQ,YAAY,OAAO,OAAO,SAAS,WAAW,QAAQ,cAAc,eAAe,WAAW,eAAe,WAAW,YAAY,WAAW,UAAU,qCAA+B,0BAA0B,mDAAgD,iBAAiB,oCAAiC,oBAAoB,mCAAmC,gBAAgB,0CAA0C,mDAAgD,sBAAsB,cAAc,eAAe,iCAAmC,yBAAyB,2BAA2B,8BAA8B,yBAAyB,4EAAmE,kBAAkB,2BAAwB,qBAAqB,oBAAoB,2EAAqE,8CAA2C,+CAA4C,yBAAyB,iBAAiB,eAAe,eAAe,eAAe,aAAa,WAAW,WAAW,WAAW,gBAAa,cAAW,cAAW,cAAW,kCAAkC,eAAe,eAAe,YAAY,YAAY,uEAAuE,uCAAoC,oEAAiE,wDAAqD,+EAA4E,gDAA6C,2EAAwE,+BAA+B,kCAA+B,2DAAuD,+CAA2C,8CAA2C,kCAA+B,gCAA6B,2DAAuD,+CAA2C,8CAA2C,kCAA+B,iBAAiB,4DAAwD,gDAA4C,+CAA4C,mCAAgC,uBAAuB,0DAAuD,mBAAmB,+CAA8C,6BAA6B,+CAA4C,oFAAiF,4KAAsK,+LAAgL,4CAA4C,mCAAmC,iCAAiC,sBAAsB,2BAA2B,cAAc,SAAS,oBAAoB,cAAc,cAAc,cAAc,sHAAyG,mKAAmJ,sJAAsI,mLAAmK,mLAAmK,oBAAiB,wEAAqE,8BAA8B,wBAAwB,kDAA+C,sBAAmB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,6BAA6B,+BAA+B,gCAA6B,4BAA4B,4BAA4B,8BAA8B,UAAU,kBAAkB,wBAAkB,eAAe,eAAY,WAAW,gBAAgB,eAAY,QAAQ,iCAA8B,qCAA+B,yBAAyB;AAAA,2LAAgQ,qBAAkB,uBAAoB,mDAAgD,iCAA8B,2EAA2D,qEAA+D,qIAAyH,gCAA0B,yCAAmC,uBAAoB,kBAAe,kBAAe,oBAAiB,mBAAgB,6BAA0B,6BAA0B,mBAAgB,KAAK,+CAA+C,6BAA6B,kFAA+E;AAAA,KAAuD;AAAA,KAAuD,6FAAyF,yFAAmF,SAAS,WAAW,wBAAwB,qBAAqB,eAAe,4BAA4B,wCAAkC,2EAAwE,0GAAoG,sCAAmC,sCAAmC,4BAAyB,2BAA2B,WAAW,aAAa,2EAAwE,4EAAyE,2DAA0D,kDAAiD,uDAAsD,mEAAkE,oEAAgE,kBAAkB,SAAS,SAAS,+BAA4B,gCAA0B,oBAAoB,eAAY,WAAW,WAAW,WAAW,gBAAgB,sBAAsB,8HAA2H,cAAc,mBAAmB,eAAe,4BAA2B,qCAAoC,4BAA2B,4MAA+L,YAAY,4BAA4B,SAAS,yBAAyB,kBAAkB,6BAA6B,8BAA8B,uBAAuB,0BAA0B,qCAAkC,wCAAqC,cAAc,iBAAiB,6CAA0C,sCAAsC,uCAAoC,wCAAwC,kCAAkC,gBAAgB,oJAAoJ,+EAA+E,mDAAmD,6DAA0D,4DAAyD,wEAAqE,wEAAqE,iDAAiD,kDAAkD,qCAAkC,qCAAqC,qCAAkC,uBAAuB,sBAAsB,6DAA0D,iEAA2D,yDAAyD,gEAA6D,yEAAsE,wBAAqB,wEAAwE,uBAAuB,SAAS,UAAU,OAAO,UAAU,gBAAgB,wBAAwB,wBAAwB,6DAA6D,+EAA4E,8DAA8D,iFAA8E,kEAAkE,qFAAkF,wDAAwD,WAAW,eAAe,0CAAyC,4BAA4B,uBAAuB,gBAAgB,2CAA0C,8BAA8B,wBAAwB,2CAA0C,8BAA8B,kBAAkB,8CAA6C,iCAAiC,+BAA+B,oBAAoB,8CAA6C,iCAAiC,8BAA8B,+CAA8C,4BAA4B,sBAAsB,WAAW,WAAW,eAAe,gCAA+B,WAAW,qBAAqB,oCAAiC,sBAAsB,2BAA2B,uCAAoC,6BAA6B,sCAAsC,4BAA4B,6CAA0C,oBAAoB,oCAAiC,iCAAiC,+CAA+C,uGAAwG,kBAAkB,YAAY,eAAe,cAAc,WAAW,+BAA4B,mBAAmB,WAAW,oCAAoC,wCAAwC,6CAA0C,gDAA6C,uCAAuC,2BAA2B,8BAA8B,sCAAsC,4GAAsG,6CAAuC,qBAAqB,uDAAiD,uDAAoD,wCAAwC,gEAAgE,oFAAoF,iFAAiF,oCAAoC,uCAAuC,wCAAwC,wCAAwC,yCAAyC,yCAAyC,0CAA0C,mCAAmC,oCAAoC,+KAA4K,oDAAoD,uDAAuD,yDAAyD,wDAAwD,yDAAyD,yDAAyD,0DAA0D,+DAA+D,gEAAgE,kEAA4D,kEAA+D,kBAAkB,yJAAyI,wKAAqJ,wDAAkD,kDAA4C,wDAAqD,kDAA+C,mBAAmB,wCAAqC,6CAA6C,wCAAqC,+BAA+B,qCAAqC,mCAAmC,qCAA+B,oCAA8B,qCAA+B,0DAA8C,gCAAgC,kCAAkC,iDAAwC,6BAA6B,uCAAoC,qCAA+B,4CAA4C,4CAA4C,kCAAkC,oCAAoC,0EAA2D,kCAAkC,8CAA2C,8DAAwD,gCAA6B,mBAAmB,iBAAiB,mBAAmB,gBAAgB,+GAA+G,2BAA2B,sBAAmB,wCAAqC,uCAAuC,+BAA4B,kDAA+C,gBAAgB,oDAAiD,iBAAiB,4BAA4B,cAAc,6BAA6B,8BAA8B,iDAAiD,4DAA4D,sIAAgI,wFAA+E,iEAA2D,aAAa,gEAA0D,+DAA4D,YAAY,YAAS,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,iCAAiC,kCAA+B,0BAA0B,2BAA2B,6BAA6B,4BAA4B,8BAA8B,sBAAsB,wBAAwB,8BAA8B,gCAAgC,+BAA+B,iCAAiC,+BAA+B,8BAA8B,6BAA0B,uCAAoC,mBAAgB,eAAe,qBAAkB,yBAAsB,0BAAuB,6BAAuB,yBAAsB,uBAAoB,4CAA4C,iCAA8B,+BAA+B,0CAAuC,0BAA0B,yBAAyB,+CAA+C,0BAA0B,oCAAiC,oEAAoE,yEAAsE,2DAAwD,sBAAmB,cAAc,mBAAmB,iBAAiB,mBAAmB,gBAAgB,sCAAqC,iBAAc,+DAA+D,+BAA4B,8BAA2B,gCAA6B,kCAA+B,iCAA8B,mCAAgC,sCAAmC,wCAAqC,+BAA4B,iCAA8B,iEAAwD,wCAAkC,sEAAgE,uCAAoC,oEAA8D,mEAAgE,4DAAsD,gCAA6B,2BAAwB,+BAA4B,oCAAiC,8BAA8B,mCAAmC,gCAA6B,gEAA0D,+DAA4D,eAAe,6DAA6D,wEAAwE,eAAY,uDAAuD,wCAAwC,oDAAoD,mDAAgD,0DAA0D,8EAA2E,6EAA0E,uFAAoF,uFAAoF,qCAAqC,8CAA8C,mHAAyF,sFAAgF,+DAAyD,gEAA0D,mDAAmD,yEAAsE,6CAA6C,yBAAyB,2HAAwH,wGAAqG,yGAAmG,kEAAkE,4CAAyC,uBAAuB,yBAAsB,kBAAe,gBAAgB,sBAAsB,iBAAiB,mBAAmB,sBAAsB,kBAAkB,gBAAgB,mBAAmB,mBAAmB,mBAAmB,uBAAuB,mBAAmB,eAAe,qBAAqB,eAAe,sBAAsB,sBAAsB,2BAA2B,sBAAsB,gBAAgB,eAAe,eAAe,wBAAwB,gBAAgB,qBAAkB,eAAe,mBAAmB,iEAA2D,sEAAgE,iBAAiB,uFAAoF,gCAAgC,6BAA6B,6DAAiE,+DAA4D,gEAA6D,oBAAoB,2GAAqG,gDAA6C,+CAA+C,kEAAkE,oEAAoE,wCAAwC,4CAAyC,mIAA6H,8CAAwC,YAAY,qBAAqB,sBAAsB,yBAAyB,wBAAwB,kEAAkE,wEAAqE,uEAAuE,0CAAuC,UAAU,SAAS,WAAW,WAAW,aAAa,UAAU,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,SAAS,UAAU,UAAO,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,WAAW,MAAM,MAAM,SAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,8CAA8C,qBAAqB,uBAAuB,6BAA6B,8CAA2C,6HAA6H,iDAAiD,6DAAuD,gDAA6C,oDAAoD,sBAAmB,2CAA2C,+CAA+C,wDAAqD,6HAAoH,mFAA0E,sFAAmF,8EAA2E,uFAAiF,oEAAmE,wBAAqB,cAAW,cAAW,WAAW,WAAW,cAAW,mBAAmB,gBAAgB,oDAA2C,0CAA0C,qCAAqC,0CAA0C,yEAAsE,qFAAkF,qEAAkE,qDAAqD,mGAAmG,qDAAqD,kBAAkB,uBAAoB,cAAc,eAAe,UAAU,WAAW,sBAAsB,eAAY,kBAAkB,4DAAyD,wBAAwB,4IAAmI,iJAAwI,8IAAqI,2IAAkI,iJAAwI,kJAAyI,iJAAwI,uJAA8I,+IAAsI,2IAAkI,4IAAmI,6IAAoI,gJAAuI,qJAA4I,mJAAuI,uJAA2I,+IAAsI,4IAAmI,gJAAuI,4IAAmI,6IAAoI,6IAAoI,+IAAsI,4IAAmI,oJAA2I,+IAAsI,oJAA2I,oJAA2I,+IAAsI,2IAAkI,mJAA0I,gJAAuI,gJAAuI,mGAA6F,wEAAkE,qCAAqC,wNAAqN,+BAA+B,2EAA2E,yEAAsE,gEAA6D,gEAA6D,6DAA0D,sGAAmG,sGAAmG,sCAAsC,oDAAoD,yBAAyB,2CAA2C,2DAA2D,6CAA6C,6DAA6D,wCAAwC,yDAAyD,wCAAwC,qDAAqD,wCAAwC,kEAAkE,8BAA8B,kEAA+D,wBAAwB,qFAAwE,sCAAmC,8CAA2C;AAAA;AAAA,2HAA8Q,sDAAmD,aAAa,qLAAqL,2LAA2L,mJAAgJ,iFAAiF,wFAAwF,qDAAkD,sKAAgK,iLAA2K,4KAAsK,4CAAyC,2CAA2C,uCAAuC,kBAAe,qBAAqB,SAAS,sBAAsB,UAAU,sBAAsB,kBAAkB,uBAAuB,mBAAmB,kCAAkC,WAAW,0BAA0B,aAAa,+BAA+B,qCAAqC,2BAA2B,kCAAkC,aAAa,oCAAoC,uBAAuB,wBAAwB,wBAAwB,yBAAyB,qBAAqB,gCAAgC,wBAAwB,6BAA6B,qBAAqB,iBAAiB,iBAAiB,8BAA8B,8BAA8B,+BAA+B,+BAA+B,+BAA4B,8BAA2B,8BAA2B,uBAAuB,uBAAuB,wBAAwB,SAAS,SAAS,aAAU,aAAU,YAAY,YAAY,SAAS,cAAc,+BAA+B,6BAA6B,UAAU,QAAQ,OAAO,QAAQ,gBAAgB,aAAa,YAAY,YAAY;AAAA,WAAmB,cAAc,YAAY,aAAa,0BAAoB,mBAAqB,gCAAgC,wCAAqC,mBAAmB,8BAA8B,gBAAgB,8EAA2E,mDAAmD,+BAA+B,gCAA6B,iCAA2B,oCAAiC,mDAA6C,gEAA0D,8FAAwF,gFAA0E,0EAAuE,gFAA0E,0EAAuE,wDAAwD,gNAAmN,iFAAkF,uGAAyG,sEAAgE,qCAAkC,4LAAiJ,uCAAmB,uCAA0B,qBAAqB,+DAA4D,mCAAmC,4DAAyD;AAAA,6BAAqC,uDAAuD,uDAAuD,yDAAyD,qEAAqE,yFAAyF,qDAAqD,6EAA6E,+BAA4B,oEAAoE,mBAAmB,4BAA4B,iCAAiC,mFAA0E,6JAAsI,2CAAwC,uFAAoF,uFAAoF,oDAAoD,oDAAoD,YAAY,0FAA0F,sFAAsF,sWAA4U,oaAAwZ,iLAA8K,wEAAqE,wCAAwC,kEAA+D,0EAA0E,4GAA6G,qEAA+D,uHAAoH,gCAAgC,+EAA4E,oLAAwK,yMAAsM,uJAAoJ,4JAAkK,gIAA4G,4DAAyD,2EAAqE,0HAAuH,6LAA6L,2DAA2D,wGAAwG,2NAA4N,SAAS,UAAU,OAAO,oBAAoB,sBAAmB,sBAAmB,iBAAiB,sBAAmB,WAAW,mDAAkD,WAAW,sGAAmG,gDAAgD,uFAAuF,YAAS,2GAAqG,UAAU,+DAA+D,gNAAuM,2LAAqL,yLAAyL,0CAAuC,iBAAiB,oBAAiB,KAAK,oBAAoB,kBAAe,YAAS,iBAAiB,8CAA6C,2HAAwH,2HAAwH,kJAA4I,iHAA2G,gEAA6D,kJAA4I,oIAA2H,2IAAkI,wJAAkJ,yCAAsC,yFAAsF,kCAA+B,uDAAoD,uDAAoD,+CAA4C,kDAA+C,iDAA2C,iDAAiD,gEAA6D,wDAAwD,yDAAyD,yDAAyD,2DAA2D,4DAAyD,4DAA4D,gCAAgC,wCAAwC,qDAAqD,uEAAuE,gDAAgD,mDAAmD,wEAAwE,6DAA0D,4KAAsK,yOAAuN,gJAA6I,0DAA0D,iGAA8F,kJAA+I,4DAA4D,oGAAiG,8IAA2I,gEAAgE,gGAA6F,2DAA2D,mGAAgG,2BAA2B,4BAA4B,0DAAoD,gKAAgK,iKAA8J,kEAA+D,uCAAuC,kDAA+C,oIAAoI,yDAAsD,gJAAgJ,2CAA2C,qCAAqC,qJAAqJ,gLAA6K,2CAA2C,0CAA0C,sCAAsC,sDAAsD,yCAAsC,yCAAsC,oDAAiD,oDAAiD,wDAAqD,wDAAqD,gEAA4D,uFAAmF,uEAAiE,gEAAgE,2DAA2D,6EAA6E,2EAA2E,+IAAyI,oJAAiJ,yIAAmI,gJAA6I,uEAAiE,uEAAoE,0EAAiE,sEAAgE,0CAAoC,sCAAmC,+CAA+C,yIAAmI,uEAAiE,uEAAiE,kEAA+D,oFAAoF,mEAAgE,kEAA+D,iEAA8D,iEAA8D,sDAAsD,4CAA4C,sDAAsD,qDAAkD,gDAAgD,4KAAyK,kLAA4K,+KAAyK,gLAA0K,mMAA0L,iMAA2L,gFAA6E,4EAAsE,8EAAwE,2HAAkH,6KAAoK,qKAA+J,gEAA6D,8DAA2D,8DAA2D,kCAA+B,kCAA+B,6BAA0B,4DAAyD,iEAA8D,wEAAqE,iEAA8D,uDAAoD,sFAAmF,sFAAmF,iFAA8E,kFAA+E,kFAA+E,6EAA0E,kFAA+E,kFAA+E,6EAA0E,+BAA4B,oCAAiC,+BAA4B,0BAAuB,wCAAqC,2CAAwC,wCAAqC,kEAA+D,mCAAgC,qDAA+C,sDAAgD,wEAAkE,+CAA8C,+CAA8C,sCAAsC,2DAA8C,2DAA8C,wCAAwC,gFAAgF,kDAA+C,gHAA0G,kDAA+C,6CAA0C,2GAAqG,8IAA8I,8IAA8I,yIAAyI,6JAA0J,qLAAkL,qLAAkL,gLAA6K,sLAAgL,wLAAkL,wLAAkL,oMAA8L,0LAAoL,kMAA4L,sMAAgM,uLAAoL,kLAA+K,+EAA+E,+EAA+E,oIAA8H,sIAAmI,wGAAwG,qJAAqJ,uHAA8G,gEAA6D,mEAAgE,kEAAkE,6DAA6D,sGAAmG,+DAA+D,sDAAmD,iDAA8C,yEAAsE,2DAAqD,sEAAmE,wCAAwC,mDAAgD,sCAAsC,sCAAsC,yEAAsE,yEAAsE,4BAAyB,yCAAsC,yCAAsC,mEAA0D,mEAA0D,8DAAqD,mDAA6C,6DAAuD,0DAAuD,gDAA6C,2CAAwC,sCAAmC,mCAAgC,gCAAgC,mKAAyJ,+CAA+C,iEAAiE,0EAA0E,oCAAoC,kGAA4F,kGAA4F,6EAA6E,0CAA0C,iEAAiE,kEAAkE,uGAAiG,kIAA+H,kIAA+H,mJAA6I,+DAA4D,wDAAqD,8DAAgE,0EAAuE,sFAAmF,0EAAuE,6DAA6D,yDAAsD,+DAA4D,oEAAoE,2BAA2B,mHAAmH,uDAAuD,mDAA6C,4DAAyD,6DAAuD,wFAAkF,wEAAqE,8EAAuE,8EAAuE,sHAA4G,kLAAiK,0LAA0K,oEAA0D,2CAAqC,kCAA4B,iJAAoI,gDAAyC,OAAO,OAAO,6DAA+D,6DAA+D,qGAAoG,oKAAyJ,4KAAkK,mIAA4H,oBAAoB,EAC1y/G,WAAW,qBAAqB", "names": [], "file": "nls.messages.de.js"}