# 部署配置
deploy:
  $shared:
    # 企业微信通知配置
    notifyWxWorkConfig:
      title: 云平台 Web
      desc: 智能基础服务云平台 Web
      iconUrl: https://cloud.intsig.net/logo.png
      # 上线发布同步 - 前端上线 通知 机器人
      url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9b65e4d-7886-405e-a5a4-eb01b79dd0c4'

  # 测试环境
  test:
    jenkinsUrl: https://jenkins.intsig.net/view/Autom/job/autom_DEVops_test/job/cloud_web_test/
    jenkinsFormData:
      server_env: test
      branch: origin/test
    jenkinsBuildTimeout: 600
    notifyWxWorkConfig:
      title: 智能基础服务云平台 前端 送测

  # 线上环境
  online:
    # jenkinsUrl: https://jenkins.intsig.net/view/Autom/job/autom_DEVops_online/job/cloud_web_online/
    jenkinsUrl: https://jenkins.intsig.net/view/Autom/job/autom_DEVops_test/job/cloud_web_test/
    jenkinsFormData:
      server_env: online
      branch: origin/main
    jenkinsBuildTimeout: 600
    notify: true
    notifyWxWorkConfig:
      title: 智能基础服务云平台 前端 上线
    tag: true
