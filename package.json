{"name": "cloud-web", "version": "2.0.0", "description": "云平台 Web", "license": "UNLICENSED", "packageManager": "pnpm@10.10.0", "private": true, "scripts": {"build": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build", "serve": "vite dev"}, "dependencies": {"@aim/helper": "0.8.31", "@ant-design-vue/pro-layout": "3.2.5", "@ant-design/icons-vue": "7.0.1", "@antv/data-set": "0.11.8", "@antv/g2": "5.3.2", "@antv/g2-plugin-slider": "2.1.1", "@antv/g2plot": "2.4.33", "@fingerprintjs/fingerprintjs": "4.6.2", "@gausszhou/vue3-drag-resize-rotate": "3.0.2", "@intsig/toolkit": "0.0.17", "@tx/style": "1.4.1", "@tx/ui": "0.12.16", "ant-design-vue": "3.2.20", "asciinema-player": "3.9.0", "axios": "1.9.0", "core-js": "3.42.0", "crypto-js": "4.2.0", "dayjs": "1.11.13", "diff": "^8.0.2", "enquire.js": "2.1.6", "guacamole-common-js": "1.5.0", "highlight.js": "11.11.1", "ipaddr.js": "^2.2.0", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "jsotp": "1.0.4", "jszip": "3.10.1", "lodash": "4.17.21", "lodash.clonedeep": "4.5.0", "lodash.get": "4.4.2", "lodash.pick": "4.4.0", "md5": "2.3.0", "mockjs2": "1.0.8", "moment": "2.30.1", "monaco-editor": "0.52.2", "node-forge": "1.3.1", "nprogress": "0.2.0", "pinyin-pro": "3.26.0", "screenfull": "6.0.2", "script-loader": "0.7.2", "simple-keyboard": "3.8.53", "socket.io-client": "4.8.1", "splitpanes": "3.1.5", "sql-formatter": "13.1.0", "uuid": "9.0.1", "v-code-diff": "1.13.1", "viser-vue": "2.4.8", "vue": "3.5.13", "vue-clipboard2": "0.3.3", "vue-code-diff": "1.2.0", "vue-cropper": "0.6.5", "vue-draggable-resizable": "2.3.0", "vue-i18n": "9.8.0", "vue-json-editor": "1.4.3", "vue-quill-editor": "3.0.6", "vue-router": "4.5.1", "vue-svg-component-runtime": "1.0.1", "vue-types": "5.1.1", "vuedraggable": "2.24.3", "vuex": "4.1.0", "vxe-table": "4.5.13", "wangeditor": "4.7.15", "xe-utils": "3.7.4", "xlsx": "0.18.5", "xterm": "5.3.0", "xterm-addon-attach": "0.9.0", "xterm-addon-fit": "0.8.0", "xterm-addon-search": "0.13.0", "zxcvbn": "4.4.2"}, "devDependencies": {"@ant-design/colors": "7.0.0", "@prettier/plugin-pug": "3.4.0", "@tx/eslint-config": "1.1.4", "@tx/plugin": "0.2.2", "@tx/prettier-config": "1.1.1", "@tx/ts-config": "1.1.1", "@tx/vite-config": "1.5.4", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.1.2", "babel-plugin-import": "1.13.8", "babel-plugin-transform-remove-console": "6.9.4", "eslint": "9.26.0", "file-loader": "6.2.0", "file-saver": "2.0.5", "js-base64": "3.7.7", "less": "4.3.0", "prettier": "3.5.3", "pug-html-loader": "1.1.7", "pug-plain-loader": "1.1.0", "typescript": "5.8.3", "unplugin-vue-components": "28.5.0", "v-contextmenu": "3.0.0", "vite": "6.3.5", "webpack-theme-color-replacer": "1.5.3"}, "prettier": "@tx/prettier-config"}